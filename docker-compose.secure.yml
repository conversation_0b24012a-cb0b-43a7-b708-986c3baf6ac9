version: '3.8'

# EVEXA Secure Production Docker Compose Configuration
# Includes security hardening, secrets management, and monitoring

services:
  # ==========================================
  # EVEXA Application (Secure)
  # ==========================================
  evexa-app:
    build:
      context: .
      dockerfile: Dockerfile.secure
      args:
        - NODE_ENV=production
        - EVEXA_SECURE_BUILD=true
    container_name: evexa-secure
    restart: unless-stopped
    
    # Security: Resource limits
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    # Security: Network configuration
    networks:
      - evexa-secure-network
    
    # Security: Port mapping (only expose necessary ports)
    ports:
      - "3000:3000"
    
    # Security: Environment variables from secrets
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - EVEXA_SECURE_MODE=true
    
    # Security: Secrets management
    secrets:
      - firebase_config
      - groq_api_key
      - jwt_secret
    
    # Security: Volume mounts (read-only where possible)
    volumes:
      - app-logs:/app/logs:rw
      - /etc/localtime:/etc/localtime:ro
    
    # Security: Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Security: Capabilities and privileges
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
      - /app/.next/cache:noexec,nosuid,size=200m
    
    # Security: User and group
    user: "1001:1001"
    
    # Dependencies
    depends_on:
      - redis-cache
      - nginx-proxy

  # ==========================================
  # Redis Cache (Secure)
  # ==========================================
  redis-cache:
    image: redis:7-alpine
    container_name: evexa-redis-secure
    restart: unless-stopped
    
    # Security: Resource limits
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M
    
    # Security: Network configuration
    networks:
      - evexa-secure-network
    
    # Security: Redis configuration
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD}
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --appendonly yes
      --appendfsync everysec
    
    # Security: Volume for persistence
    volumes:
      - redis-data:/data:rw
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    
    # Security: Health check
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5
    
    # Security: Capabilities
    cap_drop:
      - ALL
    cap_add:
      - SETGID
      - SETUID
    security_opt:
      - no-new-privileges:true

  # ==========================================
  # Nginx Reverse Proxy (Secure)
  # ==========================================
  nginx-proxy:
    image: nginx:alpine
    container_name: evexa-nginx-secure
    restart: unless-stopped
    
    # Security: Resource limits
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M
    
    # Security: Network configuration
    networks:
      - evexa-secure-network
    
    # Security: Port mapping
    ports:
      - "80:80"
      - "443:443"
    
    # Security: Configuration and SSL certificates
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx:rw
    
    # Security: Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 3s
      retries: 3
    
    # Security: Capabilities
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    security_opt:
      - no-new-privileges:true
    
    # Dependencies
    depends_on:
      - evexa-app

  # ==========================================
  # Security Monitoring (Optional)
  # ==========================================
  security-monitor:
    image: falcosecurity/falco:latest
    container_name: evexa-security-monitor
    restart: unless-stopped
    privileged: true
    
    # Security: Network configuration
    networks:
      - evexa-secure-network
    
    # Security: Volume mounts for monitoring
    volumes:
      - /var/run/docker.sock:/host/var/run/docker.sock:ro
      - /dev:/host/dev:ro
      - /proc:/host/proc:ro
      - /boot:/host/boot:ro
      - /lib/modules:/host/lib/modules:ro
      - /usr:/host/usr:ro
      - /etc:/host/etc:ro
      - ./falco/falco.yaml:/etc/falco/falco.yaml:ro
    
    # Security: Environment
    environment:
      - FALCO_GRPC_ENABLED=true
      - FALCO_GRPC_BIND_ADDRESS=0.0.0.0:5060

# ==========================================
# Networks
# ==========================================
networks:
  evexa-secure-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: evexa-secure-br
    ipam:
      config:
        - subnet: **********/16

# ==========================================
# Volumes
# ==========================================
volumes:
  app-logs:
    driver: local
  redis-data:
    driver: local
  nginx-logs:
    driver: local

# ==========================================
# Secrets
# ==========================================
secrets:
  firebase_config:
    file: ./secrets/firebase-config.json
  groq_api_key:
    file: ./secrets/groq-api-key.txt
  jwt_secret:
    file: ./secrets/jwt-secret.txt
