{"name": "evexa-cloud-functions", "version": "1.0.0", "description": "EVEXA Cloud Functions for data integrity and tenant management", "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "engines": {"node": "18"}, "dependencies": {"firebase-admin": "^11.11.0", "firebase-functions": "^4.5.0"}, "devDependencies": {"@types/jest": "^29.5.5", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.49.0", "eslint-plugin-import": "^2.28.1", "firebase-functions-test": "^3.1.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "private": true, "jest": {"preset": "ts-jest", "testEnvironment": "node", "testMatch": ["**/__tests__/**/*.test.ts"]}, "eslintConfig": {"parser": "@typescript-eslint/parser", "parserOptions": {"project": ["tsconfig.json", "tsconfig.dev.json"], "sourceType": "module"}, "extends": ["eslint:recommended", "@typescript-eslint/recommended"], "plugins": ["@typescript-eslint", "import"], "rules": {"import/no-unresolved": 0, "indent": ["error", 2], "quotes": ["error", "single"], "semi": ["error", "always"], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": "error"}}}