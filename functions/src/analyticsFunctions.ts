/**
 * Analytics Cloud Functions
 * Handle real-time analytics updates and report generation
 */

import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

const db = admin.firestore();

/**
 * Update exhibition analytics when related data changes
 */
export const updateExhibitionAnalytics = functions.firestore
  .document('{collection}/{docId}')
  .onWrite(async (change, context) => {
    const collection = context.params.collection;
    const docId = context.params.docId;
    
    // Only process relevant collections
    const relevantCollections = ['exhibition_events', 'exhibition_tasks', 'lead_contacts', 'expense_records'];
    if (!relevantCollections.includes(collection)) {
      return;
    }

    const data = change.after.exists ? change.after.data() : null;
    const beforeData = change.before.exists ? change.before.data() : null;
    
    if (!data && !beforeData) return;
    
    const exhibitionId = data?.exhibitionId || beforeData?.exhibitionId;
    const tenantId = data?.tenantId || beforeData?.tenantId;
    
    if (!exhibitionId || !tenantId) return;

    console.log(`Updating analytics for exhibition ${exhibitionId} due to ${collection} change`);

    try {
      // Calculate updated analytics
      const analytics = await calculateExhibitionAnalytics(exhibitionId, tenantId);
      
      // Update analytics document
      await db.collection('exhibition_analytics').doc(exhibitionId).set({
        exhibitionId,
        tenantId,
        ...analytics,
        lastUpdated: admin.firestore.FieldValue.serverTimestamp()
      }, { merge: true });

    } catch (error) {
      console.error(`Error updating exhibition analytics for ${exhibitionId}:`, error);
    }
  });

/**
 * Update lead analytics when lead data changes
 */
export const updateLeadAnalytics = functions.firestore
  .document('lead_contacts/{leadId}')
  .onWrite(async (change, context) => {
    const data = change.after.exists ? change.after.data() : null;
    const beforeData = change.before.exists ? change.before.data() : null;
    
    if (!data && !beforeData) return;
    
    const tenantId = data?.tenantId || beforeData?.tenantId;
    if (!tenantId) return;

    console.log(`Updating lead analytics for tenant ${tenantId}`);

    try {
      const analytics = await calculateLeadAnalytics(tenantId);
      
      await db.collection('lead_analytics').doc(tenantId).set({
        tenantId,
        ...analytics,
        lastUpdated: admin.firestore.FieldValue.serverTimestamp()
      }, { merge: true });

    } catch (error) {
      console.error(`Error updating lead analytics for tenant ${tenantId}:`, error);
    }
  });

/**
 * Update financial analytics when financial data changes
 */
export const updateFinancialAnalytics = functions.firestore
  .document('{collection}/{docId}')
  .onWrite(async (change, context) => {
    const collection = context.params.collection;
    
    // Only process financial collections
    const financialCollections = ['budget_allocations', 'expense_records', 'purchase_orders'];
    if (!financialCollections.includes(collection)) {
      return;
    }

    const data = change.after.exists ? change.after.data() : null;
    const beforeData = change.before.exists ? change.before.data() : null;
    
    if (!data && !beforeData) return;
    
    const tenantId = data?.tenantId || beforeData?.tenantId;
    if (!tenantId) return;

    console.log(`Updating financial analytics for tenant ${tenantId}`);

    try {
      const analytics = await calculateFinancialAnalytics(tenantId);
      
      await db.collection('financial_analytics').doc(tenantId).set({
        tenantId,
        ...analytics,
        lastUpdated: admin.firestore.FieldValue.serverTimestamp()
      }, { merge: true });

    } catch (error) {
      console.error(`Error updating financial analytics for tenant ${tenantId}:`, error);
    }
  });

/**
 * Generate daily reports for all tenants
 */
export const generateDailyReports = functions.pubsub
  .schedule('0 6 * * *') // Daily at 6 AM
  .timeZone('UTC')
  .onRun(async (context) => {
    console.log('Generating daily reports...');

    try {
      // Get all active tenants
      const tenantsSnapshot = await db.collection('tenants')
        .where('status', '==', 'active')
        .get();

      for (const tenantDoc of tenantsSnapshot.docs) {
        const tenantId = tenantDoc.id;
        
        try {
          await generateTenantDailyReport(tenantId);
        } catch (error) {
          console.error(`Error generating daily report for tenant ${tenantId}:`, error);
        }
      }

      console.log(`Daily reports generated for ${tenantsSnapshot.size} tenants`);

    } catch (error) {
      console.error('Error in daily report generation:', error);
    }
  });

// ===== HELPER FUNCTIONS =====

/**
 * Calculate exhibition analytics
 */
async function calculateExhibitionAnalytics(exhibitionId: string, tenantId: string) {
  const [eventsSnapshot, tasksSnapshot, leadsSnapshot, expensesSnapshot] = await Promise.all([
    db.collection('exhibition_events')
      .where('exhibitionId', '==', exhibitionId)
      .where('tenantId', '==', tenantId)
      .get(),
    db.collection('exhibition_tasks')
      .where('exhibitionId', '==', exhibitionId)
      .where('tenantId', '==', tenantId)
      .get(),
    db.collection('lead_contacts')
      .where('exhibitionId', '==', exhibitionId)
      .where('tenantId', '==', tenantId)
      .get(),
    db.collection('expense_records')
      .where('activityId', '==', exhibitionId)
      .where('tenantId', '==', tenantId)
      .get()
  ]);

  // Calculate task statistics
  const totalTasks = tasksSnapshot.size;
  const completedTasks = tasksSnapshot.docs.filter(doc => doc.data().status === 'completed').length;
  const pendingTasks = tasksSnapshot.docs.filter(doc => doc.data().status === 'pending').length;
  const inProgressTasks = tasksSnapshot.docs.filter(doc => doc.data().status === 'in_progress').length;

  // Calculate lead statistics
  const totalLeads = leadsSnapshot.size;
  const qualifiedLeads = leadsSnapshot.docs.filter(doc => doc.data().status === 'qualified').length;
  const convertedLeads = leadsSnapshot.docs.filter(doc => doc.data().status === 'converted').length;

  // Calculate financial statistics
  const totalExpenses = expensesSnapshot.docs.reduce((sum, doc) => sum + (doc.data().amount || 0), 0);

  return {
    events: {
      total: eventsSnapshot.size
    },
    tasks: {
      total: totalTasks,
      completed: completedTasks,
      pending: pendingTasks,
      inProgress: inProgressTasks,
      completionRate: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0
    },
    leads: {
      total: totalLeads,
      qualified: qualifiedLeads,
      converted: convertedLeads,
      conversionRate: totalLeads > 0 ? (convertedLeads / totalLeads) * 100 : 0
    },
    financial: {
      totalExpenses
    }
  };
}

/**
 * Calculate lead analytics for a tenant
 */
async function calculateLeadAnalytics(tenantId: string) {
  const leadsSnapshot = await db.collection('lead_contacts')
    .where('tenantId', '==', tenantId)
    .get();

  const statusCounts = {
    new: 0,
    contacted: 0,
    qualified: 0,
    converted: 0,
    lost: 0
  };

  let totalLeadScore = 0;
  let leadsWithScore = 0;

  leadsSnapshot.docs.forEach(doc => {
    const data = doc.data();
    const status = data.status || 'new';
    
    if (statusCounts.hasOwnProperty(status)) {
      statusCounts[status as keyof typeof statusCounts]++;
    }

    if (typeof data.leadScore === 'number') {
      totalLeadScore += data.leadScore;
      leadsWithScore++;
    }
  });

  const averageLeadScore = leadsWithScore > 0 ? totalLeadScore / leadsWithScore : 0;
  const conversionRate = leadsSnapshot.size > 0 ? (statusCounts.converted / leadsSnapshot.size) * 100 : 0;

  return {
    total: leadsSnapshot.size,
    statusBreakdown: statusCounts,
    averageLeadScore,
    conversionRate
  };
}

/**
 * REFACTORED: Calculate financial analytics for a tenant using unified financials collection
 */
async function calculateFinancialAnalytics(tenantId: string) {
  // Use the new unified financials collection
  const financialsSnapshot = await db.collection('financials')
    .where('tenantId', '==', tenantId)
    .get();

  const budgets = financialsSnapshot.docs.filter(doc => doc.data().type === 'budget');
  const expenses = financialsSnapshot.docs.filter(doc => doc.data().type === 'expense');
  const purchaseRequests = financialsSnapshot.docs.filter(doc => doc.data().type === 'purchase_request');
  const purchaseOrders = financialsSnapshot.docs.filter(doc => doc.data().type === 'purchase_order');

  const totalBudget = budgets.reduce((sum, doc) => sum + (doc.data().totalBudget || 0), 0);
  const totalExpenses = expenses.reduce((sum, doc) => sum + (doc.data().amount || 0), 0);
  const budgetUtilization = totalBudget > 0 ? (totalExpenses / totalBudget) * 100 : 0;

  // Calculate expenses by status
  const expensesByStatus = {
    pending: 0,
    approved: 0,
    rejected: 0,
    paid: 0
  };

  expenses.forEach(doc => {
    const status = doc.data().status?.toLowerCase() || 'pending';
    if (expensesByStatus.hasOwnProperty(status)) {
      expensesByStatus[status as keyof typeof expensesByStatus]++;
    }
  });

  // Calculate purchase request metrics
  const purchaseRequestsByStatus = {
    draft: 0,
    submitted: 0,
    approved: 0,
    rejected: 0
  };

  purchaseRequests.forEach(doc => {
    const status = doc.data().status?.toLowerCase() || 'draft';
    if (purchaseRequestsByStatus.hasOwnProperty(status)) {
      purchaseRequestsByStatus[status as keyof typeof purchaseRequestsByStatus]++;
    }
  });

  // Calculate purchase order metrics
  const purchaseOrdersByStatus = {
    draft: 0,
    sent: 0,
    acknowledged: 0,
    fulfilled: 0,
    cancelled: 0
  };

  purchaseOrders.forEach(doc => {
    const status = doc.data().status?.toLowerCase() || 'draft';
    if (purchaseOrdersByStatus.hasOwnProperty(status)) {
      purchaseOrdersByStatus[status as keyof typeof purchaseOrdersByStatus]++;
    }
  });

  return {
    totalBudget,
    totalExpenses,
    budgetUtilization,
    totalBudgets: budgets.length,
    totalExpenseRecords: expenses.length,
    totalPurchaseRequests: purchaseRequests.length,
    totalPurchaseOrders: purchaseOrders.length,
    expensesByStatus,
    purchaseRequestsByStatus,
    purchaseOrdersByStatus,
    overBudgetActivities: [], // Would calculate based on activity grouping
    pendingApprovals: expenses.filter(doc => doc.data().status === 'Pending').length +
                     purchaseRequests.filter(doc => doc.data().status === 'Submitted').length
  };

  expensesSnapshot.docs.forEach(doc => {
    const status = doc.data().status || 'pending';
    if (expensesByStatus.hasOwnProperty(status)) {
      expensesByStatus[status as keyof typeof expensesByStatus] += doc.data().amount || 0;
    }
  });

  return {
    totalBudget,
    totalExpenses,
    budgetUtilization,
    remainingBudget: totalBudget - totalExpenses,
    expensesByStatus
  };
}

/**
 * Generate daily report for a tenant
 */
async function generateTenantDailyReport(tenantId: string) {
  const reportDate = new Date();
  reportDate.setHours(0, 0, 0, 0);

  // Get analytics data
  const [exhibitionAnalytics, leadAnalytics, financialAnalytics] = await Promise.all([
    db.collection('exhibition_analytics').where('tenantId', '==', tenantId).get(),
    db.collection('lead_analytics').doc(tenantId).get(),
    db.collection('financial_analytics').doc(tenantId).get()
  ]);

  const report = {
    tenantId,
    reportDate,
    exhibitions: {
      total: exhibitionAnalytics.size,
      analytics: exhibitionAnalytics.docs.map(doc => doc.data())
    },
    leads: leadAnalytics.exists ? leadAnalytics.data() : null,
    financial: financialAnalytics.exists ? financialAnalytics.data() : null,
    generatedAt: admin.firestore.FieldValue.serverTimestamp()
  };

  // Save the report
  await db.collection('daily_reports').add(report);
  
  console.log(`Daily report generated for tenant ${tenantId}`);
}
