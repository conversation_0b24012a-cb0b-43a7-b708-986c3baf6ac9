/**
 * Notification Cloud Functions
 * Handle automated notifications and alerts
 */

import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

const db = admin.firestore();

/**
 * Send notification when a task is assigned
 */
export const onTaskAssigned = functions.firestore
  .document('exhibition_tasks/{taskId}')
  .onUpdate(async (change, context) => {
    const beforeData = change.before.data();
    const afterData = change.after.data();
    
    // Check if assignedTo field changed
    if (beforeData.assignedTo !== afterData.assignedTo && afterData.assignedTo) {
      console.log(`Task ${context.params.taskId} assigned to ${afterData.assignedTo}`);
      
      // Queue notification
      await db.collection('notification_queue').add({
        type: 'task_assigned',
        userId: afterData.assignedTo,
        tenantId: afterData.tenantId,
        data: {
          taskId: context.params.taskId,
          taskTitle: afterData.title,
          exhibitionName: afterData.exhibitionName
        },
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      });
    }
  });

/**
 * Send event reminders
 */
export const onEventReminder = functions.pubsub
  .schedule('0 9 * * *') // Daily at 9 AM
  .timeZone('UTC')
  .onRun(async (context) => {
    console.log('Checking for event reminders...');
    
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    // Find events starting tomorrow
    const eventsSnapshot = await db.collection('exhibition_events')
      .where('startDate', '>=', tomorrow)
      .where('startDate', '<', new Date(tomorrow.getTime() + 24 * 60 * 60 * 1000))
      .get();
    
    for (const eventDoc of eventsSnapshot.docs) {
      const eventData = eventDoc.data();
      
      // Queue reminder notifications
      await db.collection('notification_queue').add({
        type: 'event_reminder',
        tenantId: eventData.tenantId,
        data: {
          eventId: eventDoc.id,
          eventName: eventData.name,
          startDate: eventData.startDate
        },
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      });
    }
  });

/**
 * Alert when budget is exceeded
 */
export const onBudgetExceeded = functions.firestore
  .document('expense_records/{expenseId}')
  .onCreate(async (snap, context) => {
    const expenseData = snap.data();
    
    // Check if this expense causes budget to be exceeded
    const budgetSnapshot = await db.collection('budget_allocations')
      .where('activityId', '==', expenseData.activityId)
      .where('tenantId', '==', expenseData.tenantId)
      .get();
    
    if (!budgetSnapshot.empty) {
      const budgetDoc = budgetSnapshot.docs[0];
      const budgetData = budgetDoc.data();
      
      // Calculate total expenses
      const expensesSnapshot = await db.collection('expense_records')
        .where('activityId', '==', expenseData.activityId)
        .where('tenantId', '==', expenseData.tenantId)
        .get();
      
      const totalExpenses = expensesSnapshot.docs.reduce((sum, doc) => sum + doc.data().amount, 0);
      
      if (totalExpenses > budgetData.amount) {
        // Queue budget exceeded alert
        await db.collection('notification_queue').add({
          type: 'budget_exceeded',
          tenantId: expenseData.tenantId,
          data: {
            activityId: expenseData.activityId,
            budgetAmount: budgetData.amount,
            totalExpenses: totalExpenses,
            overage: totalExpenses - budgetData.amount
          },
          priority: 'high',
          createdAt: admin.firestore.FieldValue.serverTimestamp()
        });
      }
    }
  });

/**
 * Notify when lead status changes
 */
export const onLeadStatusChanged = functions.firestore
  .document('lead_contacts/{leadId}')
  .onUpdate(async (change, context) => {
    const beforeData = change.before.data();
    const afterData = change.after.data();
    
    if (beforeData.status !== afterData.status) {
      console.log(`Lead ${context.params.leadId} status changed from ${beforeData.status} to ${afterData.status}`);
      
      // Queue status change notification
      await db.collection('notification_queue').add({
        type: 'lead_status_changed',
        tenantId: afterData.tenantId,
        data: {
          leadId: context.params.leadId,
          leadEmail: afterData.email,
          oldStatus: beforeData.status,
          newStatus: afterData.status,
          assignedTo: afterData.assignedTo
        },
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      });
    }
  });
