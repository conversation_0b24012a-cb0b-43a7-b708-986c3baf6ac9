/**
 * EVEXA Cloud Functions
 * Main entry point for all Cloud Functions
 */

import * as admin from 'firebase-admin';

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp();
}

// ===== DATA INTEGRITY FUNCTIONS =====
export {
  onExhibitionDeleted,
  onExhibitionUpdated,
  onUserDeleted,
  onVendorDeleted,
  validateTenantIdOnCreate,
  scheduledDataConsistencyCheck
} from './dataIntegrityFunctions';

// ===== TENANT MANAGEMENT FUNCTIONS =====
export {
  onTenantCreated,
  onTenantDeleted,
  onTenantUserAdded,
  onTenantUserRemoved
} from './tenantManagementFunctions';

// ===== NOTIFICATION FUNCTIONS =====
export {
  onTaskAssigned,
  onEventReminder,
  onBudgetExceeded,
  onLeadStatusChanged
} from './notificationFunctions';

// ===== ANALYTICS FUNCTIONS =====
export {
  updateExhibitionAnalytics,
  updateLeadAnalytics,
  updateFinancialAnalytics,
  generateDailyReports
} from './analyticsFunctions';

// ===== BACKUP AND MAINTENANCE FUNCTIONS =====
export {
  scheduledBackup,
  cleanupOldData,
  optimizeDatabase
} from './maintenanceFunctions';
