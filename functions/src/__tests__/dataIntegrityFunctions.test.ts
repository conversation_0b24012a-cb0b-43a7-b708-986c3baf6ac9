/**
 * Cloud Functions Data Integrity Tests
 * Tests for actual Cloud Functions using Firebase Functions Test SDK
 */

import * as admin from 'firebase-admin';
import * as test from 'firebase-functions-test';

// Initialize Firebase Functions Test
const testEnv = test();

// Mock Firebase Admin
const mockDb = {
  collection: jest.fn(),
  batch: jest.fn(() => ({
    delete: jest.fn(),
    update: jest.fn(),
    set: jest.fn(),
    commit: jest.fn().mockResolvedValue(undefined)
  }))
};

jest.mock('firebase-admin', () => ({
  apps: [],
  initializeApp: jest.fn(),
  firestore: jest.fn(() => mockDb),
  FieldValue: {
    serverTimestamp: jest.fn(() => new Date()),
    delete: jest.fn()
  }
}));

// Import functions after mocking
import {
  onExhibitionDeleted,
  onExhibitionUpdated,
  onUserDeleted,
  scheduledDataConsistencyCheck
} from '../dataIntegrityFunctions';

describe('Data Integrity Cloud Functions', () => {
  const testTenantId = 'test-tenant-123';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    testEnv.cleanup();
  });

  describe('onExhibitionDeleted Function', () => {
    test('should cascade delete all related documents', async () => {
      const exhibitionId = 'exhibition-123';
      const exhibitionData = {
        id: exhibitionId,
        tenantId: testTenantId,
        name: 'Test Exhibition'
      };

      // Mock related documents
      const mockRelatedDocs = [
        { id: 'event-1', ref: { delete: jest.fn() } },
        { id: 'task-1', ref: { delete: jest.fn() } },
        { id: 'lead-1', ref: { delete: jest.fn() } }
      ];

      // Mock Firestore collection queries
      mockDb.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockRelatedDocs,
            size: mockRelatedDocs.length
          })
        }))
      });

      // Create test data
      const beforeSnap = testEnv.firestore.makeDocumentSnapshot(
        exhibitionData,
        `exhibitions/${exhibitionId}`
      );

      const context = {
        params: { exhibitionId }
      };

      // Execute the function
      await onExhibitionDeleted(beforeSnap, context);

      // Verify cascade collections were queried
      const expectedCollections = [
        'exhibition_events',
        'exhibition_tasks',
        'lead_contacts',
        'budget_allocations',
        'expense_records',
        'booth_meetings',
        'booth_analytics'
      ];

      expectedCollections.forEach(collection => {
        expect(mockDb.collection).toHaveBeenCalledWith(collection);
      });

      // Verify batch operations were performed
      expect(mockDb.batch).toHaveBeenCalled();
    });

    test('should handle empty related collections gracefully', async () => {
      const exhibitionId = 'exhibition-456';
      const exhibitionData = {
        id: exhibitionId,
        tenantId: testTenantId,
        name: 'Empty Exhibition'
      };

      // Mock empty collections
      mockDb.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: [],
            size: 0
          })
        }))
      });

      const beforeSnap = testEnv.firestore.makeDocumentSnapshot(
        exhibitionData,
        `exhibitions/${exhibitionId}`
      );

      const context = {
        params: { exhibitionId }
      };

      // Should not throw error with empty collections
      await expect(onExhibitionDeleted(beforeSnap, context)).resolves.not.toThrow();
    });

    test('should respect Firestore batch limits', async () => {
      const exhibitionId = 'exhibition-789';
      const exhibitionData = {
        id: exhibitionId,
        tenantId: testTenantId,
        name: 'Large Exhibition'
      };

      // Mock large number of related documents (exceeding batch limit)
      const mockLargeDataset = Array.from({ length: 600 }, (_, i) => ({
        id: `doc-${i}`,
        ref: { delete: jest.fn() }
      }));

      mockDb.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockLargeDataset,
            size: mockLargeDataset.length
          })
        }))
      });

      const beforeSnap = testEnv.firestore.makeDocumentSnapshot(
        exhibitionData,
        `exhibitions/${exhibitionId}`
      );

      const context = {
        params: { exhibitionId }
      };

      // Should handle large datasets without error
      await expect(onExhibitionDeleted(beforeSnap, context)).resolves.not.toThrow();

      // Should respect batch limit of 500 operations
      const batchMock = mockDb.batch();
      expect(batchMock.delete).toHaveBeenCalledTimes(500); // Should stop at batch limit
    });
  });

  describe('onExhibitionUpdated Function', () => {
    test('should sync duplicated data when relevant fields change', async () => {
      const exhibitionId = 'exhibition-123';
      const beforeData = {
        id: exhibitionId,
        tenantId: testTenantId,
        name: 'Old Exhibition Name',
        venue: 'Old Venue'
      };

      const afterData = {
        id: exhibitionId,
        tenantId: testTenantId,
        name: 'New Exhibition Name',
        venue: 'New Venue'
      };

      // Mock related documents that need updating
      const mockRelatedDocs = [
        { id: 'task-1', ref: { update: jest.fn() } },
        { id: 'event-1', ref: { update: jest.fn() } }
      ];

      mockDb.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockRelatedDocs,
            size: mockRelatedDocs.length
          })
        }))
      });

      const beforeSnap = testEnv.firestore.makeDocumentSnapshot(
        beforeData,
        `exhibitions/${exhibitionId}`
      );

      const afterSnap = testEnv.firestore.makeDocumentSnapshot(
        afterData,
        `exhibitions/${exhibitionId}`
      );

      const change = testEnv.makeChange(beforeSnap, afterSnap);
      const context = {
        params: { exhibitionId }
      };

      // Execute the function
      await onExhibitionUpdated(change, context);

      // Verify collections with duplicated data were queried
      const expectedCollections = [
        'exhibition_events',
        'exhibition_tasks',
        'lead_contacts',
        'budget_allocations',
        'expense_records'
      ];

      expectedCollections.forEach(collection => {
        expect(mockDb.collection).toHaveBeenCalledWith(collection);
      });

      // Verify batch update was performed
      expect(mockDb.batch).toHaveBeenCalled();
    });

    test('should skip sync when no duplicated fields change', async () => {
      const exhibitionId = 'exhibition-456';
      const beforeData = {
        id: exhibitionId,
        tenantId: testTenantId,
        name: 'Exhibition Name',
        description: 'Old description'
      };

      const afterData = {
        id: exhibitionId,
        tenantId: testTenantId,
        name: 'Exhibition Name',
        description: 'New description'
      };

      const beforeSnap = testEnv.firestore.makeDocumentSnapshot(
        beforeData,
        `exhibitions/${exhibitionId}`
      );

      const afterSnap = testEnv.firestore.makeDocumentSnapshot(
        afterData,
        `exhibitions/${exhibitionId}`
      );

      const change = testEnv.makeChange(beforeSnap, afterSnap);
      const context = {
        params: { exhibitionId }
      };

      // Execute the function
      await onExhibitionUpdated(change, context);

      // Should not perform any database operations since no duplicated fields changed
      expect(mockDb.collection).not.toHaveBeenCalled();
      expect(mockDb.batch).not.toHaveBeenCalled();
    });
  });

  describe('onUserDeleted Function', () => {
    test('should clean up user references in related documents', async () => {
      const userId = 'user-123';
      const userData = {
        id: userId,
        tenantId: testTenantId,
        displayName: 'Test User'
      };

      // Mock documents that reference the user
      const mockReferencingDocs = [
        { id: 'task-1', ref: { update: jest.fn() } },
        { id: 'event-1', ref: { update: jest.fn() } },
        { id: 'expense-1', ref: { update: jest.fn() } }
      ];

      mockDb.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockReferencingDocs,
            size: mockReferencingDocs.length
          })
        }))
      });

      const beforeSnap = testEnv.firestore.makeDocumentSnapshot(
        userData,
        `user_profiles/${userId}`
      );

      const context = {
        params: { userId }
      };

      // Execute the function
      await onUserDeleted(beforeSnap, context);

      // Verify collections with user references were queried
      const expectedCollections = [
        'exhibition_tasks',
        'exhibition_events',
        'expense_records',
        'budget_allocations',
        'lead_contacts'
      ];

      expectedCollections.forEach(collection => {
        expect(mockDb.collection).toHaveBeenCalledWith(collection);
      });

      // Verify batch update was performed
      expect(mockDb.batch).toHaveBeenCalled();
    });

    test('should handle array field updates for team members', async () => {
      const userId = 'user-456';
      const userData = {
        id: userId,
        tenantId: testTenantId,
        displayName: 'Team Member'
      };

      // Mock exhibitions with team member arrays
      const mockExhibitions = [
        { 
          id: 'exhibition-1', 
          ref: { update: jest.fn() },
          data: () => ({ teamMemberIds: [userId, 'other-user'] })
        }
      ];

      mockDb.collection.mockImplementation((collectionName: string) => {
        if (collectionName === 'exhibitions') {
          return {
            where: jest.fn(() => ({
              get: jest.fn().mockResolvedValue({
                docs: mockExhibitions,
                size: mockExhibitions.length
              })
            }))
          };
        }
        
        return {
          where: jest.fn(() => ({
            get: jest.fn().mockResolvedValue({
              docs: [],
              size: 0
            })
          }))
        };
      });

      const beforeSnap = testEnv.firestore.makeDocumentSnapshot(
        userData,
        `user_profiles/${userId}`
      );

      const context = {
        params: { userId }
      };

      // Execute the function
      await onUserDeleted(beforeSnap, context);

      // Verify exhibitions collection was queried for array-contains
      expect(mockDb.collection).toHaveBeenCalledWith('exhibitions');
    });
  });

  describe('scheduledDataConsistencyCheck Function', () => {
    test('should perform scheduled consistency checks', async () => {
      // Mock the scheduled function context
      const context = {
        timestamp: new Date().toISOString(),
        eventId: 'test-event-id'
      };

      // Mock audit log collection
      mockDb.collection.mockImplementation((collectionName: string) => {
        if (collectionName === 'audit_logs') {
          return {
            add: jest.fn().mockResolvedValue({ id: 'audit-log-id' })
          };
        }
        
        return {
          get: jest.fn().mockResolvedValue({
            docs: [],
            size: 0
          })
        };
      });

      // Execute the scheduled function
      await scheduledDataConsistencyCheck(context);

      // Verify audit log was created
      expect(mockDb.collection).toHaveBeenCalledWith('audit_logs');
    });

    test('should handle consistency check errors gracefully', async () => {
      // Mock database error
      mockDb.collection.mockImplementation(() => {
        throw new Error('Database connection failed');
      });

      const context = {
        timestamp: new Date().toISOString(),
        eventId: 'test-event-id'
      };

      // Should not throw error even if consistency check fails
      await expect(scheduledDataConsistencyCheck(context)).resolves.not.toThrow();
    });
  });
});
