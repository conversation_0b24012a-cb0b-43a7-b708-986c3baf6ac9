/**
 * Cloud Functions for Data Integrity
 * Maintains referential integrity across collections with cascade operations
 */

import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

// ===== EXHIBITION CASCADE OPERATIONS =====

/**
 * When an exhibition is deleted, cascade delete all related documents
 */
export const onExhibitionDeleted = functions.firestore
  .document('exhibitions/{exhibitionId}')
  .onDelete(async (snap, context) => {
    const exhibitionId = context.params.exhibitionId;
    const exhibitionData = snap.data();
    
    console.log(`Exhibition deleted: ${exhibitionId}, cascading deletes...`);

    try {
      const batch = db.batch();
      let operationCount = 0;

      // Collections to cascade delete
      const cascadeCollections = [
        'exhibition_events',
        'exhibition_tasks',
        'lead_contacts',
        'budget_allocations',
        'expense_records',
        'booth_meetings',
        'booth_analytics'
      ];

      for (const collectionName of cascadeCollections) {
        const query = db.collection(collectionName)
          .where('exhibitionId', '==', exhibitionId)
          .where('tenantId', '==', exhibitionData.tenantId);
        
        const snapshot = await query.get();
        
        console.log(`Found ${snapshot.size} documents in ${collectionName} to delete`);
        
        snapshot.docs.forEach(doc => {
          if (operationCount < 500) { // Firestore batch limit
            batch.delete(doc.ref);
            operationCount++;
          }
        });
      }

      // Also delete related vendor assignments
      const vendorQuery = db.collection('vendor_profiles')
        .where('assignedExhibitions', 'array-contains', exhibitionId)
        .where('tenantId', '==', exhibitionData.tenantId);
      
      const vendorSnapshot = await vendorQuery.get();
      
      vendorSnapshot.docs.forEach(doc => {
        if (operationCount < 500) {
          const currentExhibitions = doc.data().assignedExhibitions || [];
          const updatedExhibitions = currentExhibitions.filter((id: string) => id !== exhibitionId);
          
          batch.update(doc.ref, {
            assignedExhibitions: updatedExhibitions,
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          });
          operationCount++;
        }
      });

      if (operationCount > 0) {
        await batch.commit();
        console.log(`Cascade delete completed: ${operationCount} operations`);
      }

      // Log the cascade operation
      await db.collection('audit_logs').add({
        type: 'cascade_delete',
        entityType: 'exhibition',
        entityId: exhibitionId,
        tenantId: exhibitionData.tenantId,
        operationsCount: operationCount,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        details: {
          collectionsAffected: cascadeCollections,
          exhibitionName: exhibitionData.name
        }
      });

    } catch (error) {
      console.error(`Error in exhibition cascade delete for ${exhibitionId}:`, error);
      
      // Log the error
      await db.collection('audit_logs').add({
        type: 'cascade_delete_error',
        entityType: 'exhibition',
        entityId: exhibitionId,
        tenantId: exhibitionData.tenantId,
        error: error.message,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });
      
      throw error;
    }
  });

/**
 * When an exhibition is updated, sync duplicated data
 */
export const onExhibitionUpdated = functions.firestore
  .document('exhibitions/{exhibitionId}')
  .onUpdate(async (change, context) => {
    const exhibitionId = context.params.exhibitionId;
    const beforeData = change.before.data();
    const afterData = change.after.data();
    
    // Check if fields that are duplicated in other collections have changed
    const duplicatedFields = ['name', 'startDate', 'endDate', 'venue', 'city', 'country'];
    const changedFields = duplicatedFields.filter(field => 
      JSON.stringify(beforeData[field]) !== JSON.stringify(afterData[field])
    );

    if (changedFields.length === 0) {
      return; // No duplicated fields changed
    }

    console.log(`Exhibition ${exhibitionId} updated, syncing duplicated data for fields: ${changedFields.join(', ')}`);

    try {
      const batch = db.batch();
      let operationCount = 0;

      // Collections that store duplicated exhibition data
      const collectionsWithDuplicatedData = [
        'exhibition_events',
        'exhibition_tasks',
        'lead_contacts',
        'budget_allocations',
        'expense_records'
      ];

      const updates: any = {
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      };

      // Build update object with changed fields
      changedFields.forEach(field => {
        switch (field) {
          case 'name':
            updates.exhibitionName = afterData.name;
            break;
          case 'startDate':
            updates.exhibitionStartDate = afterData.startDate;
            break;
          case 'endDate':
            updates.exhibitionEndDate = afterData.endDate;
            break;
          case 'venue':
            updates.exhibitionVenue = afterData.venue;
            break;
          case 'city':
            updates.exhibitionCity = afterData.city;
            break;
          case 'country':
            updates.exhibitionCountry = afterData.country;
            break;
        }
      });

      for (const collectionName of collectionsWithDuplicatedData) {
        const query = db.collection(collectionName)
          .where('exhibitionId', '==', exhibitionId)
          .where('tenantId', '==', afterData.tenantId);
        
        const snapshot = await query.get();
        
        console.log(`Updating ${snapshot.size} documents in ${collectionName}`);
        
        snapshot.docs.forEach(doc => {
          if (operationCount < 500) {
            batch.update(doc.ref, updates);
            operationCount++;
          }
        });
      }

      if (operationCount > 0) {
        await batch.commit();
        console.log(`Data sync completed: ${operationCount} operations`);
      }

      // Log the sync operation
      await db.collection('audit_logs').add({
        type: 'data_sync',
        entityType: 'exhibition',
        entityId: exhibitionId,
        tenantId: afterData.tenantId,
        operationsCount: operationCount,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        details: {
          changedFields,
          collectionsAffected: collectionsWithDuplicatedData
        }
      });

    } catch (error) {
      console.error(`Error in exhibition data sync for ${exhibitionId}:`, error);
      
      await db.collection('audit_logs').add({
        type: 'data_sync_error',
        entityType: 'exhibition',
        entityId: exhibitionId,
        tenantId: afterData.tenantId,
        error: error.message,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });
      
      throw error;
    }
  });

// ===== USER CASCADE OPERATIONS =====

/**
 * When a user is deleted, handle cleanup of user references
 */
export const onUserDeleted = functions.firestore
  .document('user_profiles/{userId}')
  .onDelete(async (snap, context) => {
    const userId = context.params.userId;
    const userData = snap.data();
    
    console.log(`User deleted: ${userId}, cleaning up references...`);

    try {
      const batch = db.batch();
      let operationCount = 0;

      // Collections that reference users
      const collectionsWithUserRefs = [
        { collection: 'exhibition_tasks', field: 'assignedTo' },
        { collection: 'exhibition_events', field: 'organizer' },
        { collection: 'expense_records', field: 'submittedBy' },
        { collection: 'budget_allocations', field: 'createdBy' },
        { collection: 'lead_contacts', field: 'assignedTo' }
      ];

      for (const { collection: collectionName, field } of collectionsWithUserRefs) {
        const query = db.collection(collectionName)
          .where(field, '==', userId)
          .where('tenantId', '==', userData.tenantId);
        
        const snapshot = await query.get();
        
        snapshot.docs.forEach(doc => {
          if (operationCount < 500) {
            batch.update(doc.ref, {
              [field]: null,
              [`${field}Deleted`]: true,
              [`${field}DeletedAt`]: admin.firestore.FieldValue.serverTimestamp(),
              updatedAt: admin.firestore.FieldValue.serverTimestamp()
            });
            operationCount++;
          }
        });
      }

      // Handle array fields (like team members)
      const exhibitionsQuery = db.collection('exhibitions')
        .where('teamMemberIds', 'array-contains', userId)
        .where('tenantId', '==', userData.tenantId);
      
      const exhibitionsSnapshot = await exhibitionsQuery.get();
      
      exhibitionsSnapshot.docs.forEach(doc => {
        if (operationCount < 500) {
          const currentTeam = doc.data().teamMemberIds || [];
          const updatedTeam = currentTeam.filter((id: string) => id !== userId);
          
          batch.update(doc.ref, {
            teamMemberIds: updatedTeam,
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          });
          operationCount++;
        }
      });

      if (operationCount > 0) {
        await batch.commit();
        console.log(`User reference cleanup completed: ${operationCount} operations`);
      }

      // Log the cleanup operation
      await db.collection('audit_logs').add({
        type: 'user_reference_cleanup',
        entityType: 'user',
        entityId: userId,
        tenantId: userData.tenantId,
        operationsCount: operationCount,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        details: {
          userEmail: userData.email,
          userName: userData.displayName
        }
      });

    } catch (error) {
      console.error(`Error in user reference cleanup for ${userId}:`, error);
      
      await db.collection('audit_logs').add({
        type: 'user_reference_cleanup_error',
        entityType: 'user',
        entityId: userId,
        tenantId: userData.tenantId,
        error: error.message,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });
      
      throw error;
    }
  });

// ===== VENDOR CASCADE OPERATIONS =====

/**
 * When a vendor is deleted, clean up vendor references
 */
export const onVendorDeleted = functions.firestore
  .document('vendor_profiles/{vendorId}')
  .onDelete(async (snap, context) => {
    const vendorId = context.params.vendorId;
    const vendorData = snap.data();
    
    console.log(`Vendor deleted: ${vendorId}, cleaning up references...`);

    try {
      const batch = db.batch();
      let operationCount = 0;

      // Clean up vendor references in expenses
      const expensesQuery = db.collection('expense_records')
        .where('vendorId', '==', vendorId)
        .where('tenantId', '==', vendorData.tenantId);
      
      const expensesSnapshot = await expensesQuery.get();
      
      expensesSnapshot.docs.forEach(doc => {
        if (operationCount < 500) {
          batch.update(doc.ref, {
            vendorId: null,
            vendorDeleted: true,
            vendorDeletedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          });
          operationCount++;
        }
      });

      // Clean up vendor references in purchase orders
      const purchaseOrdersQuery = db.collection('purchase_orders')
        .where('vendorId', '==', vendorId)
        .where('tenantId', '==', vendorData.tenantId);
      
      const purchaseOrdersSnapshot = await purchaseOrdersQuery.get();
      
      purchaseOrdersSnapshot.docs.forEach(doc => {
        if (operationCount < 500) {
          batch.update(doc.ref, {
            vendorId: null,
            vendorDeleted: true,
            vendorDeletedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          });
          operationCount++;
        }
      });

      if (operationCount > 0) {
        await batch.commit();
        console.log(`Vendor reference cleanup completed: ${operationCount} operations`);
      }

      // Log the cleanup operation
      await db.collection('audit_logs').add({
        type: 'vendor_reference_cleanup',
        entityType: 'vendor',
        entityId: vendorId,
        tenantId: vendorData.tenantId,
        operationsCount: operationCount,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        details: {
          vendorName: vendorData.name
        }
      });

    } catch (error) {
      console.error(`Error in vendor reference cleanup for ${vendorId}:`, error);
      
      await db.collection('audit_logs').add({
        type: 'vendor_reference_cleanup_error',
        entityType: 'vendor',
        entityId: vendorId,
        tenantId: vendorData.tenantId,
        error: error.message,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });
      
      throw error;
    }
  });

// ===== TENANT ID VALIDATION =====

/**
 * Validate tenantId on document creation
 */
export const validateTenantIdOnCreate = functions.firestore
  .document('{collectionId}/{docId}')
  .onCreate(async (snap, context) => {
    const data = snap.data();
    const collectionId = context.params.collectionId;
    const docId = context.params.docId;

    // Skip system collections
    const systemCollections = ['audit_logs', 'tenants', 'tenant-users'];
    if (systemCollections.includes(collectionId)) {
      return;
    }

    // Check if document has tenantId
    if (!data.tenantId) {
      console.error(`Document created without tenantId: ${collectionId}/${docId}`);
      
      // Log the violation
      await db.collection('audit_logs').add({
        type: 'tenant_id_violation',
        entityType: collectionId,
        entityId: docId,
        violation: 'missing_tenant_id',
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        details: {
          documentData: data
        }
      });

      // Optionally delete the document or add a default tenantId
      // For now, we'll just log it
    }
  });

// ===== DATA CONSISTENCY CHECKER =====

/**
 * Scheduled function to check data consistency
 */
export const scheduledDataConsistencyCheck = functions.pubsub
  .schedule('0 2 * * *') // Run daily at 2 AM
  .timeZone('UTC')
  .onRun(async (context) => {
    console.log('Running scheduled data consistency check...');

    try {
      // Check for orphaned documents
      const orphanedCount = await checkOrphanedDocuments();
      
      // Check for missing tenantIds
      const missingTenantIdCount = await checkMissingTenantIds();
      
      // Log the results
      await db.collection('audit_logs').add({
        type: 'scheduled_consistency_check',
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        results: {
          orphanedDocuments: orphanedCount,
          missingTenantIds: missingTenantIdCount
        }
      });

      console.log(`Consistency check completed: ${orphanedCount} orphaned, ${missingTenantIdCount} missing tenantId`);

    } catch (error) {
      console.error('Error in scheduled consistency check:', error);
      
      await db.collection('audit_logs').add({
        type: 'scheduled_consistency_check_error',
        error: error.message,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });
    }
  });

/**
 * Check for orphaned documents
 */
async function checkOrphanedDocuments(): Promise<number> {
  let orphanedCount = 0;

  // Check tasks without valid exhibitions
  const tasksSnapshot = await db.collection('exhibition_tasks').get();
  
  for (const taskDoc of tasksSnapshot.docs) {
    const taskData = taskDoc.data();
    if (taskData.exhibitionId) {
      const exhibitionDoc = await db.collection('exhibitions').doc(taskData.exhibitionId).get();
      if (!exhibitionDoc.exists) {
        orphanedCount++;
        console.log(`Orphaned task found: ${taskDoc.id} references non-existent exhibition ${taskData.exhibitionId}`);
      }
    }
  }

  return orphanedCount;
}

/**
 * Check for documents missing tenantId
 */
async function checkMissingTenantIds(): Promise<number> {
  let missingCount = 0;
  
  const collections = ['exhibitions', 'exhibition_events', 'exhibition_tasks', 'lead_contacts'];
  
  for (const collectionName of collections) {
    const snapshot = await db.collection(collectionName).get();
    
    for (const doc of snapshot.docs) {
      if (!doc.data().tenantId) {
        missingCount++;
        console.log(`Document missing tenantId: ${collectionName}/${doc.id}`);
      }
    }
  }

  return missingCount;
}
