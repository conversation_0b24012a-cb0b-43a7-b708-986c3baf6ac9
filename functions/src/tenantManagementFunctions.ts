/**
 * Tenant Management Cloud Functions
 * Handle tenant lifecycle and user management
 */

import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

const db = admin.firestore();

// ===== TENANT LIFECYCLE FUNCTIONS =====

/**
 * When a new tenant is created, initialize default collections and settings
 */
export const onTenantCreated = functions.firestore
  .document('tenants/{tenantId}')
  .onCreate(async (snap, context) => {
    const tenantId = context.params.tenantId;
    const tenantData = snap.data();
    
    console.log(`New tenant created: ${tenantId}, initializing...`);

    try {
      const batch = db.batch();

      // Create default settings for the tenant
      const settingsRef = db.collection('tenant_settings').doc(tenantId);
      batch.set(settingsRef, {
        tenantId,
        theme: 'default',
        timezone: 'UTC',
        currency: 'USD',
        language: 'en',
        features: {
          analytics: true,
          reporting: true,
          integrations: true,
          customFields: true
        },
        limits: {
          maxUsers: tenantData.plan === 'enterprise' ? 1000 : tenantData.plan === 'professional' ? 10 : 5,
          maxExhibitions: tenantData.plan === 'enterprise' ? 1000 : tenantData.plan === 'professional' ? 50 : 10,
          maxStorage: tenantData.plan === 'enterprise' ? 100 : tenantData.plan === 'professional' ? 10 : 1 // GB
        },
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      // Create default user roles for the tenant
      const rolesRef = db.collection('tenant_roles').doc(tenantId);
      batch.set(rolesRef, {
        tenantId,
        roles: {
          admin: {
            name: 'Administrator',
            permissions: ['*'],
            description: 'Full access to all features'
          },
          management: {
            name: 'Management',
            permissions: [
              'exhibitions.read',
              'exhibitions.write',
              'events.read',
              'events.write',
              'tasks.read',
              'tasks.write',
              'leads.read',
              'leads.write',
              'analytics.read',
              'reports.read'
            ],
            description: 'Management level access'
          },
          user: {
            name: 'User',
            permissions: [
              'exhibitions.read',
              'events.read',
              'tasks.read',
              'tasks.write',
              'leads.read'
            ],
            description: 'Standard user access'
          }
        },
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      // Create default notification settings
      const notificationsRef = db.collection('tenant_notifications').doc(tenantId);
      batch.set(notificationsRef, {
        tenantId,
        emailNotifications: {
          taskAssignments: true,
          eventReminders: true,
          budgetAlerts: true,
          leadUpdates: true,
          systemUpdates: true
        },
        pushNotifications: {
          taskDeadlines: true,
          eventReminders: true,
          urgentAlerts: true
        },
        notificationFrequency: 'immediate',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      // Create audit log entry
      const auditRef = db.collection('audit_logs').doc();
      batch.set(auditRef, {
        type: 'tenant_created',
        tenantId,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        details: {
          tenantName: tenantData.name,
          plan: tenantData.plan,
          adminEmail: tenantData.adminEmail
        }
      });

      await batch.commit();
      
      console.log(`Tenant initialization completed for: ${tenantId}`);

      // Send welcome email to tenant admin (would integrate with email service)
      await sendTenantWelcomeEmail(tenantData);

    } catch (error) {
      console.error(`Error initializing tenant ${tenantId}:`, error);
      
      // Log the error
      await db.collection('audit_logs').add({
        type: 'tenant_creation_error',
        tenantId,
        error: error.message,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });
      
      throw error;
    }
  });

/**
 * When a tenant is deleted, clean up all tenant data
 */
export const onTenantDeleted = functions.firestore
  .document('tenants/{tenantId}')
  .onDelete(async (snap, context) => {
    const tenantId = context.params.tenantId;
    const tenantData = snap.data();
    
    console.log(`Tenant deleted: ${tenantId}, cleaning up all data...`);

    try {
      // Collections to clean up
      const collectionsToCleanup = [
        'user_profiles',
        'exhibitions',
        'exhibition_events',
        'exhibition_tasks',
        'lead_contacts',
        'budget_allocations',
        'expense_records',
        'vendor_profiles',
        'tenant_settings',
        'tenant_roles',
        'tenant_notifications'
      ];

      let totalDeleted = 0;

      for (const collectionName of collectionsToCleanup) {
        const query = db.collection(collectionName).where('tenantId', '==', tenantId);
        
        // Delete in batches to avoid timeout
        let batch = db.batch();
        let batchCount = 0;
        
        const snapshot = await query.get();
        
        for (const doc of snapshot.docs) {
          batch.delete(doc.ref);
          batchCount++;
          totalDeleted++;
          
          if (batchCount >= 500) {
            await batch.commit();
            batch = db.batch();
            batchCount = 0;
          }
        }
        
        if (batchCount > 0) {
          await batch.commit();
        }
        
        console.log(`Deleted ${snapshot.size} documents from ${collectionName}`);
      }

      // Log the cleanup operation
      await db.collection('audit_logs').add({
        type: 'tenant_data_cleanup',
        tenantId,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        details: {
          tenantName: tenantData.name,
          totalDocumentsDeleted: totalDeleted,
          collectionsAffected: collectionsToCleanup
        }
      });

      console.log(`Tenant cleanup completed: ${totalDeleted} documents deleted`);

    } catch (error) {
      console.error(`Error cleaning up tenant ${tenantId}:`, error);
      
      await db.collection('audit_logs').add({
        type: 'tenant_cleanup_error',
        tenantId,
        error: error.message,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });
      
      throw error;
    }
  });

// ===== TENANT USER MANAGEMENT =====

/**
 * When a user is added to a tenant, set up user permissions and notifications
 */
export const onTenantUserAdded = functions.firestore
  .document('user_profiles/{userId}')
  .onCreate(async (snap, context) => {
    const userId = context.params.userId;
    const userData = snap.data();
    
    if (!userData.tenantId) {
      return; // Skip if no tenant association
    }

    console.log(`New user added to tenant ${userData.tenantId}: ${userId}`);

    try {
      const batch = db.batch();

      // Create user permissions based on role
      const permissionsRef = db.collection('user_permissions').doc(userId);
      const tenantRoles = await db.collection('tenant_roles').doc(userData.tenantId).get();
      
      if (tenantRoles.exists) {
        const rolesData = tenantRoles.data();
        const userRole = rolesData?.roles[userData.role] || rolesData?.roles['user'];
        
        batch.set(permissionsRef, {
          userId,
          tenantId: userData.tenantId,
          role: userData.role,
          permissions: userRole.permissions || [],
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
      }

      // Create user notification preferences
      const notificationPrefsRef = db.collection('user_notification_preferences').doc(userId);
      batch.set(notificationPrefsRef, {
        userId,
        tenantId: userData.tenantId,
        emailNotifications: {
          taskAssignments: true,
          eventReminders: true,
          mentions: true,
          weeklyDigest: true
        },
        pushNotifications: {
          taskDeadlines: true,
          eventReminders: true,
          mentions: true
        },
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      // Update tenant user count
      const tenantRef = db.collection('tenants').doc(userData.tenantId);
      batch.update(tenantRef, {
        userCount: admin.firestore.FieldValue.increment(1),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      // Log the user addition
      const auditRef = db.collection('audit_logs').doc();
      batch.set(auditRef, {
        type: 'user_added_to_tenant',
        tenantId: userData.tenantId,
        userId,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        details: {
          userEmail: userData.email,
          userRole: userData.role,
          userName: userData.displayName
        }
      });

      await batch.commit();
      
      console.log(`User setup completed for ${userId} in tenant ${userData.tenantId}`);

      // Send welcome email to user
      await sendUserWelcomeEmail(userData);

    } catch (error) {
      console.error(`Error setting up user ${userId}:`, error);
      
      await db.collection('audit_logs').add({
        type: 'user_setup_error',
        tenantId: userData.tenantId,
        userId,
        error: error.message,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });
      
      throw error;
    }
  });

/**
 * When a user is removed from a tenant, clean up user data
 */
export const onTenantUserRemoved = functions.firestore
  .document('user_profiles/{userId}')
  .onDelete(async (snap, context) => {
    const userId = context.params.userId;
    const userData = snap.data();
    
    if (!userData.tenantId) {
      return; // Skip if no tenant association
    }

    console.log(`User removed from tenant ${userData.tenantId}: ${userId}`);

    try {
      const batch = db.batch();

      // Remove user permissions
      const permissionsRef = db.collection('user_permissions').doc(userId);
      batch.delete(permissionsRef);

      // Remove user notification preferences
      const notificationPrefsRef = db.collection('user_notification_preferences').doc(userId);
      batch.delete(notificationPrefsRef);

      // Update tenant user count
      const tenantRef = db.collection('tenants').doc(userData.tenantId);
      batch.update(tenantRef, {
        userCount: admin.firestore.FieldValue.increment(-1),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      // Log the user removal
      const auditRef = db.collection('audit_logs').doc();
      batch.set(auditRef, {
        type: 'user_removed_from_tenant',
        tenantId: userData.tenantId,
        userId,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        details: {
          userEmail: userData.email,
          userRole: userData.role,
          userName: userData.displayName
        }
      });

      await batch.commit();
      
      console.log(`User cleanup completed for ${userId} in tenant ${userData.tenantId}`);

    } catch (error) {
      console.error(`Error cleaning up user ${userId}:`, error);
      
      await db.collection('audit_logs').add({
        type: 'user_cleanup_error',
        tenantId: userData.tenantId,
        userId,
        error: error.message,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });
      
      throw error;
    }
  });

// ===== HELPER FUNCTIONS =====

/**
 * Send welcome email to new tenant admin
 */
async function sendTenantWelcomeEmail(tenantData: any): Promise<void> {
  // This would integrate with your email service (SendGrid, Mailgun, etc.)
  console.log(`Sending welcome email to tenant admin: ${tenantData.adminEmail}`);
  
  // Email content would include:
  // - Welcome message
  // - Getting started guide
  // - Login instructions
  // - Support contact information
  
  // For now, just log the action
  await db.collection('email_queue').add({
    type: 'tenant_welcome',
    to: tenantData.adminEmail,
    templateData: {
      tenantName: tenantData.name,
      adminName: tenantData.adminName,
      loginUrl: `https://app.evexa.com/login?tenant=${tenantData.id}`
    },
    priority: 'high',
    createdAt: admin.firestore.FieldValue.serverTimestamp()
  });
}

/**
 * Send welcome email to new user
 */
async function sendUserWelcomeEmail(userData: any): Promise<void> {
  console.log(`Sending welcome email to user: ${userData.email}`);
  
  await db.collection('email_queue').add({
    type: 'user_welcome',
    to: userData.email,
    templateData: {
      userName: userData.displayName,
      tenantName: userData.tenantName,
      role: userData.role,
      loginUrl: `https://app.evexa.com/login`
    },
    priority: 'normal',
    createdAt: admin.firestore.FieldValue.serverTimestamp()
  });
}
