/**
 * Maintenance Cloud Functions
 * Handle database backup, cleanup, and optimization tasks
 */

import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

const db = admin.firestore();

/**
 * Scheduled backup of critical data
 */
export const scheduledBackup = functions.pubsub
  .schedule('0 3 * * 0') // Weekly on Sunday at 3 AM
  .timeZone('UTC')
  .onRun(async (context) => {
    console.log('Starting scheduled backup...');

    try {
      const backupId = `backup_${new Date().toISOString().split('T')[0]}`;
      
      // Collections to backup
      const collectionsToBackup = [
        'tenants',
        'user_profiles',
        'exhibitions',
        'exhibition_events',
        'exhibition_tasks',
        'lead_contacts',
        'budget_allocations',
        'expense_records',
        'vendor_profiles'
      ];

      let totalDocuments = 0;

      for (const collectionName of collectionsToBackup) {
        const snapshot = await db.collection(collectionName).get();
        
        if (!snapshot.empty) {
          const batch = db.batch();
          let batchCount = 0;

          for (const doc of snapshot.docs) {
            const backupRef = db.collection('backups')
              .doc(backupId)
              .collection(collectionName)
              .doc(doc.id);
            
            batch.set(backupRef, {
              ...doc.data(),
              backedUpAt: admin.firestore.FieldValue.serverTimestamp()
            });
            
            batchCount++;
            totalDocuments++;
            
            if (batchCount >= 500) {
              await batch.commit();
              batchCount = 0;
            }
          }
          
          if (batchCount > 0) {
            await batch.commit();
          }
        }
        
        console.log(`Backed up ${snapshot.size} documents from ${collectionName}`);
      }

      // Create backup metadata
      await db.collection('backup_metadata').doc(backupId).set({
        backupId,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        totalDocuments,
        collections: collectionsToBackup,
        status: 'completed'
      });

      console.log(`Backup completed: ${totalDocuments} documents backed up`);

    } catch (error) {
      console.error('Error in scheduled backup:', error);
      
      await db.collection('audit_logs').add({
        type: 'backup_error',
        error: error.message,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });
    }
  });

/**
 * Clean up old data and temporary files
 */
export const cleanupOldData = functions.pubsub
  .schedule('0 4 * * *') // Daily at 4 AM
  .timeZone('UTC')
  .onRun(async (context) => {
    console.log('Starting data cleanup...');

    try {
      let totalDeleted = 0;

      // Clean up old audit logs (older than 90 days)
      const auditCutoff = new Date();
      auditCutoff.setDate(auditCutoff.getDate() - 90);
      
      const oldAuditLogs = await db.collection('audit_logs')
        .where('timestamp', '<', auditCutoff)
        .limit(1000)
        .get();

      if (!oldAuditLogs.empty) {
        const batch = db.batch();
        oldAuditLogs.docs.forEach(doc => {
          batch.delete(doc.ref);
          totalDeleted++;
        });
        await batch.commit();
        console.log(`Deleted ${oldAuditLogs.size} old audit logs`);
      }

      // Clean up old notification queue items (older than 30 days)
      const notificationCutoff = new Date();
      notificationCutoff.setDate(notificationCutoff.getDate() - 30);
      
      const oldNotifications = await db.collection('notification_queue')
        .where('createdAt', '<', notificationCutoff)
        .limit(1000)
        .get();

      if (!oldNotifications.empty) {
        const batch = db.batch();
        oldNotifications.docs.forEach(doc => {
          batch.delete(doc.ref);
          totalDeleted++;
        });
        await batch.commit();
        console.log(`Deleted ${oldNotifications.size} old notifications`);
      }

      // Clean up old email queue items (older than 7 days)
      const emailCutoff = new Date();
      emailCutoff.setDate(emailCutoff.getDate() - 7);
      
      const oldEmails = await db.collection('email_queue')
        .where('createdAt', '<', emailCutoff)
        .where('status', '==', 'sent')
        .limit(1000)
        .get();

      if (!oldEmails.empty) {
        const batch = db.batch();
        oldEmails.docs.forEach(doc => {
          batch.delete(doc.ref);
          totalDeleted++;
        });
        await batch.commit();
        console.log(`Deleted ${oldEmails.size} old email records`);
      }

      // Clean up old backups (older than 6 months)
      const backupCutoff = new Date();
      backupCutoff.setMonth(backupCutoff.getMonth() - 6);
      
      const oldBackups = await db.collection('backup_metadata')
        .where('createdAt', '<', backupCutoff)
        .get();

      for (const backupDoc of oldBackups.docs) {
        const backupId = backupDoc.id;
        
        // Delete backup collections
        const collectionsToDelete = backupDoc.data().collections || [];
        
        for (const collectionName of collectionsToDelete) {
          const backupCollection = await db.collection('backups')
            .doc(backupId)
            .collection(collectionName)
            .get();
          
          if (!backupCollection.empty) {
            const batch = db.batch();
            backupCollection.docs.forEach(doc => {
              batch.delete(doc.ref);
            });
            await batch.commit();
          }
        }
        
        // Delete backup metadata
        await backupDoc.ref.delete();
        totalDeleted++;
        console.log(`Deleted old backup: ${backupId}`);
      }

      // Log cleanup results
      await db.collection('audit_logs').add({
        type: 'data_cleanup',
        totalDeleted,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });

      console.log(`Data cleanup completed: ${totalDeleted} items deleted`);

    } catch (error) {
      console.error('Error in data cleanup:', error);
      
      await db.collection('audit_logs').add({
        type: 'cleanup_error',
        error: error.message,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });
    }
  });

/**
 * Optimize database performance
 */
export const optimizeDatabase = functions.pubsub
  .schedule('0 5 * * 0') // Weekly on Sunday at 5 AM
  .timeZone('UTC')
  .onRun(async (context) => {
    console.log('Starting database optimization...');

    try {
      // Update search indexes for better query performance
      await updateSearchIndexes();
      
      // Analyze and optimize collection structures
      await analyzeCollectionUsage();
      
      // Clean up unused indexes
      await cleanupUnusedIndexes();
      
      console.log('Database optimization completed');

    } catch (error) {
      console.error('Error in database optimization:', error);
      
      await db.collection('audit_logs').add({
        type: 'optimization_error',
        error: error.message,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });
    }
  });

// ===== HELPER FUNCTIONS =====

/**
 * Update search indexes for better performance
 */
async function updateSearchIndexes() {
  console.log('Updating search indexes...');
  
  // This would typically involve updating search service indexes
  // For now, we'll just log the action
  
  await db.collection('audit_logs').add({
    type: 'search_indexes_updated',
    timestamp: admin.firestore.FieldValue.serverTimestamp()
  });
}

/**
 * Analyze collection usage patterns
 */
async function analyzeCollectionUsage() {
  console.log('Analyzing collection usage...');
  
  const collections = [
    'exhibitions',
    'exhibition_events', 
    'exhibition_tasks',
    'lead_contacts',
    'budget_allocations',
    'expense_records'
  ];
  
  const usageStats = {};
  
  for (const collectionName of collections) {
    const snapshot = await db.collection(collectionName).get();
    
    // Calculate basic statistics
    const totalDocs = snapshot.size;
    let totalSize = 0;
    let lastUpdated = null;
    
    snapshot.docs.forEach(doc => {
      const data = doc.data();
      totalSize += JSON.stringify(data).length;
      
      if (data.updatedAt && (!lastUpdated || data.updatedAt > lastUpdated)) {
        lastUpdated = data.updatedAt;
      }
    });
    
    usageStats[collectionName] = {
      totalDocuments: totalDocs,
      estimatedSize: totalSize,
      lastUpdated,
      avgDocSize: totalDocs > 0 ? totalSize / totalDocs : 0
    };
  }
  
  // Store usage statistics
  await db.collection('database_stats').doc('collection_usage').set({
    stats: usageStats,
    analyzedAt: admin.firestore.FieldValue.serverTimestamp()
  });
  
  console.log('Collection usage analysis completed');
}

/**
 * Clean up unused indexes (placeholder)
 */
async function cleanupUnusedIndexes() {
  console.log('Cleaning up unused indexes...');
  
  // This would involve analyzing query patterns and removing unused composite indexes
  // For now, we'll just log the action
  
  await db.collection('audit_logs').add({
    type: 'unused_indexes_cleaned',
    timestamp: admin.firestore.FieldValue.serverTimestamp()
  });
}
