{"compilerOptions": {"module": "commonjs", "noImplicitReturns": true, "noUnusedLocals": true, "outDir": "lib", "sourceMap": true, "strict": true, "target": "es2017", "declaration": true, "declarationMap": true, "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "importHelpers": true, "incremental": true, "lib": ["es2017"], "moduleResolution": "node", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitThis": true, "resolveJsonModule": true, "skipLibCheck": true, "typeRoots": ["node_modules/@types"]}, "compileOnSave": true, "include": ["src"], "exclude": ["node_modules", "lib", "**/*.test.ts"]}