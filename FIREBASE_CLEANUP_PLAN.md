# 🔥 Firebase Database Cleanup Plan

## Current Situation
Your Firebase database has **chaotic structure** with mixed naming conventions:
- `aiConfiguration` (camelCase)
- `attendee_profiles` (snake_case)  
- `global-settings` (kebab-case)
- `tenant-data` (kebab-case)
- Multiple unused collections

## ✅ Collections to KEEP (Actually Used)
```
✅ tasks                 - Used in task management
✅ users                 - User authentication  
✅ user_profiles         - User profile data
✅ user_groups           - User group management
✅ user_settings         - User preferences
✅ tenants               - Tenant management
✅ tenant-data           - Tenant-scoped data
✅ tenant-users          - Tenant user relationships
✅ vendor_profiles       - Vendor management
✅ vendor_contracts      - Vendor contracts
✅ vendor_reviews        - Vendor reviews
✅ purchase_requests     - Purchase workflow
✅ purchase_orders       - Purchase orders
✅ purchase_invoices     - Invoice management
✅ expense_records       - Expense tracking
✅ attendee_profiles     - Attendee management
✅ security_events       - Security logging
```

## ❌ Collections to DELETE (Unused/Orphaned)
```
❌ aiConfiguration       - Not referenced in code
❌ business_metrics      - Not referenced in code
❌ evexa_business_metrics - Not referenced in code
❌ global-settings       - Not referenced in code
❌ lead_communications   - Not referenced in code
❌ lead_segments         - Not referenced in code
❌ subscription_plans    - Not referenced in code
❌ tenant_settings       - Not referenced in code
```

## 🎯 Target Structure (After Cleanup)
```
Firebase Structure:
├── tenants/
│   ├── {tenantId}/
│   │   ├── users/           → user_profiles
│   │   ├── tasks/           → exhibition_tasks
│   │   ├── events/          → exhibition_events
│   │   ├── exhibitions/     → exhibitions
│   │   ├── vendors/         → vendor_profiles
│   │   ├── expenses/        → expense_records
│   │   ├── purchases/       → purchase_requests
│   │   └── attendees/       → attendee_profiles
│   └── {anotherTenantId}/   → Completely isolated
└── superman/
    ├── admin_data/          → super_admin_data
    └── system_logs/         → security_events
```

## 🚀 How to Execute Cleanup

### Step 1: Analyze Current Structure
```bash
npm run firebase:analyze
```

### Step 2: Dry Run (Safe Preview)
```bash
npm run firebase:cleanup
```

### Step 3: Execute Cleanup (DANGER!)
```bash
npm run firebase:cleanup:execute
```

## ⚠️ Safety Features
- **Dry run by default** - No changes unless `--execute` flag
- **Backup recommendations** - Export data before cleanup
- **Incremental approach** - Delete unused first, migrate second
- **Rollback plan** - Keep backups of deleted collections

## 📋 Cleanup Steps
1. ✅ **Analyze** - Scan current structure
2. ✅ **Delete Unused** - Remove orphaned collections  
3. ✅ **Migrate Data** - Move to tenant-isolated structure
4. ✅ **Update Code** - Fix collection references
5. ✅ **Test** - Verify everything works

## 🔒 Security Benefits After Cleanup
- ✅ **Proper tenant isolation**
- ✅ **Consistent naming conventions**
- ✅ **Reduced attack surface**
- ✅ **Better performance**
- ✅ **Easier maintenance**

---

**Ready to start clean? Run the analysis first!**
```bash
npm run firebase:analyze
```
