{"indexes": [{"collectionGroup": "user_profiles", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "exhibitions", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "startDate", "order": "DESCENDING"}]}, {"collectionGroup": "exhibitions", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "startDate", "order": "ASCENDING"}]}, {"collectionGroup": "exhibition_events", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "startDate", "order": "DESCENDING"}]}, {"collectionGroup": "exhibition_events", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "exhibitionId", "order": "ASCENDING"}, {"fieldPath": "startDate", "order": "ASCENDING"}]}, {"collectionGroup": "exhibition_tasks", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "dueDate", "order": "ASCENDING"}]}, {"collectionGroup": "exhibition_tasks", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "exhibitionId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "exhibition_tasks", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "assignedTo", "order": "ASCENDING"}, {"fieldPath": "dueDate", "order": "ASCENDING"}]}, {"collectionGroup": "lead_contacts", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "lead_contacts", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "exhibitionId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "financials", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "financials", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "exhibitionId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}]}], "fieldOverrides": []}