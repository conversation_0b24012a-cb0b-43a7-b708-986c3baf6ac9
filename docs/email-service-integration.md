# EVEXA Email Service Integration

## Overview

EVEXA now includes a production-ready email service integration using SendGrid for reliable email delivery with comprehensive tracking, analytics, and quota management. This system supports tenant-specific branding, rate limiting, and real-time email event tracking.

## Features

### 🚀 Core Email Services
- **SendGrid Integration**: Production-ready email delivery with high deliverability
- **Template System**: Professional email templates with tenant branding
- **Quota Management**: Daily and monthly email limits with automatic enforcement
- **Rate Limiting**: Configurable rate limits to prevent abuse
- **Analytics Tracking**: Real-time email event tracking (opens, clicks, bounces)

### 📧 Email Types Supported
- **User Invitations**: Professional invitation emails with role assignment
- **Welcome Messages**: Onboarding emails for new users
- **Password Reset**: Secure password reset with time-limited links
- **System Notifications**: Various system-generated communications
- **Custom Emails**: Flexible custom email sending with templates

### 🎨 Tenant Customization
- **Custom Branding**: Tenant logos, colors, and company information
- **From Address**: Tenant-specific from addresses and names
- **Email Signatures**: Custom email signatures and footers
- **Unsubscribe Handling**: Automated unsubscribe management

## Configuration

### Environment Variables

Add the following to your `.env` file:

```bash
# SendGrid Configuration
SENDGRID_API_KEY=SG.your_sendgrid_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=EVEXA Platform
SENDGRID_REPLY_TO=<EMAIL>

# Email Service Settings
EMAIL_SERVICE_PROVIDER=sendgrid
EMAIL_RATE_LIMIT_PER_MINUTE=100
EMAIL_RETRY_ATTEMPTS=3
EMAIL_QUEUE_ENABLED=true

# Webhook Configuration (Optional)
SENDGRID_WEBHOOK_PUBLIC_KEY=your_webhook_verification_key
```

### SendGrid Setup

1. **Create SendGrid Account**: Sign up at [SendGrid](https://sendgrid.com)
2. **Generate API Key**: Create an API key with full access permissions
3. **Configure Domain**: Set up domain authentication for better deliverability
4. **Set Up Webhooks**: Configure webhook URL for event tracking

## Usage Examples

### Basic Email Sending

```typescript
import { EnhancedEmailService } from '@/services/enhancedEmailService';

const emailService = new EnhancedEmailService('tenant-id');

const result = await emailService.sendEmail({
  tenantId: 'tenant-id',
  to: '<EMAIL>',
  subject: 'Welcome to EVEXA',
  html: '<h1>Welcome!</h1><p>Thank you for joining us.</p>',
  priority: 'high',
  trackOpens: true,
  trackClicks: true,
  tags: ['welcome', 'onboarding']
});

if (result.success) {
  console.log('Email sent:', result.messageId);
  console.log('Quota remaining:', result.quotaRemaining);
} else {
  console.error('Email failed:', result.error);
}
```

### Sending Invitation Emails

```typescript
const result = await emailService.sendInvitationEmail(
  '<EMAIL>',
  'John Doe',
  'Admin User',
  'Exhibition Manager',
  'https://evexa.com/invitation/token123',
  '2024-01-15'
);
```

### Checking Email Quota

```typescript
const quotaStatus = await emailService.getQuotaStatus();
console.log('Daily usage:', quotaStatus.dailyUsed, '/', quotaStatus.dailyLimit);
console.log('Monthly usage:', quotaStatus.monthlyUsed, '/', quotaStatus.monthlyLimit);
```

## Email Templates

### System Templates

The system includes professional email templates for:

- **User Invitation**: Professional invitation with role assignment
- **Welcome Message**: Comprehensive onboarding email
- **Password Reset**: Secure reset with time-limited links
- **Account Security**: Suspension and security notifications

### Custom Templates

Create custom templates using the template system:

```typescript
import { getSystemTemplate, replaceTemplateVariables } from '@/templates/system-email-templates';

const template = getSystemTemplate('user_invitation_v1');
const htmlContent = replaceTemplateVariables(template.htmlContent, {
  recipientName: 'John Doe',
  senderName: 'Admin User',
  tenantName: 'Acme Corp',
  invitationUrl: 'https://evexa.com/invitation/token123'
});
```

## Webhook Integration

### Event Tracking

The system automatically tracks email events via SendGrid webhooks:

- **Delivered**: Email successfully delivered to recipient
- **Opened**: Recipient opened the email
- **Clicked**: Recipient clicked a link in the email
- **Bounced**: Email bounced (hard or soft bounce)
- **Spam Report**: Recipient marked email as spam
- **Unsubscribed**: Recipient unsubscribed

### Webhook Endpoint

Configure SendGrid to send events to:
```
POST /api/webhooks/sendgrid
```

## Quota Management

### Default Limits

- **Basic Tier**: 1,000 emails/day, 10,000 emails/month
- **Professional Tier**: 5,000 emails/day, 50,000 emails/month
- **Enterprise Tier**: Custom limits

### Quota Enforcement

The system automatically:
- Checks quota before sending emails
- Blocks sending when limits are exceeded
- Resets daily counters at midnight
- Resets monthly counters on the 1st of each month

## Analytics & Reporting

### Email Analytics

Track comprehensive email metrics:

```typescript
const analytics = await emailService.getEmailAnalytics(
  new Date('2024-01-01'),
  new Date('2024-01-31'),
  100
);

analytics.forEach(event => {
  console.log(`${event.status}: ${event.recipientEmail} at ${event.sentAt}`);
});
```

### Available Metrics

- **Delivery Rate**: Percentage of emails successfully delivered
- **Open Rate**: Percentage of delivered emails opened
- **Click Rate**: Percentage of opened emails with clicks
- **Bounce Rate**: Percentage of emails that bounced
- **Spam Rate**: Percentage of emails marked as spam

## Security Features

### Tenant Isolation

- All emails are tenant-scoped
- Quota limits are per-tenant
- Analytics are tenant-specific
- Configuration is tenant-isolated

### Webhook Security

- Signature verification for webhook authenticity
- Timestamp validation to prevent replay attacks
- IP whitelisting for webhook endpoints

### Data Protection

- Email content is not stored permanently
- Analytics data includes only metadata
- Sensitive information is encrypted
- GDPR compliance for EU tenants

## Troubleshooting

### Common Issues

1. **API Key Invalid**: Verify SendGrid API key is correct and has proper permissions
2. **Quota Exceeded**: Check daily/monthly limits and upgrade plan if needed
3. **Delivery Issues**: Verify domain authentication and sender reputation
4. **Webhook Failures**: Check webhook URL accessibility and signature verification

### Debug Mode

Enable debug logging:

```bash
EVEXA_DEBUG_MODE=true
EMAIL_DEBUG_ENABLED=true
```

### Testing

Run email service tests:

```bash
npm test src/tests/emailService.test.ts
```

## Migration Guide

### From Mock to Production

1. **Update Environment**: Add SendGrid configuration to `.env`
2. **Test Integration**: Send test emails to verify configuration
3. **Configure Webhooks**: Set up event tracking endpoints
4. **Monitor Delivery**: Watch analytics for delivery issues
5. **Adjust Quotas**: Set appropriate limits for each tenant

### Best Practices

- **Domain Authentication**: Always set up domain authentication
- **List Hygiene**: Regularly clean email lists to maintain reputation
- **Content Quality**: Use professional templates and avoid spam triggers
- **Monitor Metrics**: Track delivery rates and adjust as needed
- **Respect Limits**: Don't exceed SendGrid rate limits

## Support

For issues with the email service integration:

1. Check the troubleshooting section above
2. Review SendGrid documentation
3. Contact EVEXA support with specific error messages
4. Include relevant log entries and configuration details
