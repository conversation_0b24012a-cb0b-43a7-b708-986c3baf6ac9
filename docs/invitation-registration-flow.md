# EVEXA Invitation Registration Flow

## Overview

The EVEXA Invitation Registration Flow provides a comprehensive system for invited users to create their accounts with automatic persona assignment and tenant association. This system ensures secure, validated user onboarding with proper role assignment and team integration.

## Features

### 🔐 **Secure Registration Process**
- **Token Validation**: Cryptographically secure invitation tokens with expiration
- **Password Security**: Strong password requirements with complexity validation
- **Email Verification**: Automatic email verification for new accounts
- **Terms Acceptance**: Required acceptance of Terms of Service and Privacy Policy

### 👤 **Automatic User Setup**
- **Persona Assignment**: Automatic role assignment based on invitation persona
- **Tenant Association**: Seamless integration into the correct tenant organization
- **Profile Creation**: Complete user profile setup with preferences
- **Permission Mapping**: Automatic permission assignment based on role

### 🎯 **User Experience**
- **Guided Onboarding**: Step-by-step post-registration setup
- **Profile Completion**: Optional profile enhancement with photo and preferences
- **Team Integration**: Introduction to team structure and collaboration tools
- **Preference Configuration**: Work preferences and notification settings

## Registration Flow

### 1. **Invitation Token Validation**

When a user clicks an invitation link, the system:

```typescript
// Validate invitation token
const validation = await InvitationRegistrationService.validateInvitationToken(token);

if (!validation.isValid) {
  // Handle invalid/expired invitation
  return { error: validation.error };
}
```

**Validation Checks:**
- Token exists and is valid
- Invitation status is 'pending'
- Invitation has not expired
- Associated tenant is active

### 2. **User Registration**

The registration process includes:

```typescript
const registrationRequest = {
  invitationToken: 'inv_**********_abcdef',
  password: 'SecurePassword123!',
  confirmPassword: 'SecurePassword123!',
  acceptTerms: true,
  acceptPrivacy: true,
  profileData: {
    phone: '+**********',
    timezone: 'America/New_York',
    language: 'en'
  }
};

const result = await InvitationRegistrationService.registerFromInvitation(registrationRequest);
```

**Registration Steps:**
1. **Input Validation**: Password strength, confirmation, terms acceptance
2. **Firebase Auth**: Create Firebase Authentication user
3. **Profile Creation**: Create comprehensive user profile in Firestore
4. **Persona Assignment**: Map invitation persona to user role and permissions
5. **Invitation Acceptance**: Mark invitation as accepted
6. **Email Verification**: Send verification email if required

### 3. **Post-Registration Onboarding**

After successful registration, users go through guided onboarding:

```typescript
<PostRegistrationOnboarding 
  user={newUser}
  onComplete={() => router.push('/dashboard')}
/>
```

**Onboarding Steps:**
1. **Welcome**: Account confirmation and role overview
2. **Profile**: Photo upload and contact information
3. **Preferences**: Work preferences and notification settings
4. **Team**: Introduction to team structure and tools

## API Endpoints

### **POST /api/invitation-registration**

Register a new user from an invitation token.

**Request Body:**
```json
{
  "invitationToken": "inv_**********_abcdef",
  "password": "SecurePassword123!",
  "confirmPassword": "SecurePassword123!",
  "acceptTerms": true,
  "acceptPrivacy": true,
  "profileData": {
    "phone": "+**********",
    "timezone": "America/New_York",
    "language": "en"
  }
}
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "displayName": "John Doe",
    "role": "user",
    "tenantId": "tenant_123",
    "department": "Marketing"
  },
  "requiresEmailVerification": true
}
```

### **GET /api/invitation-registration?token={token}**

Validate an invitation token before registration.

**Response:**
```json
{
  "valid": true,
  "invitation": {
    "id": "invitation_123",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "roleName": "Marketing Manager",
    "expiresAt": "2024-01-15T10:00:00Z"
  }
}
```

## Password Requirements

The system enforces strong password requirements:

- **Minimum Length**: 8 characters
- **Uppercase**: At least one uppercase letter (A-Z)
- **Lowercase**: At least one lowercase letter (a-z)
- **Numbers**: At least one digit (0-9)
- **Special Characters**: At least one special character (!@#$%^&*(),.?":{}|<>)

## Persona to Role Mapping

The system automatically maps invitation personas to user roles:

```typescript
const roleMapping = {
  'Administrator': 'admin',
  'Manager': 'management',
  'Exhibition Manager': 'management',
  'Marketing Manager': 'management',
  'Team Lead': 'management',
  'Senior': 'senior',
  'Specialist': 'specialist',
  'Coordinator': 'coordinator',
  'Team Member': 'user' // Default
};
```

## User Profile Structure

New users receive a complete profile:

```typescript
interface EvexUser {
  id: string;
  email: string;
  displayName: string;
  firstName: string;
  lastName: string;
  role: string;
  tenantId: string;
  personaId: string;
  status: 'active';
  department: string;
  jobTitle: string;
  profileImageUrl?: string;
  phone?: string;
  isEmailVerified: boolean;
  preferences: {
    theme: 'system';
    notifications: boolean;
    language: string;
    timezone: string;
  };
  invitationAcceptedAt: Date;
  invitedBy: string;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt: Date;
}
```

## Security Features

### **Token Security**
- Cryptographically secure token generation
- Time-based expiration (default 7 days)
- Single-use tokens (marked as accepted after use)
- Tenant-scoped validation

### **Input Validation**
- Server-side password strength validation
- Email format validation
- Required field validation
- Terms and privacy acceptance validation

### **Account Security**
- Firebase Authentication integration
- Email verification requirement
- Secure password hashing
- Session management

## Error Handling

The system provides comprehensive error handling:

```typescript
// Common error scenarios
const errors = {
  'INVALID_TOKEN': 'Invitation not found or invalid',
  'EXPIRED_TOKEN': 'This invitation has expired',
  'USED_TOKEN': 'This invitation is no longer valid',
  'WEAK_PASSWORD': 'Password does not meet security requirements',
  'EMAIL_EXISTS': 'An account with this email already exists',
  'TERMS_NOT_ACCEPTED': 'You must accept the Terms of Service',
  'PRIVACY_NOT_ACCEPTED': 'You must accept the Privacy Policy'
};
```

## Testing

Run the comprehensive test suite:

```bash
npm test src/tests/invitationRegistration.test.ts
```

**Test Coverage:**
- Token validation scenarios
- Password requirement validation
- Registration success flows
- Error handling
- Profile creation
- Persona mapping
- API endpoint testing

## Usage Examples

### **Basic Registration**

```typescript
import { InvitationRegistrationService } from '@/services/invitationRegistrationService';

// Validate token first
const validation = await InvitationRegistrationService.validateInvitationToken(token);

if (validation.isValid) {
  // Proceed with registration
  const result = await InvitationRegistrationService.registerFromInvitation({
    invitationToken: token,
    password: 'SecurePass123!',
    confirmPassword: 'SecurePass123!',
    acceptTerms: true,
    acceptPrivacy: true
  });
  
  if (result.success) {
    // Registration successful
    console.log('User created:', result.user);
  }
}
```

### **With Profile Data**

```typescript
const result = await InvitationRegistrationService.registerFromInvitation({
  invitationToken: token,
  password: 'SecurePass123!',
  confirmPassword: 'SecurePass123!',
  acceptTerms: true,
  acceptPrivacy: true,
  profileData: {
    phone: '+**********',
    timezone: 'America/New_York',
    language: 'en',
    profileImageUrl: 'https://example.com/photo.jpg'
  }
});
```

## Integration Points

### **With Invitation Management**
- Automatic invitation acceptance
- Status updates
- Analytics tracking

### **With User Management**
- Profile creation
- Role assignment
- Permission mapping

### **With Email Service**
- Verification emails
- Welcome messages
- Onboarding sequences

### **With Authentication**
- Firebase Auth integration
- Session management
- Security policies

## Best Practices

1. **Always validate tokens** before showing registration form
2. **Enforce strong passwords** with client and server validation
3. **Require terms acceptance** for legal compliance
4. **Send verification emails** for account security
5. **Guide users through onboarding** for better adoption
6. **Log registration events** for analytics and debugging
7. **Handle errors gracefully** with user-friendly messages
8. **Test thoroughly** with various scenarios and edge cases

## Troubleshooting

### **Common Issues**

1. **Token Validation Fails**
   - Check token format and expiration
   - Verify invitation exists in database
   - Ensure tenant is active

2. **Registration Fails**
   - Validate password requirements
   - Check Firebase Auth configuration
   - Verify Firestore permissions

3. **Email Verification Issues**
   - Check Firebase Auth settings
   - Verify email service configuration
   - Test with different email providers

4. **Profile Creation Errors**
   - Validate Firestore security rules
   - Check required field validation
   - Verify persona exists

The invitation registration flow provides a secure, user-friendly way for invited users to join EVEXA with proper role assignment and team integration.
