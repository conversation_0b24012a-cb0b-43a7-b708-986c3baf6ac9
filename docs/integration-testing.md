# EVEXA Integration Testing Guide

## 🧪 **Overview**

This document describes the comprehensive end-to-end integration testing system for EVEXA, covering the complete user journey from invitation to full system usage with proper persona permissions and data access validation.

## 🎯 **Test Coverage**

### **Complete User Journey Tests**
- **Admin invitation workflow**: Sending invitations with persona assignment
- **User registration flow**: Accepting invitations and creating accounts
- **Persona permission validation**: Ensuring users have correct module access
- **Data creation and management**: Testing CRUD operations within permissions
- **Subscription limit enforcement**: Validating user limits and upgrade prompts
- **Tenant data isolation**: Ensuring proper multi-tenant security
- **Session management**: Login, logout, and session persistence
- **Error handling**: Network errors, validation failures, and recovery

### **Persona-Specific Workflows**
- **Exhibition Manager**: Full exhibition and task management capabilities
- **Marketing Specialist**: Marketing-focused features and analytics access
- **Financial Controller**: Financial oversight and reporting permissions
- **Basic User**: Limited read-only access with restricted features
- **Cross-persona collaboration**: Team workflows across different roles
- **Permission updates**: Real-time persona changes and access control

## 🚀 **Quick Start**

### **Prerequisites**
```bash
# Install dependencies
npm install

# Install Playwright browsers
npx playwright install

# Start development server
npm run dev
```

### **Run All Integration Tests**
```bash
npm run test:integration
```

### **Run Specific Test Suites**
```bash
# Complete user journey
npm run test:integration:journey

# Persona workflows
npm run test:integration:personas

# User onboarding
npm run test:e2e user-onboarding.spec.ts
```

### **Run Tests in Headless Mode**
```bash
npm run test:integration:headless
```

## 📁 **Test Structure**

```
e2e/
├── complete-user-journey.spec.ts    # End-to-end user journey tests
├── persona-workflows.spec.ts        # Persona-specific workflow tests
├── user-onboarding.spec.ts         # User onboarding flow tests
└── utils/
    └── integration-test-helpers.ts  # Shared test utilities and helpers

scripts/
└── run-integration-tests.js        # Custom test runner with reporting
```

## 🔧 **Test Utilities**

### **AuthHelpers**
```typescript
// Login and logout utilities
await AuthHelpers.loginUser(page, testUser);
await AuthHelpers.logoutUser(page);
await AuthHelpers.verifyUserSession(page, testUser);
```

### **InvitationHelpers**
```typescript
// Invitation workflow utilities
const token = await InvitationHelpers.sendInvitation(page, invitationData);
await InvitationHelpers.acceptInvitation(page, token, password);
await InvitationHelpers.verifyInvitationInList(page, email, 'pending');
```

### **PermissionHelpers**
```typescript
// Permission validation utilities
await PermissionHelpers.verifyModuleAccess(page, 'exhibitions', true);
await PermissionHelpers.verifyActionPermission(page, '[data-testid="create-btn"]', false);
await PermissionHelpers.testPersonaPermissions(page, 'exhibition-manager');
```

### **DataHelpers**
```typescript
// Data creation utilities
await DataHelpers.createExhibition(page, exhibitionData);
await DataHelpers.createTask(page, taskData);
```

### **SubscriptionHelpers**
```typescript
// Subscription and limits testing
await SubscriptionHelpers.verifyUserLimits(page, currentCount, limitCount);
await SubscriptionHelpers.testInvitationLimits(page);
```

## 📊 **Test Scenarios**

### **1. Complete User Journey**
```typescript
test('Admin can send invitation with persona assignment', async () => {
  // Admin login and navigation to user management
  // Fill invitation form with persona selection
  // Verify invitation sent successfully
  // Check invitation appears in pending list
});

test('Invitee receives and accepts invitation', async () => {
  // Navigate to invitation URL
  // Verify invitation details displayed
  // Complete registration form
  // Accept terms and conditions
  // Verify successful account creation
});

test('New user has correct persona permissions', async () => {
  // Verify access to allowed modules
  // Verify restriction from forbidden modules
  // Test specific action permissions
  // Validate persona-specific features
});
```

### **2. Persona Workflows**
```typescript
test.describe('Exhibition Manager Persona', () => {
  test('Can manage exhibitions', async () => {
    // Create, edit, delete exhibitions
    // Manage exhibition team members
    // Access exhibition analytics
  });
  
  test('Can manage tasks', async () => {
    // Create and assign tasks
    // Update task status
    // View task analytics
  });
  
  test('Cannot access restricted modules', async () => {
    // Verify no access to financials
    // Verify no access to user management
    // Verify no access to system settings
  });
});
```

### **3. Subscription Limits**
```typescript
test('Subscription limits are enforced correctly', async () => {
  // Check current user count vs limits
  // Test warning at 80% capacity
  // Test blocking at 100% capacity
  // Verify upgrade prompts appear
});
```

### **4. Data Isolation**
```typescript
test('Data isolation between tenants is maintained', async () => {
  // Verify user sees only tenant data
  // Check API calls include tenant context
  // Validate cross-tenant access prevention
});
```

## 🎯 **Test Data Management**

### **Test Users**
```typescript
const testAdmin = {
  email: '<EMAIL>',
  password: 'AdminPassword123!',
  displayName: 'Test Admin'
};

const testInvitee = {
  email: `invited-${Date.now()}@e2etest.com`,
  firstName: 'John',
  lastName: 'Doe',
  password: 'InviteePassword123!'
};
```

### **Test Tenants**
```typescript
const testTenant = {
  id: 'e2e-test-tenant',
  name: 'E2E Test Company',
  domain: 'e2etest.com'
};
```

### **Dynamic Data Generation**
```typescript
// Use timestamps for unique data
const uniqueEmail = `user-${Date.now()}@example.com`;
const exhibitionName = `Test Exhibition ${Date.now()}`;

// Use date helpers for consistent date handling
const startDate = DateHelpers.getFutureDate(30);
const endDate = DateHelpers.getFutureDate(33);
```

## 🔍 **Best Practices**

### **Test Organization**
- **Group related tests** using `test.describe()`
- **Use descriptive test names** that explain the scenario
- **Separate setup and teardown** logic clearly
- **Share common utilities** through helper functions

### **Data Management**
- **Use unique identifiers** to avoid test conflicts
- **Clean up test data** after test completion
- **Use realistic test data** that matches production patterns
- **Avoid hardcoded values** that may change

### **Error Handling**
- **Test both success and failure scenarios**
- **Verify error messages are user-friendly**
- **Ensure graceful degradation** when services fail
- **Test recovery mechanisms** after errors

### **Performance**
- **Run tests sequentially** for integration scenarios
- **Use appropriate timeouts** for different operations
- **Minimize test data creation** to reduce execution time
- **Reuse browser contexts** when possible

## 📈 **Reporting and Monitoring**

### **Test Reports**
The custom test runner generates comprehensive reports including:
- **Test suite execution status**
- **Detailed pass/fail breakdown**
- **Execution time metrics**
- **Error details and stack traces**

### **CI/CD Integration**
```bash
# In CI pipeline
npm run test:integration:headless

# Exit codes
# 0 = All tests passed
# 1 = One or more tests failed
```

### **Monitoring**
- **Track test execution times** to identify performance regressions
- **Monitor test failure rates** to catch system issues early
- **Alert on critical test failures** that affect core user journeys

## 🚨 **Troubleshooting**

### **Common Issues**

**Development Server Not Running**
```bash
# Start the dev server first
npm run dev
```

**Playwright Not Installed**
```bash
# Install Playwright browsers
npx playwright install
```

**Test Timeouts**
```bash
# Increase timeout for slow operations
test.setTimeout(120000); // 2 minutes
```

**Element Not Found**
```bash
# Use proper data-testid selectors
await page.locator('[data-testid="element-id"]')
```

### **Debug Commands**
```bash
# Run tests with UI for debugging
npm run test:e2e:ui

# Run specific test file
npx playwright test e2e/complete-user-journey.spec.ts

# Run tests in headed mode
npx playwright test --headed

# Generate trace files
npx playwright test --trace on
```

## 🎯 **Success Metrics**

### **Coverage Goals**
- ✅ **100% critical user journeys** covered
- ✅ **All persona workflows** tested
- ✅ **Complete permission matrix** validated
- ✅ **Subscription limits** thoroughly tested
- ✅ **Data isolation** verified across tenants

### **Performance Targets**
- **Complete journey tests**: < 2 minutes
- **Persona workflow tests**: < 1.5 minutes
- **Individual test cases**: < 30 seconds
- **Overall test suite**: < 5 minutes

### **Quality Standards**
- **Zero flaky tests** in CI environment
- **Clear error messages** for all failures
- **Comprehensive test coverage** of edge cases
- **Maintainable test code** with good documentation
