# EVEXA Multi-Tenant Collection Schema

## Architecture Overview

**Flat Collections with tenantId Stamping** - All shared data lives in root-level collections with tenantId field for security and performance.

**Strategic Data Duplication** - Performance-critical fields are duplicated to avoid expensive joins in list views.

**Cloud Functions for Integrity** - Automated data consistency and cascade operations across collections.

## Root-Level Collections (tenantId Stamped)

### exhibitions/
```typescript
{
  id: string;
  tenantId: string;                    // Security stamp
  name: string;
  description: string;
  startDate: Timestamp;
  endDate: Timestamp;
  venue: {
    name: string;
    address: string;
    city: string;
    country: string;
  };
  status: 'planning' | 'active' | 'completed' | 'cancelled';
  budget: {
    total: number;
    currency: string;
    allocated: number;
    spent: number;
  };
  createdBy: string;                   // userId
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### tasks/
```typescript
{
  id: string;
  tenantId: string;                    // Security stamp
  exhibitionId: string;                // Relationship
  exhibitionName: string;              // DUPLICATED for performance
  title: string;
  description: string;
  assignedTo: string;                  // userId
  assignedToName: string;              // DUPLICATED for performance
  status: 'todo' | 'in_progress' | 'completed' | 'blocked';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate: Timestamp;
  estimatedHours: number;
  actualHours: number;
  tags: string[];
  createdBy: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### events/
```typescript
{
  id: string;
  tenantId: string;                    // Security stamp
  exhibitionId: string;                // Relationship
  exhibitionName: string;              // DUPLICATED for performance
  title: string;
  description: string;
  startTime: Timestamp;
  endTime: Timestamp;
  location: string;
  attendees: string[];                 // userIds
  type: 'meeting' | 'presentation' | 'setup' | 'breakdown' | 'other';
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  createdBy: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### vendors/
```typescript
{
  id: string;
  tenantId: string;                    // Security stamp
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    country: string;
    zipCode: string;
  };
  category: string;                    // 'catering', 'av_equipment', 'logistics', etc.
  rating: number;                      // 1-5 stars
  notes: string;
  contracts: {
    exhibitionId: string;
    exhibitionName: string;            // DUPLICATED for performance
    contractValue: number;
    status: 'draft' | 'active' | 'completed';
  }[];
  createdBy: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### financials/ (CONSOLIDATED)
```typescript
{
  id: string;
  tenantId: string;                    // Security stamp
  type: 'budget' | 'expense' | 'purchase_order' | 'invoice';
  exhibitionId: string;                // Relationship
  exhibitionName: string;              // DUPLICATED for performance
  title: string;
  description: string;
  amount: number;
  currency: string;
  category: string;                    // 'venue', 'catering', 'marketing', etc.
  status: 'draft' | 'pending' | 'approved' | 'paid' | 'rejected';
  vendorId?: string;                   // Optional relationship
  vendorName?: string;                 // DUPLICATED for performance
  approvedBy?: string;                 // userId
  approvedAt?: Timestamp;
  dueDate?: Timestamp;
  paidDate?: Timestamp;
  attachments: string[];               // File URLs
  createdBy: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### attendees/
```typescript
{
  id: string;
  tenantId: string;                    // Security stamp
  exhibitionId: string;                // Relationship
  exhibitionName: string;              // DUPLICATED for performance
  firstName: string;
  lastName: string;
  email: string;
  company: string;
  title: string;
  phone: string;
  category: 'vip' | 'media' | 'client' | 'prospect' | 'partner' | 'general';
  registrationStatus: 'registered' | 'confirmed' | 'attended' | 'no_show';
  checkInTime?: Timestamp;
  interests: string[];
  notes: string;
  createdBy: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### social_posts/
```typescript
{
  id: string;
  tenantId: string;                    // Security stamp
  exhibitionId?: string;               // Optional relationship
  exhibitionName?: string;             // DUPLICATED for performance
  platform: 'linkedin' | 'twitter' | 'facebook' | 'instagram';
  content: string;
  mediaUrls: string[];
  scheduledFor: Timestamp;
  status: 'draft' | 'scheduled' | 'published' | 'failed';
  publishedAt?: Timestamp;
  engagement: {
    likes: number;
    shares: number;
    comments: number;
  };
  createdBy: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### notifications/
```typescript
{
  id: string;
  tenantId: string;                    // Security stamp
  userId: string;                      // Target user
  type: 'task_assigned' | 'deadline_approaching' | 'budget_exceeded' | 'approval_needed';
  title: string;
  message: string;
  relatedId?: string;                  // ID of related document
  relatedType?: string;                // Type of related document
  read: boolean;
  actionUrl?: string;
  createdAt: Timestamp;
}
```

## Tenant-Specific Collections

### tenants/{tenantId}/
```typescript
// tenant_info/
{
  name: string;
  slug: string;
  status: 'active' | 'suspended' | 'inactive';
  plan: 'basic' | 'professional' | 'enterprise';
  description: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// subscription/
{
  plan: 'basic' | 'professional' | 'enterprise';
  userLimit: number;
  currentUsers: number;
  billingCycle: 'monthly' | 'yearly';
  nextBillingDate: Timestamp;
  status: 'active' | 'past_due' | 'cancelled';
  features: string[];
}

// branding/
{
  logo: string;                        // URL
  primaryColor: string;
  secondaryColor: string;
  emailTemplate: string;
}
```

## Data Integrity Rules

### Cloud Function Triggers

1. **Exhibition Deleted** → Delete all related tasks, events, financials, attendees
2. **User Deleted** → Reassign tasks, update createdBy fields
3. **Exhibition Updated** → Update duplicated exhibitionName in all related collections
4. **Vendor Updated** → Update duplicated vendorName in financials

### Performance Optimizations

1. **Task Lists** → No need to join with exhibitions (exhibitionName embedded)
2. **Financial Reports** → Single collection query for all financial data
3. **Vendor Contracts** → Embedded exhibition info for quick display
4. **User Activity** → Embedded user names to avoid user lookups

This schema balances performance, consistency, and scalability for production use.
