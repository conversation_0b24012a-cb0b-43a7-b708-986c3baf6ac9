# EVEXA Data Integrity Testing Guide

## 🔒 **Overview**

This document describes the comprehensive data integrity testing system for EVEXA, covering Cloud Functions for referential integrity, cascade delete operations, data duplication synchronization mechanisms, and orphaned record detection and cleanup.

## 🎯 **Test Coverage**

### **Cloud Functions Data Integrity**
- **Cascade delete operations**: Exhibition, user, and vendor deletion with proper cleanup
- **Data duplication sync**: Automatic synchronization of duplicated data across collections
- **Referential integrity**: Maintaining relationships between related documents
- **Tenant isolation**: Ensuring operations respect multi-tenant boundaries
- **Batch operation limits**: Handling large datasets within Firestore constraints
- **Error handling**: Graceful handling of database errors and timeouts

### **Data Duplication Synchronization**
- **Exhibition data sync**: Name, venue, date changes propagated to related documents
- **User profile sync**: Display name and profile changes across all references
- **Vendor data sync**: Company information updates in purchase records
- **Rule-based sync**: Configurable duplication rules with enable/disable functionality
- **Performance optimization**: Efficient batch operations for large datasets
- **Consistency validation**: Detecting and fixing inconsistent duplicated data

### **Orphaned Record Detection**
- **Exhibition orphans**: Tasks, events, leads without valid exhibition references
- **User orphans**: Documents referencing deleted or non-existent users
- **Financial orphans**: Budget allocations and expenses without valid exhibitions
- **Cross-collection validation**: Complex referential integrity across multiple collections
- **Auto-cleanup**: Automated removal of orphaned records with safety checks
- **Cleanup suggestions**: Recommendations for manual review of complex cases

## 🚀 **Quick Start**

### **Prerequisites**
```bash
# Install dependencies
npm install

# Install Firebase CLI (for emulators)
npm install -g firebase-tools

# Install Cloud Functions dependencies
cd functions && npm install && cd ..
```

### **Run All Data Integrity Tests**
```bash
npm run test:data-integrity
```

### **Run Specific Test Suites**
```bash
# Cloud Functions tests
npm run test:data-integrity:cloud-functions

# Cascade delete tests
npm run test:data-integrity:cascade

# Data duplication sync tests
npm run test:data-integrity:duplication

# Orphaned records tests
npm run test:data-integrity:orphaned
```

### **Run with Firebase Emulators**
```bash
# Start emulators in separate terminal
firebase emulators:start --only firestore,functions

# Run tests with emulator support
npm run test:data-integrity --skip-emulators
```

## 📁 **Test Structure**

```
src/__tests__/data-integrity/
├── cloudFunctionIntegrity.test.ts      # Cloud Functions integration tests
├── dataDuplicationSync.test.ts         # Data duplication sync tests
└── orphanedRecords.test.ts             # Orphaned record detection tests

functions/src/__tests__/
└── dataIntegrityFunctions.test.ts      # Cloud Functions unit tests

scripts/
└── test-data-integrity.js              # Custom test runner with emulator support
```

## 🔧 **Test Scenarios**

### **1. Cascade Delete Operations**
```typescript
test('should cascade delete all related documents when exhibition is deleted', async () => {
  // Setup exhibition with related documents
  const exhibitionId = 'exhibition-123';
  const relatedCollections = [
    'exhibition_events',
    'exhibition_tasks', 
    'lead_contacts',
    'budget_allocations',
    'expense_records'
  ];
  
  // Verify all related documents are deleted
  // Verify tenant isolation is maintained
  // Verify batch limits are respected
});
```

### **2. Data Duplication Sync**
```typescript
test('should sync exhibition name changes to related documents', async () => {
  // Update exhibition name
  const updatedFields = { name: 'New Exhibition Name' };
  
  // Verify sync to all collections with duplicated data
  const results = await duplicationService.syncDuplicatedData(
    COLLECTIONS.EXHIBITIONS,
    exhibitionId,
    updatedFields
  );
  
  // Verify consistency across all related documents
});
```

### **3. Orphaned Record Detection**
```typescript
test('should detect orphaned tasks without valid exhibitions', async () => {
  // Create tasks referencing non-existent exhibitions
  const orphanedTasks = [
    { exhibitionId: 'non-existent-exhibition-1' },
    { exhibitionId: 'non-existent-exhibition-2' }
  ];
  
  // Run integrity check
  const result = await integrityChecker.runIntegrityCheck({
    collections: ['exhibition_tasks'],
    includeOrphanCheck: true
  });
  
  // Verify orphaned records are detected
  expect(result.summary.totalIssues).toBeGreaterThan(0);
});
```

## 🛠 **Cloud Functions Testing**

### **Firebase Functions Test SDK**
```typescript
import * as test from 'firebase-functions-test';

const testEnv = test();

test('onExhibitionDeleted should cascade delete related documents', async () => {
  const beforeSnap = testEnv.firestore.makeDocumentSnapshot(
    exhibitionData,
    `exhibitions/${exhibitionId}`
  );
  
  const context = { params: { exhibitionId } };
  
  // Execute the function
  await onExhibitionDeleted(beforeSnap, context);
  
  // Verify cascade operations
});
```

### **Emulator Integration**
```bash
# Start Firebase emulators
firebase emulators:start --only firestore,functions

# Run tests with emulator environment
FIRESTORE_EMULATOR_HOST=localhost:8080 npm test
```

## 📊 **Data Integrity Validation**

### **Referential Integrity Rules**
- **Exhibition → Tasks**: All tasks must reference valid exhibitions
- **Exhibition → Events**: All events must reference valid exhibitions  
- **Exhibition → Leads**: All leads must reference valid exhibitions
- **User → Tasks**: All task assignments must reference valid users
- **User → Events**: All event organizers must reference valid users
- **Vendor → Purchases**: All purchases must reference valid vendors

### **Data Duplication Rules**
```typescript
const DUPLICATION_RULES = {
  exhibitionName: {
    sourceCollection: 'exhibitions',
    sourceField: 'name',
    targetCollections: [
      { collection: 'exhibition_tasks', targetField: 'exhibitionName' },
      { collection: 'exhibition_events', targetField: 'exhibitionName' },
      { collection: 'lead_contacts', targetField: 'exhibitionName' }
    ]
  },
  userDisplayName: {
    sourceCollection: 'user_profiles',
    sourceField: 'displayName',
    targetCollections: [
      { collection: 'exhibition_tasks', targetField: 'assignedToName' },
      { collection: 'exhibition_events', targetField: 'organizerName' }
    ]
  }
};
```

### **Tenant Isolation Validation**
- All operations must respect tenant boundaries
- Cross-tenant data access must be prevented
- Cascade operations must be tenant-scoped
- Orphaned record detection must be tenant-aware

## 🔍 **Performance Testing**

### **Large Dataset Handling**
```typescript
test('should handle large datasets efficiently', async () => {
  // Mock 1000+ documents
  const mockLargeDataset = Array.from({ length: 1000 }, ...);
  
  const startTime = Date.now();
  const result = await integrityChecker.runIntegrityCheck({
    collections: ['exhibition_tasks']
  });
  const duration = Date.now() - startTime;
  
  // Should complete within reasonable time
  expect(duration).toBeLessThan(10000);
});
```

### **Batch Operation Limits**
- Firestore batch operations limited to 500 operations
- Large datasets automatically split into multiple batches
- Progress tracking for long-running operations
- Memory-efficient processing of large collections

## 🚨 **Error Handling**

### **Common Error Scenarios**
- **Database connection failures**: Graceful degradation and retry logic
- **Permission denied errors**: Proper error messages and fallback behavior
- **Timeout errors**: Reasonable timeouts with progress indicators
- **Batch operation failures**: Partial success handling and rollback

### **Error Recovery**
```typescript
test('should handle database connection errors gracefully', async () => {
  // Mock database error
  db.collection.mockRejectedValue(new Error('Connection failed'));
  
  // Should not throw error
  await expect(
    duplicationService.syncDuplicatedData(...)
  ).resolves.not.toThrow();
});
```

## 📈 **Monitoring and Reporting**

### **Test Reports**
The data integrity test runner generates comprehensive reports including:
- **Test suite execution status** with pass/fail breakdown
- **Data integrity metrics** showing orphaned records, consistency issues
- **Performance metrics** with execution times and throughput
- **Error analysis** with detailed failure information

### **Continuous Integration**
```yaml
# CI Pipeline Integration
- name: Data Integrity Tests
  run: |
    npm run test:data-integrity
    
# Exit codes:
# 0 = All tests passed, data integrity maintained
# 1 = Tests failed, data integrity issues detected
```

### **Monitoring Alerts**
- **Critical integrity violations**: Immediate alerts for data corruption
- **Performance degradation**: Alerts when operations exceed thresholds
- **Orphaned record accumulation**: Warnings when orphaned records increase
- **Sync failures**: Alerts when data duplication sync fails

## 🎯 **Success Metrics**

### **Coverage Goals**
- ✅ **100% cascade delete scenarios** tested
- ✅ **All data duplication rules** validated
- ✅ **Complete orphaned record detection** across all collections
- ✅ **Referential integrity** maintained across all relationships
- ✅ **Tenant isolation** verified in all operations

### **Performance Targets**
- **Cloud Functions execution**: < 30 seconds for cascade operations
- **Data duplication sync**: < 5 seconds for typical updates
- **Orphaned record detection**: < 60 seconds for full collection scan
- **Large dataset processing**: < 10 seconds per 1000 documents

### **Quality Standards**
- **Zero data corruption** in production environments
- **100% referential integrity** maintained across all operations
- **Complete tenant isolation** with no cross-tenant data leakage
- **Automated cleanup** of orphaned records with manual review options

## 🔧 **Troubleshooting**

### **Common Issues**

**Firebase Emulators Not Starting**
```bash
# Check if ports are in use
lsof -i :8080 -i :5001

# Kill existing processes
firebase emulators:kill

# Restart emulators
firebase emulators:start --only firestore,functions
```

**Cloud Functions Test Failures**
```bash
# Ensure Functions dependencies are installed
cd functions && npm install

# Run Functions tests separately
cd functions && npm test
```

**Large Dataset Timeouts**
```bash
# Increase test timeout
jest --testTimeout=120000

# Run with verbose output for debugging
npm run test:data-integrity --verbose
```

### **Debug Commands**
```bash
# Run specific test file
npx jest src/__tests__/data-integrity/cloudFunctionIntegrity.test.ts

# Run with coverage
npm run test:data-integrity --coverage

# Run in watch mode for development
npx jest --watch src/__tests__/data-integrity/
```

## 🎯 **Best Practices**

### **Test Data Management**
- Use unique identifiers to avoid test conflicts
- Clean up test data after each test run
- Use realistic data volumes for performance testing
- Maintain separate test tenants for isolation

### **Error Simulation**
- Test network failures and timeouts
- Simulate permission denied scenarios
- Test partial batch operation failures
- Validate recovery mechanisms

### **Performance Optimization**
- Use batch operations for bulk updates
- Implement efficient querying strategies
- Monitor memory usage during large operations
- Optimize Cloud Functions for cold start performance

The comprehensive data integrity testing system ensures that EVEXA's multi-tenant architecture maintains perfect referential integrity, efficient data synchronization, and robust orphaned record cleanup across all operations.
