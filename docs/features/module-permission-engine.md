# Module Permission Engine

## Overview

The Module Permission Engine is a comprehensive permission checking system that provides robust, cached, and real-time permission validation for EVEXA's multi-tenant architecture. It enhances the existing permission system with advanced features like custom rules, bulk operations, and performance optimizations.

## Key Features

### 1. Enhanced Permission Checking
- **Cached Permission Checks**: Intelligent caching with TTL for improved performance
- **Bulk Permission Validation**: Check multiple permissions in a single operation
- **Real-time Updates**: Automatic cache invalidation when permissions change
- **Custom Permission Rules**: Define custom logic for specific scenarios

### 2. Advanced Permission Context
- **User Context**: Complete user information including persona and subscription
- **Custom Overrides**: Temporary or permanent permission overrides
- **Subscription Enforcement**: Automatic premium feature access control
- **Tenant Isolation**: Strict tenant-based permission boundaries

### 3. Performance Optimizations
- **Intelligent Caching**: Multi-level caching with automatic invalidation
- **Bulk Operations**: Efficient batch permission checking
- **Lazy Loading**: Load permissions only when needed
- **Memory Management**: Automatic cache cleanup and optimization

## Architecture

### Core Components

#### ModulePermissionEngine
The main engine that handles all permission operations:

```typescript
class ModulePermissionEngine {
  // Core permission checking
  async checkPermission(context, module, action, options): Promise<boolean>
  async checkBulkPermissions(context, permissions): Promise<BulkPermissionResult>
  
  // Module access management
  async getUserModuleAccess(context): Promise<ModuleAccess[]>
  
  // Custom rules
  addRule(rule: PermissionRule): void
  removeRule(ruleId: string): void
  
  // Cache management
  invalidateCache(pattern?: string): void
  getCacheStats(): { size: number; hitRate: number }
}
```

#### Permission Context
Comprehensive context for permission evaluation:

```typescript
interface PermissionContext {
  userId: string;
  tenantId: string;
  persona?: TenantPersona;
  subscriptionTier?: 'basic' | 'professional' | 'enterprise';
  customOverrides?: ModuleOverride[];
}
```

#### Custom Permission Rules
Flexible rule system for complex scenarios:

```typescript
interface PermissionRule {
  id: string;
  name: string;
  description: string;
  condition: (context: PermissionContext) => boolean;
  modules: EvexaModule[];
  actions: PermissionAction[];
  priority: number;
}
```

## Usage Guide

### Basic Permission Checking

```typescript
import { createModulePermissionEngine, createPermissionContext } from '@/services/modulePermissionEngine';

// Initialize engine
const engine = createModulePermissionEngine('tenant-123');

// Create context
const context = createPermissionContext(
  'user-456',
  'tenant-123',
  userPersona,
  'professional'
);

// Check single permission
const hasAccess = await engine.checkPermission(
  context,
  'exhibitions',
  'write'
);

// Check multiple permissions
const bulkResult = await engine.checkBulkPermissions(context, [
  { module: 'exhibitions', action: 'read' },
  { module: 'tasks', action: 'write' },
  { module: 'budgets', action: 'admin' }
]);
```

### React Hooks

#### Enhanced Module Access Hook

```typescript
import { useModuleAccessEnhanced } from '@/hooks/useModulePermissionEngine';

function ExhibitionComponent() {
  const { 
    hasAccess, 
    permissions, 
    isLoading, 
    canWrite, 
    canDelete 
  } = useModuleAccessEnhanced('exhibitions', 'read');

  if (isLoading) return <LoadingSpinner />;
  if (!hasAccess) return <AccessDenied />;

  return (
    <div>
      <ExhibitionList />
      {canWrite && <CreateButton />}
      {canDelete && <DeleteButton />}
    </div>
  );
}
```

#### Bulk Permissions Hook

```typescript
import { useBulkPermissions } from '@/hooks/useModulePermissionEngine';

function AdminPanel() {
  const { 
    hasAllPermissions, 
    deniedPermissions, 
    isLoading 
  } = useBulkPermissions([
    { module: 'users', action: 'admin' },
    { module: 'settings', action: 'write' },
    { module: 'analytics', action: 'read' }
  ]);

  if (isLoading) return <LoadingSpinner />;
  if (!hasAllPermissions) {
    return <MissingPermissions denied={deniedPermissions} />;
  }

  return <AdminDashboard />;
}
```

### Enhanced Permission Gates

```typescript
import { 
  EnhancedPermissionGate, 
  BulkPermissionGate,
  WriteGate,
  AdminGate 
} from '@/components/permissions/EnhancedPermissionGate';

// Single module gate with upgrade prompt
<EnhancedPermissionGate 
  module="exhibitions" 
  action="write"
  showUpgradePrompt={true}
  showPermissionInfo={true}
>
  <CreateExhibitionForm />
</EnhancedPermissionGate>

// Multiple permissions gate
<BulkPermissionGate
  permissions={[
    { module: 'budgets', action: 'write' },
    { module: 'expenses', action: 'admin' }
  ]}
  requireAll={true}
  showMissingPermissions={true}
>
  <FinancialManagement />
</BulkPermissionGate>

// Convenience gates
<WriteGate module="tasks">
  <TaskEditor />
</WriteGate>

<AdminGate module="users">
  <UserManagement />
</AdminGate>
```

### API Middleware

```typescript
import { 
  withEnhancedPermissions,
  withModulePermission,
  withAdminPermission 
} from '@/middleware/enhancedPermissionMiddleware';

// Basic module permission
export default withModulePermission('exhibitions', 'write')(
  async (req, res) => {
    // Handler has access to:
    // - req.user (authenticated user)
    // - req.permissionContext (permission context)
    // - req.permissionEngine (permission engine)
    
    const exhibitions = await getExhibitions(req.user.tenantId);
    res.json(exhibitions);
  }
);

// Advanced permissions with custom validation
export default withEnhancedPermissions(
  async (req, res) => {
    // Handle request
  },
  {
    permissions: [
      { module: 'budgets', action: 'admin' },
      { module: 'expenses', action: 'write' }
    ],
    systemPermissions: ['canManageSettings'],
    requireAll: true,
    customValidator: async (context) => {
      // Custom validation logic
      return context.persona?.limitations?.maxBudget > 100000;
    },
    cachePermissions: true,
    logAccess: true
  }
);
```

### Custom Permission Rules

```typescript
// Add emergency access rule
engine.addRule({
  id: 'emergency_access',
  name: 'Emergency Access',
  description: 'Emergency access for critical operations',
  condition: (context) => {
    return context.persona?.customizations?.featureFlags?.emergencyAccess === true;
  },
  modules: ['exhibitions', 'tasks', 'contacts'],
  actions: ['read', 'write'],
  priority: 900
});

// Add time-based rule
engine.addRule({
  id: 'business_hours_only',
  name: 'Business Hours Access',
  description: 'Restrict admin access to business hours',
  condition: (context) => {
    const hour = new Date().getHours();
    return hour >= 9 && hour <= 17; // 9 AM to 5 PM
  },
  modules: ['settings', 'users'],
  actions: ['admin'],
  priority: 800
});
```

## Performance Considerations

### Caching Strategy
- **Permission Results**: Cached for 5 minutes by default
- **User Context**: Cached until persona changes
- **Module Metadata**: Cached indefinitely
- **Custom Rules**: Invalidate cache when rules change

### Optimization Tips
1. **Use Bulk Operations**: Check multiple permissions at once
2. **Enable Caching**: Use `useCache: true` for repeated checks
3. **Batch Updates**: Update multiple permissions together
4. **Monitor Cache**: Use `getCacheStats()` to monitor performance

### Memory Management
- Automatic cache cleanup based on TTL
- Pattern-based cache invalidation
- Memory usage monitoring
- Configurable cache limits

## Security Features

### Tenant Isolation
- All permissions are tenant-scoped
- Cross-tenant access prevention
- Secure context validation

### Audit Trail
- Optional access logging
- Permission check tracking
- Failed access attempts logging
- Custom audit hooks

### Subscription Enforcement
- Automatic premium feature blocking
- Subscription tier validation
- Upgrade prompts for restricted features
- Grace period handling

## Integration Points

### Authentication System
- Seamless integration with auth context
- Automatic user persona loading
- Token-based permission validation

### Subscription Management
- Real-time subscription status checking
- Feature flag integration
- Billing system coordination

### Analytics & Monitoring
- Permission usage analytics
- Performance metrics
- Error tracking and reporting
- Cache hit rate monitoring

## Troubleshooting

### Common Issues

1. **Permissions Not Working**
   - Check persona assignment
   - Verify module configuration
   - Clear permission cache
   - Review custom rules

2. **Performance Issues**
   - Monitor cache hit rates
   - Optimize bulk operations
   - Review rule complexity
   - Check memory usage

3. **Cache Problems**
   - Invalidate specific patterns
   - Adjust TTL settings
   - Monitor cache size
   - Review invalidation logic

### Debug Tools

```typescript
// Get cache statistics
const stats = engine.getCacheStats();
console.log('Cache size:', stats.size);
console.log('Hit rate:', stats.hitRate);

// Clear cache for debugging
engine.invalidateCache(); // Clear all
engine.invalidateCache('user-123'); // Clear for specific user

// Enable detailed logging
const options = {
  logAccess: true,
  includeReason: true
};
```

## Future Enhancements

### Planned Features
- **Real-time Permission Updates**: WebSocket-based permission changes
- **Advanced Analytics**: Detailed permission usage analytics
- **Machine Learning**: Intelligent permission recommendations
- **External Integrations**: LDAP, Active Directory, SAML support

### Performance Improvements
- **Distributed Caching**: Redis-based caching for scalability
- **Background Processing**: Async permission preloading
- **Edge Caching**: CDN-based permission caching
- **Database Optimization**: Improved query performance
