# Subscription Enforcement System

## Overview

The Subscription Enforcement System provides comprehensive user limit enforcement based on subscription tiers, ensuring that tenants cannot exceed their plan limits while providing clear upgrade paths and graceful handling of limit scenarios.

## Key Features

### 1. User Limit Enforcement
- **Real-time Validation**: Check user limits before adding new users
- **Subscription Tier Integration**: Enforce limits based on Basic (3 users), Professional (10 users), Enterprise (unlimited)
- **Grace Periods**: Support for trial periods and temporary overages
- **Automatic Counting**: Real-time user count tracking and updates

### 2. Invitation Management
- **Invitation Limits**: Prevent sending invitations when user slots are full
- **Pending Invitation Tracking**: Count pending invitations against user limits
- **Bulk Invitation Validation**: Check limits for multiple invitations at once

### 3. Usage Analytics
- **Detailed Breakdowns**: Active users, inactive users, pending invitations
- **Usage Percentages**: Visual indicators for limit proximity
- **Trend Analysis**: Usage patterns and growth tracking

### 4. Enforcement Actions
- **Smart Blocking**: Block actions that would exceed limits
- **Warning System**: Alert users when approaching limits (80%+)
- **Upgrade Prompts**: Contextual upgrade suggestions with direct links

## Architecture

### Core Components

#### SubscriptionEnforcementService
Main service for subscription limit enforcement:

```typescript
class SubscriptionEnforcementService {
  // User limit checking
  async checkUserLimit(requestedUsers, options): Promise<UserLimitCheck>
  async checkInvitationLimit(requestedInvitations): Promise<InvitationLimitCheck>
  
  // Usage analytics
  async getUserUsageBreakdown(options): Promise<UserUsageBreakdown>
  
  // Enforcement actions
  async enforceUserLimit(requestedUsers, options): Promise<EnforcementAction>
  
  // Maintenance
  async updateUserCount(): Promise<void>
  async cleanupInactiveUsers(inactiveDays): Promise<CleanupResult>
}
```

#### React Hooks
- `useSubscriptionEnforcement()` - Main enforcement hook
- `useUserLimits()` - User limit checking and analytics
- `useInvitationLimits()` - Invitation-specific limits
- `useEnforcementActions()` - Action validation and blocking

#### UI Components
- `UserLimitDisplay` - Visual limit indicators and progress bars
- `UserLimitGate` - Conditional rendering based on limits
- `EnforcementDialog` - Upgrade prompts and limit notifications
- `EnhancedUserInvitation` - Invitation system with limit checking

## Subscription Tiers

### Basic Plan
- **User Limit**: 3 users
- **Features**: Core functionality
- **Overage**: Not allowed
- **Upgrade Path**: Professional Plan

### Professional Plan
- **User Limit**: 10 users
- **Features**: Advanced features + integrations
- **Overage**: 2 users grace period
- **Upgrade Path**: Enterprise Plan

### Enterprise Plan
- **User Limit**: Unlimited
- **Features**: All features + custom integrations
- **Overage**: N/A
- **Support**: Dedicated account management

## Usage Guide

### Basic User Limit Checking

```typescript
import { useUserLimits } from '@/hooks/useSubscriptionEnforcement';

function UserManagement() {
  const { 
    canAddUsers, 
    availableSlots, 
    usagePercentage, 
    isNearLimit, 
    isAtLimit 
  } = useUserLimits();

  const handleAddUser = () => {
    if (!canAddUsers(1)) {
      // Show upgrade prompt
      return;
    }
    // Proceed with user addition
  };

  return (
    <div>
      <div>Available Slots: {availableSlots}</div>
      <div>Usage: {usagePercentage}%</div>
      {isNearLimit && <WarningMessage />}
      {isAtLimit && <UpgradePrompt />}
    </div>
  );
}
```

### User Invitation with Limits

```typescript
import { EnhancedUserInvitation } from '@/components/admin/EnhancedUserInvitation';

function InviteUsers() {
  return (
    <EnhancedUserInvitation
      onInvitationSent={(invitation) => {
        console.log('Invitation sent:', invitation);
        // Update user count
        // Refresh UI
      }}
    />
  );
}
```

### Permission Gates with Limits

```typescript
import { UserLimitGate } from '@/components/subscription/SubscriptionEnforcementComponents';

function AddUserButton() {
  return (
    <UserLimitGate 
      requestedUsers={1}
      showUpgradePrompt={true}
      fallback={<UpgradeRequiredMessage />}
    >
      <Button onClick={handleAddUser}>
        Add User
      </Button>
    </UserLimitGate>
  );
}
```

### Bulk Operations

```typescript
import { useBulkPermissions } from '@/hooks/useSubscriptionEnforcement';

function BulkUserInvitation() {
  const { checkAction } = useEnforcementActions();
  
  const handleBulkInvite = async (emailList: string[]) => {
    const action = await checkAction(emailList.length);
    
    if (action.type === 'block') {
      showUpgradeDialog(action);
      return;
    }
    
    if (action.type === 'warn') {
      showWarningMessage(action);
    }
    
    // Proceed with bulk invitation
    await sendBulkInvitations(emailList);
  };
}
```

## Enforcement Scenarios

### Scenario 1: User at Limit
```typescript
// Current: 10/10 users (Professional Plan)
// Action: Try to add 1 user
// Result: Block with upgrade prompt

{
  type: 'block',
  message: 'User limit exceeded. Upgrade to add more users.',
  upgradeUrl: '/billing/upgrade?plan=enterprise',
  contactSupport: false
}
```

### Scenario 2: User Near Limit
```typescript
// Current: 8/10 users (Professional Plan)
// Action: Try to add 1 user
// Result: Allow with warning

{
  type: 'warn',
  message: 'You\'re using 90% of your user limit. Consider upgrading soon.',
  upgradeUrl: '/billing/upgrade'
}
```

### Scenario 3: Grace Period
```typescript
// Current: 5/5 users (Trial ending in 3 days)
// Action: Try to add 1 user
// Result: Allow with grace period

{
  type: 'allow',
  message: 'Grace period active for trial subscription'
}
```

## Integration Points

### User Management System
- Automatic user count updates when users are added/removed
- Integration with user invitation workflows
- Real-time limit checking in admin interfaces

### Billing System
- Subscription tier detection and limit application
- Upgrade flow integration with contextual prompts
- Usage tracking for billing analytics

### Permission System
- Integration with Module Permission Engine
- Subscription-based feature access control
- Graceful degradation for over-limit scenarios

## Monitoring and Analytics

### Usage Metrics
- Current user count vs. limits
- Usage percentage and trends
- Invitation success/failure rates
- Upgrade conversion tracking

### Alerts and Notifications
- Near-limit warnings (80% usage)
- At-limit notifications
- Failed invitation attempts
- Cleanup recommendations

### Reporting
- Monthly usage reports
- Limit breach incidents
- Upgrade opportunity identification
- Cost optimization recommendations

## Security Considerations

### Tenant Isolation
- All limits are tenant-scoped
- Cross-tenant limit checking prevention
- Secure user count validation

### Data Integrity
- Atomic user count updates
- Consistent limit enforcement
- Audit trail for limit changes

### Performance
- Cached limit checks for frequently accessed data
- Efficient user counting queries
- Optimized bulk operations

## Troubleshooting

### Common Issues

1. **Incorrect User Counts**
   - Run `updateUserCount()` to refresh
   - Check for orphaned user records
   - Verify subscription plan configuration

2. **Limits Not Enforcing**
   - Verify subscription status
   - Check plan feature configuration
   - Review enforcement service initialization

3. **Performance Issues**
   - Monitor user counting query performance
   - Implement caching for frequent checks
   - Optimize bulk operations

### Debug Tools

```typescript
// Get detailed usage breakdown
const breakdown = await service.getUserUsageBreakdown({
  includeInactive: true,
  includePending: true
});

// Force user count update
await service.updateUserCount();

// Check specific limit
const limitCheck = await service.checkUserLimit(5, {
  allowOverage: true,
  gracePeriodDays: 7
});
```

## Future Enhancements

### Planned Features
- **Dynamic Limits**: Adjust limits based on usage patterns
- **Predictive Analytics**: Forecast when limits will be reached
- **Automated Cleanup**: Smart inactive user removal
- **Custom Overages**: Configurable overage policies

### Advanced Enforcement
- **Time-based Limits**: Different limits for different time periods
- **Role-based Limits**: Different limits for different user types
- **Geographic Limits**: Region-specific user limits
- **API Rate Limiting**: Request-based enforcement

## Best Practices

### Implementation
1. Always check limits before user operations
2. Provide clear upgrade paths and messaging
3. Use grace periods for trial users
4. Implement proper error handling

### User Experience
1. Show progress indicators for limit usage
2. Provide contextual upgrade prompts
3. Explain limit benefits clearly
4. Offer alternative solutions when blocked

### Performance
1. Cache frequently checked limits
2. Use bulk operations when possible
3. Monitor and optimize query performance
4. Implement proper error boundaries
