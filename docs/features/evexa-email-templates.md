# EVEXA Email Templates System

## Overview

The EVEXA Email Templates System provides professional, branded email templates for all system communications including user invitations, welcome messages, security notifications, and account management. The system integrates with Firebase Email Extension and supports tenant-specific branding customization.

## Key Features

### 1. Professional Email Templates
- **User Invitation**: Professional invitation emails with role assignment
- **Welcome Messages**: Comprehensive onboarding emails for new users
- **Password Reset**: Secure password reset with time-limited links
- **Account Security**: Suspension and security notifications
- **System Notifications**: Various system-generated communications

### 2. EVEXA Branding
- **Consistent Design**: Professional EVEXA brand identity across all emails
- **Responsive Layout**: Mobile-optimized templates that work on all devices
- **Tenant Customization**: Company logos and branding integration
- **Professional Typography**: Clean, readable fonts and styling

### 3. Advanced Features
- **Variable Replacement**: Dynamic content with Handlebars-style variables
- **Conditional Content**: Show/hide content based on data availability
- **Multi-format Support**: Both HTML and plain text versions
- **Template Validation**: Built-in validation and error checking

## Template Categories

### System Templates (Transactional)

#### User Invitation Template (`user_invitation_v1`)
- **Purpose**: Invite new users to join a tenant organization
- **Features**: Role assignment, expiration dates, security notices
- **Variables**: recipientName, senderName, roleName, invitationUrl, expirationDate
- **Branding**: Tenant logo, company name, support contact

#### Welcome Email Template (`welcome_new_user_v1`)
- **Purpose**: Welcome new users after account creation
- **Features**: Quick start guide, help resources, dashboard access
- **Variables**: recipientName, loginUrl, tenantName
- **Content**: Onboarding steps, feature highlights, support links

#### Password Reset Template (`password_reset_v1`)
- **Purpose**: Secure password reset functionality
- **Features**: Time-limited links, security information, clear instructions
- **Variables**: recipientName, resetUrl, tenantName
- **Security**: 1-hour expiration, clear security messaging

#### Account Suspension Template (`account_suspended_v1`)
- **Purpose**: Notify users of account suspension
- **Features**: Suspension reason, next steps, support contact
- **Variables**: recipientName, suspensionReason, supportEmail
- **Tone**: Professional, informative, supportive

## Technical Implementation

### Template Structure

```typescript
interface EmailTemplate {
  id: string;                    // Unique template identifier
  name: string;                  // Human-readable name
  description: string;           // Template description
  category: 'transactional';     // Template category
  type: 'invitation' | 'welcome' | 'alert';
  subject: string;               // Email subject with variables
  preheader?: string;            // Email preheader text
  htmlContent: string;           // HTML email content
  textContent: string;           // Plain text version
  variables: EmailVariable[];    // Required/optional variables
  tags: string[];               // Template tags
  isActive: boolean;            // Template status
  isPremium: boolean;           // Premium feature flag
}
```

### Variable System

```typescript
interface EmailVariable {
  name: string;                  // Variable name (e.g., 'recipientName')
  label: string;                 // Display label
  type: 'text' | 'email' | 'url' | 'date' | 'image';
  required: boolean;             // Required flag
  defaultValue?: string;         // Default value
  description?: string;          // Variable description
}
```

### Branding Integration

```typescript
interface TenantEmailBranding {
  companyName: string;           // Tenant company name
  logoUrl?: string;              // Company logo URL
  primaryColor?: string;         // Brand primary color
  secondaryColor?: string;       // Brand secondary color
  supportEmail?: string;         // Support contact email
  fromName?: string;             // Email sender name
  fromEmail?: string;            // Email sender address
}
```

## Usage Guide

### Basic Email Sending

```typescript
import { useSystemEmail } from '@/hooks/useSystemEmail';

function InviteUser() {
  const { sendUserInvitation } = useSystemEmail();

  const handleInvite = async () => {
    const result = await sendUserInvitation(
      '<EMAIL>',           // Recipient email
      'John Doe',                   // Recipient name
      'Jane Smith',                 // Sender name
      'Exhibition Manager',         // Role name
      'https://app.evexa.com/accept?token=abc123', // Invitation URL
      '2024-01-15'                  // Expiration date
    );

    if (result.success) {
      console.log('Invitation sent:', result.messageId);
    } else {
      console.error('Failed to send:', result.error);
    }
  };

  return <Button onClick={handleInvite}>Send Invitation</Button>;
}
```

### Custom Email Sending

```typescript
import { useSystemEmail } from '@/hooks/useSystemEmail';

function CustomEmail() {
  const { sendEmail } = useSystemEmail();

  const handleSend = async () => {
    const result = await sendEmail({
      to: '<EMAIL>',
      templateId: 'welcome_new_user_v1',
      variables: {
        recipientName: 'John Doe',
        loginUrl: 'https://app.evexa.com/dashboard'
      },
      priority: 'high',
      trackOpens: true,
      trackClicks: true
    });

    return result;
  };
}
```

### Template Preview

```typescript
import { EmailTemplatePreview } from '@/components/email/EmailTemplatePreview';

function AdminEmailSettings() {
  return (
    <div>
      <h1>Email Template Management</h1>
      <EmailTemplatePreview />
    </div>
  );
}
```

## Email Queue System

### Queue Processing
- **Immediate Processing**: High-priority emails sent immediately
- **Scheduled Delivery**: Support for scheduled email sending
- **Retry Logic**: Automatic retry for failed deliveries
- **Status Tracking**: Complete delivery status monitoring

### Queue States
- `queued`: Email queued for sending
- `sending`: Currently being processed
- `sent`: Successfully sent to email service
- `delivered`: Confirmed delivery to recipient
- `failed`: Delivery failed (with retry logic)
- `bounced`: Email bounced back

### Priority Levels
- `high`: Security alerts, invitations (immediate processing)
- `normal`: Welcome emails, notifications (standard queue)
- `low`: Newsletters, bulk communications (background processing)

## Branding Customization

### Tenant Logo Integration
```typescript
// Automatic logo integration in templates
{{#if tenantLogo}}
<img src="{{tenantLogo}}" alt="{{tenantName}}" class="tenant-logo">
{{/if}}
```

### Color Customization
```css
.evexa-header {
  background: linear-gradient(135deg, {{primaryColor}} 0%, {{secondaryColor}} 100%);
}

.evexa-button {
  background: {{primaryColor}};
}
```

### Custom From Address
```typescript
// Tenant-specific sender configuration
{
  from: {
    email: branding.fromEmail || '<EMAIL>',
    name: branding.fromName || branding.companyName || 'EVEXA'
  },
  replyTo: branding.supportEmail || '<EMAIL>'
}
```

## Security Features

### Template Security
- **Variable Sanitization**: All variables are sanitized before insertion
- **XSS Prevention**: HTML content is validated and escaped
- **Link Validation**: All URLs are validated for security
- **Content Filtering**: Malicious content detection and removal

### Delivery Security
- **Encrypted Transmission**: All emails sent over encrypted connections
- **Authentication**: SPF, DKIM, and DMARC authentication
- **Rate Limiting**: Prevents abuse and spam
- **Audit Trail**: Complete sending history and tracking

### Privacy Protection
- **Data Minimization**: Only necessary data included in emails
- **Secure Links**: Time-limited, encrypted invitation links
- **Unsubscribe Support**: Easy unsubscribe mechanisms
- **GDPR Compliance**: Privacy-compliant email handling

## Analytics and Monitoring

### Email Metrics
- **Delivery Rates**: Successful delivery percentages
- **Open Rates**: Email open tracking and analytics
- **Click Rates**: Link click tracking and analysis
- **Bounce Rates**: Bounce detection and management

### Template Performance
- **Usage Statistics**: Template usage frequency
- **Success Rates**: Template-specific delivery success
- **User Engagement**: Recipient interaction metrics
- **A/B Testing**: Template variant performance comparison

## Integration Points

### Firebase Email Extension
- **Seamless Integration**: Direct integration with Firebase Email Extension
- **Automatic Processing**: Background email processing
- **Delivery Tracking**: Real-time delivery status updates
- **Error Handling**: Comprehensive error reporting and retry logic

### User Management System
- **Invitation Workflow**: Integrated with user invitation process
- **Account Lifecycle**: Automated emails for account events
- **Role Assignment**: Dynamic role-based email content
- **Subscription Integration**: Subscription-aware email features

### Tenant Management
- **Multi-tenant Support**: Isolated email sending per tenant
- **Branding Inheritance**: Automatic tenant branding application
- **Custom Domains**: Support for custom email domains
- **White-label Options**: Complete branding customization

## Best Practices

### Template Design
1. **Mobile-First**: Design for mobile devices first
2. **Clear CTAs**: Prominent, clear call-to-action buttons
3. **Scannable Content**: Use headers, bullets, and white space
4. **Brand Consistency**: Maintain consistent branding across templates

### Content Guidelines
1. **Clear Subject Lines**: Descriptive, action-oriented subjects
2. **Personal Tone**: Professional but friendly communication
3. **Value Proposition**: Clear benefits and next steps
4. **Security Messaging**: Transparent security information

### Technical Optimization
1. **Image Optimization**: Compressed, web-optimized images
2. **Fallback Content**: Plain text versions for all emails
3. **Link Testing**: Validate all links before sending
4. **Performance Monitoring**: Track and optimize delivery performance

## Troubleshooting

### Common Issues
1. **Template Not Rendering**: Check variable names and syntax
2. **Images Not Loading**: Verify image URLs and accessibility
3. **Delivery Failures**: Check email addresses and domain settings
4. **Branding Issues**: Verify tenant branding configuration

### Debug Tools
```typescript
// Template validation
const validation = validateTemplate(templateId);
console.log('Validation result:', validation);

// Variable testing
const preview = replaceTemplateVariables(template, testVariables);
console.log('Preview content:', preview);

// Delivery tracking
const status = await getEmailStatus(messageId);
console.log('Delivery status:', status);
```

## Future Enhancements

### Planned Features
- **Template Builder**: Visual template editor
- **Advanced Analytics**: Detailed engagement metrics
- **A/B Testing**: Built-in template testing framework
- **Automation Rules**: Trigger-based email automation

### Integration Roadmap
- **CRM Integration**: Connect with customer relationship management
- **Marketing Automation**: Advanced marketing email workflows
- **External Providers**: Support for multiple email service providers
- **API Extensions**: Enhanced API for custom integrations
