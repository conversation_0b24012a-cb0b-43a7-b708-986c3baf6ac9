# Custom Persona Creation Feature

## Overview

The Custom Persona Creation feature allows tenant administrators to create custom personas with specific module access combinations tailored to their organization's needs. This feature is part of EVEXA's multi-tenant architecture and provides flexible role-based access control.

## Features

### 1. Persona Templates
- **Blank Persona**: Start with no permissions and configure manually
- **Exhibition Focused**: Core exhibition management with basic permissions
- **Marketing Focused**: Marketing and lead management with social media access
- **Financial Focused**: Budget and expense management with financial reporting

### 2. Module Permissions Editor
- Visual interface for configuring module permissions
- Category-based filtering (core, financial, marketing, logistics, analytics, admin)
- Individual permission actions (read, write, delete, admin)
- Quick preset buttons (none, read, write, admin)
- Real-time permission preview

### 3. System Permissions
- User management capabilities
- Settings access control
- Analytics viewing permissions
- Data export rights
- Integration management
- Support access

### 4. Target Users Management
- Define target user types for each persona
- Tag-based interface for easy management
- Helps with persona organization and assignment

### 5. Persona Management
- Create custom personas from templates or scratch
- Edit existing custom personas
- Duplicate personas (including default ones)
- Delete custom personas with safety checks
- View persona details and permissions

## Technical Implementation

### Core Components

#### PersonaEditorDialog
- Main dialog for creating/editing personas
- Form validation and error handling
- Template selection interface
- Module permissions configuration
- System permissions setup

#### ModulePermissionsEditor
- Grid-based module selection interface
- Category filtering
- Permission level presets
- Individual action toggles
- Visual feedback for configured modules

#### PersonaTemplateSelector
- Pre-built persona templates
- Template preview and selection
- Quick start for common use cases

#### TargetUsersEditor
- Tag-based user type management
- Add/remove target user types
- Keyboard shortcuts for efficiency

### Data Structure

```typescript
interface PersonaTemplate {
  id: string;
  name: string;
  description: string;
  category: 'default' | 'custom';
  isActive: boolean;
  permissions: PersonaPermissions;
  targetUsers: string[];
  limitations?: {
    maxExhibitions?: number;
    maxLeads?: number;
    maxBudget?: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

interface PersonaPermissions {
  modules: ModulePermission[];
  systemPermissions: {
    canManageUsers: boolean;
    canManageSettings: boolean;
    canViewAnalytics: boolean;
    canExportData: boolean;
    canManageIntegrations: boolean;
    canAccessSupport: boolean;
  };
}
```

### Service Layer

#### TenantPersonaService
- `createCustomPersona()`: Create new custom persona
- `updateCustomPersona()`: Update existing custom persona
- `deleteCustomPersona()`: Delete custom persona with validation
- `getAllPersonas()`: Get all personas (default + custom)
- `assignPersonaToUser()`: Assign persona to user

## Usage Guide

### Creating a Custom Persona

1. **Navigate to User Role Management**
   - Go to Admin → User Role Management
   - Click on "Persona Management" tab

2. **Start Persona Creation**
   - Click "Create Persona" button
   - Choose a template or start blank

3. **Configure Basic Information**
   - Enter persona name (required)
   - Add description (required)
   - Set active status

4. **Set Module Permissions**
   - Filter by category if needed
   - Use preset buttons for quick setup
   - Toggle individual permissions as needed

5. **Configure System Permissions**
   - Set administrative capabilities
   - Control access to sensitive features

6. **Add Target Users**
   - Define user types this persona is for
   - Use descriptive labels

7. **Save and Assign**
   - Validate all required fields
   - Save the persona
   - Assign to users as needed

### Duplicating Personas

1. Find the persona to duplicate
2. Click the duplicate button (copy icon)
3. Modify the duplicated persona as needed
4. Save with a new name

### Managing Personas

- **View**: Click the eye icon to view persona details
- **Edit**: Click the edit icon to modify custom personas
- **Delete**: Click the trash icon to delete (with confirmation)
- **Duplicate**: Click the copy icon to create a copy

## Validation Rules

### Required Fields
- Persona name must not be empty
- Description must not be empty
- At least one module permission must be configured

### Business Rules
- Custom personas can be edited/deleted
- Default personas can only be viewed/duplicated
- Personas in use cannot be deleted
- Persona names must be unique within tenant

## Security Considerations

### Tenant Isolation
- All personas are isolated by tenantId
- Users can only access personas from their tenant
- Cross-tenant access is prevented

### Permission Validation
- Module permissions are validated against available modules
- System permissions are checked before granting access
- Subscription limits are enforced

### Access Control
- Only users with `canManageUsers` permission can create personas
- Persona assignment requires administrative privileges
- Audit trail for persona changes

## Integration Points

### User Management
- Personas are assigned to users through PersonaAssignment
- User permissions are derived from assigned persona
- Real-time permission updates

### Module Access Control
- Each module checks user persona permissions
- Graceful degradation for insufficient permissions
- Clear feedback for restricted features

### Subscription Enforcement
- Persona features respect subscription limits
- Premium modules require appropriate subscription
- Upgrade prompts for restricted features

## Future Enhancements

### Planned Features
- Persona analytics and usage statistics
- Bulk persona assignment
- Persona inheritance and hierarchies
- Advanced permission conditions
- Integration with external identity providers

### Performance Optimizations
- Cached permission lookups
- Optimized database queries
- Real-time permission updates
- Background permission validation

## Troubleshooting

### Common Issues
1. **Persona not saving**: Check validation errors
2. **Permissions not working**: Verify module configuration
3. **Users can't access features**: Check persona assignment
4. **Duplicate names**: Ensure unique persona names

### Debug Steps
1. Check browser console for errors
2. Verify tenantId in requests
3. Confirm user has management permissions
4. Review persona assignment status
