#!/usr/bin/env node

/**
 * PROPER Firebase Cleanup - Fix the lazy cleanup
 */

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    }),
    projectId: process.env.FIREBASE_PROJECT_ID
  });
}

const db = admin.firestore();

async function properCleanup() {
  console.log('🧹 PROPER CLEANUP - Fixing the lazy cleanup job...\n');
  
  // Step 1: Delete remaining root-level collections that should have been removed
  const rootCollectionsToDelete = ['tasks', 'user_profiles', 'users'];
  
  console.log('🗑️  Deleting remaining root-level collections...\n');
  
  for (const collectionName of rootCollectionsToDelete) {
    try {
      const snapshot = await db.collection(collectionName).get();
      if (!snapshot.empty) {
        console.log(`🗑️  Deleting ${collectionName} (${snapshot.size} docs)`);
        const batch = db.batch();
        snapshot.docs.forEach(doc => batch.delete(doc.ref));
        await batch.commit();
        console.log(`✅ Deleted ${snapshot.size} documents from ${collectionName}`);
      } else {
        console.log(`✅ ${collectionName} already empty`);
      }
    } catch (error) {
      console.error(`❌ Error deleting ${collectionName}:`, error.message);
    }
  }
  
  // Step 2: Clean up the messy tenant-data structure
  console.log('\n🧹 Cleaning up messy tenant-data structure...\n');
  
  try {
    const tenantDataSnapshot = await db.collection('tenant-data').get();
    
    for (const doc of tenantDataSnapshot.docs) {
      console.log(`🗑️  Processing tenant-data/${doc.id}`);
      
      // Get all subcollections
      const subcollections = await doc.ref.listCollections();
      
      for (const subcol of subcollections) {
        const subDocs = await subcol.get();
        if (!subDocs.empty) {
          console.log(`   🗑️  Deleting ${subDocs.size} docs from ${subcol.id}`);
          const batch = db.batch();
          subDocs.docs.forEach(subDoc => batch.delete(subDoc.ref));
          await batch.commit();
          console.log(`   ✅ Deleted ${subDocs.size} docs from ${subcol.id}`);
        }
      }
      
      // Delete the parent document
      await doc.ref.delete();
      console.log(`   ✅ Deleted tenant-data/${doc.id}`);
    }
    
    console.log('✅ Cleaned up tenant-data structure');
  } catch (error) {
    console.error('❌ Error cleaning tenant-data:', error.message);
  }
  
  // Step 3: Verify final structure
  console.log('\n📋 Verifying final database structure...\n');
  
  const allCollections = await db.listCollections();
  console.log('🔍 Remaining root-level collections:');
  
  for (const collection of allCollections) {
    const snapshot = await collection.limit(1).get();
    console.log(`   - ${collection.id} (${snapshot.size > 0 ? 'has data' : 'empty'})`);
  }
  
  // Check tenants structure
  const tenantsSnapshot = await db.collection('tenants').get();
  console.log(`\n🏢 Tenants (${tenantsSnapshot.size}):`);
  
  for (const tenant of tenantsSnapshot.docs) {
    console.log(`   - ${tenant.id}: ${tenant.data().name}`);
    
    // Check tenant collections
    const tenantCollections = await tenant.ref.listCollections();
    console.log(`     Collections: ${tenantCollections.length}`);
    
    for (const col of tenantCollections.slice(0, 5)) { // Show first 5
      const colSnapshot = await col.limit(1).get();
      console.log(`       - ${col.id} (${colSnapshot.size > 0 ? 'has data' : 'empty'})`);
    }
    if (tenantCollections.length > 5) {
      console.log(`       ... and ${tenantCollections.length - 5} more`);
    }
  }
  
  // Step 4: Clean up duplicate tenants
  console.log('\n🧹 Cleaning up duplicate tenants...\n');

  const correctTenantId = 'evexa-development-company';
  const allTenantsSnapshot = await db.collection('tenants').get();

  for (const tenant of allTenantsSnapshot.docs) {
    if (tenant.id !== correctTenantId) {
      console.log(`🗑️  Deleting duplicate tenant: ${tenant.id}`);

      // Delete all subcollections first
      const subcollections = await tenant.ref.listCollections();
      for (const subcol of subcollections) {
        const subDocs = await subcol.get();
        if (!subDocs.empty) {
          const batch = db.batch();
          subDocs.docs.forEach(doc => batch.delete(doc.ref));
          await batch.commit();
          console.log(`   ✅ Deleted ${subDocs.size} docs from ${subcol.id}`);
        }
      }

      // Delete the tenant document
      await tenant.ref.delete();
      console.log(`   ✅ Deleted tenant ${tenant.id}`);
    } else {
      console.log(`✅ Keeping correct tenant: ${tenant.id}`);
    }
  }

  console.log('\n✅ PROPER CLEANUP COMPLETED!');
  console.log('\n🎯 Your database should now have ONLY:');
  console.log('├── tenants/');
  console.log('│   └── evexa-development-company/');
  console.log('│       ├── users/ (with <EMAIL>)');
  console.log('│       └── ... (18 empty collections)');
  console.log('└── tenant-users (system collection)');
}

async function verifyFinalStructure() {
  console.log('\n🔍 FINAL VERIFICATION - Database Structure\n');

  const allCollections = await db.listCollections();
  console.log('📋 Root-level collections:');

  for (const collection of allCollections) {
    const snapshot = await collection.limit(1).get();
    console.log(`   - ${collection.id} (${snapshot.size > 0 ? 'has data' : 'empty'})`);
  }

  const tenantsSnapshot = await db.collection('tenants').get();
  console.log(`\n🏢 Tenants (${tenantsSnapshot.size}):`);

  for (const tenant of tenantsSnapshot.docs) {
    console.log(`   - ${tenant.id}: ${tenant.data().name}`);

    const tenantCollections = await tenant.ref.listCollections();
    console.log(`     Collections: ${tenantCollections.length}`);

    // Check users collection specifically
    const usersCol = tenantCollections.find(col => col.id === 'users');
    if (usersCol) {
      const usersSnapshot = await usersCol.get();
      console.log(`       - users: ${usersSnapshot.size} user(s)`);
      usersSnapshot.docs.forEach(user => {
        const userData = user.data();
        console.log(`         * ${userData.email} (${userData.role})`);
      });
    }
  }

  console.log('\n✅ VERIFICATION COMPLETE!');
}

if (require.main === module) {
  properCleanup()
    .then(() => verifyFinalStructure())
    .catch(console.error);
}
