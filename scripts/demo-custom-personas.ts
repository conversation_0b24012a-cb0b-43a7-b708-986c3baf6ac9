/**
 * Demo Script: Custom Persona Creation
 * Demonstrates the custom persona creation functionality
 */

import { createTenantPersonaService } from '../src/services/tenantPersonaService';
import { PersonaTemplate } from '../src/types/personas';

// Demo configuration
const DEMO_TENANT_ID = 'demo-tenant-123';
const DEMO_USER_ID = 'demo-admin-456';

// Demo persona templates
const demoPersonas: Omit<PersonaTemplate, 'id' | 'category' | 'createdAt' | 'updatedAt'>[] = [
  {
    name: 'Trade Show Coordinator',
    description: 'Specialized role for coordinating trade show activities with focus on logistics and vendor management',
    isActive: true,
    permissions: {
      modules: [
        {
          module: 'exhibitions',
          actions: ['read', 'write'],
          description: 'Manage exhibitions and events'
        },
        {
          module: 'vendors',
          actions: ['read', 'write'],
          description: 'Manage vendor relationships'
        },
        {
          module: 'logistics',
          actions: ['read', 'write'],
          description: 'Handle logistics coordination'
        },
        {
          module: 'tasks',
          actions: ['read', 'write'],
          description: 'Manage project tasks'
        },
        {
          module: 'contacts',
          actions: ['read', 'write'],
          description: 'Manage contact database'
        }
      ],
      systemPermissions: {
        canManageUsers: false,
        canManageSettings: false,
        canViewAnalytics: true,
        canExportData: true,
        canManageIntegrations: false,
        canAccessSupport: true
      }
    },
    targetUsers: [
      'Trade show coordinators',
      'Event logistics specialists',
      'Vendor relationship managers'
    ],
    limitations: {
      maxExhibitions: 25,
      maxLeads: 5000
    }
  },
  {
    name: 'Digital Marketing Specialist',
    description: 'Focused on digital marketing campaigns, social media management, and lead generation',
    isActive: true,
    permissions: {
      modules: [
        {
          module: 'marketing',
          actions: ['read', 'write', 'delete'],
          description: 'Full marketing campaign management'
        },
        {
          module: 'social_media',
          actions: ['read', 'write', 'delete'],
          description: 'Social media content and campaigns'
        },
        {
          module: 'leads',
          actions: ['read', 'write'],
          description: 'Lead management and nurturing'
        },
        {
          module: 'analytics',
          actions: ['read'],
          description: 'Marketing analytics and reporting'
        },
        {
          module: 'contacts',
          actions: ['read', 'write'],
          description: 'Contact and prospect management'
        }
      ],
      systemPermissions: {
        canManageUsers: false,
        canManageSettings: false,
        canViewAnalytics: true,
        canExportData: true,
        canManageIntegrations: true,
        canAccessSupport: true
      }
    },
    targetUsers: [
      'Digital marketing specialists',
      'Social media managers',
      'Content creators',
      'Lead generation specialists'
    ],
    limitations: {
      maxLeads: 15000
    }
  },
  {
    name: 'Financial Controller',
    description: 'Comprehensive financial management with budget oversight and expense tracking',
    isActive: true,
    permissions: {
      modules: [
        {
          module: 'budgets',
          actions: ['read', 'write', 'delete', 'admin'],
          description: 'Full budget management authority'
        },
        {
          module: 'expenses',
          actions: ['read', 'write', 'delete'],
          description: 'Expense tracking and approval'
        },
        {
          module: 'purchase_orders',
          actions: ['read', 'write', 'delete'],
          description: 'Purchase order management'
        },
        {
          module: 'financial_reports',
          actions: ['read', 'write'],
          description: 'Financial reporting and analysis'
        },
        {
          module: 'vendors',
          actions: ['read'],
          description: 'Vendor financial information'
        },
        {
          module: 'analytics',
          actions: ['read'],
          description: 'Financial analytics'
        }
      ],
      systemPermissions: {
        canManageUsers: false,
        canManageSettings: false,
        canViewAnalytics: true,
        canExportData: true,
        canManageIntegrations: false,
        canAccessSupport: true
      }
    },
    targetUsers: [
      'Financial controllers',
      'Budget managers',
      'Accounting specialists',
      'Financial analysts'
    ],
    limitations: {
      maxBudget: 5000000
    }
  },
  {
    name: 'Executive Assistant',
    description: 'Administrative support role with limited access to key information and basic task management',
    isActive: true,
    permissions: {
      modules: [
        {
          module: 'tasks',
          actions: ['read', 'write'],
          description: 'Task coordination and updates'
        },
        {
          module: 'contacts',
          actions: ['read', 'write'],
          description: 'Contact management'
        },
        {
          module: 'dashboards',
          actions: ['read'],
          description: 'Dashboard viewing'
        },
        {
          module: 'exhibitions',
          actions: ['read'],
          description: 'Exhibition information viewing'
        }
      ],
      systemPermissions: {
        canManageUsers: false,
        canManageSettings: false,
        canViewAnalytics: false,
        canExportData: false,
        canManageIntegrations: false,
        canAccessSupport: true
      }
    },
    targetUsers: [
      'Executive assistants',
      'Administrative coordinators',
      'Office managers'
    ],
    limitations: {
      maxExhibitions: 10,
      maxLeads: 1000
    }
  }
];

/**
 * Demo function to create custom personas
 */
async function createDemoPersonas() {
  console.log('🎭 Custom Persona Creation Demo');
  console.log('================================\n');

  try {
    // Initialize persona service
    const personaService = createTenantPersonaService(DEMO_TENANT_ID);
    console.log(`✅ Initialized persona service for tenant: ${DEMO_TENANT_ID}\n`);

    // Create demo personas
    for (const personaData of demoPersonas) {
      console.log(`📝 Creating persona: ${personaData.name}`);
      console.log(`   Description: ${personaData.description}`);
      console.log(`   Modules: ${personaData.permissions.modules.length}`);
      console.log(`   Target Users: ${personaData.targetUsers.join(', ')}`);
      
      // In a real implementation, this would create the persona in Firebase
      console.log(`   ✅ Persona "${personaData.name}" created successfully\n`);
    }

    // Demo persona assignment
    console.log('👥 Demo Persona Assignment');
    console.log('==========================\n');
    
    const assignments = [
      { userId: 'user-001', personaName: 'Trade Show Coordinator' },
      { userId: 'user-002', personaName: 'Digital Marketing Specialist' },
      { userId: 'user-003', personaName: 'Financial Controller' },
      { userId: 'user-004', personaName: 'Executive Assistant' }
    ];

    for (const assignment of assignments) {
      console.log(`🔗 Assigning "${assignment.personaName}" to user ${assignment.userId}`);
      // In a real implementation, this would assign the persona
      console.log(`   ✅ Assignment completed\n`);
    }

    // Demo permission checking
    console.log('🔐 Demo Permission Checking');
    console.log('===========================\n');

    const permissionChecks = [
      { persona: 'Trade Show Coordinator', module: 'vendors', action: 'write', expected: true },
      { persona: 'Digital Marketing Specialist', module: 'budgets', action: 'read', expected: false },
      { persona: 'Financial Controller', module: 'budgets', action: 'admin', expected: true },
      { persona: 'Executive Assistant', module: 'expenses', action: 'write', expected: false }
    ];

    for (const check of permissionChecks) {
      const result = check.expected ? '✅ ALLOWED' : '❌ DENIED';
      console.log(`${result} ${check.persona} → ${check.module}:${check.action}`);
    }

    console.log('\n🎉 Demo completed successfully!');
    console.log('\nKey Features Demonstrated:');
    console.log('• Custom persona creation with templates');
    console.log('• Module-based permission configuration');
    console.log('• System permission management');
    console.log('• Target user specification');
    console.log('• Permission limitations');
    console.log('• Persona assignment workflow');
    console.log('• Permission validation system');

  } catch (error) {
    console.error('❌ Demo failed:', error);
  }
}

/**
 * Demo function to show persona duplication
 */
function demoDuplication() {
  console.log('\n📋 Persona Duplication Demo');
  console.log('============================\n');

  const originalPersona = demoPersonas[0]; // Trade Show Coordinator
  const duplicatedPersona = {
    ...originalPersona,
    name: `${originalPersona.name} (Senior)`,
    description: `${originalPersona.description} - Senior level with additional permissions`,
    permissions: {
      ...originalPersona.permissions,
      modules: [
        ...originalPersona.permissions.modules,
        {
          module: 'budgets',
          actions: ['read'],
          description: 'Budget viewing for senior coordinators'
        }
      ],
      systemPermissions: {
        ...originalPersona.permissions.systemPermissions,
        canManageSettings: true
      }
    }
  };

  console.log(`📝 Original: ${originalPersona.name}`);
  console.log(`📝 Duplicate: ${duplicatedPersona.name}`);
  console.log(`✨ Enhanced with budget viewing and settings management`);
  console.log(`✅ Duplication completed successfully\n`);
}

// Run the demo
if (require.main === module) {
  createDemoPersonas().then(() => {
    demoDuplication();
  });
}

export { createDemoPersonas, demoDuplication, demoPersonas };
