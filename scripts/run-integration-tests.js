#!/usr/bin/env node

/**
 * Integration Test Runner
 * Comprehensive test runner for end-to-end integration testing
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Test configuration
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  timeout: 30000,
  retries: 2,
  workers: 1, // Sequential execution for integration tests
  browsers: ['chromium'], // Focus on Chromium for integration tests
  headless: true
};

// Test suites
const TEST_SUITES = {
  'complete-journey': {
    file: 'e2e/complete-user-journey.spec.ts',
    description: 'Complete user journey from invitation to system usage',
    timeout: 120000 // 2 minutes for complete journey
  },
  'persona-workflows': {
    file: 'e2e/persona-workflows.spec.ts',
    description: 'Persona-specific workflows and permissions',
    timeout: 90000 // 1.5 minutes for persona tests
  },
  'user-onboarding': {
    file: 'e2e/user-onboarding.spec.ts',
    description: 'User onboarding and registration flows',
    timeout: 60000 // 1 minute for onboarding
  }
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logStep(step, message) {
  log(`\n[${step}] ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// Check if development server is running
async function checkDevServer() {
  logStep('SETUP', 'Checking development server...');
  
  try {
    const response = await fetch(TEST_CONFIG.baseUrl);
    if (response.ok) {
      logSuccess('Development server is running');
      return true;
    }
  } catch (error) {
    logError('Development server is not running');
    log('Please start the development server with: npm run dev', 'yellow');
    return false;
  }
}

// Setup test environment
async function setupTestEnvironment() {
  logStep('SETUP', 'Setting up test environment...');
  
  // Check if Playwright is installed
  try {
    require('@playwright/test');
    logSuccess('Playwright is installed');
  } catch (error) {
    logError('Playwright is not installed');
    log('Please install Playwright with: npx playwright install', 'yellow');
    return false;
  }
  
  // Check test files exist
  const missingFiles = [];
  for (const [name, config] of Object.entries(TEST_SUITES)) {
    if (!fs.existsSync(config.file)) {
      missingFiles.push(config.file);
    }
  }
  
  if (missingFiles.length > 0) {
    logError('Missing test files:');
    missingFiles.forEach(file => log(`  - ${file}`, 'red'));
    return false;
  }
  
  logSuccess('Test environment is ready');
  return true;
}

// Run a specific test suite
async function runTestSuite(suiteName, config) {
  return new Promise((resolve) => {
    logStep('TEST', `Running ${config.description}...`);
    
    const args = [
      'test',
      config.file,
      '--config=playwright.config.ts',
      `--timeout=${config.timeout}`,
      `--retries=${TEST_CONFIG.retries}`,
      `--workers=${TEST_CONFIG.workers}`,
      '--reporter=list'
    ];
    
    if (TEST_CONFIG.headless) {
      args.push('--headed=false');
    }
    
    const child = spawn('npx', ['playwright', ...args], {
      stdio: 'pipe',
      cwd: process.cwd()
    });
    
    let output = '';
    let errorOutput = '';
    
    child.stdout.on('data', (data) => {
      output += data.toString();
      process.stdout.write(data);
    });
    
    child.stderr.on('data', (data) => {
      errorOutput += data.toString();
      process.stderr.write(data);
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        logSuccess(`${suiteName} tests passed`);
        resolve({ success: true, output, errorOutput });
      } else {
        logError(`${suiteName} tests failed with code ${code}`);
        resolve({ success: false, output, errorOutput, code });
      }
    });
    
    child.on('error', (error) => {
      logError(`Failed to run ${suiteName} tests: ${error.message}`);
      resolve({ success: false, error: error.message });
    });
  });
}

// Generate test report
function generateTestReport(results) {
  logHeader('INTEGRATION TEST REPORT');
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(r => r.success).length;
  const failedTests = totalTests - passedTests;
  
  log(`\nTotal Test Suites: ${totalTests}`);
  log(`Passed: ${passedTests}`, passedTests > 0 ? 'green' : 'reset');
  log(`Failed: ${failedTests}`, failedTests > 0 ? 'red' : 'reset');
  
  log('\nDetailed Results:', 'bright');
  for (const [suiteName, result] of Object.entries(results)) {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    const color = result.success ? 'green' : 'red';
    log(`  ${status} ${suiteName}`, color);
    
    if (!result.success && result.code) {
      log(`    Exit code: ${result.code}`, 'red');
    }
    
    if (!result.success && result.error) {
      log(`    Error: ${result.error}`, 'red');
    }
  }
  
  // Overall result
  if (failedTests === 0) {
    log('\n🎉 All integration tests passed!', 'green');
    return true;
  } else {
    log(`\n💥 ${failedTests} test suite(s) failed`, 'red');
    return false;
  }
}

// Main execution function
async function main() {
  const args = process.argv.slice(2);
  const specificSuite = args.find(arg => !arg.startsWith('--'));
  const headlessFlag = args.includes('--headless');
  const verboseFlag = args.includes('--verbose');
  
  if (headlessFlag) {
    TEST_CONFIG.headless = true;
  }
  
  logHeader('EVEXA INTEGRATION TEST RUNNER');
  
  // Pre-flight checks
  if (!(await checkDevServer())) {
    process.exit(1);
  }
  
  if (!(await setupTestEnvironment())) {
    process.exit(1);
  }
  
  // Determine which tests to run
  let suitesToRun = TEST_SUITES;
  if (specificSuite) {
    if (TEST_SUITES[specificSuite]) {
      suitesToRun = { [specificSuite]: TEST_SUITES[specificSuite] };
      log(`Running specific test suite: ${specificSuite}`, 'yellow');
    } else {
      logError(`Unknown test suite: ${specificSuite}`);
      log('Available test suites:', 'yellow');
      Object.keys(TEST_SUITES).forEach(name => log(`  - ${name}`, 'yellow'));
      process.exit(1);
    }
  }
  
  // Run tests
  const results = {};
  const startTime = Date.now();
  
  for (const [suiteName, config] of Object.entries(suitesToRun)) {
    const result = await runTestSuite(suiteName, config);
    results[suiteName] = result;
    
    // Stop on first failure if running all tests
    if (!result.success && !specificSuite) {
      logWarning('Stopping test execution due to failure');
      break;
    }
  }
  
  const endTime = Date.now();
  const duration = Math.round((endTime - startTime) / 1000);
  
  // Generate report
  const allPassed = generateTestReport(results);
  
  log(`\nTotal execution time: ${duration} seconds`, 'cyan');
  
  // Exit with appropriate code
  process.exit(allPassed ? 0 : 1);
}

// Handle uncaught errors
process.on('unhandledRejection', (error) => {
  logError(`Unhandled rejection: ${error.message}`);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  logError(`Uncaught exception: ${error.message}`);
  process.exit(1);
});

// Show help if requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  logHeader('INTEGRATION TEST RUNNER HELP');
  log('\nUsage:');
  log('  node scripts/run-integration-tests.js [suite-name] [options]');
  log('\nAvailable test suites:');
  Object.entries(TEST_SUITES).forEach(([name, config]) => {
    log(`  ${name.padEnd(20)} - ${config.description}`);
  });
  log('\nOptions:');
  log('  --headless    Run tests in headless mode');
  log('  --verbose     Enable verbose output');
  log('  --help, -h    Show this help message');
  log('\nExamples:');
  log('  node scripts/run-integration-tests.js');
  log('  node scripts/run-integration-tests.js complete-journey');
  log('  node scripts/run-integration-tests.js --headless');
  process.exit(0);
}

// Run the main function
main().catch((error) => {
  logError(`Fatal error: ${error.message}`);
  process.exit(1);
});
