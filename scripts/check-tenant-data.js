#!/usr/bin/env node

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    }),
    projectId: process.env.FIREBASE_PROJECT_ID
  });
}

const db = admin.firestore();

async function checkAndFixTenantData() {
  console.log('🔍 Checking tenant data...\n');
  
  const tenantsSnapshot = await db.collection('tenants').get();
  
  console.log(`📋 Found ${tenantsSnapshot.size} tenant(s):\n`);
  
  for (const doc of tenantsSnapshot.docs) {
    const data = doc.data();
    console.log(`🏢 Tenant ID: ${doc.id}`);
    console.log(`   Name: ${data.name}`);
    console.log(`   Type: ${data.type || 'NOT SET'}`);
    console.log(`   Status: ${data.status}`);
    console.log(`   Slug: ${data.slug || 'NOT SET'}`);
    console.log(`   Plan: ${data.plan || 'NOT SET'}`);
    console.log(`   Created: ${data.createdAt?.toDate?.()}`);
    console.log('');
    
    // Fix the tenant data if it's the development company
    if (doc.id === 'evexa-development-company') {
      console.log('🔧 Fixing evexa-development-company tenant data...');
      
      const updatedData = {
        ...data,
        type: 'development',
        slug: 'evexa-dev',
        plan: 'development',
        status: 'active',
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      };
      
      await doc.ref.update(updatedData);
      console.log('✅ Updated tenant data:');
      console.log(`   Type: development`);
      console.log(`   Slug: evexa-dev`);
      console.log(`   Plan: development`);
      console.log('');
    }
  }
  
  console.log('✅ Tenant data check completed!');
}

if (require.main === module) {
  checkAndFixTenantData().catch(console.error);
}
