#!/usr/bin/env node

/**
 * EVEXA Persona Management Script
 * Initialize, assign, and manage user personas and permissions
 */

const admin = require('firebase-admin');
const path = require('path');

// Initialize Firebase Admin
const serviceAccountPath = path.join(__dirname, '../firebase-service-account.json');

if (!admin.apps.length) {
  try {
    const serviceAccount = require(serviceAccountPath);
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      databaseURL: 'https://evexa-6600c-default-rtdb.firebaseio.com'
    });
    console.log('✅ Firebase Admin initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize Firebase Admin:', error.message);
    process.exit(1);
  }
}

const db = admin.firestore();

// Configuration
const CONFIG = {
  DEFAULT_TENANT_ID: 'evexa-super-admin-tenant',
  DRY_RUN: process.argv.includes('--dry-run'),
  VERBOSE: process.argv.includes('--verbose'),
  TENANT_ID: process.argv.includes('--tenant') 
    ? process.argv[process.argv.indexOf('--tenant') + 1] 
    : 'evexa-super-admin-tenant',
  ACTION: process.argv[2] || 'help'
};

// Default personas configuration
const DEFAULT_PERSONAS = {
  admin: {
    name: 'Administrator',
    description: 'Full system access with all administrative privileges',
    targetUsers: ['Tenant administrators', 'System administrators', 'IT managers']
  },
  'exhibition-manager': {
    name: 'Exhibition Manager',
    description: 'Comprehensive exhibition management with full access to core exhibition features',
    targetUsers: ['Exhibition managers', 'Event coordinators', 'Project managers']
  },
  'marketing-specialist': {
    name: 'Marketing Specialist',
    description: 'Focused on marketing activities, lead management, and promotional campaigns',
    targetUsers: ['Marketing specialists', 'Digital marketers', 'Content creators']
  },
  'financial-controller': {
    name: 'Financial Controller',
    description: 'Complete financial oversight with access to budgets, expenses, and reports',
    targetUsers: ['Financial controllers', 'Accountants', 'Budget managers']
  },
  'logistics-coordinator': {
    name: 'Logistics Coordinator',
    description: 'Specialized in logistics, shipping, inventory, and vendor management',
    targetUsers: ['Logistics coordinators', 'Shipping managers', 'Inventory managers']
  },
  'basic-user': {
    name: 'Basic User',
    description: 'Limited access for general users with read-only access to most modules',
    targetUsers: ['General staff', 'Temporary workers', 'Interns']
  }
};

/**
 * Main function
 */
async function main() {
  console.log('👥 EVEXA Persona Management');
  console.log('=' .repeat(60));
  console.log(`Action: ${CONFIG.ACTION}`);
  console.log(`Mode: ${CONFIG.DRY_RUN ? 'DRY RUN' : 'LIVE'}`);
  console.log(`Tenant ID: ${CONFIG.TENANT_ID}`);
  console.log('');

  switch (CONFIG.ACTION) {
    case 'list':
      await listPersonas();
      break;
    case 'assign':
      await assignPersonas();
      break;
    case 'users':
      await listUserPersonas();
      break;
    case 'stats':
      await showPersonaStats();
      break;
    case 'validate':
      await validatePersonaAssignments();
      break;
    case 'help':
    default:
      showHelp();
      break;
  }
}

/**
 * List all available personas
 */
async function listPersonas() {
  console.log('📋 Available Personas:\n');

  // List default personas
  console.log('🔹 Default Personas:');
  Object.entries(DEFAULT_PERSONAS).forEach(([id, persona]) => {
    console.log(`   ${id}`);
    console.log(`   Name: ${persona.name}`);
    console.log(`   Description: ${persona.description}`);
    console.log(`   Target Users: ${persona.targetUsers.join(', ')}`);
    console.log('');
  });

  // List custom personas
  try {
    const customPersonasSnapshot = await db.collection('tenant_personas')
      .where('tenantId', '==', CONFIG.TENANT_ID)
      .where('category', '==', 'custom')
      .get();

    if (!customPersonasSnapshot.empty) {
      console.log('🔸 Custom Personas:');
      customPersonasSnapshot.docs.forEach(doc => {
        const data = doc.data();
        console.log(`   ${doc.id}`);
        console.log(`   Name: ${data.name}`);
        console.log(`   Description: ${data.description}`);
        console.log(`   Created: ${data.createdAt?.toDate?.()?.toLocaleDateString() || 'Unknown'}`);
        console.log('');
      });
    } else {
      console.log('🔸 No custom personas found');
    }

  } catch (error) {
    console.error('❌ Error listing custom personas:', error.message);
  }
}

/**
 * Assign personas to users
 */
async function assignPersonas() {
  const userEmail = process.argv[4];
  const personaId = process.argv[5];

  if (!userEmail || !personaId) {
    console.error('❌ Usage: node manage-personas.js assign <user-email> <persona-id>');
    return;
  }

  try {
    // Find user by email
    const userSnapshot = await db.collection('user_profiles')
      .where('tenantId', '==', CONFIG.TENANT_ID)
      .where('email', '==', userEmail)
      .get();

    if (userSnapshot.empty) {
      console.error(`❌ User not found: ${userEmail}`);
      return;
    }

    const userDoc = userSnapshot.docs[0];
    const userId = userDoc.id;
    const userData = userDoc.data();

    console.log(`👤 Found user: ${userData.displayName} (${userEmail})`);

    // Verify persona exists
    if (!DEFAULT_PERSONAS[personaId]) {
      // Check custom personas
      const customPersonaDoc = await db.collection('tenant_personas').doc(personaId).get();
      if (!customPersonaDoc.exists() || customPersonaDoc.data().tenantId !== CONFIG.TENANT_ID) {
        console.error(`❌ Persona not found: ${personaId}`);
        return;
      }
    }

    if (!CONFIG.DRY_RUN) {
      // Deactivate existing assignments
      const existingAssignments = await db.collection('persona_assignments')
        .where('tenantId', '==', CONFIG.TENANT_ID)
        .where('userId', '==', userId)
        .where('isActive', '==', true)
        .get();

      const batch = db.batch();

      existingAssignments.docs.forEach(doc => {
        batch.update(doc.ref, {
          isActive: false,
          deactivatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
      });

      // Create new assignment
      const assignmentRef = db.collection('persona_assignments').doc();
      batch.set(assignmentRef, {
        userId,
        personaId,
        tenantId: CONFIG.TENANT_ID,
        assignedBy: 'system',
        assignedAt: admin.firestore.FieldValue.serverTimestamp(),
        isActive: true
      });

      await batch.commit();

      console.log(`✅ Assigned persona '${personaId}' to user '${userEmail}'`);
    } else {
      console.log(`📝 [DRY RUN] Would assign persona '${personaId}' to user '${userEmail}'`);
    }

  } catch (error) {
    console.error('❌ Error assigning persona:', error.message);
  }
}

/**
 * List users and their assigned personas
 */
async function listUserPersonas() {
  console.log('👥 User Persona Assignments:\n');

  try {
    // Get all active assignments
    const assignmentsSnapshot = await db.collection('persona_assignments')
      .where('tenantId', '==', CONFIG.TENANT_ID)
      .where('isActive', '==', true)
      .get();

    if (assignmentsSnapshot.empty) {
      console.log('📭 No persona assignments found');
      return;
    }

    // Get user details for each assignment
    const userIds = [...new Set(assignmentsSnapshot.docs.map(doc => doc.data().userId))];
    const userPromises = userIds.map(userId => 
      db.collection('user_profiles').doc(userId).get()
    );
    const userDocs = await Promise.all(userPromises);
    
    const users = {};
    userDocs.forEach(doc => {
      if (doc.exists()) {
        users[doc.id] = doc.data();
      }
    });

    // Display assignments
    assignmentsSnapshot.docs.forEach(doc => {
      const assignment = doc.data();
      const user = users[assignment.userId];
      
      if (user) {
        console.log(`👤 ${user.displayName || 'Unknown'} (${user.email || 'No email'})`);
        console.log(`   Persona: ${assignment.personaId}`);
        console.log(`   Assigned: ${assignment.assignedAt?.toDate?.()?.toLocaleDateString() || 'Unknown'}`);
        console.log(`   Assigned By: ${assignment.assignedBy || 'Unknown'}`);
        console.log('');
      }
    });

  } catch (error) {
    console.error('❌ Error listing user personas:', error.message);
  }
}

/**
 * Show persona usage statistics
 */
async function showPersonaStats() {
  console.log('📊 Persona Usage Statistics:\n');

  try {
    // Get all active assignments
    const assignmentsSnapshot = await db.collection('persona_assignments')
      .where('tenantId', '==', CONFIG.TENANT_ID)
      .where('isActive', '==', true)
      .get();

    if (assignmentsSnapshot.empty) {
      console.log('📭 No persona assignments found');
      return;
    }

    // Count assignments per persona
    const personaStats = {};
    assignmentsSnapshot.docs.forEach(doc => {
      const assignment = doc.data();
      personaStats[assignment.personaId] = (personaStats[assignment.personaId] || 0) + 1;
    });

    // Display statistics
    console.log('Persona Usage:');
    Object.entries(personaStats)
      .sort(([,a], [,b]) => b - a)
      .forEach(([personaId, count]) => {
        const personaName = DEFAULT_PERSONAS[personaId]?.name || personaId;
        console.log(`   ${personaName}: ${count} users`);
      });

    console.log(`\nTotal Users: ${assignmentsSnapshot.size}`);
    console.log(`Total Personas Used: ${Object.keys(personaStats).length}`);

  } catch (error) {
    console.error('❌ Error generating persona stats:', error.message);
  }
}

/**
 * Validate persona assignments
 */
async function validatePersonaAssignments() {
  console.log('🔍 Validating Persona Assignments:\n');

  try {
    let issues = 0;

    // Check for users without personas
    const usersSnapshot = await db.collection('user_profiles')
      .where('tenantId', '==', CONFIG.TENANT_ID)
      .get();

    const assignmentsSnapshot = await db.collection('persona_assignments')
      .where('tenantId', '==', CONFIG.TENANT_ID)
      .where('isActive', '==', true)
      .get();

    const assignedUserIds = new Set(assignmentsSnapshot.docs.map(doc => doc.data().userId));

    console.log('Users without persona assignments:');
    usersSnapshot.docs.forEach(doc => {
      if (!assignedUserIds.has(doc.id)) {
        const userData = doc.data();
        console.log(`   ⚠️  ${userData.displayName || 'Unknown'} (${userData.email || 'No email'})`);
        issues++;
      }
    });

    if (issues === 0) {
      console.log('   ✅ All users have persona assignments');
    }

    // Check for duplicate assignments
    console.log('\nChecking for duplicate assignments:');
    const userAssignments = {};
    assignmentsSnapshot.docs.forEach(doc => {
      const assignment = doc.data();
      if (!userAssignments[assignment.userId]) {
        userAssignments[assignment.userId] = [];
      }
      userAssignments[assignment.userId].push(assignment);
    });

    Object.entries(userAssignments).forEach(([userId, assignments]) => {
      if (assignments.length > 1) {
        console.log(`   ⚠️  User ${userId} has ${assignments.length} active assignments`);
        issues++;
      }
    });

    if (Object.values(userAssignments).every(assignments => assignments.length <= 1)) {
      console.log('   ✅ No duplicate assignments found');
    }

    console.log(`\nValidation complete: ${issues} issues found`);

  } catch (error) {
    console.error('❌ Error validating persona assignments:', error.message);
  }
}

/**
 * Show help information
 */
function showHelp() {
  console.log('📖 EVEXA Persona Management\n');
  console.log('Usage: node manage-personas.js <action> [options]\n');
  console.log('Actions:');
  console.log('  list                    List all available personas');
  console.log('  assign <email> <persona> Assign persona to user');
  console.log('  users                   List users and their personas');
  console.log('  stats                   Show persona usage statistics');
  console.log('  validate                Validate persona assignments');
  console.log('  help                    Show this help message\n');
  console.log('Options:');
  console.log('  --tenant <id>           Specify tenant ID (default: evexa-super-admin-tenant)');
  console.log('  --dry-run               Show what would be done without making changes');
  console.log('  --verbose               Show detailed output\n');
  console.log('Examples:');
  console.log('  node manage-personas.js list');
  console.log('  node manage-personas.<NAME_EMAIL> admin');
  console.log('  node manage-personas.js users --tenant my-tenant');
  console.log('  node manage-personas.js stats --verbose');
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
