#!/usr/bin/env node

/**
 * Deploy Firestore Indexes Only (Bypass Rules Validation)
 * Simple script to deploy only indexes without rules validation
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Check if firestore.indexes.json exists
function checkIndexFile() {
  const indexPath = path.join(process.cwd(), 'firestore.indexes.json');
  if (!fs.existsSync(indexPath)) {
    logError('firestore.indexes.json not found');
    logInfo('Run: npm run cleanup:indexes first');
    return false;
  }
  
  try {
    const content = fs.readFileSync(indexPath, 'utf8');
    const config = JSON.parse(content);
    
    if (!config.indexes || !Array.isArray(config.indexes)) {
      logError('Invalid firestore.indexes.json structure');
      return false;
    }
    
    logSuccess(`Found ${config.indexes.length} indexes in configuration`);
    return true;
  } catch (error) {
    logError(`Failed to parse firestore.indexes.json: ${error.message}`);
    return false;
  }
}

// Try multiple deployment approaches
async function deployIndexes() {
  logInfo('Attempting to deploy indexes...');
  
  // Method 1: Try with --force flag
  logInfo('Method 1: Deploying with --force flag...');
  let result = await tryDeploy(['deploy', '--only', 'firestore:indexes', '--force']);
  
  if (result.success) {
    return result;
  }
  
  // Method 2: Try without force flag but ignore rules errors
  logInfo('Method 2: Deploying without force flag...');
  result = await tryDeploy(['deploy', '--only', 'firestore:indexes'], true);
  
  if (result.success) {
    return result;
  }
  
  // Method 3: Try using firebase firestore:indexes command directly
  logInfo('Method 3: Using direct firestore:indexes command...');
  result = await tryDeploy(['firestore:indexes']);
  
  return result;
}

// Try a specific deployment command
async function tryDeploy(args, ignoreRulesError = false) {
  return new Promise((resolve) => {
    const child = spawn('firebase', args, {
      stdio: 'pipe',
      cwd: process.cwd()
    });
    
    let output = '';
    let errorOutput = '';
    
    child.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      process.stdout.write(text);
    });
    
    child.stderr.on('data', (data) => {
      const text = data.toString();
      errorOutput += text;
      process.stderr.write(text);
    });
    
    child.on('close', (code) => {
      // Check if it's just a rules permission error but indexes deployed
      if (code !== 0 && ignoreRulesError) {
        if (errorOutput.includes('firebaserules.googleapis.com') && 
            (output.includes('indexes') || output.includes('deployed'))) {
          logWarning('Rules validation failed, but indexes may have deployed successfully');
          resolve({ success: true, output, errorOutput, code, partial: true });
          return;
        }
      }
      
      if (code === 0) {
        logSuccess('Indexes deployed successfully!');
        resolve({ success: true, output, errorOutput });
      } else {
        logError(`Deployment failed with code ${code}`);
        resolve({ success: false, output, errorOutput, code });
      }
    });
    
    child.on('error', (error) => {
      logError(`Failed to run Firebase CLI: ${error.message}`);
      resolve({ success: false, error: error.message });
    });
  });
}

// Manual instructions if all methods fail
function showManualInstructions() {
  log('\n' + '='.repeat(60), 'cyan');
  log('  MANUAL DEPLOYMENT INSTRUCTIONS', 'bright');
  log('='.repeat(60), 'cyan');
  
  log('\nIf automatic deployment fails, you can deploy manually:', 'yellow');
  log('\n1. Open Firebase Console:', 'bright');
  log('   https://console.firebase.google.com/project/tradeshow-os/firestore/indexes', 'blue');
  
  log('\n2. Delete unnecessary indexes manually:', 'bright');
  log('   • Look for indexes with wrong collection names', 'yellow');
  log('   • Delete indexes for non-existent collections', 'yellow');
  log('   • Keep only the 12 essential indexes listed above', 'yellow');
  
  log('\n3. Or try these Firebase CLI commands:', 'bright');
  log('   firebase login --reauth', 'blue');
  log('   firebase use tradeshow-os', 'blue');
  log('   firebase deploy --only firestore:indexes --force', 'blue');
  
  log('\n4. Essential indexes to keep:', 'bright');
  log('   • user_profiles: 1 index', 'green');
  log('   • exhibitions: 2 indexes', 'green');
  log('   • exhibition_events: 2 indexes', 'green');
  log('   • exhibition_tasks: 3 indexes', 'green');
  log('   • lead_contacts: 2 indexes', 'green');
  log('   • financials: 2 indexes', 'green');
  log('   Total: 12 indexes', 'green');
}

// Main function
async function main() {
  log('\n' + '='.repeat(60), 'cyan');
  log('  DEPLOY FIRESTORE INDEXES ONLY', 'bright');
  log('='.repeat(60), 'cyan');
  
  // Check if index file exists
  if (!checkIndexFile()) {
    process.exit(1);
  }
  
  // Try to deploy
  const result = await deployIndexes();
  
  if (result.success) {
    logSuccess('🎉 Index deployment completed!');
    if (result.partial) {
      logWarning('Rules validation failed, but indexes should be deployed');
      logInfo('Check Firebase Console to confirm index deployment');
    }
    logInfo('Your Firebase now has only the essential 12 indexes');
  } else {
    logError('All deployment methods failed');
    showManualInstructions();
    process.exit(1);
  }
}

// Show help
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  log('\n' + '='.repeat(60), 'cyan');
  log('  DEPLOY INDEXES ONLY HELP', 'bright');
  log('='.repeat(60), 'cyan');
  log('\nThis script deploys only Firestore indexes, bypassing rules validation.');
  log('\nUsage:');
  log('  node scripts/deploy-indexes-only.js');
  log('\nPrerequisites:');
  log('  • Run "npm run cleanup:indexes" first');
  log('  • Ensure you have Firebase CLI access');
  log('\nWhat it does:');
  log('  • Tries multiple deployment methods');
  log('  • Ignores rules validation errors');
  log('  • Provides manual instructions if needed');
  process.exit(0);
}

// Run the main function
main().catch((error) => {
  logError(`Fatal error: ${error.message}`);
  process.exit(1);
});
