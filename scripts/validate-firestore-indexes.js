#!/usr/bin/env node

/**
 * Firestore Index Validation and Deployment Script
 * Validates index configuration against actual queries and deploys indexes safely
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Expected collection names based on COLLECTIONS constant
const EXPECTED_COLLECTIONS = {
  // Core collections that need indexes
  'user_profiles': {
    queries: [
      { fields: ['tenantId', 'status', 'createdAt'], description: 'Get active users by tenant' }
    ]
  },
  'exhibitions': {
    queries: [
      { fields: ['tenantId', 'status', 'startDate'], description: 'Get active exhibitions by start date' },
      { fields: ['tenantId', 'startDate'], description: 'Get upcoming exhibitions' }
    ]
  },
  'exhibition_events': {
    queries: [
      { fields: ['tenantId', 'status', 'startDate'], description: 'Get active events by date' },
      { fields: ['tenantId', 'exhibitionId', 'startDate'], description: 'Get events for exhibition' }
    ]
  },
  'exhibition_tasks': {
    queries: [
      { fields: ['tenantId', 'status', 'dueDate'], description: 'Get tasks by due date' },
      { fields: ['tenantId', 'exhibitionId', 'createdAt'], description: 'Get tasks for exhibition' },
      { fields: ['tenantId', 'assignedTo', 'dueDate'], description: 'Get tasks assigned to user' }
    ]
  },
  'lead_contacts': {
    queries: [
      { fields: ['tenantId', 'status', 'createdAt'], description: 'Get leads by status' },
      { fields: ['tenantId', 'exhibitionId', 'createdAt'], description: 'Get leads for exhibition' }
    ]
  },
  'financials': {
    queries: [
      { fields: ['tenantId', 'type', 'createdAt'], description: 'Get financial records by type' },
      { fields: ['tenantId', 'exhibitionId', 'type'], description: 'Get financial records for exhibition' }
    ]
  }
};

// Load and validate firestore.indexes.json
function loadIndexConfig() {
  const indexPath = path.join(process.cwd(), 'firestore.indexes.json');
  
  if (!fs.existsSync(indexPath)) {
    logError('firestore.indexes.json not found');
    return null;
  }

  try {
    const content = fs.readFileSync(indexPath, 'utf8');
    const config = JSON.parse(content);
    
    if (!config.indexes || !Array.isArray(config.indexes)) {
      logError('Invalid firestore.indexes.json structure');
      return null;
    }

    return config;
  } catch (error) {
    logError(`Failed to parse firestore.indexes.json: ${error.message}`);
    return null;
  }
}

// Validate index configuration
function validateIndexes(config) {
  logHeader('VALIDATING FIRESTORE INDEXES');
  
  const issues = [];
  const indexesByCollection = {};
  
  // Group indexes by collection
  config.indexes.forEach((index, i) => {
    const collection = index.collectionGroup;
    if (!indexesByCollection[collection]) {
      indexesByCollection[collection] = [];
    }
    indexesByCollection[collection].push({ ...index, originalIndex: i });
  });

  // Check for missing indexes
  Object.keys(EXPECTED_COLLECTIONS).forEach(collection => {
    if (!indexesByCollection[collection]) {
      issues.push({
        type: 'missing_collection',
        collection,
        message: `No indexes found for collection: ${collection}`
      });
      return;
    }

    const expectedQueries = EXPECTED_COLLECTIONS[collection].queries;
    const actualIndexes = indexesByCollection[collection];

    expectedQueries.forEach(expectedQuery => {
      const matchingIndex = actualIndexes.find(index => {
        if (index.fields.length !== expectedQuery.fields.length) return false;
        
        return expectedQuery.fields.every((field, i) => {
          const indexField = index.fields[i];
          return indexField.fieldPath === field;
        });
      });

      if (!matchingIndex) {
        issues.push({
          type: 'missing_index',
          collection,
          query: expectedQuery,
          message: `Missing index for query: ${expectedQuery.description}`
        });
      }
    });
  });

  // Check for unexpected collections
  Object.keys(indexesByCollection).forEach(collection => {
    if (!EXPECTED_COLLECTIONS[collection]) {
      issues.push({
        type: 'unexpected_collection',
        collection,
        message: `Unexpected collection in indexes: ${collection}`
      });
    }
  });

  // Check for duplicate indexes
  Object.keys(indexesByCollection).forEach(collection => {
    const indexes = indexesByCollection[collection];
    const seen = new Set();
    
    indexes.forEach(index => {
      const signature = index.fields.map(f => `${f.fieldPath}:${f.order}`).join(',');
      if (seen.has(signature)) {
        issues.push({
          type: 'duplicate_index',
          collection,
          signature,
          message: `Duplicate index found for collection: ${collection}`
        });
      }
      seen.add(signature);
    });
  });

  return issues;
}

// Display validation results
function displayValidationResults(issues) {
  if (issues.length === 0) {
    logSuccess('All indexes are valid!');
    return true;
  }

  logWarning(`Found ${issues.length} issue(s):`);
  
  const groupedIssues = {};
  issues.forEach(issue => {
    if (!groupedIssues[issue.type]) {
      groupedIssues[issue.type] = [];
    }
    groupedIssues[issue.type].push(issue);
  });

  Object.keys(groupedIssues).forEach(type => {
    log(`\n${type.toUpperCase().replace('_', ' ')}:`, 'yellow');
    groupedIssues[type].forEach(issue => {
      log(`  • ${issue.message}`, 'red');
      if (issue.query) {
        log(`    Fields: [${issue.query.fields.join(', ')}]`, 'blue');
      }
    });
  });

  return false;
}

// Deploy indexes to Firebase
async function deployIndexes() {
  return new Promise((resolve) => {
    logInfo('Deploying indexes to Firebase...');
    
    const child = spawn('firebase', ['deploy', '--only', 'firestore:indexes'], {
      stdio: 'pipe',
      cwd: process.cwd()
    });
    
    let output = '';
    let errorOutput = '';
    
    child.stdout.on('data', (data) => {
      output += data.toString();
      process.stdout.write(data);
    });
    
    child.stderr.on('data', (data) => {
      errorOutput += data.toString();
      process.stderr.write(data);
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        logSuccess('Indexes deployed successfully!');
        resolve({ success: true, output, errorOutput });
      } else {
        logError(`Index deployment failed with code ${code}`);
        resolve({ success: false, output, errorOutput, code });
      }
    });
    
    child.on('error', (error) => {
      logError(`Failed to run Firebase CLI: ${error.message}`);
      resolve({ success: false, error: error.message });
    });
  });
}

// Generate index summary
function generateIndexSummary(config) {
  logHeader('INDEX SUMMARY');
  
  const collectionCounts = {};
  let totalIndexes = 0;
  
  config.indexes.forEach(index => {
    const collection = index.collectionGroup;
    collectionCounts[collection] = (collectionCounts[collection] || 0) + 1;
    totalIndexes++;
  });
  
  log(`Total indexes: ${totalIndexes}`, 'cyan');
  log(`Collections with indexes: ${Object.keys(collectionCounts).length}`, 'cyan');
  
  log('\nIndexes by collection:', 'bright');
  Object.keys(collectionCounts).sort().forEach(collection => {
    const count = collectionCounts[collection];
    const isExpected = EXPECTED_COLLECTIONS[collection];
    const status = isExpected ? '✅' : '⚠️';
    log(`  ${status} ${collection}: ${count} index(es)`, isExpected ? 'green' : 'yellow');
  });
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const shouldDeploy = args.includes('--deploy');
  const forceValidation = args.includes('--force');
  
  logHeader('FIRESTORE INDEX VALIDATOR');
  
  // Load configuration
  const config = loadIndexConfig();
  if (!config) {
    process.exit(1);
  }
  
  // Generate summary
  generateIndexSummary(config);
  
  // Validate indexes
  const issues = validateIndexes(config);
  const isValid = displayValidationResults(issues);
  
  if (!isValid && !forceValidation) {
    logError('Index validation failed. Fix issues before deploying.');
    logInfo('Use --force to deploy anyway (not recommended)');
    process.exit(1);
  }
  
  // Deploy if requested
  if (shouldDeploy) {
    const result = await deployIndexes();
    if (!result.success) {
      process.exit(1);
    }
  } else {
    logInfo('Use --deploy flag to deploy indexes to Firebase');
  }
  
  logSuccess('Index validation completed successfully!');
}

// Show help
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  logHeader('FIRESTORE INDEX VALIDATOR HELP');
  log('\nUsage:');
  log('  node scripts/validate-firestore-indexes.js [options]');
  log('\nOptions:');
  log('  --deploy      Deploy indexes to Firebase after validation');
  log('  --force       Deploy even if validation fails (not recommended)');
  log('  --help, -h    Show this help message');
  log('\nExamples:');
  log('  node scripts/validate-firestore-indexes.js');
  log('  node scripts/validate-firestore-indexes.js --deploy');
  log('  node scripts/validate-firestore-indexes.js --deploy --force');
  process.exit(0);
}

// Run the main function
main().catch((error) => {
  logError(`Fatal error: ${error.message}`);
  process.exit(1);
});
