#!/usr/bin/env node

/**
 * EVEXA Data Duplication Management Script
 * Initialize, validate, and manage strategic data duplication across collections
 */

const admin = require('firebase-admin');
const path = require('path');

// Initialize Firebase Admin
const serviceAccountPath = path.join(__dirname, '../firebase-service-account.json');

if (!admin.apps.length) {
  try {
    const serviceAccount = require(serviceAccountPath);
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      databaseURL: 'https://evexa-6600c-default-rtdb.firebaseio.com'
    });
    console.log('✅ Firebase Admin initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize Firebase Admin:', error.message);
    process.exit(1);
  }
}

const db = admin.firestore();

// Configuration
const CONFIG = {
  DEFAULT_TENANT_ID: 'evexa-super-admin-tenant',
  BATCH_SIZE: 100,
  DRY_RUN: process.argv.includes('--dry-run'),
  VERBOSE: process.argv.includes('--verbose'),
  RULE: process.argv.includes('--rule') 
    ? process.argv[process.argv.indexOf('--rule') + 1] 
    : null,
  ACTION: process.argv[2] || 'help'
};

// Duplication rules configuration
const DUPLICATION_RULES = {
  exhibitionName: {
    sourceCollection: 'exhibitions',
    sourceField: 'name',
    targetCollections: [
      { collection: 'exhibition_events', targetField: 'exhibitionName', relationshipField: 'exhibitionId' },
      { collection: 'exhibition_tasks', targetField: 'exhibitionName', relationshipField: 'exhibitionId' },
      { collection: 'lead_contacts', targetField: 'exhibitionName', relationshipField: 'exhibitionId' },
      { collection: 'budget_allocations', targetField: 'exhibitionName', relationshipField: 'exhibitionId' },
      { collection: 'expense_records', targetField: 'exhibitionName', relationshipField: 'exhibitionId' }
    ],
    description: 'Duplicate exhibition name for faster list views and filtering'
  },

  exhibitionDates: {
    sourceCollection: 'exhibitions',
    sourceFields: ['startDate', 'endDate'],
    targetCollections: [
      { 
        collection: 'exhibition_events', 
        targetFields: ['exhibitionStartDate', 'exhibitionEndDate'], 
        relationshipField: 'exhibitionId' 
      },
      { 
        collection: 'exhibition_tasks', 
        targetFields: ['exhibitionStartDate', 'exhibitionEndDate'], 
        relationshipField: 'exhibitionId' 
      }
    ],
    description: 'Duplicate exhibition dates for timeline views and deadline calculations'
  },

  exhibitionLocation: {
    sourceCollection: 'exhibitions',
    sourceFields: ['venue', 'city', 'country'],
    targetCollections: [
      { 
        collection: 'exhibition_events', 
        targetFields: ['exhibitionVenue', 'exhibitionCity', 'exhibitionCountry'], 
        relationshipField: 'exhibitionId' 
      },
      { 
        collection: 'lead_contacts', 
        targetFields: ['exhibitionVenue', 'exhibitionCity', 'exhibitionCountry'], 
        relationshipField: 'exhibitionId' 
      }
    ],
    description: 'Duplicate exhibition location for geographical filtering and reporting'
  },

  userDisplayName: {
    sourceCollection: 'user_profiles',
    sourceField: 'displayName',
    targetCollections: [
      { collection: 'exhibition_tasks', targetField: 'assignedToName', relationshipField: 'assignedTo' },
      { collection: 'exhibition_events', targetField: 'organizerName', relationshipField: 'organizer' },
      { collection: 'expense_records', targetField: 'submittedByName', relationshipField: 'submittedBy' },
      { collection: 'lead_contacts', targetField: 'assignedToName', relationshipField: 'assignedTo' }
    ],
    description: 'Duplicate user display names for better UI without additional lookups'
  },

  vendorName: {
    sourceCollection: 'vendor_profiles',
    sourceField: 'name',
    targetCollections: [
      { collection: 'expense_records', targetField: 'vendorName', relationshipField: 'vendorId' },
      { collection: 'purchase_orders', targetField: 'vendorName', relationshipField: 'vendorId' }
    ],
    description: 'Duplicate vendor names for financial reporting without joins'
  }
};

/**
 * Main function
 */
async function main() {
  console.log('🔄 EVEXA Data Duplication Management');
  console.log('=' .repeat(60));
  console.log(`Action: ${CONFIG.ACTION}`);
  console.log(`Mode: ${CONFIG.DRY_RUN ? 'DRY RUN' : 'LIVE'}`);
  console.log(`Tenant ID: ${CONFIG.DEFAULT_TENANT_ID}`);
  if (CONFIG.RULE) console.log(`Rule: ${CONFIG.RULE}`);
  console.log('');

  switch (CONFIG.ACTION) {
    case 'initialize':
      await initializeDuplication();
      break;
    case 'validate':
      await validateDuplication();
      break;
    case 'sync':
      await syncDuplication();
      break;
    case 'list':
      listRules();
      break;
    case 'help':
    default:
      showHelp();
      break;
  }
}

/**
 * Initialize duplicated data for all or specific rules
 */
async function initializeDuplication() {
  console.log('🚀 Initializing data duplication...\n');

  const rulesToProcess = CONFIG.RULE 
    ? [CONFIG.RULE] 
    : Object.keys(DUPLICATION_RULES);

  let totalUpdated = 0;
  let totalErrors = 0;

  for (const ruleKey of rulesToProcess) {
    const rule = DUPLICATION_RULES[ruleKey];
    if (!rule) {
      console.error(`❌ Rule '${ruleKey}' not found`);
      continue;
    }

    console.log(`📋 Processing rule: ${ruleKey}`);
    console.log(`   ${rule.description}`);

    try {
      const result = await initializeRule(rule, ruleKey);
      totalUpdated += result.updated;
      totalErrors += result.errors;

      console.log(`   ✅ Completed: ${result.updated} documents updated, ${result.errors} errors`);

    } catch (error) {
      console.error(`   ❌ Error processing rule ${ruleKey}:`, error.message);
      totalErrors++;
    }

    console.log('');
  }

  console.log('='.repeat(60));
  console.log(`Initialization complete: ${totalUpdated} documents updated, ${totalErrors} errors`);
}

/**
 * Initialize a specific duplication rule
 */
async function initializeRule(rule, ruleKey) {
  const result = { updated: 0, errors: 0 };

  try {
    // Get all source documents
    const sourceSnapshot = await db.collection(rule.sourceCollection)
      .where('tenantId', '==', CONFIG.DEFAULT_TENANT_ID)
      .get();

    if (sourceSnapshot.empty) {
      console.log(`   📭 No source documents found in ${rule.sourceCollection}`);
      return result;
    }

    console.log(`   📊 Found ${sourceSnapshot.size} source documents`);

    // Process each source document
    for (const sourceDoc of sourceSnapshot.docs) {
      const sourceData = sourceDoc.data();
      
      try {
        const updateCount = await syncSourceDocument(rule, sourceDoc.id, sourceData);
        result.updated += updateCount;
      } catch (error) {
        console.error(`   ⚠️  Error syncing document ${sourceDoc.id}:`, error.message);
        result.errors++;
      }
    }

  } catch (error) {
    console.error(`   ❌ Error initializing rule ${ruleKey}:`, error.message);
    result.errors++;
  }

  return result;
}

/**
 * Sync duplicated data for a source document
 */
async function syncSourceDocument(rule, sourceDocId, sourceData) {
  let totalUpdated = 0;

  for (const target of rule.targetCollections) {
    try {
      // Find target documents
      const targetSnapshot = await db.collection(target.collection)
        .where('tenantId', '==', CONFIG.DEFAULT_TENANT_ID)
        .where(target.relationshipField, '==', sourceDocId)
        .get();

      if (targetSnapshot.empty) continue;

      // Prepare update data
      const updateData = buildUpdateData(rule, target, sourceData);
      updateData.updatedAt = admin.firestore.FieldValue.serverTimestamp();
      updateData.lastDuplicationSync = admin.firestore.FieldValue.serverTimestamp();

      if (!CONFIG.DRY_RUN) {
        // Update documents in batches
        const batch = db.batch();
        let batchCount = 0;

        for (const targetDoc of targetSnapshot.docs) {
          batch.update(targetDoc.ref, updateData);
          batchCount++;
          totalUpdated++;

          if (batchCount >= 500) {
            await batch.commit();
            batchCount = 0;
          }
        }

        if (batchCount > 0) {
          await batch.commit();
        }
      } else {
        totalUpdated += targetSnapshot.size;
      }

      if (CONFIG.VERBOSE) {
        console.log(`     📝 ${CONFIG.DRY_RUN ? '[DRY RUN] ' : ''}Updated ${targetSnapshot.size} documents in ${target.collection}`);
      }

    } catch (error) {
      console.error(`     ❌ Error updating ${target.collection}:`, error.message);
    }
  }

  return totalUpdated;
}

/**
 * Build update data from source to target
 */
function buildUpdateData(rule, target, sourceData) {
  const updateData = {};

  if (rule.sourceField && target.targetField) {
    // Single field mapping
    updateData[target.targetField] = sourceData[rule.sourceField];
  } else if (rule.sourceFields && target.targetFields) {
    // Multiple field mapping
    for (let i = 0; i < rule.sourceFields.length && i < target.targetFields.length; i++) {
      const sourceField = rule.sourceFields[i];
      const targetField = target.targetFields[i];
      updateData[targetField] = sourceData[sourceField];
    }
  }

  return updateData;
}

/**
 * Validate duplicated data consistency
 */
async function validateDuplication() {
  console.log('🔍 Validating data duplication consistency...\n');

  const rulesToValidate = CONFIG.RULE 
    ? [CONFIG.RULE] 
    : Object.keys(DUPLICATION_RULES);

  let totalInconsistencies = 0;

  for (const ruleKey of rulesToValidate) {
    const rule = DUPLICATION_RULES[ruleKey];
    if (!rule) {
      console.error(`❌ Rule '${ruleKey}' not found`);
      continue;
    }

    console.log(`🔍 Validating rule: ${ruleKey}`);

    try {
      const inconsistencies = await validateRule(rule);
      totalInconsistencies += inconsistencies.length;

      if (inconsistencies.length === 0) {
        console.log(`   ✅ All data is consistent`);
      } else {
        console.log(`   ⚠️  Found ${inconsistencies.length} inconsistencies`);
        
        if (CONFIG.VERBOSE) {
          inconsistencies.slice(0, 5).forEach(inc => {
            console.log(`     - ${inc.targetCollection}/${inc.targetId}: ${inc.field} mismatch`);
          });
          if (inconsistencies.length > 5) {
            console.log(`     ... and ${inconsistencies.length - 5} more`);
          }
        }
      }

    } catch (error) {
      console.error(`   ❌ Error validating rule ${ruleKey}:`, error.message);
    }

    console.log('');
  }

  console.log('='.repeat(60));
  console.log(`Validation complete: ${totalInconsistencies} total inconsistencies found`);
  
  if (totalInconsistencies > 0) {
    console.log('\n💡 Run with --action sync to fix inconsistencies');
  }
}

/**
 * Validate a specific rule
 */
async function validateRule(rule) {
  const inconsistencies = [];

  try {
    // Get all source documents
    const sourceSnapshot = await db.collection(rule.sourceCollection)
      .where('tenantId', '==', CONFIG.DEFAULT_TENANT_ID)
      .get();

    for (const sourceDoc of sourceSnapshot.docs) {
      const sourceData = sourceDoc.data();

      // Check each target collection
      for (const target of rule.targetCollections) {
        const targetSnapshot = await db.collection(target.collection)
          .where('tenantId', '==', CONFIG.DEFAULT_TENANT_ID)
          .where(target.relationshipField, '==', sourceDoc.id)
          .get();

        for (const targetDoc of targetSnapshot.docs) {
          const targetData = targetDoc.data();

          // Compare field values
          if (rule.sourceField && target.targetField) {
            if (sourceData[rule.sourceField] !== targetData[target.targetField]) {
              inconsistencies.push({
                sourceId: sourceDoc.id,
                targetCollection: target.collection,
                targetId: targetDoc.id,
                field: target.targetField,
                sourceValue: sourceData[rule.sourceField],
                targetValue: targetData[target.targetField]
              });
            }
          } else if (rule.sourceFields && target.targetFields) {
            for (let i = 0; i < rule.sourceFields.length && i < target.targetFields.length; i++) {
              const sourceField = rule.sourceFields[i];
              const targetField = target.targetFields[i];
              
              if (sourceData[sourceField] !== targetData[targetField]) {
                inconsistencies.push({
                  sourceId: sourceDoc.id,
                  targetCollection: target.collection,
                  targetId: targetDoc.id,
                  field: targetField,
                  sourceValue: sourceData[sourceField],
                  targetValue: targetData[targetField]
                });
              }
            }
          }
        }
      }
    }

  } catch (error) {
    console.error(`Error validating rule:`, error.message);
  }

  return inconsistencies;
}

/**
 * Sync duplicated data (same as initialize but with different messaging)
 */
async function syncDuplication() {
  console.log('🔄 Syncing duplicated data...\n');
  await initializeDuplication();
}

/**
 * List all available duplication rules
 */
function listRules() {
  console.log('📋 Available Duplication Rules:\n');

  Object.entries(DUPLICATION_RULES).forEach(([key, rule]) => {
    console.log(`🔹 ${key}`);
    console.log(`   Source: ${rule.sourceCollection}.${rule.sourceField || rule.sourceFields?.join(', ')}`);
    console.log(`   Targets: ${rule.targetCollections.length} collections`);
    console.log(`   Description: ${rule.description}`);
    console.log('');
  });
}

/**
 * Show help information
 */
function showHelp() {
  console.log('📖 EVEXA Data Duplication Management\n');
  console.log('Usage: node manage-data-duplication.js <action> [options]\n');
  console.log('Actions:');
  console.log('  initialize  Initialize duplicated data for all or specific rules');
  console.log('  validate    Check consistency of duplicated data');
  console.log('  sync        Sync duplicated data (same as initialize)');
  console.log('  list        List all available duplication rules');
  console.log('  help        Show this help message\n');
  console.log('Options:');
  console.log('  --rule <name>   Process only the specified rule');
  console.log('  --dry-run       Show what would be done without making changes');
  console.log('  --verbose       Show detailed output\n');
  console.log('Examples:');
  console.log('  node manage-data-duplication.js initialize');
  console.log('  node manage-data-duplication.js validate --rule exhibitionName');
  console.log('  node manage-data-duplication.js sync --dry-run --verbose');
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
