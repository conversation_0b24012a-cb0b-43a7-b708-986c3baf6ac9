#!/usr/bin/env node

/**
 * EVEXA Data Migration Script
 * Migrates existing nested collection data to flat root-level collections with proper tenantId stamping
 */

const admin = require('firebase-admin');
const path = require('path');

// Initialize Firebase Admin
const serviceAccountPath = path.join(__dirname, '../firebase-service-account.json');

if (!admin.apps.length) {
  try {
    const serviceAccount = require(serviceAccountPath);
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      databaseURL: 'https://evexa-6600c-default-rtdb.firebaseio.com'
    });
    console.log('✅ Firebase Admin initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize Firebase Admin:', error.message);
    process.exit(1);
  }
}

const db = admin.firestore();

// Migration configuration
const MIGRATION_CONFIG = {
  DEFAULT_TENANT_ID: 'evexa-super-admin-tenant',
  BATCH_SIZE: 100,
  DRY_RUN: process.argv.includes('--dry-run'),
  VERBOSE: process.argv.includes('--verbose'),
  FORCE: process.argv.includes('--force')
};

// Collections to migrate from root level to tenant-scoped
const ROOT_COLLECTIONS_TO_MIGRATE = [
  {
    source: 'users',
    target: 'user_profiles',
    description: 'User profiles with authentication data',
    requiredFields: ['email']
  },
  {
    source: 'exhibitions',
    target: 'exhibitions',
    description: 'Exhibition management data',
    requiredFields: ['name', 'startDate', 'endDate']
  },
  {
    source: 'events',
    target: 'exhibition_events',
    description: 'Exhibition events and activities',
    requiredFields: ['name', 'exhibitionId']
  },
  {
    source: 'tasks',
    target: 'exhibition_tasks',
    description: 'Task management data',
    requiredFields: ['title']
  },
  {
    source: 'leads',
    target: 'lead_contacts',
    description: 'Lead and contact management',
    requiredFields: ['email']
  },
  {
    source: 'budgets',
    target: 'budget_allocations',
    description: 'Budget allocation data',
    requiredFields: ['amount', 'activityId']
  },
  {
    source: 'expenses',
    target: 'expense_records',
    description: 'Expense tracking data',
    requiredFields: ['amount', 'activityId']
  },
  {
    source: 'vendors',
    target: 'vendor_profiles',
    description: 'Vendor management data',
    requiredFields: ['name']
  }
];

// Nested collections to migrate to flat structure
const NESTED_COLLECTIONS_TO_MIGRATE = [
  {
    sourcePattern: 'exhibitions/{exhibitionId}/tasks',
    target: 'exhibition_tasks',
    parentField: 'exhibitionId',
    description: 'Tasks nested under exhibitions'
  },
  {
    sourcePattern: 'exhibitions/{exhibitionId}/events',
    target: 'exhibition_events',
    parentField: 'exhibitionId',
    description: 'Events nested under exhibitions'
  },
  {
    sourcePattern: 'exhibitions/{exhibitionId}/leads',
    target: 'lead_contacts',
    parentField: 'exhibitionId',
    description: 'Leads nested under exhibitions'
  },
  {
    sourcePattern: 'exhibitions/{exhibitionId}/budgets',
    target: 'budget_allocations',
    parentField: 'exhibitionId',
    description: 'Budgets nested under exhibitions'
  },
  {
    sourcePattern: 'exhibitions/{exhibitionId}/expenses',
    target: 'expense_records',
    parentField: 'exhibitionId',
    description: 'Expenses nested under exhibitions'
  }
];

/**
 * Main migration function
 */
async function runMigration() {
  console.log('🚀 EVEXA Data Migration to Flat Collections');
  console.log('=' .repeat(60));
  console.log(`Mode: ${MIGRATION_CONFIG.DRY_RUN ? 'DRY RUN' : 'LIVE MIGRATION'}`);
  console.log(`Tenant ID: ${MIGRATION_CONFIG.DEFAULT_TENANT_ID}`);
  console.log(`Batch Size: ${MIGRATION_CONFIG.BATCH_SIZE}`);
  console.log('');

  const startTime = new Date();
  const results = {
    rootCollections: [],
    nestedCollections: [],
    totalProcessed: 0,
    totalSkipped: 0,
    totalErrors: 0
  };

  try {
    // Phase 1: Migrate root-level collections
    console.log('📋 Phase 1: Migrating root-level collections...\n');
    
    for (const config of ROOT_COLLECTIONS_TO_MIGRATE) {
      const result = await migrateRootCollection(config);
      results.rootCollections.push(result);
      results.totalProcessed += result.processed;
      results.totalSkipped += result.skipped;
      results.totalErrors += result.errors.length;
    }

    // Phase 2: Migrate nested collections
    console.log('\n📋 Phase 2: Migrating nested collections...\n');
    
    for (const config of NESTED_COLLECTIONS_TO_MIGRATE) {
      const result = await migrateNestedCollection(config);
      results.nestedCollections.push(result);
      results.totalProcessed += result.processed;
      results.totalSkipped += result.skipped;
      results.totalErrors += result.errors.length;
    }

    // Generate report
    const endTime = new Date();
    const duration = (endTime.getTime() - startTime.getTime()) / 1000;
    
    console.log('\n' + '='.repeat(60));
    console.log('MIGRATION COMPLETE');
    console.log('='.repeat(60));
    console.log(`Duration: ${duration.toFixed(2)} seconds`);
    console.log(`Total Documents Processed: ${results.totalProcessed}`);
    console.log(`Total Documents Skipped: ${results.totalSkipped}`);
    console.log(`Total Errors: ${results.totalErrors}`);
    console.log(`Success Rate: ${((results.totalProcessed / (results.totalProcessed + results.totalSkipped)) * 100).toFixed(2)}%`);

    if (results.totalErrors > 0) {
      console.log('\n❌ ERRORS ENCOUNTERED:');
      [...results.rootCollections, ...results.nestedCollections].forEach(result => {
        if (result.errors.length > 0) {
          console.log(`\n${result.source}:`);
          result.errors.forEach(error => console.log(`  - ${error}`));
        }
      });
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

/**
 * Migrate a root-level collection
 */
async function migrateRootCollection(config) {
  console.log(`📦 Migrating ${config.source} → ${config.target}`);
  console.log(`   ${config.description}`);

  const result = {
    source: config.source,
    target: config.target,
    processed: 0,
    skipped: 0,
    errors: []
  };

  try {
    // Get source documents
    const sourceSnapshot = await db.collection(config.source).get();
    
    if (sourceSnapshot.empty) {
      console.log(`   ✅ No documents found in ${config.source}`);
      return result;
    }

    console.log(`   📊 Found ${sourceSnapshot.size} documents to migrate`);

    // Process in batches
    const batches = [];
    let currentBatch = [];

    for (const doc of sourceSnapshot.docs) {
      try {
        const data = doc.data();
        
        // Validate required fields
        const missingFields = config.requiredFields.filter(field => !data[field]);
        if (missingFields.length > 0) {
          result.errors.push(`Document ${doc.id} missing required fields: ${missingFields.join(', ')}`);
          result.skipped++;
          continue;
        }

        // Add tenant ID and timestamps
        const migratedData = {
          ...data,
          tenantId: MIGRATION_CONFIG.DEFAULT_TENANT_ID,
          createdAt: data.createdAt || admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          migratedAt: admin.firestore.FieldValue.serverTimestamp(),
          migrationVersion: '1.0'
        };

        currentBatch.push({
          id: doc.id,
          data: migratedData
        });

        if (currentBatch.length >= MIGRATION_CONFIG.BATCH_SIZE) {
          batches.push([...currentBatch]);
          currentBatch = [];
        }

      } catch (error) {
        result.errors.push(`Error processing document ${doc.id}: ${error.message}`);
        result.skipped++;
      }
    }

    // Add remaining documents
    if (currentBatch.length > 0) {
      batches.push(currentBatch);
    }

    // Execute batches
    if (!MIGRATION_CONFIG.DRY_RUN) {
      for (let i = 0; i < batches.length; i++) {
        const batch = db.batch();
        
        for (const item of batches[i]) {
          const docRef = db.collection(config.target).doc(item.id);
          batch.set(docRef, item.data);
        }
        
        await batch.commit();
        result.processed += batches[i].length;
        
        if (MIGRATION_CONFIG.VERBOSE) {
          console.log(`   ✅ Batch ${i + 1}/${batches.length} completed (${batches[i].length} documents)`);
        }
      }
    } else {
      result.processed = batches.reduce((sum, batch) => sum + batch.length, 0);
      console.log(`   📝 [DRY RUN] Would migrate ${result.processed} documents`);
    }

    console.log(`   ✅ Completed: ${result.processed} processed, ${result.skipped} skipped`);

  } catch (error) {
    result.errors.push(`Collection migration failed: ${error.message}`);
    console.error(`   ❌ Error migrating ${config.source}:`, error.message);
  }

  return result;
}

/**
 * Migrate nested collections
 */
async function migrateNestedCollection(config) {
  console.log(`📦 Migrating nested ${config.sourcePattern} → ${config.target}`);
  console.log(`   ${config.description}`);

  const result = {
    source: config.sourcePattern,
    target: config.target,
    processed: 0,
    skipped: 0,
    errors: []
  };

  try {
    // Extract collection name from pattern
    const collectionName = config.sourcePattern.split('/').pop();
    
    // Use collection group query
    const nestedSnapshot = await db.collectionGroup(collectionName).get();
    
    if (nestedSnapshot.empty) {
      console.log(`   ✅ No nested documents found for ${collectionName}`);
      return result;
    }

    console.log(`   📊 Found ${nestedSnapshot.size} nested documents to migrate`);

    // Process documents
    const batches = [];
    let currentBatch = [];

    for (const doc of nestedSnapshot.docs) {
      try {
        const data = doc.data();
        
        // Extract parent ID from document path
        const pathParts = doc.ref.path.split('/');
        const parentId = pathParts[pathParts.length - 3];

        // Add tenant ID, parent reference, and timestamps
        const migratedData = {
          ...data,
          tenantId: MIGRATION_CONFIG.DEFAULT_TENANT_ID,
          [config.parentField]: parentId,
          createdAt: data.createdAt || admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          migratedAt: admin.firestore.FieldValue.serverTimestamp(),
          migrationVersion: '1.0'
        };

        currentBatch.push({
          id: doc.id,
          data: migratedData
        });

        if (currentBatch.length >= MIGRATION_CONFIG.BATCH_SIZE) {
          batches.push([...currentBatch]);
          currentBatch = [];
        }

      } catch (error) {
        result.errors.push(`Error processing nested document ${doc.id}: ${error.message}`);
        result.skipped++;
      }
    }

    // Add remaining documents
    if (currentBatch.length > 0) {
      batches.push(currentBatch);
    }

    // Execute batches
    if (!MIGRATION_CONFIG.DRY_RUN) {
      for (let i = 0; i < batches.length; i++) {
        const batch = db.batch();
        
        for (const item of batches[i]) {
          const docRef = db.collection(config.target).doc(item.id);
          batch.set(docRef, item.data);
        }
        
        await batch.commit();
        result.processed += batches[i].length;
        
        if (MIGRATION_CONFIG.VERBOSE) {
          console.log(`   ✅ Batch ${i + 1}/${batches.length} completed (${batches[i].length} documents)`);
        }
      }
    } else {
      result.processed = batches.reduce((sum, batch) => sum + batch.length, 0);
      console.log(`   📝 [DRY RUN] Would migrate ${result.processed} documents`);
    }

    console.log(`   ✅ Completed: ${result.processed} processed, ${result.skipped} skipped`);

  } catch (error) {
    result.errors.push(`Nested collection migration failed: ${error.message}`);
    console.error(`   ❌ Error migrating ${config.sourcePattern}:`, error.message);
  }

  return result;
}

// Run migration if called directly
if (require.main === module) {
  runMigration().catch(console.error);
}

module.exports = { runMigration };
