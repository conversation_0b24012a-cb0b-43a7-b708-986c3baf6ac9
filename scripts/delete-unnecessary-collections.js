#!/usr/bin/env node

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    }),
    projectId: process.env.FIREBASE_PROJECT_ID
  });
}

const db = admin.firestore();

async function deleteUnnecessaryCollections() {
  console.log('🗑️  Deleting unnecessary root-level collections...\n');
  
  const collectionsToDelete = ['tasks', 'tenant-data', 'tenant-users'];
  
  for (const collectionName of collectionsToDelete) {
    try {
      console.log(`🗑️  Deleting collection: ${collectionName}`);
      
      // Get all documents in the collection
      const snapshot = await db.collection(collectionName).get();
      
      if (snapshot.empty) {
        console.log(`   ✅ Collection '${collectionName}' is already empty`);
      } else {
        console.log(`   🗑️  Found ${snapshot.size} documents to delete`);
        
        // Delete all documents
        const batch = db.batch();
        snapshot.docs.forEach(doc => {
          batch.delete(doc.ref);
        });
        await batch.commit();
        
        console.log(`   ✅ Deleted ${snapshot.size} documents from '${collectionName}'`);
      }
      
      console.log(`   ✅ Collection '${collectionName}' processed`);
      
    } catch (error) {
      console.error(`   ❌ Error deleting '${collectionName}':`, error.message);
    }
  }
  
  console.log('\n🔍 Verifying final structure...\n');
  
  const allCollections = await db.listCollections();
  console.log('📋 Remaining root-level collections:');
  
  for (const collection of allCollections) {
    const snapshot = await collection.limit(1).get();
    console.log(`   - ${collection.id} (${snapshot.size > 0 ? 'has data' : 'empty'})`);
  }
  
  console.log('\n✅ PERFECT! Clean multi-tenant architecture achieved!');
  console.log('🎯 You now have ONLY the tenants collection at root level!');
}

if (require.main === module) {
  deleteUnnecessaryCollections().catch(console.error);
}
