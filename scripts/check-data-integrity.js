#!/usr/bin/env node

/**
 * EVEXA Data Integrity Check Script
 * Scans database for missing or incorrect tenantId stamps and data consistency issues
 */

const admin = require('firebase-admin');
const path = require('path');

// Initialize Firebase Admin
const serviceAccountPath = path.join(__dirname, '../firebase-service-account.json');

if (!admin.apps.length) {
  try {
    const serviceAccount = require(serviceAccountPath);
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      databaseURL: 'https://evexa-6600c-default-rtdb.firebaseio.com'
    });
    console.log('✅ Firebase Admin initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize Firebase Admin:', error.message);
    process.exit(1);
  }
}

const db = admin.firestore();

// Configuration
const CONFIG = {
  DEFAULT_TENANT_ID: 'evexa-super-admin-tenant',
  BATCH_SIZE: 100,
  DRY_RUN: process.argv.includes('--dry-run'),
  AUTO_FIX: process.argv.includes('--auto-fix'),
  VERBOSE: process.argv.includes('--verbose'),
  COLLECTIONS: process.argv.includes('--collections') 
    ? process.argv[process.argv.indexOf('--collections') + 1]?.split(',') 
    : null
};

// Core collections to check
const CORE_COLLECTIONS = [
  'user_profiles',
  'exhibitions',
  'exhibition_events',
  'exhibition_tasks',
  'lead_contacts',
  'budget_allocations',
  'expense_records',
  'vendor_profiles'
];

/**
 * Main integrity check function
 */
async function runIntegrityCheck() {
  console.log('🔍 EVEXA Data Integrity Check');
  console.log('=' .repeat(60));
  console.log(`Mode: ${CONFIG.DRY_RUN ? 'DRY RUN' : 'LIVE CHECK'}`);
  console.log(`Auto-fix: ${CONFIG.AUTO_FIX ? 'ENABLED' : 'DISABLED'}`);
  console.log(`Expected Tenant ID: ${CONFIG.DEFAULT_TENANT_ID}`);
  console.log('');

  const startTime = new Date();
  const collectionsToCheck = CONFIG.COLLECTIONS || CORE_COLLECTIONS;
  const results = [];

  for (const collectionName of collectionsToCheck) {
    console.log(`\n📁 Checking collection: ${collectionName}`);
    
    try {
      const result = await checkCollection(collectionName);
      results.push(result);
      
      console.log(`   ✅ Completed: ${result.totalDocuments} documents, ${result.issues.length} issues`);
      
      if (result.issues.length > 0) {
        const critical = result.issues.filter(i => i.severity === 'critical').length;
        const high = result.issues.filter(i => i.severity === 'high').length;
        
        if (critical > 0) console.log(`   🚨 Critical issues: ${critical}`);
        if (high > 0) console.log(`   ⚠️  High priority issues: ${high}`);
        
        if (CONFIG.VERBOSE) {
          result.issues.slice(0, 5).forEach(issue => {
            console.log(`      - ${issue.type}: ${issue.description}`);
          });
          if (result.issues.length > 5) {
            console.log(`      ... and ${result.issues.length - 5} more issues`);
          }
        }
      }
      
    } catch (error) {
      console.error(`   ❌ Error checking ${collectionName}:`, error.message);
      results.push({
        collectionName,
        totalDocuments: 0,
        issues: [{
          type: 'check_error',
          severity: 'critical',
          description: `Failed to check collection: ${error.message}`
        }],
        duration: 0
      });
    }
  }

  // Generate final report
  const endTime = new Date();
  const totalDuration = (endTime.getTime() - startTime.getTime()) / 1000;
  
  generateReport(results, totalDuration);
}

/**
 * Check integrity of a single collection
 */
async function checkCollection(collectionName) {
  const startTime = new Date();
  const result = {
    collectionName,
    totalDocuments: 0,
    validDocuments: 0,
    invalidDocuments: 0,
    issues: [],
    fixedIssues: 0,
    duration: 0
  };

  try {
    // Get all documents
    const snapshot = await db.collection(collectionName).get();
    result.totalDocuments = snapshot.size;

    if (snapshot.empty) {
      return result;
    }

    // Check each document
    for (const doc of snapshot.docs) {
      const docData = doc.data();
      const docId = doc.id;
      const issues = [];

      // Check tenant ID
      const tenantIssues = checkTenantId(docId, docData);
      issues.push(...tenantIssues);

      // Check required fields
      const fieldIssues = checkRequiredFields(docId, docData, collectionName);
      issues.push(...fieldIssues);

      // Check data consistency
      const consistencyIssues = checkDataConsistency(docId, docData, collectionName);
      issues.push(...consistencyIssues);

      if (issues.length === 0) {
        result.validDocuments++;
      } else {
        result.invalidDocuments++;
        result.issues.push(...issues);

        // Auto-fix if enabled
        if (CONFIG.AUTO_FIX && !CONFIG.DRY_RUN) {
          const fixableIssues = issues.filter(issue => issue.autoFixable);
          
          for (const issue of fixableIssues) {
            try {
              await fixIssue(collectionName, docId, docData, issue);
              issue.fixed = true;
              result.fixedIssues++;
            } catch (error) {
              console.error(`Failed to fix issue for ${docId}:`, error.message);
            }
          }
        }
      }
    }

  } catch (error) {
    result.issues.push({
      documentId: 'N/A',
      type: 'collection_error',
      severity: 'critical',
      description: `Collection check failed: ${error.message}`,
      autoFixable: false
    });
  }

  const endTime = new Date();
  result.duration = (endTime.getTime() - startTime.getTime()) / 1000;

  return result;
}

/**
 * Check tenant ID issues
 */
function checkTenantId(docId, docData) {
  const issues = [];

  if (!docData.tenantId) {
    issues.push({
      documentId: docId,
      type: 'missing_tenant_id',
      severity: 'critical',
      description: 'Document missing tenantId field',
      suggestedFix: `Add tenantId: "${CONFIG.DEFAULT_TENANT_ID}"`,
      autoFixable: true
    });
  } else if (typeof docData.tenantId !== 'string' || docData.tenantId.trim() === '') {
    issues.push({
      documentId: docId,
      type: 'invalid_tenant_id',
      severity: 'high',
      description: 'Invalid tenantId format',
      suggestedFix: `Set tenantId to "${CONFIG.DEFAULT_TENANT_ID}"`,
      autoFixable: true
    });
  } else if (docData.tenantId !== CONFIG.DEFAULT_TENANT_ID) {
    issues.push({
      documentId: docId,
      type: 'wrong_tenant_id',
      severity: 'medium',
      description: `Document belongs to different tenant: ${docData.tenantId}`,
      suggestedFix: 'Verify tenant ownership',
      autoFixable: false
    });
  }

  return issues;
}

/**
 * Check required fields
 */
function checkRequiredFields(docId, docData, collectionName) {
  const issues = [];
  
  const requiredFields = {
    'user_profiles': ['email', 'displayName', 'role'],
    'exhibitions': ['name', 'startDate', 'endDate', 'venue'],
    'exhibition_events': ['name', 'exhibitionId', 'startDate'],
    'exhibition_tasks': ['title', 'status'],
    'lead_contacts': ['email'],
    'budget_allocations': ['amount', 'activityId'],
    'expense_records': ['amount', 'activityId', 'description'],
    'vendor_profiles': ['name']
  };

  const required = requiredFields[collectionName] || [];
  
  for (const field of required) {
    if (!docData[field] || docData[field] === '') {
      issues.push({
        documentId: docId,
        type: 'missing_required_field',
        severity: 'high',
        description: `Missing required field: ${field}`,
        suggestedFix: `Add value for ${field}`,
        autoFixable: false
      });
    }
  }

  return issues;
}

/**
 * Check data consistency
 */
function checkDataConsistency(docId, docData, collectionName) {
  const issues = [];

  // Check timestamps
  if (!docData.createdAt) {
    issues.push({
      documentId: docId,
      type: 'missing_timestamp',
      severity: 'medium',
      description: 'Missing createdAt timestamp',
      suggestedFix: 'Add createdAt timestamp',
      autoFixable: true
    });
  }

  if (!docData.updatedAt) {
    issues.push({
      documentId: docId,
      type: 'missing_timestamp',
      severity: 'medium',
      description: 'Missing updatedAt timestamp',
      suggestedFix: 'Add updatedAt timestamp',
      autoFixable: true
    });
  }

  // Collection-specific checks
  if (collectionName === 'exhibitions') {
    if (docData.startDate && docData.endDate) {
      const start = new Date(docData.startDate.toDate ? docData.startDate.toDate() : docData.startDate);
      const end = new Date(docData.endDate.toDate ? docData.endDate.toDate() : docData.endDate);
      
      if (start >= end) {
        issues.push({
          documentId: docId,
          type: 'invalid_date_range',
          severity: 'high',
          description: 'Start date must be before end date',
          suggestedFix: 'Fix date range',
          autoFixable: false
        });
      }
    }
  }

  return issues;
}

/**
 * Fix an issue
 */
async function fixIssue(collectionName, docId, docData, issue) {
  const docRef = db.collection(collectionName).doc(docId);
  const updates = {};

  switch (issue.type) {
    case 'missing_tenant_id':
      updates.tenantId = CONFIG.DEFAULT_TENANT_ID;
      break;
      
    case 'invalid_tenant_id':
      updates.tenantId = CONFIG.DEFAULT_TENANT_ID;
      break;
      
    case 'missing_timestamp':
      if (issue.description.includes('createdAt')) {
        updates.createdAt = admin.firestore.FieldValue.serverTimestamp();
      }
      if (issue.description.includes('updatedAt')) {
        updates.updatedAt = admin.firestore.FieldValue.serverTimestamp();
      }
      break;
  }

  if (Object.keys(updates).length > 0) {
    updates.integrityFixed = true;
    updates.integrityFixedAt = admin.firestore.FieldValue.serverTimestamp();
    
    await docRef.update(updates);
  }
}

/**
 * Generate final report
 */
function generateReport(results, totalDuration) {
  console.log('\n' + '='.repeat(60));
  console.log('INTEGRITY CHECK COMPLETE');
  console.log('='.repeat(60));
  
  const totalDocuments = results.reduce((sum, r) => sum + r.totalDocuments, 0);
  const totalIssues = results.reduce((sum, r) => sum + r.issues.length, 0);
  const totalFixed = results.reduce((sum, r) => sum + (r.fixedIssues || 0), 0);
  const criticalIssues = results.reduce((sum, r) => 
    sum + r.issues.filter(i => i.severity === 'critical').length, 0
  );

  console.log(`Duration: ${totalDuration.toFixed(2)} seconds`);
  console.log(`Collections Checked: ${results.length}`);
  console.log(`Total Documents: ${totalDocuments}`);
  console.log(`Total Issues: ${totalIssues}`);
  console.log(`Critical Issues: ${criticalIssues}`);
  console.log(`Fixed Issues: ${totalFixed}`);
  
  if (totalDocuments > 0) {
    const healthScore = ((totalDocuments - totalIssues + totalFixed) / totalDocuments * 100).toFixed(2);
    console.log(`Database Health Score: ${healthScore}%`);
  }

  if (criticalIssues > 0) {
    console.log('\n🚨 CRITICAL ISSUES FOUND - IMMEDIATE ACTION REQUIRED');
  } else if (totalIssues > 0) {
    console.log('\n⚠️  Issues found - Review and fix when possible');
  } else {
    console.log('\n✅ Database integrity is good!');
  }

  // Show recommendations
  console.log('\nRECOMMENDations:');
  if (criticalIssues > 0) {
    console.log('- Fix critical issues immediately');
  }
  if (totalIssues > totalFixed) {
    console.log('- Run with --auto-fix to fix auto-fixable issues');
  }
  if (totalIssues === 0) {
    console.log('- Database is healthy, no action needed');
  }
  
  console.log('\n' + '='.repeat(60));
}

// Run the integrity check
if (require.main === module) {
  runIntegrityCheck().catch(console.error);
}

module.exports = { runIntegrityCheck };
