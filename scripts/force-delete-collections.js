#!/usr/bin/env node

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    }),
    projectId: process.env.FIREBASE_PROJECT_ID
  });
}

const db = admin.firestore();

async function forceDeleteCollections() {
  console.log('🔥 FORCE DELETING collections completely...\n');
  
  const collectionsToDelete = ['tasks', 'tenant-data', 'tenant-users'];
  
  for (const collectionName of collectionsToDelete) {
    try {
      console.log(`🗑️  Force deleting collection: ${collectionName}`);
      
      // Method 1: Delete all documents in batches
      let deleted = 0;
      let hasMore = true;
      
      while (hasMore) {
        const snapshot = await db.collection(collectionName).limit(500).get();
        
        if (snapshot.empty) {
          hasMore = false;
          break;
        }
        
        const batch = db.batch();
        snapshot.docs.forEach(doc => {
          batch.delete(doc.ref);
        });
        
        await batch.commit();
        deleted += snapshot.size;
        console.log(`   🗑️  Deleted ${snapshot.size} documents (total: ${deleted})`);
      }
      
      // Method 2: Try to delete the collection reference itself
      try {
        // This might not work in all Firestore versions, but worth trying
        const collectionRef = db.collection(collectionName);
        
        // Delete any remaining subcollections
        const subcollections = await collectionRef.listCollections?.();
        if (subcollections) {
          for (const subcol of subcollections) {
            const subDocs = await subcol.get();
            if (!subDocs.empty) {
              const batch = db.batch();
              subDocs.docs.forEach(doc => batch.delete(doc.ref));
              await batch.commit();
              console.log(`   🗑️  Deleted ${subDocs.size} docs from subcollection ${subcol.id}`);
            }
          }
        }
        
        console.log(`   ✅ Collection '${collectionName}' force deleted`);
        
      } catch (error) {
        console.log(`   ⚠️  Collection metadata may still show (Firestore limitation): ${error.message}`);
      }
      
    } catch (error) {
      console.error(`   ❌ Error force deleting '${collectionName}':`, error.message);
    }
  }
  
  console.log('\n🔍 Checking final status...\n');
  
  // Wait a moment for changes to propagate
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  const allCollections = await db.listCollections();
  console.log('📋 Root-level collections after force delete:');
  
  for (const collection of allCollections) {
    const snapshot = await collection.limit(1).get();
    const docCount = snapshot.size;
    
    if (docCount === 0) {
      console.log(`   - ${collection.id} (EMPTY - may still show in console due to Firestore behavior)`);
    } else {
      console.log(`   - ${collection.id} (${docCount} documents) ✅`);
    }
  }
  
  console.log('\n💡 IMPORTANT NOTE:');
  console.log('   If empty collections still appear in Firebase Console, this is normal Firestore behavior.');
  console.log('   Empty collections are harmless and do not affect your app or consume resources.');
  console.log('   The only way to completely remove them is to delete the entire Firebase project.');
  
  console.log('\n✅ Force deletion completed!');
  console.log('🎯 Your app will only use the tenants collection - others are ignored!');
}

if (require.main === module) {
  forceDeleteCollections().catch(console.error);
}
