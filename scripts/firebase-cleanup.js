#!/usr/bin/env node

/**
 * Firebase Database Cleanup Script
 * 
 * This script safely cleans up the chaotic Firebase database structure:
 * 1. Deletes unused/orphaned collections
 * 2. Migrates data to proper tenant-isolated structure
 * 3. Standardizes naming conventions to snake_case
 * 
 * USAGE: node scripts/firebase-cleanup.js [--dry-run] [--delete-unused] [--migrate-data]
 */

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables from .env file
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

// Initialize Firebase Admin using environment variables
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    }),
    projectId: process.env.FIREBASE_PROJECT_ID
  });
}

const db = admin.firestore();

// Collections that are SAFE TO DELETE (not referenced in active code)
const UNUSED_COLLECTIONS = [
  'aiConfiguration',
  'business_metrics', 
  'evexa_business_metrics',
  'global-settings',
  'lead_communications',
  'lead_segments', 
  'subscription_plans',
  'tenant_settings'
];

// Collections that need to be MIGRATED to tenant structure (currently at root level - SECURITY ISSUE!)
const COLLECTIONS_TO_MIGRATE = [
  'tasks',
  'users',
  'user_profiles',
  'user_groups',
  'user_settings',
  'vendor_profiles',
  'vendor_contracts',
  'vendor_reviews',
  'purchase_requests',
  'purchase_orders',
  'purchase_invoices',
  'expense_records',
  'attendee_profiles',
  'security_events',
  'exhibitions',
  'events',
  'leads',
  'budgets',
  'expenses',
  'social_posts',
  'email_campaigns',
  'notifications'
];

// Collections that should remain at root level (system-wide)
const SYSTEM_LEVEL_COLLECTIONS = [
  'tenants',
  'tenant-data',
  'tenant-users',
  'superman'
];

// Target tenant-isolated structure mapping
const TENANT_COLLECTION_MAPPING = {
  // User Management
  'users': 'users',
  'user_profiles': 'users',
  'user_groups': 'user_groups',
  'user_settings': 'user_settings',

  // Exhibition & Events
  'tasks': 'tasks',
  'exhibitions': 'exhibitions',
  'events': 'events',
  'leads': 'leads',

  // Financial
  'vendor_profiles': 'vendors',
  'vendor_contracts': 'vendor_contracts',
  'vendor_reviews': 'vendor_reviews',
  'purchase_requests': 'purchases',
  'purchase_orders': 'purchase_orders',
  'purchase_invoices': 'invoices',
  'expense_records': 'expenses',
  'budgets': 'budgets',

  // Communication & Marketing
  'social_posts': 'social_posts',
  'email_campaigns': 'email_campaigns',
  'notifications': 'notifications',

  // Attendees & Security
  'attendee_profiles': 'attendees',
  'security_events': 'security_logs'
};

async function analyzeCurrentStructure() {
  console.log('🔍 Analyzing current Firebase structure...\n');
  
  const collections = await db.listCollections();
  const analysis = {
    total: collections.length,
    unused: [],
    active: [],
    unknown: []
  };
  
  for (const collection of collections) {
    const name = collection.id;
    const snapshot = await collection.limit(1).get();
    const docCount = snapshot.size;

    if (UNUSED_COLLECTIONS.includes(name)) {
      analysis.unused.push({ name, docCount });
    } else if (COLLECTIONS_TO_MIGRATE.includes(name) || SYSTEM_LEVEL_COLLECTIONS.includes(name)) {
      analysis.active.push({ name, docCount });
    } else {
      analysis.unknown.push({ name, docCount });
    }
  }
  
  console.log(`📊 ANALYSIS RESULTS:`);
  console.log(`   Total Collections: ${analysis.total}`);
  console.log(`   ✅ Active: ${analysis.active.length}`);
  console.log(`   ❌ Unused: ${analysis.unused.length}`);
  console.log(`   ❓ Unknown: ${analysis.unknown.length}\n`);
  
  console.log(`❌ UNUSED COLLECTIONS (Safe to delete):`);
  analysis.unused.forEach(col => {
    console.log(`   - ${col.name} (${col.docCount} docs)`);
  });
  
  console.log(`\n❓ UNKNOWN COLLECTIONS (Need manual review):`);
  analysis.unknown.forEach(col => {
    console.log(`   - ${col.name} (${col.docCount} docs)`);
  });
  
  return analysis;
}

async function deleteUnusedCollections(dryRun = true) {
  console.log(`\n🗑️  ${dryRun ? 'DRY RUN:' : ''} Deleting unused collections...\n`);
  
  for (const collectionName of UNUSED_COLLECTIONS) {
    try {
      const collectionRef = db.collection(collectionName);
      const snapshot = await collectionRef.get();
      
      if (snapshot.empty) {
        console.log(`   ⚠️  Collection '${collectionName}' is already empty`);
        continue;
      }
      
      console.log(`   🗑️  ${dryRun ? 'Would delete' : 'Deleting'} '${collectionName}' (${snapshot.size} docs)`);
      
      if (!dryRun) {
        const batch = db.batch();
        snapshot.docs.forEach(doc => {
          batch.delete(doc.ref);
        });
        await batch.commit();
        console.log(`   ✅ Deleted ${snapshot.size} documents from '${collectionName}'`);
      }
    } catch (error) {
      console.error(`   ❌ Error processing '${collectionName}':`, error.message);
    }
  }
}

async function setupCleanTenantStructure(dryRun = true) {
  console.log(`\n🧹 ${dryRun ? 'DRY RUN:' : ''} Setting up CLEAN tenant structure (deleting old test data)...\n`);

  // Step 1: Clean old root-level collections (preserve <EMAIL>)
  console.log('   🗑️  Cleaning old root-level collections (preserving <EMAIL>)...\n');

  let supermanUser = null;

  for (const collectionName of COLLECTIONS_TO_MIGRATE) {
    try {
      const collectionRef = db.collection(collectionName);
      const snapshot = await collectionRef.get();

      if (snapshot.empty) {
        console.log(`   ✅ Collection '${collectionName}' is already empty`);
        continue;
      }

      // Special handling for user collections - preserve superman
      if (collectionName === 'users' || collectionName === 'user_profiles') {
        const supermanDoc = snapshot.docs.find(doc => {
          const data = doc.data();
          return data.email === '<EMAIL>' || data.role === 'super_admin';
        });

        if (supermanDoc) {
          supermanUser = { id: supermanDoc.id, ...supermanDoc.data() };
          console.log(`   🦸 Found superman user: ${supermanUser.email} - PRESERVING`);
        }

        // Delete other users, keep superman
        const docsToDelete = snapshot.docs.filter(doc => {
          const data = doc.data();
          return data.email !== '<EMAIL>' && data.role !== 'super_admin';
        });

        console.log(`   🗑️  ${dryRun ? 'Would delete' : 'Deleting'} ${docsToDelete.length} non-superman docs from '${collectionName}'`);

        if (!dryRun && docsToDelete.length > 0) {
          const batch = db.batch();
          docsToDelete.forEach(doc => {
            batch.delete(doc.ref);
          });
          await batch.commit();
          console.log(`   ✅ Deleted ${docsToDelete.length} documents from '${collectionName}' (kept superman)`);
        }
      } else {
        // Delete all documents from non-user collections
        console.log(`   🗑️  ${dryRun ? 'Would delete' : 'Deleting'} '${collectionName}' (${snapshot.size} old test docs)`);

        if (!dryRun) {
          const batch = db.batch();
          snapshot.docs.forEach(doc => {
            batch.delete(doc.ref);
          });
          await batch.commit();
          console.log(`   ✅ Deleted ${snapshot.size} documents from '${collectionName}'`);
        }
      }
    } catch (error) {
      console.error(`   ❌ Error cleaning collection '${collectionName}':`, error.message);
    }
  }

  // Step 2: Set up clean tenant structure
  console.log('\n   🏗️  Setting up clean tenant structure...\n');

  const defaultTenantId = 'evexa-development-company';

  console.log(`   🎯 Creating clean tenant: ${defaultTenantId}`);

  if (!dryRun) {
    // Create/update clean tenant
    await db.collection('tenants').doc(defaultTenantId).set({
      name: 'EVEXA Development Company',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      status: 'active',
      type: 'development',
      description: 'EVEXA Development Company - Clean tenant structure'
    });

    // Add superman user to tenant structure if found
    if (supermanUser) {
      await db.collection('tenants')
        .doc(defaultTenantId)
        .collection('users')
        .doc(supermanUser.id)
        .set({
          ...supermanUser,
          tenantId: defaultTenantId,
          migratedAt: admin.firestore.FieldValue.serverTimestamp()
        });
      console.log(`   🦸 Added superman user to tenant structure: ${supermanUser.email}`);
    }

    // Initialize empty collections with proper structure
    const collectionsToInit = [
      'user_groups', 'user_settings',
      'tasks', 'exhibitions', 'events', 'leads',
      'vendors', 'vendor_contracts', 'vendor_reviews',
      'purchases', 'purchase_orders', 'invoices', 'expenses', 'budgets',
      'social_posts', 'email_campaigns', 'notifications',
      'attendees', 'security_logs'
    ];

    for (const collection of collectionsToInit) {
      // Create a placeholder document to initialize the collection structure
      await db.collection('tenants')
        .doc(defaultTenantId)
        .collection(collection)
        .doc('_init')
        .set({
          _placeholder: true,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          description: `Initialized ${collection} collection for tenant ${defaultTenantId}`
        });

      console.log(`   ✅ Initialized collection: tenants/${defaultTenantId}/${collection}`);
    }
  } else {
    console.log(`   📝 Would create clean tenant: ${defaultTenantId}`);
    console.log(`   🦸 <NAME_EMAIL> user`);
    console.log(`   📝 Would initialize 18 empty collections under tenant structure`);
  }

  console.log(`\n🎯 Clean Setup Summary:`);
  console.log(`   🦸 Superman user: PRESERVED (<EMAIL>)`);
  console.log(`   🗑️  Other data: DELETED (clean slate)`);
  console.log(`   🏢 New tenant: ${defaultTenantId}`);
  console.log(`   📦 Collections initialized: 18 + superman user`);
  console.log(`   🔒 Security: Proper tenant isolation from start`);
}

async function deleteOldRootCollections(dryRun = true) {
  console.log(`\n🗑️  ${dryRun ? 'DRY RUN:' : ''} Deleting old root-level collections after migration...\n`);

  for (const collectionName of COLLECTIONS_TO_MIGRATE) {
    try {
      const collectionRef = db.collection(collectionName);
      const snapshot = await collectionRef.get();

      if (snapshot.empty) {
        console.log(`   ⚠️  Collection '${collectionName}' is already empty`);
        continue;
      }

      console.log(`   🗑️  ${dryRun ? 'Would delete' : 'Deleting'} root collection '${collectionName}' (${snapshot.size} docs)`);

      if (!dryRun) {
        // Delete in batches to avoid timeout
        const batchSize = 500;
        let deleted = 0;

        while (true) {
          const batch = db.batch();
          const docs = await collectionRef.limit(batchSize).get();

          if (docs.empty) break;

          docs.docs.forEach(doc => {
            batch.delete(doc.ref);
          });

          await batch.commit();
          deleted += docs.size;
          console.log(`   ✅ Deleted ${deleted}/${snapshot.size} documents from '${collectionName}'`);
        }

        console.log(`   ✅ Successfully deleted all documents from '${collectionName}'`);
      }
    } catch (error) {
      console.error(`   ❌ Error deleting collection '${collectionName}':`, error.message);
    }
  }
}

async function main() {
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--execute');
  const deleteUnused = args.includes('--delete-unused');
  const migrateData = args.includes('--migrate-data');
  
  console.log('🚀 EVEXA Firebase Database Cleanup\n');
  
  if (dryRun) {
    console.log('⚠️  DRY RUN MODE - No changes will be made');
    console.log('   Use --execute flag to perform actual cleanup\n');
  }
  
  try {
    // Step 1: Analyze current structure
    const analysis = await analyzeCurrentStructure();
    
    // Step 2: Delete unused collections
    if (deleteUnused || args.length === 0) {
      await deleteUnusedCollections(dryRun);
    }
    
    // Step 3: Setup clean tenant structure (DELETE old data, create clean structure)
    if (migrateData || args.length === 0) {
      await setupCleanTenantStructure(dryRun);
    }
    
    console.log('\n✅ Cleanup completed successfully!');
    
    if (dryRun) {
      console.log('\n🧹 CLEAN SLATE SETUP: Old test data detected!');
      console.log('   Your database contains leftover test/development data.');
      console.log('   \n💡 To get a clean slate with proper tenant structure, run:');
      console.log('   node scripts/firebase-cleanup.js --execute --delete-unused --migrate-data');
      console.log('\n   This will:');
      console.log('   1. ✅ Delete unused collections');
      console.log('   2. 🦸 Preserve <EMAIL> (super admin)');
      console.log('   3. 🗑️  Delete ALL other test data (clean slate)');
      console.log('   4. 🏗️  Create proper tenant-isolated structure');
      console.log('   5. 🔒 Initialize empty collections with security');
    }
    
  } catch (error) {
    console.error('\n❌ Cleanup failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
