#!/usr/bin/env node

/**
 * Performance Benchmark Runner
 * Comprehensive performance benchmarking for query optimization, UI loading, and cost validation
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Benchmark configuration
const BENCHMARK_CONFIG = {
  timeout: 120000, // 2 minutes for comprehensive benchmarks
  retries: 1,
  verbose: false,
  saveResults: true
};

// Benchmark test suites
const BENCHMARK_SUITES = {
  'query-performance': {
    path: 'src/__tests__/performance/performanceBenchmarking.test.ts',
    description: 'Query performance benchmarking (duplication vs joins, flat vs nested)',
    timeout: 60000
  },
  'ui-loading': {
    path: 'src/__tests__/performance/uiLoadingBenchmarks.test.ts',
    description: 'UI component loading time benchmarks',
    timeout: 45000
  },
  'data-volumes': {
    path: 'src/__tests__/performance/dataVolumeBenchmarks.test.ts',
    description: 'Realistic data volume performance testing',
    timeout: 90000
  },
  'cost-optimization': {
    path: 'src/__tests__/performance/costOptimizationBenchmarks.test.ts',
    description: 'Cost optimization validation and projections',
    timeout: 30000
  }
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logStep(step, message) {
  log(`\n[${step}] ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logMetric(label, value, unit = '', color = 'cyan') {
  log(`📊 ${label}: ${value}${unit}`, color);
}

// Check prerequisites
async function checkPrerequisites() {
  logStep('SETUP', 'Checking prerequisites...');
  
  // Check if Node.js performance API is available
  if (typeof performance === 'undefined') {
    logWarning('Performance API not available - some metrics may be limited');
  } else {
    logSuccess('Performance API is available');
  }
  
  // Check if test files exist
  const missingFiles = [];
  for (const [name, config] of Object.entries(BENCHMARK_SUITES)) {
    if (!fs.existsSync(config.path)) {
      missingFiles.push(config.path);
    }
  }
  
  if (missingFiles.length > 0) {
    logError('Missing benchmark test files:');
    missingFiles.forEach(file => log(`  - ${file}`, 'red'));
    return false;
  }
  
  logSuccess('All prerequisites met');
  return true;
}

// Run a specific benchmark suite
async function runBenchmarkSuite(suiteName, config) {
  return new Promise((resolve) => {
    logStep('BENCHMARK', `Running ${config.description}...`);
    
    const args = [
      'test',
      config.path,
      '--testTimeout',
      config.timeout.toString(),
      '--verbose'
    ];
    
    if (BENCHMARK_CONFIG.verbose) {
      args.push('--verbose');
    }
    
    const child = spawn('npx', ['jest', ...args], {
      stdio: 'pipe',
      cwd: process.cwd(),
      env: {
        ...process.env,
        NODE_ENV: 'test',
        BENCHMARK_MODE: 'true'
      }
    });
    
    let output = '';
    let errorOutput = '';
    
    child.stdout.on('data', (data) => {
      output += data.toString();
      if (BENCHMARK_CONFIG.verbose) {
        process.stdout.write(data);
      }
    });
    
    child.stderr.on('data', (data) => {
      errorOutput += data.toString();
      if (BENCHMARK_CONFIG.verbose) {
        process.stderr.write(data);
      }
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        logSuccess(`${suiteName} benchmarks completed`);
        const metrics = extractMetricsFromOutput(output);
        displayBenchmarkMetrics(suiteName, metrics);
        resolve({ success: true, output, errorOutput, metrics });
      } else {
        logError(`${suiteName} benchmarks failed with code ${code}`);
        if (!BENCHMARK_CONFIG.verbose && errorOutput) {
          log('Error output:', 'red');
          log(errorOutput, 'red');
        }
        resolve({ success: false, output, errorOutput, code });
      }
    });
    
    child.on('error', (error) => {
      logError(`Failed to run ${suiteName} benchmarks: ${error.message}`);
      resolve({ success: false, error: error.message });
    });
  });
}

// Extract performance metrics from test output
function extractMetricsFromOutput(output) {
  const metrics = {};
  
  // Extract common performance patterns
  const patterns = {
    queryTime: /Query completed in ([\d.]+)ms/g,
    improvement: /([\d.]+)% faster/g,
    costSavings: /saving \$([\d.]+)/g,
    loadTime: /loaded in ([\d.]+)ms/g,
    throughput: /([\d.]+) docs\/sec/g
  };
  
  for (const [key, pattern] of Object.entries(patterns)) {
    const matches = [...output.matchAll(pattern)];
    if (matches.length > 0) {
      metrics[key] = matches.map(match => parseFloat(match[1]));
    }
  }
  
  return metrics;
}

// Display benchmark metrics
function displayBenchmarkMetrics(suiteName, metrics) {
  if (Object.keys(metrics).length === 0) return;
  
  log(`\n📈 ${suiteName} Metrics:`, 'bright');
  
  if (metrics.queryTime) {
    const avgQueryTime = metrics.queryTime.reduce((sum, time) => sum + time, 0) / metrics.queryTime.length;
    logMetric('Average Query Time', avgQueryTime.toFixed(2), 'ms');
  }
  
  if (metrics.improvement) {
    const avgImprovement = metrics.improvement.reduce((sum, imp) => sum + imp, 0) / metrics.improvement.length;
    logMetric('Average Performance Improvement', avgImprovement.toFixed(1), '%', 'green');
  }
  
  if (metrics.costSavings) {
    const totalSavings = metrics.costSavings.reduce((sum, saving) => sum + saving, 0);
    logMetric('Total Cost Savings', totalSavings.toFixed(4), '$', 'green');
  }
  
  if (metrics.loadTime) {
    const avgLoadTime = metrics.loadTime.reduce((sum, time) => sum + time, 0) / metrics.loadTime.length;
    logMetric('Average Load Time', avgLoadTime.toFixed(2), 'ms');
  }
  
  if (metrics.throughput) {
    const avgThroughput = metrics.throughput.reduce((sum, tp) => sum + tp, 0) / metrics.throughput.length;
    logMetric('Average Throughput', avgThroughput.toFixed(1), ' docs/sec');
  }
}

// Generate comprehensive benchmark report
function generateBenchmarkReport(results) {
  logHeader('PERFORMANCE BENCHMARK REPORT');
  
  const totalSuites = Object.keys(results).length;
  const passedSuites = Object.values(results).filter(r => r.success).length;
  const failedSuites = totalSuites - passedSuites;
  
  log(`\nTotal Benchmark Suites: ${totalSuites}`);
  log(`Passed: ${passedSuites}`, passedSuites > 0 ? 'green' : 'reset');
  log(`Failed: ${failedSuites}`, failedSuites > 0 ? 'red' : 'reset');
  
  log('\nDetailed Results:', 'bright');
  for (const [suiteName, result] of Object.entries(results)) {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    const color = result.success ? 'green' : 'red';
    const description = BENCHMARK_SUITES[suiteName]?.description || 'Unknown benchmark suite';
    
    log(`  ${status} ${suiteName} - ${description}`, color);
    
    if (!result.success && result.code) {
      log(`    Exit code: ${result.code}`, 'red');
    }
    
    if (!result.success && result.error) {
      log(`    Error: ${result.error}`, 'red');
    }
  }
  
  // Performance insights
  log('\nPerformance Insights:', 'bright');
  const allMetrics = Object.values(results)
    .filter(r => r.success && r.metrics)
    .map(r => r.metrics);
  
  if (allMetrics.length > 0) {
    const totalImprovements = allMetrics
      .flatMap(m => m.improvement || [])
      .reduce((sum, imp) => sum + imp, 0);
    
    const totalCostSavings = allMetrics
      .flatMap(m => m.costSavings || [])
      .reduce((sum, saving) => sum + saving, 0);
    
    if (totalImprovements > 0) {
      logMetric('Total Performance Improvements', totalImprovements.toFixed(1), '%', 'green');
    }
    
    if (totalCostSavings > 0) {
      logMetric('Total Cost Savings', totalCostSavings.toFixed(4), '$', 'green');
    }
    
    log('  ✓ Query optimization validated');
    log('  ✓ UI loading performance measured');
    log('  ✓ Data volume scalability tested');
    log('  ✓ Cost optimization confirmed');
  }
  
  // Overall result
  if (failedSuites === 0) {
    log('\n🎉 All performance benchmarks passed!', 'green');
    log('🚀 System performance is optimized and ready for production', 'green');
    return true;
  } else {
    log(`\n💥 ${failedSuites} benchmark suite(s) failed`, 'red');
    log('⚠️  Performance issues detected - review failed benchmarks', 'red');
    return false;
  }
}

// Save benchmark results to file
async function saveBenchmarkResults(results) {
  if (!BENCHMARK_CONFIG.saveResults) return;
  
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `benchmark-results-${timestamp}.json`;
    const filepath = path.join(process.cwd(), 'benchmark-results', filename);
    
    // Ensure directory exists
    const dir = path.dirname(filepath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    const reportData = {
      timestamp: new Date().toISOString(),
      results,
      summary: {
        totalSuites: Object.keys(results).length,
        passedSuites: Object.values(results).filter(r => r.success).length,
        failedSuites: Object.values(results).filter(r => !r.success).length
      }
    };
    
    fs.writeFileSync(filepath, JSON.stringify(reportData, null, 2));
    logSuccess(`Benchmark results saved to ${filename}`);
  } catch (error) {
    logWarning(`Failed to save benchmark results: ${error.message}`);
  }
}

// Main execution function
async function main() {
  const args = process.argv.slice(2);
  const specificSuite = args.find(arg => !arg.startsWith('--'));
  const verboseFlag = args.includes('--verbose');
  const noSaveFlag = args.includes('--no-save');
  
  if (verboseFlag) {
    BENCHMARK_CONFIG.verbose = true;
  }
  
  if (noSaveFlag) {
    BENCHMARK_CONFIG.saveResults = false;
  }
  
  logHeader('EVEXA PERFORMANCE BENCHMARK RUNNER');
  
  // Pre-flight checks
  if (!(await checkPrerequisites())) {
    process.exit(1);
  }
  
  // Determine which benchmarks to run
  let suitesToRun = BENCHMARK_SUITES;
  if (specificSuite) {
    if (BENCHMARK_SUITES[specificSuite]) {
      suitesToRun = { [specificSuite]: BENCHMARK_SUITES[specificSuite] };
      log(`Running specific benchmark suite: ${specificSuite}`, 'yellow');
    } else {
      logError(`Unknown benchmark suite: ${specificSuite}`);
      log('Available benchmark suites:', 'yellow');
      Object.keys(BENCHMARK_SUITES).forEach(name => log(`  - ${name}`, 'yellow'));
      process.exit(1);
    }
  }
  
  // Run benchmarks
  const results = {};
  const startTime = Date.now();
  
  for (const [suiteName, config] of Object.entries(suitesToRun)) {
    const result = await runBenchmarkSuite(suiteName, config);
    results[suiteName] = result;
    
    // Stop on first failure if running all benchmarks
    if (!result.success && !specificSuite) {
      logWarning('Stopping benchmark execution due to failure');
      break;
    }
  }
  
  const endTime = Date.now();
  const duration = Math.round((endTime - startTime) / 1000);
  
  // Generate report
  const allPassed = generateBenchmarkReport(results);
  
  // Save results
  await saveBenchmarkResults(results);
  
  log(`\nTotal execution time: ${duration} seconds`, 'cyan');
  
  // Exit with appropriate code
  process.exit(allPassed ? 0 : 1);
}

// Handle uncaught errors
process.on('unhandledRejection', (error) => {
  logError(`Unhandled rejection: ${error.message}`);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  logError(`Uncaught exception: ${error.message}`);
  process.exit(1);
});

// Show help if requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  logHeader('PERFORMANCE BENCHMARK RUNNER HELP');
  log('\nUsage:');
  log('  node scripts/run-performance-benchmarks.js [suite-name] [options]');
  log('\nAvailable benchmark suites:');
  Object.entries(BENCHMARK_SUITES).forEach(([name, config]) => {
    log(`  ${name.padEnd(20)} - ${config.description}`);
  });
  log('\nOptions:');
  log('  --verbose     Enable verbose output');
  log('  --no-save     Skip saving benchmark results to file');
  log('  --help, -h    Show this help message');
  log('\nExamples:');
  log('  node scripts/run-performance-benchmarks.js');
  log('  node scripts/run-performance-benchmarks.js query-performance');
  log('  node scripts/run-performance-benchmarks.js --verbose');
  process.exit(0);
}

// Run the main function
main().catch((error) => {
  logError(`Fatal error: ${error.message}`);
  process.exit(1);
});
