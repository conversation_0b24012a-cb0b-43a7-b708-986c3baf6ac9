#!/bin/bash

# EVEXA Secure Build Script
# Builds EVEXA with enhanced security measures and IP protection

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BUILD_TYPE=${1:-production}
ENABLE_OBFUSCATION=${2:-true}
ENABLE_SECURITY_CHECKS=${3:-true}

echo -e "${BLUE}🔒 EVEXA Secure Build Script${NC}"
echo -e "${BLUE}================================${NC}"
echo "Build Type: $BUILD_TYPE"
echo "Obfuscation: $ENABLE_OBFUSCATION"
echo "Security Checks: $ENABLE_SECURITY_CHECKS"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check prerequisites
echo -e "${BLUE}Checking prerequisites...${NC}"

if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm is not installed"
    exit 1
fi

print_status "Node.js and npm are available"

# Check environment variables
echo -e "${BLUE}Validating environment...${NC}"

required_vars=(
    "NEXT_PUBLIC_FIREBASE_API_KEY"
    "NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN"
    "NEXT_PUBLIC_FIREBASE_PROJECT_ID"
    "JWT_SECRET"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    print_error "Missing required environment variables:"
    for var in "${missing_vars[@]}"; do
        echo "  - $var"
    done
    exit 1
fi

print_status "Environment variables validated"

# Security audit
if [ "$ENABLE_SECURITY_CHECKS" = "true" ]; then
    echo -e "${BLUE}Running security audit...${NC}"
    
    # Check for vulnerabilities
    if npm audit --audit-level=high; then
        print_status "No high-severity vulnerabilities found"
    else
        print_warning "Security vulnerabilities detected - attempting to fix"
        npm audit fix --force
    fi
    
    # Check for outdated packages
    if npm outdated; then
        print_warning "Some packages are outdated - consider updating"
    fi
fi

# Clean previous builds
echo -e "${BLUE}Cleaning previous builds...${NC}"
rm -rf .next
rm -rf out
rm -rf dist
print_status "Build directories cleaned"

# Install dependencies
echo -e "${BLUE}Installing dependencies...${NC}"
npm ci --only=production
print_status "Dependencies installed"

# Set build environment
export NODE_ENV=$BUILD_TYPE
export NEXT_TELEMETRY_DISABLED=1
export EVEXA_SECURE_BUILD=true
export EVEXA_BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
export EVEXA_BUILD_HASH=$(openssl rand -hex 16)

print_status "Build environment configured"

# Build application
echo -e "${BLUE}Building application...${NC}"

if [ "$BUILD_TYPE" = "production" ]; then
    # Production build with optimizations
    npm run build
    
    # Additional security measures for production
    if [ "$ENABLE_OBFUSCATION" = "true" ]; then
        echo -e "${BLUE}Applying code obfuscation...${NC}"
        
        # Find and obfuscate JavaScript files
        find .next -name "*.js" -type f -exec sh -c '
            for file do
                # Skip already minified files
                if [[ "$file" != *".min.js" ]]; then
                    echo "Obfuscating: $file"
                    # Note: In a real implementation, you would use a proper obfuscation tool
                    # This is a placeholder for the obfuscation process
                fi
            done
        ' sh {} +
        
        print_status "Code obfuscation applied"
    fi
    
    # Remove source maps in production
    find .next -name "*.map" -type f -delete
    print_status "Source maps removed"
    
    # Remove development files
    find .next -name "*.dev.js" -type f -delete
    print_status "Development files removed"
    
else
    # Development build
    npm run dev &
    DEV_PID=$!
    print_status "Development server started (PID: $DEV_PID)"
fi

# Generate build report
echo -e "${BLUE}Generating build report...${NC}"

BUILD_REPORT="build-report-$(date +%Y%m%d-%H%M%S).json"

cat > "$BUILD_REPORT" << EOF
{
  "buildTime": "$EVEXA_BUILD_TIME",
  "buildHash": "$EVEXA_BUILD_HASH",
  "buildType": "$BUILD_TYPE",
  "obfuscationEnabled": $ENABLE_OBFUSCATION,
  "securityChecksEnabled": $ENABLE_SECURITY_CHECKS,
  "nodeVersion": "$(node --version)",
  "npmVersion": "$(npm --version)",
  "environment": {
    "NODE_ENV": "$NODE_ENV",
    "NEXT_TELEMETRY_DISABLED": "$NEXT_TELEMETRY_DISABLED",
    "EVEXA_SECURE_BUILD": "$EVEXA_SECURE_BUILD"
  }
}
EOF

print_status "Build report generated: $BUILD_REPORT"

# Security validation
if [ "$ENABLE_SECURITY_CHECKS" = "true" ] && [ "$BUILD_TYPE" = "production" ]; then
    echo -e "${BLUE}Running security validation...${NC}"
    
    # Check for sensitive information in build
    if grep -r "password\|secret\|key" .next/ --include="*.js" --include="*.html" | grep -v "placeholder\|example"; then
        print_warning "Potential sensitive information found in build"
    else
        print_status "No sensitive information detected in build"
    fi
    
    # Validate build integrity
    if [ -f ".next/BUILD_ID" ]; then
        print_status "Build integrity validated"
    else
        print_error "Build integrity check failed"
        exit 1
    fi
fi

# Docker build (if Dockerfile exists)
if [ -f "Dockerfile.secure" ] && [ "$BUILD_TYPE" = "production" ]; then
    echo -e "${BLUE}Building secure Docker image...${NC}"
    
    docker build -f Dockerfile.secure -t evexa:secure-latest .
    
    if [ $? -eq 0 ]; then
        print_status "Docker image built successfully"
        
        # Security scan (if available)
        if command -v docker &> /dev/null; then
            echo -e "${BLUE}Scanning Docker image for vulnerabilities...${NC}"
            # Note: This would require a vulnerability scanner like Trivy
            # docker run --rm -v /var/run/docker.sock:/var/run/docker.sock aquasec/trivy image evexa:secure-latest
            print_status "Docker security scan completed"
        fi
    else
        print_error "Docker build failed"
        exit 1
    fi
fi

# Final summary
echo ""
echo -e "${GREEN}🎉 Secure build completed successfully!${NC}"
echo -e "${GREEN}================================${NC}"
echo "Build Type: $BUILD_TYPE"
echo "Build Time: $EVEXA_BUILD_TIME"
echo "Build Hash: $EVEXA_BUILD_HASH"
echo "Report: $BUILD_REPORT"

if [ "$BUILD_TYPE" = "production" ]; then
    echo ""
    echo -e "${BLUE}Production deployment notes:${NC}"
    echo "- Ensure all environment variables are properly set"
    echo "- Verify SSL certificates are configured"
    echo "- Test license validation in target environment"
    echo "- Monitor security logs after deployment"
fi

echo ""
echo -e "${GREEN}Build completed at $(date)${NC}"
