# EVEXA Secure Production Dockerfile
# Multi-stage build with security hardening and IP protection

# ==========================================
# Stage 1: Dependencies and Build
# ==========================================
FROM node:18-alpine AS deps
LABEL maintainer="EVEXA Security Team"
LABEL description="Secure EVEXA Production Container"

# Security: Run as non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Security: Install security updates
RUN apk update && apk upgrade
RUN apk add --no-cache libc6-compat dumb-init

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json package-lock.json* ./

# Install dependencies with security audit
RUN npm ci --only=production --audit --audit-level=high
RUN npm audit fix --force

# ==========================================
# Stage 2: Build Application
# ==========================================
FROM node:18-alpine AS builder
WORKDIR /app

# Copy dependencies from previous stage
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Security: Set build-time environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV EVEXA_SECURE_BUILD=true

# Build application with security optimizations
RUN npm run build

# ==========================================
# Stage 3: Production Runtime
# ==========================================
FROM node:18-alpine AS runner
WORKDIR /app

# Security: Install runtime security tools
RUN apk add --no-cache \
    dumb-init \
    curl \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# Security: Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Security: Set proper permissions
RUN mkdir -p /app/.next/cache
RUN chown -R nextjs:nodejs /app

# Copy built application
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Security: Remove unnecessary files
RUN rm -rf /app/src /app/.git /app/node_modules/.cache

# Security: Set environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Security: Expose only necessary port
EXPOSE 3000

# Security: Switch to non-root user
USER nextjs

# Security: Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Security: Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start application
CMD ["node", "server.js"]

# ==========================================
# Security Labels and Metadata
# ==========================================
LABEL security.scan="required"
LABEL security.level="high"
LABEL version="1.0.0"
LABEL build.date="$(date -u +'%Y-%m-%dT%H:%M:%SZ')"
