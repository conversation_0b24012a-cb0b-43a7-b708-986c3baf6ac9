"use client";

import React from 'react';
import { BillingDashboard } from '@/components/billing/BillingDashboard';
import { useAuth } from '@/contexts/auth-context';
// import { useTenant } from '@/contexts/tenant-context';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

export default function BillingPage() {
  const { user, isLoading } = useAuth();
  // const { tenant, isLoading: tenantLoading } = useTenant();
  const tenant = { id: 'default-tenant', name: 'Default Tenant' }; // Temporary fallback
  const tenantLoading = false;

  if (isLoading || tenantLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!user || !tenant || !tenant.id) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">Access Denied</h3>
            <p className="text-muted-foreground">
              You need to be logged in and have a valid tenant to access billing information.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <BillingDashboard tenantId={tenant?.id || ''} />
    </div>
  );
}
