"use client";

import React, { useEffect, useState } from 'react';
import { useSearchPara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { useTenant } from '@/contexts/tenant-context';
import { toast } from 'sonner';

export default function BillingSuccessPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  const { tenant } = useTenant();
  const [loading, setLoading] = useState(true);
  const [sessionData, setSessionData] = useState<any>(null);

  const sessionId = searchParams.get('session_id');

  useEffect(() => {
    if (sessionId && user && tenant?.id) {
      verifySession();
    }
  }, [sessionId, user, tenant]);

  const verifySession = async () => {
    try {
      // TODO: Get proper auth token from Firebase Auth
      const token = 'temp-token';
      
      const response = await fetch(`/api/billing/verify-session?session_id=${sessionId}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSessionData(data);
        toast.success('Subscription activated successfully!');
      } else {
        throw new Error('Failed to verify session');
      }
    } catch (error) {
      console.error('Error verifying session:', error);
      toast.error('Failed to verify payment. Please contact support.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-12">
        <Card className="max-w-md mx-auto">
          <CardContent className="pt-6">
            <div className="text-center">
              <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Verifying Payment</h3>
              <p className="text-muted-foreground">
                Please wait while we confirm your subscription...
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-12">
      <Card className="max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4">
            <CheckCircle className="h-16 w-16 text-green-500" />
          </div>
          <CardTitle className="text-2xl">Payment Successful!</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-muted-foreground">
            Thank you for subscribing to EVEXA. Your subscription has been activated successfully.
          </p>
          
          {sessionData && (
            <div className="bg-muted p-4 rounded-lg text-left">
              <h4 className="font-semibold mb-2">Subscription Details:</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Plan:</span>
                  <span>{sessionData.planName}</span>
                </div>
                <div className="flex justify-between">
                  <span>Amount:</span>
                  <span>${sessionData.amount}</span>
                </div>
                <div className="flex justify-between">
                  <span>Billing Cycle:</span>
                  <span>{sessionData.billingCycle}</span>
                </div>
                <div className="flex justify-between">
                  <span>Next Billing:</span>
                  <span>{new Date(sessionData.nextBilling).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-2">
            <Button 
              onClick={() => router.push('/billing')} 
              className="w-full"
            >
              View Billing Dashboard
            </Button>
            <Button 
              variant="outline" 
              onClick={() => router.push('/dashboard')} 
              className="w-full"
            >
              Go to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
