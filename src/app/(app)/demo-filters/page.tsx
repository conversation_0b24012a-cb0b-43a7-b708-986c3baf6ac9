"use client";

import React, { useState } from 'react';
import { EnhancedFilters, type FilterSection, type FilterCriteria } from "@/components/ui/enhanced-filters";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Building, 
  MapPin, 
  DollarSign, 
  Users, 
  AlertTriangle, 
  TrendingUp,
  Flag,
  Target,
  User,
  Calendar
} from "lucide-react";

export default function DemoFiltersPage() {
  const [searchValue, setSearchValue] = useState('');
  const [exhibitionCriteria, setExhibitionCriteria] = useState<FilterCriteria>({
    status: [],
    industries: [],
    locations: [],
    riskLevel: [],
    budgetRange: { min: null, max: null },
    leadGoalRange: { min: null, max: null }
  });

  const [taskCriteria, setTaskCriteria] = useState<FilterCriteria>({
    statuses: [],
    priorities: [],
    assignees: [],
    budgetRange: { min: null, max: null }
  });

  const exhibitionSections: FilterSection[] = [
    {
      key: 'status',
      label: 'Status',
      icon: <TrendingUp className="h-4 w-4" />,
      type: 'badge',
      options: [
        { value: 'upcoming', label: 'Upcoming', color: 'bg-blue-100 text-blue-700' },
        { value: 'active', label: 'Active', color: 'bg-green-100 text-green-700' },
        { value: 'completed', label: 'Completed', color: 'bg-gray-100 text-gray-700' },
        { value: 'at-risk', label: 'At Risk', color: 'bg-red-100 text-red-700' }
      ]
    },
    {
      key: 'industries',
      label: 'Industries',
      icon: <Building className="h-4 w-4" />,
      type: 'badge',
      options: [
        { value: 'technology', label: 'Technology' },
        { value: 'healthcare', label: 'Healthcare' },
        { value: 'finance', label: 'Finance' },
        { value: 'manufacturing', label: 'Manufacturing' },
        { value: 'retail', label: 'Retail' },
        { value: 'automotive', label: 'Automotive' }
      ]
    },
    {
      key: 'locations',
      label: 'Locations',
      icon: <MapPin className="h-4 w-4" />,
      type: 'badge',
      options: [
        { value: 'new-york', label: 'New York' },
        { value: 'london', label: 'London' },
        { value: 'tokyo', label: 'Tokyo' },
        { value: 'berlin', label: 'Berlin' },
        { value: 'singapore', label: 'Singapore' }
      ]
    },
    {
      key: 'riskLevel',
      label: 'Risk Level',
      icon: <AlertTriangle className="h-4 w-4" />,
      type: 'badge',
      options: [
        { value: 'low', label: 'Low Risk', color: 'bg-green-100 text-green-700' },
        { value: 'medium', label: 'Medium Risk', color: 'bg-yellow-100 text-yellow-700' },
        { value: 'high', label: 'High Risk', color: 'bg-red-100 text-red-700' }
      ]
    },
    {
      key: 'budgetRange',
      label: 'Budget Range ($)',
      icon: <DollarSign className="h-4 w-4" />,
      type: 'range'
    },
    {
      key: 'leadGoalRange',
      label: 'Lead Goal Range',
      icon: <Users className="h-4 w-4" />,
      type: 'range'
    }
  ];

  const taskSections: FilterSection[] = [
    {
      key: 'statuses',
      label: 'Status',
      icon: <Target className="h-4 w-4" />,
      type: 'checkbox',
      options: [
        { value: 'todo', label: 'To Do' },
        { value: 'in-progress', label: 'In Progress' },
        { value: 'review', label: 'In Review' },
        { value: 'done', label: 'Done' }
      ]
    },
    {
      key: 'priorities',
      label: 'Priority',
      icon: <Flag className="h-4 w-4" />,
      type: 'checkbox',
      options: [
        { value: 'low', label: 'Low' },
        { value: 'medium', label: 'Medium' },
        { value: 'high', label: 'High' },
        { value: 'urgent', label: 'Urgent' }
      ]
    },
    {
      key: 'assignees',
      label: 'Assignee',
      icon: <User className="h-4 w-4" />,
      type: 'checkbox',
      options: [
        { value: 'current-user', label: 'My Tasks' },
        { value: 'john-doe', label: 'John Doe' },
        { value: 'jane-smith', label: 'Jane Smith' },
        { value: 'bob-wilson', label: 'Bob Wilson' }
      ]
    },
    {
      key: 'budgetRange',
      label: 'Budget Range ($)',
      icon: <DollarSign className="h-4 w-4" />,
      type: 'range'
    }
  ];

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Enhanced Filters Demo</h1>
        <p className="text-muted-foreground">
          Demonstration of improved filter components with better UX and no scrolling issues.
        </p>
      </div>

      {/* Exhibition Filters - Compact Mode */}
      <Card>
        <CardHeader>
          <CardTitle>Exhibition Filters (Compact Mode)</CardTitle>
          <CardDescription>
            Clean, modern filters without scrolling issues. Uses popover for advanced options.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <EnhancedFilters
            searchValue={searchValue}
            onSearchChange={setSearchValue}
            searchPlaceholder="Search exhibitions by name, location, or industry..."
            sections={exhibitionSections}
            criteria={exhibitionCriteria}
            onCriteriaChange={setExhibitionCriteria}
            compact={true}
          />
          
          {/* Show active filters */}
          <div className="mt-4 space-y-2">
            <h4 className="text-sm font-medium">Active Filters:</h4>
            <div className="flex flex-wrap gap-2">
              {searchValue && (
                <Badge variant="secondary">Search: {searchValue}</Badge>
              )}
              {Object.entries(exhibitionCriteria).map(([key, value]) => {
                if (Array.isArray(value) && value.length > 0) {
                  return value.map(v => (
                    <Badge key={`${key}-${v}`} variant="secondary">
                      {key}: {v}
                    </Badge>
                  ));
                } else if (typeof value === 'object' && value !== null && (value.min || value.max)) {
                  return (
                    <Badge key={key} variant="secondary">
                      {key}: {value.min || 0} - {value.max || '∞'}
                    </Badge>
                  );
                }
                return null;
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Task Filters - Expanded Mode */}
      <Card>
        <CardHeader>
          <CardTitle>Task Filters (Expanded Mode)</CardTitle>
          <CardDescription>
            Full filter panel with collapsible sections and grid layouts for better organization.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <EnhancedFilters
            searchValue={searchValue}
            onSearchChange={setSearchValue}
            searchPlaceholder="Search tasks, tags, assignees..."
            sections={taskSections}
            criteria={taskCriteria}
            onCriteriaChange={setTaskCriteria}
            compact={false}
          />
          
          {/* Show active filters */}
          <div className="mt-4 space-y-2">
            <h4 className="text-sm font-medium">Active Filters:</h4>
            <div className="flex flex-wrap gap-2">
              {searchValue && (
                <Badge variant="secondary">Search: {searchValue}</Badge>
              )}
              {Object.entries(taskCriteria).map(([key, value]) => {
                if (Array.isArray(value) && value.length > 0) {
                  return value.map(v => (
                    <Badge key={`${key}-${v}`} variant="secondary">
                      {key}: {v}
                    </Badge>
                  ));
                } else if (typeof value === 'object' && value !== null && (value.min || value.max)) {
                  return (
                    <Badge key={key} variant="secondary">
                      {key}: {value.min || 0} - {value.max || '∞'}
                    </Badge>
                  );
                }
                return null;
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Benefits */}
      <Card>
        <CardHeader>
          <CardTitle>Improvements Made</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium text-green-600">✅ Fixed Issues</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Removed ugly scrollbars</li>
                <li>• Better visual hierarchy</li>
                <li>• Consistent spacing and layout</li>
                <li>• Improved hover states</li>
                <li>• Grid-based organization</li>
                <li>• Responsive design</li>
                <li>• Clear visual feedback</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-blue-600">🚀 New Features</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Compact and expanded modes</li>
                <li>• Reusable component</li>
                <li>• Better filter organization</li>
                <li>• Smooth animations</li>
                <li>• Consistent styling</li>
                <li>• Easy to customize</li>
                <li>• Type-safe implementation</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
