"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

export default function ExhibitionsAddPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the onboarding page
    router.replace('/exhibitions/onboarding');
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
        <p className="text-muted-foreground">Redirecting to exhibition onboarding...</p>
      </div>
    </div>
  );
}
