"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  MessageSquare, 
  Palette, 
  FileText, 
  Video,
  Plus,
  Users,
  Activity,
  Clock,
  TrendingUp,
  ArrowLeft
} from "lucide-react";
import Link from 'next/link';
import { useParams } from 'next/navigation';
import ChatChannelsTable from './components/ChatChannelsTable';
import ChatMessagesTable from './components/ChatMessagesTable';
import WhiteboardsTable from './components/WhiteboardsTable';
import DocumentsTable from './components/DocumentsTable';
import VideoConferencesTable from './components/VideoConferencesTable';
import CollaborationSessionsTable from './components/CollaborationSessionsTable';
import type { ExhibitionChatChannel, ExhibitionWhiteboard, ExhibitionDocument, ExhibitionVideoConference, ExhibitionCollaborationSession } from '@/types/firestore';

interface CollaborationMetrics {
  totalChannels: number;
  activeChannels: number;
  totalMessages: number;
  totalWhiteboards: number;
  totalDocuments: number;
  totalConferences: number;
  activeParticipants: number;
}

export default function CollaborationPage() {
  const params = useParams();
  const exhibitionId = params.id as string;
  const [activeTab, setActiveTab] = useState('chat');
  const [selectedChannel, setSelectedChannel] = useState<ExhibitionChatChannel | null>(null);
  
  // Mock metrics - in production, these would come from real data
  const metrics: CollaborationMetrics = {
    totalChannels: 8,
    activeChannels: 5,
    totalMessages: 247,
    totalWhiteboards: 12,
    totalDocuments: 18,
    totalConferences: 6,
    activeParticipants: 23,
  };

  const handleChannelSelect = (channel: ExhibitionChatChannel) => {
    setSelectedChannel(channel);
  };

  const handleChannelEdit = (channel: ExhibitionChatChannel) => {
    console.log('Edit channel:', channel);
  };

  const handleChannelDelete = (channelId: string) => {
    console.log('Delete channel:', channelId);
  };

  const handleWhiteboardOpen = (whiteboard: ExhibitionWhiteboard) => {
    console.log('Open whiteboard:', whiteboard);
  };

  const handleWhiteboardEdit = (whiteboard: ExhibitionWhiteboard) => {
    console.log('Edit whiteboard:', whiteboard);
  };

  const handleWhiteboardDelete = (whiteboardId: string) => {
    console.log('Delete whiteboard:', whiteboardId);
  };

  const handleDocumentOpen = (document: ExhibitionDocument) => {
    console.log('Open document:', document);
  };

  const handleDocumentEdit = (document: ExhibitionDocument) => {
    console.log('Edit document:', document);
  };

  const handleDocumentDelete = (documentId: string) => {
    console.log('Delete document:', documentId);
  };

  const handleConferenceJoin = (conference: ExhibitionVideoConference) => {
    console.log('Join conference:', conference);
  };

  const handleConferenceEdit = (conference: ExhibitionVideoConference) => {
    console.log('Edit conference:', conference);
  };

  const handleConferenceDelete = (conferenceId: string) => {
    console.log('Delete conference:', conferenceId);
  };

  const handleSessionJoin = (session: ExhibitionCollaborationSession) => {
    console.log('Join session:', session);
  };

  const handleSessionEdit = (session: ExhibitionCollaborationSession) => {
    console.log('Edit session:', session);
  };

  const handleSessionDelete = (sessionId: string) => {
    console.log('Delete session:', sessionId);
  };

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" asChild>
            <Link href={`/exhibitions/${exhibitionId}`}>
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight font-headline flex items-center">
              <Users className="mr-3 h-8 w-8 text-primary" />
              Collaboration Hub
            </h1>
            <p className="text-muted-foreground">
              Real-time collaboration tools for your exhibition team
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <MessageSquare className="mr-2 h-4 w-4" />
            New Channel
          </Button>
          <Button variant="outline">
            <Video className="mr-2 h-4 w-4" />
            Schedule Meeting
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Create
          </Button>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Channels</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeChannels}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.totalChannels} total channels
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Messages Today</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalMessages}</div>
            <p className="text-xs text-muted-foreground">
              +12% from yesterday
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Participants</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeParticipants}</div>
            <p className="text-xs text-muted-foreground">
              Online now
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Collaboration Score</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">94%</div>
            <p className="text-xs text-muted-foreground">
              Team engagement
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full max-w-3xl grid-cols-5">
          <TabsTrigger value="chat">
            <MessageSquare className="mr-2 h-4 w-4" />
            Chat
          </TabsTrigger>
          <TabsTrigger value="whiteboards">
            <Palette className="mr-2 h-4 w-4" />
            Whiteboards
          </TabsTrigger>
          <TabsTrigger value="documents">
            <FileText className="mr-2 h-4 w-4" />
            Documents
          </TabsTrigger>
          <TabsTrigger value="meetings">
            <Video className="mr-2 h-4 w-4" />
            Meetings
          </TabsTrigger>
          <TabsTrigger value="sessions">
            <Users className="mr-2 h-4 w-4" />
            Sessions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="chat" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-1">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Chat Channels
                  <Button size="sm" variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    New
                  </Button>
                </CardTitle>
                <CardDescription>
                  {metrics.totalChannels} channels, {metrics.activeChannels} active
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ChatChannelsTable
                  exhibitionId={exhibitionId}
                  onChannelSelect={handleChannelSelect}
                  onChannelEdit={handleChannelEdit}
                  onChannelDelete={handleChannelDelete}
                />
              </CardContent>
            </Card>

            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>
                  {selectedChannel ? `#${selectedChannel.name}` : 'All Messages'}
                </CardTitle>
                <CardDescription>
                  {selectedChannel 
                    ? selectedChannel.description || 'Channel messages'
                    : 'Messages from all channels'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ChatMessagesTable
                  exhibitionId={exhibitionId}
                  channelId={selectedChannel?.id}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="whiteboards" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Whiteboards
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  New Whiteboard
                </Button>
              </CardTitle>
              <CardDescription>
                {metrics.totalWhiteboards} whiteboards for visual collaboration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <WhiteboardsTable
                exhibitionId={exhibitionId}
                onWhiteboardOpen={handleWhiteboardOpen}
                onWhiteboardEdit={handleWhiteboardEdit}
                onWhiteboardDelete={handleWhiteboardDelete}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Documents
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  New Document
                </Button>
              </CardTitle>
              <CardDescription>
                {metrics.totalDocuments} collaborative documents
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DocumentsTable
                exhibitionId={exhibitionId}
                onDocumentOpen={handleDocumentOpen}
                onDocumentEdit={handleDocumentEdit}
                onDocumentDelete={handleDocumentDelete}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="meetings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Video Conferences
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Schedule Meeting
                </Button>
              </CardTitle>
              <CardDescription>
                {metrics.totalConferences} video conferences and meetings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <VideoConferencesTable
                exhibitionId={exhibitionId}
                onConferenceJoin={handleConferenceJoin}
                onConferenceEdit={handleConferenceEdit}
                onConferenceDelete={handleConferenceDelete}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sessions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Collaboration Sessions
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  New Session
                </Button>
              </CardTitle>
              <CardDescription>
                Active and completed collaboration sessions across all tools
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CollaborationSessionsTable
                exhibitionId={exhibitionId}
                onSessionJoin={handleSessionJoin}
                onSessionEdit={handleSessionEdit}
                onSessionDelete={handleSessionDelete}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
