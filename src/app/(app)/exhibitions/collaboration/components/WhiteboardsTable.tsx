"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Palette, 
  Users, 
  Lock, 
  Globe, 
  Eye,
  Edit3,
  Trash2,
  Copy,
  Download,
  Share2,
  Settings,
  Clock,
  Activity,
  Layers
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format, formatDistanceToNow } from 'date-fns';
import type { ExhibitionWhiteboard } from '@/types/firestore';
import { getWhiteboardsAction } from '../actions';

interface WhiteboardsTableProps {
  exhibitionId: string;
  className?: string;
  onWhiteboardOpen?: (whiteboard: ExhibitionWhiteboard) => void;
  onWhiteboardEdit?: (whiteboard: ExhibitionWhiteboard) => void;
  onWhiteboardDelete?: (whiteboardId: string) => void;
}

interface EnhancedWhiteboard extends ExhibitionWhiteboard {
  elementCount: number;
  collaboratorCount: number;
  lastEditedBy?: string;
  lastEditedAt?: Date;
  isActive: boolean;
  viewCount: number;
}

export default function WhiteboardsTable({ 
  exhibitionId, 
  className, 
  onWhiteboardOpen, 
  onWhiteboardEdit, 
  onWhiteboardDelete 
}: WhiteboardsTableProps) {
  const [whiteboards, setWhiteboards] = useState<EnhancedWhiteboard[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchWhiteboards = async () => {
      try {
        setIsLoading(true);
        
        const whiteboardsData = await getWhiteboardsAction(exhibitionId);
        
        // Enhance whiteboards with additional data
        const enhancedWhiteboards: EnhancedWhiteboard[] = whiteboardsData.map(whiteboard => ({
          ...whiteboard,
          elementCount: whiteboard.elements?.length || 0,
          collaboratorCount: whiteboard.collaborators?.length || 0,
          lastEditedBy: whiteboard.collaborators?.[0] || whiteboard.createdBy,
          lastEditedAt: new Date(whiteboard.updatedAt),
          isActive: Math.random() > 0.3, // Mock active status
          viewCount: Math.floor(Math.random() * 50) + 1, // Mock view count
        }));

        setWhiteboards(enhancedWhiteboards);
      } catch (error) {
        console.error('Error fetching whiteboards:', error);
        toast({
          title: "Error",
          description: "Failed to load whiteboards. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchWhiteboards();
  }, [exhibitionId, toast]);

  const columns: AdvancedTableColumn<EnhancedWhiteboard>[] = [
    {
      accessorKey: 'name',
      title: 'Whiteboard',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const whiteboard = row.original;
        
        return (
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <Palette className="h-4 w-4 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2">
                <div className="font-medium">{whiteboard.name}</div>
                {whiteboard.isActive && (
                  <Badge variant="default" className="h-5 px-1.5 text-xs">
                    Live
                  </Badge>
                )}
              </div>
              {whiteboard.description && (
                <div className="text-xs text-muted-foreground truncate">
                  {whiteboard.description}
                </div>
              )}
              <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Layers className="h-3 w-3" />
                  {whiteboard.elementCount} elements
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  {whiteboard.viewCount} views
                </div>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'isPublic',
      title: 'Visibility',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const isPublic = row.original.isPublic;
        return (
          <Badge variant={isPublic ? 'default' : 'secondary'} className="flex items-center w-fit">
            {isPublic ? (
              <>
                <Globe className="h-3 w-3 mr-1" />
                Public
              </>
            ) : (
              <>
                <Lock className="h-3 w-3 mr-1" />
                Private
              </>
            )}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'collaborators',
      title: 'Collaborators',
      sortable: true,
      cell: ({ row }) => {
        const whiteboard = row.original;
        const collaborators = whiteboard.collaborators || [];
        
        return (
          <div className="flex items-center space-x-2">
            <div className="flex -space-x-2">
              {collaborators.slice(0, 3).map((collaboratorId, index) => (
                <Avatar key={collaboratorId} className="h-6 w-6 border-2 border-background">
                  <AvatarFallback className="text-xs">
                    {collaboratorId.slice(-2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              ))}
              {collaborators.length > 3 && (
                <div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                  <span className="text-xs font-medium">+{collaborators.length - 3}</span>
                </div>
              )}
            </div>
            <span className="text-sm text-muted-foreground">
              {whiteboard.collaboratorCount} member{whiteboard.collaboratorCount !== 1 ? 's' : ''}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'elementCount',
      title: 'Elements',
      sortable: true,
      cell: ({ row }) => {
        const count = row.original.elementCount;
        return (
          <div className="flex items-center">
            <Layers className="h-4 w-4 mr-2 text-muted-foreground" />
            <span className="text-sm">{count}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'lastEditedAt',
      title: 'Last Edited',
      sortable: true,
      cell: ({ row }) => {
        const lastEdited = row.original.lastEditedAt;
        const lastEditedBy = row.original.lastEditedBy;
        
        if (!lastEdited) {
          return <span className="text-sm text-muted-foreground">Never</span>;
        }
        
        return (
          <div className="text-sm">
            <div className="flex items-center">
              <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
              {formatDistanceToNow(lastEdited, { addSuffix: true })}
            </div>
            {lastEditedBy && (
              <div className="text-xs text-muted-foreground">
                by {lastEditedBy.slice(-8)}
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      title: 'Created',
      sortable: true,
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        if (!createdAt) return null;
        
        const date = new Date(createdAt);
        return (
          <div className="text-sm text-muted-foreground">
            {format(date, 'MMM d, yyyy')}
          </div>
        );
      },
    },
  ];

  const rowActions = (row: EnhancedWhiteboard): AdvancedTableRowAction<EnhancedWhiteboard>[] => {
    const actions: AdvancedTableRowAction<EnhancedWhiteboard>[] = [
      {
        type: 'view',
        label: 'Open Whiteboard',
        onClick: () => onWhiteboardOpen?.(row),
        icon: <Eye className="h-4 w-4" />,
      },
    ];

    if (onWhiteboardEdit) {
      actions.push({
        type: 'edit',
        label: 'Edit Whiteboard',
        onClick: () => onWhiteboardEdit(row),
        icon: <Edit3 className="h-4 w-4" />,
      });
    }

    actions.push({
      type: 'copy',
      label: 'Duplicate',
      onClick: () => {
        toast({
          title: "Duplicate Whiteboard",
          description: `Creating a copy of ${row.name}...`,
        });
      },
      icon: <Copy className="h-4 w-4" />,
    });

    actions.push({
      type: 'share',
      label: 'Share',
      onClick: () => {
        const shareUrl = `${window.location.origin}/exhibitions/${exhibitionId}/collaboration/whiteboards/${row.id}`;
        navigator.clipboard.writeText(shareUrl);
        toast({
          title: "Link Copied",
          description: "Whiteboard link copied to clipboard",
        });
      },
      icon: <Share2 className="h-4 w-4" />,
    });

    actions.push({
      type: 'download',
      label: 'Export',
      onClick: () => {
        toast({
          title: "Export Whiteboard",
          description: `Exporting ${row.name} as image...`,
        });
      },
      icon: <Download className="h-4 w-4" />,
    });

    actions.push({
      type: 'custom',
      label: 'Settings',
      onClick: () => {
        toast({
          title: "Whiteboard Settings",
          description: `Opening settings for ${row.name}...`,
        });
      },
      icon: <Settings className="h-4 w-4" />,
    });

    if (onWhiteboardDelete) {
      actions.push({
        type: 'delete',
        label: 'Delete',
        onClick: () => onWhiteboardDelete(row.id!),
        icon: <Trash2 className="h-4 w-4" />,
        variant: 'destructive',
      });
    }

    return actions;
  };

  const filters = [
    {
      id: 'isPublic',
      label: 'Visibility',
      type: 'select' as const,
      options: [
        { label: 'Public', value: 'true' },
        { label: 'Private', value: 'false' },
      ],
    },
    {
      id: 'isActive',
      label: 'Status',
      type: 'select' as const,
      options: [
        { label: 'Active', value: 'true' },
        { label: 'Inactive', value: 'false' },
      ],
    },
  ];

  const bulkActions = [
    {
      id: 'share',
      label: 'Share Selected',
      icon: <Share2 className="h-4 w-4" />,
      onClick: (selectedRows: EnhancedWhiteboard[]) => {
        toast({
          title: "Share Whiteboards",
          description: `Sharing ${selectedRows.length} whiteboard(s)...`,
        });
      },
    },
    {
      id: 'export',
      label: 'Export Selected',
      icon: <Download className="h-4 w-4" />,
      onClick: (selectedRows: EnhancedWhiteboard[]) => {
        toast({
          title: "Export Whiteboards",
          description: `Exporting ${selectedRows.length} whiteboard(s)...`,
        });
      },
    },
    {
      id: 'delete',
      label: 'Delete Selected',
      icon: <Trash2 className="h-4 w-4" />,
      variant: 'destructive' as const,
      onClick: (selectedRows: EnhancedWhiteboard[]) => {
        toast({
          title: "Delete Whiteboards",
          description: `Deleting ${selectedRows.length} whiteboard(s)...`,
        });
      },
    },
  ];

  return (
    <AdvancedDataTable
      data={whiteboards}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      bulkActions={bulkActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName={`whiteboards-${exhibitionId}`}
      searchPlaceholder="Search whiteboards..."
      emptyMessage="No whiteboards found. Create a new whiteboard to start collaborating visually."
      loading={isLoading}
      className={className}
      variant="default"
      onRowClick={onWhiteboardOpen}
    />
  );
}
