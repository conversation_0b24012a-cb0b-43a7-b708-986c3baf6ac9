"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  FileText, 
  File, 
  Edit, 
  Users, 
  Lock, 
  Globe, 
  Eye,
  Edit2,
  Trash2,
  Copy,
  Download,
  Share2,
  Settings,
  Clock,
  Activity,
  FileCheck,
  AlertCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format, formatDistanceToNow } from 'date-fns';
import type { ExhibitionDocument } from '@/types/firestore';
import { getDocumentsAction } from '../actions';

interface DocumentsTableProps {
  exhibitionId: string;
  className?: string;
  onDocumentOpen?: (document: ExhibitionDocument) => void;
  onDocumentEdit?: (document: ExhibitionDocument) => void;
  onDocumentDelete?: (documentId: string) => void;
}

interface EnhancedDocument extends ExhibitionDocument {
  wordCount: number;
  collaboratorCount: number;
  lastEditedBy?: string;
  lastEditedAt?: Date;
  isActive: boolean;
  viewCount: number;
  commentCount: number;
  hasUnresolvedComments: boolean;
}

export default function DocumentsTable({ 
  exhibitionId, 
  className, 
  onDocumentOpen, 
  onDocumentEdit, 
  onDocumentDelete 
}: DocumentsTableProps) {
  const [documents, setDocuments] = useState<EnhancedDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        setIsLoading(true);
        
        const documentsData = await getDocumentsAction(exhibitionId);
        
        // Enhance documents with additional data
        const enhancedDocuments: EnhancedDocument[] = documentsData.map(document => ({
          ...document,
          wordCount: document.content ? document.content.split(' ').length : 0,
          collaboratorCount: document.collaborators?.length || 0,
          lastEditedBy: document.collaborators?.[0] || document.createdBy,
          lastEditedAt: new Date(document.updatedAt),
          isActive: Math.random() > 0.4, // Mock active status
          viewCount: Math.floor(Math.random() * 100) + 1, // Mock view count
          commentCount: Math.floor(Math.random() * 10), // Mock comment count
          hasUnresolvedComments: Math.random() > 0.7, // Mock unresolved comments
        }));

        setDocuments(enhancedDocuments);
      } catch (error) {
        console.error('Error fetching documents:', error);
        toast({
          title: "Error",
          description: "Failed to load documents. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDocuments();
  }, [exhibitionId, toast]);

  const columns: AdvancedTableColumn<EnhancedDocument>[] = [
    {
      accessorKey: 'title',
      title: 'Document',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const document = row.original;
        const typeIcons = {
          text: FileText,
          markdown: File,
          rich_text: Edit,
        };
        const IconComponent = typeIcons[document.type] || FileText;
        
        return (
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <IconComponent className="h-4 w-4 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2">
                <div className="font-medium">{document.title}</div>
                {document.isActive && (
                  <Badge variant="default" className="h-5 px-1.5 text-xs">
                    Live
                  </Badge>
                )}
                {document.hasUnresolvedComments && (
                  <AlertCircle className="h-4 w-4 text-orange-500" />
                )}
              </div>
              <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  {document.wordCount} words
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  {document.viewCount} views
                </div>
                {document.commentCount > 0 && (
                  <div className="flex items-center gap-1">
                    <Activity className="h-3 w-3" />
                    {document.commentCount} comments
                  </div>
                )}
              </div>
              {document.tags && document.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-1">
                  {document.tags.slice(0, 3).map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {document.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{document.tags.length - 3} more
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'type',
      title: 'Type',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const type = row.original.type;
        const typeConfig = {
          text: { variant: 'default' as const, label: 'Plain Text', icon: FileText },
          markdown: { variant: 'secondary' as const, label: 'Markdown', icon: File },
          rich_text: { variant: 'outline' as const, label: 'Rich Text', icon: Edit },
        };
        const config = typeConfig[type];
        const IconComponent = config.icon;
        
        return (
          <Badge variant={config.variant} className="flex items-center w-fit">
            <IconComponent className="h-3 w-3 mr-1" />
            {config.label}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'permissions',
      title: 'Access',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const document = row.original;
        const isPublic = document.isPublic;
        const permissions = document.permissions;
        
        return (
          <div className="space-y-1">
            <Badge variant={isPublic ? 'default' : 'secondary'} className="flex items-center w-fit">
              {isPublic ? (
                <>
                  <Globe className="h-3 w-3 mr-1" />
                  Public
                </>
              ) : (
                <>
                  <Lock className="h-3 w-3 mr-1" />
                  Private
                </>
              )}
            </Badge>
            {permissions && (
              <Badge variant="outline" className="text-xs">
                {permissions === 'view' ? 'View Only' : permissions === 'comment' ? 'Can Comment' : 'Can Edit'}
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'collaborators',
      title: 'Collaborators',
      sortable: true,
      cell: ({ row }) => {
        const document = row.original;
        const collaborators = document.collaborators || [];
        
        return (
          <div className="flex items-center space-x-2">
            <div className="flex -space-x-2">
              {collaborators.slice(0, 3).map((collaboratorId, index) => (
                <Avatar key={collaboratorId} className="h-6 w-6 border-2 border-background">
                  <AvatarFallback className="text-xs">
                    {collaboratorId.slice(-2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              ))}
              {collaborators.length > 3 && (
                <div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                  <span className="text-xs font-medium">+{collaborators.length - 3}</span>
                </div>
              )}
            </div>
            <span className="text-sm text-muted-foreground">
              {document.collaboratorCount} member{document.collaboratorCount !== 1 ? 's' : ''}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'lastEditedAt',
      title: 'Last Edited',
      sortable: true,
      cell: ({ row }) => {
        const lastEdited = row.original.lastEditedAt;
        const lastEditedBy = row.original.lastEditedBy;
        
        if (!lastEdited) {
          return <span className="text-sm text-muted-foreground">Never</span>;
        }
        
        return (
          <div className="text-sm">
            <div className="flex items-center">
              <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
              {formatDistanceToNow(lastEdited, { addSuffix: true })}
            </div>
            {lastEditedBy && (
              <div className="text-xs text-muted-foreground">
                by {lastEditedBy.slice(-8)}
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'version',
      title: 'Version',
      sortable: true,
      cell: ({ row }) => {
        const version = row.original.version || 1;
        return (
          <div className="flex items-center">
            <FileCheck className="h-4 w-4 mr-2 text-muted-foreground" />
            <span className="text-sm">v{version}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      title: 'Created',
      sortable: true,
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        if (!createdAt) return null;
        
        const date = new Date(createdAt);
        return (
          <div className="text-sm text-muted-foreground">
            {format(date, 'MMM d, yyyy')}
          </div>
        );
      },
    },
  ];

  const rowActions = (row: EnhancedDocument): AdvancedTableRowAction<EnhancedDocument>[] => {
    const actions: AdvancedTableRowAction<EnhancedDocument>[] = [
      {
        type: 'view',
        label: 'Open Document',
        onClick: () => onDocumentOpen?.(row),
        icon: <Eye className="h-4 w-4" />,
      },
    ];

    if (onDocumentEdit) {
      actions.push({
        type: 'edit',
        label: 'Edit Document',
        onClick: () => onDocumentEdit(row),
        icon: <Edit2 className="h-4 w-4" />,
      });
    }

    actions.push({
      type: 'copy',
      label: 'Duplicate',
      onClick: () => {
        toast({
          title: "Duplicate Document",
          description: `Creating a copy of ${row.title}...`,
        });
      },
      icon: <Copy className="h-4 w-4" />,
    });

    actions.push({
      type: 'share',
      label: 'Share',
      onClick: () => {
        const shareUrl = `${window.location.origin}/exhibitions/${exhibitionId}/collaboration/documents/${row.id}`;
        navigator.clipboard.writeText(shareUrl);
        toast({
          title: "Link Copied",
          description: "Document link copied to clipboard",
        });
      },
      icon: <Share2 className="h-4 w-4" />,
    });

    actions.push({
      type: 'download',
      label: 'Export',
      onClick: () => {
        toast({
          title: "Export Document",
          description: `Exporting ${row.title}...`,
        });
      },
      icon: <Download className="h-4 w-4" />,
    });

    actions.push({
      type: 'custom',
      label: 'Version History',
      onClick: () => {
        toast({
          title: "Version History",
          description: `Viewing version history for ${row.title}...`,
        });
      },
      icon: <FileCheck className="h-4 w-4" />,
    });

    actions.push({
      type: 'custom',
      label: 'Settings',
      onClick: () => {
        toast({
          title: "Document Settings",
          description: `Opening settings for ${row.title}...`,
        });
      },
      icon: <Settings className="h-4 w-4" />,
    });

    if (onDocumentDelete) {
      actions.push({
        type: 'delete',
        label: 'Delete',
        onClick: () => onDocumentDelete(row.id!),
        icon: <Trash2 className="h-4 w-4" />,
        variant: 'destructive',
      });
    }

    return actions;
  };

  const filters = [
    {
      id: 'type',
      label: 'Document Type',
      type: 'select' as const,
      options: [
        { label: 'Plain Text', value: 'text' },
        { label: 'Markdown', value: 'markdown' },
        { label: 'Rich Text', value: 'rich_text' },
      ],
    },
    {
      id: 'permissions',
      label: 'Permissions',
      type: 'select' as const,
      options: [
        { label: 'View Only', value: 'view' },
        { label: 'Can Comment', value: 'comment' },
        { label: 'Can Edit', value: 'edit' },
      ],
    },
    {
      id: 'isPublic',
      label: 'Visibility',
      type: 'select' as const,
      options: [
        { label: 'Public', value: 'true' },
        { label: 'Private', value: 'false' },
      ],
    },
  ];

  const bulkActions = [
    {
      id: 'share',
      label: 'Share Selected',
      icon: <Share2 className="h-4 w-4" />,
      onClick: (selectedRows: EnhancedDocument[]) => {
        toast({
          title: "Share Documents",
          description: `Sharing ${selectedRows.length} document(s)...`,
        });
      },
    },
    {
      id: 'export',
      label: 'Export Selected',
      icon: <Download className="h-4 w-4" />,
      onClick: (selectedRows: EnhancedDocument[]) => {
        toast({
          title: "Export Documents",
          description: `Exporting ${selectedRows.length} document(s)...`,
        });
      },
    },
    {
      id: 'delete',
      label: 'Delete Selected',
      icon: <Trash2 className="h-4 w-4" />,
      variant: 'destructive' as const,
      onClick: (selectedRows: EnhancedDocument[]) => {
        toast({
          title: "Delete Documents",
          description: `Deleting ${selectedRows.length} document(s)...`,
        });
      },
    },
  ];

  return (
    <AdvancedDataTable
      data={documents}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      bulkActions={bulkActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName={`documents-${exhibitionId}`}
      searchPlaceholder="Search documents..."
      emptyMessage="No documents found. Create a new document to start collaborating."
      loading={isLoading}
      className={className}
      variant="default"
      onRowClick={onDocumentOpen}
    />
  );
}
