"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Video, 
  Users, 
  Calendar, 
  Clock, 
  Play,
  Pause,
  Square,
  Eye,
  Edit2,
  Trash2,
  Copy,
  Share2,
  Settings,
  Phone,
  PhoneOff,
  Mic,
  MicOff,
  Monitor,
  Activity
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format, formatDistanceToNow, addMinutes } from 'date-fns';
import type { ExhibitionVideoConference } from '@/types/firestore';
import { getVideoConferencesAction } from '../actions';

interface VideoConferencesTableProps {
  exhibitionId: string;
  className?: string;
  onConferenceJoin?: (conference: ExhibitionVideoConference) => void;
  onConferenceEdit?: (conference: ExhibitionVideoConference) => void;
  onConferenceDelete?: (conferenceId: string) => void;
}

interface EnhancedVideoConference extends ExhibitionVideoConference {
  participantCount: number;
  duration: number; // in minutes
  isLive: boolean;
  hasRecording: boolean;
  recordingDuration?: number;
  chatMessageCount: number;
  screenShareActive: boolean;
}

export default function VideoConferencesTable({ 
  exhibitionId, 
  className, 
  onConferenceJoin, 
  onConferenceEdit, 
  onConferenceDelete 
}: VideoConferencesTableProps) {
  const [conferences, setConferences] = useState<EnhancedVideoConference[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchConferences = async () => {
      try {
        setIsLoading(true);
        
        const conferencesData = await getVideoConferencesAction(exhibitionId);
        
        // Enhance conferences with additional data
        const enhancedConferences: EnhancedVideoConference[] = conferencesData.map(conference => {
          const startTime = new Date(conference.scheduledStartTime);
          const endTime = new Date(conference.scheduledEndTime);
          const duration = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));
          
          return {
            ...conference,
            participantCount: conference.participants?.length || 0,
            duration,
            isLive: conference.status === 'in_progress',
            hasRecording: conference.isRecording || Math.random() > 0.5,
            recordingDuration: conference.status === 'completed' ? duration : undefined,
            chatMessageCount: Math.floor(Math.random() * 50),
            screenShareActive: conference.status === 'in_progress' && Math.random() > 0.5,
          };
        });

        setConferences(enhancedConferences);
      } catch (error) {
        console.error('Error fetching video conferences:', error);
        toast({
          title: "Error",
          description: "Failed to load video conferences. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchConferences();
  }, [exhibitionId, toast]);

  const columns: AdvancedTableColumn<EnhancedVideoConference>[] = [
    {
      accessorKey: 'title',
      title: 'Conference',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const conference = row.original;
        
        return (
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <Video className="h-4 w-4 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2">
                <div className="font-medium">{conference.title}</div>
                {conference.isLive && (
                  <Badge variant="destructive" className="h-5 px-1.5 text-xs animate-pulse">
                    LIVE
                  </Badge>
                )}
                {conference.isRecording && (
                  <Badge variant="default" className="h-5 px-1.5 text-xs">
                    REC
                  </Badge>
                )}
              </div>
              {conference.description && (
                <div className="text-xs text-muted-foreground truncate">
                  {conference.description}
                </div>
              )}
              <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {conference.duration}m
                </div>
                {conference.chatMessageCount > 0 && (
                  <div className="flex items-center gap-1">
                    <Activity className="h-3 w-3" />
                    {conference.chatMessageCount} messages
                  </div>
                )}
                {conference.screenShareActive && (
                  <div className="flex items-center gap-1">
                    <Monitor className="h-3 w-3" />
                    Screen sharing
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      title: 'Status',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const status = row.original.status;
        const statusConfig = {
          scheduled: { variant: 'secondary' as const, label: 'Scheduled', icon: Calendar },
          in_progress: { variant: 'default' as const, label: 'In Progress', icon: Play },
          completed: { variant: 'outline' as const, label: 'Completed', icon: Square },
          cancelled: { variant: 'destructive' as const, label: 'Cancelled', icon: PhoneOff },
        };
        const config = statusConfig[status];
        const IconComponent = config.icon;
        
        return (
          <Badge variant={config.variant} className="flex items-center w-fit">
            <IconComponent className="h-3 w-3 mr-1" />
            {config.label}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'participants',
      title: 'Participants',
      sortable: true,
      cell: ({ row }) => {
        const conference = row.original;
        const participants = conference.participants || [];
        
        return (
          <div className="flex items-center space-x-2">
            <div className="flex -space-x-2">
              {participants.slice(0, 3).map((participantId, index) => (
                <Avatar key={participantId} className="h-6 w-6 border-2 border-background">
                  <AvatarFallback className="text-xs">
                    {participantId.slice(-2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              ))}
              {participants.length > 3 && (
                <div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                  <span className="text-xs font-medium">+{participants.length - 3}</span>
                </div>
              )}
            </div>
            <span className="text-sm text-muted-foreground">
              {conference.participantCount} member{conference.participantCount !== 1 ? 's' : ''}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'scheduledStartTime',
      title: 'Scheduled Time',
      sortable: true,
      cell: ({ row }) => {
        const startTime = new Date(row.original.scheduledStartTime);
        const endTime = new Date(row.original.scheduledEndTime);
        
        return (
          <div className="text-sm">
            <div>{format(startTime, 'MMM d, HH:mm')}</div>
            <div className="text-xs text-muted-foreground">
              to {format(endTime, 'HH:mm')}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'settings',
      title: 'Features',
      sortable: false,
      cell: ({ row }) => {
        const settings = row.original.settings;
        const features = [];
        
        if (settings?.allowScreenShare) features.push('Screen Share');
        if (settings?.allowChat) features.push('Chat');
        if (settings?.requirePassword) features.push('Password');
        if (settings?.waitingRoom) features.push('Waiting Room');
        
        return (
          <div className="flex flex-wrap gap-1">
            {features.slice(0, 2).map((feature, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {feature}
              </Badge>
            ))}
            {features.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{features.length - 2} more
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'hasRecording',
      title: 'Recording',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const conference = row.original;
        if (!conference.hasRecording) {
          return <span className="text-sm text-muted-foreground">None</span>;
        }
        
        return (
          <div className="flex items-center">
            <Video className="h-4 w-4 mr-2 text-muted-foreground" />
            <span className="text-sm">
              {conference.recordingDuration ? `${conference.recordingDuration}m` : 'Available'}
            </span>
          </div>
        );
      },
    },
  ];

  const rowActions = (row: EnhancedVideoConference): AdvancedTableRowAction<EnhancedVideoConference>[] => {
    const actions: AdvancedTableRowAction<EnhancedVideoConference>[] = [];

    if (row.status === 'scheduled' || row.status === 'in_progress') {
      actions.push({
        type: 'custom',
        label: row.status === 'in_progress' ? 'Join Meeting' : 'Start Meeting',
        onClick: () => onConferenceJoin?.(row),
        icon: <Play className="h-4 w-4" />,
        variant: 'default',
      });
    }

    if (row.status === 'completed' && row.hasRecording) {
      actions.push({
        type: 'view',
        label: 'View Recording',
        onClick: () => {
          toast({
            title: "Open Recording",
            description: `Opening recording for ${row.title}...`,
          });
        },
        icon: <Eye className="h-4 w-4" />,
      });
    }

    if (onConferenceEdit && row.status !== 'completed') {
      actions.push({
        type: 'edit',
        label: 'Edit Meeting',
        onClick: () => onConferenceEdit(row),
        icon: <Edit2 className="h-4 w-4" />,
      });
    }

    actions.push({
      type: 'copy',
      label: 'Copy Meeting Link',
      onClick: () => {
        navigator.clipboard.writeText(row.meetingUrl);
        toast({
          title: "Link Copied",
          description: "Meeting link copied to clipboard",
        });
      },
      icon: <Copy className="h-4 w-4" />,
    });

    actions.push({
      type: 'share',
      label: 'Share Meeting',
      onClick: () => {
        toast({
          title: "Share Meeting",
          description: `Sharing ${row.title}...`,
        });
      },
      icon: <Share2 className="h-4 w-4" />,
    });

    actions.push({
      type: 'custom',
      label: 'Meeting Settings',
      onClick: () => {
        toast({
          title: "Meeting Settings",
          description: `Opening settings for ${row.title}...`,
        });
      },
      icon: <Settings className="h-4 w-4" />,
    });

    if (onConferenceDelete && row.status !== 'in_progress') {
      actions.push({
        type: 'delete',
        label: 'Delete Meeting',
        onClick: () => onConferenceDelete(row.id!),
        icon: <Trash2 className="h-4 w-4" />,
        variant: 'destructive',
      });
    }

    return actions;
  };

  const filters = [
    {
      id: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { label: 'Scheduled', value: 'scheduled' },
        { label: 'In Progress', value: 'in_progress' },
        { label: 'Completed', value: 'completed' },
        { label: 'Cancelled', value: 'cancelled' },
      ],
    },
    {
      id: 'hasRecording',
      label: 'Recording',
      type: 'select' as const,
      options: [
        { label: 'Has Recording', value: 'true' },
        { label: 'No Recording', value: 'false' },
      ],
    },
  ];

  return (
    <AdvancedDataTable
      data={conferences}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName={`video-conferences-${exhibitionId}`}
      searchPlaceholder="Search video conferences..."
      emptyMessage="No video conferences found. Schedule a new meeting to start collaborating."
      loading={isLoading}
      className={className}
      variant="default"
      onRowClick={onConferenceJoin}
      defaultSorting={[{ id: 'scheduledStartTime', desc: false }]}
    />
  );
}
