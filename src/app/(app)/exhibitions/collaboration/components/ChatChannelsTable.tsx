"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  MessageSquare, 
  Users, 
  Lock, 
  Globe, 
  Hash,
  Eye,
  Edit2,
  Trash2,
  UserPlus,
  Settings,
  MessageCircle,
  Clock,
  Activity
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format, formatDistanceToNow } from 'date-fns';
import type { ExhibitionChatChannel, ExhibitionChatMessage } from '@/types/firestore';
import { getChatChannelsAction, getChatMessagesAction } from '../actions';

interface ChatChannelsTableProps {
  exhibitionId: string;
  className?: string;
  onChannelSelect?: (channel: ExhibitionChatChannel) => void;
  onChannelEdit?: (channel: ExhibitionChatChannel) => void;
  onChannelDelete?: (channelId: string) => void;
}

interface EnhancedChatChannel extends ExhibitionChatChannel {
  messageCount: number;
  lastMessageAt?: Date;
  lastMessagePreview?: string;
  activeParticipants: number;
  unreadCount: number;
}

export default function ChatChannelsTable({ 
  exhibitionId, 
  className, 
  onChannelSelect, 
  onChannelEdit, 
  onChannelDelete 
}: ChatChannelsTableProps) {
  const [channels, setChannels] = useState<EnhancedChatChannel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchChannelsData = async () => {
      try {
        setIsLoading(true);
        
        const channelsData = await getChatChannelsAction(exhibitionId);
        
        // Enhance channels with message data
        const enhancedChannels = await Promise.all(
          channelsData.map(async (channel) => {
            try {
              const messages = await getChatMessagesAction(exhibitionId, channel.id!);
              const lastMessage = messages[messages.length - 1];
              
              return {
                ...channel,
                messageCount: messages.length,
                lastMessageAt: lastMessage ? new Date(lastMessage.createdAt!) : undefined,
                lastMessagePreview: lastMessage?.content || '',
                activeParticipants: channel.participants?.length || 0,
                unreadCount: Math.floor(Math.random() * 5), // Mock unread count
              } as EnhancedChatChannel;
            } catch (error) {
              console.error(`Error fetching messages for channel ${channel.id}:`, error);
              return {
                ...channel,
                messageCount: 0,
                activeParticipants: channel.participants?.length || 0,
                unreadCount: 0,
              } as EnhancedChatChannel;
            }
          })
        );

        setChannels(enhancedChannels);
      } catch (error) {
        console.error('Error fetching chat channels:', error);
        toast({
          title: "Error",
          description: "Failed to load chat channels. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchChannelsData();
  }, [exhibitionId, toast]);

  const columns: AdvancedTableColumn<EnhancedChatChannel>[] = [
    {
      accessorKey: 'name',
      title: 'Channel',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const channel = row.original;
        const typeIcons = {
          general: Hash,
          team: Users,
          vendors: MessageSquare,
          logistics: Activity,
          private: Lock,
        };
        const IconComponent = typeIcons[channel.type] || Hash;
        
        return (
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <IconComponent className="h-4 w-4 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2">
                <div className="font-medium">{channel.name}</div>
                {channel.unreadCount > 0 && (
                  <Badge variant="destructive" className="h-5 px-1.5 text-xs">
                    {channel.unreadCount}
                  </Badge>
                )}
              </div>
              {channel.description && (
                <div className="text-xs text-muted-foreground truncate">
                  {channel.description}
                </div>
              )}
              {channel.lastMessagePreview && (
                <div className="text-xs text-muted-foreground truncate mt-1">
                  {channel.lastMessagePreview}
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'type',
      title: 'Type',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const type = row.original.type;
        const typeConfig = {
          general: { variant: 'default' as const, label: 'General', icon: Globe },
          team: { variant: 'secondary' as const, label: 'Team', icon: Users },
          vendors: { variant: 'outline' as const, label: 'Vendors', icon: MessageSquare },
          logistics: { variant: 'outline' as const, label: 'Logistics', icon: Activity },
          private: { variant: 'destructive' as const, label: 'Private', icon: Lock },
        };
        const config = typeConfig[type];
        const IconComponent = config.icon;
        
        return (
          <Badge variant={config.variant} className="flex items-center w-fit">
            <IconComponent className="h-3 w-3 mr-1" />
            {config.label}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'participants',
      title: 'Participants',
      sortable: true,
      cell: ({ row }) => {
        const channel = row.original;
        return (
          <div className="flex items-center">
            <Users className="h-4 w-4 mr-2 text-muted-foreground" />
            <span className="text-sm">{channel.activeParticipants}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'messageCount',
      title: 'Messages',
      sortable: true,
      cell: ({ row }) => {
        const count = row.original.messageCount;
        return (
          <div className="flex items-center">
            <MessageCircle className="h-4 w-4 mr-2 text-muted-foreground" />
            <span className="text-sm">{count}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'lastMessageAt',
      title: 'Last Activity',
      sortable: true,
      cell: ({ row }) => {
        const lastActivity = row.original.lastMessageAt;
        if (!lastActivity) {
          return <span className="text-sm text-muted-foreground">No messages</span>;
        }
        
        return (
          <div className="flex items-center text-sm">
            <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
            {formatDistanceToNow(lastActivity, { addSuffix: true })}
          </div>
        );
      },
    },
    {
      accessorKey: 'isArchived',
      title: 'Status',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const isArchived = row.original.isArchived;
        return (
          <Badge variant={isArchived ? 'secondary' : 'default'}>
            {isArchived ? 'Archived' : 'Active'}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      title: 'Created',
      sortable: true,
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        if (!createdAt) return null;
        
        const date = new Date(createdAt);
        return (
          <div className="text-sm text-muted-foreground">
            {format(date, 'MMM d, yyyy')}
          </div>
        );
      },
    },
  ];

  const rowActions = (row: EnhancedChatChannel): AdvancedTableRowAction<EnhancedChatChannel>[] => {
    const actions: AdvancedTableRowAction<EnhancedChatChannel>[] = [
      {
        type: 'view',
        label: 'Open Channel',
        onClick: () => onChannelSelect?.(row),
        icon: <MessageSquare className="h-4 w-4" />,
      },
    ];

    if (onChannelEdit) {
      actions.push({
        type: 'edit',
        label: 'Edit Channel',
        onClick: () => onChannelEdit(row),
        icon: <Edit2 className="h-4 w-4" />,
      });
    }

    actions.push({
      type: 'custom',
      label: 'Manage Members',
      onClick: () => {
        toast({
          title: "Manage Members",
          description: `Managing members for ${row.name}...`,
        });
      },
      icon: <UserPlus className="h-4 w-4" />,
    });

    actions.push({
      type: 'custom',
      label: 'Channel Settings',
      onClick: () => {
        toast({
          title: "Channel Settings",
          description: `Opening settings for ${row.name}...`,
        });
      },
      icon: <Settings className="h-4 w-4" />,
    });

    if (onChannelDelete && !row.isArchived) {
      actions.push({
        type: 'delete',
        label: 'Archive Channel',
        onClick: () => onChannelDelete(row.id!),
        icon: <Trash2 className="h-4 w-4" />,
        variant: 'destructive',
      });
    }

    return actions;
  };

  const filters = [
    {
      id: 'type',
      label: 'Channel Type',
      type: 'select' as const,
      options: [
        { label: 'General', value: 'general' },
        { label: 'Team', value: 'team' },
        { label: 'Vendors', value: 'vendors' },
        { label: 'Logistics', value: 'logistics' },
        { label: 'Private', value: 'private' },
      ],
    },
    {
      id: 'isArchived',
      label: 'Status',
      type: 'select' as const,
      options: [
        { label: 'Active', value: 'false' },
        { label: 'Archived', value: 'true' },
      ],
    },
  ];

  const bulkActions = [
    {
      id: 'archive',
      label: 'Archive Selected',
      icon: <Trash2 className="h-4 w-4" />,
      variant: 'destructive' as const,
      onClick: (selectedRows: EnhancedChatChannel[]) => {
        toast({
          title: "Archive Channels",
          description: `Archiving ${selectedRows.length} channel(s)...`,
        });
      },
    },
  ];

  return (
    <AdvancedDataTable
      data={channels}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      bulkActions={bulkActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName={`chat-channels-${exhibitionId}`}
      searchPlaceholder="Search channels..."
      emptyMessage="No chat channels found. Create a new channel to start collaborating."
      loading={isLoading}
      className={className}
      variant="default"
      onRowClick={onChannelSelect}
    />
  );
}
