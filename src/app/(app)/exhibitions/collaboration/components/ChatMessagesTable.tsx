"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  MessageSquare, 
  File, 
  Image, 
  Mic, 
  Settings,
  Reply,
  Edit2,
  Trash2,
  Copy,
  Flag,
  Heart,
  ThumbsUp,
  Smile,
  Clock,
  Eye
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format, formatDistanceToNow } from 'date-fns';
import type { ExhibitionChatMessage } from '@/types/firestore';
import { getChatMessagesAction } from '../actions';

interface ChatMessagesTableProps {
  exhibitionId: string;
  channelId?: string;
  className?: string;
  onMessageReply?: (message: ExhibitionChatMessage) => void;
  onMessageEdit?: (message: ExhibitionChatMessage) => void;
  onMessageDelete?: (messageId: string) => void;
}

interface EnhancedChatMessage extends ExhibitionChatMessage {
  reactionCount: number;
  hasUserReacted: boolean;
  replyCount: number;
}

export default function ChatMessagesTable({ 
  exhibitionId, 
  channelId, 
  className, 
  onMessageReply, 
  onMessageEdit, 
  onMessageDelete 
}: ChatMessagesTableProps) {
  const [messages, setMessages] = useState<EnhancedChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchMessages = async () => {
      try {
        setIsLoading(true);
        
        const messagesData = channelId 
          ? await getChatMessagesAction(exhibitionId, channelId)
          : await getChatMessagesAction(exhibitionId);
        
        // Enhance messages with reaction and reply data
        const enhancedMessages: EnhancedChatMessage[] = messagesData.map(message => ({
          ...message,
          reactionCount: message.reactions?.reduce((sum, reaction) => sum + reaction.count, 0) || 0,
          hasUserReacted: message.reactions?.some(reaction => 
            reaction.users.includes('current-user-id') // Replace with actual current user ID
          ) || false,
          replyCount: Math.floor(Math.random() * 3), // Mock reply count
        }));

        setMessages(enhancedMessages);
      } catch (error) {
        console.error('Error fetching chat messages:', error);
        toast({
          title: "Error",
          description: "Failed to load chat messages. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchMessages();
  }, [exhibitionId, channelId, toast]);

  const columns: AdvancedTableColumn<EnhancedChatMessage>[] = [
    {
      accessorKey: 'content',
      title: 'Message',
      sortable: false,
      filterable: true,
      cell: ({ row }) => {
        const message = row.original;
        const typeIcons = {
          text: MessageSquare,
          file: File,
          image: Image,
          voice: Mic,
          system: Settings,
        };
        const IconComponent = typeIcons[message.type];
        
        return (
          <div className="flex items-start space-x-3 max-w-md">
            <Avatar className="h-8 w-8 flex-shrink-0">
              <AvatarImage src={message.senderAvatar} alt={message.senderName} />
              <AvatarFallback>
                {message.senderName.split(' ').map(n => n[0]).join('').toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium text-sm">{message.senderName}</span>
                <IconComponent className="h-3 w-3 text-muted-foreground" />
                {message.isEdited && (
                  <Badge variant="outline" className="text-xs">edited</Badge>
                )}
              </div>
              <div className="text-sm">
                {message.type === 'text' ? (
                  <p className="break-words">{message.content}</p>
                ) : message.type === 'file' ? (
                  <div className="flex items-center gap-2 p-2 border rounded">
                    <File className="h-4 w-4" />
                    <span>{message.fileName}</span>
                    <Badge variant="outline" className="text-xs">
                      {message.fileSize ? `${Math.round(message.fileSize / 1024)}KB` : ''}
                    </Badge>
                  </div>
                ) : message.type === 'image' ? (
                  <div className="flex items-center gap-2 p-2 border rounded">
                    <Image className="h-4 w-4" />
                    <span>{message.fileName || 'Image'}</span>
                  </div>
                ) : (
                  <p className="text-muted-foreground italic">{message.content}</p>
                )}
              </div>
              {message.mentions.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-1">
                  {message.mentions.map((mention, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      @{mention}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'type',
      title: 'Type',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const type = row.original.type;
        const typeConfig = {
          text: { variant: 'default' as const, label: 'Text', icon: MessageSquare },
          file: { variant: 'secondary' as const, label: 'File', icon: File },
          image: { variant: 'outline' as const, label: 'Image', icon: Image },
          voice: { variant: 'outline' as const, label: 'Voice', icon: Mic },
          system: { variant: 'destructive' as const, label: 'System', icon: Settings },
        };
        const config = typeConfig[type];
        const IconComponent = config.icon;
        
        return (
          <Badge variant={config.variant} className="flex items-center w-fit">
            <IconComponent className="h-3 w-3 mr-1" />
            {config.label}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'reactions',
      title: 'Reactions',
      sortable: true,
      cell: ({ row }) => {
        const message = row.original;
        if (!message.reactions || message.reactions.length === 0) {
          return <span className="text-sm text-muted-foreground">None</span>;
        }
        
        return (
          <div className="flex flex-wrap gap-1">
            {message.reactions.slice(0, 3).map((reaction, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {reaction.emoji} {reaction.count}
              </Badge>
            ))}
            {message.reactions.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{message.reactions.length - 3} more
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'replyCount',
      title: 'Replies',
      sortable: true,
      cell: ({ row }) => {
        const count = row.original.replyCount;
        return (
          <div className="flex items-center">
            <Reply className="h-4 w-4 mr-2 text-muted-foreground" />
            <span className="text-sm">{count}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      title: 'Sent',
      sortable: true,
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        if (!createdAt) return null;
        
        const date = new Date(createdAt);
        return (
          <div className="text-sm text-muted-foreground">
            <div>{format(date, 'MMM d, HH:mm')}</div>
            <div className="text-xs">
              {formatDistanceToNow(date, { addSuffix: true })}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'isDeleted',
      title: 'Status',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const message = row.original;
        if (message.isDeleted) {
          return <Badge variant="destructive">Deleted</Badge>;
        }
        if (message.isEdited) {
          return <Badge variant="secondary">Edited</Badge>;
        }
        return <Badge variant="default">Active</Badge>;
      },
    },
  ];

  const rowActions = (row: EnhancedChatMessage): AdvancedTableRowAction<EnhancedChatMessage>[] => {
    const actions: AdvancedTableRowAction<EnhancedChatMessage>[] = [];

    if (!row.isDeleted) {
      if (onMessageReply) {
        actions.push({
          type: 'custom',
          label: 'Reply',
          onClick: () => onMessageReply(row),
          icon: <Reply className="h-4 w-4" />,
        });
      }

      actions.push({
        type: 'custom',
        label: 'React',
        onClick: () => {
          toast({
            title: "Add Reaction",
            description: "Reaction feature coming soon...",
          });
        },
        icon: <Heart className="h-4 w-4" />,
      });

      actions.push({
        type: 'copy',
        label: 'Copy Message',
        onClick: () => {
          navigator.clipboard.writeText(row.content);
          toast({
            title: "Copied",
            description: "Message copied to clipboard",
          });
        },
        icon: <Copy className="h-4 w-4" />,
      });

      // Only show edit/delete for own messages (mock check)
      if (row.senderId === 'current-user-id') {
        if (onMessageEdit) {
          actions.push({
            type: 'edit',
            label: 'Edit Message',
            onClick: () => onMessageEdit(row),
            icon: <Edit2 className="h-4 w-4" />,
          });
        }

        if (onMessageDelete) {
          actions.push({
            type: 'delete',
            label: 'Delete Message',
            onClick: () => onMessageDelete(row.id!),
            icon: <Trash2 className="h-4 w-4" />,
            variant: 'destructive',
          });
        }
      }

      actions.push({
        type: 'custom',
        label: 'Report',
        onClick: () => {
          toast({
            title: "Report Message",
            description: "Message reported for review",
          });
        },
        icon: <Flag className="h-4 w-4" />,
        variant: 'destructive',
      });
    }

    return actions;
  };

  const filters = [
    {
      id: 'type',
      label: 'Message Type',
      type: 'select' as const,
      options: [
        { label: 'Text', value: 'text' },
        { label: 'File', value: 'file' },
        { label: 'Image', value: 'image' },
        { label: 'Voice', value: 'voice' },
        { label: 'System', value: 'system' },
      ],
    },
    {
      id: 'senderName',
      label: 'Sender',
      type: 'select' as const,
      options: Array.from(new Set(messages.map(m => m.senderName))).map(name => ({
        label: name,
        value: name,
      })),
    },
  ];

  return (
    <AdvancedDataTable
      data={messages}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName={`chat-messages-${channelId || 'all'}`}
      searchPlaceholder="Search messages..."
      emptyMessage="No messages found. Start the conversation!"
      loading={isLoading}
      className={className}
      variant="default"
      defaultSorting={[{ id: 'createdAt', desc: true }]}
    />
  );
}
