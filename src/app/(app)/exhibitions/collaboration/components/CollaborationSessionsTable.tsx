"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Users, 
  MessageSquare, 
  FileText, 
  Palette, 
  Video,
  Activity,
  Eye,
  Edit2,
  Trash2,
  Play,
  Pause,
  Square,
  Clock,
  Calendar,
  Settings
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format, formatDistanceToNow } from 'date-fns';
import type { ExhibitionCollaborationSession } from '@/types/firestore';
import { getCollaborationSessionsAction } from '../actions';

interface CollaborationSessionsTableProps {
  exhibitionId: string;
  className?: string;
  onSessionJoin?: (session: ExhibitionCollaborationSession) => void;
  onSessionEdit?: (session: ExhibitionCollaborationSession) => void;
  onSessionDelete?: (sessionId: string) => void;
}

interface EnhancedCollaborationSession extends ExhibitionCollaborationSession {
  participantCount: number;
  duration: number; // in minutes
  isActive: boolean;
  activityCount: number;
  lastActivityAt?: Date;
}

export default function CollaborationSessionsTable({ 
  exhibitionId, 
  className, 
  onSessionJoin, 
  onSessionEdit, 
  onSessionDelete 
}: CollaborationSessionsTableProps) {
  const [sessions, setSessions] = useState<EnhancedCollaborationSession[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchSessions = async () => {
      try {
        setIsLoading(true);
        
        const sessionsData = await getCollaborationSessionsAction(exhibitionId);
        
        // Enhance sessions with additional data
        const enhancedSessions: EnhancedCollaborationSession[] = sessionsData.map(session => {
          const startTime = session.startTime ? new Date(session.startTime) : new Date();
          const endTime = session.endTime ? new Date(session.endTime) : new Date();
          const duration = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));
          
          return {
            ...session,
            participantCount: session.participants?.length || 0,
            duration: duration > 0 ? duration : 0,
            isActive: session.status === 'active',
            activityCount: Math.floor(Math.random() * 50) + 1, // Mock activity count
            lastActivityAt: new Date(Date.now() - Math.random() * 60 * 60 * 1000), // Mock last activity
          };
        });

        setSessions(enhancedSessions);
      } catch (error) {
        console.error('Error fetching collaboration sessions:', error);
        toast({
          title: "Error",
          description: "Failed to load collaboration sessions. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchSessions();
  }, [exhibitionId, toast]);

  const columns: AdvancedTableColumn<EnhancedCollaborationSession>[] = [
    {
      accessorKey: 'title',
      title: 'Session',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const session = row.original;
        const typeIcons = {
          chat: MessageSquare,
          document: FileText,
          whiteboard: Palette,
          video_conference: Video,
          mixed: Users,
        };
        const IconComponent = typeIcons[session.type] || Users;
        
        return (
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <IconComponent className="h-4 w-4 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2">
                <div className="font-medium">{session.title}</div>
                {session.isActive && (
                  <Badge variant="default" className="h-5 px-1.5 text-xs animate-pulse">
                    Live
                  </Badge>
                )}
              </div>
              {session.description && (
                <div className="text-xs text-muted-foreground truncate">
                  {session.description}
                </div>
              )}
              <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {session.duration > 0 ? `${session.duration}m` : 'Ongoing'}
                </div>
                <div className="flex items-center gap-1">
                  <Activity className="h-3 w-3" />
                  {session.activityCount} activities
                </div>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'type',
      title: 'Type',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const type = row.original.type;
        const typeConfig = {
          chat: { variant: 'default' as const, label: 'Chat', icon: MessageSquare },
          document: { variant: 'secondary' as const, label: 'Document', icon: FileText },
          whiteboard: { variant: 'outline' as const, label: 'Whiteboard', icon: Palette },
          video_conference: { variant: 'destructive' as const, label: 'Video Call', icon: Video },
          mixed: { variant: 'default' as const, label: 'Mixed', icon: Users },
        };
        const config = typeConfig[type];
        const IconComponent = config.icon;
        
        return (
          <Badge variant={config.variant} className="flex items-center w-fit">
            <IconComponent className="h-3 w-3 mr-1" />
            {config.label}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'status',
      title: 'Status',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const status = row.original.status;
        const statusConfig = {
          active: { variant: 'default' as const, label: 'Active', icon: Play },
          paused: { variant: 'secondary' as const, label: 'Paused', icon: Pause },
          completed: { variant: 'outline' as const, label: 'Completed', icon: Square },
          cancelled: { variant: 'destructive' as const, label: 'Cancelled', icon: Square },
        };
        const config = statusConfig[status];
        const IconComponent = config.icon;
        
        return (
          <Badge variant={config.variant} className="flex items-center w-fit">
            <IconComponent className="h-3 w-3 mr-1" />
            {config.label}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'participants',
      title: 'Participants',
      sortable: true,
      cell: ({ row }) => {
        const session = row.original;
        const participants = session.participants || [];
        
        return (
          <div className="flex items-center space-x-2">
            <div className="flex -space-x-2">
              {participants.slice(0, 3).map((participantId, index) => (
                <Avatar key={participantId} className="h-6 w-6 border-2 border-background">
                  <AvatarFallback className="text-xs">
                    {participantId.slice(-2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              ))}
              {participants.length > 3 && (
                <div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                  <span className="text-xs font-medium">+{participants.length - 3}</span>
                </div>
              )}
            </div>
            <span className="text-sm text-muted-foreground">
              {session.participantCount} member{session.participantCount !== 1 ? 's' : ''}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'startTime',
      title: 'Started',
      sortable: true,
      cell: ({ row }) => {
        const startTime = row.original.startTime;
        if (!startTime) {
          return <span className="text-sm text-muted-foreground">Not started</span>;
        }
        
        const date = new Date(startTime);
        return (
          <div className="text-sm">
            <div>{format(date, 'MMM d, HH:mm')}</div>
            <div className="text-xs text-muted-foreground">
              {formatDistanceToNow(date, { addSuffix: true })}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'lastActivityAt',
      title: 'Last Activity',
      sortable: true,
      cell: ({ row }) => {
        const lastActivity = row.original.lastActivityAt;
        if (!lastActivity) {
          return <span className="text-sm text-muted-foreground">No activity</span>;
        }
        
        return (
          <div className="flex items-center text-sm">
            <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
            {formatDistanceToNow(lastActivity, { addSuffix: true })}
          </div>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      title: 'Created',
      sortable: true,
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        if (!createdAt) return null;
        
        const date = new Date(createdAt);
        return (
          <div className="text-sm text-muted-foreground">
            {format(date, 'MMM d, yyyy')}
          </div>
        );
      },
    },
  ];

  const rowActions = (row: EnhancedCollaborationSession): AdvancedTableRowAction<EnhancedCollaborationSession>[] => {
    const actions: AdvancedTableRowAction<EnhancedCollaborationSession>[] = [];

    if (row.status === 'active' || row.status === 'paused') {
      actions.push({
        type: 'custom',
        label: 'Join Session',
        onClick: () => onSessionJoin?.(row),
        icon: <Play className="h-4 w-4" />,
        variant: 'default',
      });
    }

    actions.push({
      type: 'view',
      label: 'View Details',
      onClick: () => {
        toast({
          title: "View Session",
          description: `Viewing details for ${row.title}...`,
        });
      },
      icon: <Eye className="h-4 w-4" />,
    });

    if (onSessionEdit && row.status !== 'completed') {
      actions.push({
        type: 'edit',
        label: 'Edit Session',
        onClick: () => onSessionEdit(row),
        icon: <Edit2 className="h-4 w-4" />,
      });
    }

    actions.push({
      type: 'custom',
      label: 'Session Settings',
      onClick: () => {
        toast({
          title: "Session Settings",
          description: `Opening settings for ${row.title}...`,
        });
      },
      icon: <Settings className="h-4 w-4" />,
    });

    if (onSessionDelete && row.status !== 'active') {
      actions.push({
        type: 'delete',
        label: 'Delete Session',
        onClick: () => onSessionDelete(row.id!),
        icon: <Trash2 className="h-4 w-4" />,
        variant: 'destructive',
      });
    }

    return actions;
  };

  const filters = [
    {
      id: 'type',
      label: 'Session Type',
      type: 'select' as const,
      options: [
        { label: 'Chat', value: 'chat' },
        { label: 'Document', value: 'document' },
        { label: 'Whiteboard', value: 'whiteboard' },
        { label: 'Video Conference', value: 'video_conference' },
        { label: 'Mixed', value: 'mixed' },
      ],
    },
    {
      id: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Paused', value: 'paused' },
        { label: 'Completed', value: 'completed' },
        { label: 'Cancelled', value: 'cancelled' },
      ],
    },
  ];

  const bulkActions = [
    {
      id: 'pause',
      label: 'Pause Selected',
      icon: <Pause className="h-4 w-4" />,
      onClick: (selectedRows: EnhancedCollaborationSession[]) => {
        toast({
          title: "Pause Sessions",
          description: `Pausing ${selectedRows.length} session(s)...`,
        });
      },
    },
    {
      id: 'complete',
      label: 'Complete Selected',
      icon: <Square className="h-4 w-4" />,
      onClick: (selectedRows: EnhancedCollaborationSession[]) => {
        toast({
          title: "Complete Sessions",
          description: `Completing ${selectedRows.length} session(s)...`,
        });
      },
    },
  ];

  return (
    <AdvancedDataTable
      data={sessions}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      bulkActions={bulkActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName={`collaboration-sessions-${exhibitionId}`}
      searchPlaceholder="Search collaboration sessions..."
      emptyMessage="No collaboration sessions found. Start a new session to begin collaborating."
      loading={isLoading}
      className={className}
      variant="default"
      onRowClick={onSessionJoin}
      defaultSorting={[{ id: 'startTime', desc: true }]}
    />
  );
}
