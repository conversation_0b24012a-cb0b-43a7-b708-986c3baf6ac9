"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Shield, AlertTriangle, Lock, RefreshCw } from 'lucide-react';

export default function SecurityErrorPage() {
  const handleRefresh = () => {
    window.location.reload();
  };

  const handleContact = () => {
    window.location.href = 'mailto:<EMAIL>?subject=Security%20Error%20Report';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full">
        <Card className="border-red-200 shadow-xl">
          <CardHeader className="text-center pb-6">
            <div className="w-20 h-20 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
              <Shield className="w-10 h-10 text-red-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-red-800 mb-2">
              Security Protection Active
            </CardTitle>
            <p className="text-red-600">
              EVEXA has detected suspicious activity and activated security measures
            </p>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Security Alert */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-red-800 mb-1">Security Violation Detected</h3>
                  <p className="text-red-700 text-sm">
                    Our security system has detected potentially unauthorized access attempts or 
                    tampering with the application. This could include:
                  </p>
                  <ul className="mt-2 text-red-700 text-sm list-disc list-inside space-y-1">
                    <li>Developer tools being opened</li>
                    <li>Code debugging attempts</li>
                    <li>Unauthorized modification of application files</li>
                    <li>Suspicious network activity</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Protection Measures */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Lock className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-blue-800 mb-1">Protection Measures Active</h3>
                  <p className="text-blue-700 text-sm">
                    EVEXA is protected by advanced security measures including:
                  </p>
                  <ul className="mt-2 text-blue-700 text-sm list-disc list-inside space-y-1">
                    <li>Real-time integrity monitoring</li>
                    <li>Anti-tampering protection</li>
                    <li>License validation</li>
                    <li>Intellectual property protection</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* What to do */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-800 mb-2">What you can do:</h3>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-sm font-medium text-gray-600">1</div>
                  <div>
                    <p className="text-gray-700 text-sm">
                      <strong>Close developer tools</strong> if you have them open
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-sm font-medium text-gray-600">2</div>
                  <div>
                    <p className="text-gray-700 text-sm">
                      <strong>Refresh the page</strong> to restart the application
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-sm font-medium text-gray-600">3</div>
                  <div>
                    <p className="text-gray-700 text-sm">
                      <strong>Contact support</strong> if this error persists
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button 
                onClick={handleRefresh}
                className="flex-1 bg-blue-600 hover:bg-blue-700"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh Application
              </Button>
              <Button 
                onClick={handleContact}
                variant="outline"
                className="flex-1 border-gray-300 hover:bg-gray-50"
              >
                Contact Support
              </Button>
            </div>

            {/* Footer */}
            <div className="text-center pt-4 border-t border-gray-200">
              <p className="text-xs text-gray-500">
                This security measure helps protect EVEXA's intellectual property and ensures 
                authorized usage only. All security events are logged for monitoring purposes.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
