"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import {
  Building2,
  Plus,
  Edit,
  Trash2,
  Users,
  DollarSign,
  Calendar,
  Mail,
  Phone,
  Globe,
  Star,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  BarChart3,
  Key,
  Palette,
  FileText,
  TrendingUp,
  Handshake
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/auth-context';
import {
  exhibitionOrganizerPartnershipService,
  type ExhibitionOrganizerPartner,
  type PartnershipApplication
} from '@/services/exhibitionOrganizerPartnershipService';

export default function ExhibitionOrganizerPartnershipsPage() {
  const { user } = useAuth();
  const { toast } = useToast();

  const [partners, setPartners] = useState<ExhibitionOrganizerPartner[]>([]);
  const [applications, setApplications] = useState<PartnershipApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('partners');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showApplicationDialog, setShowApplicationDialog] = useState(false);

  const [newPartner, setNewPartner] = useState({
    organizationName: '',
    contactInfo: {
      primaryContact: '',
      email: '',
      phone: '',
      website: '',
      address: {
        street: '',
        city: '',
        state: '',
        country: '',
        zipCode: ''
      }
    },
    partnershipTier: 'bronze' as ExhibitionOrganizerPartner['partnershipTier'],
    revenueSharing: {
      model: 'percentage' as const,
      percentage: 15,
      paymentSchedule: 'monthly' as const,
      minimumPayout: 100
    },
    clientLimits: {
      maxClients: 50,
      currentClients: 0,
      maxExhibitionsPerClient: 10
    }
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [partnersData, applicationsData] = await Promise.all([
        exhibitionOrganizerPartnershipService.getAllPartners(),
        exhibitionOrganizerPartnershipService.getApplications()
      ]);
      setPartners(partnersData);
      setApplications(applicationsData);
    } catch (error) {
      console.error('Failed to load partnership data:', error);
      toast({
        title: "Error",
        description: "Failed to load partnership data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePartner = async () => {
    try {
      const partner = await exhibitionOrganizerPartnershipService.createPartner({
        ...newPartner,
        status: 'active',
        whiteLabelConfig: {
          enabled: false
        },
        apiAccess: {
          enabled: false,
          allowedEndpoints: [],
          rateLimit: 1000
        },
        features: {
          whiteLabeling: newPartner.partnershipTier !== 'bronze',
          customBranding: newPartner.partnershipTier !== 'bronze',
          apiAccess: true,
          prioritySupport: newPartner.partnershipTier === 'platinum' || newPartner.partnershipTier === 'gold',
          customReporting: newPartner.partnershipTier === 'platinum',
          dedicatedAccount: newPartner.partnershipTier === 'platinum',
          trainingProgram: true,
          marketingSupport: newPartner.partnershipTier !== 'bronze'
        },
        performance: {
          clientsReferred: 0,
          totalRevenue: 0,
          averageClientValue: 0,
          retentionRate: 0,
          satisfactionScore: 0
        },
        agreement: {
          startDate: new Date(),
          contractTerms: 'Standard Exhibition Organizer Partnership Agreement',
          signedBy: user?.email || 'system',
          signedDate: new Date()
        },
        onboardingStatus: {
          currentStep: 1,
          totalSteps: 7,
          completedSteps: [],
          nextAction: 'Complete partner profile setup',
          assignedTo: 'Partnership Team'
        }
      });

      setPartners(prev => [partner, ...prev]);
      setShowCreateDialog(false);

      toast({
        title: "Success",
        description: "Exhibition organizer partner created successfully"
      });
    } catch (error) {
      console.error('Failed to create partner:', error);
      toast({
        title: "Error",
        description: "Failed to create partner",
        variant: "destructive"
      });
    }
  };

  const handleReviewApplication = async (applicationId: string, status: 'approved' | 'rejected', notes: string) => {
    try {
      await exhibitionOrganizerPartnershipService.reviewApplication(
        applicationId,
        status,
        notes,
        user?.email || 'system'
      );

      setApplications(prev => prev.map(app =>
        app.id === applicationId
          ? { ...app, status, reviewNotes: notes, reviewedBy: user?.email || 'system', reviewedAt: new Date() }
          : app
      ));

      toast({
        title: "Success",
        description: `Application ${status} successfully`
      });
    } catch (error) {
      console.error('Failed to review application:', error);
      toast({
        title: "Error",
        description: "Failed to review application",
        variant: "destructive"
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      case 'terminated': return 'bg-gray-100 text-gray-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'submitted': return 'bg-blue-100 text-blue-800';
      case 'under_review': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTierColor = (tier: ExhibitionOrganizerPartner['partnershipTier']) => {
    switch (tier) {
      case 'platinum': return 'bg-purple-100 text-purple-800';
      case 'gold': return 'bg-yellow-100 text-yellow-800';
      case 'silver': return 'bg-gray-100 text-gray-800';
      case 'bronze': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-muted-foreground">You need to be authenticated to access this page.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Building2 className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold">Exhibition Organizer Partnership Program</h1>
            <p className="text-muted-foreground">
              Manage exhibition organizer partnerships, white-label solutions, and revenue sharing
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Partner
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create Exhibition Organizer Partner</DialogTitle>
                <DialogDescription>Add a new exhibition organizer to the partnership program</DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="organizationName">Organization Name</Label>
                    <Input
                      id="organizationName"
                      value={newPartner.organizationName}
                      onChange={(e) => setNewPartner(prev => ({ ...prev, organizationName: e.target.value }))}
                      placeholder="Exhibition Organizer Inc."
                    />
                  </div>
                  <div>
                    <Label htmlFor="partnershipTier">Partnership Tier</Label>
                    <Select
                      value={newPartner.partnershipTier}
                      onValueChange={(value) => setNewPartner(prev => ({
                        ...prev,
                        partnershipTier: value as ExhibitionOrganizerPartner['partnershipTier']
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="platinum">Platinum</SelectItem>
                        <SelectItem value="gold">Gold</SelectItem>
                        <SelectItem value="silver">Silver</SelectItem>
                        <SelectItem value="bronze">Bronze</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="primaryContact">Primary Contact</Label>
                    <Input
                      id="primaryContact"
                      value={newPartner.contactInfo.primaryContact}
                      onChange={(e) => setNewPartner(prev => ({
                        ...prev,
                        contactInfo: { ...prev.contactInfo, primaryContact: e.target.value }
                      }))}
                      placeholder="John Smith"
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newPartner.contactInfo.email}
                      onChange={(e) => setNewPartner(prev => ({
                        ...prev,
                        contactInfo: { ...prev.contactInfo, email: e.target.value }
                      }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      value={newPartner.contactInfo.phone}
                      onChange={(e) => setNewPartner(prev => ({
                        ...prev,
                        contactInfo: { ...prev.contactInfo, phone: e.target.value }
                      }))}
                      placeholder="+****************"
                    />
                  </div>
                  <div>
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      value={newPartner.contactInfo.website}
                      onChange={(e) => setNewPartner(prev => ({
                        ...prev,
                        contactInfo: { ...prev.contactInfo, website: e.target.value }
                      }))}
                      placeholder="https://organizer.com"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="revenuePercentage">Revenue Share %</Label>
                    <Input
                      id="revenuePercentage"
                      type="number"
                      value={newPartner.revenueSharing.percentage}
                      onChange={(e) => setNewPartner(prev => ({
                        ...prev,
                        revenueSharing: { ...prev.revenueSharing, percentage: Number(e.target.value) }
                      }))}
                      placeholder="15"
                    />
                  </div>
                  <div>
                    <Label htmlFor="maxClients">Max Clients</Label>
                    <Input
                      id="maxClients"
                      type="number"
                      value={newPartner.clientLimits.maxClients}
                      onChange={(e) => setNewPartner(prev => ({
                        ...prev,
                        clientLimits: { ...prev.clientLimits, maxClients: Number(e.target.value) }
                      }))}
                      placeholder="50"
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreatePartner}>
                    Create Partner
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Partnership Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Partners</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{partners.length}</div>
            <p className="text-xs text-muted-foreground">
              {partners.filter(p => p.status === 'active').length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(partners.reduce((sum, p) => sum + p.performance.totalRevenue, 0))}
            </div>
            <p className="text-xs text-muted-foreground">
              From all partners
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Clients Referred</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {partners.reduce((sum, p) => sum + p.performance.clientsReferred, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total referrals
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Applications</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {applications.filter(a => a.status === 'submitted' || a.status === 'under_review').length}
            </div>
            <p className="text-xs text-muted-foreground">
              Need review
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="partners">Active Partners</TabsTrigger>
          <TabsTrigger value="applications">Applications</TabsTrigger>
          <TabsTrigger value="revenue">Revenue Sharing</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="partners" className="space-y-4">
          <div className="grid gap-4">
            {partners.map((partner) => (
              <Card key={partner.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Building2 className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <CardTitle className="text-lg">{partner.organizationName}</CardTitle>
                        <CardDescription>{partner.contactInfo.primaryContact}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(partner.status)}>
                        {partner.status}
                      </Badge>
                      <Badge className={getTierColor(partner.partnershipTier)}>
                        {partner.partnershipTier}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Contact Information</h4>
                      <div className="space-y-1 text-sm text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <Mail className="h-3 w-3" />
                          {partner.contactInfo.email}
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="h-3 w-3" />
                          {partner.contactInfo.phone}
                        </div>
                        {partner.contactInfo.website && (
                          <div className="flex items-center gap-2">
                            <Globe className="h-3 w-3" />
                            <a href={partner.contactInfo.website} target="_blank" rel="noopener noreferrer" className="hover:underline">
                              Website
                            </a>
                          </div>
                        )}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Performance</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Clients Referred:</span>
                          <span className="font-medium">{partner.performance.clientsReferred}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Total Revenue:</span>
                          <span className="font-medium">{formatCurrency(partner.performance.totalRevenue)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Retention Rate:</span>
                          <span className="font-medium">{partner.performance.retentionRate}%</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Partnership Details</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Revenue Share:</span>
                          <span className="font-medium">{partner.revenueSharing.percentage}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Client Limit:</span>
                          <span className="font-medium">{partner.clientLimits.currentClients}/{partner.clientLimits.maxClients}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">White Label:</span>
                          <span className="font-medium">{partner.whiteLabelConfig.enabled ? 'Enabled' : 'Disabled'}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end gap-2 mt-4">
                    <Button variant="outline" size="sm">
                      <Key className="h-3 w-3 mr-1" />
                      API Keys
                    </Button>
                    <Button variant="outline" size="sm">
                      <Palette className="h-3 w-3 mr-1" />
                      White Label
                    </Button>
                    <Button variant="outline" size="sm">
                      <BarChart3 className="h-3 w-3 mr-1" />
                      Analytics
                    </Button>
                    <Button variant="outline" size="sm">
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="applications" className="space-y-4">
          <div className="grid gap-4">
            {applications.map((application) => (
              <Card key={application.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <FileText className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <CardTitle className="text-lg">{application.organizationName}</CardTitle>
                        <CardDescription>{application.contactInfo.primaryContact}</CardDescription>
                      </div>
                    </div>
                    <Badge className={getStatusColor(application.status)}>
                      {application.status.replace('_', ' ')}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Business Information</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Years in Business:</span>
                          <span className="font-medium">{application.businessInfo.yearsInBusiness}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Number of Events:</span>
                          <span className="font-medium">{application.businessInfo.numberOfEvents}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Avg Event Size:</span>
                          <span className="font-medium">{application.businessInfo.averageEventSize}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Partnership Interest</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Desired Tier:</span>
                          <Badge className={getTierColor(application.partnershipInterest.desiredTier)}>
                            {application.partnershipInterest.desiredTier}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Expected Clients:</span>
                          <span className="font-medium">{application.partnershipInterest.expectedClients}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">White Label Interest:</span>
                          <span className="font-medium">{application.partnershipInterest.whiteLabelInterest ? 'Yes' : 'No'}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {application.status === 'submitted' || application.status === 'under_review' ? (
                    <div className="flex justify-end gap-2 mt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleReviewApplication(application.id!, 'rejected', 'Application does not meet requirements')}
                      >
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        Reject
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleReviewApplication(application.id!, 'approved', 'Application approved for partnership program')}
                      >
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Approve
                      </Button>
                    </div>
                  ) : (
                    <div className="mt-4 p-3 bg-muted rounded-md">
                      <h4 className="font-medium mb-1">Review Notes</h4>
                      <p className="text-sm text-muted-foreground">{application.reviewNotes}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        Reviewed by {application.reviewedBy} on {application.reviewedAt?.toLocaleDateString()}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Sharing Overview</CardTitle>
              <CardDescription>Partner revenue sharing and payout information</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Revenue Sharing Dashboard</h3>
                <p className="text-muted-foreground mb-4">
                  Revenue sharing calculations and payout management
                </p>
                <Button>
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Calculate Payouts
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Partnership Tiers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {['platinum', 'gold', 'silver', 'bronze'].map((tier) => {
                    const tierPartners = partners.filter(p => p.partnershipTier === tier);
                    const count = tierPartners.length;
                    const percentage = partners.length > 0 ? (count / partners.length) * 100 : 0;

                    return (
                      <div key={tier} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge className={getTierColor(tier as ExhibitionOrganizerPartner['partnershipTier'])}>
                            {tier}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-20 bg-muted rounded-full h-2">
                            <div
                              className="bg-primary h-2 rounded-full"
                              style={{ width: `${percentage}%` }}
                            />
                          </div>
                          <span className="text-sm font-medium w-8 text-right">{count}</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Partner Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {partners.slice(0, 5).map((partner) => (
                    <div key={partner.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <h4 className="font-medium">{partner.organizationName}</h4>
                          <p className="text-sm text-muted-foreground">{partner.performance.clientsReferred} clients</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{formatCurrency(partner.performance.totalRevenue)}</div>
                        <div className="text-sm text-muted-foreground">revenue</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}