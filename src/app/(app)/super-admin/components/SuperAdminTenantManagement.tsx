"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Building2,
  Users,
  Search,
  Eye,
  Edit,
  Trash2,
  Plus,
  Filter,
  Activity
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { getCollection } from '@/services/firestoreService';
import { updateTenant, getTenantUsers, removeUserFromTenant, updateTenantUser } from '@/services/tenantService';
import { getTenantUsers as getTenantUsersFromTenantAware } from '@/services/tenantAwareFirestoreService';
import type { Tenant, TenantUser } from '@/types/firestore';
import { Timestamp } from 'firebase/firestore';

// Helper function to format Firestore dates
const formatFirestoreDate = (date: Date | Timestamp | string | undefined): string => {
  if (!date) return 'N/A';
  
  if (date instanceof Timestamp) {
    return date.toDate().toLocaleDateString();
  }
  
  if (date instanceof Date) {
    return date.toLocaleDateString();
  }
  
  if (typeof date === 'string') {
    return new Date(date).toLocaleDateString();
  }
  
  return 'N/A';
};

export default function SuperAdminTenantManagement() {
  const { user } = useAuth();
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);
  const [editingTenant, setEditingTenant] = useState(false);
  const [showUserManagement, setShowUserManagement] = useState(false);
  const [showAddUser, setShowAddUser] = useState(false);
  const [tenantUsers, setTenantUsers] = useState<TenantUser[]>([]);
  const [editForm, setEditForm] = useState<Partial<Tenant>>({});
  const [saving, setSaving] = useState(false);

  // Load all tenants
  useEffect(() => {
    loadTenants();
  }, []);

  const loadTenants = async () => {
    setLoading(true);
    try {
      const tenantsData = await getCollection('tenants');
      setTenants(tenantsData as Tenant[]);
    } catch (error) {
      console.error('Error loading tenants:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handler functions for tenant actions
  const handleEditTenant = () => {
    if (selectedTenant) {
      setEditForm({
        name: selectedTenant.name,
        slug: selectedTenant.slug,
        status: selectedTenant.status,
        plan: selectedTenant.plan,
        description: selectedTenant.description
      });
      setEditingTenant(true);
    }
  };

  const handleSaveTenant = async () => {
    if (!selectedTenant || !editForm) return;

    setSaving(true);
    try {
      await updateTenant(selectedTenant.id, editForm);

      // Update local state
      setSelectedTenant({ ...selectedTenant, ...editForm });
      setTenants(prev => prev.map(t =>
        t.id === selectedTenant.id ? { ...t, ...editForm } : t
      ));

      setEditingTenant(false);
      alert('Tenant updated successfully!');
    } catch (error) {
      console.error('Error updating tenant:', error);
      alert('Error updating tenant. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleManageUsers = async () => {
    setShowUserManagement(true);
    await loadTenantUsers();
  };

  const handleAddUser = () => {
    setShowAddUser(true);
  };

  const loadTenantUsers = async () => {
    if (!selectedTenant) return;

    try {
      // Try both possible locations for tenant users
      let usersData: TenantUser[] = [];

      try {
        // First try the tenant-aware service
        usersData = await getTenantUsersFromTenantAware(selectedTenant.id);
      } catch (error) {
        console.log('Trying alternative user loading method...');
        // Fallback to direct collection access
        const rawUsers = await getCollection(`tenants/${selectedTenant.id}/users`);
        usersData = rawUsers || [];
      }

      setTenantUsers(usersData);
    } catch (error) {
      console.error('Error loading tenant users:', error);
      setTenantUsers([]);
    }
  };

  const handleDeleteUser = async (userId: string, userEmail: string) => {
    if (!selectedTenant) return;

    // Prevent superman from deleting himself
    if (user?.email === userEmail) {
      alert('You cannot delete your own account!');
      return;
    }

    const confirmed = window.confirm(
      `Are you sure you want to remove ${userEmail} from this tenant? This action cannot be undone.`
    );

    if (confirmed) {
      try {
        await removeUserFromTenant(selectedTenant.id, userId);
        await loadTenantUsers(); // Reload users
        alert('User removed successfully!');
      } catch (error) {
        console.error('Error removing user:', error);
        alert('Error removing user. Please try again.');
      }
    }
  };

  const handleEditUser = async (userId: string, currentRole: string) => {
    if (!selectedTenant) return;

    const newRole = prompt(`Enter new role for user (current: ${currentRole}):`);
    if (!newRole || newRole === currentRole) return;

    try {
      await updateTenantUser(selectedTenant.id, userId, { role: newRole as any });
      await loadTenantUsers(); // Reload users
      alert('User role updated successfully!');
    } catch (error) {
      console.error('Error updating user:', error);
      alert('Error updating user. Please try again.');
    }
  };

  const handleSuspendTenant = async () => {
    if (!selectedTenant) return;

    const confirmed = window.confirm(
      `Are you sure you want to suspend tenant "${selectedTenant.name}"? This will disable access for all users.`
    );

    if (confirmed) {
      try {
        // TODO: Implement suspend tenant functionality
        console.log('Suspending tenant:', selectedTenant.id);
        alert('Suspend tenant functionality will be implemented here');
      } catch (error) {
        console.error('Error suspending tenant:', error);
        alert('Error suspending tenant');
      }
    }
  };

  // Filter tenants based on search and status
  const filteredTenants = tenants.filter(tenant => {
    const matchesSearch = tenant.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tenant.slug?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || tenant.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Super Admin - Tenant Management</CardTitle>
          <CardDescription>Loading tenant information...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (selectedTenant) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button variant="outline" onClick={() => setSelectedTenant(null)}>
              ← Back to Tenant List
            </Button>
            <div>
              <h2 className="text-2xl font-bold">Managing: {selectedTenant.name}</h2>
              <p className="text-muted-foreground">
                Tenant administration for {selectedTenant.slug}.evexa.com
              </p>
            </div>
          </div>
          <Badge variant={selectedTenant.status === 'active' ? 'default' : 'secondary'}>
            {selectedTenant.status}
          </Badge>
        </div>

        {/* Tenant Details Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Tenant Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2">Basic Information</h4>
                <div className="space-y-2 text-sm">
                  <div><strong>Name:</strong> {selectedTenant.name}</div>
                  <div><strong>Slug:</strong> {selectedTenant.slug}</div>
                  <div><strong>Status:</strong> {selectedTenant.status}</div>
                  <div><strong>Plan:</strong> {selectedTenant.plan}</div>
                  {selectedTenant.description && (
                    <div><strong>Description:</strong> {selectedTenant.description}</div>
                  )}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Metadata</h4>
                <div className="space-y-2 text-sm">
                  <div><strong>Created:</strong> {formatFirestoreDate(selectedTenant.createdAt)}</div>
                  <div><strong>Last Active:</strong> {formatFirestoreDate(selectedTenant.lastActiveAt)}</div>
                  <div><strong>Admin Users:</strong> {tenantUsers.filter(u => u.role === 'admin' || u.role === 'super_admin').length}</div>
                  <div><strong>Total Users:</strong> {tenantUsers.length}</div>
                </div>
              </div>
            </div>

            <div className="flex gap-2 pt-4">
              <Button variant="outline" size="sm" onClick={handleEditTenant}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Tenant
              </Button>
              <Button variant="outline" size="sm" onClick={handleManageUsers}>
                <Users className="h-4 w-4 mr-2" />
                Manage Users
              </Button>
              <Button variant="destructive" size="sm" onClick={handleSuspendTenant}>
                <Trash2 className="h-4 w-4 mr-2" />
                Suspend Tenant
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* User Management Section */}
        {showUserManagement && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  User Management
                </span>
                <Button variant="outline" size="sm" onClick={() => setShowUserManagement(false)}>
                  <Eye className="h-4 w-4 mr-2" />
                  Hide
                </Button>
              </CardTitle>
              <CardDescription>
                Manage users for {selectedTenant.name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Current Users ({tenantUsers.length})</h4>
                  <Button size="sm" onClick={handleAddUser}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add User
                  </Button>
                </div>

                {/* Current Users List */}
                <div className="space-y-3">
                  {tenantUsers.length > 0 ? (
                    tenantUsers.map((tenantUser, index) => (
                      <div key={tenantUser.id || index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                            <Users className="h-4 w-4" />
                          </div>
                          <div>
                            <p className="font-medium">{tenantUser.email}</p>
                            <p className="text-sm text-muted-foreground">{tenantUser.displayName || 'No display name'}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={tenantUser.role === 'admin' || tenantUser.role === 'super_admin' ? 'default' : 'secondary'}>
                            {tenantUser.role}
                          </Badge>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditUser(tenantUser.id || tenantUser.userId, tenantUser.role)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteUser(tenantUser.id || tenantUser.userId, tenantUser.email)}
                            disabled={tenantUser.email === user?.email} // Disable if it's the current user
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      No users found. Click "Add User" to invite users to this tenant.
                    </div>
                  )}
                </div>

                {/* Add User Form */}
                {showAddUser && (
                  <div className="border rounded-lg p-4 bg-muted/50">
                    <h5 className="font-medium mb-3">Add New User</h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Email Address</Label>
                        <Input placeholder="<EMAIL>" />
                      </div>
                      <div className="space-y-2">
                        <Label>Display Name</Label>
                        <Input placeholder="John Doe" />
                      </div>
                      <div className="space-y-2">
                        <Label>Role</Label>
                        <Select defaultValue="user">
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="user">User</SelectItem>
                            <SelectItem value="admin">Admin</SelectItem>
                            <SelectItem value="manager">Manager</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label>Department</Label>
                        <Input placeholder="Marketing" />
                      </div>
                    </div>
                    <div className="flex gap-2 mt-4">
                      <Button size="sm">
                        Send Invitation
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => setShowAddUser(false)}>
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Edit Tenant Section */}
        {editingTenant && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Edit className="h-5 w-5" />
                  Edit Tenant
                </span>
                <Button variant="outline" size="sm" onClick={() => setEditingTenant(false)}>
                  <Eye className="h-4 w-4 mr-2" />
                  Hide
                </Button>
              </CardTitle>
              <CardDescription>
                Modify tenant settings for {selectedTenant.name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Tenant Name</Label>
                    <Input
                      value={editForm.name || ''}
                      onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Slug</Label>
                    <Input
                      value={editForm.slug || ''}
                      onChange={(e) => setEditForm(prev => ({ ...prev, slug: e.target.value }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Status</Label>
                    <Select
                      value={editForm.status || 'active'}
                      onValueChange={(value) => setEditForm(prev => ({ ...prev, status: value as any }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="suspended">Suspended</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Plan</Label>
                    <Select
                      value={editForm.plan || 'development'}
                      onValueChange={(value) => setEditForm(prev => ({ ...prev, plan: value as any }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="development">Development</SelectItem>
                        <SelectItem value="basic">Basic</SelectItem>
                        <SelectItem value="professional">Professional</SelectItem>
                        <SelectItem value="enterprise">Enterprise</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Description</Label>
                  <Input
                    value={editForm.description || ''}
                    onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
                  />
                </div>
                <div className="flex gap-2">
                  <Button onClick={handleSaveTenant} disabled={saving}>
                    {saving ? 'Saving...' : 'Save Changes'}
                  </Button>
                  <Button variant="outline" onClick={() => setEditingTenant(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Additional Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Advanced Features</CardTitle>
            <CardDescription>
              Additional tenant management capabilities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                <Activity className="h-6 w-6" />
                <span>Usage Analytics</span>
              </Button>
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                <Building2 className="h-6 w-6" />
                <span>Billing & Subscription</span>
              </Button>
            </div>
            <div className="mt-4 text-sm text-muted-foreground">
              <p>These features will include:</p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Detailed usage analytics and reporting</li>
                <li>Billing history and subscription management</li>
                <li>Data export and backup options</li>
                <li>Security audit logs</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Super Admin - Tenant Management</h2>
          <p className="text-muted-foreground">
            Manage all tenants across the EVEXA platform
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Create New Tenant
        </Button>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="search">Search Tenants</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name or slug..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="status">Filter by Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tenants List */}
      <Card>
        <CardHeader>
          <CardTitle>All Tenants ({filteredTenants.length})</CardTitle>
          <CardDescription>
            Click on a tenant to manage its settings and users
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredTenants.map((tenant) => (
              <div key={tenant.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center">
                    <Building2 className="h-6 w-6" />
                  </div>
                  <div>
                    <h4 className="font-medium">{tenant.name}</h4>
                    <p className="text-sm text-muted-foreground">{tenant.slug}.evexa.com</p>
                    <p className="text-xs text-muted-foreground">
                      Created: {formatFirestoreDate(tenant.createdAt)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={tenant.status === 'active' ? 'default' : 'secondary'}>
                    {tenant.status}
                  </Badge>
                  <Badge variant="outline">
                    {tenant.plan}
                  </Badge>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setSelectedTenant(tenant)}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Manage
                  </Button>
                </div>
              </div>
            ))}
            {filteredTenants.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                No tenants found matching your criteria.
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
