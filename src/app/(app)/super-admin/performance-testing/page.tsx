/**
 * Performance Testing Page
 * Super Admin interface for comprehensive performance testing and optimization
 */

"use client";

import React from 'react';
import { Zap } from 'lucide-react';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import PerformanceTestingDashboard from '@/components/admin/PerformanceTestingDashboard';
import { SystemPermissionGate } from "@/components/permissions/PermissionGate";
import { UpgradePrompt } from "@/components/billing/UpgradePrompt";

export default function PerformanceTestingPage() {
  return (
    <SystemPermissionGate 
      requiredRole="super_admin"
      fallback={
        <UpgradePrompt 
          trigger="feature_locked" 
          feature="Performance Testing" 
          compact 
        />
      }
    >
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Zap className="h-8 w-8" />
            Performance Testing & Optimization
          </h1>
          <p className="text-muted-foreground mt-2">
            Comprehensive performance testing suite for query optimization, indexing validation, and multi-tenant scalability
          </p>
        </div>

        {/* Performance Testing Dashboard */}
        <PerformanceTestingDashboard />
      </div>
    </SystemPermissionGate>
  );
}
