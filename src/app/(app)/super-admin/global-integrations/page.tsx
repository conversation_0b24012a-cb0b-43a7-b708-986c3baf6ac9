"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import {
  Globe,
  Plus,
  Edit,
  Trash2,
  Building,
  Zap,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Star,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Eye,
  BarChart3,
  Shield,
  Link,
  Database,
  Settings
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/auth-context';
import {
  globalIntegrationService,
  type VenueIntegration,
  type APIPartnership,
  type ComplianceFramework,
  type GlobalMarket
} from '@/services/globalIntegrationService';
import { internationalComplianceService } from '@/services/internationalComplianceService';

export default function GlobalIntegrationsPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [venueIntegrations, setVenueIntegrations] = useState<VenueIntegration[]>([]);
  const [apiPartnerships, setApiPartnerships] = useState<APIPartnership[]>([]);
  const [complianceFrameworks, setComplianceFrameworks] = useState<ComplianceFramework[]>([]);
  const [globalMarkets, setGlobalMarkets] = useState<GlobalMarket[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('venues');
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const [newVenueIntegration, setNewVenueIntegration] = useState({
    venueName: '',
    venueType: 'convention_center' as VenueIntegration['venueType'],
    location: {
      country: '',
      city: '',
      address: ''
    },
    contactInfo: {
      primaryContact: '',
      email: '',
      phone: '',
      website: ''
    },
    integrationDetails: {
      authMethod: 'api_key' as const,
      supportedFeatures: [] as string[],
      dataMapping: {}
    },
    capabilities: {
      bookingManagement: false,
      floorPlanIntegration: false,
      capacityManagement: false,
      equipmentBooking: false,
      cateringIntegration: false,
      securityIntegration: false,
      parkingManagement: false,
      realTimeAvailability: false
    }
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [venuesData, partnershipsData, marketsData, complianceData] = await Promise.all([
        globalIntegrationService.getAllVenueIntegrations(),
        globalIntegrationService.getAllAPIPartnerships(),
        globalIntegrationService.getAllGlobalMarkets(),
        globalIntegrationService.getAllComplianceFrameworks()
      ]);
      setVenueIntegrations(venuesData);
      setApiPartnerships(partnershipsData);
      setGlobalMarkets(marketsData);
      setComplianceFrameworks(complianceData);
    } catch (error) {
      console.error('Failed to load global integration data:', error);
      toast({
        title: "Error",
        description: "Failed to load global integration data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateVenueIntegration = async () => {
    try {
      const integration = await globalIntegrationService.createVenueIntegration({
        ...newVenueIntegration,
        complianceInfo: {
          certifications: [],
          safetyStandards: [],
          accessibilityCompliance: [],
          environmentalCertifications: []
        },
        status: 'pending'
      });
      
      setVenueIntegrations(prev => [integration, ...prev]);
      setShowCreateDialog(false);
      
      toast({
        title: "Success",
        description: "Venue integration created successfully"
      });
    } catch (error) {
      console.error('Failed to create venue integration:', error);
      toast({
        title: "Error",
        description: "Failed to create venue integration",
        variant: "destructive"
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'testing': return 'bg-blue-100 text-blue-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getVenueTypeIcon = (type: VenueIntegration['venueType']) => {
    switch (type) {
      case 'convention_center': return <Building className="h-4 w-4" />;
      case 'hotel': return <Building className="h-4 w-4" />;
      case 'exhibition_hall': return <Building className="h-4 w-4" />;
      case 'outdoor_venue': return <MapPin className="h-4 w-4" />;
      case 'virtual_venue': return <Globe className="h-4 w-4" />;
      default: return <Building className="h-4 w-4" />;
    }
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-muted-foreground">You need to be authenticated to access this page.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Globe className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold">Global Integration & Partnerships</h1>
            <p className="text-muted-foreground">
              Manage venue integrations, API partnerships, and international compliance frameworks
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Integration
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create Venue Integration</DialogTitle>
                <DialogDescription>Add a new venue integration to the global network</DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="venueName">Venue Name</Label>
                    <Input
                      id="venueName"
                      value={newVenueIntegration.venueName}
                      onChange={(e) => setNewVenueIntegration(prev => ({ ...prev, venueName: e.target.value }))}
                      placeholder="Convention Center Name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="venueType">Venue Type</Label>
                    <Select 
                      value={newVenueIntegration.venueType} 
                      onValueChange={(value) => setNewVenueIntegration(prev => ({ 
                        ...prev, 
                        venueType: value as VenueIntegration['venueType'] 
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="convention_center">Convention Center</SelectItem>
                        <SelectItem value="hotel">Hotel</SelectItem>
                        <SelectItem value="exhibition_hall">Exhibition Hall</SelectItem>
                        <SelectItem value="outdoor_venue">Outdoor Venue</SelectItem>
                        <SelectItem value="virtual_venue">Virtual Venue</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="country">Country</Label>
                    <Input
                      id="country"
                      value={newVenueIntegration.location.country}
                      onChange={(e) => setNewVenueIntegration(prev => ({ 
                        ...prev, 
                        location: { ...prev.location, country: e.target.value }
                      }))}
                      placeholder="United States"
                    />
                  </div>
                  <div>
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={newVenueIntegration.location.city}
                      onChange={(e) => setNewVenueIntegration(prev => ({ 
                        ...prev, 
                        location: { ...prev.location, city: e.target.value }
                      }))}
                      placeholder="Las Vegas"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    value={newVenueIntegration.location.address}
                    onChange={(e) => setNewVenueIntegration(prev => ({ 
                      ...prev, 
                      location: { ...prev.location, address: e.target.value }
                    }))}
                    placeholder="123 Convention Blvd"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="primaryContact">Primary Contact</Label>
                    <Input
                      id="primaryContact"
                      value={newVenueIntegration.contactInfo.primaryContact}
                      onChange={(e) => setNewVenueIntegration(prev => ({ 
                        ...prev, 
                        contactInfo: { ...prev.contactInfo, primaryContact: e.target.value }
                      }))}
                      placeholder="John Smith"
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newVenueIntegration.contactInfo.email}
                      onChange={(e) => setNewVenueIntegration(prev => ({ 
                        ...prev, 
                        contactInfo: { ...prev.contactInfo, email: e.target.value }
                      }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateVenueIntegration}>
                    Create Integration
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Global Integration Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Venue Integrations</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{venueIntegrations.length}</div>
            <p className="text-xs text-muted-foreground">
              {venueIntegrations.filter(v => v.status === 'active').length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API Partnerships</CardTitle>
            <Link className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{apiPartnerships.length}</div>
            <p className="text-xs text-muted-foreground">
              {apiPartnerships.filter(p => p.status === 'active').length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Global Markets</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{globalMarkets.length}</div>
            <p className="text-xs text-muted-foreground">
              {globalMarkets.filter(m => m.status === 'active').length} active markets
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Compliance Score</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">92%</div>
            <p className="text-xs text-muted-foreground">
              Global compliance average
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="venues">Venue Integrations</TabsTrigger>
          <TabsTrigger value="api">API Partnerships</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
          <TabsTrigger value="markets">Global Markets</TabsTrigger>
        </TabsList>

        <TabsContent value="venues" className="space-y-4">
          <div className="grid gap-4">
            {venueIntegrations.map((venue) => (
              <Card key={venue.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getVenueTypeIcon(venue.venueType)}
                      <div>
                        <CardTitle className="text-lg">{venue.venueName}</CardTitle>
                        <CardDescription>{venue.location.city}, {venue.location.country}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(venue.status)}>
                        {venue.status}
                      </Badge>
                      <Badge variant="outline">
                        {venue.venueType.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Contact Information</h4>
                      <div className="space-y-1 text-sm text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <Mail className="h-3 w-3" />
                          {venue.contactInfo.email}
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="h-3 w-3" />
                          {venue.contactInfo.phone}
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-3 w-3" />
                          {venue.location.address}
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Capabilities</h4>
                      <div className="space-y-1 text-sm">
                        {Object.entries(venue.capabilities).filter(([_, enabled]) => enabled).map(([capability, _]) => (
                          <div key={capability} className="flex items-center gap-2">
                            <CheckCircle className="h-3 w-3 text-green-600" />
                            <span className="capitalize">{capability.replace(/([A-Z])/g, ' $1').trim()}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Integration Details</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Auth Method:</span>
                          <span className="font-medium">{venue.integrationDetails.authMethod}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Features:</span>
                          <span className="font-medium">{venue.integrationDetails.supportedFeatures.length}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Version:</span>
                          <span className="font-medium">{venue.metadata.integrationVersion}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end gap-2 mt-4">
                    <Button variant="outline" size="sm">
                      <Activity className="h-3 w-3 mr-1" />
                      Test Connection
                    </Button>
                    <Button variant="outline" size="sm">
                      <Settings className="h-3 w-3 mr-1" />
                      Configure
                    </Button>
                    <Button variant="outline" size="sm">
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="api" className="space-y-4">
          <div className="grid gap-4">
            {apiPartnerships.map((partnership) => (
              <Card key={partnership.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Link className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <CardTitle className="text-lg">{partnership.partnerName}</CardTitle>
                        <CardDescription>{partnership.region} • {partnership.partnerType.replace('_', ' ')}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(partnership.status)}>
                        {partnership.status}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">API Details</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Version:</span>
                          <span className="font-medium">{partnership.apiDetails.version}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Auth:</span>
                          <span className="font-medium">{partnership.apiDetails.authMethod}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Endpoints:</span>
                          <span className="font-medium">{partnership.apiDetails.endpoints.length}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Performance</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Uptime:</span>
                          <span className="font-medium">{partnership.performance.uptime.toFixed(1)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Response Time:</span>
                          <span className="font-medium">{partnership.performance.averageResponseTime.toFixed(0)}ms</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Error Rate:</span>
                          <span className="font-medium">{(partnership.performance.errorRate * 100).toFixed(2)}%</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Data Exchange</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Sync:</span>
                          <span className="font-medium">{partnership.dataExchange.syncFrequency}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Format:</span>
                          <span className="font-medium">{partnership.dataExchange.dataFormat.toUpperCase()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Last Sync:</span>
                          <span className="font-medium">{partnership.performance.lastHealthCheck.toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end gap-2 mt-4">
                    <Button variant="outline" size="sm">
                      <Activity className="h-3 w-3 mr-1" />
                      Health Check
                    </Button>
                    <Button variant="outline" size="sm">
                      <Database className="h-3 w-3 mr-1" />
                      Sync Data
                    </Button>
                    <Button variant="outline" size="sm">
                      <Edit className="h-3 w-3 mr-1" />
                      Configure
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-4">
          <div className="grid gap-4">
            {complianceFrameworks.map((framework) => (
              <Card key={framework.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Shield className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <CardTitle className="text-lg">{framework.name}</CardTitle>
                        <CardDescription>{framework.region} • {framework.certificationBody}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(framework.status)}>
                        {framework.status}
                      </Badge>
                      <Badge variant="outline">
                        Score: {framework.complianceScore}%
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Description</h4>
                      <p className="text-sm text-muted-foreground">{framework.description}</p>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Requirements</h4>
                      <div className="space-y-1 text-sm">
                        {framework.requirements.slice(0, 3).map((requirement, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <CheckCircle className="h-3 w-3 text-green-600" />
                            <span>{requirement}</span>
                          </div>
                        ))}
                        {framework.requirements.length > 3 && (
                          <div className="text-xs text-muted-foreground">
                            +{framework.requirements.length - 3} more requirements
                          </div>
                        )}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Audit Information</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Last Audit:</span>
                          <span className="font-medium">{framework.lastAuditDate.toLocaleDateString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Next Audit:</span>
                          <span className="font-medium">{framework.nextAuditDate.toLocaleDateString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Compliance Score:</span>
                          <span className="font-medium">{framework.complianceScore}%</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Compliance Progress</span>
                      <span className="text-sm text-muted-foreground">{framework.complianceScore}%</span>
                    </div>
                    <Progress value={framework.complianceScore} className="h-2" />
                  </div>

                  <div className="flex justify-end gap-2 mt-4">
                    <Button variant="outline" size="sm">
                      <Eye className="h-3 w-3 mr-1" />
                      View Details
                    </Button>
                    <Button variant="outline" size="sm">
                      <Activity className="h-3 w-3 mr-1" />
                      Run Assessment
                    </Button>
                    <Button variant="outline" size="sm">
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}

            {complianceFrameworks.length === 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>International Compliance Frameworks</CardTitle>
                  <CardDescription>Manage compliance requirements across different regions</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No Compliance Frameworks</h3>
                    <p className="text-muted-foreground mb-4">
                      Add international compliance frameworks and certifications
                    </p>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Framework
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="markets" className="space-y-4">
          <div className="grid gap-4">
            {globalMarkets.map((market) => (
              <Card key={market.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Globe className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <CardTitle className="text-lg">{market.region}</CardTitle>
                        <CardDescription>{market.countries.join(', ')}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(market.status)}>
                        {market.status}
                      </Badge>
                      <Badge variant="outline">
                        {market.marketInfo.marketMaturity}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Market Information</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Market Size:</span>
                          <span className="font-medium">${market.marketInfo.marketSize.toLocaleString()}M</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Growth Rate:</span>
                          <span className="font-medium">{market.marketInfo.growthRate}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Competitors:</span>
                          <span className="font-medium">{market.marketInfo.competitorCount}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Localization</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Languages:</span>
                          <span className="font-medium">{market.localization.languages.length}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Currencies:</span>
                          <span className="font-medium">{market.localization.currencies.join(', ')}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Time Zones:</span>
                          <span className="font-medium">{market.localization.timeZones.length}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Requirements</h4>
                      <div className="space-y-1 text-sm">
                        {Object.entries(market.businessRequirements).filter(([_, required]) => required).map(([requirement, _]) => (
                          <div key={requirement} className="flex items-center gap-2">
                            <CheckCircle className="h-3 w-3 text-green-600" />
                            <span className="capitalize">{requirement.replace(/([A-Z])/g, ' $1').trim()}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end gap-2 mt-4">
                    <Button variant="outline" size="sm">
                      <BarChart3 className="h-3 w-3 mr-1" />
                      Market Analysis
                    </Button>
                    <Button variant="outline" size="sm">
                      <Target className="h-3 w-3 mr-1" />
                      Opportunities
                    </Button>
                    <Button variant="outline" size="sm">
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
