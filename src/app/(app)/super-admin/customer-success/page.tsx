"use client";

import React, { useState, useEffect } from 'react';
import { Heart, Crown, TrendingUp, Users, CheckCircle, AlertTriangle, BarChart3, Target } from 'lucide-react';
import CustomerSuccessDashboard from '@/components/customer-success/CustomerSuccessDashboard';
import { useAuth } from '@/contexts/auth-context';
import { useTenantAware } from '@/hooks/useTenantAware';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2 } from 'lucide-react';
import { isSuperAdmin, getAllTenants } from '@/services/superAdminService';

import { useToast } from '@/hooks/use-toast';
import type { Tenant } from '@/types/firestore';

export default function SuperAdminCustomerSuccessPage() {
  const { user, loading } = useAuth();
  const { currentTenant } = useTenantAware();
  const { toast } = useToast();
  const [dataLoading, setDataLoading] = useState(true);
  const [customerSuccessData, setCustomerSuccessData] = useState<any>(null);
  const [tenants, setTenants] = useState<Tenant[]>([]);

  useEffect(() => {
    if (user && isSuperAdmin(user.id)) {
      loadCustomerSuccessData();
    }
  }, [user]);

  const loadCustomerSuccessData = async () => {
    try {
      setDataLoading(true);

      // Get real tenant data
      const tenantsData = await getAllTenants();

      // Calculate REAL customer success metrics from Firebase data
      const activeTenants = tenantsData.filter(t => t.status === 'active');
      const totalTenants = tenantsData.length;

      // Calculate churn rate (tenants that became inactive in last 30 days)
      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      const recentlyInactive = tenantsData.filter(t =>
        t.status !== 'active' &&
        t.updated_at &&
        new Date(t.updated_at.toDate()) > thirtyDaysAgo
      );
      const churnRate = totalTenants > 0 ? (recentlyInactive.length / totalTenants) * 100 : 0;

      // Calculate average tenant age (days since creation)
      const avgTenantAge = totalTenants > 0 ?
        tenantsData.reduce((sum, t) => {
          const created = t.created_at ? new Date(t.created_at.toDate()) : new Date();
          const age = (now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24);
          return sum + age;
        }, 0) / totalTenants : 0;

      // Get REAL support ticket data from Firebase
      const { db } = await import('@/lib/firebase');
      const { collection, getDocs, query, where } = await import('firebase/firestore');

      const supportTicketsRef = collection(db, 'support_tickets');
      const ticketsSnapshot = await getDocs(supportTicketsRef);

      const allTickets = ticketsSnapshot.docs.map(doc => doc.data());
      const openTickets = allTickets.filter(ticket => ticket.status === 'open' || ticket.status === 'in_progress');
      const resolvedTickets = allTickets.filter(ticket => ticket.status === 'resolved' || ticket.status === 'closed');

      // Calculate real average response time from tickets
      const ticketsWithResponseTime = resolvedTickets.filter(ticket =>
        ticket.createdAt && ticket.firstResponseAt
      );

      let avgResponseTime = 'No data';
      if (ticketsWithResponseTime.length > 0) {
        const totalResponseTime = ticketsWithResponseTime.reduce((sum, ticket) => {
          const created = ticket.createdAt.toDate();
          const responded = ticket.firstResponseAt.toDate();
          return sum + (responded.getTime() - created.getTime());
        }, 0);

        const avgMs = totalResponseTime / ticketsWithResponseTime.length;
        const avgHours = Math.round((avgMs / (1000 * 60 * 60)) * 10) / 10;
        avgResponseTime = `${avgHours} hours`;
      }

      // Get real customer satisfaction from feedback collection
      const feedbackRef = collection(db, 'customer_feedback');
      const feedbackSnapshot = await getDocs(feedbackRef);

      let satisfaction = 0;
      if (!feedbackSnapshot.empty) {
        const feedbackData = feedbackSnapshot.docs.map(doc => doc.data());
        const ratings = feedbackData.filter(f => f.rating && f.rating > 0);
        if (ratings.length > 0) {
          satisfaction = Math.round((ratings.reduce((sum, f) => sum + f.rating, 0) / ratings.length) * 10) / 10;
        }
      }

      const customerSuccessData = {
        totalCustomers: totalTenants,
        activeCustomers: activeTenants.length,
        churnRate: Math.round(churnRate * 100) / 100,
        satisfaction: satisfaction || 0,
        avgTenantAge: Math.round(avgTenantAge),
        supportTickets: {
          open: openTickets.length,
          resolved: resolvedTickets.length,
          avgResponseTime
        }
      };

      setTenants(tenantsData);
      setCustomerSuccessData(customerSuccessData);
    } catch (error) {
      console.error('Error loading customer success data:', error);
      toast({
        title: "Error",
        description: "Failed to load customer success data",
        variant: "destructive"
      });
    } finally {
      setDataLoading(false);
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default: return <BarChart3 className="h-4 w-4 text-blue-500" />;
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!user) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Please log in to access customer success features.
        </AlertDescription>
      </Alert>
    );
  }

  // Check if user is super admin
  if (!isSuperAdmin(user.id)) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          You don't have permission to access super admin customer success features. Only super administrators can access this section.
        </AlertDescription>
      </Alert>
    );
  }

  if (dataLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-3">
          <Heart className="h-6 w-6" />
          <div>
            <h1 className="text-2xl font-bold">Customer Success Management</h1>
            <p className="text-muted-foreground">Loading customer success data...</p>
          </div>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Heart className="h-6 w-6" />
        <div>
          <h1 className="text-2xl font-bold">Customer Success Management</h1>
          <p className="text-muted-foreground">
            Platform-wide customer success monitoring, health tracking, and engagement management across all tenants
          </p>
        </div>
        <Badge variant="outline" className="ml-auto bg-blue-50 text-blue-700 border-blue-200">
          <Crown className="h-3 w-3 mr-1" />
          Super Admin View
        </Badge>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Tenants</p>
                <p className="text-2xl font-bold">{tenants.length}</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Health Score</p>
                <p className={`text-2xl font-bold ${getHealthScoreColor(customerSuccessData?.healthScores?.[0]?.score || 0)}`}>
                  {customerSuccessData?.healthScores?.[0]?.score || 0}
                </p>
              </div>
              <Heart className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Support Tickets</p>
                <p className="text-2xl font-bold">{customerSuccessData?.supportMetrics?.totalTickets || 0}</p>
                <p className="text-xs text-green-600">
                  {customerSuccessData?.supportMetrics?.resolvedTickets || 0} resolved
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Satisfaction</p>
                <p className="text-2xl font-bold">{customerSuccessData?.supportMetrics?.satisfactionScore || 0}</p>
                <p className="text-xs text-muted-foreground">out of 5.0</p>
              </div>
              <Target className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="health-scores">Health Scores</TabsTrigger>
          <TabsTrigger value="onboarding">Onboarding</TabsTrigger>
          <TabsTrigger value="support">Support Metrics</TabsTrigger>
          <TabsTrigger value="detailed">Detailed Dashboard</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-blue-800">Platform Customer Success Overview</CardTitle>
              <CardDescription className="text-blue-600">
                Monitor customer success metrics across all EVEXA tenants and client organizations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border border-blue-200 rounded-lg bg-white">
                  <div className="text-2xl font-bold text-blue-600">Platform-wide</div>
                  <div className="text-sm text-blue-500">Customer Success</div>
                </div>
                <div className="text-center p-4 border border-blue-200 rounded-lg bg-white">
                  <div className="text-2xl font-bold text-green-600">All Tenants</div>
                  <div className="text-sm text-green-500">Health Monitoring</div>
                </div>
                <div className="text-center p-4 border border-blue-200 rounded-lg bg-white">
                  <div className="text-2xl font-bold text-purple-600">Global</div>
                  <div className="text-sm text-purple-500">Success Metrics</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Support Overview</CardTitle>
                <CardDescription>Platform-wide support metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Resolution Rate</span>
                    <span className="font-medium">
                      {customerSuccessData?.supportMetrics ?
                        Math.round((customerSuccessData.supportMetrics.resolvedTickets / customerSuccessData.supportMetrics.totalTickets) * 100) : 0}%
                    </span>
                  </div>
                  <Progress value={customerSuccessData?.supportMetrics ?
                    Math.round((customerSuccessData.supportMetrics.resolvedTickets / customerSuccessData.supportMetrics.totalTickets) * 100) : 0} />

                  <div className="flex justify-between items-center">
                    <span className="text-sm">Avg Resolution Time</span>
                    <span className="font-medium">{customerSuccessData?.supportMetrics?.averageResolutionTime || 0}h</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm">Customer Satisfaction</span>
                    <span className="font-medium">{customerSuccessData?.supportMetrics?.satisfactionScore || 0}/5.0</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Tenant Health Summary</CardTitle>
                <CardDescription>Health scores across all tenants</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {customerSuccessData?.healthScores?.map((tenant: any) => (
                    <div key={tenant.tenantId} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-sm">{tenant.tenantName}</h4>
                        <div className="flex items-center gap-2">
                          {getTrendIcon(tenant.trend)}
                          <span className={`font-bold ${getHealthScoreColor(tenant.score)}`}>
                            {tenant.score}
                          </span>
                        </div>
                      </div>
                      <Progress value={tenant.score} className="mb-2" />
                      <div className="grid grid-cols-4 gap-2 text-xs">
                        <div className="text-center">
                          <div className="font-medium">{tenant.factors.usage}</div>
                          <div className="text-muted-foreground">Usage</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium">{tenant.factors.engagement}</div>
                          <div className="text-muted-foreground">Engagement</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium">{tenant.factors.support}</div>
                          <div className="text-muted-foreground">Support</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium">{tenant.factors.adoption}</div>
                          <div className="text-muted-foreground">Adoption</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="detailed" className="space-y-4">
          <CustomerSuccessDashboard
            tenantId={currentTenant?.id || 'platform-wide'}
            userRole="super_admin"
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
