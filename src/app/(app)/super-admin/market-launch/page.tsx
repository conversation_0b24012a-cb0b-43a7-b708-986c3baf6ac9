"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Rocket, 
  Target, 
  Handshake, 
  DollarSign, 
  Users, 
  TrendingUp,
  Calendar,
  Mail,
  CheckCircle,
  AlertTriangle,
  Clock,
  BarChart3,
  Globe,
  Star,
  Activity,
  Zap
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { useTenant } from '@/contexts/tenant-context';
import PartnershipManagement from '@/components/market-launch/PartnershipManagement';
import ClientAcquisition from '@/components/market-launch/ClientAcquisition';
import LaunchMarketing from '@/components/market-launch/LaunchMarketing';
import { launchMetricsService, type LaunchMetrics, type LaunchTask } from '@/services/launchMetricsService';
import Link from 'next/link';

export default function MarketLaunchPage() {
  const { user } = useAuth();
  const { tenant } = useTenant();
  
  const [launchMetrics, setLaunchMetrics] = useState<LaunchMetrics | null>(null);
  const [launchTasks, setLaunchTasks] = useState<LaunchTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadLaunchData();
  }, []);

  const loadLaunchData = async () => {
    try {
      setLoading(true);

      // Load real launch metrics from Firebase
      const [metrics, tasks] = await Promise.all([
        launchMetricsService.calculateLaunchMetrics(),
        launchMetricsService.getAllLaunchTasks()
      ]);

      setLaunchMetrics(metrics);
      setLaunchTasks(tasks);

    } catch (error) {
      console.error('Failed to load launch data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'blocked': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Rocket className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-muted-foreground">You need to be authenticated to access this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Rocket className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold">Market Launch Center</h1>
            <p className="text-muted-foreground">
              Phase 4: Market Launch & Partnership (Weeks 13-16) - Command Center
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button onClick={loadLaunchData} disabled={loading} variant="outline">
            <Activity className="h-4 w-4 mr-2" />
            {loading ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button>
            <Target className="h-4 w-4 mr-2" />
            Launch Checklist
          </Button>
        </div>
      </div>

      {/* Launch Readiness Alert */}
      {launchMetrics && (
        <Alert className={launchMetrics.readinessScore >= 90 ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50'}>
          <Rocket className="h-4 w-4" />
          <AlertDescription>
            <div className="flex items-center justify-between">
              <span>
                Launch Readiness: <strong>{launchMetrics.readinessScore}%</strong>
                {launchMetrics.readinessScore >= 90 ? ' - Ready for Launch!' : ' - Preparation in Progress'}
              </span>
              <Progress value={launchMetrics.readinessScore} className="w-32" />
            </div>
          </AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Launch Overview</TabsTrigger>
          <TabsTrigger value="partnerships">Partnerships</TabsTrigger>
          <TabsTrigger value="clients">Client Acquisition</TabsTrigger>
          <TabsTrigger value="marketing">Marketing</TabsTrigger>
          <TabsTrigger value="operations">Operations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Key Metrics */}
          {launchMetrics && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Revenue Progress</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(launchMetrics.currentRevenue)}</div>
                  <p className="text-xs text-muted-foreground">
                    of {formatCurrency(launchMetrics.revenueTarget)} target
                  </p>
                  <Progress value={(launchMetrics.currentRevenue / launchMetrics.revenueTarget) * 100} className="mt-2" />
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Partnerships</CardTitle>
                  <Handshake className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{launchMetrics.partnershipsActive}</div>
                  <p className="text-xs text-muted-foreground">
                    Strategic partnerships active
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Clients Onboarded</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{launchMetrics.clientsOnboarded}</div>
                  <p className="text-xs text-muted-foreground">
                    New clients this quarter
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Lead Conversion</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{launchMetrics.leadConversion}%</div>
                  <p className="text-xs text-muted-foreground">
                    Lead to client conversion rate
                  </p>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Launch Tasks */}
          <Card>
            <CardHeader>
              <CardTitle>Launch Tasks</CardTitle>
              <CardDescription>Critical tasks for market launch preparation</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {launchTasks.map((task, index) => (
                  <div key={`${task.id}-${index}`} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{task.title}</h4>
                        <Badge className={getStatusColor(task.status)}>
                          {task.status.replace('_', ' ')}
                        </Badge>
                        <span className={`text-sm ${getPriorityColor(task.priority)}`}>
                          {task.priority} priority
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{task.description}</p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>Assignee: {task.assignee}</span>
                        <span>Due: {task.dueDate.toLocaleDateString()}</span>
                        <span>Category: {task.category}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {task.status === 'completed' && <CheckCircle className="h-5 w-5 text-green-600" />}
                      {task.status === 'in_progress' && <Clock className="h-5 w-5 text-blue-600" />}
                      {task.status === 'blocked' && <AlertTriangle className="h-5 w-5 text-red-600" />}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="partnerships" className="space-y-4">
          <PartnershipManagement onPartnershipUpdate={loadLaunchData} />
        </TabsContent>

        <TabsContent value="clients" className="space-y-4">
          <ClientAcquisition onMetricsUpdate={loadLaunchData} />
        </TabsContent>

        <TabsContent value="marketing" className="space-y-4">
          <LaunchMarketing onCampaignUpdate={loadLaunchData} />
        </TabsContent>

        <TabsContent value="operations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Operations Dashboard</CardTitle>
              <CardDescription>Operational readiness and system monitoring</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Operations Center</h3>
                <p className="text-muted-foreground mb-4">
                  System monitoring and operational readiness dashboard
                </p>
                <Button asChild>
                  <Link href="/super-admin/production-monitoring">
                    <Activity className="h-4 w-4 mr-2" />
                    View Monitoring
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
