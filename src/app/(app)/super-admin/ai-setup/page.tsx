"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Brain, Key, CheckCircle, ExternalLink, Zap, Crown, Shield } from 'lucide-react';
import { toast } from 'sonner';

export default function SuperAdminAISetupPage() {
  const [apiKey, setApiKey] = useState('');
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);

  const testGroqAPI = async () => {
    if (!apiKey.trim()) {
      toast.error('Please enter your Groq API key');
      return;
    }

    setTesting(true);
    setTestResult(null);

    try {
      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: 'llama3-8b-8192',
          max_tokens: 50,
          temperature: 0.7,
          messages: [
            {
              role: 'user',
              content: 'Hello! Please respond with "EVEXA AI is working perfectly!"'
            }
          ]
        })
      });

      if (response.ok) {
        const data = await response.json();
        setTestResult({
          success: true,
          response: data.choices[0]?.message?.content || 'Test successful'
        });
        
        // Store API key in localStorage for now
        localStorage.setItem('groq_api_key', apiKey);
        
        toast.success('AI setup completed successfully!');
      } else {
        const errorData = await response.json();
        setTestResult({
          success: false,
          error: errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`
        });
      }
    } catch (error) {
      setTestResult({
        success: false,
        error: error instanceof Error ? error.message : 'Network error occurred'
      });
    } finally {
      setTesting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Crown className="h-6 w-6 text-blue-600" />
          <Brain className="h-6 w-6 text-blue-600" />
          <h1 className="text-3xl font-bold">Super Admin AI Setup</h1>
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <Shield className="h-3 w-3 mr-1" />
            Platform Configuration
          </Badge>
        </div>
        <p className="text-muted-foreground">
          Configure AI providers and API keys for the entire EVEXA platform. This setup affects all tenants and users.
        </p>
      </div>

      {/* Platform Notice */}
      <Alert className="border-blue-200 bg-blue-50">
        <Shield className="h-4 w-4" />
        <AlertDescription className="text-blue-800">
          <strong>Super Admin Only:</strong> This configuration applies platform-wide and affects all tenant AI features. 
          Changes here will impact all users across all tenants.
        </AlertDescription>
      </Alert>

      {/* Setup Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Get Your Free Groq API Key
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-sm font-medium text-blue-600">1</div>
              <div>
                <p className="font-medium">Visit Groq Console</p>
                <p className="text-sm text-muted-foreground">Go to console.groq.com and create a free account</p>
                <Button variant="outline" size="sm" className="mt-2" asChild>
                  <a href="https://console.groq.com" target="_blank" rel="noopener noreferrer">
                    Open Groq Console <ExternalLink className="h-4 w-4 ml-1" />
                  </a>
                </Button>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-sm font-medium text-blue-600">2</div>
              <div>
                <p className="font-medium">Create API Key</p>
                <p className="text-sm text-muted-foreground">Navigate to API Keys section and create a new key</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-sm font-medium text-blue-600">3</div>
              <div>
                <p className="font-medium">Copy and Test</p>
                <p className="text-sm text-muted-foreground">Copy your API key and test it below</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* API Key Input */}
      <Card>
        <CardHeader>
          <CardTitle>Platform AI Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="api-key">Groq API Key</Label>
            <Input
              id="api-key"
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="gsk_..."
              className="font-mono"
            />
            <p className="text-xs text-muted-foreground">
              This API key will be used for all AI features across the platform
            </p>
          </div>

          <Button
            onClick={testGroqAPI}
            disabled={testing || !apiKey.trim()}
            className="w-full"
          >
            {testing ? (
              <>
                <Zap className="h-4 w-4 mr-2 animate-spin" />
                Testing Platform AI Connection...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Test Platform AI Connection
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResult && (
        <Card>
          <CardHeader>
            <CardTitle>Connection Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            {testResult.success ? (
              <div className="space-y-3">
                <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-sm text-green-800">
                    <strong>Success:</strong> {testResult.response}
                  </p>
                </div>
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    Perfect! Platform AI is now configured and ready. All tenants can now use EVEXA AI features including:
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Social media content generation</li>
                      <li>Budget predictions and analysis</li>
                      <li>Lead scoring and insights</li>
                      <li>Performance analytics</li>
                      <li>Virtual assistant (EVEXANA)</li>
                      <li>Workflow automation</li>
                    </ul>
                  </AlertDescription>
                </Alert>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-800">
                    <strong>Error:</strong> {testResult.error}
                  </p>
                </div>
                <Alert variant="destructive">
                  <AlertDescription>
                    Please check your API key and try again. Make sure you copied the complete key from Groq Console.
                  </AlertDescription>
                </Alert>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
