"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  CreditCard, 
  Users, 
  Zap, 
  Crown, 
  Building2,
  Check,
  X,
  TrendingUp,
  DollarSign,
  Calendar,
  Settings,
  Plus,
  Edit,
  Trash2
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, Loader2 } from 'lucide-react';
import { isSuperAdmin } from '@/services/superAdminService';

interface PlanFeature {
  name: string;
  included: boolean;
  limit?: string;
}

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: {
    monthly: number;
    yearly: number;
  };
  features: PlanFeature[];
  maxUsers: number;
  maxExhibitions: number;
  storage: string;
  support: string;
  popular?: boolean;
  enterprise?: boolean;
}

const subscriptionPlans: SubscriptionPlan[] = [
  {
    id: 'starter',
    name: 'Starter',
    description: 'Perfect for small teams getting started with exhibition management',
    price: { monthly: 29, yearly: 290 },
    maxUsers: 5,
    maxExhibitions: 10,
    storage: '10 GB',
    support: 'Email Support',
    features: [
      { name: 'Basic Exhibition Management', included: true },
      { name: 'Task Management', included: true },
      { name: 'Lead Tracking', included: true },
      { name: 'Basic Reports', included: true },
      { name: 'Email Integration', included: true },
      { name: 'Mobile App Access', included: false },
      { name: 'Advanced Analytics', included: false },
      { name: 'Custom Branding', included: false },
      { name: 'API Access', included: false },
      { name: 'Priority Support', included: false }
    ]
  },
  {
    id: 'professional',
    name: 'Professional',
    description: 'Advanced features for growing exhibition teams',
    price: { monthly: 79, yearly: 790 },
    maxUsers: 25,
    maxExhibitions: 50,
    storage: '100 GB',
    support: 'Priority Email & Chat',
    popular: true,
    features: [
      { name: 'Advanced Exhibition Management', included: true },
      { name: 'Task Management', included: true },
      { name: 'Lead Tracking & CRM', included: true },
      { name: 'Advanced Reports & Analytics', included: true },
      { name: 'Email Integration', included: true },
      { name: 'Mobile App Access', included: true },
      { name: 'Custom Branding', included: true },
      { name: 'Workflow Automation', included: true },
      { name: 'API Access', included: false },
      { name: 'Dedicated Support', included: false }
    ]
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'Complete solution for large organizations with advanced needs',
    price: { monthly: 199, yearly: 1990 },
    maxUsers: 100,
    maxExhibitions: -1, // Unlimited
    storage: '1 TB',
    support: 'Dedicated Success Manager',
    enterprise: true,
    features: [
      { name: 'Full Exhibition Management Suite', included: true },
      { name: 'Advanced Task & Project Management', included: true },
      { name: 'Complete CRM & Lead Management', included: true },
      { name: 'Custom Reports & Dashboards', included: true },
      { name: 'Full Integration Suite', included: true },
      { name: 'Mobile App Access', included: true },
      { name: 'White-label Branding', included: true },
      { name: 'Advanced Workflow Automation', included: true },
      { name: 'Full API Access', included: true },
      { name: 'Dedicated Success Manager', included: true }
    ]
  }
];

export default function SuperAdminPlansPage() {
  const { user, loading } = useAuth();
  const [activeTab, setActiveTab] = useState('plans');

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!user || !isSuperAdmin(user.id)) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          You don't have permission to access subscription plans management. Only super administrators can access this section.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <CreditCard className="h-6 w-6" />
        <div>
          <h1 className="text-2xl font-bold">Subscription Plans Management</h1>
          <p className="text-muted-foreground">
            Manage EVEXA subscription plans, pricing, and features for all tenants
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="plans">Current Plans</TabsTrigger>
          <TabsTrigger value="analytics">Plan Analytics</TabsTrigger>
          <TabsTrigger value="settings">Plan Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="plans" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {subscriptionPlans.map((plan) => (
              <Card key={plan.id} className={`relative ${plan.popular ? 'border-blue-500 shadow-lg' : ''} ${plan.enterprise ? 'border-purple-500' : ''}`}>
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-500 text-white">Most Popular</Badge>
                  </div>
                )}
                {plan.enterprise && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-purple-500 text-white">Enterprise</Badge>
                  </div>
                )}
                
                <CardHeader className="text-center">
                  <div className="flex justify-center mb-2">
                    {plan.id === 'starter' && <Users className="h-8 w-8 text-green-500" />}
                    {plan.id === 'professional' && <Zap className="h-8 w-8 text-blue-500" />}
                    {plan.id === 'enterprise' && <Crown className="h-8 w-8 text-purple-500" />}
                  </div>
                  <CardTitle className="text-xl">{plan.name}</CardTitle>
                  <CardDescription className="text-sm">{plan.description}</CardDescription>
                  <div className="mt-4">
                    <div className="text-3xl font-bold">${plan.price.monthly}</div>
                    <div className="text-sm text-muted-foreground">per month</div>
                    <div className="text-xs text-green-600">Save ${(plan.price.monthly * 12) - plan.price.yearly}/year</div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Max Users:</span>
                      <span className="font-medium">{plan.maxUsers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Exhibitions:</span>
                      <span className="font-medium">{plan.maxExhibitions === -1 ? 'Unlimited' : plan.maxExhibitions}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Storage:</span>
                      <span className="font-medium">{plan.storage}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Support:</span>
                      <span className="font-medium">{plan.support}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        {feature.included ? (
                          <Check className="h-4 w-4 text-green-500" />
                        ) : (
                          <X className="h-4 w-4 text-red-500" />
                        )}
                        <span className={feature.included ? '' : 'text-muted-foreground'}>
                          {feature.name}
                        </span>
                      </div>
                    ))}
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    <Button variant="outline" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Create New Plan
              </CardTitle>
              <CardDescription>
                Add a new subscription plan with custom features and pricing
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Plan
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Subscriptions</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,234</div>
                <p className="text-xs text-muted-foreground">
                  +12% from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$89,432</div>
                <p className="text-xs text-muted-foreground">
                  +8% from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Churn Rate</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2.4%</div>
                <p className="text-xs text-muted-foreground">
                  -0.3% from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg. LTV</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$2,847</div>
                <p className="text-xs text-muted-foreground">
                  +15% from last month
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Plan Distribution</CardTitle>
              <CardDescription>Current subscription distribution across plans</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span>Starter</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">45%</span>
                    <span className="font-medium">556 subscriptions</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span>Professional</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">40%</span>
                    <span className="font-medium">494 subscriptions</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                    <span>Enterprise</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">15%</span>
                    <span className="font-medium">184 subscriptions</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Plan Configuration</CardTitle>
              <CardDescription>Global settings for subscription plans and billing</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center py-8 text-muted-foreground">
                <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Plan configuration settings coming soon.</p>
                <p className="text-sm">Configure billing cycles, trial periods, and plan features.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
