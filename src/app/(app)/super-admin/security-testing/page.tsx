/**
 * Security Testing Page
 * Comprehensive security testing interface for super admins
 */

"use client";

import React from 'react';
import { Shield } from 'lucide-react';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import SecurityTestingSuite from '@/components/admin/SecurityTestingSuite';
import SecurityReportDashboard from '@/components/admin/SecurityReportDashboard';
import { SystemPermissionGate } from "@/components/permissions/PermissionGate";
import { UpgradePrompt } from "@/components/billing/UpgradePrompt";

export default function SecurityTestingPage() {
  return (
    <SystemPermissionGate 
      systemPermission="canManageSettings"
      fallback={
        <div className="container mx-auto py-8">
          <UpgradePrompt 
            trigger="feature_locked"
            feature="Security Testing"
            description="Access to security testing requires super admin permissions."
          />
        </div>
      }
    >
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="h-8 w-8" />
            Security Testing & Monitoring
          </h1>
          <p className="text-muted-foreground mt-2">
            Comprehensive security testing suite and monitoring dashboard for tenant isolation, access controls, and data security
          </p>
        </div>

        {/* Security Testing Tabs */}
        <Tabs defaultValue="dashboard" className="w-full">
          <TabsList>
            <TabsTrigger value="dashboard">Security Dashboard</TabsTrigger>
            <TabsTrigger value="testing">Security Testing</TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-6">
            <SecurityReportDashboard />
          </TabsContent>

          <TabsContent value="testing" className="space-y-6">
            <SecurityTestingSuite />
          </TabsContent>
        </Tabs>
      </div>
    </SystemPermissionGate>
  );
}
