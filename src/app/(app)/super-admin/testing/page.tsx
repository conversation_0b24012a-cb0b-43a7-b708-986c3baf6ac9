/**
 * Comprehensive Testing Page
 * Complete testing interface for super admins
 */

"use client";

import React from 'react';
import { TestTube } from 'lucide-react';
import ComprehensiveTestingSuite from '@/components/testing/ComprehensiveTestingSuite';
import { SystemPermissionGate } from "@/components/permissions/PermissionGate";
import { UpgradePrompt } from "@/components/billing/UpgradePrompt";

export default function TestingPage() {
  return (
    <SystemPermissionGate 
      systemPermission="canManageSettings"
      fallback={
        <div className="container mx-auto py-8">
          <UpgradePrompt 
            trigger="feature_locked"
            feature="Comprehensive Testing"
            description="Access to comprehensive testing requires super admin permissions."
          />
        </div>
      }
    >
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <TestTube className="h-8 w-8" />
            Comprehensive Testing
          </h1>
          <p className="text-muted-foreground mt-2">
            Complete testing suite for all modules and features to ensure functionality and integration
          </p>
        </div>

        {/* Comprehensive Testing Suite */}
        <ComprehensiveTestingSuite />
      </div>
    </SystemPermissionGate>
  );
}
