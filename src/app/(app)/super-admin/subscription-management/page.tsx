"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  CreditCard, 
  Plus, 
  Edit, 
  Trash2, 
  Users, 
  DollarSign,
  Calendar,
  TrendingUp,
  Star,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  BarChart3,
  Crown,
  Zap,
  Shield,
  Building
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/auth-context';
import { 
  subscriptionService, 
  type SubscriptionPlan, 
  type TenantSubscription 
} from '@/services/subscriptionService';

function SubscriptionManagementPage() {
  const { user } = useAuth();
  const { toast } = useToast();

  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [subscriptions, setSubscriptions] = useState<TenantSubscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('plans');
  const [editingPlan, setEditingPlan] = useState<SubscriptionPlan | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [formData, setFormData] = useState<Partial<SubscriptionPlan>>({});
  const [isCleaningUp, setIsCleaningUp] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [plansData, subscriptionsData] = await Promise.all([
        subscriptionService.getAllPlans(),
        subscriptionService.getAllActiveSubscriptions()
      ]);
      setPlans(plansData);
      setSubscriptions(subscriptionsData);
    } catch (error) {
      console.error('Error loading subscription data:', error);
      toast({
        title: "Error",
        description: "Failed to load subscription data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCleanupDuplicates = async () => {
    if (!confirm('This will remove duplicate subscription plans. Are you sure?')) {
      return;
    }

    try {
      setIsCleaningUp(true);
      await subscriptionService.cleanupDuplicatePlans();
      await loadData();
      toast({
        title: "Success",
        description: "Duplicate plans cleaned up successfully"
      });
    } catch (error) {
      console.error('Error cleaning up duplicates:', error);
      toast({
        title: "Error",
        description: "Failed to clean up duplicate plans",
        variant: "destructive"
      });
    } finally {
      setIsCleaningUp(false);
    }
  };

  const handleEditPlan = (plan: SubscriptionPlan) => {
    setEditingPlan(plan);
    setFormData(plan);
    setIsDialogOpen(true);
  };

  const handleCreatePlan = () => {
    setEditingPlan(null);
    setFormData({
      name: '',
      displayName: '',
      description: '',
      tier: 'starter',
      pricing: {
        monthly: 0,
        yearly: 0,
        currency: 'USD',
        yearlyDiscount: 0
      },
      features: {
        maxExhibitions: 1,
        maxEvents: 10,
        maxUsers: 5,
        maxTasks: 100,
        maxLeads: 500,
        maxVendors: 50,
        storageGB: 5,
        apiCallsPerMonth: 1000,
        emailsPerMonth: 1000,
        customBranding: false,
        advancedAnalytics: false,
        prioritySupport: false,
        whiteLabeling: false,
        apiAccess: false,
        customIntegrations: false,
        dedicatedAccount: false,
        ssoIntegration: false,
        auditLogs: false,
        dataExport: false,
        customReports: false,
        workflowAutomation: false,
        aiFeatures: false,
        mobileApp: false,
        offlineSync: false
      },
      addOns: [],
      isActive: true,
      isPopular: false,
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
    setIsDialogOpen(true);
  };

  const handleSavePlan = async () => {
    try {
      if (!formData.name || !formData.displayName) {
        toast({
          title: "Validation Error",
          description: "Plan name and display name are required",
          variant: "destructive"
        });
        return;
      }

      if (editingPlan) {
        await subscriptionService.updatePlan(editingPlan.id, formData);
        toast({
          title: "Success",
          description: "Plan updated successfully"
        });
      } else {
        await subscriptionService.createPlan(formData as Omit<SubscriptionPlan, 'id' | 'metadata'>);
        toast({
          title: "Success",
          description: "Plan created successfully"
        });
      }

      setIsDialogOpen(false);
      loadData();
    } catch (error) {
      console.error('Error saving plan:', error);
      toast({
        title: "Error",
        description: "Failed to save plan",
        variant: "destructive"
      });
    }
  };

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => {
      if (field.includes('.')) {
        const [parent, child] = field.split('.');
        return {
          ...prev,
          [parent]: {
            ...((prev as any)[parent] || {}),
            [child]: value
          }
        };
      }
      return { ...prev, [field]: value };
    });
  };

  const getTierIcon = (tier: SubscriptionPlan['tier']) => {
    switch (tier) {
      case 'free': return <Users className="h-5 w-5" />;
      case 'starter': return <Zap className="h-5 w-5" />;
      case 'professional': return <Star className="h-5 w-5" />;
      case 'enterprise': return <Crown className="h-5 w-5" />;
      case 'custom': return <Shield className="h-5 w-5" />;
      default: return <Users className="h-5 w-5" />;
    }
  };

  const getTierColor = (tier: SubscriptionPlan['tier']) => {
    switch (tier) {
      case 'free': return 'bg-gray-100 text-gray-800';
      case 'starter': return 'bg-blue-100 text-blue-800';
      case 'professional': return 'bg-purple-100 text-purple-800';
      case 'enterprise': return 'bg-yellow-100 text-yellow-800';
      case 'custom': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(amount);
  };

  const formatFeatureValue = (value: number | boolean, unit?: string) => {
    if (typeof value === 'boolean') {
      return value ? 'Yes' : 'No';
    }
    if (value === -1) {
      return 'Unlimited';
    }
    return `${value.toLocaleString()}${unit ? ` ${unit}` : ''}`;
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-muted-foreground">You need to be authenticated to access this page.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div>
      <h1>Subscription Management</h1>
    </div>
  );
}

export default SubscriptionManagementPage;
