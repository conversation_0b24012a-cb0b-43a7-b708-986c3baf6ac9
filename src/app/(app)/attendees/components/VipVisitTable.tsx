"use client";

import React, { useMemo } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableFilter, type AdvancedTableAction } from '@/components/ui/advanced-data-table';
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Edit2,
  Trash2,
  Eye,
  UserCheck,
  Calendar,
  Building,
  Users,
  Star,
  Archive,
  Download,
  MessageSquare,
  Phone,
  Mail
} from "lucide-react";
import type { VipVisit } from '@/types/firestore';
import { deleteVipVisitAction } from '../actions';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import { useRouter } from 'next/navigation';

interface VipVisitTableProps {
  visits: VipVisit[];
  onActionSuccess: () => void;
}

function formatDateDisplay(dateInput?: Timestamp | Date | string): string {
  if (!dateInput) return 'N/A';
  let date: Date;
  if (dateInput instanceof Timestamp) {
    date = dateInput.toDate();
  } else if (typeof dateInput === 'string' || dateInput instanceof Date) {
    date = new Date(dateInput);
  } else {
    return 'N/A';
  }
  if (isNaN(date.getTime())) return 'Invalid Date';
  return format(date, 'MMM dd, yyyy HH:mm');
}

export default function VipVisitTable({ visits, onActionSuccess }: VipVisitTableProps) {
  const { toast } = useToast();
  const router = useRouter();

  // Define columns
  const columns: AdvancedTableColumn<VipVisit>[] = useMemo(() => [
    {
      accessorKey: 'vipName',
      title: 'VIP Name',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const visit = row.original;
        return (
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarFallback>
                <UserCheck className="h-4 w-4" />
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{visit.vipName}</div>
              <div className="text-sm text-muted-foreground">VIP Guest</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'exhibitionName',
      title: 'Exhibition',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const visit = row.original;
        return (
          <div className="flex items-center gap-2">
            <Building className="h-4 w-4 text-muted-foreground" />
            <span>{visit.exhibitionName || visit.exhibitionId?.substring(0, 8) + '...' || 'N/A'}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'visitDateTime',
      title: 'Date & Time',
      sortable: true,
      cell: ({ row }) => (
        <div className="flex items-center gap-2 text-sm">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          {formatDateDisplay(row.original.visitDateTime)}
        </div>
      ),
    },
    {
      accessorKey: 'hostNames',
      title: 'Host(s)',
      sortable: false,
      filterable: true,
      cell: ({ row }) => {
        const visit = row.original;
        const hosts = visit.hostNames || [];
        return (
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <span className="max-w-xs truncate" title={hosts.join(', ')}>
              {hosts.length > 0 ? hosts.join(', ') : 'N/A'}
            </span>
          </div>
        );
      },
    },
  ], []);

  // Define filters
  const filters: AdvancedTableFilter[] = [
    {
      id: 'vipName',
      label: 'VIP Name',
      type: 'text',
      placeholder: 'Filter by VIP name...',
    },
    {
      id: 'exhibitionName',
      label: 'Exhibition',
      type: 'text',
      placeholder: 'Filter by exhibition...',
    },
  ];

  // Define bulk actions
  const bulkActions: AdvancedTableAction<VipVisit>[] = [
    {
      label: 'Export Selected',
      icon: Download,
      onClick: (selectedVisits) => {
        console.log('Exporting visits:', selectedVisits);
        toast({
          title: "Export Started",
          description: `Exporting ${selectedVisits.length} VIP visits...`,
        });
      },
      variant: 'default',
    },
    {
      label: 'Mark as Completed',
      icon: Star,
      onClick: (selectedVisits) => {
        console.log('Marking as completed:', selectedVisits);
        toast({
          title: "Status Updated",
          description: `${selectedVisits.length} visits marked as completed`,
        });
      },
      variant: 'default',
    },
    {
      label: 'Archive Selected',
      icon: Archive,
      onClick: (selectedVisits) => {
        console.log('Archiving visits:', selectedVisits);
        toast({
          title: "Visits Archived",
          description: `${selectedVisits.length} visits archived`,
        });
      },
      variant: 'secondary',
    },
    {
      label: 'Delete Selected',
      icon: Trash2,
      onClick: async (selectedVisits) => {
        if (confirm(`Are you sure you want to delete ${selectedVisits.length} VIP visits?`)) {
          // Implement bulk delete
          console.log('Deleting visits:', selectedVisits);
          toast({
            title: "Visits Deleted",
            description: `${selectedVisits.length} visits deleted`,
            variant: "destructive",
          });
        }
      },
      variant: 'destructive',
    },
  ];

  // Row actions
  const getRowActions = (visit: VipVisit) => [
    {
      type: 'view' as const,
      label: 'View Details',
      href: `/attendees/vip-visits/${visit.id}`,
    },
    {
      type: 'edit' as const,
      label: 'Edit Visit',
      href: `/attendees/vip-visits/${visit.id}/edit`,
    },
    {
      type: 'email' as const,
      label: 'Send Email',
      onClick: (visitData: VipVisit) => alert(`Sending email about ${visitData.vipName} visit`),
    },
    {
      type: 'call' as const,
      label: 'Call VIP',
      onClick: (visitData: VipVisit) => alert(`Calling ${visitData.vipName}`),
    },
    {
      type: 'message' as const,
      label: 'Send Message',
      onClick: (visitData: VipVisit) => alert(`Sending message about ${visitData.vipName} visit`),
    },
    {
      type: 'favorite' as const,
      label: 'Mark as Important',
      onClick: (visitData: VipVisit) => alert(`Marked ${visitData.vipName} visit as important`),
    },
    {
      type: 'archive' as const,
      label: 'Archive Visit',
      onClick: (visitData: VipVisit) => alert(`Archived ${visitData.vipName} visit`),
      variant: 'secondary' as const,
    },
    {
      type: 'delete' as const,
      label: 'Delete Visit',
      onClick: async (visitData: VipVisit) => {
        if (confirm(`Are you sure you want to delete the visit for ${visitData.vipName}?`)) {
          const result = await deleteVipVisitAction(visitData.id);
          if (result.success) {
            toast({
              title: "Visit Deleted",
              description: `${visitData.vipName} visit has been deleted`,
            });
            onActionSuccess();
          } else {
            toast({
              title: "Error",
              description: result.error || "Failed to delete visit",
              variant: "destructive",
            });
          }
        }
      },
      variant: 'destructive' as const,
    },
  ];

  // Mobile card renderer
  const mobileCardRenderer = (visit: VipVisit) => (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarFallback>
              <UserCheck className="h-4 w-4" />
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{visit.vipName}</div>
            <div className="text-sm text-muted-foreground">VIP Guest</div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span className="text-muted-foreground">Exhibition:</span>
          <span className="ml-2">{visit.exhibitionName || 'N/A'}</span>
        </div>
        <div>
          <span className="text-muted-foreground">Date:</span>
          <span className="ml-2">{formatDateDisplay(visit.visitDateTime)}</span>
        </div>
      </div>

      <div className="text-sm">
        <span className="text-muted-foreground">Hosts:</span>
        <span className="ml-2">{visit.hostNames?.join(', ') || 'N/A'}</span>
      </div>
    </div>
  );

  return (
    <AdvancedDataTable
      data={visits}
      columns={columns}
      loading={false}
      enableVirtualization={visits.length > 100}
      enableGlobalSearch={true}
      searchPlaceholder="Search VIP visits by name, exhibition, or host..."
      enableColumnFilters={true}
      filters={filters}
      enableRowSelection={true}
      enableColumnResizing={true}
      enableColumnVisibility={true}
      bulkActions={bulkActions}
      enableRowActions={true}
      rowActions={getRowActions}
      maxVisibleRowActions={3}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="vip-visits-export"
      mobileCardRenderer={mobileCardRenderer}
      onRowClick={(visit) => router.push(`/attendees/vip-visits/${visit.id}`)}
      onRefresh={onActionSuccess}
      variant="default"
      className="w-full"
      emptyMessage="No VIP visits scheduled. Click 'Add New Visit' to get started."
    />
  );
}