"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableFilter, type AdvancedTableAction } from '@/components/ui/advanced-data-table';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Edit2, 
  Trash2, 
  Eye, 
  UserCircle, 
  Thermometer, 
  Mail, 
  Phone, 
  Building,
  Calendar,
  Star,
  Archive,
  Download
} from "lucide-react";
import type { Lead, Exhibition, EvexUser } from '@/types/firestore';
import { getLeadsAction, deleteLeadAction } from '../actions';
import { getExhibitionsForSelect, getUsersForSelect } from '@/services/firestoreService';
import { useToast } from "@/hooks/use-toast";
import { format, parseISO } from "date-fns";
import { Timestamp } from 'firebase/firestore';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { LEAD_STATUSES, NO_ASSIGNEE_PLACEHOLDER } from '../schemas';

interface ModernLeadTableProps {
  refreshTrigger?: number;
}

function formatDateDisplay(dateInput?: Timestamp | Date | string): string {
  if (!dateInput) return 'N/A';
  let date: Date;
  if (dateInput instanceof Timestamp) {
    date = dateInput.toDate();
  } else if (typeof dateInput === 'string') {
    date = parseISO(dateInput);
  } else if (dateInput instanceof Date) {
    date = dateInput;
  } else {
    return 'N/A';
  }
  if (isNaN(date.getTime())) return 'Invalid Date';
  return format(date, 'MMM dd, yyyy HH:mm');
}

const getStatusBadgeVariant = (status?: Lead['status']): "default" | "secondary" | "destructive" | "outline" => {
  switch (status) {
    case 'new': return 'default';
    case 'contacted': return 'secondary';
    case 'qualified': return 'default';
    case 'converted': return 'default';
    case 'lost': return 'destructive';
    default: return 'outline';
  }
};

const getTemperatureBadge = (temperature?: Lead['temperature']) => {
  const config = {
    hot: { color: 'bg-red-500', label: 'Hot', icon: '🔥' },
    warm: { color: 'bg-orange-500', label: 'Warm', icon: '🌡️' },
    cold: { color: 'bg-blue-500', label: 'Cold', icon: '❄️' },
  };
  
  const temp = config[temperature as keyof typeof config] || { color: 'bg-gray-500', label: 'Unknown', icon: '❓' };
  
  return (
    <Badge variant="outline" className="gap-1">
      <span>{temp.icon}</span>
      {temp.label}
    </Badge>
  );
};

export function ModernLeadTable({ refreshTrigger }: ModernLeadTableProps) {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [exhibitions, setExhibitions] = useState<Exhibition[]>([]);
  const [users, setUsers] = useState<EvexUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const router = useRouter();

  // Load data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const [leadsResult, exhibitionsResult, usersResult] = await Promise.all([
          getLeadsAction(),
          getExhibitionsForSelect(),
          getUsersForSelect()
        ]);

        if (leadsResult.success) {
          setLeads(leadsResult.data || []);
        } else {
          throw new Error(leadsResult.error || 'Failed to load leads');
        }

        setExhibitions(exhibitionsResult);
        setUsers(usersResult);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        toast({
          title: "Error",
          description: "Failed to load leads data",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [refreshTrigger, toast]);

  // Create lookup maps
  const exhibitionsMap = useMemo(() => 
    exhibitions.reduce((acc, ex) => ({ ...acc, [ex.id]: ex }), {} as Record<string, Exhibition>),
    [exhibitions]
  );

  const usersMap = useMemo(() => 
    users.reduce((acc, user) => ({ ...acc, [user.id]: user }), {} as Record<string, EvexUser>),
    [users]
  );

  // Define columns
  const columns: AdvancedTableColumn<Lead>[] = useMemo(() => [
    {
      accessorKey: 'firstName',
      title: 'Contact',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const lead = row.original;
        return (
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={lead.profilePicture} />
              <AvatarFallback>
                {lead.firstName?.[0]}{lead.lastName?.[0]}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{lead.firstName} {lead.lastName}</div>
              <div className="text-sm text-muted-foreground">{lead.jobTitle || 'No title'}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'company',
      title: 'Company',
      sortable: true,
      filterable: true,
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Building className="h-4 w-4 text-muted-foreground" />
          <span>{row.original.company || 'N/A'}</span>
        </div>
      ),
    },
    {
      accessorKey: 'exhibitionId',
      title: 'Exhibition',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const exhibition = exhibitionsMap[row.original.exhibitionId || ''];
        return exhibition ? (
          <Link href={`/exhibitions/${exhibition.id}`} className="hover:underline text-primary text-sm">
            {exhibition.name}
          </Link>
        ) : (
          <span className="text-muted-foreground">N/A</span>
        );
      },
    },
    {
      accessorKey: 'temperature',
      title: 'Temperature',
      sortable: true,
      filterable: true,
      cell: ({ row }) => getTemperatureBadge(row.original.temperature),
    },
    {
      accessorKey: 'status',
      title: 'Status',
      sortable: true,
      filterable: true,
      cell: ({ row }) => (
        <Badge variant={getStatusBadgeVariant(row.original.status)}>
          {row.original.status || 'Unknown'}
        </Badge>
      ),
    },
    {
      accessorKey: 'assignedTo',
      title: 'Assigned To',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const user = usersMap[row.original.assignedTo || ''];
        return user ? (
          <div className="flex items-center gap-2">
            <Avatar className="h-6 w-6">
              <AvatarImage src={user.profilePicture} />
              <AvatarFallback className="text-xs">
                {user.firstName?.[0]}{user.lastName?.[0]}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm">{user.firstName} {user.lastName}</span>
          </div>
        ) : (
          <span className="text-muted-foreground text-sm">{NO_ASSIGNEE_PLACEHOLDER}</span>
        );
      },
    },
    {
      accessorKey: 'capturedAt',
      title: 'Captured At',
      sortable: true,
      cell: ({ row }) => (
        <div className="flex items-center gap-2 text-sm">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          {formatDateDisplay(row.original.capturedAt)}
        </div>
      ),
    },
  ], [exhibitionsMap, usersMap]);

  // Define filters
  const filters: AdvancedTableFilter[] = [
    {
      id: 'status',
      label: 'Status',
      type: 'select',
      options: LEAD_STATUSES.map(status => ({ label: status, value: status })),
      placeholder: 'All statuses',
    },
    {
      id: 'temperature',
      label: 'Temperature',
      type: 'select',
      options: [
        { label: 'Hot', value: 'hot' },
        { label: 'Warm', value: 'warm' },
        { label: 'Cold', value: 'cold' },
      ],
      placeholder: 'All temperatures',
    },
    {
      id: 'company',
      label: 'Company',
      type: 'text',
      placeholder: 'Filter by company...',
    },
  ];

  // Define bulk actions
  const bulkActions: AdvancedTableAction<Lead>[] = [
    {
      label: 'Export Selected',
      icon: Download,
      onClick: (selectedLeads) => {
        console.log('Exporting leads:', selectedLeads);
        toast({
          title: "Export Started",
          description: `Exporting ${selectedLeads.length} leads...`,
        });
      },
      variant: 'default',
    },
    {
      label: 'Mark as Qualified',
      icon: Star,
      onClick: (selectedLeads) => {
        console.log('Marking as qualified:', selectedLeads);
        toast({
          title: "Status Updated",
          description: `${selectedLeads.length} leads marked as qualified`,
        });
      },
      variant: 'default',
    },
    {
      label: 'Archive Selected',
      icon: Archive,
      onClick: (selectedLeads) => {
        console.log('Archiving leads:', selectedLeads);
        toast({
          title: "Leads Archived",
          description: `${selectedLeads.length} leads archived`,
        });
      },
      variant: 'secondary',
    },
    {
      label: 'Delete Selected',
      icon: Trash2,
      onClick: async (selectedLeads) => {
        if (confirm(`Are you sure you want to delete ${selectedLeads.length} leads?`)) {
          // Implement bulk delete
          console.log('Deleting leads:', selectedLeads);
          toast({
            title: "Leads Deleted",
            description: `${selectedLeads.length} leads deleted`,
            variant: "destructive",
          });
        }
      },
      variant: 'destructive',
    },
  ];

  // Mobile card renderer
  const mobileCardRenderer = (lead: Lead) => (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={lead.profilePicture} />
            <AvatarFallback>
              {lead.firstName?.[0]}{lead.lastName?.[0]}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{lead.firstName} {lead.lastName}</div>
            <div className="text-sm text-muted-foreground">{lead.company || 'No company'}</div>
          </div>
        </div>
        {getTemperatureBadge(lead.temperature)}
      </div>
      
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span className="text-muted-foreground">Status:</span>
          <Badge variant={getStatusBadgeVariant(lead.status)} className="ml-2">
            {lead.status || 'Unknown'}
          </Badge>
        </div>
        <div>
          <span className="text-muted-foreground">Captured:</span>
          <span className="ml-2">{formatDateDisplay(lead.capturedAt)}</span>
        </div>
      </div>
    </div>
  );

  return (
    <AdvancedDataTable
      data={leads}
      columns={columns}
      loading={loading}
      error={error}
      enableVirtualization={leads.length > 100}
      enableGlobalSearch={true}
      searchPlaceholder="Search leads by name, company, or email..."
      enableColumnFilters={true}
      filters={filters}
      enableRowSelection={true}
      enableColumnResizing={true}
      enableColumnVisibility={true}
      bulkActions={bulkActions}
      primaryAction={{
        label: "Add Lead",
        onClick: () => router.push('/attendees/leads/add'),
        icon: UserCircle
      }}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="leads-export"
      mobileCardRenderer={mobileCardRenderer}
      onRowClick={(lead) => router.push(`/attendees/leads/${lead.id}`)}
      onRefresh={() => window.location.reload()}
      variant="default"
      className="w-full"
      emptyMessage="No leads found. Start capturing leads at your exhibitions!"
    />
  );
}
