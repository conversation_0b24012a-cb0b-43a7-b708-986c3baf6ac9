"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, Upload, ArrowLeft, FileSignature } from "lucide-react";
import Link from 'next/link';
import ApprovalDocumentsTable from '../components/ApprovalDocumentsTable';

export default function ApprovalDocumentsPage() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button asChild variant="ghost" size="sm">
              <Link href="/approvals">
                <ArrowLeft className="mr-2 h-4 w-4" /> Back to Approvals
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold tracking-tight font-headline flex items-center">
            <FileText className="mr-3 h-8 w-8 text-primary" />
            Approval Documents
          </h1>
          <p className="text-muted-foreground">
            Manage all approval-related documents, signatures, and supporting materials.
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href="/approvals/documents/signatures">
              <FileSignature className="mr-2 h-4 w-4" /> Signatures
            </Link>
          </Button>
          <Button asChild>
            <Link href="/approvals/documents/upload">
              <Upload className="mr-2 h-4 w-4" /> Upload Document
            </Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Document Management</CardTitle>
        </CardHeader>
        <CardContent>
          <ApprovalDocumentsTable showApprovalFilter={true} />
        </CardContent>
      </Card>
    </div>
  );
}
