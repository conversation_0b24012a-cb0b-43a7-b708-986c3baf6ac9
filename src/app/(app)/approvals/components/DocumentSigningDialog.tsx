'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { FileSignature, PenTool, Download, Eye, Clock, CheckCircle, XCircle, Loader2, Users, Mail } from "lucide-react";
import { cn } from "@/lib/utils";
import type { SigningRequest, SigningRequestSigner } from '@/services/documentSigningService';
import { documentSigningService } from '@/services/documentSigningService';

interface DocumentSigningDialogProps {
  isOpen: boolean;
  onClose: () => void;
  documentId: string;
  documentName: string;
  documentUrl: string;
  documentType: 'approval' | 'contract' | 'vendor_document' | 'purchase_order' | 'other';
  requesterId: string;
  requesterName: string;
  onSigningComplete?: (signedDocumentUrl: string) => void;
}

interface SignatureCanvasProps {
  onSignatureChange: (signatureData: string) => void;
  width?: number;
  height?: number;
}

const SignatureCanvas: React.FC<SignatureCanvasProps> = ({ 
  onSignatureChange, 
  width = 400, 
  height = 200 
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set up canvas
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
  }, []);

  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.beginPath();
    ctx.moveTo(x, y);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.lineTo(x, y);
    ctx.stroke();
  };

  const stopDrawing = () => {
    if (!isDrawing) return;
    setIsDrawing(false);

    const canvas = canvasRef.current;
    if (!canvas) return;

    // Convert canvas to base64
    const signatureData = canvas.toDataURL('image/png');
    onSignatureChange(signatureData);
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    onSignatureChange('');
  };

  return (
    <div className="space-y-4">
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
        <canvas
          ref={canvasRef}
          width={width}
          height={height}
          className="border border-gray-200 rounded cursor-crosshair bg-white"
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={stopDrawing}
          onMouseLeave={stopDrawing}
        />
      </div>
      <div className="flex justify-between items-center">
        <p className="text-sm text-gray-600">Draw your signature above</p>
        <Button variant="outline" size="sm" onClick={clearSignature}>
          Clear
        </Button>
      </div>
    </div>
  );
};

export const DocumentSigningDialog: React.FC<DocumentSigningDialogProps> = ({
  isOpen,
  onClose,
  documentId,
  documentName,
  documentUrl,
  documentType,
  requesterId,
  requesterName,
  onSigningComplete
}) => {
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState<'setup' | 'sign' | 'review'>('setup');
  const [signers, setSigners] = useState<Omit<SigningRequestSigner, 'status' | 'remindersSent'>[]>([]);
  const [newSignerEmail, setNewSignerEmail] = useState('');
  const [newSignerName, setNewSignerName] = useState('');
  const [message, setMessage] = useState('');
  const [dueDate, setDueDate] = useState('');
  const [signatureData, setSignatureData] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [signingRequest, setSigningRequest] = useState<SigningRequest | null>(null);

  const addSigner = () => {
    if (!newSignerEmail || !newSignerName) {
      toast({
        title: "Missing Information",
        description: "Please enter both email and name for the signer.",
        variant: "destructive"
      });
      return;
    }

    const newSigner: Omit<SigningRequestSigner, 'status' | 'remindersSent'> = {
      id: `signer_${Date.now()}`,
      email: newSignerEmail,
      name: newSignerName,
      order: signers.length + 1
    };

    setSigners([...signers, newSigner]);
    setNewSignerEmail('');
    setNewSignerName('');
  };

  const removeSigner = (signerId: string) => {
    setSigners(signers.filter(s => s.id !== signerId));
  };

  const createSigningRequest = async () => {
    if (signers.length === 0) {
      toast({
        title: "No Signers",
        description: "Please add at least one signer.",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      const result = await documentSigningService.createSigningRequest({
        documentId,
        documentName,
        documentUrl,
        documentType,
        requesterId,
        requesterName,
        signers: signers.map(s => ({ ...s, status: 'pending', remindersSent: 0 })),
        message,
        dueDate: dueDate ? new Date(dueDate) : undefined,
        reminderSettings: {
          enabled: true,
          intervalDays: 3,
          maxReminders: 3
        }
      });

      if (result.success && result.requestId) {
        // Send the signing request
        const sendResult = await documentSigningService.sendSigningRequest(result.requestId);
        
        if (sendResult.success) {
          toast({
            title: "Signing Request Sent",
            description: "The document has been sent to all signers for signature."
          });
          
          // Load the created request
          const request = await documentSigningService.getSigningRequest(result.requestId);
          setSigningRequest(request);
          setCurrentStep('review');
        } else {
          throw new Error(sendResult.error || 'Failed to send signing request');
        }
      } else {
        throw new Error(result.error || 'Failed to create signing request');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to create signing request',
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const processSignature = async () => {
    if (!signatureData) {
      toast({
        title: "No Signature",
        description: "Please provide your signature.",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      // This would be called from a signing interface
      // For now, just show success
      toast({
        title: "Signature Processed",
        description: "Your signature has been recorded successfully."
      });
      
      if (onSigningComplete) {
        onSigningComplete(documentUrl); // In real implementation, this would be the signed document URL
      }
      
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process signature",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderSetupStep = () => (
    <div className="space-y-6">
      <div>
        <Label htmlFor="message">Message to Signers (Optional)</Label>
        <Textarea
          id="message"
          placeholder="Add a message for the signers..."
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          className="mt-1"
        />
      </div>

      <div>
        <Label htmlFor="dueDate">Due Date (Optional)</Label>
        <Input
          id="dueDate"
          type="date"
          value={dueDate}
          onChange={(e) => setDueDate(e.target.value)}
          className="mt-1"
        />
      </div>

      <div>
        <Label>Signers</Label>
        <div className="mt-2 space-y-2">
          {signers.map((signer) => (
            <div key={signer.id} className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium">{signer.name}</p>
                <p className="text-sm text-gray-600">{signer.email}</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeSigner(signer.id)}
              >
                <XCircle className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>

        <div className="mt-4 space-y-2">
          <div className="flex gap-2">
            <Input
              placeholder="Signer name"
              value={newSignerName}
              onChange={(e) => setNewSignerName(e.target.value)}
            />
            <Input
              placeholder="Signer email"
              type="email"
              value={newSignerEmail}
              onChange={(e) => setNewSignerEmail(e.target.value)}
            />
            <Button onClick={addSigner}>Add</Button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSignStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <FileSignature className="h-12 w-12 mx-auto text-blue-600 mb-4" />
        <h3 className="text-lg font-semibold">Sign Document</h3>
        <p className="text-gray-600">Please provide your signature below</p>
      </div>

      <SignatureCanvas onSignatureChange={setSignatureData} />
    </div>
  );

  const renderReviewStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <CheckCircle className="h-12 w-12 mx-auto text-green-600 mb-4" />
        <h3 className="text-lg font-semibold">Signing Request Created</h3>
        <p className="text-gray-600">The document has been sent to all signers</p>
      </div>

      {signingRequest && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Signers ({signingRequest.signers.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {signingRequest.signers.map((signer) => (
                <div key={signer.id} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{signer.name}</p>
                    <p className="text-sm text-gray-600">{signer.email}</p>
                  </div>
                  <Badge variant={signer.status === 'signed' ? 'default' : 'secondary'}>
                    {signer.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileSignature className="h-5 w-5" />
            Document Signing
          </DialogTitle>
          <DialogDescription>
            {currentStep === 'setup' && 'Set up document signing request'}
            {currentStep === 'sign' && 'Provide your digital signature'}
            {currentStep === 'review' && 'Review signing request status'}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Document:</span>
              <Button variant="outline" size="sm" asChild>
                <a href={documentUrl} target="_blank" rel="noopener noreferrer">
                  <Eye className="h-4 w-4 mr-1" />
                  View
                </a>
              </Button>
            </div>
            <p className="text-sm text-gray-600">{documentName}</p>
          </div>

          {currentStep === 'setup' && renderSetupStep()}
          {currentStep === 'sign' && renderSignStep()}
          {currentStep === 'review' && renderReviewStep()}
        </div>

        <DialogFooter>
          {currentStep === 'setup' && (
            <>
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button onClick={createSigningRequest} disabled={isLoading}>
                {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                Send for Signing
              </Button>
            </>
          )}
          {currentStep === 'sign' && (
            <>
              <Button variant="outline" onClick={() => setCurrentStep('setup')}>
                Back
              </Button>
              <Button onClick={processSignature} disabled={isLoading || !signatureData}>
                {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                Sign Document
              </Button>
            </>
          )}
          {currentStep === 'review' && (
            <Button onClick={onClose}>
              Close
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
