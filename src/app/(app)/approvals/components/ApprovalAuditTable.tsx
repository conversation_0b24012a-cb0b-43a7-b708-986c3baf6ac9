"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { 
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  FileText,
  User,
  Calendar,
  MessageSquare,
  AlertTriangle,
  ArrowRight,
  Download,
  ExternalLink,
  History,
  Shield,
  Activity,
  Edit,
  Trash2
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import { formatDistanceToNow, format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface ApprovalAuditEntry {
  id?: string;
  approvalId: string;
  approvalType: 'travel' | 'purchase' | 'marketing' | 'per_diem' | 'social_post' | 'content';
  action: 'submitted' | 'approved' | 'rejected' | 'escalated' | 'timeout' | 'cancelled' | 'modified';
  performedBy: string;
  performedByName?: string;
  performedAt: Timestamp | Date | string;
  previousStatus?: string;
  newStatus: string;
  comments?: string;
  metadata?: {
    ipAddress?: string;
    userAgent?: string;
    location?: string;
    reason?: string;
    escalatedTo?: string[];
  };
  createdAt?: Timestamp | Date | string;
}

interface ApprovalAuditTableProps {
  auditEntries: ApprovalAuditEntry[];
  isLoading?: boolean;
  onRefresh?: () => void;
  onViewDetails?: (entry: ApprovalAuditEntry) => void;
  showApprovalFilter?: boolean;
}

export default function ApprovalAuditTable({
  auditEntries,
  isLoading = false,
  onRefresh,
  onViewDetails,
  showApprovalFilter = true
}: ApprovalAuditTableProps) {
  const { toast } = useToast();
  const router = useRouter();

  // Format date helper
  const formatDate = (date: any) => {
    if (!date) return 'N/A';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return format(dateObj, 'MMM dd, yyyy HH:mm:ss');
  };

  // Action badge component
  const ActionBadge = ({ action }: { action: string }) => {
    const getActionConfig = (action: string) => {
      switch (action) {
        case 'submitted':
          return { variant: 'secondary' as const, icon: FileText, color: 'text-blue-600' };
        case 'approved':
          return { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' };
        case 'rejected':
          return { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' };
        case 'escalated':
          return { variant: 'outline' as const, icon: AlertTriangle, color: 'text-orange-600' };
        case 'timeout':
          return { variant: 'outline' as const, icon: Clock, color: 'text-gray-600' };
        case 'cancelled':
          return { variant: 'secondary' as const, icon: XCircle, color: 'text-gray-600' };
        case 'modified':
          return { variant: 'outline' as const, icon: Edit, color: 'text-purple-600' };
        default:
          return { variant: 'secondary' as const, icon: Activity, color: 'text-gray-600' };
      }
    };

    const config = getActionConfig(action);
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className={`h-3 w-3 ${config.color}`} />
        {action.charAt(0).toUpperCase() + action.slice(1)}
      </Badge>
    );
  };

  // Type badge component
  const TypeBadge = ({ type }: { type: string }) => {
    const getTypeColor = (type: string) => {
      switch (type) {
        case 'travel': return 'bg-blue-100 text-blue-800';
        case 'purchase': return 'bg-green-100 text-green-800';
        case 'marketing': return 'bg-purple-100 text-purple-800';
        case 'per_diem': return 'bg-yellow-100 text-yellow-800';
        case 'social_post': return 'bg-pink-100 text-pink-800';
        case 'content': return 'bg-indigo-100 text-indigo-800';
        default: return 'bg-gray-100 text-gray-800';
      }
    };

    return (
      <Badge variant="outline" className={`${getTypeColor(type)} border-0`}>
        {type.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  // Status change component
  const StatusChange = ({ previousStatus, newStatus }: { previousStatus?: string, newStatus: string }) => {
    if (!previousStatus) {
      return <span className="text-sm text-gray-600">{newStatus}</span>;
    }

    return (
      <div className="flex items-center gap-2 text-sm">
        <span className="text-gray-500">{previousStatus}</span>
        <ArrowRight className="h-3 w-3 text-gray-400" />
        <span className="font-medium">{newStatus}</span>
      </div>
    );
  };

  // Table columns
  const columns: ColumnDef<ApprovalAuditEntry>[] = [
    {
      accessorKey: "performedAt",
      header: "Timestamp",
      cell: ({ row }) => {
        const performedAt = row.original.performedAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(performedAt)}
          </div>
        );
      },
    },
    {
      accessorKey: "approvalId",
      header: "Approval ID",
      cell: ({ row }) => {
        const entry = row.original;
        return (
          <div className="flex items-center gap-2">
            <Shield className="h-4 w-4 text-gray-500" />
            <span className="font-mono text-sm">{entry.approvalId.slice(-8)}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "approvalType",
      header: "Type",
      cell: ({ row }) => <TypeBadge type={row.original.approvalType} />,
    },
    {
      accessorKey: "action",
      header: "Action",
      cell: ({ row }) => <ActionBadge action={row.original.action} />,
    },
    {
      accessorKey: "performedBy",
      header: "Performed By",
      cell: ({ row }) => {
        const entry = row.original;
        const displayName = entry.performedByName || entry.performedBy;
        
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-xs">
                {displayName.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm">{displayName}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "newStatus",
      header: "Status Change",
      cell: ({ row }) => (
        <StatusChange 
          previousStatus={row.original.previousStatus} 
          newStatus={row.original.newStatus} 
        />
      ),
    },
    {
      accessorKey: "comments",
      header: "Comments",
      cell: ({ row }) => {
        const comments = row.original.comments;
        if (!comments) return <span className="text-sm text-gray-400">—</span>;
        
        return (
          <div className="flex items-center gap-1">
            <MessageSquare className="h-3 w-3 text-gray-500" />
            <span className="text-sm text-gray-600 truncate max-w-xs" title={comments}>
              {comments}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "metadata",
      header: "Details",
      cell: ({ row }) => {
        const metadata = row.original.metadata;
        if (!metadata) return <span className="text-sm text-gray-400">—</span>;
        
        const details = [];
        if (metadata.reason) details.push(`Reason: ${metadata.reason}`);
        if (metadata.escalatedTo) details.push(`Escalated to: ${metadata.escalatedTo.join(', ')}`);
        if (metadata.location) details.push(`Location: ${metadata.location}`);
        
        if (details.length === 0) return <span className="text-sm text-gray-400">—</span>;
        
        return (
          <div className="text-sm text-gray-600 truncate max-w-xs" title={details.join(' | ')}>
            {details[0]}
          </div>
        );
      },
    },
  ];

  // Row actions
  const getRowActions = (entry: ApprovalAuditEntry) => [
    {
      type: 'view' as const,
      label: 'View Details',
      icon: <Eye className="h-4 w-4" />,
      onClick: () => onViewDetails?.(entry),
    },
    {
      type: 'external' as const,
      label: 'View Approval',
      icon: <ExternalLink className="h-4 w-4" />,
      onClick: () => router.push(`/approvals/${entry.approvalId}`),
    },
  ];

  // Filter options
  const filterOptions = [
    ...(showApprovalFilter ? [{
      key: 'approvalType',
      label: 'Type',
      options: [
        { label: 'Travel', value: 'travel' },
        { label: 'Purchase', value: 'purchase' },
        { label: 'Marketing', value: 'marketing' },
        { label: 'Per Diem', value: 'per_diem' },
        { label: 'Social Post', value: 'social_post' },
        { label: 'Content', value: 'content' },
      ]
    }] : []),
    {
      key: 'action',
      label: 'Action',
      options: [
        { label: 'Submitted', value: 'submitted' },
        { label: 'Approved', value: 'approved' },
        { label: 'Rejected', value: 'rejected' },
        { label: 'Escalated', value: 'escalated' },
        { label: 'Timeout', value: 'timeout' },
        { label: 'Cancelled', value: 'cancelled' },
        { label: 'Modified', value: 'modified' },
      ]
    },
  ];

  // Table actions
  const tableActions = [
    {
      label: 'Export Audit Log',
      icon: <Download className="h-4 w-4" />,
      onClick: () => {
        // Export functionality would be handled by the AdvancedDataTable
      },
      variant: 'outline' as const,
    },
  ];

  return (
    <AdvancedDataTable
      data={auditEntries}
      columns={columns}
      isLoading={isLoading}
      onRefresh={onRefresh}
      tableActions={tableActions}
      rowActions={getRowActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search by approval ID, user, comments..."
      emptyStateMessage="No audit entries found"
      emptyStateDescription="Approval audit trail will appear here as actions are performed."
      enableRowSelection={false}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="approval-audit-log"
      defaultSorting={[{ id: 'performedAt', desc: true }]}
    />
  );
}
