"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { 
  FileText,
  Download,
  Eye,
  Trash2,
  FileSignature,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Upload,
  Send,
  Edit
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { ApprovalDocument, SignatureStatus } from '@/types/firestore';
import { getApprovalDocumentsAction, deleteApprovalDocumentAction, createDocumentSigningRequestAction } from '../actions';
import { formatDistanceToNow } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

const documentTypeConfig = {
  'approval_form': { color: 'bg-blue-100 text-blue-800', label: 'Approval Form' },
  'supporting_document': { color: 'bg-green-100 text-green-800', label: 'Supporting Doc' },
  'contract': { color: 'bg-purple-100 text-purple-800', label: 'Contract' },
  'agreement': { color: 'bg-orange-100 text-orange-800', label: 'Agreement' },
  'other': { color: 'bg-gray-100 text-gray-800', label: 'Other' },
};

const signatureStatusConfig = {
  'Not Required': { color: 'bg-gray-100 text-gray-800', icon: FileText },
  'Pending Signature': { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
  'Partially Signed': { color: 'bg-blue-100 text-blue-800', icon: FileSignature },
  'Fully Signed': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
  'Signature Rejected': { color: 'bg-red-100 text-red-800', icon: XCircle },
  'Signature Expired': { color: 'bg-orange-100 text-orange-800', icon: AlertTriangle },
};

const approvalTypeConfig = {
  'travel': { color: 'bg-blue-100 text-blue-800', label: 'Travel' },
  'purchase': { color: 'bg-green-100 text-green-800', label: 'Purchase' },
  'marketing': { color: 'bg-purple-100 text-purple-800', label: 'Marketing' },
  'per_diem': { color: 'bg-orange-100 text-orange-800', label: 'Per Diem' },
  'social_post': { color: 'bg-pink-100 text-pink-800', label: 'Social Post' },
};

interface ApprovalDocumentsTableProps {
  approvalId?: string;
  approvalType?: string;
  showApprovalFilter?: boolean;
}

export default function ApprovalDocumentsTable({ 
  approvalId, 
  approvalType, 
  showApprovalFilter = true 
}: ApprovalDocumentsTableProps) {
  const { toast } = useToast();
  const [documents, setDocuments] = useState<ApprovalDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load approval documents
  useEffect(() => {
    loadDocuments();
  }, [approvalId, approvalType]);

  const loadDocuments = async () => {
    setIsLoading(true);
    try {
      let fetchedDocuments: ApprovalDocument[];
      
      if (approvalId) {
        fetchedDocuments = await getApprovalDocumentsAction(approvalId);
      } else {
        // Load all documents - this would need to be implemented in actions
        fetchedDocuments = [];
      }
      
      // Filter by approval type if specified
      if (approvalType) {
        fetchedDocuments = fetchedDocuments.filter(doc => doc.approvalType === approvalType);
      }
      
      setDocuments(fetchedDocuments);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load approval documents.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const handleDelete = async (documentId: string) => {
    if (!confirm('Are you sure you want to delete this document?')) return;
    
    try {
      await deleteApprovalDocumentAction(documentId);
      await loadDocuments();
      toast({
        title: "Success",
        description: "Document deleted successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete document.",
        variant: "destructive"
      });
    }
  };

  const handleRequestSignature = async (document: ApprovalDocument) => {
    try {
      // This would open a dialog to configure signing request
      console.log('Request signature for:', document);
      toast({
        title: "Feature Coming Soon",
        description: "Document signing workflow will be available soon."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to request signature.",
        variant: "destructive"
      });
    }
  };

  const handleDownload = (document: ApprovalDocument) => {
    // Open document URL in new tab for download
    window.open(document.documentUrl, '_blank');
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const columns: ColumnDef<ApprovalDocument>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "documentName",
      header: "Document Name",
      cell: ({ row }) => {
        const document = row.original;
        const typeConfig = documentTypeConfig[document.documentType];
        
        return (
          <div className="flex flex-col">
            <span className="font-medium">{document.documentName}</span>
            <Badge className={`${typeConfig.color} w-fit text-xs mt-1`}>
              {typeConfig.label}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: "approvalType",
      header: "Approval Type",
      cell: ({ row }) => {
        const approvalType = row.original.approvalType;
        const config = approvalTypeConfig[approvalType];
        
        return (
          <Badge className={`${config.color} capitalize`}>
            {config.label}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
      enableHiding: !!approvalType, // Hide if filtering by specific type
    },
    {
      id: "signatureStatus",
      header: "Signature Status",
      cell: ({ row }) => {
        const document = row.original;
        const status = document.signatureStatus;
        const config = signatureStatusConfig[status];
        const StatusIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <StatusIcon className="h-3 w-3" />
            {status}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.original.signatureStatus);
      },
    },
    {
      id: "requiresSignature",
      header: "Signature Required",
      cell: ({ row }) => {
        const requiresSignature = row.original.requiresSignature;
        
        return (
          <Badge variant={requiresSignature ? "default" : "secondary"}>
            {requiresSignature ? "Yes" : "No"}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        const requiresSignature = row.original.requiresSignature;
        return value.includes(requiresSignature ? 'yes' : 'no');
      },
    },
    {
      accessorKey: "uploadedBy",
      header: "Uploaded By",
      cell: ({ row }) => {
        const uploadedBy = row.original.uploadedBy;
        
        return (
          <div className="text-sm">
            {uploadedBy}
          </div>
        );
      },
    },
    {
      accessorKey: "uploadedAt",
      header: "Uploaded",
      cell: ({ row }) => {
        const uploadedAt = row.original.uploadedAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(uploadedAt)}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const document = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDownload(document)}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDownload(document)}
            >
              <Download className="h-4 w-4" />
            </Button>
            {document.requiresSignature && document.signatureStatus === 'Not Required' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleRequestSignature(document)}
              >
                <FileSignature className="h-4 w-4" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open edit dialog
                console.log('Edit document:', document);
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDelete(document.id!)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], [approvalType]);

  const tableActions = [
    {
      label: "Upload Document",
      icon: Upload,
      onClick: () => {
        // Open upload dialog
        console.log('Upload new document');
      },
      variant: "default" as const,
    },
    {
      label: "Request Signatures",
      icon: FileSignature,
      onClick: (selectedRows: ApprovalDocument[]) => {
        const signingDocs = selectedRows.filter(doc => doc.requiresSignature);
        console.log('Request signatures for:', signingDocs);
        toast({
          title: "Feature Coming Soon",
          description: "Bulk signature requests will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Download Selected",
      icon: Download,
      onClick: (selectedRows: ApprovalDocument[]) => {
        selectedRows.forEach(doc => handleDownload(doc));
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Delete Selected",
      icon: Trash2,
      onClick: async (selectedRows: ApprovalDocument[]) => {
        if (confirm(`Delete ${selectedRows.length} selected documents?`)) {
          try {
            await Promise.all(selectedRows.map(doc => deleteApprovalDocumentAction(doc.id!)));
            await loadDocuments();
            toast({
              title: "Success",
              description: `${selectedRows.length} documents deleted successfully.`
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to delete selected documents.",
              variant: "destructive"
            });
          }
        }
      },
      variant: "destructive" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    {
      key: 'approvalType',
      label: 'Approval Type',
      options: [
        { label: 'Travel', value: 'travel' },
        { label: 'Purchase', value: 'purchase' },
        { label: 'Marketing', value: 'marketing' },
        { label: 'Per Diem', value: 'per_diem' },
        { label: 'Social Post', value: 'social_post' },
      ]
    },
    {
      key: 'signatureStatus',
      label: 'Signature Status',
      options: [
        { label: 'Not Required', value: 'Not Required' },
        { label: 'Pending Signature', value: 'Pending Signature' },
        { label: 'Partially Signed', value: 'Partially Signed' },
        { label: 'Fully Signed', value: 'Fully Signed' },
        { label: 'Signature Rejected', value: 'Signature Rejected' },
        { label: 'Signature Expired', value: 'Signature Expired' },
      ]
    },
    {
      key: 'requiresSignature',
      label: 'Signature Required',
      options: [
        { label: 'Yes', value: 'yes' },
        { label: 'No', value: 'no' },
      ]
    },
  ];

  // Bulk actions
  const bulkActions = [
    {
      label: 'Download Selected',
      icon: <Download className="h-4 w-4" />,
      onClick: (selectedDocuments: ApprovalDocument[]) => {
        selectedDocuments.forEach(doc => {
          if (doc.documentUrl) {
            window.open(doc.documentUrl, '_blank');
          }
        });
      },
    },
    {
      label: 'Delete Selected',
      icon: <Trash2 className="h-4 w-4" />,
      onClick: async (selectedDocuments: ApprovalDocument[]) => {
        for (const doc of selectedDocuments) {
          if (doc.id) {
            try {
              await deleteApprovalDocumentAction(doc.id);
            } catch (error) {
              console.error(`Failed to delete document ${doc.id}:`, error);
            }
          }
        }
        loadDocuments();
      },
      variant: 'destructive' as const,
    },
  ];

  return (
    <AdvancedDataTable
      data={documents}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadDocuments}
      tableActions={tableActions}
      bulkActions={bulkActions}
      filterOptions={showApprovalFilter ? filterOptions : filterOptions.slice(1)}
      searchPlaceholder="Search documents by name, type..."
      emptyStateMessage="No approval documents found"
      emptyStateDescription="Upload documents to support your approval requests."
      enableRowSelection={true}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="approval-documents"
    />
  );
}
