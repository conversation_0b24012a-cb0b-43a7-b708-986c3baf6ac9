"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { 
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  FileText,
  User,
  Calendar,
  MessageSquare,
  AlertTriangle,
  ArrowRight,
  Download,
  ExternalLink,
  History
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { ContentApproval, StepApproval } from '@/types/firestore';
import { formatDistanceToNow, format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface ApprovalHistoryTableProps {
  approvals: ContentApproval[];
  isLoading?: boolean;
  onRefresh?: () => void;
  onViewDetails?: (approval: ContentApproval) => void;
}

export default function ApprovalHistoryTable({
  approvals,
  isLoading = false,
  onRefresh,
  onViewDetails
}: ApprovalHistoryTableProps) {
  const { toast } = useToast();
  const router = useRouter();

  // Format date helper
  const formatDate = (date: any) => {
    if (!date) return 'N/A';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return format(dateObj, 'MMM dd, yyyy HH:mm');
  };

  // Status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    const getStatusConfig = (status: string) => {
      switch (status) {
        case 'approved':
          return { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' };
        case 'rejected':
          return { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' };
        case 'pending':
          return { variant: 'secondary' as const, icon: Clock, color: 'text-yellow-600' };
        case 'escalated':
          return { variant: 'outline' as const, icon: AlertTriangle, color: 'text-orange-600' };
        case 'timeout':
          return { variant: 'outline' as const, icon: Clock, color: 'text-gray-600' };
        default:
          return { variant: 'secondary' as const, icon: Clock, color: 'text-gray-600' };
      }
    };

    const config = getStatusConfig(status);
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className={`h-3 w-3 ${config.color}`} />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  // Approval progress component
  const ApprovalProgress = ({ approvals: stepApprovals, currentStep }: { approvals: StepApproval[], currentStep: number }) => (
    <div className="flex items-center gap-1">
      {stepApprovals.map((stepApproval, index) => {
        const isCurrentStep = index === currentStep;
        const isCompleted = stepApproval.status === 'approved';
        const isRejected = stepApproval.status === 'rejected';
        
        return (
          <div key={stepApproval.stepId} className="flex items-center">
            <div
              className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium border-2 ${
                isCompleted
                  ? 'bg-green-100 border-green-500 text-green-700'
                  : isRejected
                  ? 'bg-red-100 border-red-500 text-red-700'
                  : isCurrentStep
                  ? 'bg-blue-100 border-blue-500 text-blue-700'
                  : 'bg-gray-100 border-gray-300 text-gray-500'
              }`}
              title={`Step ${index + 1}: ${stepApproval.status}`}
            >
              {isCompleted ? (
                <CheckCircle className="h-3 w-3" />
              ) : isRejected ? (
                <XCircle className="h-3 w-3" />
              ) : (
                index + 1
              )}
            </div>
            {index < stepApprovals.length - 1 && (
              <ArrowRight className="h-3 w-3 text-gray-400 mx-1" />
            )}
          </div>
        );
      })}
    </div>
  );

  // Duration component
  const Duration = ({ submittedAt, completedAt }: { submittedAt: any, completedAt?: any }) => {
    if (!completedAt) {
      return (
        <span className="text-sm text-gray-500">
          {formatDistanceToNow(submittedAt instanceof Timestamp ? submittedAt.toDate() : new Date(submittedAt))} ago
        </span>
      );
    }

    const submitted = submittedAt instanceof Timestamp ? submittedAt.toDate() : new Date(submittedAt);
    const completed = completedAt instanceof Timestamp ? completedAt.toDate() : new Date(completedAt);
    const duration = completed.getTime() - submitted.getTime();
    const hours = Math.floor(duration / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));

    return (
      <span className="text-sm text-gray-600">
        {hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`}
      </span>
    );
  };

  // Table columns
  const columns: ColumnDef<ContentApproval>[] = [
    {
      accessorKey: "postId",
      header: "Content ID",
      cell: ({ row }) => {
        const approval = row.original;
        return (
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4 text-gray-500" />
            <span className="font-mono text-sm">{approval.postId.slice(-8)}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => <StatusBadge status={row.original.status} />,
    },
    {
      accessorKey: "approvals",
      header: "Progress",
      cell: ({ row }) => (
        <ApprovalProgress 
          approvals={row.original.approvals} 
          currentStep={row.original.currentStep} 
        />
      ),
    },
    {
      accessorKey: "submittedBy",
      header: "Submitted By",
      cell: ({ row }) => {
        const submittedBy = row.original.submittedBy;
        
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-xs">
                {submittedBy.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm">{submittedBy}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "submittedAt",
      header: "Submitted",
      cell: ({ row }) => {
        const submittedAt = row.original.submittedAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(submittedAt)}
          </div>
        );
      },
    },
    {
      accessorKey: "completedAt",
      header: "Duration",
      cell: ({ row }) => (
        <Duration 
          submittedAt={row.original.submittedAt} 
          completedAt={row.original.completedAt} 
        />
      ),
    },
    {
      accessorKey: "notes",
      header: "Notes",
      cell: ({ row }) => {
        const notes = row.original.notes;
        if (!notes) return <span className="text-sm text-gray-400">—</span>;
        
        return (
          <div className="flex items-center gap-1">
            <MessageSquare className="h-3 w-3 text-gray-500" />
            <span className="text-sm text-gray-600 truncate max-w-xs" title={notes}>
              {notes}
            </span>
          </div>
        );
      },
    },
  ];

  // Row actions
  const getRowActions = (approval: ContentApproval) => [
    {
      type: 'view' as const,
      label: 'View Details',
      icon: <Eye className="h-4 w-4" />,
      onClick: () => onViewDetails?.(approval),
    },
    {
      type: 'external' as const,
      label: 'View Content',
      icon: <ExternalLink className="h-4 w-4" />,
      onClick: () => router.push(`/smm/posts/${approval.postId}`),
    },
  ];

  // Filter options
  const filterOptions = [
    {
      key: 'status',
      label: 'Status',
      options: [
        { label: 'Approved', value: 'approved' },
        { label: 'Rejected', value: 'rejected' },
        { label: 'Pending', value: 'pending' },
        { label: 'Escalated', value: 'escalated' },
        { label: 'Timeout', value: 'timeout' },
      ]
    },
  ];

  // Table actions
  const tableActions = [
    {
      label: 'Export History',
      icon: <Download className="h-4 w-4" />,
      onClick: () => {
        // Export functionality would be handled by the AdvancedDataTable
      },
      variant: 'outline' as const,
    },
  ];

  return (
    <AdvancedDataTable
      data={approvals}
      columns={columns}
      isLoading={isLoading}
      onRefresh={onRefresh}
      tableActions={tableActions}
      rowActions={getRowActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search by content ID, submitter..."
      emptyStateMessage="No approval history found"
      emptyStateDescription="Approval history will appear here once content goes through approval workflows."
      enableRowSelection={false}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="approval-history"
      defaultSorting={[{ id: 'submittedAt', desc: true }]}
    />
  );
}
