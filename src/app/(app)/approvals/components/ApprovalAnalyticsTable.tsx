"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { 
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  XCircle,
  Users,
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  Target,
  Zap,
  AlertTriangle
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import { formatDistanceToNow, format } from 'date-fns';

interface ApprovalAnalytics {
  id?: string;
  period: string; // 'daily', 'weekly', 'monthly'
  date: Date | string;
  approvalType: 'travel' | 'purchase' | 'marketing' | 'per_diem' | 'social_post' | 'all';
  totalSubmitted: number;
  totalApproved: number;
  totalRejected: number;
  totalPending: number;
  avgApprovalTime: number; // in hours
  avgRejectionTime: number; // in hours
  approvalRate: number; // percentage
  rejectionRate: number; // percentage
  bottleneckSteps: string[];
  topApprovers: { userId: string; name: string; count: number }[];
  slaCompliance: number; // percentage
  escalationRate: number; // percentage
}

interface ApprovalAnalyticsTableProps {
  analytics: ApprovalAnalytics[];
  isLoading?: boolean;
  onRefresh?: () => void;
  period?: 'daily' | 'weekly' | 'monthly';
}

export default function ApprovalAnalyticsTable({
  analytics,
  isLoading = false,
  onRefresh,
  period = 'daily'
}: ApprovalAnalyticsTableProps) {
  const { toast } = useToast();

  // Format date helper
  const formatDate = (date: any) => {
    if (!date) return 'N/A';
    
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    switch (period) {
      case 'daily':
        return format(dateObj, 'MMM dd, yyyy');
      case 'weekly':
        return format(dateObj, "'Week of' MMM dd, yyyy");
      case 'monthly':
        return format(dateObj, 'MMMM yyyy');
      default:
        return format(dateObj, 'MMM dd, yyyy');
    }
  };

  // Approval rate component
  const ApprovalRateCell = ({ approved, rejected, pending }: { approved: number, rejected: number, pending: number }) => {
    const total = approved + rejected + pending;
    if (total === 0) return <span className="text-sm text-gray-400">—</span>;
    
    const approvalRate = (approved / total) * 100;
    const rejectionRate = (rejected / total) * 100;
    
    return (
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          <CheckCircle className="h-3 w-3 text-green-600" />
          <span className="text-sm font-medium">{approvalRate.toFixed(1)}%</span>
        </div>
        <div className="flex items-center gap-2">
          <XCircle className="h-3 w-3 text-red-600" />
          <span className="text-sm text-gray-600">{rejectionRate.toFixed(1)}%</span>
        </div>
      </div>
    );
  };

  // Performance indicator component
  const PerformanceIndicator = ({ value, threshold, isHigherBetter = true }: { 
    value: number, 
    threshold: number, 
    isHigherBetter?: boolean 
  }) => {
    const isGood = isHigherBetter ? value >= threshold : value <= threshold;
    const Icon = isGood ? TrendingUp : TrendingDown;
    const color = isGood ? 'text-green-600' : 'text-red-600';
    
    return (
      <div className="flex items-center gap-1">
        <Icon className={`h-3 w-3 ${color}`} />
        <span className={`text-sm font-medium ${color}`}>
          {value.toFixed(1)}{typeof threshold === 'number' && threshold < 10 ? 'h' : '%'}
        </span>
      </div>
    );
  };

  // Type badge component
  const TypeBadge = ({ type }: { type: string }) => {
    if (type === 'all') {
      return <Badge variant="outline" className="bg-gray-100 text-gray-800 border-0">ALL TYPES</Badge>;
    }
    
    const getTypeColor = (type: string) => {
      switch (type) {
        case 'travel': return 'bg-blue-100 text-blue-800';
        case 'purchase': return 'bg-green-100 text-green-800';
        case 'marketing': return 'bg-purple-100 text-purple-800';
        case 'per_diem': return 'bg-yellow-100 text-yellow-800';
        case 'social_post': return 'bg-pink-100 text-pink-800';
        default: return 'bg-gray-100 text-gray-800';
      }
    };

    return (
      <Badge variant="outline" className={`${getTypeColor(type)} border-0`}>
        {type.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  // Volume indicator component
  const VolumeIndicator = ({ submitted, approved, rejected, pending }: {
    submitted: number,
    approved: number,
    rejected: number,
    pending: number
  }) => (
    <div className="space-y-1">
      <div className="flex items-center justify-between text-xs">
        <span className="text-gray-500">Total</span>
        <span className="font-medium">{submitted}</span>
      </div>
      <div className="flex gap-1">
        <div className="flex-1 h-2 bg-green-200 rounded-full overflow-hidden">
          <div 
            className="h-full bg-green-600" 
            style={{ width: `${submitted > 0 ? (approved / submitted) * 100 : 0}%` }}
          />
        </div>
        <div className="flex-1 h-2 bg-red-200 rounded-full overflow-hidden">
          <div 
            className="h-full bg-red-600" 
            style={{ width: `${submitted > 0 ? (rejected / submitted) * 100 : 0}%` }}
          />
        </div>
        <div className="flex-1 h-2 bg-yellow-200 rounded-full overflow-hidden">
          <div 
            className="h-full bg-yellow-600" 
            style={{ width: `${submitted > 0 ? (pending / submitted) * 100 : 0}%` }}
          />
        </div>
      </div>
    </div>
  );

  // Table columns
  const columns: ColumnDef<ApprovalAnalytics>[] = [
    {
      accessorKey: "date",
      header: "Period",
      cell: ({ row }) => {
        const date = row.original.date;
        return (
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-500" />
            <span className="font-medium">{formatDate(date)}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "approvalType",
      header: "Type",
      cell: ({ row }) => <TypeBadge type={row.original.approvalType} />,
    },
    {
      accessorKey: "totalSubmitted",
      header: "Volume",
      cell: ({ row }) => (
        <VolumeIndicator 
          submitted={row.original.totalSubmitted}
          approved={row.original.totalApproved}
          rejected={row.original.totalRejected}
          pending={row.original.totalPending}
        />
      ),
    },
    {
      accessorKey: "approvalRate",
      header: "Success Rate",
      cell: ({ row }) => (
        <ApprovalRateCell 
          approved={row.original.totalApproved}
          rejected={row.original.totalRejected}
          pending={row.original.totalPending}
        />
      ),
    },
    {
      accessorKey: "avgApprovalTime",
      header: "Avg. Approval Time",
      cell: ({ row }) => (
        <PerformanceIndicator 
          value={row.original.avgApprovalTime} 
          threshold={24} 
          isHigherBetter={false}
        />
      ),
    },
    {
      accessorKey: "slaCompliance",
      header: "SLA Compliance",
      cell: ({ row }) => (
        <PerformanceIndicator 
          value={row.original.slaCompliance} 
          threshold={90} 
          isHigherBetter={true}
        />
      ),
    },
    {
      accessorKey: "escalationRate",
      header: "Escalation Rate",
      cell: ({ row }) => {
        const rate = row.original.escalationRate;
        const isHigh = rate > 10;
        
        return (
          <div className="flex items-center gap-1">
            <AlertTriangle className={`h-3 w-3 ${isHigh ? 'text-red-600' : 'text-green-600'}`} />
            <span className={`text-sm font-medium ${isHigh ? 'text-red-600' : 'text-green-600'}`}>
              {rate.toFixed(1)}%
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "topApprovers",
      header: "Top Approvers",
      cell: ({ row }) => {
        const topApprovers = row.original.topApprovers.slice(0, 3);
        if (topApprovers.length === 0) {
          return <span className="text-sm text-gray-400">—</span>;
        }
        
        return (
          <div className="space-y-1">
            {topApprovers.map((approver, index) => (
              <div key={approver.userId} className="flex items-center gap-2 text-xs">
                <span className="w-4 h-4 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center font-medium">
                  {index + 1}
                </span>
                <span className="truncate max-w-20">{approver.name}</span>
                <span className="text-gray-500">({approver.count})</span>
              </div>
            ))}
          </div>
        );
      },
    },
  ];

  // Filter options
  const filterOptions = [
    {
      key: 'approvalType',
      label: 'Type',
      options: [
        { label: 'All Types', value: 'all' },
        { label: 'Travel', value: 'travel' },
        { label: 'Purchase', value: 'purchase' },
        { label: 'Marketing', value: 'marketing' },
        { label: 'Per Diem', value: 'per_diem' },
        { label: 'Social Post', value: 'social_post' },
      ]
    },
  ];

  // Table actions
  const tableActions = [
    {
      label: 'Export Analytics',
      icon: <BarChart3 className="h-4 w-4" />,
      onClick: () => {
        // Export functionality would be handled by the AdvancedDataTable
      },
      variant: 'outline' as const,
    },
  ];

  return (
    <AdvancedDataTable
      data={analytics}
      columns={columns}
      isLoading={isLoading}
      onRefresh={onRefresh}
      tableActions={tableActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search analytics..."
      emptyStateMessage="No analytics data found"
      emptyStateDescription="Analytics data will appear here once approvals are processed."
      enableRowSelection={false}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="approval-analytics"
      defaultSorting={[{ id: 'date', desc: true }]}
    />
  );
}
