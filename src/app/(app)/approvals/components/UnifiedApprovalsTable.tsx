"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { 
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  FileText,
  DollarSign,
  Plane,
  ShoppingCart,
  Megaphone,
  Share2,
  AlertTriangle,
  User,
  Calendar,
  Building,
  MessageSquare
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import { formatDistanceToNow, format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import Link from 'next/link';

// Import actions from different modules
import { approveTravelRequestAction, rejectTravelRequestAction } from '../../travel/actions';
import { approvePurchaseRequestAction, rejectPurchaseRequestAction } from '../../procurement/actions';
import { approveMarketingMaterialAction, rejectMarketingMaterialAction } from '../../marketing/actions';
import { updatePostStatusAction } from '../../smm/actions';

interface UnifiedApprovalItem {
  id: string;
  type: 'Travel' | 'Purchase' | 'Marketing' | 'Social Post' | 'Per Diem';
  description: string;
  submittedBy: string;
  submittedAt?: Date;
  amount?: string;
  status: string;
  link: string;
  rawItem: any;
  requestorId: string;
  exhibitionName?: string;
  approverName?: string;
  priority?: 'Low' | 'Medium' | 'High' | 'Urgent';
  dueDate?: Date;
}

const typeIcons = {
  'Travel': Plane,
  'Purchase': ShoppingCart,
  'Marketing': Megaphone,
  'Social Post': Share2,
  'Per Diem': DollarSign,
};

const typeColors = {
  'Travel': 'bg-blue-100 text-blue-800',
  'Purchase': 'bg-green-100 text-green-800',
  'Marketing': 'bg-purple-100 text-purple-800',
  'Social Post': 'bg-pink-100 text-pink-800',
  'Per Diem': 'bg-orange-100 text-orange-800',
};

const statusConfig = {
  'Pending Approval': { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
  'Approved': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
  'Rejected': { color: 'bg-red-100 text-red-800', icon: XCircle },
  'Under Review': { color: 'bg-blue-100 text-blue-800', icon: Eye },
  'Escalated': { color: 'bg-orange-100 text-orange-800', icon: AlertTriangle },
};

const priorityConfig = {
  'Low': { color: 'bg-gray-100 text-gray-800' },
  'Medium': { color: 'bg-yellow-100 text-yellow-800' },
  'High': { color: 'bg-orange-100 text-orange-800' },
  'Urgent': { color: 'bg-red-100 text-red-800' },
};

interface UnifiedApprovalsTableProps {
  approvalItems: UnifiedApprovalItem[];
  isLoading: boolean;
  onRefresh: () => void;
}

export default function UnifiedApprovalsTable({ 
  approvalItems, 
  isLoading, 
  onRefresh 
}: UnifiedApprovalsTableProps) {
  const { toast } = useToast();

  const handleApprove = async (item: UnifiedApprovalItem) => {
    try {
      let result;
      
      switch (item.type) {
        case 'Travel':
          result = await approveTravelRequestAction(item.id);
          break;
        case 'Purchase':
          result = await approvePurchaseRequestAction(item.id);
          break;
        case 'Marketing':
          result = await approveMarketingMaterialAction(item.id);
          break;
        case 'Social Post':
          result = await updatePostStatusAction(item.id, 'approved');
          break;
        default:
          throw new Error('Unsupported approval type');
      }

      if (result.success) {
        toast({
          title: "Approved",
          description: `${item.type} request has been approved successfully.`
        });
        onRefresh();
      } else {
        throw new Error(result.error || 'Approval failed');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to approve ${item.type.toLowerCase()} request.`,
        variant: "destructive"
      });
    }
  };

  const handleReject = async (item: UnifiedApprovalItem) => {
    const reason = prompt('Please provide a reason for rejection:');
    if (!reason) return;

    try {
      let result;
      
      switch (item.type) {
        case 'Travel':
          result = await rejectTravelRequestAction(item.id, reason);
          break;
        case 'Purchase':
          result = await rejectPurchaseRequestAction(item.id, reason);
          break;
        case 'Marketing':
          result = await rejectMarketingMaterialAction(item.id, reason);
          break;
        case 'Social Post':
          result = await updatePostStatusAction(item.id, 'rejected');
          break;
        default:
          throw new Error('Unsupported approval type');
      }

      if (result.success) {
        toast({
          title: "Rejected",
          description: `${item.type} request has been rejected.`
        });
        onRefresh();
      } else {
        throw new Error(result.error || 'Rejection failed');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to reject ${item.type.toLowerCase()} request.`,
        variant: "destructive"
      });
    }
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const formatDateTime = (date: any) => {
    if (!date) return 'Not set';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return format(dateObj, 'MMM dd, yyyy HH:mm');
  };

  const columns: ColumnDef<UnifiedApprovalItem>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }) => {
        const type = row.original.type;
        const TypeIcon = typeIcons[type];
        const colorClass = typeColors[type];
        
        return (
          <Badge className={`${colorClass} flex items-center gap-1 w-fit`}>
            <TypeIcon className="h-3 w-3" />
            {type}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: ({ row }) => {
        const item = row.original;
        
        return (
          <div className="max-w-sm">
            <p className="font-medium line-clamp-2">
              {item.description}
            </p>
            {item.exhibitionName && item.exhibitionName !== 'N/A' && (
              <div className="flex items-center gap-1 mt-1">
                <Building className="h-3 w-3 text-gray-400" />
                <span className="text-xs text-gray-500">{item.exhibitionName}</span>
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "submittedBy",
      header: "Submitted By",
      cell: ({ row }) => {
        const submittedBy = row.original.submittedBy;
        
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-xs">
                {submittedBy.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm">{submittedBy}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "submittedAt",
      header: "Submitted",
      cell: ({ row }) => {
        const submittedAt = row.original.submittedAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(submittedAt)}
          </div>
        );
      },
    },
    {
      accessorKey: "amount",
      header: "Amount/Details",
      cell: ({ row }) => {
        const amount = row.original.amount;
        
        if (!amount || amount === 'N/A') {
          return <span className="text-gray-400">No amount</span>;
        }
        
        return (
          <div className="flex items-center gap-1">
            <DollarSign className="h-3 w-3 text-gray-400" />
            <span className="font-medium">{amount}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.status;
        const config = statusConfig[status] || statusConfig['Pending Approval'];
        const StatusIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <StatusIcon className="h-3 w-3" />
            {status}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: "priority",
      header: "Priority",
      cell: ({ row }) => {
        const priority = row.original.priority || 'Medium';
        const config = priorityConfig[priority];
        
        return (
          <Badge className={`${config.color} text-xs`}>
            {priority}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        const priority = row.original.priority || 'Medium';
        return value.includes(priority);
      },
    },
    {
      id: "dueDate",
      header: "Due Date",
      cell: ({ row }) => {
        const dueDate = row.original.dueDate;
        
        if (!dueDate) {
          return <span className="text-gray-400">Not set</span>;
        }
        
        const isOverdue = new Date(dueDate) < new Date();
        
        return (
          <div className={`text-sm ${isOverdue ? 'text-red-600' : 'text-gray-600'}`}>
            {formatDateTime(dueDate)}
            {isOverdue && (
              <Badge variant="destructive" className="ml-2 text-xs">
                Overdue
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const item = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              asChild
            >
              <Link href={item.link}>
                <Eye className="h-4 w-4" />
              </Link>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleApprove(item)}
              className="text-green-600 hover:text-green-700"
            >
              <CheckCircle className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleReject(item)}
              className="text-red-600 hover:text-red-700"
            >
              <XCircle className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open comment dialog
                console.log('Add comment to:', item);
              }}
            >
              <MessageSquare className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], []);

  const tableActions = [
    {
      label: "Approve Selected",
      icon: CheckCircle,
      onClick: async (selectedRows: UnifiedApprovalItem[]) => {
        if (confirm(`Approve ${selectedRows.length} selected items?`)) {
          try {
            await Promise.all(selectedRows.map(item => handleApprove(item)));
            toast({
              title: "Success",
              description: `${selectedRows.length} items approved successfully.`
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to approve selected items.",
              variant: "destructive"
            });
          }
        }
      },
      variant: "default" as const,
      requiresSelection: true,
    },
    {
      label: "Reject Selected",
      icon: XCircle,
      onClick: async (selectedRows: UnifiedApprovalItem[]) => {
        const reason = prompt('Please provide a reason for rejection:');
        if (!reason) return;

        if (confirm(`Reject ${selectedRows.length} selected items?`)) {
          try {
            await Promise.all(selectedRows.map(item => handleReject(item)));
            toast({
              title: "Success",
              description: `${selectedRows.length} items rejected.`
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to reject selected items.",
              variant: "destructive"
            });
          }
        }
      },
      variant: "destructive" as const,
      requiresSelection: true,
    },
    {
      label: "Escalate Selected",
      icon: AlertTriangle,
      onClick: (selectedRows: UnifiedApprovalItem[]) => {
        console.log('Escalate items:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Escalation workflow will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
  ];

  // Bulk actions
  const bulkActions = [
    {
      label: 'Approve Selected',
      icon: <CheckCircle className="h-4 w-4" />,
      onClick: async (selectedItems: UnifiedApprovalItem[]) => {
        for (const item of selectedItems) {
          try {
            await handleApprove(item);
          } catch (error) {
            console.error(`Failed to approve ${item.type} ${item.id}:`, error);
          }
        }
        onRefresh();
      },
    },
    {
      label: 'Reject Selected',
      icon: <XCircle className="h-4 w-4" />,
      onClick: async (selectedItems: UnifiedApprovalItem[]) => {
        for (const item of selectedItems) {
          try {
            await handleReject(item);
          } catch (error) {
            console.error(`Failed to reject ${item.type} ${item.id}:`, error);
          }
        }
        onRefresh();
      },
      variant: 'destructive' as const,
    },
  ];

  const filterOptions = [
    {
      key: 'type',
      label: 'Type',
      options: [
        { label: 'Travel', value: 'Travel' },
        { label: 'Purchase', value: 'Purchase' },
        { label: 'Marketing', value: 'Marketing' },
        { label: 'Social Post', value: 'Social Post' },
        { label: 'Per Diem', value: 'Per Diem' },
      ]
    },
    {
      key: 'status',
      label: 'Status',
      options: [
        { label: 'Pending Approval', value: 'Pending Approval' },
        { label: 'Approved', value: 'Approved' },
        { label: 'Rejected', value: 'Rejected' },
        { label: 'Under Review', value: 'Under Review' },
        { label: 'Escalated', value: 'Escalated' },
      ]
    },
    {
      key: 'priority',
      label: 'Priority',
      options: [
        { label: 'Low', value: 'Low' },
        { label: 'Medium', value: 'Medium' },
        { label: 'High', value: 'High' },
        { label: 'Urgent', value: 'Urgent' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={approvalItems}
      columns={columns}
      isLoading={isLoading}
      onRefresh={onRefresh}
      tableActions={tableActions}
      bulkActions={bulkActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search approvals by description, submitter..."
      emptyStateMessage="No pending approvals"
      emptyStateDescription="All approval requests have been processed."
      enableRowSelection={true}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="pending-approvals"
    />
  );
}
