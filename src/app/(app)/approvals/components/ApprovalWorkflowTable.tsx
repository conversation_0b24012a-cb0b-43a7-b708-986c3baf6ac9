"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { 
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Edit,
  Trash2,
  Users,
  Settings,
  Play,
  Pause,
  AlertTriangle,
  FileText,
  Calendar,
  User,
  ArrowRight,
  Plus
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { ContentApprovalWorkflow, ApprovalStep } from '@/types/firestore';
import { formatDistanceToNow, format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface ApprovalWorkflowTableProps {
  workflows: ContentApprovalWorkflow[];
  isLoading?: boolean;
  onRefresh?: () => void;
  onEdit?: (workflow: ContentApprovalWorkflow) => void;
  onDelete?: (workflowId: string) => void;
  onToggleStatus?: (workflowId: string, isActive: boolean) => void;
}

export default function ApprovalWorkflowTable({
  workflows,
  isLoading = false,
  onRefresh,
  onEdit,
  onDelete,
  onToggleStatus
}: ApprovalWorkflowTableProps) {
  const { toast } = useToast();
  const router = useRouter();

  // Format date helper
  const formatDate = (date: any) => {
    if (!date) return 'N/A';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return format(dateObj, 'MMM dd, yyyy');
  };

  // Status badge component
  const StatusBadge = ({ isActive }: { isActive: boolean }) => (
    <Badge variant={isActive ? "default" : "secondary"} className="flex items-center gap-1">
      {isActive ? (
        <>
          <Play className="h-3 w-3" />
          Active
        </>
      ) : (
        <>
          <Pause className="h-3 w-3" />
          Inactive
        </>
      )}
    </Badge>
  );

  // Steps summary component
  const StepsSummary = ({ steps }: { steps: ApprovalStep[] }) => (
    <div className="flex items-center gap-2">
      <Users className="h-4 w-4 text-gray-500" />
      <span className="text-sm text-gray-600">
        {steps.length} step{steps.length !== 1 ? 's' : ''}
      </span>
      <div className="flex -space-x-1">
        {steps.slice(0, 3).map((step, index) => (
          <div
            key={step.id}
            className="w-6 h-6 rounded-full bg-blue-100 border-2 border-white flex items-center justify-center text-xs font-medium text-blue-600"
            title={step.name}
          >
            {index + 1}
          </div>
        ))}
        {steps.length > 3 && (
          <div className="w-6 h-6 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-xs font-medium text-gray-600">
            +{steps.length - 3}
          </div>
        )}
      </div>
    </div>
  );

  // Platforms badge component
  const PlatformsBadge = ({ platforms }: { platforms?: string[] }) => {
    if (!platforms || platforms.length === 0) return <span className="text-sm text-gray-500">All platforms</span>;
    
    return (
      <div className="flex flex-wrap gap-1">
        {platforms.slice(0, 2).map((platform) => (
          <Badge key={platform} variant="outline" className="text-xs">
            {platform}
          </Badge>
        ))}
        {platforms.length > 2 && (
          <Badge variant="outline" className="text-xs">
            +{platforms.length - 2}
          </Badge>
        )}
      </div>
    );
  };

  // Table columns
  const columns: ColumnDef<ContentApprovalWorkflow>[] = [
    {
      accessorKey: "name",
      header: "Workflow Name",
      cell: ({ row }) => {
        const workflow = row.original;
        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">{workflow.name}</span>
            {workflow.description && (
              <span className="text-sm text-gray-500 truncate max-w-xs">
                {workflow.description}
              </span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "isActive",
      header: "Status",
      cell: ({ row }) => <StatusBadge isActive={row.original.isActive} />,
    },
    {
      accessorKey: "steps",
      header: "Approval Steps",
      cell: ({ row }) => <StepsSummary steps={row.original.steps} />,
    },
    {
      accessorKey: "platforms",
      header: "Platforms",
      cell: ({ row }) => <PlatformsBadge platforms={row.original.platforms} />,
    },
    {
      accessorKey: "contentTypes",
      header: "Content Types",
      cell: ({ row }) => {
        const contentTypes = row.original.contentTypes;
        if (!contentTypes || contentTypes.length === 0) {
          return <span className="text-sm text-gray-500">All types</span>;
        }
        
        return (
          <div className="flex flex-wrap gap-1">
            {contentTypes.slice(0, 2).map((type) => (
              <Badge key={type} variant="outline" className="text-xs">
                {type}
              </Badge>
            ))}
            {contentTypes.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{contentTypes.length - 2}
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(createdAt)}
          </div>
        );
      },
    },
  ];

  // Row actions
  const getRowActions = (workflow: ContentApprovalWorkflow) => [
    {
      type: 'view' as const,
      label: 'View Details',
      icon: <Eye className="h-4 w-4" />,
      onClick: () => router.push(`/approvals/workflows/${workflow.id}`),
    },
    {
      type: 'edit' as const,
      label: 'Edit Workflow',
      icon: <Edit className="h-4 w-4" />,
      onClick: () => onEdit?.(workflow),
    },
    {
      type: 'custom' as const,
      label: workflow.isActive ? 'Deactivate' : 'Activate',
      icon: workflow.isActive ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />,
      onClick: () => onToggleStatus?.(workflow.id!, !workflow.isActive),
    },
    {
      type: 'delete' as const,
      label: 'Delete Workflow',
      icon: <Trash2 className="h-4 w-4" />,
      variant: 'destructive' as const,
      onClick: () => onDelete?.(workflow.id!),
      disabled: () => workflow.isActive, // Can't delete active workflows
    },
  ];

  // Bulk actions
  const bulkActions = [
    {
      label: 'Activate Selected',
      icon: <Play className="h-4 w-4" />,
      onClick: (selectedWorkflows: ContentApprovalWorkflow[]) => {
        selectedWorkflows.forEach(workflow => {
          if (!workflow.isActive) {
            onToggleStatus?.(workflow.id!, true);
          }
        });
      },
    },
    {
      label: 'Deactivate Selected',
      icon: <Pause className="h-4 w-4" />,
      onClick: (selectedWorkflows: ContentApprovalWorkflow[]) => {
        selectedWorkflows.forEach(workflow => {
          if (workflow.isActive) {
            onToggleStatus?.(workflow.id!, false);
          }
        });
      },
    },
  ];

  // Table actions
  const tableActions = [
    {
      label: 'New Workflow',
      icon: <Plus className="h-4 w-4" />,
      onClick: () => router.push('/approvals/workflows/new'),
      variant: 'default' as const,
    },
  ];

  // Filter options
  const filterOptions = [
    {
      key: 'isActive',
      label: 'Status',
      options: [
        { label: 'Active', value: 'true' },
        { label: 'Inactive', value: 'false' },
      ]
    },
    {
      key: 'platforms',
      label: 'Platform',
      options: [
        { label: 'Facebook', value: 'facebook' },
        { label: 'Instagram', value: 'instagram' },
        { label: 'Twitter', value: 'twitter' },
        { label: 'LinkedIn', value: 'linkedin' },
        { label: 'YouTube', value: 'youtube' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={workflows}
      columns={columns}
      isLoading={isLoading}
      onRefresh={onRefresh}
      tableActions={tableActions}
      bulkActions={bulkActions}
      rowActions={getRowActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search workflows by name, description..."
      emptyStateMessage="No approval workflows found"
      emptyStateDescription="Create your first approval workflow to streamline content approval processes."
      enableRowSelection={true}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="approval-workflows"
    />
  );
}
