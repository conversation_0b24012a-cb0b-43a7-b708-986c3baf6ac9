'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { FileSignature, Upload, Download, Eye, Trash2, Plus, FileText, Loader2 } from "lucide-react";
import { useAuth } from '@/contexts/auth-context';
import { 
  getApprovalDocumentsAction, 
  uploadApprovalDocumentAction, 
  createDocumentSigningRequestAction,
  deleteApprovalDocumentAction,
  type ApprovalDocument 
} from '../actions';
import { DocumentSigningDialog } from './DocumentSigningDialog';

interface ApprovalDocumentsPanelProps {
  approvalId: string;
  approvalType: ApprovalDocument['approvalType'];
  onDocumentsChange?: () => void;
}

export const ApprovalDocumentsPanel: React.FC<ApprovalDocumentsPanelProps> = ({
  approvalId,
  approvalType,
  onDocumentsChange
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [documents, setDocuments] = useState<ApprovalDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [showSigningDialog, setShowSigningDialog] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<ApprovalDocument | null>(null);
  const [uploadData, setUploadData] = useState({
    file: null as File | null,
    documentType: 'supporting_document' as ApprovalDocument['documentType'],
    requiresSignature: false
  });
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    loadDocuments();
  }, [approvalId]);

  const loadDocuments = async () => {
    try {
      setIsLoading(true);
      const docs = await getApprovalDocumentsAction(approvalId);
      setDocuments(docs);
    } catch (error) {
      console.error('Error loading documents:', error);
      toast({
        title: "Error",
        description: "Failed to load documents",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileUpload = async () => {
    if (!uploadData.file || !user?.id) {
      toast({
        title: "Missing Information",
        description: "Please select a file to upload.",
        variant: "destructive"
      });
      return;
    }

    setIsUploading(true);
    try {
      const result = await uploadApprovalDocumentAction({
        approvalId,
        approvalType,
        file: uploadData.file,
        documentType: uploadData.documentType,
        requiresSignature: uploadData.requiresSignature,
        uploadedBy: user.id
      });

      if (result.success) {
        toast({
          title: "Document Uploaded",
          description: "The document has been uploaded successfully."
        });
        
        setShowUploadDialog(false);
        setUploadData({
          file: null,
          documentType: 'supporting_document',
          requiresSignature: false
        });
        
        await loadDocuments();
        onDocumentsChange?.();
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : 'Failed to upload document',
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleRequestSigning = (document: ApprovalDocument) => {
    setSelectedDocument(document);
    setShowSigningDialog(true);
  };

  const handleDeleteDocument = async (documentId: string) => {
    if (!window.confirm('Are you sure you want to delete this document?')) {
      return;
    }

    try {
      const result = await deleteApprovalDocumentAction(documentId);
      
      if (result.success) {
        toast({
          title: "Document Deleted",
          description: "The document has been deleted successfully."
        });
        
        await loadDocuments();
        onDocumentsChange?.();
      } else {
        throw new Error(result.error || 'Delete failed');
      }
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: error instanceof Error ? error.message : 'Failed to delete document',
        variant: "destructive"
      });
    }
  };

  const getSignatureStatusBadge = (status: ApprovalDocument['signatureStatus']) => {
    switch (status) {
      case 'Signed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Signed</Badge>;
      case 'Pending Signature':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending Signature</Badge>;
      case 'Not Applicable':
        return <Badge variant="outline">No Signature Required</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getDocumentTypeLabel = (type: ApprovalDocument['documentType']) => {
    switch (type) {
      case 'approval_form':
        return 'Approval Form';
      case 'supporting_document':
        return 'Supporting Document';
      case 'contract':
        return 'Contract';
      case 'agreement':
        return 'Agreement';
      default:
        return 'Other';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Approval Documents
            </CardTitle>
            <CardDescription>
              Manage documents related to this approval request
            </CardDescription>
          </div>
          <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Document
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Upload Document</DialogTitle>
                <DialogDescription>
                  Add a document to this approval request
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="file">Document File</Label>
                  <Input
                    id="file"
                    type="file"
                    accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
                    onChange={(e) => setUploadData(prev => ({ 
                      ...prev, 
                      file: e.target.files?.[0] || null 
                    }))}
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="documentType">Document Type</Label>
                  <Select 
                    value={uploadData.documentType} 
                    onValueChange={(value: ApprovalDocument['documentType']) => 
                      setUploadData(prev => ({ ...prev, documentType: value }))
                    }
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="approval_form">Approval Form</SelectItem>
                      <SelectItem value="supporting_document">Supporting Document</SelectItem>
                      <SelectItem value="contract">Contract</SelectItem>
                      <SelectItem value="agreement">Agreement</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="requiresSignature"
                    checked={uploadData.requiresSignature}
                    onChange={(e) => setUploadData(prev => ({ 
                      ...prev, 
                      requiresSignature: e.target.checked 
                    }))}
                  />
                  <Label htmlFor="requiresSignature">Requires Signature</Label>
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setShowUploadDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleFileUpload} disabled={isUploading || !uploadData.file}>
                  {isUploading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Upload
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        ) : documents.length > 0 ? (
          <div className="space-y-4">
            {documents.map((document) => (
              <div key={document.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-medium">{document.documentName}</h4>
                    <Badge variant="outline" className="text-xs">
                      {getDocumentTypeLabel(document.documentType)}
                    </Badge>
                    {getSignatureStatusBadge(document.signatureStatus)}
                  </div>
                  <p className="text-sm text-gray-600">
                    Uploaded {new Date(document.uploadedAt as string).toLocaleDateString()}
                  </p>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm" asChild>
                    <a href={document.documentUrl} target="_blank" rel="noopener noreferrer">
                      <Eye className="h-4 w-4" />
                    </a>
                  </Button>
                  
                  <Button variant="ghost" size="sm" asChild>
                    <a href={document.documentUrl} download>
                      <Download className="h-4 w-4" />
                    </a>
                  </Button>
                  
                  {document.requiresSignature && document.signatureStatus === 'Pending Signature' && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleRequestSigning(document)}
                    >
                      <FileSignature className="h-4 w-4 mr-1" />
                      Sign
                    </Button>
                  )}
                  
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleDeleteDocument(document.id!)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No documents uploaded yet</p>
            <p className="text-sm">Click "Add Document" to upload files</p>
          </div>
        )}
      </CardContent>

      {/* Document Signing Dialog */}
      {selectedDocument && (
        <DocumentSigningDialog
          isOpen={showSigningDialog}
          onClose={() => {
            setShowSigningDialog(false);
            setSelectedDocument(null);
          }}
          documentId={selectedDocument.id!}
          documentName={selectedDocument.documentName}
          documentUrl={selectedDocument.documentUrl}
          documentType="approval"
          requesterId={user?.id || ''}
          requesterName={user?.displayName || user?.email || ''}
          onSigningComplete={() => {
            loadDocuments();
            onDocumentsChange?.();
          }}
        />
      )}
    </Card>
  );
};
