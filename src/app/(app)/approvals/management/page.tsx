"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  ClipboardCheck,
  Workflow,
  History,
  FileText,
  Settings,
  RefreshCw,
  Plus,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react';
import { ListPageLayout } from '@/components/ui/detail-page-layout';
import ApprovalWorkflowTable from '../components/ApprovalWorkflowTable';
import ApprovalHistoryTable from '../components/ApprovalHistoryTable';
import ApprovalDocumentsTable from '../components/ApprovalDocumentsTable';
import ApprovalAuditTable from '../components/ApprovalAuditTable';
import ApprovalAnalyticsTable from '../components/ApprovalAnalyticsTable';
import UnifiedApprovalsTable from '../components/UnifiedApprovalsTable';
import type { ContentApprovalWorkflow, ContentApproval, ApprovalDocument } from '@/types/firestore';
import { getApprovalWorkflowsAction } from '@/app/(app)/smm/actions';
import {
  getApprovalDocumentsAction,
  getContentApprovalsAction,
  getApprovalAuditEntriesAction,
  getApprovalAnalyticsAction
} from '../actions';
import Link from 'next/link';

interface ApprovalStats {
  totalPending: number;
  totalApproved: number;
  totalRejected: number;
  avgApprovalTime: number;
  activeWorkflows: number;
}

export default function ApprovalManagementPage() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('pending');
  const [isLoading, setIsLoading] = useState(false);
  
  // Data states
  const [workflows, setWorkflows] = useState<ContentApprovalWorkflow[]>([]);
  const [approvalHistory, setApprovalHistory] = useState<ContentApproval[]>([]);
  const [documents, setDocuments] = useState<ApprovalDocument[]>([]);
  const [pendingApprovals, setPendingApprovals] = useState<any[]>([]);
  const [auditEntries, setAuditEntries] = useState<any[]>([]);
  const [analytics, setAnalytics] = useState<any[]>([]);
  const [stats, setStats] = useState<ApprovalStats>({
    totalPending: 0,
    totalApproved: 0,
    totalRejected: 0,
    avgApprovalTime: 0,
    activeWorkflows: 0
  });

  // Load all data
  const loadData = async () => {
    setIsLoading(true);
    try {
      // Load workflows
      const workflowsData = await getApprovalWorkflowsAction();
      setWorkflows(workflowsData);

      // Load documents
      const documentsResult = await getApprovalDocumentsAction();
      if (documentsResult.success && documentsResult.documents) {
        setDocuments(documentsResult.documents);
      }

      // Load approval history
      const historyResult = await getContentApprovalsAction();
      if (historyResult.success && historyResult.approvals) {
        setApprovalHistory(historyResult.approvals);
      }

      // Load audit entries
      const auditResult = await getApprovalAuditEntriesAction();
      if (auditResult.success && auditResult.entries) {
        setAuditEntries(auditResult.entries);
      }

      // Load analytics
      const analyticsResult = await getApprovalAnalyticsAction('daily');
      if (analyticsResult.success && analyticsResult.analytics) {
        setAnalytics(analyticsResult.analytics);
      }

      // Calculate stats
      const activeWorkflowCount = workflowsData.filter(w => w.isActive).length;
      const historyData = historyResult.success ? historyResult.approvals || [] : [];
      const approvedCount = historyData.filter((a: any) => a.status === 'approved').length;
      const rejectedCount = historyData.filter((a: any) => a.status === 'rejected').length;
      const pendingCount = historyData.filter((a: any) => a.status === 'pending').length;

      setStats(prev => ({
        ...prev,
        activeWorkflows: activeWorkflowCount,
        totalApproved: approvedCount,
        totalRejected: rejectedCount,
        totalPending: pendingCount
      }));

    } catch (error) {
      console.error('Error loading approval data:', error);
      toast({
        title: "Error",
        description: "Failed to load approval data. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // Handle workflow actions
  const handleEditWorkflow = (workflow: ContentApprovalWorkflow) => {
    // Navigate to edit workflow page
    window.location.href = `/approvals/workflows/${workflow.id}/edit`;
  };

  const handleDeleteWorkflow = async (workflowId: string) => {
    try {
      // Delete workflow logic would go here
      toast({
        title: "Success",
        description: "Workflow deleted successfully."
      });
      loadData();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete workflow.",
        variant: "destructive"
      });
    }
  };

  const handleToggleWorkflowStatus = async (workflowId: string, isActive: boolean) => {
    try {
      // Toggle workflow status logic would go here
      toast({
        title: "Success",
        description: `Workflow ${isActive ? 'activated' : 'deactivated'} successfully.`
      });
      loadData();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update workflow status.",
        variant: "destructive"
      });
    }
  };

  const handleViewApprovalDetails = (approval: ContentApproval) => {
    // Navigate to approval details page
    window.location.href = `/approvals/history/${approval.id}`;
  };

  // Stats cards
  const StatsCard = ({ title, value, icon: Icon, color, description }: {
    title: string;
    value: number;
    icon: any;
    color: string;
    description?: string;
  }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className={`h-4 w-4 ${color}`} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && (
          <p className="text-xs text-muted-foreground">{description}</p>
        )}
      </CardContent>
    </Card>
  );

  const headerActions = (
    <div className="flex gap-2">
      <Button asChild variant="outline">
        <Link href="/approvals/workflows/new">
          <Plus className="mr-2 h-4 w-4" />
          New Workflow
        </Link>
      </Button>
      <Button variant="outline" onClick={loadData} disabled={isLoading}>
        <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
        Refresh
      </Button>
    </div>
  );

  return (
    <ListPageLayout
      title="Approval Management"
      description="Manage approval workflows, review pending requests, and track approval history"
      headerActions={headerActions}
      icon={<ClipboardCheck className="h-8 w-8" />}
    >
      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5 mb-6">
        <StatsCard
          title="Pending Approvals"
          value={stats.totalPending}
          icon={Clock}
          color="text-yellow-600"
          description="Awaiting review"
        />
        <StatsCard
          title="Approved Today"
          value={stats.totalApproved}
          icon={CheckCircle}
          color="text-green-600"
          description="Completed approvals"
        />
        <StatsCard
          title="Rejected"
          value={stats.totalRejected}
          icon={XCircle}
          color="text-red-600"
          description="Declined requests"
        />
        <StatsCard
          title="Avg. Approval Time"
          value={stats.avgApprovalTime}
          icon={TrendingUp}
          color="text-blue-600"
          description="Hours to complete"
        />
        <StatsCard
          title="Active Workflows"
          value={stats.activeWorkflows}
          icon={Workflow}
          color="text-purple-600"
          description="Currently in use"
        />
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="pending" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Pending
          </TabsTrigger>
          <TabsTrigger value="workflows" className="flex items-center gap-2">
            <Workflow className="h-4 w-4" />
            Workflows
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <History className="h-4 w-4" />
            History
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Documents
          </TabsTrigger>
          <TabsTrigger value="audit" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Audit
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pending Approvals</CardTitle>
              <CardDescription>
                Review and process approval requests from all modules
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UnifiedApprovalsTable
                approvalItems={pendingApprovals}
                isLoading={isLoading}
                onRefresh={loadData}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="workflows" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Approval Workflows</CardTitle>
              <CardDescription>
                Manage approval workflows and their configurations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ApprovalWorkflowTable
                workflows={workflows}
                isLoading={isLoading}
                onRefresh={loadData}
                onEdit={handleEditWorkflow}
                onDelete={handleDeleteWorkflow}
                onToggleStatus={handleToggleWorkflowStatus}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Approval History</CardTitle>
              <CardDescription>
                View completed approval processes and their outcomes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ApprovalHistoryTable
                approvals={approvalHistory}
                isLoading={isLoading}
                onRefresh={loadData}
                onViewDetails={handleViewApprovalDetails}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Approval Documents</CardTitle>
              <CardDescription>
                Manage documents related to approval processes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ApprovalDocumentsTable
                documents={documents}
                isLoading={isLoading}
                onRefresh={loadData}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Approval Audit Trail</CardTitle>
              <CardDescription>
                Complete audit log of all approval actions and changes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ApprovalAuditTable
                auditEntries={auditEntries}
                isLoading={isLoading}
                onRefresh={loadData}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Approval Analytics</CardTitle>
              <CardDescription>
                Performance metrics and insights for approval processes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ApprovalAnalyticsTable
                analytics={analytics}
                isLoading={isLoading}
                onRefresh={loadData}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </ListPageLayout>
  );
}
