'use client';

import React, { useState, useEffect, useRef } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { FileSignature, Download, Eye, Clock, CheckCircle, XCircle, Loader2, AlertTriangle } from "lucide-react";
import { useAuth } from '@/contexts/auth-context';
import { getDocumentSigningRequestAction, processApprovalDocumentSignatureAction } from '../../actions';
import type { SigningRequest } from '@/services/documentSigningService';
import { format } from 'date-fns';

interface SignatureCanvasProps {
  onSignatureChange: (signatureData: string) => void;
  width?: number;
  height?: number;
}

const SignatureCanvas: React.FC<SignatureCanvasProps> = ({ 
  onSignatureChange, 
  width = 500, 
  height = 200 
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set up canvas
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    // Set background to white
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  }, []);

  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.beginPath();
    ctx.moveTo(x, y);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.lineTo(x, y);
    ctx.stroke();
  };

  const stopDrawing = () => {
    if (!isDrawing) return;
    setIsDrawing(false);

    const canvas = canvasRef.current;
    if (!canvas) return;

    // Convert canvas to base64
    const signatureData = canvas.toDataURL('image/png');
    onSignatureChange(signatureData);
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    onSignatureChange('');
  };

  return (
    <div className="space-y-4">
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 bg-gray-50">
        <canvas
          ref={canvasRef}
          width={width}
          height={height}
          className="border border-gray-200 rounded cursor-crosshair bg-white mx-auto block"
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={stopDrawing}
          onMouseLeave={stopDrawing}
        />
      </div>
      <div className="flex justify-between items-center">
        <p className="text-sm text-gray-600">Draw your signature in the box above</p>
        <Button variant="outline" size="sm" onClick={clearSignature}>
          Clear Signature
        </Button>
      </div>
    </div>
  );
};

export default function DocumentSigningPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const { toast } = useToast();
  
  const requestId = params.requestId as string;
  const [signingRequest, setSigningRequest] = useState<SigningRequest | null>(null);
  const [currentSigner, setCurrentSigner] = useState<SigningRequest['signers'][0] | null>(null);
  const [signatureData, setSignatureData] = useState('');
  const [signerName, setSignerName] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadSigningRequest();
  }, [requestId]);

  const loadSigningRequest = async () => {
    try {
      setIsLoading(true);
      const request = await getDocumentSigningRequestAction(requestId);
      
      if (!request) {
        setError('Signing request not found');
        return;
      }

      setSigningRequest(request);
      
      // Find current user's signer record
      const signer = request.signers.find(s => 
        s.email === user?.email || s.id === user?.id
      );
      
      if (!signer) {
        setError('You are not authorized to sign this document');
        return;
      }

      if (signer.status === 'signed') {
        setError('You have already signed this document');
        return;
      }

      if (signer.status === 'expired') {
        setError('This signing request has expired');
        return;
      }

      setCurrentSigner(signer);
      setSignerName(signer.name);
    } catch (error) {
      console.error('Error loading signing request:', error);
      setError('Failed to load signing request');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSign = async () => {
    if (!signatureData) {
      toast({
        title: "No Signature",
        description: "Please provide your signature before proceeding.",
        variant: "destructive"
      });
      return;
    }

    if (!currentSigner || !signingRequest) {
      return;
    }

    setIsProcessing(true);
    try {
      const result = await processApprovalDocumentSignatureAction({
        requestId,
        signerId: currentSigner.id,
        signatureData,
        signatureMethod: 'digital',
        ipAddress: await getClientIP(),
        userAgent: navigator.userAgent
      });

      if (result.success) {
        toast({
          title: "Document Signed",
          description: "Your signature has been recorded successfully."
        });
        
        // Redirect to success page or back to approvals
        router.push('/approvals?signed=true');
      } else {
        throw new Error(result.error || 'Failed to process signature');
      }
    } catch (error) {
      toast({
        title: "Signing Failed",
        description: error instanceof Error ? error.message : 'Failed to process signature',
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDecline = async () => {
    if (!window.confirm('Are you sure you want to decline to sign this document?')) {
      return;
    }

    // TODO: Implement decline functionality
    toast({
      title: "Document Declined",
      description: "You have declined to sign this document."
    });
    
    router.push('/approvals');
  };

  const getClientIP = async (): Promise<string> => {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch {
      return 'unknown';
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!signingRequest || !currentSigner) {
    return (
      <div className="container mx-auto py-8">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>Invalid signing request</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <FileSignature className="h-12 w-12 mx-auto text-blue-600 mb-4" />
          <h1 className="text-2xl font-bold">Document Signature Required</h1>
          <p className="text-gray-600 mt-2">
            Please review the document and provide your signature below
          </p>
        </div>

        {/* Document Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Document Details</span>
              <Button variant="outline" size="sm" asChild>
                <a href={signingRequest.documentUrl} target="_blank" rel="noopener noreferrer">
                  <Eye className="h-4 w-4 mr-2" />
                  View Document
                </a>
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm font-medium">Document Name</Label>
              <p className="text-sm text-gray-600">{signingRequest.documentName}</p>
            </div>
            
            <div>
              <Label className="text-sm font-medium">Requested by</Label>
              <p className="text-sm text-gray-600">{signingRequest.requesterName}</p>
            </div>

            {signingRequest.message && (
              <div>
                <Label className="text-sm font-medium">Message</Label>
                <p className="text-sm text-gray-600">{signingRequest.message}</p>
              </div>
            )}

            {signingRequest.dueDate && (
              <div>
                <Label className="text-sm font-medium">Due Date</Label>
                <p className="text-sm text-gray-600">
                  {format(new Date(signingRequest.dueDate as string), 'PPP')}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Signature Section */}
        <Card>
          <CardHeader>
            <CardTitle>Your Signature</CardTitle>
            <CardDescription>
              Please provide your signature to complete the signing process
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="signerName">Full Name</Label>
              <Input
                id="signerName"
                value={signerName}
                onChange={(e) => setSignerName(e.target.value)}
                placeholder="Enter your full name"
                className="mt-1"
              />
            </div>

            <div>
              <Label>Digital Signature</Label>
              <div className="mt-2">
                <SignatureCanvas onSignatureChange={setSignatureData} />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex justify-center gap-4">
          <Button
            variant="outline"
            onClick={handleDecline}
            disabled={isProcessing}
          >
            Decline to Sign
          </Button>
          <Button
            onClick={handleSign}
            disabled={isProcessing || !signatureData || !signerName}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isProcessing && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Sign Document
          </Button>
        </div>
      </div>
    </div>
  );
}
