'use server';

import { 
  addDocument, 
  updateDocument, 
  getDocumentById, 
  getCollection,
  uploadFileToStorage 
} from '@/services/firestoreService';
import { revalidatePath } from 'next/cache';
import { Timestamp } from 'firebase/firestore';
import { documentSigningService, type SigningRequest, type DocumentSignature } from '@/services/documentSigningService';
import type { SignatureStatus } from '@/types/firestore';

export interface ApprovalDocument {
  id?: string;
  approvalId: string;
  approvalType: 'travel' | 'purchase' | 'marketing' | 'per_diem' | 'social_post';
  documentName: string;
  documentUrl: string;
  documentType: 'approval_form' | 'supporting_document' | 'contract' | 'agreement' | 'other';
  requiresSignature: boolean;
  signatureStatus: SignatureStatus;
  signedDocumentUrl?: string;
  signingRequestId?: string;
  uploadedBy: string;
  uploadedAt: Timestamp | Date | string;
  createdAt?: Timestamp | Date | string;
  updatedAt?: Timestamp | Date | string;
}

/**
 * Create an approval document
 */
export async function createApprovalDocumentAction(data: {
  approvalId: string;
  approvalType: ApprovalDocument['approvalType'];
  documentName: string;
  documentUrl: string;
  documentType: ApprovalDocument['documentType'];
  requiresSignature: boolean;
  uploadedBy: string;
}): Promise<{ success: boolean; documentId?: string; error?: string }> {
  try {
    const document: Omit<ApprovalDocument, 'id'> = {
      ...data,
      signatureStatus: data.requiresSignature ? 'Pending Signature' : 'Not Applicable',
      uploadedAt: Timestamp.now(),
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };

    const documentId = await addDocument('approval_documents', document);

    revalidatePath('/approvals');
    return { success: true, documentId: typeof documentId === 'string' ? documentId : documentId.id };
  } catch (error) {
    console.error('Error creating approval document:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Upload and create approval document from file
 */
export async function uploadApprovalDocumentAction(data: {
  approvalId: string;
  approvalType: ApprovalDocument['approvalType'];
  file: File;
  documentType: ApprovalDocument['documentType'];
  requiresSignature: boolean;
  uploadedBy: string;
}): Promise<{ success: boolean; documentId?: string; error?: string }> {
  try {
    // Upload file to storage
    const pathPrefix = `approvals/${data.approvalId}/documents/`;
    const documentUrl = await uploadFileToStorage(data.file, pathPrefix);

    // Create document record
    return await createApprovalDocumentAction({
      approvalId: data.approvalId,
      approvalType: data.approvalType,
      documentName: data.file.name,
      documentUrl,
      documentType: data.documentType,
      requiresSignature: data.requiresSignature,
      uploadedBy: data.uploadedBy
    });
  } catch (error) {
    console.error('Error uploading approval document:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Get approval documents for a specific approval
 */
export async function getApprovalDocumentsAction(approvalId: string): Promise<ApprovalDocument[]> {
  try {
    // This would need a query implementation in firestoreService
    // For now, return empty array
    const allDocuments = await getCollection<ApprovalDocument>('approval_documents');
    return allDocuments.filter(doc => doc.approvalId === approvalId);
  } catch (error) {
    console.error('Error getting approval documents:', error);
    return [];
  }
}

/**
 * Create signing request for approval document
 */
export async function createDocumentSigningRequestAction(data: {
  documentId: string;
  signers: Array<{
    email: string;
    name: string;
    role?: string;
  }>;
  message?: string;
  dueDate?: string;
  requesterId: string;
  requesterName: string;
}): Promise<{ success: boolean; requestId?: string; error?: string }> {
  try {
    const document = await getDocumentById<ApprovalDocument>('approval_documents', data.documentId);
    if (!document) {
      return { success: false, error: 'Document not found' };
    }

    // Create signing request
    const result = await documentSigningService.createSigningRequest({
      documentId: data.documentId,
      documentName: document.documentName,
      documentUrl: document.documentUrl,
      documentType: 'approval',
      requesterId: data.requesterId,
      requesterName: data.requesterName,
      signers: data.signers.map((signer, index) => ({
        id: `signer_${Date.now()}_${index}`,
        email: signer.email,
        name: signer.name,
        role: signer.role,
        order: index + 1,
        status: 'pending',
        remindersSent: 0
      })),
      message: data.message,
      dueDate: data.dueDate ? new Date(data.dueDate) : undefined,
      reminderSettings: {
        enabled: true,
        intervalDays: 3,
        maxReminders: 3
      }
    });

    if (result.success && result.requestId) {
      // Update document with signing request ID
      await updateDocument('approval_documents', data.documentId, {
        signingRequestId: result.requestId,
        signatureStatus: 'Pending Signature',
        updatedAt: Timestamp.now()
      });

      // Send the signing request
      const sendResult = await documentSigningService.sendSigningRequest(result.requestId);
      
      if (!sendResult.success) {
        return { success: false, error: sendResult.error };
      }

      revalidatePath('/approvals');
      return { success: true, requestId: result.requestId };
    }

    return { success: false, error: result.error };
  } catch (error) {
    console.error('Error creating document signing request:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Process signature for approval document
 */
export async function processApprovalDocumentSignatureAction(data: {
  requestId: string;
  signerId: string;
  signatureData: string;
  signatureMethod: DocumentSignature['signatureMethod'];
  ipAddress?: string;
  userAgent?: string;
}): Promise<{ success: boolean; signatureId?: string; error?: string }> {
  try {
    const result = await documentSigningService.processSignature(data);
    
    if (result.success) {
      // Get the signing request to find the document
      const signingRequest = await documentSigningService.getSigningRequest(data.requestId);
      
      if (signingRequest) {
        // Check if all signers have signed
        const allSigned = signingRequest.signers.every(s => s.status === 'signed');
        
        if (allSigned) {
          // Update document signature status
          await updateDocument('approval_documents', signingRequest.documentId, {
            signatureStatus: 'Signed',
            updatedAt: Timestamp.now()
          });
        }
      }

      revalidatePath('/approvals');
    }

    return result;
  } catch (error) {
    console.error('Error processing approval document signature:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Get signing request for approval document
 */
export async function getDocumentSigningRequestAction(requestId: string): Promise<SigningRequest | null> {
  try {
    return await documentSigningService.getSigningRequest(requestId);
  } catch (error) {
    console.error('Error getting document signing request:', error);
    return null;
  }
}

/**
 * Update approval document signature status
 */
export async function updateApprovalDocumentSignatureStatusAction(
  documentId: string,
  status: SignatureStatus,
  signedDocumentUrl?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const updateData: any = {
      signatureStatus: status,
      updatedAt: Timestamp.now()
    };

    if (signedDocumentUrl) {
      updateData.signedDocumentUrl = signedDocumentUrl;
    }

    await updateDocument('approval_documents', documentId, updateData);

    revalidatePath('/approvals');
    return { success: true };
  } catch (error) {
    console.error('Error updating approval document signature status:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Get all content approvals (history)
 */
export async function getContentApprovalsAction(): Promise<{ success: boolean; approvals?: any[]; error?: string }> {
  try {
    const approvals = await getCollection('content_approvals');

    return {
      success: true,
      approvals: approvals || []
    };
  } catch (error) {
    console.error('Error fetching content approvals:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Update approval workflow status
 */
export async function updateApprovalWorkflowStatusAction(
  workflowId: string,
  isActive: boolean
): Promise<{ success: boolean; error?: string }> {
  try {
    await updateDocument('content_approval_workflows', workflowId, {
      isActive,
      updatedAt: Timestamp.now()
    });

    revalidatePath('/approvals');
    return { success: true };
  } catch (error) {
    console.error('Error updating approval workflow status:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Delete approval workflow
 */
export async function deleteApprovalWorkflowAction(
  workflowId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Check if workflow is active
    const workflow = await getDocumentById('content_approval_workflows', workflowId);
    if (workflow?.isActive) {
      return { success: false, error: 'Cannot delete active workflow. Please deactivate it first.' };
    }

    await updateDocument('content_approval_workflows', workflowId, {
      isActive: false,
      deletedAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    revalidatePath('/approvals');
    return { success: true };
  } catch (error) {
    console.error('Error deleting approval workflow:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Get approval audit entries
 */
export async function getApprovalAuditEntriesAction(): Promise<{ success: boolean; entries?: any[]; error?: string }> {
  try {
    // In a real implementation, this would fetch from an audit_logs collection
    // For now, we'll return mock data structure
    const entries = await getCollection('audit_logs') || [];

    // Filter for approval-related audit entries
    const approvalEntries = entries.filter((entry: any) =>
      entry.category === 'approval' ||
      entry.action?.includes('approve') ||
      entry.action?.includes('reject') ||
      entry.entityType?.includes('approval')
    );

    return {
      success: true,
      entries: approvalEntries
    };
  } catch (error) {
    console.error('Error fetching approval audit entries:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get approval analytics data
 */
export async function getApprovalAnalyticsAction(
  period: 'daily' | 'weekly' | 'monthly' = 'daily'
): Promise<{ success: boolean; analytics?: any[]; error?: string }> {
  try {
    // In a real implementation, this would aggregate data from various approval collections
    // For now, we'll return a structure that matches the expected analytics format

    // This would typically involve complex aggregation queries across:
    // - travel_entries, purchase_requests, marketing_materials, per_diem_requests, social_posts
    // - content_approvals, approval_documents
    // - audit_logs for timing and performance data

    const mockAnalytics = [
      {
        id: '1',
        period,
        date: new Date(),
        approvalType: 'all' as const,
        totalSubmitted: 45,
        totalApproved: 32,
        totalRejected: 8,
        totalPending: 5,
        avgApprovalTime: 18.5,
        avgRejectionTime: 12.3,
        approvalRate: 71.1,
        rejectionRate: 17.8,
        bottleneckSteps: ['Legal Review', 'Budget Approval'],
        topApprovers: [
          { userId: '1', name: 'John Smith', count: 15 },
          { userId: '2', name: 'Sarah Johnson', count: 12 },
          { userId: '3', name: 'Mike Wilson', count: 8 }
        ],
        slaCompliance: 85.2,
        escalationRate: 12.5
      }
    ];

    return {
      success: true,
      analytics: mockAnalytics
    };
  } catch (error) {
    console.error('Error fetching approval analytics:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Delete approval document
 */
export async function deleteApprovalDocumentAction(documentId: string): Promise<{ success: boolean; error?: string }> {
  try {
    // Note: This would also need to handle file deletion from storage
    await updateDocument('approval_documents', documentId, {
      deleted: true,
      updatedAt: Timestamp.now()
    });
    
    revalidatePath('/approvals');
    return { success: true };
  } catch (error) {
    console.error('Error deleting approval document:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Get all signing requests for user
 */
export async function getUserSigningRequestsAction(userEmail: string): Promise<SigningRequest[]> {
  try {
    // This would need a query implementation to find signing requests where user is a signer
    // For now, return empty array
    return [];
  } catch (error) {
    console.error('Error getting user signing requests:', error);
    return [];
  }
}
