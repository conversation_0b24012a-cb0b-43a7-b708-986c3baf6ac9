/**
 * Professional System Test Page
 * Tests authentication and data services with proper tenant isolation
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  CheckCircle, 
  XCircle, 
  User, 
  Building, 
  Shield, 
  Database,
  RefreshCw
} from "lucide-react";
import { useAuth } from '@/contexts/auth-context';
import { useTenant } from '@/contexts/tenant-context';
import { professionalDataService } from '@/services/professionalDataService';

interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'pending';
  message: string;
  details?: any;
}

export default function ProfessionalTestPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  
  const { user, isAuthenticated } = useAuth();
  const { tenant } = useTenant();

  const runTests = async () => {
    setIsRunning(true);
    const results: TestResult[] = [];

    // Test 1: Authentication
    results.push({
      name: 'Authentication System',
      status: isAuthenticated ? 'pass' : 'fail',
      message: isAuthenticated 
        ? `User authenticated: ${user?.email}` 
        : 'User not authenticated',
      details: user ? {
        id: user.id,
        email: user.email,
        role: user.role,
        tenantId: user.tenantId
      } : null
    });

    // Test 2: Tenant Context
    results.push({
      name: 'Tenant Context',
      status: tenant ? 'pass' : 'fail',
      message: tenant 
        ? `Tenant loaded: ${tenant.name || tenant.id}` 
        : 'Tenant context not available',
      details: tenant
    });

    // Test 3: Tenant Isolation
    if (user?.tenantId) {
      try {
        const tenantUsers = await professionalDataService.users.getByTenant(user.tenantId);
        results.push({
          name: 'Tenant Data Isolation',
          status: 'pass',
          message: `Found ${tenantUsers.length} users in tenant ${user.tenantId}`,
          details: { userCount: tenantUsers.length, tenantId: user.tenantId }
        });
      } catch (error) {
        results.push({
          name: 'Tenant Data Isolation',
          status: 'fail',
          message: `Failed to fetch tenant data: ${error}`,
          details: { error }
        });
      }
    } else {
      results.push({
        name: 'Tenant Data Isolation',
        status: 'fail',
        message: 'No tenant ID available for testing',
        details: null
      });
    }

    // Test 4: Data Services
    if (user?.tenantId) {
      try {
        const [exhibitions, events, tasks, leads, budgets] = await Promise.all([
          professionalDataService.exhibitions.getByTenant(user.tenantId),
          professionalDataService.events.getByTenant(user.tenantId),
          professionalDataService.tasks.getByTenant(user.tenantId),
          professionalDataService.leads.getByTenant(user.tenantId),
          professionalDataService.budgets.getByTenant(user.tenantId)
        ]);

        results.push({
          name: 'Data Services',
          status: 'pass',
          message: 'All data services working correctly',
          details: {
            exhibitions: exhibitions.length,
            events: events.length,
            tasks: tasks.length,
            leads: leads.length,
            budgets: budgets.length
          }
        });
      } catch (error) {
        results.push({
          name: 'Data Services',
          status: 'fail',
          message: `Data services error: ${error}`,
          details: { error }
        });
      }
    }

    // Test 5: Role-Based Access
    results.push({
      name: 'Role-Based Access',
      status: user?.role ? 'pass' : 'fail',
      message: user?.role 
        ? `User role: ${user.role}` 
        : 'No role assigned',
      details: {
        role: user?.role,
        isAdmin: user?.role === 'admin' || user?.role === 'super_admin',
        isSuperAdmin: user?.role === 'super_admin'
      }
    });

    setTestResults(results);
    setIsRunning(false);
  };

  useEffect(() => {
    if (isAuthenticated && user) {
      runTests();
    }
  }, [isAuthenticated, user]);

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'fail':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <RefreshCw className="h-5 w-5 text-yellow-500 animate-spin" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return 'bg-green-50 border-green-200';
      case 'fail':
        return 'bg-red-50 border-red-200';
      case 'pending':
        return 'bg-yellow-50 border-yellow-200';
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto p-6">
        <Alert>
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Please log in to test the professional system. Use one of your working credentials:
            <ul className="mt-2 space-y-1">
              <li>• <EMAIL> / Superman123!</li>
              <li>• <EMAIL> / Admin123!</li>
              <li>• <EMAIL> / Manager123!</li>
              <li>• <EMAIL> / User123!</li>
            </ul>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const passedTests = testResults.filter(t => t.status === 'pass').length;
  const totalTests = testResults.length;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Professional System Test</h1>
          <p className="text-muted-foreground">
            Testing authentication, tenant isolation, and data services
          </p>
        </div>
        <Button onClick={runTests} disabled={isRunning}>
          <RefreshCw className={`h-4 w-4 mr-2 ${isRunning ? 'animate-spin' : ''}`} />
          Run Tests
        </Button>
      </div>

      {/* System Status */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">User</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{user?.displayName}</div>
            <p className="text-xs text-muted-foreground">{user?.email}</p>
            <Badge variant="outline" className="mt-1">{user?.role}</Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tenant</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{tenant?.name || 'N/A'}</div>
            <p className="text-xs text-muted-foreground">{user?.tenantId}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tests</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{passedTests}/{totalTests}</div>
            <p className="text-xs text-muted-foreground">Tests Passed</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {passedTests === totalTests ? 'PASS' : 'FAIL'}
            </div>
            <p className="text-xs text-muted-foreground">System Status</p>
          </CardContent>
        </Card>
      </div>

      {/* Test Results */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Test Results</h2>
        {testResults.map((test, index) => (
          <Card key={index} className={getStatusColor(test.status)}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">{test.name}</CardTitle>
                {getStatusIcon(test.status)}
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm mb-2">{test.message}</p>
              {test.details && (
                <details className="text-xs">
                  <summary className="cursor-pointer text-muted-foreground">
                    View Details
                  </summary>
                  <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                    {JSON.stringify(test.details, null, 2)}
                  </pre>
                </details>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
