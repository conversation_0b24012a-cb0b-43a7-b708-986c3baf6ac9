"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  BarChart3,
  TrendingUp,
  DollarSign,
  Users,
  Target,
  Activity,
  Zap,
  Brain,
  RefreshCw,
  Download,
  Settings,
  PieChart,
  LineChart,
  Building2,
  MessageSquare,
  Calendar,
  ArrowRight
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';
import { ModulePermissionGate } from "@/components/permissions/PermissionGate";
import { UpgradePrompt } from "@/components/billing/UpgradePrompt";

// Import analytics components
import FinancialAnalyticsWidget from '@/components/dashboard/widgets/FinancialAnalyticsWidget';
import RealTimeAnalyticsWidget from '@/app/(app)/dashboard/components/widgets/RealTimeAnalyticsWidget';
import { AnalyticsDashboard } from '@/app/(app)/dashboard/components/analytics-dashboard';

// Import services - NO MOCK DATA, ONLY REAL FIREBASE DATA
import { AnalyticsService } from '@/services/analyticsService';
import { evexanaAnalyticsService } from '@/services/evexanaAnalyticsService';
import { travelIntelligenceAnalyticsService } from '@/services/travelIntelligenceAnalyticsService';
import {
  getExhibitions,
  getEvents,
  getLeads,
  getTasks,
  getAllBudgets,
  getAllExpenses,
  getVendors,
  getBoothAnalytics
} from '@/services/firestoreService';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';

import type {
  Exhibition,
  Event as EvexEvent,
  Lead,
  Task,
  Budget,
  Expense,
  Vendor,
  BoothAnalyticsData
} from '@/types/firestore';

interface AnalyticsOverview {
  totalEvents: number;
  totalLeads: number;
  totalRevenue: number;
  activeVendors: number;
  completedTasks: number;
  avgROI: number;
  engagementRate: number;
  conversionRate: number;
}

export default function CentralizedAnalyticsPage() {
  return (
    <ModulePermissionGate
      module="analytics"
      action="read"
      fallback={
        <div className="container mx-auto py-8">
          <UpgradePrompt
            trigger="feature_locked"
            feature="Analytics"
            description="Access to analytics requires appropriate permissions."
          />
        </div>
      }
    >
      <CentralizedAnalyticsContent />
    </ModulePermissionGate>
  );
}

function CentralizedAnalyticsContent() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  
  // Data states
  const [analyticsData, setAnalyticsData] = useState<{
    exhibitions: Exhibition[];
    events: EvexEvent[];
    leads: Lead[];
    tasks: Task[];
    budgets: Budget[];
    expenses: Expense[];
    vendors: Vendor[];
    boothAnalytics: BoothAnalyticsData | null;
  } | null>(null);

  const [overview, setOverview] = useState<AnalyticsOverview | null>(null);
  const [evexanaMetrics, setEvexanaMetrics] = useState<any>(null);
  const [travelAnalytics, setTravelAnalytics] = useState<any>(null);
  const [socialMediaAnalytics, setSocialMediaAnalytics] = useState<any>(null);
  const [contactAnalytics, setContactAnalytics] = useState<any>(null);
  const [attendeeAnalytics, setAttendeeAnalytics] = useState<any>(null);

  useEffect(() => {
    loadAllAnalytics();
  }, []);

  const loadAllAnalytics = async () => {
    setIsLoading(true);
    try {
      // Load all data in parallel - NO MOCK DATA, ONLY REAL FIREBASE DATA
      const [exhibitions, events, leads, tasks, budgets, expenses, vendors] = await Promise.all([
        getExhibitions(),
        getEvents(),
        getLeads(),
        getTasks(),
        getAllBudgets(),
        getAllExpenses(),
        getVendors()
      ]);

      // Load social media analytics from Firebase
      try {
        const socialSnapshot = await getDocs(collection(db, 'social_posts'));
        const analyticsSnapshot = await getDocs(collection(db, 'social_analytics'));

        const socialPosts = socialSnapshot.docs.map(doc => doc.data());
        const socialAnalytics = analyticsSnapshot.docs.map(doc => doc.data());

        // Calculate real social media metrics
        const totalReach = socialAnalytics.reduce((sum, item) => sum + (item.reach || 0), 0);
        const totalEngagement = socialAnalytics.reduce((sum, item) => sum + (item.engagement || 0), 0);
        const postsPublished = socialPosts.length;
        const newFollowers = socialAnalytics.reduce((sum, item) => sum + (item.newFollowers || 0), 0);
        const engagementRate = postsPublished > 0 ? (totalEngagement / postsPublished) : 0;

        setSocialMediaAnalytics({
          totalReach,
          engagementRate,
          postsPublished,
          newFollowers
        });
      } catch (error) {
        console.log('No social media analytics data available');
        setSocialMediaAnalytics({
          totalReach: 0,
          engagementRate: 0,
          postsPublished: 0,
          newFollowers: 0
        });
      }

      // Load attendee analytics from Firebase
      try {
        const attendeeSnapshot = await getDocs(collection(db, 'attendees'));
        const experienceSnapshot = await getDocs(collection(db, 'attendee_experience'));

        const attendees = attendeeSnapshot.docs.map(doc => doc.data());
        const experiences = experienceSnapshot.docs.map(doc => doc.data());

        // Calculate real attendee metrics
        const totalAttendees = attendees.length;
        const engagementRate = experiences.length > 0 ?
          (experiences.filter(exp => exp.engaged).length / experiences.length) * 100 : 0;
        const averageExperienceScore = experiences.length > 0 ?
          experiences.reduce((sum, exp) => sum + (exp.score || 0), 0) / experiences.length : 0;

        setAttendeeAnalytics({
          totalAttendees,
          engagementRate,
          averageExperienceScore
        });
      } catch (error) {
        console.log('No attendee analytics data available');
        setAttendeeAnalytics({
          totalAttendees: 0,
          engagementRate: 0,
          averageExperienceScore: 0
        });
      }

      // Load booth analytics (real data only)
      let boothAnalytics: BoothAnalyticsData | null = null;
      try {
        // Use first exhibition ID if available, otherwise default
        const exhibitionId = exhibitions.length > 0 && exhibitions[0].id ? exhibitions[0].id : 'default';
        boothAnalytics = await getBoothAnalytics(exhibitionId);
      } catch (error) {
        console.log('No booth analytics data available');
        boothAnalytics = null;
      }

      const data = { exhibitions, events, leads, tasks, budgets, expenses, vendors, boothAnalytics };
      setAnalyticsData(data);

      // Calculate overview metrics from REAL DATA ONLY
      const totalRevenue = budgets.reduce((sum, budget) => sum + (budget.totalBudget || 0), 0);
      const completedTasks = tasks.filter(task => task.status === 'Done').length;
      const activeVendors = vendors.filter(vendor => vendor.status === 'Active').length;
      const totalSpent = expenses.reduce((sum, expense) => sum + (expense.amount || 0), 0);

      // Calculate real ROI from actual data
      const actualROI = totalRevenue > 0 && totalSpent > 0 ? ((totalRevenue - totalSpent) / totalSpent) * 100 : 0;

      // Calculate real engagement rate from leads and events
      const totalAttendees = leads.length;
      const totalEvents = exhibitions.length + events.length;
      const realEngagementRate = totalEvents > 0 ? (totalAttendees / totalEvents) * 100 : 0;

      // Calculate real conversion rate from leads
      const convertedLeads = leads.filter(lead => lead.status === 'converted').length;
      const realConversionRate = leads.length > 0 ? (convertedLeads / leads.length) * 100 : 0;

      const overviewMetrics: AnalyticsOverview = {
        totalEvents: exhibitions.length + events.length,
        totalLeads: leads.length,
        totalRevenue,
        activeVendors,
        completedTasks,
        avgROI: Number(actualROI.toFixed(1)),
        engagementRate: Number(realEngagementRate.toFixed(1)),
        conversionRate: Number(realConversionRate.toFixed(1))
      };

      setOverview(overviewMetrics);

      // Load EVEXANA analytics (real data only)
      const evexanaData = evexanaAnalyticsService.getAnalyticsMetrics();
      setEvexanaMetrics(evexanaData);

      // Load Travel Analytics (real data only)
      try {
        const endDate = new Date();
        const startDate = new Date(endDate.getFullYear(), endDate.getMonth() - 3, 1);
        const travelData = await travelIntelligenceAnalyticsService.generateTravelSpendAnalytics('quarterly', startDate, endDate);
        setTravelAnalytics(travelData);
      } catch (error) {
        console.log('No travel analytics data available');
        setTravelAnalytics(null);
      }



      // Load contact analytics from Firebase
      try {
        const contactSnapshot = await getDocs(collection(db, 'contacts'));
        const contacts = contactSnapshot.docs.map(doc => doc.data());

        // Calculate real contact metrics
        const totalContacts = contacts.length;
        const qualifiedContacts = contacts.filter(contact => contact.status === 'qualified').length;
        const activeContacts = contacts.filter(contact => contact.status === 'active' || contact.status === 'qualified').length;
        const conversionRate = totalContacts > 0 ? (qualifiedContacts / totalContacts) * 100 : 0;

        setContactAnalytics({
          totalContacts,
          qualifiedContacts,
          activeContacts,
          conversionRate
        });
      } catch (error) {
        console.log('No contact analytics data available');
        setContactAnalytics({
          totalContacts: 0,
          qualifiedContacts: 0,
          activeContacts: 0,
          conversionRate: 0
        });
      }

      setLastRefresh(new Date());
      
      toast({
        title: "Analytics Updated",
        description: "All analytics data has been refreshed successfully.",
      });
    } catch (error) {
      console.error('Error loading analytics:', error);
      toast({
        title: "Error",
        description: "Failed to load analytics data. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const exportAllAnalytics = () => {
    // Export functionality for all analytics
    toast({
      title: "Export Started",
      description: "Comprehensive analytics export has been initiated.",
    });
  };

  if (isLoading && !analyticsData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto text-primary" />
          <p className="text-muted-foreground">Loading comprehensive analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight font-headline flex items-center gap-2">
            <BarChart3 className="h-8 w-8" /> 
            Analytics Hub
          </h1>
          <p className="text-muted-foreground">
            Comprehensive analytics and insights across all EVEXA modules
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            Last updated: {lastRefresh.toLocaleString()}
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" onClick={loadAllAnalytics} disabled={isLoading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh All
          </Button>
          <Button variant="outline" onClick={exportAllAnalytics}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" asChild>
            <Link href="/settings/analytics-config">
              <Settings className="mr-2 h-4 w-4" />
              Configure
            </Link>
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      {overview && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Events</p>
                  <p className="text-2xl font-bold">{overview.totalEvents}</p>
                </div>
                <Calendar className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Revenue</p>
                  <p className="text-2xl font-bold">${overview.totalRevenue.toLocaleString()}</p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Active Vendors</p>
                  <p className="text-2xl font-bold">{overview.activeVendors}</p>
                </div>
                <Building2 className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Avg ROI</p>
                  <p className="text-2xl font-bold">{overview.avgROI}%</p>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5 lg:grid-cols-10">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="marketing">Marketing</TabsTrigger>
          <TabsTrigger value="contacts">Contacts</TabsTrigger>
          <TabsTrigger value="attendees">Attendees</TabsTrigger>
          <TabsTrigger value="social">Social Media</TabsTrigger>
          <TabsTrigger value="vendors">Vendors</TabsTrigger>
          <TabsTrigger value="booth">Booth</TabsTrigger>
          <TabsTrigger value="travel">Travel</TabsTrigger>
          <TabsTrigger value="ai">AI Insights</TabsTrigger>
          <TabsTrigger value="realtime">Real-time</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {analyticsData && (
            <AnalyticsDashboard 
              data={analyticsData}
              onRefresh={loadAllAnalytics}
              loading={isLoading}
            />
          )}
        </TabsContent>

        <TabsContent value="financial" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <FinancialAnalyticsWidget />
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Financial Analytics
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/financials/analytics">
                      <ArrowRight className="mr-2 h-4 w-4" />
                      View Details
                    </Link>
                  </Button>
                </CardTitle>
                <CardDescription>
                  Comprehensive financial performance analysis
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Access detailed financial analytics including profitability analysis, 
                  cost center breakdown, ROI predictions, and financial scenarios.
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="marketing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Marketing Analytics
                <Button variant="outline" size="sm" asChild>
                  <Link href="/marketing/analytics">
                    <ArrowRight className="mr-2 h-4 w-4" />
                    View Details
                  </Link>
                </Button>
              </CardTitle>
              <CardDescription>
                Campaign performance, attribution analysis, and ROI tracking
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">{overview?.engagementRate}%</p>
                  <p className="text-sm text-muted-foreground">Engagement Rate</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{overview?.conversionRate}%</p>
                  <p className="text-sm text-muted-foreground">Conversion Rate</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">{overview?.totalLeads}</p>
                  <p className="text-sm text-muted-foreground">Total Leads</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contacts" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Contact Analytics
                <Button variant="outline" size="sm" asChild>
                  <Link href="/contacts?tab=analytics">
                    <ArrowRight className="h-4 w-4 mr-2" />
                    View in Contacts
                  </Link>
                </Button>
              </CardTitle>
              <CardDescription>
                Contact engagement, segmentation, and conversion analytics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{contactAnalytics?.totalContacts || 0}</div>
                  <div className="text-sm text-muted-foreground">Total Contacts</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{contactAnalytics?.activeContacts || 0}</div>
                  <div className="text-sm text-muted-foreground">Active Contacts</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{contactAnalytics?.conversionRate?.toFixed(1) || 0}%</div>
                  <div className="text-sm text-muted-foreground">Conversion Rate</div>
                </div>
              </div>

            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="attendees" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Attendee Analytics
                <Button variant="outline" size="sm" asChild>
                  <Link href="/attendees/experience?tab=analytics">
                    <ArrowRight className="h-4 w-4 mr-2" />
                    View in Attendees
                  </Link>
                </Button>
              </CardTitle>
              <CardDescription>
                Attendee engagement, experience, and behavior analytics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{attendeeAnalytics?.totalAttendees || 0}</div>
                  <div className="text-sm text-muted-foreground">Total Attendees</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{attendeeAnalytics?.engagementRate?.toFixed(1) || 0}%</div>
                  <div className="text-sm text-muted-foreground">Engagement Rate</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {attendeeAnalytics?.averageExperienceScore?.toFixed(1) || '0.0'}
                  </div>
                  <div className="text-sm text-muted-foreground">Avg Experience Score</div>
                </div>
              </div>

            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="social" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Social Media Analytics
                <Button variant="outline" size="sm" asChild>
                  <Link href="/smm/analytics">
                    <ArrowRight className="h-4 w-4 mr-2" />
                    View in Social Media Hub
                  </Link>
                </Button>
              </CardTitle>
              <CardDescription>
                Social media performance, engagement, and reach analytics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {socialMediaAnalytics?.totalReach?.toLocaleString() || '0'}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Reach</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {socialMediaAnalytics?.engagementRate?.toFixed(1) || '0'}%
                  </div>
                  <div className="text-sm text-muted-foreground">Engagement Rate</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {socialMediaAnalytics?.postsPublished || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Posts Published</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">
                    {socialMediaAnalytics?.newFollowers?.toLocaleString() || '0'}
                  </div>
                  <div className="text-sm text-muted-foreground">New Followers</div>
                </div>
              </div>
              {socialMediaAnalytics && (
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">Social media analytics data loaded from Firebase</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="vendors" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Vendor Analytics
                <Button variant="outline" size="sm" asChild>
                  <Link href="/procurement/vendors/analytics">
                    <ArrowRight className="mr-2 h-4 w-4" />
                    View Details
                  </Link>
                </Button>
              </CardTitle>
              <CardDescription>
                Vendor performance, spending analysis, and risk assessment
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">{overview?.activeVendors || 0}</p>
                  <p className="text-sm text-muted-foreground">Active Vendors</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">
                    {analyticsData?.vendors ?
                      (analyticsData.vendors.reduce((sum, v) => sum + (v.rating || 0), 0) / analyticsData.vendors.length).toFixed(1)
                      : '0'}/5
                  </p>
                  <p className="text-sm text-muted-foreground">Avg Rating</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="booth" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Booth Analytics
                <Button variant="outline" size="sm" asChild>
                  <Link href="/performance/booth-analytics">
                    <ArrowRight className="mr-2 h-4 w-4" />
                    View Details
                  </Link>
                </Button>
              </CardTitle>
              <CardDescription>
                Real-time booth visitor analytics and engagement metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analyticsData?.boothAnalytics ? (
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">
                      {analyticsData.boothAnalytics.hourlyTraffic.reduce((sum, h) => sum + h.visitors, 0)}
                    </p>
                    <p className="text-sm text-muted-foreground">Total Visitors</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">
                      {(analyticsData.boothAnalytics.zoneDwellTimes.reduce((sum, z) => sum + z.time, 0) /
                        analyticsData.boothAnalytics.zoneDwellTimes.length || 1).toFixed(1)}m
                    </p>
                    <p className="text-sm text-muted-foreground">Avg Dwell Time</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-purple-600">
                      {analyticsData.boothAnalytics.hourlyTraffic.length > 0 ?
                        analyticsData.boothAnalytics.hourlyTraffic.reduce((prev, current) =>
                          (prev.visitors > current.visitors) ? prev : current
                        ).time : 'N/A'}
                    </p>
                    <p className="text-sm text-muted-foreground">Peak Hour</p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No booth analytics data available</p>
                  <p className="text-sm text-muted-foreground mt-2">
                    Booth analytics will appear here when exhibition data is available
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="travel" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Travel Intelligence Analytics
                <Button variant="outline" size="sm" asChild>
                  <Link href="/travel/intelligence-analytics">
                    <ArrowRight className="mr-2 h-4 w-4" />
                    View Details
                  </Link>
                </Button>
              </CardTitle>
              <CardDescription>
                Travel spend analysis, policy compliance, and optimization insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              {travelAnalytics ? (
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">
                      ${travelAnalytics.totalSpend?.toLocaleString() || '0'}
                    </p>
                    <p className="text-sm text-muted-foreground">Total Travel Spend</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">
                      {travelAnalytics.complianceRate || '0'}%
                    </p>
                    <p className="text-sm text-muted-foreground">Policy Compliance</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-purple-600">
                      ${travelAnalytics.potentialSavings?.toLocaleString() || '0'}
                    </p>
                    <p className="text-sm text-muted-foreground">Potential Savings</p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No travel analytics data available</p>
                  <p className="text-sm text-muted-foreground mt-2">
                    Travel analytics will appear here when travel data is available
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ai" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                EVEXANA AI Analytics
              </CardTitle>
              <CardDescription>
                AI assistant usage, insights generation, and automation metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              {evexanaMetrics && (
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">{evexanaMetrics.totalInteractions}</p>
                    <p className="text-sm text-muted-foreground">Total Interactions</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">{evexanaMetrics.uniqueUsers}</p>
                    <p className="text-sm text-muted-foreground">Active Users</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-purple-600">{evexanaMetrics.userSatisfactionScore}%</p>
                    <p className="text-sm text-muted-foreground">Satisfaction</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="realtime" className="space-y-6">
          <RealTimeAnalyticsWidget />
        </TabsContent>
      </Tabs>
    </div>
  );
}
