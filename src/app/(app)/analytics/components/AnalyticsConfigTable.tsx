"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { 
  Settings, 
  Eye, 
  Edit2, 
  Trash2, 
  Play, 
  Pause, 
  BarChart3, 
  Activity, 
  Calendar, 
  Target,
  TrendingUp,
  Database,
  Clock
} from 'lucide-react';
import { getAnalyticsConfigs } from '@/services/firestoreService';
import type { AnalyticsConfig } from '@/types/firestore';

interface AnalyticsConfigTableProps {
  className?: string;
  onConfigEdit?: (config: AnalyticsConfig) => void;
  onConfigDelete?: (configId: string) => void;
  onConfigToggle?: (configId: string, enabled: boolean) => void;
}

interface EnhancedAnalyticsConfig extends AnalyticsConfig {
  metricsCount: number;
  lastExecuted?: Date;
  executionCount: number;
  averageExecutionTime: number;
  dataPointsCollected: number;
}

export default function AnalyticsConfigTable({ 
  className, 
  onConfigEdit, 
  onConfigDelete, 
  onConfigToggle 
}: AnalyticsConfigTableProps) {
  const [configs, setConfigs] = useState<EnhancedAnalyticsConfig[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchConfigs = async () => {
      try {
        setIsLoading(true);
        
        const configsData = await getAnalyticsConfigs();
        
        // Enhance configs with additional data
        const enhancedConfigs: EnhancedAnalyticsConfig[] = configsData.map(config => ({
          ...config,
          metricsCount: config.metrics?.length || 0,
          lastExecuted: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // Mock last execution
          executionCount: Math.floor(Math.random() * 100) + 1, // Mock execution count
          averageExecutionTime: Math.floor(Math.random() * 5000) + 500, // Mock execution time in ms
          dataPointsCollected: Math.floor(Math.random() * 10000) + 100, // Mock data points
        }));

        setConfigs(enhancedConfigs);
      } catch (error) {
        console.error('Error fetching analytics configs:', error);
        toast({
          title: "Error",
          description: "Failed to load analytics configurations. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchConfigs();
  }, [toast]);

  const columns: AdvancedTableColumn<EnhancedAnalyticsConfig>[] = [
    {
      accessorKey: 'name',
      title: 'Configuration',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const config = row.original;
        return (
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <BarChart3 className="h-4 w-4 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="font-medium">{config.name}</div>
              {config.description && (
                <div className="text-xs text-muted-foreground truncate">
                  {config.description}
                </div>
              )}
              <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Target className="h-3 w-3" />
                  {config.metricsCount} metrics
                </div>
                <div className="flex items-center gap-1">
                  <Database className="h-3 w-3" />
                  {config.dataPointsCollected} data points
                </div>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'type',
      title: 'Type',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const type = row.original.type;
        const typeConfig = {
          booth_analytics: { variant: 'default' as const, label: 'Booth Analytics' },
          performance_metrics: { variant: 'secondary' as const, label: 'Performance' },
          business_metrics: { variant: 'outline' as const, label: 'Business' },
          custom: { variant: 'destructive' as const, label: 'Custom' },
        };
        const config = typeConfig[type] || typeConfig.custom;
        
        return (
          <Badge variant={config.variant}>
            {config.label}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'enabled',
      title: 'Status',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const enabled = row.original.enabled;
        return (
          <Badge variant={enabled ? 'default' : 'secondary'} className={enabled ? 'bg-green-100 text-green-700' : ''}>
            {enabled ? (
              <>
                <Play className="h-3 w-3 mr-1" />
                Active
              </>
            ) : (
              <>
                <Pause className="h-3 w-3 mr-1" />
                Inactive
              </>
            )}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'schedule',
      title: 'Schedule',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const schedule = row.original.schedule;
        return schedule ? (
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-muted-foreground" />
            <Badge variant="outline" className="text-xs">
              {schedule}
            </Badge>
          </div>
        ) : (
          <span className="text-muted-foreground text-sm">Manual</span>
        );
      },
    },
    {
      accessorKey: 'lastExecuted',
      title: 'Last Executed',
      sortable: true,
      cell: ({ row }) => {
        const lastExecuted = row.original.lastExecuted;
        if (!lastExecuted) {
          return <span className="text-sm text-muted-foreground">Never</span>;
        }
        
        return (
          <div className="text-sm">
            <div>{format(lastExecuted, 'MMM d, HH:mm')}</div>
            <div className="text-xs text-muted-foreground">
              {row.original.executionCount} executions
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'performance',
      title: 'Performance',
      sortable: true,
      cell: ({ row }) => {
        const config = row.original;
        const avgTime = config.averageExecutionTime;
        const timeLabel = avgTime < 1000 ? `${avgTime}ms` : `${(avgTime / 1000).toFixed(1)}s`;
        
        return (
          <div className="flex items-center gap-2">
            <Activity className="h-4 w-4 text-muted-foreground" />
            <Badge variant={avgTime < 2000 ? 'default' : avgTime < 5000 ? 'secondary' : 'destructive'}>
              {timeLabel} avg
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      title: 'Created',
      sortable: true,
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        if (!createdAt) return null;
        
        const date = new Date(createdAt);
        return (
          <div className="flex items-center gap-1 text-sm">
            <Calendar className="h-3 w-3 text-muted-foreground" />
            {format(date, 'MMM d, yyyy')}
          </div>
        );
      },
    },
  ];

  const rowActions = (row: EnhancedAnalyticsConfig): AdvancedTableRowAction<EnhancedAnalyticsConfig>[] => {
    const actions: AdvancedTableRowAction<EnhancedAnalyticsConfig>[] = [
      {
        type: 'view',
        label: 'View Configuration',
        onClick: () => {
          toast({
            title: "View Configuration",
            description: `Viewing ${row.name} configuration...`,
          });
        },
        icon: <Eye className="h-4 w-4" />,
      },
    ];

    if (onConfigEdit) {
      actions.push({
        type: 'edit',
        label: 'Edit Configuration',
        onClick: () => onConfigEdit(row),
        icon: <Edit2 className="h-4 w-4" />,
      });
    }

    if (onConfigToggle) {
      actions.push({
        type: 'custom',
        label: row.enabled ? 'Disable' : 'Enable',
        onClick: () => onConfigToggle(row.id!, !row.enabled),
        icon: row.enabled ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />,
        variant: row.enabled ? 'secondary' : 'default',
      });
    }

    actions.push({
      type: 'custom',
      label: 'Run Now',
      onClick: () => {
        toast({
          title: "Execute Configuration",
          description: `Running ${row.name} analytics...`,
        });
      },
      icon: <TrendingUp className="h-4 w-4" />,
    });

    if (onConfigDelete) {
      actions.push({
        type: 'delete',
        label: 'Delete Configuration',
        onClick: () => onConfigDelete(row.id!),
        icon: <Trash2 className="h-4 w-4" />,
        variant: 'destructive',
      });
    }

    return actions;
  };

  const filters = [
    {
      id: 'type',
      label: 'Configuration Type',
      type: 'select' as const,
      options: [
        { label: 'Booth Analytics', value: 'booth_analytics' },
        { label: 'Performance Metrics', value: 'performance_metrics' },
        { label: 'Business Metrics', value: 'business_metrics' },
        { label: 'Custom', value: 'custom' },
      ],
    },
    {
      id: 'enabled',
      label: 'Status',
      type: 'select' as const,
      options: [
        { label: 'Active', value: 'true' },
        { label: 'Inactive', value: 'false' },
      ],
    },
    {
      id: 'schedule',
      label: 'Schedule Type',
      type: 'select' as const,
      options: [
        { label: 'Hourly', value: 'hourly' },
        { label: 'Daily', value: 'daily' },
        { label: 'Weekly', value: 'weekly' },
        { label: 'Manual', value: '' },
      ],
    },
  ];

  const bulkActions = [
    {
      id: 'enable',
      label: 'Enable Selected',
      icon: <Play className="h-4 w-4" />,
      onClick: (selectedRows: EnhancedAnalyticsConfig[]) => {
        toast({
          title: "Enable Configurations",
          description: `Enabling ${selectedRows.length} configuration(s)...`,
        });
      },
    },
    {
      id: 'disable',
      label: 'Disable Selected',
      icon: <Pause className="h-4 w-4" />,
      onClick: (selectedRows: EnhancedAnalyticsConfig[]) => {
        toast({
          title: "Disable Configurations",
          description: `Disabling ${selectedRows.length} configuration(s)...`,
        });
      },
    },
    {
      id: 'execute',
      label: 'Execute Selected',
      icon: <TrendingUp className="h-4 w-4" />,
      onClick: (selectedRows: EnhancedAnalyticsConfig[]) => {
        toast({
          title: "Execute Configurations",
          description: `Running ${selectedRows.length} configuration(s)...`,
        });
      },
    },
  ];

  return (
    <AdvancedDataTable
      data={configs}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      bulkActions={bulkActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="analytics-configurations"
      searchPlaceholder="Search analytics configurations..."
      emptyMessage="No analytics configurations found. Create a new configuration to start collecting data."
      loading={isLoading}
      className={className}
      variant="default"
    />
  );
}
