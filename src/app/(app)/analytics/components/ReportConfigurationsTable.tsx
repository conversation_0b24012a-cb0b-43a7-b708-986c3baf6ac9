"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { 
  FileText, 
  Eye, 
  Edit2, 
  Trash2, 
  Play, 
  Pause, 
  Download, 
  Share2,
  Calendar,
  Clock,
  Settings,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  LineChart,
  Users,
  Building
} from 'lucide-react';
// import { getReportConfigurations } from '@/services/firestoreService';
import type { ReportConfiguration } from '@/types/firestore';

interface ReportConfigurationsTableProps {
  className?: string;
  onConfigEdit?: (config: ReportConfiguration) => void;
  onConfigDelete?: (configId: string) => void;
  onConfigToggle?: (configId: string, enabled: boolean) => void;
  onConfigGenerate?: (configId: string) => void;
}

interface EnhancedReportConfiguration extends ReportConfiguration {
  lastGenerated?: Date;
  generationCount: number;
  averageGenerationTime: number;
  recipientCount: number;
  fileSize: number;
}

export default function ReportConfigurationsTable({ 
  className, 
  onConfigEdit, 
  onConfigDelete, 
  onConfigToggle,
  onConfigGenerate
}: ReportConfigurationsTableProps) {
  const [configs, setConfigs] = useState<EnhancedReportConfiguration[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchConfigs = async () => {
      try {
        setIsLoading(true);
        
        // Mock report configurations data
        const configsData: ReportConfiguration[] = [
          {
            id: '1',
            name: 'Daily Performance Report',
            description: 'Daily summary of key performance metrics',
            type: 'performance',
            format: 'pdf',
            schedule: 'daily',
            enabled: true,
            recipients: ['<EMAIL>', '<EMAIL>'],
            parameters: {
              includeCharts: true,
              includeComparisons: true,
              dateRange: '24h',
            },
            createdBy: 'user1',
            createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: '2',
            name: 'Weekly Analytics Summary',
            description: 'Comprehensive weekly analytics report',
            type: 'analytics',
            format: 'excel',
            schedule: 'weekly',
            enabled: true,
            recipients: ['<EMAIL>'],
            parameters: {
              includeRawData: true,
              includeVisualizations: false,
              dateRange: '7d',
            },
            createdBy: 'user2',
            createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: '3',
            name: 'Executive Dashboard',
            description: 'High-level executive summary report',
            type: 'executive',
            format: 'pdf',
            schedule: 'weekly',
            enabled: false,
            recipients: ['<EMAIL>', '<EMAIL>'],
            parameters: {
              includeFinancials: true,
              includeKPIs: true,
              dateRange: '30d',
            },
            createdBy: 'user1',
            createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          },
        ];
        
        // Enhance configs with additional data
        const enhancedConfigs: EnhancedReportConfiguration[] = configsData.map(config => ({
          ...config,
          lastGenerated: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
          generationCount: Math.floor(Math.random() * 50) + 1,
          averageGenerationTime: Math.floor(Math.random() * 30) + 5, // seconds
          recipientCount: config.recipients?.length || 0,
          fileSize: Math.floor(Math.random() * 5000) + 500, // KB
        }));

        setConfigs(enhancedConfigs);
      } catch (error) {
        console.error('Error fetching report configurations:', error);
        toast({
          title: "Error",
          description: "Failed to load report configurations. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchConfigs();
  }, [toast]);

  const columns: AdvancedTableColumn<EnhancedReportConfiguration>[] = [
    {
      accessorKey: 'name',
      title: 'Report Configuration',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const config = row.original;
        const typeIcons = {
          performance: BarChart3,
          analytics: PieChart,
          executive: LineChart,
          custom: FileText,
        };
        const IconComponent = typeIcons[config.type] || FileText;
        
        return (
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <IconComponent className="h-4 w-4 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="font-medium">{config.name}</div>
              {config.description && (
                <div className="text-xs text-muted-foreground truncate">
                  {config.description}
                </div>
              )}
              <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  {config.recipientCount} recipients
                </div>
                <div className="flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  {config.fileSize}KB avg
                </div>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'type',
      title: 'Type',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const type = row.original.type;
        const typeConfig = {
          performance: { variant: 'default' as const, label: 'Performance' },
          analytics: { variant: 'secondary' as const, label: 'Analytics' },
          executive: { variant: 'outline' as const, label: 'Executive' },
          custom: { variant: 'destructive' as const, label: 'Custom' },
        };
        const config = typeConfig[type] || typeConfig.custom;
        
        return (
          <Badge variant={config.variant}>
            {config.label}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'format',
      title: 'Format',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const format = row.original.format;
        return (
          <Badge variant="outline" className="uppercase">
            {format}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'schedule',
      title: 'Schedule',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const schedule = row.original.schedule;
        return (
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-muted-foreground" />
            <Badge variant="outline" className="text-xs capitalize">
              {schedule}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'enabled',
      title: 'Status',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const enabled = row.original.enabled;
        return (
          <Badge variant={enabled ? 'default' : 'secondary'} className={enabled ? 'bg-green-100 text-green-700' : ''}>
            {enabled ? (
              <>
                <Play className="h-3 w-3 mr-1" />
                Active
              </>
            ) : (
              <>
                <Pause className="h-3 w-3 mr-1" />
                Inactive
              </>
            )}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'lastGenerated',
      title: 'Last Generated',
      sortable: true,
      cell: ({ row }) => {
        const lastGenerated = row.original.lastGenerated;
        if (!lastGenerated) {
          return <span className="text-sm text-muted-foreground">Never</span>;
        }
        
        return (
          <div className="text-sm">
            <div>{format(lastGenerated, 'MMM d, HH:mm')}</div>
            <div className="text-xs text-muted-foreground">
              {row.original.generationCount} times
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'averageGenerationTime',
      title: 'Gen. Time',
      sortable: true,
      cell: ({ row }) => {
        const avgTime = row.original.averageGenerationTime;
        return (
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-muted-foreground" />
            <Badge variant={avgTime < 10 ? 'default' : avgTime < 20 ? 'secondary' : 'destructive'}>
              {avgTime}s
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      title: 'Created',
      sortable: true,
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        if (!createdAt) return null;
        
        const date = new Date(createdAt);
        return (
          <div className="flex items-center gap-1 text-sm">
            <Calendar className="h-3 w-3 text-muted-foreground" />
            {format(date, 'MMM d, yyyy')}
          </div>
        );
      },
    },
  ];

  const rowActions = (row: EnhancedReportConfiguration): AdvancedTableRowAction<EnhancedReportConfiguration>[] => {
    const actions: AdvancedTableRowAction<EnhancedReportConfiguration>[] = [
      {
        type: 'view',
        label: 'View Configuration',
        onClick: () => {
          toast({
            title: "View Configuration",
            description: `Viewing ${row.name} configuration...`,
          });
        },
        icon: <Eye className="h-4 w-4" />,
      },
    ];

    if (onConfigGenerate) {
      actions.push({
        type: 'custom',
        label: 'Generate Now',
        onClick: () => onConfigGenerate(row.id!),
        icon: <Play className="h-4 w-4" />,
        variant: 'default',
      });
    }

    if (onConfigEdit) {
      actions.push({
        type: 'edit',
        label: 'Edit Configuration',
        onClick: () => onConfigEdit(row),
        icon: <Edit2 className="h-4 w-4" />,
      });
    }

    if (onConfigToggle) {
      actions.push({
        type: 'custom',
        label: row.enabled ? 'Disable' : 'Enable',
        onClick: () => onConfigToggle(row.id!, !row.enabled),
        icon: row.enabled ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />,
        variant: row.enabled ? 'secondary' : 'default',
      });
    }

    actions.push({
      type: 'share',
      label: 'Share Configuration',
      onClick: () => {
        toast({
          title: "Share Configuration",
          description: `Sharing ${row.name} configuration...`,
        });
      },
      icon: <Share2 className="h-4 w-4" />,
    });

    if (onConfigDelete) {
      actions.push({
        type: 'delete',
        label: 'Delete Configuration',
        onClick: () => onConfigDelete(row.id!),
        icon: <Trash2 className="h-4 w-4" />,
        variant: 'destructive',
      });
    }

    return actions;
  };

  const filters = [
    {
      id: 'type',
      label: 'Report Type',
      type: 'select' as const,
      options: [
        { label: 'Performance', value: 'performance' },
        { label: 'Analytics', value: 'analytics' },
        { label: 'Executive', value: 'executive' },
        { label: 'Custom', value: 'custom' },
      ],
    },
    {
      id: 'format',
      label: 'Format',
      type: 'select' as const,
      options: [
        { label: 'PDF', value: 'pdf' },
        { label: 'Excel', value: 'excel' },
        { label: 'CSV', value: 'csv' },
        { label: 'JSON', value: 'json' },
      ],
    },
    {
      id: 'enabled',
      label: 'Status',
      type: 'select' as const,
      options: [
        { label: 'Active', value: 'true' },
        { label: 'Inactive', value: 'false' },
      ],
    },
    {
      id: 'schedule',
      label: 'Schedule',
      type: 'select' as const,
      options: [
        { label: 'Daily', value: 'daily' },
        { label: 'Weekly', value: 'weekly' },
        { label: 'Monthly', value: 'monthly' },
        { label: 'Manual', value: 'manual' },
      ],
    },
  ];

  const bulkActions = [
    {
      id: 'generate',
      label: 'Generate Selected',
      icon: <Play className="h-4 w-4" />,
      onClick: (selectedRows: EnhancedReportConfiguration[]) => {
        toast({
          title: "Generate Reports",
          description: `Generating ${selectedRows.length} report(s)...`,
        });
      },
    },
    {
      id: 'enable',
      label: 'Enable Selected',
      icon: <Play className="h-4 w-4" />,
      onClick: (selectedRows: EnhancedReportConfiguration[]) => {
        toast({
          title: "Enable Configurations",
          description: `Enabling ${selectedRows.length} configuration(s)...`,
        });
      },
    },
    {
      id: 'disable',
      label: 'Disable Selected',
      icon: <Pause className="h-4 w-4" />,
      onClick: (selectedRows: EnhancedReportConfiguration[]) => {
        toast({
          title: "Disable Configurations",
          description: `Disabling ${selectedRows.length} configuration(s)...`,
        });
      },
    },
  ];

  return (
    <AdvancedDataTable
      data={configs}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      bulkActions={bulkActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="report-configurations"
      searchPlaceholder="Search report configurations..."
      emptyMessage="No report configurations found. Create a new configuration to start generating automated reports."
      loading={isLoading}
      className={className}
      variant="default"
    />
  );
}
