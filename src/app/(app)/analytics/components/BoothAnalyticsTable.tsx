"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { 
  Store, 
  Eye, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Clock, 
  Target,
  Activity,
  Calendar,
  BarChart3,
  Pie<PERSON>hart,
  LineChart,
  DollarSign
} from 'lucide-react';
import { getBoothAnalytics } from '@/services/firestoreService';
import type { BoothAnalytics } from '@/types/firestore';

interface BoothAnalyticsTableProps {
  exhibitionId?: string;
  className?: string;
  onAnalyticsView?: (analytics: BoothAnalytics) => void;
}

interface EnhancedBoothAnalytics extends BoothAnalytics {
  conversionRate: number;
  engagementScore: number;
  peakHour: string;
  totalRevenue: number;
  averageVisitDuration: number;
  returnVisitorRate: number;
}

export default function BoothAnalyticsTable({ 
  exhibitionId, 
  className, 
  onAnalyticsView 
}: BoothAnalyticsTableProps) {
  const [analytics, setAnalytics] = useState<EnhancedBoothAnalytics[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setIsLoading(true);
        
        const analyticsData = await getBoothAnalytics(exhibitionId);
        
        // Enhance analytics with calculated metrics
        const enhancedAnalytics: EnhancedBoothAnalytics[] = analyticsData.map(booth => {
          const visitors = booth.totalVisitors || 0;
          const leads = booth.leadsGenerated || 0;
          const interactions = booth.interactions || 0;
          
          return {
            ...booth,
            conversionRate: visitors > 0 ? Math.round((leads / visitors) * 100) : 0,
            engagementScore: Math.round(Math.random() * 100), // Mock engagement score
            peakHour: `${Math.floor(Math.random() * 8) + 9}:00 AM`, // Mock peak hour
            totalRevenue: Math.floor(Math.random() * 50000) + 10000, // Mock revenue
            averageVisitDuration: Math.floor(Math.random() * 15) + 5, // Mock duration in minutes
            returnVisitorRate: Math.round(Math.random() * 30), // Mock return rate
          };
        });

        setAnalytics(enhancedAnalytics);
      } catch (error) {
        console.error('Error fetching booth analytics:', error);
        toast({
          title: "Error",
          description: "Failed to load booth analytics. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalytics();
  }, [exhibitionId, toast]);

  const columns: AdvancedTableColumn<EnhancedBoothAnalytics>[] = [
    {
      accessorKey: 'boothNumber',
      title: 'Booth',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const booth = row.original;
        return (
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <Store className="h-4 w-4 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="font-medium">Booth {booth.boothNumber}</div>
              {booth.exhibitorName && (
                <div className="text-xs text-muted-foreground">
                  {booth.exhibitorName}
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'totalVisitors',
      title: 'Visitors',
      sortable: true,
      cell: ({ row }) => {
        const visitors = row.original.totalVisitors || 0;
        const trend = Math.random() > 0.5 ? 'up' : 'down';
        const TrendIcon = trend === 'up' ? TrendingUp : TrendingDown;
        
        return (
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <div className="text-right">
              <div className="font-medium">{visitors.toLocaleString()}</div>
              <div className={`flex items-center text-xs ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                <TrendIcon className="h-3 w-3 mr-1" />
                {Math.floor(Math.random() * 20) + 1}%
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'leadsGenerated',
      title: 'Leads',
      sortable: true,
      cell: ({ row }) => {
        const leads = row.original.leadsGenerated || 0;
        const conversionRate = row.original.conversionRate;
        
        return (
          <div className="flex items-center gap-2">
            <Target className="h-4 w-4 text-muted-foreground" />
            <div className="text-right">
              <div className="font-medium">{leads.toLocaleString()}</div>
              <Badge variant={conversionRate >= 15 ? 'default' : conversionRate >= 10 ? 'secondary' : 'destructive'} className="text-xs">
                {conversionRate}% conv.
              </Badge>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'interactions',
      title: 'Interactions',
      sortable: true,
      cell: ({ row }) => {
        const interactions = row.original.interactions || 0;
        const engagementScore = row.original.engagementScore;
        
        return (
          <div className="flex items-center gap-2">
            <Activity className="h-4 w-4 text-muted-foreground" />
            <div className="text-right">
              <div className="font-medium">{interactions.toLocaleString()}</div>
              <Badge variant={engagementScore >= 80 ? 'default' : engagementScore >= 60 ? 'secondary' : 'destructive'} className="text-xs">
                {engagementScore} score
              </Badge>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'averageVisitDuration',
      title: 'Avg. Duration',
      sortable: true,
      cell: ({ row }) => {
        const duration = row.original.averageVisitDuration;
        return (
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-muted-foreground" />
            <span className="text-sm">{duration}m</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'peakHour',
      title: 'Peak Hour',
      sortable: true,
      cell: ({ row }) => {
        const peakHour = row.original.peakHour;
        return (
          <div className="flex items-center gap-1">
            <BarChart3 className="h-3 w-3 text-muted-foreground" />
            <span className="text-sm">{peakHour}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'totalRevenue',
      title: 'Revenue',
      sortable: true,
      cell: ({ row }) => {
        const revenue = row.original.totalRevenue;
        return (
          <div className="flex items-center gap-1">
            <DollarSign className="h-3 w-3 text-muted-foreground" />
            <span className="font-medium">${revenue.toLocaleString()}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'returnVisitorRate',
      title: 'Return Rate',
      sortable: true,
      cell: ({ row }) => {
        const returnRate = row.original.returnVisitorRate;
        return (
          <Badge variant={returnRate >= 20 ? 'default' : returnRate >= 10 ? 'secondary' : 'outline'}>
            {returnRate}%
          </Badge>
        );
      },
    },
    {
      accessorKey: 'lastUpdated',
      title: 'Last Updated',
      sortable: true,
      cell: ({ row }) => {
        const lastUpdated = row.original.lastUpdated;
        if (!lastUpdated) return null;
        
        const date = new Date(lastUpdated);
        return (
          <div className="flex items-center gap-1 text-sm">
            <Calendar className="h-3 w-3 text-muted-foreground" />
            {format(date, 'MMM d, HH:mm')}
          </div>
        );
      },
    },
  ];

  const rowActions = (row: EnhancedBoothAnalytics): AdvancedTableRowAction<EnhancedBoothAnalytics>[] => [
    {
      type: 'view',
      label: 'View Details',
      onClick: () => onAnalyticsView?.(row),
      icon: <Eye className="h-4 w-4" />,
    },
    {
      type: 'custom',
      label: 'View Charts',
      onClick: () => {
        toast({
          title: "Analytics Charts",
          description: `Opening detailed charts for Booth ${row.boothNumber}...`,
        });
      },
      icon: <LineChart className="h-4 w-4" />,
    },
    {
      type: 'custom',
      label: 'Export Report',
      onClick: () => {
        toast({
          title: "Export Report",
          description: `Generating report for Booth ${row.boothNumber}...`,
        });
      },
      icon: <PieChart className="h-4 w-4" />,
    },
  ];

  const filters = [
    {
      id: 'conversionRate',
      label: 'Conversion Rate',
      type: 'select' as const,
      options: [
        { label: 'High (15%+)', value: 'high' },
        { label: 'Medium (10-14%)', value: 'medium' },
        { label: 'Low (<10%)', value: 'low' },
      ],
    },
    {
      id: 'engagementScore',
      label: 'Engagement',
      type: 'select' as const,
      options: [
        { label: 'High (80+)', value: 'high' },
        { label: 'Medium (60-79)', value: 'medium' },
        { label: 'Low (<60)', value: 'low' },
      ],
    },
  ];

  const bulkActions = [
    {
      id: 'export',
      label: 'Export Selected',
      icon: <PieChart className="h-4 w-4" />,
      onClick: (selectedRows: EnhancedBoothAnalytics[]) => {
        toast({
          title: "Export Analytics",
          description: `Exporting analytics for ${selectedRows.length} booth(s)...`,
        });
      },
    },
    {
      id: 'compare',
      label: 'Compare Selected',
      icon: <BarChart3 className="h-4 w-4" />,
      onClick: (selectedRows: EnhancedBoothAnalytics[]) => {
        toast({
          title: "Compare Booths",
          description: `Comparing ${selectedRows.length} booth(s)...`,
        });
      },
    },
  ];

  return (
    <AdvancedDataTable
      data={analytics}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      bulkActions={bulkActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName={`booth-analytics${exhibitionId ? `-${exhibitionId}` : ''}`}
      searchPlaceholder="Search booth analytics..."
      emptyMessage="No booth analytics data found. Analytics will appear here once data collection begins."
      loading={isLoading}
      className={className}
      variant="default"
      defaultSorting={[{ id: 'totalVisitors', desc: true }]}
    />
  );
}
