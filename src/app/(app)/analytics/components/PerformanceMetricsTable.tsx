"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { 
  Target, 
  Eye, 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Clock, 
  Award,
  AlertTriangle,
  CheckCircle,
  Calendar,
  BarChart3,
  Zap,
  Users
} from 'lucide-react';
// import { getPerformanceMetrics } from '@/services/firestoreService';
import type { PerformanceMetric } from '@/types/firestore';

interface PerformanceMetricsTableProps {
  exhibitionId?: string;
  className?: string;
  onMetricView?: (metric: PerformanceMetric) => void;
}

interface EnhancedPerformanceMetric extends PerformanceMetric {
  performanceScore: number;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
  status: 'excellent' | 'good' | 'warning' | 'critical';
  lastCalculated: Date;
}

export default function PerformanceMetricsTable({ 
  exhibitionId, 
  className, 
  onMetricView 
}: PerformanceMetricsTableProps) {
  const [metrics, setMetrics] = useState<EnhancedPerformanceMetric[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        setIsLoading(true);
        
        // Mock performance metrics data
        const metricsData: PerformanceMetric[] = [
          {
            id: '1',
            name: 'Visitor Engagement Rate',
            description: 'Percentage of visitors who interact with booth content',
            category: 'engagement',
            currentValue: 78,
            targetValue: 85,
            unit: 'percentage',
            frequency: 'hourly',
            createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: '2',
            name: 'Lead Conversion Rate',
            description: 'Percentage of visitors converted to leads',
            category: 'conversion',
            currentValue: 12,
            targetValue: 15,
            unit: 'percentage',
            frequency: 'daily',
            createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: '3',
            name: 'Total Revenue',
            description: 'Total revenue generated from exhibition',
            category: 'conversion',
            currentValue: 125000,
            targetValue: 150000,
            unit: 'currency',
            frequency: 'daily',
            createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          },
        ];
        
        // Enhance metrics with calculated data
        const enhancedMetrics: EnhancedPerformanceMetric[] = metricsData.map(metric => {
          const currentValue = metric.currentValue || 0;
          const targetValue = metric.targetValue || 0;
          const performanceScore = targetValue > 0 ? Math.round((currentValue / targetValue) * 100) : 0;
          
          // Determine trend
          const trendPercentage = Math.floor(Math.random() * 30) + 1;
          const trend = Math.random() > 0.6 ? 'up' : Math.random() > 0.3 ? 'down' : 'stable';
          
          // Determine status based on performance score
          let status: 'excellent' | 'good' | 'warning' | 'critical';
          if (performanceScore >= 90) status = 'excellent';
          else if (performanceScore >= 75) status = 'good';
          else if (performanceScore >= 50) status = 'warning';
          else status = 'critical';
          
          return {
            ...metric,
            performanceScore,
            trend,
            trendPercentage,
            status,
            lastCalculated: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
          };
        });

        setMetrics(enhancedMetrics);
      } catch (error) {
        console.error('Error fetching performance metrics:', error);
        toast({
          title: "Error",
          description: "Failed to load performance metrics. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchMetrics();
  }, [exhibitionId, toast]);

  const columns: AdvancedTableColumn<EnhancedPerformanceMetric>[] = [
    {
      accessorKey: 'name',
      title: 'Metric',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const metric = row.original;
        const statusIcons = {
          excellent: CheckCircle,
          good: CheckCircle,
          warning: AlertTriangle,
          critical: AlertTriangle,
        };
        const StatusIcon = statusIcons[metric.status];
        
        return (
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <Target className="h-4 w-4 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2">
                <div className="font-medium">{metric.name}</div>
                <StatusIcon className={`h-4 w-4 ${
                  metric.status === 'excellent' ? 'text-green-600' :
                  metric.status === 'good' ? 'text-blue-600' :
                  metric.status === 'warning' ? 'text-yellow-600' :
                  'text-red-600'
                }`} />
              </div>
              {metric.description && (
                <div className="text-xs text-muted-foreground truncate">
                  {metric.description}
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'category',
      title: 'Category',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const category = row.original.category;
        const categoryConfig = {
          attendance: { variant: 'default' as const, label: 'Attendance', icon: Users },
          engagement: { variant: 'secondary' as const, label: 'Engagement', icon: Activity },
          conversion: { variant: 'outline' as const, label: 'Conversion', icon: Target },
          satisfaction: { variant: 'destructive' as const, label: 'Satisfaction', icon: Award },
        };
        const config = categoryConfig[category] || { variant: 'outline' as const, label: category, icon: BarChart3 };
        const IconComponent = config.icon;
        
        return (
          <Badge variant={config.variant} className="flex items-center w-fit">
            <IconComponent className="h-3 w-3 mr-1" />
            {config.label}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'currentValue',
      title: 'Current',
      sortable: true,
      cell: ({ row }) => {
        const metric = row.original;
        const trend = metric.trend;
        const TrendIcon = trend === 'up' ? TrendingUp : trend === 'down' ? TrendingDown : Activity;
        
        return (
          <div className="text-right">
            <div className="font-medium">
              {metric.unit === 'percentage' ? `${metric.currentValue}%` : 
               metric.unit === 'currency' ? `$${metric.currentValue?.toLocaleString()}` :
               metric.currentValue?.toLocaleString()}
            </div>
            <div className={`flex items-center justify-end text-xs ${
              trend === 'up' ? 'text-green-600' : 
              trend === 'down' ? 'text-red-600' : 
              'text-gray-600'
            }`}>
              <TrendIcon className="h-3 w-3 mr-1" />
              {trend === 'stable' ? 'Stable' : `${metric.trendPercentage}%`}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'targetValue',
      title: 'Target',
      sortable: true,
      cell: ({ row }) => {
        const metric = row.original;
        return (
          <div className="text-right font-medium">
            {metric.unit === 'percentage' ? `${metric.targetValue}%` : 
             metric.unit === 'currency' ? `$${metric.targetValue?.toLocaleString()}` :
             metric.targetValue?.toLocaleString()}
          </div>
        );
      },
    },
    {
      accessorKey: 'performanceScore',
      title: 'Performance',
      sortable: true,
      cell: ({ row }) => {
        const score = row.original.performanceScore;
        const status = row.original.status;
        
        return (
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4 text-muted-foreground" />
            <Badge variant={
              status === 'excellent' ? 'default' :
              status === 'good' ? 'secondary' :
              status === 'warning' ? 'outline' :
              'destructive'
            } className={
              status === 'excellent' ? 'bg-green-100 text-green-700' :
              status === 'good' ? 'bg-blue-100 text-blue-700' :
              status === 'warning' ? 'bg-yellow-100 text-yellow-700' :
              'bg-red-100 text-red-700'
            }>
              {score}%
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'frequency',
      title: 'Frequency',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const frequency = row.original.frequency;
        return (
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-muted-foreground" />
            <Badge variant="outline" className="text-xs capitalize">
              {frequency}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'lastCalculated',
      title: 'Last Updated',
      sortable: true,
      cell: ({ row }) => {
        const lastCalculated = row.original.lastCalculated;
        return (
          <div className="flex items-center gap-1 text-sm">
            <Calendar className="h-3 w-3 text-muted-foreground" />
            {format(lastCalculated, 'MMM d, HH:mm')}
          </div>
        );
      },
    },
  ];

  const rowActions = (row: EnhancedPerformanceMetric): AdvancedTableRowAction<EnhancedPerformanceMetric>[] => [
    {
      type: 'view',
      label: 'View Details',
      onClick: () => onMetricView?.(row),
      icon: <Eye className="h-4 w-4" />,
    },
    {
      type: 'custom',
      label: 'View Trend',
      onClick: () => {
        toast({
          title: "Metric Trend",
          description: `Opening trend analysis for ${row.name}...`,
        });
      },
      icon: <TrendingUp className="h-4 w-4" />,
    },
    {
      type: 'custom',
      label: 'Recalculate',
      onClick: () => {
        toast({
          title: "Recalculate Metric",
          description: `Recalculating ${row.name}...`,
        });
      },
      icon: <Zap className="h-4 w-4" />,
    },
  ];

  const filters = [
    {
      id: 'category',
      label: 'Category',
      type: 'select' as const,
      options: [
        { label: 'Attendance', value: 'attendance' },
        { label: 'Engagement', value: 'engagement' },
        { label: 'Conversion', value: 'conversion' },
        { label: 'Satisfaction', value: 'satisfaction' },
      ],
    },
    {
      id: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { label: 'Excellent', value: 'excellent' },
        { label: 'Good', value: 'good' },
        { label: 'Warning', value: 'warning' },
        { label: 'Critical', value: 'critical' },
      ],
    },
    {
      id: 'frequency',
      label: 'Frequency',
      type: 'select' as const,
      options: [
        { label: 'Real-time', value: 'realtime' },
        { label: 'Hourly', value: 'hourly' },
        { label: 'Daily', value: 'daily' },
        { label: 'Weekly', value: 'weekly' },
      ],
    },
  ];

  const bulkActions = [
    {
      id: 'recalculate',
      label: 'Recalculate Selected',
      icon: <Zap className="h-4 w-4" />,
      onClick: (selectedRows: EnhancedPerformanceMetric[]) => {
        toast({
          title: "Recalculate Metrics",
          description: `Recalculating ${selectedRows.length} metric(s)...`,
        });
      },
    },
    {
      id: 'export',
      label: 'Export Selected',
      icon: <BarChart3 className="h-4 w-4" />,
      onClick: (selectedRows: EnhancedPerformanceMetric[]) => {
        toast({
          title: "Export Metrics",
          description: `Exporting ${selectedRows.length} metric(s)...`,
        });
      },
    },
  ];

  return (
    <AdvancedDataTable
      data={metrics}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      bulkActions={bulkActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName={`performance-metrics${exhibitionId ? `-${exhibitionId}` : ''}`}
      searchPlaceholder="Search performance metrics..."
      emptyMessage="No performance metrics found. Configure metrics to start tracking performance."
      loading={isLoading}
      className={className}
      variant="default"
      defaultSorting={[{ id: 'performanceScore', desc: true }]}
    />
  );
}
