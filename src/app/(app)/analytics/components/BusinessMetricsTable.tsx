"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { 
  DollarSign, 
  Eye, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Target, 
  Award,
  Building,
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  Zap
} from 'lucide-react';
// import { getBusinessMetrics } from '@/services/firestoreService';
import type { BusinessMetric } from '@/types/firestore';

interface BusinessMetricsTableProps {
  exhibitionId?: string;
  className?: string;
  onMetricView?: (metric: BusinessMetric) => void;
}

interface EnhancedBusinessMetric extends BusinessMetric {
  performanceScore: number;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
  status: 'excellent' | 'good' | 'warning' | 'critical';
  lastCalculated: Date;
  roi: number;
}

export default function BusinessMetricsTable({ 
  exhibitionId, 
  className, 
  onMetricView 
}: BusinessMetricsTableProps) {
  const [metrics, setMetrics] = useState<EnhancedBusinessMetric[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        setIsLoading(true);
        
        // Mock business metrics data
        const metricsData: BusinessMetric[] = [
          {
            id: '1',
            name: 'Total Revenue',
            description: 'Total revenue generated from exhibition',
            category: 'revenue',
            currentValue: 285000,
            targetValue: 300000,
            unit: 'currency',
            period: 'exhibition',
            createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: '2',
            name: 'Cost Per Lead',
            description: 'Average cost to acquire each lead',
            category: 'cost',
            currentValue: 45,
            targetValue: 40,
            unit: 'currency',
            period: 'daily',
            createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: '3',
            name: 'Customer Acquisition Cost',
            description: 'Cost to acquire a new customer',
            category: 'cost',
            currentValue: 180,
            targetValue: 150,
            unit: 'currency',
            period: 'weekly',
            createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          },
          {
            id: '4',
            name: 'Return on Investment',
            description: 'ROI for the entire exhibition',
            category: 'profitability',
            currentValue: 320,
            targetValue: 300,
            unit: 'percentage',
            period: 'exhibition',
            createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          },
        ];
        
        // Enhance metrics with calculated data
        const enhancedMetrics: EnhancedBusinessMetric[] = metricsData.map(metric => {
          const currentValue = metric.currentValue || 0;
          const targetValue = metric.targetValue || 0;
          
          // For cost metrics, lower is better
          const isCostMetric = metric.category === 'cost';
          const performanceScore = targetValue > 0 ? 
            Math.round(isCostMetric ? 
              Math.max(0, (targetValue / currentValue) * 100) : 
              (currentValue / targetValue) * 100
            ) : 0;
          
          // Determine trend
          const trendPercentage = Math.floor(Math.random() * 25) + 1;
          const trend = Math.random() > 0.6 ? 'up' : Math.random() > 0.3 ? 'down' : 'stable';
          
          // Determine status based on performance score
          let status: 'excellent' | 'good' | 'warning' | 'critical';
          if (performanceScore >= 95) status = 'excellent';
          else if (performanceScore >= 80) status = 'good';
          else if (performanceScore >= 60) status = 'warning';
          else status = 'critical';
          
          // Calculate ROI
          const roi = Math.floor(Math.random() * 400) + 100;
          
          return {
            ...metric,
            performanceScore,
            trend,
            trendPercentage,
            status,
            lastCalculated: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
            roi,
          };
        });

        setMetrics(enhancedMetrics);
      } catch (error) {
        console.error('Error fetching business metrics:', error);
        toast({
          title: "Error",
          description: "Failed to load business metrics. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchMetrics();
  }, [exhibitionId, toast]);

  const columns: AdvancedTableColumn<EnhancedBusinessMetric>[] = [
    {
      accessorKey: 'name',
      title: 'Business Metric',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const metric = row.original;
        const categoryIcons = {
          revenue: DollarSign,
          cost: Target,
          profitability: Award,
          efficiency: Zap,
        };
        const IconComponent = categoryIcons[metric.category] || BarChart3;
        
        return (
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <IconComponent className="h-4 w-4 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="font-medium">{metric.name}</div>
              {metric.description && (
                <div className="text-xs text-muted-foreground truncate">
                  {metric.description}
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'category',
      title: 'Category',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const category = row.original.category;
        const categoryConfig = {
          revenue: { variant: 'default' as const, label: 'Revenue', icon: DollarSign },
          cost: { variant: 'secondary' as const, label: 'Cost', icon: Target },
          profitability: { variant: 'outline' as const, label: 'Profitability', icon: Award },
          efficiency: { variant: 'destructive' as const, label: 'Efficiency', icon: Zap },
        };
        const config = categoryConfig[category] || { variant: 'outline' as const, label: category, icon: BarChart3 };
        const IconComponent = config.icon;
        
        return (
          <Badge variant={config.variant} className="flex items-center w-fit">
            <IconComponent className="h-3 w-3 mr-1" />
            {config.label}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'currentValue',
      title: 'Current',
      sortable: true,
      cell: ({ row }) => {
        const metric = row.original;
        const trend = metric.trend;
        const TrendIcon = trend === 'up' ? TrendingUp : trend === 'down' ? TrendingDown : Activity;
        
        return (
          <div className="text-right">
            <div className="font-medium">
              {metric.unit === 'percentage' ? `${metric.currentValue}%` : 
               metric.unit === 'currency' ? `$${metric.currentValue?.toLocaleString()}` :
               metric.currentValue?.toLocaleString()}
            </div>
            <div className={`flex items-center justify-end text-xs ${
              trend === 'up' ? 'text-green-600' : 
              trend === 'down' ? 'text-red-600' : 
              'text-gray-600'
            }`}>
              <TrendIcon className="h-3 w-3 mr-1" />
              {trend === 'stable' ? 'Stable' : `${metric.trendPercentage}%`}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'targetValue',
      title: 'Target',
      sortable: true,
      cell: ({ row }) => {
        const metric = row.original;
        return (
          <div className="text-right font-medium">
            {metric.unit === 'percentage' ? `${metric.targetValue}%` : 
             metric.unit === 'currency' ? `$${metric.targetValue?.toLocaleString()}` :
             metric.targetValue?.toLocaleString()}
          </div>
        );
      },
    },
    {
      accessorKey: 'performanceScore',
      title: 'Performance',
      sortable: true,
      cell: ({ row }) => {
        const score = row.original.performanceScore;
        const status = row.original.status;
        
        return (
          <div className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
            <Badge variant={
              status === 'excellent' ? 'default' :
              status === 'good' ? 'secondary' :
              status === 'warning' ? 'outline' :
              'destructive'
            } className={
              status === 'excellent' ? 'bg-green-100 text-green-700' :
              status === 'good' ? 'bg-blue-100 text-blue-700' :
              status === 'warning' ? 'bg-yellow-100 text-yellow-700' :
              'bg-red-100 text-red-700'
            }>
              {score}%
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'roi',
      title: 'ROI',
      sortable: true,
      cell: ({ row }) => {
        const roi = row.original.roi;
        return (
          <div className="flex items-center gap-1">
            <Award className="h-3 w-3 text-muted-foreground" />
            <Badge variant={roi >= 300 ? 'default' : roi >= 200 ? 'secondary' : 'outline'}>
              {roi}%
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'period',
      title: 'Period',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const period = row.original.period;
        return (
          <Badge variant="outline" className="text-xs capitalize">
            {period}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'lastCalculated',
      title: 'Last Updated',
      sortable: true,
      cell: ({ row }) => {
        const lastCalculated = row.original.lastCalculated;
        return (
          <div className="flex items-center gap-1 text-sm">
            <Calendar className="h-3 w-3 text-muted-foreground" />
            {format(lastCalculated, 'MMM d, HH:mm')}
          </div>
        );
      },
    },
  ];

  const rowActions = (row: EnhancedBusinessMetric): AdvancedTableRowAction<EnhancedBusinessMetric>[] => [
    {
      type: 'view',
      label: 'View Details',
      onClick: () => onMetricView?.(row),
      icon: <Eye className="h-4 w-4" />,
    },
    {
      type: 'custom',
      label: 'View Analysis',
      onClick: () => {
        toast({
          title: "Business Analysis",
          description: `Opening detailed analysis for ${row.name}...`,
        });
      },
      icon: <PieChart className="h-4 w-4" />,
    },
    {
      type: 'custom',
      label: 'Forecast',
      onClick: () => {
        toast({
          title: "Generate Forecast",
          description: `Generating forecast for ${row.name}...`,
        });
      },
      icon: <TrendingUp className="h-4 w-4" />,
    },
  ];

  const filters = [
    {
      id: 'category',
      label: 'Category',
      type: 'select' as const,
      options: [
        { label: 'Revenue', value: 'revenue' },
        { label: 'Cost', value: 'cost' },
        { label: 'Profitability', value: 'profitability' },
        { label: 'Efficiency', value: 'efficiency' },
      ],
    },
    {
      id: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { label: 'Excellent', value: 'excellent' },
        { label: 'Good', value: 'good' },
        { label: 'Warning', value: 'warning' },
        { label: 'Critical', value: 'critical' },
      ],
    },
    {
      id: 'period',
      label: 'Period',
      type: 'select' as const,
      options: [
        { label: 'Daily', value: 'daily' },
        { label: 'Weekly', value: 'weekly' },
        { label: 'Monthly', value: 'monthly' },
        { label: 'Exhibition', value: 'exhibition' },
      ],
    },
  ];

  return (
    <AdvancedDataTable
      data={metrics}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName={`business-metrics${exhibitionId ? `-${exhibitionId}` : ''}`}
      searchPlaceholder="Search business metrics..."
      emptyMessage="No business metrics found. Configure business metrics to start tracking financial performance."
      loading={isLoading}
      className={className}
      variant="default"
      defaultSorting={[{ id: 'performanceScore', desc: true }]}
    />
  );
}
