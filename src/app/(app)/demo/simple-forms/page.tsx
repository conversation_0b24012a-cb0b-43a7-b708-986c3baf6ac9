"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ModernForm, ModernFormField, ModernFormSection } from '@/components/ui/modern-form';
import { useToast } from '@/hooks/use-toast';

export default function SimpleFormsDemo() {
  const { toast } = useToast();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    industry: '',
    description: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setIsSubmitting(false);
    
    toast({
      title: "Form Submitted!",
      description: "Your information has been saved successfully.",
    });
  };

  const handleFieldChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Simple Forms Demo</h1>
        <p className="text-muted-foreground">
          Basic form components to test functionality
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Basic Form */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Form</CardTitle>
            <CardDescription>Simple form with standard components</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleFieldChange('name', e.target.value)}
                placeholder="Enter your name..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleFieldChange('email', e.target.value)}
                placeholder="Enter your email..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="company">Company</Label>
              <Input
                id="company"
                value={formData.company}
                onChange={(e) => handleFieldChange('company', e.target.value)}
                placeholder="Enter your company..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="industry">Industry</Label>
              <Select
                value={formData.industry}
                onValueChange={(value) => handleFieldChange('industry', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select industry..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="technology">Technology</SelectItem>
                  <SelectItem value="healthcare">Healthcare</SelectItem>
                  <SelectItem value="finance">Finance</SelectItem>
                  <SelectItem value="manufacturing">Manufacturing</SelectItem>
                  <SelectItem value="retail">Retail</SelectItem>
                  <SelectItem value="education">Education</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleFieldChange('description', e.target.value)}
                placeholder="Tell us about your business..."
                rows={3}
              />
            </div>

            <Button 
              onClick={handleSubmit} 
              disabled={isSubmitting}
              className="w-full"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Form'}
            </Button>
          </CardContent>
        </Card>

        {/* Modern Form */}
        <Card>
          <CardHeader>
            <CardTitle>Modern Form</CardTitle>
            <CardDescription>Using ModernForm components</CardDescription>
          </CardHeader>
          <CardContent>
            <ModernForm
              title="Contact Information"
              description="Please fill out your details"
              variant="inline"
              onSubmit={handleSubmit}
              isSubmitting={isSubmitting}
              submitLabel="Save Information"
            >
              <ModernFormSection
                title="Personal Details"
                description="Your basic information"
              >
                <ModernFormField
                  label="Full Name"
                  description="Enter your complete name"
                  required={true}
                >
                  <Input
                    value={formData.name}
                    onChange={(e) => handleFieldChange('name', e.target.value)}
                    placeholder="John Doe"
                  />
                </ModernFormField>

                <ModernFormField
                  label="Email Address"
                  description="We'll use this to contact you"
                  required={true}
                >
                  <Input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleFieldChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </ModernFormField>
              </ModernFormSection>

              <ModernFormSection
                title="Business Information"
                description="Tell us about your business"
              >
                <ModernFormField
                  label="Company Name"
                  description="Your organization's name"
                >
                  <Input
                    value={formData.company}
                    onChange={(e) => handleFieldChange('company', e.target.value)}
                    placeholder="Acme Corporation"
                  />
                </ModernFormField>

                <ModernFormField
                  label="Industry"
                  description="Select your primary industry"
                  required={true}
                >
                  <Select
                    value={formData.industry}
                    onValueChange={(value) => handleFieldChange('industry', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Choose industry..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="technology">Technology</SelectItem>
                      <SelectItem value="healthcare">Healthcare</SelectItem>
                      <SelectItem value="finance">Finance</SelectItem>
                      <SelectItem value="manufacturing">Manufacturing</SelectItem>
                      <SelectItem value="retail">Retail</SelectItem>
                      <SelectItem value="education">Education</SelectItem>
                    </SelectContent>
                  </Select>
                </ModernFormField>

                <ModernFormField
                  label="Business Description"
                  description="Brief description of your business"
                >
                  <Textarea
                    value={formData.description}
                    onChange={(e) => handleFieldChange('description', e.target.value)}
                    placeholder="We provide innovative solutions..."
                    rows={3}
                  />
                </ModernFormField>
              </ModernFormSection>
            </ModernForm>
          </CardContent>
        </Card>
      </div>

      {/* Form Data Display */}
      <Card>
        <CardHeader>
          <CardTitle>Form Data</CardTitle>
          <CardDescription>Current form values</CardDescription>
        </CardHeader>
        <CardContent>
          <pre className="bg-muted p-4 rounded-lg text-sm overflow-auto">
            {JSON.stringify(formData, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
}
