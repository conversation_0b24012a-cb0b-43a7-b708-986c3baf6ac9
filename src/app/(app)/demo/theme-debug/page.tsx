"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ThemeProvider, ThemeToggle, ThemeSelector } from '@/components/providers/theme-provider';
import { useTheme } from '@/lib/theme';

function ThemeDebugContent() {
  const { variant, mode, effectiveMode } = useTheme();
  
  React.useEffect(() => {
    console.log('Current theme:', { variant, mode, effectiveMode });
    console.log('HTML classes:', document.documentElement.className);
    console.log('Body classes:', document.body.className);
    console.log('CSS Variables:', {
      background: getComputedStyle(document.documentElement).getPropertyValue('--background'),
      primary: getComputedStyle(document.documentElement).getPropertyValue('--primary'),
      foreground: getComputedStyle(document.documentElement).getPropertyValue('--foreground'),
    });
  }, [variant, mode, effectiveMode]);

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Theme Debug Page</h1>
        <p className="text-muted-foreground">
          Debug professional theme application
        </p>
        <div className="flex justify-center gap-2 flex-wrap">
          <Badge variant="secondary">Current: {variant}</Badge>
          <Badge variant="secondary">Mode: {effectiveMode}</Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Theme Information</CardTitle>
            <CardDescription>Current theme settings</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <strong>Variant:</strong> {variant}
            </div>
            <div>
              <strong>Mode:</strong> {mode}
            </div>
            <div>
              <strong>Effective Mode:</strong> {effectiveMode}
            </div>
            <div>
              <strong>HTML Classes:</strong> 
              <code className="block mt-1 p-2 bg-muted rounded text-xs">
                {document.documentElement.className}
              </code>
            </div>
            <div>
              <strong>Body Classes:</strong> 
              <code className="block mt-1 p-2 bg-muted rounded text-xs">
                {document.body.className}
              </code>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Theme Controls</CardTitle>
            <CardDescription>Switch themes to test</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium">Toggle Mode:</span>
              <ThemeToggle />
            </div>
            
            <ThemeSelector />
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Primary Colors</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button className="w-full">Primary Button</Button>
            <Button variant="secondary" className="w-full">Secondary Button</Button>
            <Button variant="outline" className="w-full">Outline Button</Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Background Test</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="p-3 bg-background border rounded">Background</div>
              <div className="p-3 bg-card border rounded">Card</div>
              <div className="p-3 bg-muted border rounded">Muted</div>
              <div className="p-3 bg-primary text-primary-foreground rounded">Primary</div>
              <div className="p-3 bg-secondary text-secondary-foreground rounded">Secondary</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>CSS Variables</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1 text-xs">
              <div>--background: <span className="font-mono">{getComputedStyle(document.documentElement).getPropertyValue('--background')}</span></div>
              <div>--primary: <span className="font-mono">{getComputedStyle(document.documentElement).getPropertyValue('--primary')}</span></div>
              <div>--foreground: <span className="font-mono">{getComputedStyle(document.documentElement).getPropertyValue('--foreground')}</span></div>
              <div>--card: <span className="font-mono">{getComputedStyle(document.documentElement).getPropertyValue('--card')}</span></div>
              <div>--muted: <span className="font-mono">{getComputedStyle(document.documentElement).getPropertyValue('--muted')}</span></div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Professional Theme Test</CardTitle>
          <CardDescription>These should be yellow and black when professional theme is active</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h3 className="font-semibold">Expected Professional Colors:</h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded" style={{ backgroundColor: '#FFFF00' }}></div>
                  <span>Primary: #FFFF00 (Yellow)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded" style={{ backgroundColor: '#141414' }}></div>
                  <span>Background: #141414 (Dark)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded" style={{ backgroundColor: '#FFD700' }}></div>
                  <span>Accent: #FFD700 (Gold)</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <h3 className="font-semibold">Current Applied Colors:</h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded bg-primary"></div>
                  <span>Primary (Current)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded bg-background border"></div>
                  <span>Background (Current)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded bg-accent"></div>
                  <span>Accent (Current)</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function ThemeDebugPage() {
  return (
    <ThemeProvider defaultMode="dark" defaultVariant="professional">
      <ThemeDebugContent />
    </ThemeProvider>
  );
}
