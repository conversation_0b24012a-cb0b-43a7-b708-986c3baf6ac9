"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { SmartForm, SmartFormStep, SmartFormField } from '@/components/ui/smart-form';
import { AdvancedSearch, SearchFilter, SavedSearch } from '@/components/ui/advanced-search';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';

export default function SmartFormsDemo() {
  const { toast } = useToast();
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([
    {
      id: '1',
      name: 'Active Exhibitions',
      query: 'active',
      filters: { status: 'active', type: 'exhibition' },
      createdAt: new Date(),
      favorite: true
    }
  ]);

  // Smart Form Configuration
  const formSteps: SmartFormStep[] = [
    {
      id: 'basic-info',
      title: 'Basic Information',
      description: 'Enter the basic details for your exhibition',
      fields: [
        {
          id: 'exhibition-name',
          type: 'text',
          label: 'Exhibition Name',
          placeholder: 'Enter exhibition name...',
          required: true,
          aiSuggestion: { enabled: true, context: 'exhibition naming' },
          debounceMs: 500
        },
        {
          id: 'description',
          type: 'richtext',
          label: 'Description',
          placeholder: 'Describe your exhibition...',
          richTextOptions: {
            height: 150,
            toolbar: ['bold', 'italic', 'bulletList', '|', 'link', 'quote']
          }
        },
        {
          id: 'industry',
          type: 'select',
          label: 'Industry',
          placeholder: 'Select industry...',
          required: true,
          options: [
            { value: 'technology', label: 'Technology' },
            { value: 'healthcare', label: 'Healthcare' },
            { value: 'finance', label: 'Finance' },
            { value: 'manufacturing', label: 'Manufacturing' }
          ]
        }
      ]
    },
    {
      id: 'schedule',
      title: 'Schedule & Location',
      description: 'Set up the timing and location details',
      fields: [
        {
          id: 'start-date',
          type: 'datetime',
          label: 'Start Date & Time',
          placeholder: 'Select start date and time...',
          required: true,
          timezone: 'America/New_York'
        },
        {
          id: 'end-date',
          type: 'datetime',
          label: 'End Date & Time',
          placeholder: 'Select end date and time...',
          required: true,
          timezone: 'America/New_York'
        },
        {
          id: 'timezone',
          type: 'timezone',
          label: 'Event Timezone',
          placeholder: 'Select timezone...',
          required: true
        },
        {
          id: 'location',
          type: 'text',
          label: 'Venue Location',
          placeholder: 'Enter venue address...',
          required: true
        }
      ]
    },
    {
      id: 'media',
      title: 'Media & Documents',
      description: 'Upload relevant files and media',
      optional: true,
      fields: [
        {
          id: 'banner-image',
          type: 'file',
          label: 'Banner Image',
          placeholder: 'Upload banner image...',
          fileAccept: 'image/svg+xml,image/png,image/jpeg,image/jpg,image/gif',
          maxFileSize: 5
        },
        {
          id: 'brochure',
          type: 'file',
          label: 'Exhibition Brochure',
          placeholder: 'Upload brochure PDF...',
          fileAccept: '.pdf,.doc,.docx',
          maxFileSize: 10
        },
        {
          id: 'additional-notes',
          type: 'textarea',
          label: 'Additional Notes',
          placeholder: 'Any additional information...',
          debounceMs: 1000
        }
      ]
    }
  ];

  // Search Filters Configuration
  const searchFilters: SearchFilter[] = [
    {
      id: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { value: 'active', label: 'Active' },
        { value: 'upcoming', label: 'Upcoming' },
        { value: 'completed', label: 'Completed' },
        { value: 'cancelled', label: 'Cancelled' }
      ],
      placeholder: 'Select status...'
    },
    {
      id: 'type',
      label: 'Type',
      type: 'select',
      options: [
        { value: 'exhibition', label: 'Exhibition' },
        { value: 'conference', label: 'Conference' },
        { value: 'trade-show', label: 'Trade Show' },
        { value: 'seminar', label: 'Seminar' }
      ],
      placeholder: 'Select type...'
    },
    {
      id: 'date-from',
      label: 'Date From',
      type: 'date',
      placeholder: 'Start date...'
    },
    {
      id: 'date-to',
      label: 'Date To',
      type: 'date',
      placeholder: 'End date...'
    },
    {
      id: 'budget-min',
      label: 'Min Budget',
      type: 'number',
      placeholder: 'Minimum budget...'
    }
  ];

  const handleFormSubmit = (data: Record<string, any>) => {
    console.log('Form submitted:', data);
    toast({
      title: "Form Submitted Successfully!",
      description: "Your exhibition has been created with all the provided details.",
    });
  };

  const handleFormAutoSave = async (data: Record<string, any>) => {
    console.log('Auto-saving form data:', data);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
  };

  const handleSearch = async (query: string, filters: Record<string, any>) => {
    console.log('Searching:', { query, filters });
    setSearchLoading(true);
    
    // Simulate search API call
    setTimeout(() => {
      const mockResults = [
        { id: 1, name: 'Tech Expo 2024', status: 'active', type: 'exhibition' },
        { id: 2, name: 'Healthcare Summit', status: 'upcoming', type: 'conference' },
        { id: 3, name: 'Finance Forum', status: 'completed', type: 'seminar' }
      ].filter(item => {
        const matchesQuery = !query || item.name.toLowerCase().includes(query.toLowerCase());
        const matchesStatus = !filters.status || item.status === filters.status;
        const matchesType = !filters.type || item.type === filters.type;
        return matchesQuery && matchesStatus && matchesType;
      });
      
      setSearchResults(mockResults);
      setSearchLoading(false);
    }, 800);
  };

  const handleSaveSavedSearch = (search: Omit<SavedSearch, 'id' | 'createdAt'>) => {
    const newSearch: SavedSearch = {
      ...search,
      id: Date.now().toString(),
      createdAt: new Date()
    };
    setSavedSearches(prev => [...prev, newSearch]);
  };

  const handleDeleteSavedSearch = (id: string) => {
    setSavedSearches(prev => prev.filter(s => s.id !== id));
  };

  const handleToggleFavorite = (id: string) => {
    setSavedSearches(prev => prev.map(s => 
      s.id === id ? { ...s, favorite: !s.favorite } : s
    ));
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Smart Forms & Advanced Search Demo</h1>
        <p className="text-muted-foreground">
          Showcasing Phase 4: Forms & Input Modernization features
        </p>
        <div className="flex justify-center gap-2">
          <Badge variant="secondary">Auto-save</Badge>
          <Badge variant="secondary">Smart Validation</Badge>
          <Badge variant="secondary">Multi-step Wizards</Badge>
          <Badge variant="secondary">Rich Text Editor</Badge>
          <Badge variant="secondary">File Upload</Badge>
          <Badge variant="secondary">Timezone-aware DateTime</Badge>
          <Badge variant="secondary">Advanced Search</Badge>
        </div>
      </div>

      <Tabs defaultValue="smart-form" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="smart-form">Smart Form</TabsTrigger>
          <TabsTrigger value="advanced-search">Advanced Search</TabsTrigger>
        </TabsList>

        <TabsContent value="smart-form" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Multi-step Smart Form</CardTitle>
              <CardDescription>
                Features auto-save, smart validation, AI suggestions, and modern input components
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SmartForm
                steps={formSteps}
                onSubmit={handleFormSubmit}
                autoSave={true}
                autoSaveDelay={2000}
                onAutoSave={handleFormAutoSave}
                aiAssistance={true}
                formId="exhibition-demo"
                showProgress={true}
                allowSkipOptional={true}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced-search" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Advanced Search & Filtering</CardTitle>
              <CardDescription>
                Features instant search, advanced filters, saved searches, and search history
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <AdvancedSearch
                placeholder="Search exhibitions, events, tasks..."
                filters={searchFilters}
                onSearch={handleSearch}
                loading={searchLoading}
                showHistory={true}
                showSavedSearches={true}
                showKeyboardShortcuts={true}
                savedSearches={savedSearches}
                onSaveSavedSearch={handleSaveSavedSearch}
                onDeleteSavedSearch={handleDeleteSavedSearch}
                onToggleFavorite={handleToggleFavorite}
              />

              {/* Search Results */}
              {searchResults.length > 0 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Search Results ({searchResults.length})</h3>
                  <div className="grid gap-4">
                    {searchResults.map((result) => (
                      <Card key={result.id}>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium">{result.name}</h4>
                              <div className="flex gap-2 mt-1">
                                <Badge variant="outline">{result.status}</Badge>
                                <Badge variant="secondary">{result.type}</Badge>
                              </div>
                            </div>
                            <Button variant="outline" size="sm">
                              View Details
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
