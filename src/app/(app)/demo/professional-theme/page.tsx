"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ThemeProvider, ThemeToggle } from '@/components/providers/theme-provider';
import { 
  Clock, 
  Infinity, 
  DollarSign, 
  Pause, 
  ArrowRight, 
  Check,
  Star,
  Users,
  Zap,
  Shield,
  Headphones
} from 'lucide-react';

function ProfessionalThemeContent() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-sm">E</span>
              </div>
              <h1 className="text-xl font-bold">EVEXA Professional</h1>
            </div>
            <div className="flex items-center gap-4">
              <Badge variant="secondary">Professional Theme</Badge>
              <ThemeToggle />
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-4 py-2 rounded-full text-4xl font-bold mb-4">
            <Clock className="h-8 w-8" />
            48h
          </div>
          <h2 className="text-2xl font-bold mb-2">Lightning speed delivery</h2>
          <p className="text-muted-foreground max-w-md mx-auto">
            Make your request, and you'll have it done in around two working days.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {/* Feature 1 */}
          <Card className="relative overflow-hidden">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-3 mb-2">
                <div className="h-10 w-10 rounded-lg bg-muted flex items-center justify-center">
                  <Infinity className="h-5 w-5" />
                </div>
                <Badge className="bg-primary text-primary-foreground">You</Badge>
              </div>
              <CardTitle className="text-lg">Unlimited designs and revisions</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Request unlimited designs and we will work on them one at a time until you are 100% satisfied.
              </p>
            </CardContent>
          </Card>

          {/* Feature 2 */}
          <Card className="relative overflow-hidden">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-3 mb-2">
                <div className="h-10 w-10 rounded-lg bg-muted flex items-center justify-center">
                  <DollarSign className="h-5 w-5" />
                </div>
              </div>
              <CardTitle className="text-lg">Top-notch work at a fraction of the cost</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Say goodbye to costly traditional agencies. Get all your design needs covered for just $1,990/month.
              </p>
              <div className="text-2xl font-bold">$32,000</div>
              <div className="text-xs text-muted-foreground">Invoice</div>
            </CardContent>
          </Card>

          {/* Feature 3 */}
          <Card className="relative overflow-hidden">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-3 mb-2">
                <div className="h-10 w-10 rounded-lg bg-muted flex items-center justify-center">
                  <Pause className="h-5 w-5" />
                </div>
                <Badge className="bg-primary text-primary-foreground">You</Badge>
              </div>
              <CardTitle className="text-lg">Design on your terms. Pause at any time</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                No more requests for the time being? Just hit pause and resume your subscription at a future date.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Pricing Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Monthly Plan */}
          <Card className="relative overflow-hidden border-primary">
            <CardHeader>
              <div className="flex items-center justify-between mb-2">
                <Badge variant="secondary" className="bg-primary text-primary-foreground">
                  <Star className="h-3 w-3 mr-1" />
                  2 spots left
                </Badge>
              </div>
              <CardTitle className="text-primary text-2xl">Monthly</CardTitle>
              <div className="flex items-baseline gap-1">
                <span className="text-3xl font-bold">$1,990</span>
                <span className="text-muted-foreground">/mo</span>
              </div>
              <CardDescription>
                Ideal for startups with an ongoing demand for design services.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button className="w-full bg-primary hover:bg-primary/90 text-primary-foreground">
                Subscribe <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
              <Button variant="outline" className="w-full">
                Book a call
              </Button>
              
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-primary" />
                  <span>Unlimited requests</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-primary" />
                  <span>Unlimited revisions</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-primary" />
                  <span>~48 hour delivery</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-primary" />
                  <span>Pause or cancel anytime</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-primary" />
                  <span>Unlimited brands</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-primary" />
                  <span>Invite your team</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-primary" />
                  <span>Versatile services</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Custom Plan */}
          <Card className="relative overflow-hidden">
            <CardHeader>
              <CardTitle className="text-primary text-2xl">Custom</CardTitle>
              <CardDescription>
                Ideal for startups with project-based design needs.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button variant="outline" className="w-full">
                Book a call
              </Button>
              
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-primary" />
                  <span>Custom pricing</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-primary" />
                  <span>3 revisions</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-primary" />
                  <span>Managed via Slack</span>
                </div>
              </div>

              <div className="mt-6 p-4 bg-muted rounded-lg">
                <div className="text-sm font-medium mb-2">Add-on</div>
                <div className="text-primary text-xl font-bold">Framer development</div>
                <div className="text-lg">
                  <span className="text-primary font-bold">+$500</span>
                  <span className="text-muted-foreground">/mo</span>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Framer website development + 3 free months of Pro annual subscription.
                </p>
                <Button className="w-full mt-3 bg-primary hover:bg-primary/90 text-primary-foreground">
                  Subscribe <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Bottom CTA */}
        <Card className="bg-primary text-primary-foreground">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold mb-2">Refer a friend & earn</h3>
            <p className="mb-4 opacity-90">Receive $250 for each referral</p>
            <Button variant="secondary" className="bg-background text-foreground hover:bg-background/90">
              Join now
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function ProfessionalThemeDemo() {
  return (
    <ThemeProvider defaultMode="dark" defaultVariant="professional">
      <ProfessionalThemeContent />
    </ThemeProvider>
  );
}
