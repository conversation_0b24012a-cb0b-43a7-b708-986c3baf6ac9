"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Theme<PERSON>rovider, ThemeToggle, ThemeSelector } from '@/components/providers/theme-provider';
import { I18nProvider, LanguageSelector } from '@/components/providers/i18n-provider';
import { Badge } from '@/components/ui/badge';

function ProvidersTestContent() {
  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Providers Test</h1>
        <p className="text-muted-foreground">
          Testing Theme and i18n providers functionality
        </p>
        <div className="flex justify-center gap-2">
          <Badge variant="secondary">Theme Provider</Badge>
          <Badge variant="secondary">i18n Provider</Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Theme Controls</CardTitle>
            <CardDescription>Test theme switching functionality</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium">Toggle Theme:</span>
              <ThemeToggle />
            </div>
            
            <ThemeSelector />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Language Controls</CardTitle>
            <CardDescription>Test language switching functionality</CardDescription>
          </CardHeader>
          <CardContent>
            <LanguageSelector variant="list" />
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Provider Status</CardTitle>
          <CardDescription>Verify providers are working correctly</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="font-medium">Theme Provider</div>
              <div className="text-green-600">✅ Active</div>
            </div>
            <div>
              <div className="font-medium">i18n Provider</div>
              <div className="text-green-600">✅ Active</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function ProvidersTest() {
  return (
    <ThemeProvider defaultMode="system" defaultVariant="default">
      <I18nProvider defaultLanguage="en">
        <ProvidersTestContent />
      </I18nProvider>
    </ThemeProvider>
  );
}
