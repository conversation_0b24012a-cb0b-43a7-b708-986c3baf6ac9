"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

// Import enhanced components
import { CountrySelector } from '@/components/ui/country-selector';
import { CurrencySelector } from '@/components/ui/currency-selector';
import { ModernCheckbox, createCheckboxItem } from '@/components/ui/modern-checkbox';

// Import icons for checkboxes
import { 
  Brush, 
  Eraser, 
  Scissors, 
  SwatchBook, 
  Palette,
  Paintbrush,
  Layers,
  Move,
  RotateCcw,
  Zap,
  Shield,
  Globe,
  Smartphone,
  Laptop,
  Tablet
} from 'lucide-react';

export default function EnhancedComponentsDemo() {
  const { toast } = useToast();
  
  // Component states
  const [selectedCountry, setSelectedCountry] = useState('');
  const [selectedCurrency, setSelectedCurrency] = useState('');
  const [compactCountry, setCompactCountry] = useState('');
  const [compactCurrency, setCompactCurrency] = useState('');
  
  // Checkbox states
  const [designTools, setDesignTools] = useState<string[]>(['1']);
  const [features, setFeatures] = useState<string[]>([]);
  const [devices, setDevices] = useState<string[]>([]);
  const [preferences, setPreferences] = useState<string[]>([]);

  // Checkbox items
  const designToolItems = [
    createCheckboxItem('1', 'Palette', { icon: SwatchBook, defaultChecked: true }),
    createCheckboxItem('2', 'Brush', { icon: Brush }),
    createCheckboxItem('3', 'Eraser', { icon: Eraser }),
    createCheckboxItem('4', 'Cut', { icon: Scissors }),
  ];

  const featureItems = [
    createCheckboxItem('layers', 'Layer Management', { 
      icon: Layers, 
      description: 'Organize your design with multiple layers' 
    }),
    createCheckboxItem('transform', 'Transform Tools', { 
      icon: Move, 
      description: 'Move, scale, and rotate objects' 
    }),
    createCheckboxItem('history', 'Undo/Redo', { 
      icon: RotateCcw, 
      description: 'Track and revert changes' 
    }),
    createCheckboxItem('effects', 'Visual Effects', { 
      icon: Zap, 
      description: 'Apply filters and effects' 
    }),
  ];

  const deviceItems = [
    createCheckboxItem('mobile', 'Mobile', { icon: Smartphone }),
    createCheckboxItem('tablet', 'Tablet', { icon: Tablet }),
    createCheckboxItem('desktop', 'Desktop', { icon: Laptop }),
    createCheckboxItem('web', 'Web', { icon: Globe }),
  ];

  const preferenceItems = [
    createCheckboxItem('security', 'Enhanced Security', { 
      icon: Shield,
      description: 'Enable additional security features'
    }),
    createCheckboxItem('performance', 'Performance Mode', { 
      icon: Zap,
      description: 'Optimize for better performance'
    }),
    createCheckboxItem('sync', 'Cloud Sync', { 
      icon: Globe,
      description: 'Sync data across devices'
    }),
  ];

  const handleSubmit = () => {
    toast({
      title: "Settings Saved!",
      description: `Country: ${selectedCountry}, Currency: ${selectedCurrency}, Tools: ${designTools.length} selected`,
    });
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Enhanced Components Demo</h1>
        <p className="text-muted-foreground">
          Enhanced search functionality and modern checkboxes
        </p>
        <div className="flex justify-center gap-2 flex-wrap">
          <Badge variant="secondary">Enhanced Search</Badge>
          <Badge variant="secondary">Modern Checkboxes</Badge>
          <Badge variant="secondary">Better UX</Badge>
        </div>
      </div>

      <Tabs defaultValue="search" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="search">Enhanced Search</TabsTrigger>
          <TabsTrigger value="checkboxes">Modern Checkboxes</TabsTrigger>
          <TabsTrigger value="combined">Combined Form</TabsTrigger>
        </TabsList>

        <TabsContent value="search" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Country Selector with Search</CardTitle>
                <CardDescription>Type to search through 195+ countries</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Default Variant (with search)</label>
                  <CountrySelector
                    value={selectedCountry}
                    onChange={setSelectedCountry}
                    showFlag={true}
                    showDialCode={true}
                    showSearch={true}
                    showPopular={true}
                    placeholder="Search and select country..."
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Compact Variant (with search)</label>
                  <CountrySelector
                    value={compactCountry}
                    onChange={setCompactCountry}
                    variant="compact"
                    showFlag={true}
                    showDialCode={true}
                    showSearch={true}
                    showPopular={true}
                    placeholder="Search countries..."
                  />
                </div>

                <div className="p-3 bg-muted rounded-lg text-sm">
                  <p><strong>Selected:</strong> {selectedCountry || 'None'}</p>
                  <p><strong>Compact:</strong> {compactCountry || 'None'}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Currency Selector with Search</CardTitle>
                <CardDescription>Type to search through 150+ currencies</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Default Variant (with search)</label>
                  <CurrencySelector
                    value={selectedCurrency}
                    onChange={setSelectedCurrency}
                    showSymbol={true}
                    showFlag={true}
                    showSearch={true}
                    showPopular={true}
                    placeholder="Search and select currency..."
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Compact Variant (with search)</label>
                  <CurrencySelector
                    value={compactCurrency}
                    onChange={setCompactCurrency}
                    variant="compact"
                    showSymbol={true}
                    showFlag={true}
                    showSearch={true}
                    showPopular={true}
                    placeholder="Search currencies..."
                  />
                </div>

                <div className="p-3 bg-muted rounded-lg text-sm">
                  <p><strong>Selected:</strong> {selectedCurrency || 'None'}</p>
                  <p><strong>Compact:</strong> {compactCurrency || 'None'}</p>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Search Features</CardTitle>
              <CardDescription>Enhanced search capabilities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-medium">Country Search Features:</h4>
                  <ul className="text-sm space-y-1 text-muted-foreground">
                    <li>• Search by country name (e.g., "United States")</li>
                    <li>• Search by country code (e.g., "US", "UK")</li>
                    <li>• Search by currency (e.g., "USD", "EUR")</li>
                    <li>• Search by continent (e.g., "Europe", "Asia")</li>
                    <li>• Popular countries section</li>
                    <li>• Real-time filtering</li>
                  </ul>
                </div>
                <div className="space-y-3">
                  <h4 className="font-medium">Currency Search Features:</h4>
                  <ul className="text-sm space-y-1 text-muted-foreground">
                    <li>• Search by currency name (e.g., "Dollar")</li>
                    <li>• Search by currency code (e.g., "USD", "EUR")</li>
                    <li>• Search by symbol (e.g., "$", "€")</li>
                    <li>• Search by region (e.g., "North America")</li>
                    <li>• Popular currencies section</li>
                    <li>• Real-time filtering</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="checkboxes" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Card Variant Checkboxes</CardTitle>
                <CardDescription>Modern card-style checkboxes with icons</CardDescription>
              </CardHeader>
              <CardContent>
                <ModernCheckbox
                  items={designToolItems}
                  value={designTools}
                  onChange={setDesignTools}
                  variant="card"
                  columns={2}
                  size="md"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Card with Descriptions</CardTitle>
                <CardDescription>Checkboxes with detailed descriptions</CardDescription>
              </CardHeader>
              <CardContent>
                <ModernCheckbox
                  items={featureItems}
                  value={features}
                  onChange={setFeatures}
                  variant="card"
                  columns={1}
                  size="md"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Compact Variant</CardTitle>
                <CardDescription>Space-efficient checkbox pills</CardDescription>
              </CardHeader>
              <CardContent>
                <ModernCheckbox
                  items={deviceItems}
                  value={devices}
                  onChange={setDevices}
                  variant="compact"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Default Variant</CardTitle>
                <CardDescription>Traditional checkbox list</CardDescription>
              </CardHeader>
              <CardContent>
                <ModernCheckbox
                  items={preferenceItems}
                  value={preferences}
                  onChange={setPreferences}
                  variant="default"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="combined" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Complete Form Example</CardTitle>
              <CardDescription>All enhanced components working together</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Country</label>
                  <CountrySelector
                    value={selectedCountry}
                    onChange={setSelectedCountry}
                    showFlag={true}
                    showDialCode={true}
                    placeholder="Search countries..."
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Currency</label>
                  <CurrencySelector
                    value={selectedCurrency}
                    onChange={setSelectedCurrency}
                    showSymbol={true}
                    showFlag={true}
                    placeholder="Search currencies..."
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">Design Tools</h4>
                <ModernCheckbox
                  items={designToolItems}
                  value={designTools}
                  onChange={setDesignTools}
                  variant="card"
                  columns={4}
                  size="sm"
                />
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">Target Devices</h4>
                <ModernCheckbox
                  items={deviceItems}
                  value={devices}
                  onChange={setDevices}
                  variant="compact"
                />
              </div>

              <Button onClick={handleSubmit} className="w-full">
                Save Settings
              </Button>

              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">Current Selection:</h4>
                <div className="text-sm space-y-1">
                  <p><strong>Country:</strong> {selectedCountry || 'None'}</p>
                  <p><strong>Currency:</strong> {selectedCurrency || 'None'}</p>
                  <p><strong>Design Tools:</strong> {designTools.length} selected</p>
                  <p><strong>Devices:</strong> {devices.length} selected</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
