"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  CentralizedButton,
  CentralizedInput,
  CentralizedCard,
  CentralizedContainer,
  CentralizedStack,
  CentralizedGrid,
} from '@/components/ui/centralized-components';
import {
  User,
  Mail,
  Phone,
  Building,
  Calendar,
  MapPin,
  Star,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  Package,
  Truck,
  Users,
  Target,
  TrendingUp,
  Award,
  Zap,
  Shield,
  Globe,
  Rocket,
  PlusCircle,
  Filter,
  Search,
  Bot,
  RefreshCw,
  Trash2,
  X,
  Settings,
  ArrowLeft,
  ArrowRight,
  Upload,
  AlertTriangle
} from 'lucide-react';

// Sample data for the table
const sampleData = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    company: 'Acme Corp',
    status: 'Active',
    role: 'Manager',
    lastLogin: '2024-01-15',
    revenue: 125000,
    location: 'New York'
  },
  {
    id: '2',
    name: '<PERSON> Smith',
    email: '<EMAIL>',
    company: 'Tech Solutions',
    status: 'Inactive',
    role: 'Developer',
    lastLogin: '2024-01-10',
    revenue: 85000,
    location: 'San Francisco'
  },
  {
    id: '3',
    name: 'Mike Johnson',
    email: '<EMAIL>',
    company: 'Global Industries',
    status: 'Active',
    role: 'Director',
    lastLogin: '2024-01-16',
    revenue: 200000,
    location: 'London'
  },
  {
    id: '4',
    name: 'Sarah Wilson',
    email: '<EMAIL>',
    company: 'Innovation Labs',
    status: 'Pending',
    role: 'Analyst',
    lastLogin: '2024-01-12',
    revenue: 65000,
    location: 'Toronto'
  },
  {
    id: '5',
    name: 'David Brown',
    email: '<EMAIL>',
    company: 'Future Systems',
    status: 'Active',
    role: 'Engineer',
    lastLogin: '2024-01-14',
    revenue: 95000,
    location: 'Berlin'
  }
];

// Table columns
const columns = [
  {
    accessorKey: 'name',
    title: 'Name',
    header: 'Name',
    cell: ({ row }: any) => (
      <div className="flex items-center space-x-2">
        <User className="h-4 w-4 text-muted-foreground" />
        <span className="font-medium">{row.getValue('name')}</span>
      </div>
    ),
  },
  {
    accessorKey: 'email',
    title: 'Email',
    header: 'Email',
    cell: ({ row }: any) => (
      <div className="flex items-center space-x-2">
        <Mail className="h-4 w-4 text-muted-foreground" />
        <span>{row.getValue('email')}</span>
      </div>
    ),
  },
  {
    accessorKey: 'company',
    title: 'Company',
    header: 'Company',
    cell: ({ row }: any) => (
      <div className="flex items-center space-x-2">
        <Building className="h-4 w-4 text-muted-foreground" />
        <span>{row.getValue('company')}</span>
      </div>
    ),
  },
  {
    accessorKey: 'status',
    title: 'Status',
    header: 'Status',
    cell: ({ row }: any) => {
      const status = row.getValue('status') as string;
      return (
        <Badge 
          variant={
            status === 'Active' ? 'default' : 
            status === 'Inactive' ? 'destructive' : 
            'secondary'
          }
        >
          {status === 'Active' && <CheckCircle className="mr-1 h-3 w-3" />}
          {status === 'Inactive' && <XCircle className="mr-1 h-3 w-3" />}
          {status === 'Pending' && <Clock className="mr-1 h-3 w-3" />}
          {status}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'role',
    title: 'Role',
    header: 'Role',
    cell: ({ row }: any) => (
      <span className="text-sm">{row.getValue('role')}</span>
    ),
  },
  {
    accessorKey: 'revenue',
    title: 'Revenue',
    header: 'Revenue',
    cell: ({ row }: any) => (
      <div className="flex items-center space-x-1">
        <DollarSign className="h-4 w-4 text-green-600" />
        <span className="font-medium">
          ${(row.getValue('revenue') as number).toLocaleString()}
        </span>
      </div>
    ),
  },
  {
    accessorKey: 'location',
    title: 'Location',
    header: 'Location',
    cell: ({ row }: any) => (
      <div className="flex items-center space-x-2">
        <MapPin className="h-4 w-4 text-muted-foreground" />
        <span>{row.getValue('location')}</span>
      </div>
    ),
  },
];

// Filters for the table
const filters = [
  {
    id: 'status',
    label: 'Status',
    type: 'select' as const,
    placeholder: 'Filter by status',
    options: [
      { label: 'Active', value: 'Active' },
      { label: 'Inactive', value: 'Inactive' },
      { label: 'Pending', value: 'Pending' },
    ],
  },
  {
    id: 'role',
    label: 'Role',
    type: 'select' as const,
    placeholder: 'Filter by role',
    options: [
      { label: 'Manager', value: 'Manager' },
      { label: 'Developer', value: 'Developer' },
      { label: 'Director', value: 'Director' },
      { label: 'Analyst', value: 'Analyst' },
      { label: 'Engineer', value: 'Engineer' },
    ],
  },
];

// Bulk actions
const bulkActions = [
  {
    label: 'Activate Selected',
    onClick: (selectedRows: any[]) => {
      console.log('Activating:', selectedRows);
    },
    icon: CheckCircle,
    variant: 'default' as const,
  },
  {
    label: 'Deactivate Selected',
    onClick: (selectedRows: any[]) => {
      console.log('Deactivating:', selectedRows);
    },
    icon: XCircle,
    variant: 'destructive' as const,
  },
];

export default function UIComponentsDemo() {
  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
          EVEXA UI Components Showcase
        </h1>
        <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
          Standardized UI components for the EVEXA platform. Choose your preferred styles for consistent implementation across all modules.
        </p>
        <div className="flex items-center justify-center gap-2 text-sm">
          <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            ✅ NEW: UI Consistency Fixes
          </Badge>
          <span className="text-muted-foreground">Sidebar alignment, button behaviors, and input consistency fixed</span>
        </div>
      </div>

      <Tabs defaultValue="table" className="w-full">
        <TabsList className="grid w-full grid-cols-9">
          <TabsTrigger value="table">Advanced Table</TabsTrigger>
          <TabsTrigger value="buttons">Enhanced Buttons</TabsTrigger>
          <TabsTrigger value="headers">Page Headers</TabsTrigger>
          <TabsTrigger value="forms">Modern Forms</TabsTrigger>
          <TabsTrigger value="cards">Component Cards</TabsTrigger>
          <TabsTrigger value="navigation">Navigation</TabsTrigger>
          <TabsTrigger value="modals">Modals & Dialogs</TabsTrigger>
          <TabsTrigger value="data">Data Display</TabsTrigger>
          <TabsTrigger value="fixes">UI Fixes</TabsTrigger>
        </TabsList>

        <TabsContent value="table" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5 text-primary" />
                Advanced Data Table with New Header Design
              </CardTitle>
              <CardDescription>
                Updated table header matching your design - Search, Add product, Actions, Filter
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AdvancedDataTable
                data={sampleData}
                columns={columns}
                enableGlobalSearch={true}
                searchPlaceholder="Search"
                enableColumnFilters={true}
                filters={filters}
                enableRowSelection={true}
                enableMultiRowSelection={true}
                bulkActions={bulkActions}
                primaryAction={{
                  label: "Add User",
                  onClick: () => console.log("Add user clicked"),
                  icon: User
                }}
                enableColumnVisibility={true}
                enableExport={true}
                exportFormats={['csv', 'excel']}
                exportFileName="demo-data"
                enableSorting={true}
                enablePagination={true}
                pageSize={10}
                variant="default"
                className="w-full"
              />
            </CardContent>
          </Card>

          <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
            <CardHeader>
              <CardTitle className="text-green-800 dark:text-green-200">
                ✅ New Table Header Features
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-semibold">Left Side</h4>
                  <ul className="text-sm space-y-1">
                    <li>• <strong>Search Input</strong>: Full-width search with icon</li>
                    <li>• <strong>Results Count</strong>: Shows total results</li>
                    <li>• <strong>Bulk Actions</strong>: Appears when rows selected</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold">Right Side</h4>
                  <ul className="text-sm space-y-1">
                    <li>• <strong>Add Product</strong>: Primary blue button with slide animation</li>
                    <li>• <strong>Actions Dropdown</strong>: Column visibility, export, refresh</li>
                    <li>• <strong>Filter Button</strong>: Toggle filters with active count</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="buttons" className="space-y-6">
          {/* Sliding Icon Animation Pattern */}
          <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
            <CardHeader>
              <CardTitle className="text-green-800 dark:text-green-200">
                ✅ Enhanced EVEXA Buttons (Recommended)
              </CardTitle>
              <CardDescription className="text-green-700 dark:text-green-300">
                Professional buttons with sliding icon animations - perfect for EVEXA's platform
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Primary Actions */}
                <div className="space-y-3">
                  <Badge variant="default">Primary Actions</Badge>

                  <button className="w-full group relative inline-flex items-center justify-center overflow-hidden rounded-md bg-primary px-6 py-3 text-primary-foreground hover:bg-primary/90 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                    <span className="absolute -start-full transition-all group-hover:start-3">
                      <PlusCircle className="size-5" />
                    </span>
                    <span className="text-sm font-medium transition-all group-hover:ms-3">Create Task</span>
                  </button>

                  <button className="w-full group relative inline-flex items-center justify-center overflow-hidden rounded-md bg-green-600 px-6 py-3 text-white hover:bg-green-700 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                    <span className="absolute -start-full transition-all group-hover:start-3">
                      <CheckCircle className="size-5" />
                    </span>
                    <span className="text-sm font-medium transition-all group-hover:ms-3">Save Changes</span>
                  </button>

                  <button className="w-full group relative inline-flex items-center justify-center overflow-hidden rounded-md bg-purple-600 px-6 py-3 text-white hover:bg-purple-700 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                    <span className="absolute -start-full transition-all group-hover:start-3">
                      <Zap className="size-5" />
                    </span>
                    <span className="text-sm font-medium transition-all group-hover:ms-3">AI Create</span>
                  </button>
                </div>

                {/* Secondary Actions */}
                <div className="space-y-3">
                  <Badge variant="secondary">Secondary Actions</Badge>

                  <button className="w-full group relative inline-flex items-center justify-center overflow-hidden rounded-md border border-input px-6 py-3 text-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden">
                    <span className="absolute -start-full transition-all group-hover:start-3">
                      <Filter className="size-5" />
                    </span>
                    <span className="text-sm font-medium transition-all group-hover:ms-3">Filter Results</span>
                  </button>

                  <button className="w-full group relative inline-flex items-center justify-center overflow-hidden rounded-md border border-input px-6 py-3 text-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden">
                    <span className="absolute -start-full transition-all group-hover:start-3">
                      <Search className="size-5" />
                    </span>
                    <span className="text-sm font-medium transition-all group-hover:ms-3">Search</span>
                  </button>

                  <button className="w-full group relative inline-flex items-center justify-center overflow-hidden rounded-md border border-input px-6 py-3 text-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden">
                    <span className="absolute -start-full transition-all group-hover:start-3">
                      <RefreshCw className="size-5 transition-transform group-hover:rotate-180" />
                    </span>
                    <span className="text-sm font-medium transition-all group-hover:ms-3">Refresh</span>
                  </button>
                </div>

                {/* Destructive Actions */}
                <div className="space-y-3">
                  <Badge variant="destructive">Destructive Actions</Badge>

                  <button className="w-full group relative inline-flex items-center justify-center overflow-hidden rounded-md bg-red-600 px-6 py-3 text-white hover:bg-red-700 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                    <span className="absolute -start-full transition-all group-hover:start-3">
                      <Trash2 className="size-5" />
                    </span>
                    <span className="text-sm font-medium transition-all group-hover:ms-3">Delete Item</span>
                  </button>

                  <button className="w-full group relative inline-flex items-center justify-center overflow-hidden rounded-md border border-input px-6 py-3 text-muted-foreground hover:bg-muted hover:text-foreground transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden">
                    <span className="absolute -start-full transition-all group-hover:start-3">
                      <X className="size-5" />
                    </span>
                    <span className="text-sm font-medium transition-all group-hover:ms-3">Cancel</span>
                  </button>

                  <button className="w-full group relative inline-flex items-center justify-center overflow-hidden rounded-md px-6 py-3 text-muted-foreground hover:bg-muted hover:text-foreground transition-all duration-200 hover:scale-105 hover:shadow-sm focus:ring-2 focus:outline-hidden">
                    <span className="absolute -start-full transition-all group-hover:start-3">
                      <Settings className="size-5" />
                    </span>
                    <span className="text-sm font-medium transition-all group-hover:ms-3">Settings</span>
                  </button>
                </div>
              </div>

              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-semibold mb-2">How It Works:</h4>
                <ul className="text-sm space-y-1">
                  <li>• <code>overflow-hidden</code> hides icon when off-screen</li>
                  <li>• <code>absolute -start-full</code> positions icon completely off-screen left</li>
                  <li>• <code>group-hover:start-3</code> slides icon to position on hover</li>
                  <li>• <code>group-hover:ms-3</code> adds margin to text to make room</li>
                  <li>• <code>transition-all</code> makes everything smooth</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="headers" className="space-y-6">
          {/* Clean Page Headers */}
          <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
            <CardHeader>
              <CardTitle className="text-blue-800 dark:text-blue-200">
                ✅ Clean Page Headers (Recommended)
              </CardTitle>
              <CardDescription className="text-blue-700 dark:text-blue-300">
                Simplified page headers that avoid duplication with table actions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">

              {/* Option 1: Simple Title Only */}
              <div className="space-y-4">
                <Badge variant="default">Option 1: Simple Title Only</Badge>
                <div className="p-6 border rounded-lg bg-background">
                  <div className="flex items-center gap-3">
                    <div className="text-primary">
                      <Package className="h-8 w-8" />
                    </div>
                    <div>
                      <h1 className="text-3xl font-bold tracking-tight">Task Management</h1>
                      <p className="text-muted-foreground">Organize and track your exhibition tasks efficiently</p>
                    </div>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  <strong>Best for:</strong> Pages where the table has the primary action button. Keeps header clean and avoids duplication.
                </p>
              </div>

              {/* Option 2: Title with Actions (No Filter) */}
              <div className="space-y-4">
                <Badge variant="secondary">Option 2: Title with Actions (No Filter)</Badge>
                <div className="p-6 border rounded-lg bg-background">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="text-primary">
                        <Users className="h-8 w-8" />
                      </div>
                      <div>
                        <h1 className="text-3xl font-bold tracking-tight">Team Members</h1>
                        <p className="text-muted-foreground">Manage your exhibition team and permissions</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <button className="group relative inline-flex items-center overflow-hidden rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                        <span className="absolute -start-full transition-all group-hover:start-2">
                          <PlusCircle className="size-4" />
                        </span>
                        <span className="text-sm font-medium transition-all group-hover:ms-2">Add Member</span>
                      </button>
                    </div>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  <strong>Best for:</strong> Clean header with primary action only. No filter button to avoid duplication with table filters.
                </p>
              </div>

              {/* Option 3: With Back Arrow (Like Add Task Page) */}
              <div className="space-y-4">
                <Badge variant="outline">Option 3: With Back Arrow (Like Add Task Page)</Badge>
                <div className="p-6 border rounded-lg bg-background">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <button className="flex items-center justify-center w-10 h-10 rounded-md border border-input text-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden">
                        <ArrowLeft className="size-4" />
                      </button>
                      <div className="text-primary">
                        <Package className="h-8 w-8" />
                      </div>
                      <div>
                        <h1 className="text-3xl font-bold tracking-tight">Add New Task</h1>
                        <p className="text-muted-foreground">Create a new task for your exhibition project</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <button className="group relative inline-flex items-center overflow-hidden rounded-md border border-input px-4 py-2 text-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden">
                        <span className="absolute -start-full transition-all group-hover:start-2">
                          <X className="size-4" />
                        </span>
                        <span className="text-sm font-medium transition-all group-hover:ms-2">Cancel</span>
                      </button>
                      <button className="group relative inline-flex items-center overflow-hidden rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                        <span className="absolute -start-full transition-all group-hover:start-2">
                          <CheckCircle className="size-4" />
                        </span>
                        <span className="text-sm font-medium transition-all group-hover:ms-2">Save Task</span>
                      </button>
                    </div>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  <strong>Best for:</strong> Form pages, detail pages, or any page where user needs to navigate back. Includes back arrow and form actions.
                </p>
              </div>

              {/* Option 4: Title with Breadcrumbs */}
              <div className="space-y-4">
                <Badge variant="outline">Option 4: Title with Breadcrumbs</Badge>
                <div className="p-6 border rounded-lg bg-background">
                  <div className="space-y-3">
                    <nav className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <span>Dashboard</span>
                      <span>/</span>
                      <span>Financials</span>
                      <span>/</span>
                      <span className="text-foreground font-medium">Budgets</span>
                    </nav>
                    <div className="flex items-center gap-3">
                      <div className="text-primary">
                        <DollarSign className="h-8 w-8" />
                      </div>
                      <div>
                        <h1 className="text-3xl font-bold tracking-tight">Budget Management</h1>
                        <p className="text-muted-foreground">Track and manage exhibition budgets and expenses</p>
                      </div>
                    </div>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  <strong>Best for:</strong> Deep navigation pages where users need context of where they are.
                </p>
              </div>

              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-semibold mb-2">Design Principles:</h4>
                <ul className="text-sm space-y-1">
                  <li>• <strong>Avoid duplication:</strong> If table has "Create" button, don't add another in header</li>
                  <li>• <strong>Keep it clean:</strong> Headers should provide context, not clutter</li>
                  <li>• <strong>Use icons consistently:</strong> Match the module's primary icon</li>
                  <li>• <strong>Descriptive text:</strong> Help users understand the page purpose</li>
                  <li>• <strong>Responsive design:</strong> Stack elements on mobile</li>
                </ul>
              </div>

              <div className="p-4 bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">⚠️ Avoid This:</h4>
                <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                  <li>• Multiple "Create" buttons on the same page</li>
                  <li>• Overcrowded headers with too many actions</li>
                  <li>• Inconsistent icon usage across modules</li>
                  <li>• Headers that compete with table toolbars</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="forms" className="space-y-6">
          {/* Modern Form Components */}
          <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
            <CardHeader>
              <CardTitle className="text-blue-800 dark:text-blue-200">
                ✅ Modern Form Components (Standardized)
              </CardTitle>
              <CardDescription className="text-blue-700 dark:text-blue-300">
                Comprehensive form system with validation, auto-save, progress tracking, and consistent styling
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">

              {/* Form Variants */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

                {/* Default Form */}
                <div className="space-y-3">
                  <Badge variant="default">Default Form</Badge>
                  <div className="p-4 border rounded-lg bg-background">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Full Name</label>
                        <input
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                          placeholder="Enter your full name"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Email</label>
                        <input
                          type="email"
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                          placeholder="Enter your email"
                        />
                      </div>
                      <button className="w-full group relative inline-flex items-center justify-center overflow-hidden rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                        <span className="absolute -start-full transition-all group-hover:start-2">
                          <CheckCircle className="size-4" />
                        </span>
                        <span className="text-sm font-medium transition-all group-hover:ms-2">Submit</span>
                      </button>
                    </div>
                  </div>
                </div>

                {/* Card Form */}
                <div className="space-y-3">
                  <Badge variant="secondary">Card Form</Badge>
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Contact Information</CardTitle>
                      <CardDescription>Please provide your contact details</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Company</label>
                        <input
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                          placeholder="Your company name"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Phone</label>
                        <input
                          type="tel"
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                          placeholder="+****************"
                        />
                      </div>
                    </CardContent>
                    <CardFooter>
                      <button className="w-full group relative inline-flex items-center justify-center overflow-hidden rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                        <span className="absolute -start-full transition-all group-hover:start-2">
                          <CheckCircle className="size-4" />
                        </span>
                        <span className="text-sm font-medium transition-all group-hover:ms-2">Save Contact</span>
                      </button>
                    </CardFooter>
                  </Card>
                </div>

                {/* Multi-Step Form */}
                <div className="space-y-3">
                  <Badge variant="outline">Multi-Step Form</Badge>
                  <div className="p-4 border rounded-lg bg-background">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="font-medium">Step 2 of 3</span>
                          <span className="text-muted-foreground">67%</span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <div className="bg-primary h-2 rounded-full transition-all duration-300" style={{width: '67%'}}></div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Preferences</label>
                        <select className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
                          <option>Select preference</option>
                          <option>Email notifications</option>
                          <option>SMS notifications</option>
                          <option>No notifications</option>
                        </select>
                      </div>
                      <div className="flex gap-2">
                        <button className="flex-1 group relative inline-flex items-center justify-center overflow-hidden rounded-md border border-input px-4 py-2 text-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden">
                          <span className="absolute -start-full transition-all group-hover:start-2">
                            <ArrowLeft className="size-4" />
                          </span>
                          <span className="text-sm font-medium transition-all group-hover:ms-2">Previous</span>
                        </button>
                        <button className="flex-1 group relative inline-flex items-center justify-center overflow-hidden rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                          <span className="absolute -end-full transition-all group-hover:end-2">
                            <ArrowRight className="size-4" />
                          </span>
                          <span className="text-sm font-medium transition-all group-hover:me-2">Next</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Professional Fieldsets (DaisyUI-inspired) */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Professional Fieldsets (DaisyUI-inspired)</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                  {/* User Profile Fieldset */}
                  <fieldset className="border border-border rounded-lg bg-card p-6 space-y-4">
                    <legend className="px-2 text-lg font-semibold text-foreground">User Profile</legend>

                    <div className="space-y-2">
                      <label className="text-sm font-medium text-foreground">Full Name</label>
                      <input
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                        placeholder="John Doe"
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium text-foreground">Email</label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <input
                          type="email"
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 pl-10 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium text-foreground">Company</label>
                      <div className="relative">
                        <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <input
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 pl-10 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                          placeholder="ACME Corp"
                        />
                      </div>
                    </div>

                    <p className="text-xs text-muted-foreground">This information will be used for your exhibition profile</p>
                  </fieldset>

                  {/* Exhibition Settings Fieldset */}
                  <fieldset className="border border-border rounded-lg bg-card p-6 space-y-4">
                    <legend className="px-2 text-lg font-semibold text-foreground">Exhibition Settings</legend>

                    <div className="space-y-2">
                      <label className="text-sm font-medium text-foreground">Exhibition Name</label>
                      <input
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                        placeholder="Tech Expo 2024"
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium text-foreground">Event Date</label>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <input
                          type="date"
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 pl-10 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium text-foreground">Location</label>
                      <div className="relative">
                        <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <input
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 pl-10 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                          placeholder="Convention Center"
                        />
                      </div>
                    </div>

                    <p className="text-xs text-muted-foreground">Configure your exhibition details and preferences</p>
                  </fieldset>
                </div>
              </div>

              {/* Join Components (DaisyUI-inspired) */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Join Components (DaisyUI-inspired)</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                  {/* Search with Button */}
                  <div className="space-y-3">
                    <h4 className="font-medium">Search with Action</h4>
                    <div className="flex">
                      <input
                        className="flex h-10 flex-1 rounded-l-md border border-r-0 border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10"
                        placeholder="Search exhibitions..."
                      />
                      <button className="group relative inline-flex items-center overflow-hidden rounded-r-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden focus:z-10">
                        <span className="absolute -start-full transition-all group-hover:start-2">
                          <Search className="size-4" />
                        </span>
                        <span className="text-sm font-medium transition-all group-hover:ms-2">Search</span>
                      </button>
                    </div>
                  </div>

                  {/* Email Subscription */}
                  <div className="space-y-3">
                    <h4 className="font-medium">Newsletter Subscription</h4>
                    <div className="flex">
                      <div className="relative flex-1">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <input
                          type="email"
                          className="flex h-10 w-full rounded-l-md border border-r-0 border-input bg-background px-3 py-2 pl-10 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10"
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <button className="group relative inline-flex items-center overflow-hidden rounded-r-md bg-green-600 px-4 py-2 text-white hover:bg-green-700 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden focus:z-10">
                        <span className="absolute -start-full transition-all group-hover:start-2">
                          <CheckCircle className="size-4" />
                        </span>
                        <span className="text-sm font-medium transition-all group-hover:ms-2">Subscribe</span>
                      </button>
                    </div>
                  </div>

                  {/* Quantity Selector */}
                  <div className="space-y-3">
                    <h4 className="font-medium">Quantity Selector</h4>
                    <div className="flex w-fit">
                      <button className="flex items-center justify-center w-10 h-10 rounded-l-md border border-r-0 border-input bg-background hover:bg-accent transition-colors">
                        <span className="text-lg font-bold">-</span>
                      </button>
                      <input
                        type="number"
                        className="flex h-10 w-16 border border-input bg-background px-3 py-2 text-sm text-center ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10"
                        defaultValue="1"
                        min="1"
                      />
                      <button className="flex items-center justify-center w-10 h-10 rounded-r-md border border-l-0 border-input bg-background hover:bg-accent transition-colors">
                        <span className="text-lg font-bold">+</span>
                      </button>
                    </div>
                  </div>

                  {/* Filter Dropdown */}
                  <div className="space-y-3">
                    <h4 className="font-medium">Filter with Dropdown</h4>
                    <div className="flex">
                      <select className="flex h-10 flex-1 rounded-l-md border border-r-0 border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10">
                        <option>All Categories</option>
                        <option>Technology</option>
                        <option>Healthcare</option>
                        <option>Finance</option>
                      </select>
                      <button className="group relative inline-flex items-center overflow-hidden rounded-r-md border border-input px-4 py-2 text-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden focus:z-10">
                        <span className="absolute -start-full transition-all group-hover:start-2">
                          <Filter className="size-4" />
                        </span>
                        <span className="text-sm font-medium transition-all group-hover:ms-2">Filter</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-semibold mb-2">Form Component Features:</h4>
                <ul className="text-sm space-y-1">
                  <li>• <strong>Consistent styling</strong>: All inputs, labels, and buttons follow the same design system</li>
                  <li>• <strong>Enhanced buttons</strong>: Sliding icon animations on all form actions</li>
                  <li>• <strong>Icon integration</strong>: Prefix icons for better UX and visual clarity</li>
                  <li>• <strong>Validation feedback</strong>: Real-time validation with clear error messages</li>
                  <li>• <strong>Progress tracking</strong>: Visual progress indicators for multi-step forms</li>
                  <li>• <strong>Auto-save</strong>: Automatic saving with visual feedback</li>
                  <li>• <strong>Accessibility</strong>: Proper labels, focus management, and keyboard navigation</li>
                  <li>• <strong>Responsive design</strong>: Forms adapt to different screen sizes</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cards" className="space-y-6">
          {/* Card Components */}
          <Card className="border-purple-200 bg-purple-50 dark:border-purple-800 dark:bg-purple-950">
            <CardHeader>
              <CardTitle className="text-purple-800 dark:text-purple-200">
                ✅ Card Components (Standardized)
              </CardTitle>
              <CardDescription className="text-purple-700 dark:text-purple-300">
                Consistent card layouts for content, stats, actions, and data display across EVEXA
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">

              {/* Basic Cards */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Basic Card Variants</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">

                  {/* Simple Card */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Simple Card</CardTitle>
                      <CardDescription>Basic card with header and content</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        This is a simple card with consistent spacing and typography. Perfect for basic content display.
                      </p>
                    </CardContent>
                  </Card>

                  {/* Card with Icon */}
                  <Card>
                    <CardHeader>
                      <div className="flex items-center gap-2">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          <Users className="h-5 w-5 text-primary" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">Team Card</CardTitle>
                          <CardDescription>Card with icon header</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Enhanced card with icon in header for better visual hierarchy and context.
                      </p>
                    </CardContent>
                  </Card>

                  {/* Card with Actions */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Action Card</CardTitle>
                      <CardDescription>Card with footer actions</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Card with action buttons in the footer for user interactions.
                      </p>
                    </CardContent>
                    <CardFooter className="flex gap-2">
                      <button className="group relative inline-flex items-center overflow-hidden rounded-md border border-input px-3 py-1.5 text-sm text-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden">
                        <span className="absolute -start-full transition-all group-hover:start-1">
                          <X className="size-3" />
                        </span>
                        <span className="text-xs font-medium transition-all group-hover:ms-1">Cancel</span>
                      </button>
                      <button className="group relative inline-flex items-center overflow-hidden rounded-md bg-primary px-3 py-1.5 text-primary-foreground hover:bg-primary/90 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                        <span className="absolute -start-full transition-all group-hover:start-1">
                          <CheckCircle className="size-3" />
                        </span>
                        <span className="text-xs font-medium transition-all group-hover:ms-1">Confirm</span>
                      </button>
                    </CardFooter>
                  </Card>
                </div>
              </div>

              {/* Professional Stats Cards (DaisyUI-inspired) */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Professional Statistics (DaisyUI-inspired)</h3>

                {/* Stats Container */}
                <div className="bg-background border border-border rounded-lg shadow-sm">
                  <div className="grid grid-cols-1 md:grid-cols-4 divide-y md:divide-y-0 md:divide-x divide-border">

                    {/* Stat 1 */}
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-2">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          <Users className="h-6 w-6 text-primary" />
                        </div>
                      </div>
                      <div className="text-sm font-medium text-muted-foreground mb-1">Total Users</div>
                      <div className="text-3xl font-bold text-foreground mb-1">25.6K</div>
                      <div className="text-xs text-green-600 flex items-center gap-1">
                        <TrendingUp className="h-3 w-3" />
                        21% more than last month
                      </div>
                    </div>

                    {/* Stat 2 */}
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-2">
                        <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                          <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
                        </div>
                      </div>
                      <div className="text-sm font-medium text-muted-foreground mb-1">Revenue</div>
                      <div className="text-3xl font-bold text-green-600 mb-1">$89.4K</div>
                      <div className="text-xs text-green-600 flex items-center gap-1">
                        <TrendingUp className="h-3 w-3" />
                        ↗︎ 400 (22%)
                      </div>
                    </div>

                    {/* Stat 3 */}
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-2">
                        <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                          <Package className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                        </div>
                      </div>
                      <div className="text-sm font-medium text-muted-foreground mb-1">Orders</div>
                      <div className="text-3xl font-bold text-foreground mb-1">1,200</div>
                      <div className="text-xs text-red-600 flex items-center gap-1">
                        <TrendingUp className="h-3 w-3 rotate-180" />
                        ↘︎ 90 (14%)
                      </div>
                    </div>

                    {/* Stat 4 with Actions */}
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-2">
                        <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                          <Target className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                        </div>
                      </div>
                      <div className="text-sm font-medium text-muted-foreground mb-1">Account Balance</div>
                      <div className="text-3xl font-bold text-foreground mb-2">$89,400</div>
                      <div className="flex gap-1">
                        <button className="group relative inline-flex items-center overflow-hidden rounded-md bg-green-600 px-2 py-1 text-xs text-white hover:bg-green-700 transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden">
                          <span className="absolute -start-full transition-all group-hover:start-1">
                            <PlusCircle className="size-3" />
                          </span>
                          <span className="text-xs font-medium transition-all group-hover:ms-1">Add funds</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Pricing Cards */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Enhanced Pricing Cards (DaisyUI-inspired)</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">

                  {/* Basic Plan */}
                  <Card className="relative">
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start mb-4">
                        <h3 className="text-2xl font-bold">Basic</h3>
                        <span className="text-xl font-semibold">$19/mo</span>
                      </div>
                      <ul className="space-y-3 mb-6">
                        <li className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span>Up to 5 exhibitions</span>
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span>Basic analytics</span>
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span>Email support</span>
                        </li>
                        <li className="flex items-center gap-2 text-sm opacity-50">
                          <XCircle className="h-4 w-4 text-muted-foreground" />
                          <span className="line-through">Advanced features</span>
                        </li>
                      </ul>
                      <button className="w-full group relative inline-flex items-center justify-center overflow-hidden rounded-md border border-input px-4 py-2 text-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden">
                        <span className="absolute -start-full transition-all group-hover:start-2">
                          <Rocket className="size-4" />
                        </span>
                        <span className="text-sm font-medium transition-all group-hover:ms-2">Get Started</span>
                      </button>
                    </CardContent>
                  </Card>

                  {/* Professional Plan - Popular */}
                  <Card className="relative border-primary shadow-lg scale-105">
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-primary text-primary-foreground px-3 py-1">Most Popular</Badge>
                    </div>
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start mb-4">
                        <h3 className="text-2xl font-bold">Professional</h3>
                        <span className="text-xl font-semibold">$49/mo</span>
                      </div>
                      <ul className="space-y-3 mb-6">
                        <li className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span>Unlimited exhibitions</span>
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span>Advanced analytics</span>
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span>Priority support</span>
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span>Team collaboration</span>
                        </li>
                      </ul>
                      <button className="w-full group relative inline-flex items-center justify-center overflow-hidden rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                        <span className="absolute -start-full transition-all group-hover:start-2">
                          <Star className="size-4" />
                        </span>
                        <span className="text-sm font-medium transition-all group-hover:ms-2">Choose Plan</span>
                      </button>
                    </CardContent>
                  </Card>

                  {/* Enterprise Plan */}
                  <Card className="relative">
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start mb-4">
                        <h3 className="text-2xl font-bold">Enterprise</h3>
                        <span className="text-xl font-semibold">$99/mo</span>
                      </div>
                      <ul className="space-y-3 mb-6">
                        <li className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span>Everything in Professional</span>
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span>Custom integrations</span>
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span>Dedicated support</span>
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span>SLA guarantee</span>
                        </li>
                      </ul>
                      <button className="w-full group relative inline-flex items-center justify-center overflow-hidden rounded-md bg-gradient-to-r from-purple-600 to-blue-600 px-4 py-2 text-white hover:from-purple-700 hover:to-blue-700 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                        <span className="absolute -start-full transition-all group-hover:start-2">
                          <Shield className="size-4" />
                        </span>
                        <span className="text-sm font-medium transition-all group-hover:ms-2">Contact Sales</span>
                      </button>
                    </CardContent>
                  </Card>
                </div>
              </div>

              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-semibold mb-2">Card Component Features:</h4>
                <ul className="text-sm space-y-1">
                  <li>• <strong>Consistent spacing</strong>: Standardized padding and margins across all cards</li>
                  <li>• <strong>Enhanced buttons</strong>: Sliding icon animations in card actions</li>
                  <li>• <strong>Icon integration</strong>: Consistent icon styling and positioning</li>
                  <li>• <strong>Stat displays</strong>: Professional statistics with trend indicators</li>
                  <li>• <strong>Hover effects</strong>: Subtle shadows and scaling on interaction</li>
                  <li>• <strong>Theme awareness</strong>: Colors adapt to light/dark themes</li>
                  <li>• <strong>Responsive design</strong>: Cards adapt to different screen sizes</li>
                  <li>• <strong>Accessibility</strong>: Proper contrast and focus management</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="navigation" className="space-y-6">
          {/* Navigation Components */}
          <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
            <CardHeader>
              <CardTitle className="text-green-800 dark:text-green-200">
                ✅ Navigation Components (Standardized)
              </CardTitle>
              <CardDescription className="text-green-700 dark:text-green-300">
                Consistent navigation elements with enhanced interactions and accessibility
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">

              {/* Breadcrumbs */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Breadcrumb Navigation</h3>
                <div className="space-y-3">

                  {/* Simple Breadcrumbs */}
                  <div className="p-4 border rounded-lg bg-background">
                    <nav className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <button className="hover:text-foreground transition-colors">Dashboard</button>
                      <span>/</span>
                      <button className="hover:text-foreground transition-colors">Financials</button>
                      <span>/</span>
                      <span className="text-foreground font-medium">Budgets</span>
                    </nav>
                  </div>

                  {/* Enhanced Breadcrumbs with Icons */}
                  <div className="p-4 border rounded-lg bg-background">
                    <nav className="flex items-center space-x-2 text-sm">
                      <button className="flex items-center gap-1 text-muted-foreground hover:text-foreground transition-colors">
                        <Building className="h-3 w-3" />
                        <span>Dashboard</span>
                      </button>
                      <ArrowRight className="h-3 w-3 text-muted-foreground" />
                      <button className="flex items-center gap-1 text-muted-foreground hover:text-foreground transition-colors">
                        <Users className="h-3 w-3" />
                        <span>Team</span>
                      </button>
                      <ArrowRight className="h-3 w-3 text-muted-foreground" />
                      <span className="flex items-center gap-1 text-foreground font-medium">
                        <User className="h-3 w-3" />
                        <span>John Doe</span>
                      </span>
                    </nav>
                  </div>
                </div>
              </div>

              {/* Tabs Navigation */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Tab Navigation</h3>
                <div className="space-y-3">

                  {/* Standard Tabs */}
                  <div className="p-4 border rounded-lg bg-background">
                    <div className="border-b border-border">
                      <nav className="flex space-x-8">
                        <button className="border-b-2 border-primary text-primary py-2 px-1 text-sm font-medium">
                          Overview
                        </button>
                        <button className="border-b-2 border-transparent text-muted-foreground hover:text-foreground hover:border-border py-2 px-1 text-sm font-medium transition-colors">
                          Analytics
                        </button>
                        <button className="border-b-2 border-transparent text-muted-foreground hover:text-foreground hover:border-border py-2 px-1 text-sm font-medium transition-colors">
                          Settings
                        </button>
                      </nav>
                    </div>
                  </div>

                  {/* Enhanced Tabs with Icons */}
                  <div className="p-4 border rounded-lg bg-background">
                    <div className="border-b border-border">
                      <nav className="flex space-x-6">
                        <button className="flex items-center gap-2 border-b-2 border-primary text-primary py-2 px-1 text-sm font-medium">
                          <Package className="h-4 w-4" />
                          <span>Products</span>
                        </button>
                        <button className="flex items-center gap-2 border-b-2 border-transparent text-muted-foreground hover:text-foreground hover:border-border py-2 px-1 text-sm font-medium transition-colors">
                          <TrendingUp className="h-4 w-4" />
                          <span>Analytics</span>
                        </button>
                        <button className="flex items-center gap-2 border-b-2 border-transparent text-muted-foreground hover:text-foreground hover:border-border py-2 px-1 text-sm font-medium transition-colors">
                          <Settings className="h-4 w-4" />
                          <span>Settings</span>
                        </button>
                      </nav>
                    </div>
                  </div>
                </div>
              </div>

              {/* Pagination */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Pagination</h3>
                <div className="space-y-3">

                  {/* Standard Pagination */}
                  <div className="p-4 border rounded-lg bg-background">
                    <nav className="flex items-center justify-between">
                      <div className="text-sm text-muted-foreground">
                        Showing 1 to 10 of 97 results
                      </div>
                      <div className="flex items-center space-x-2">
                        <button className="group relative inline-flex items-center overflow-hidden rounded-md border border-input px-3 py-1.5 text-sm text-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden">
                          <span className="absolute -start-full transition-all group-hover:start-1">
                            <ArrowLeft className="size-3" />
                          </span>
                          <span className="text-xs font-medium transition-all group-hover:ms-1">Previous</span>
                        </button>

                        <div className="flex items-center space-x-1">
                          <button className="w-8 h-8 rounded-md bg-primary text-primary-foreground text-sm font-medium">1</button>
                          <button className="w-8 h-8 rounded-md hover:bg-accent text-sm font-medium transition-colors">2</button>
                          <button className="w-8 h-8 rounded-md hover:bg-accent text-sm font-medium transition-colors">3</button>
                          <span className="px-2 text-muted-foreground">...</span>
                          <button className="w-8 h-8 rounded-md hover:bg-accent text-sm font-medium transition-colors">10</button>
                        </div>

                        <button className="group relative inline-flex items-center overflow-hidden rounded-md border border-input px-3 py-1.5 text-sm text-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden">
                          <span className="absolute -end-full transition-all group-hover:end-1">
                            <ArrowRight className="size-3" />
                          </span>
                          <span className="text-xs font-medium transition-all group-hover:me-1">Next</span>
                        </button>
                      </div>
                    </nav>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-semibold mb-2">Navigation Component Features:</h4>
                <ul className="text-sm space-y-1">
                  <li>• <strong>Enhanced buttons</strong>: Sliding icon animations in pagination controls</li>
                  <li>• <strong>Consistent hover states</strong>: Smooth transitions and visual feedback</li>
                  <li>• <strong>Icon integration</strong>: Meaningful icons for better navigation context</li>
                  <li>• <strong>Active states</strong>: Clear indication of current location/selection</li>
                  <li>• <strong>Accessibility</strong>: Proper ARIA labels and keyboard navigation</li>
                  <li>• <strong>Responsive design</strong>: Navigation adapts to different screen sizes</li>
                  <li>• <strong>Theme awareness</strong>: Colors and styles adapt to current theme</li>
                  <li>• <strong>Professional styling</strong>: Clean, modern appearance throughout</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="modals" className="space-y-6">
          {/* Modal & Dialog Components */}
          <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
            <CardHeader>
              <CardTitle className="text-orange-800 dark:text-orange-200">
                ✅ Modal & Dialog Components (Standardized)
              </CardTitle>
              <CardDescription className="text-orange-700 dark:text-orange-300">
                Consistent modal and dialog patterns with proper focus management and accessibility
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">

              {/* Dialog Examples */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Dialog Patterns</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                  {/* Confirmation Dialog */}
                  <div className="p-4 border rounded-lg bg-background">
                    <h4 className="font-medium mb-3">Confirmation Dialog</h4>
                    <div className="p-4 border-2 border-dashed border-muted rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-red-100 dark:bg-red-900 rounded-full">
                            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
                          </div>
                          <div>
                            <h3 className="font-semibold">Delete Item</h3>
                            <p className="text-sm text-muted-foreground">This action cannot be undone.</p>
                          </div>
                        </div>
                        <div className="flex gap-2 justify-end">
                          <button className="group relative inline-flex items-center overflow-hidden rounded-md border border-input px-4 py-2 text-sm text-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden">
                            <span className="absolute -start-full transition-all group-hover:start-2">
                              <X className="size-4" />
                            </span>
                            <span className="text-sm font-medium transition-all group-hover:ms-2">Cancel</span>
                          </button>
                          <button className="group relative inline-flex items-center overflow-hidden rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                            <span className="absolute -start-full transition-all group-hover:start-2">
                              <Trash2 className="size-4" />
                            </span>
                            <span className="text-sm font-medium transition-all group-hover:ms-2">Delete</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Form Dialog */}
                  <div className="p-4 border rounded-lg bg-background">
                    <h4 className="font-medium mb-3">Form Dialog</h4>
                    <div className="p-4 border-2 border-dashed border-muted rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h3 className="font-semibold">Add New User</h3>
                          <button className="p-1 hover:bg-accent rounded-md transition-colors">
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                        <div className="space-y-3">
                          <div className="space-y-1">
                            <label className="text-sm font-medium">Name</label>
                            <input className="w-full h-8 px-2 text-sm border border-input rounded-md bg-background" placeholder="Enter name" />
                          </div>
                          <div className="space-y-1">
                            <label className="text-sm font-medium">Email</label>
                            <input className="w-full h-8 px-2 text-sm border border-input rounded-md bg-background" placeholder="Enter email" />
                          </div>
                        </div>
                        <div className="flex gap-2 justify-end">
                          <button className="group relative inline-flex items-center overflow-hidden rounded-md border border-input px-3 py-1.5 text-sm text-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden">
                            <span className="absolute -start-full transition-all group-hover:start-1">
                              <X className="size-3" />
                            </span>
                            <span className="text-xs font-medium transition-all group-hover:ms-1">Cancel</span>
                          </button>
                          <button className="group relative inline-flex items-center overflow-hidden rounded-md bg-primary px-3 py-1.5 text-primary-foreground hover:bg-primary/90 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                            <span className="absolute -start-full transition-all group-hover:start-1">
                              <CheckCircle className="size-3" />
                            </span>
                            <span className="text-xs font-medium transition-all group-hover:ms-1">Save</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Toast Notifications */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Toast Notifications</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                  {/* Success Toast */}
                  <div className="p-4 border rounded-lg bg-background">
                    <h4 className="font-medium mb-3">Success Toast</h4>
                    <div className="p-3 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-lg">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-green-800 dark:text-green-200">Success!</p>
                          <p className="text-xs text-green-700 dark:text-green-300">Your changes have been saved.</p>
                        </div>
                        <button className="p-1 hover:bg-green-100 dark:hover:bg-green-900 rounded-md transition-colors">
                          <X className="h-4 w-4 text-green-600 dark:text-green-400" />
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Error Toast */}
                  <div className="p-4 border rounded-lg bg-background">
                    <h4 className="font-medium mb-3">Error Toast</h4>
                    <div className="p-3 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg">
                      <div className="flex items-center gap-3">
                        <XCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-red-800 dark:text-red-200">Error!</p>
                          <p className="text-xs text-red-700 dark:text-red-300">Something went wrong. Please try again.</p>
                        </div>
                        <button className="p-1 hover:bg-red-100 dark:hover:bg-red-900 rounded-md transition-colors">
                          <X className="h-4 w-4 text-red-600 dark:text-red-400" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-semibold mb-2">Modal & Dialog Features:</h4>
                <ul className="text-sm space-y-1">
                  <li>• <strong>Enhanced buttons</strong>: Sliding icon animations in all dialog actions</li>
                  <li>• <strong>Consistent patterns</strong>: Standardized layouts for different dialog types</li>
                  <li>• <strong>Focus management</strong>: Proper focus trapping and restoration</li>
                  <li>• <strong>Escape key handling</strong>: Close dialogs with keyboard shortcuts</li>
                  <li>• <strong>Backdrop behavior</strong>: Click outside to close with proper event handling</li>
                  <li>• <strong>Toast notifications</strong>: Non-intrusive feedback with auto-dismiss</li>
                  <li>• <strong>Accessibility</strong>: ARIA labels, roles, and screen reader support</li>
                  <li>• <strong>Theme integration</strong>: Colors and styles adapt to current theme</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="data" className="space-y-6">
          {/* Data Display Components */}
          <Card className="border-indigo-200 bg-indigo-50 dark:border-indigo-800 dark:bg-indigo-950">
            <CardHeader>
              <CardTitle className="text-indigo-800 dark:text-indigo-200">
                ✅ Data Display Components (Standardized)
              </CardTitle>
              <CardDescription className="text-indigo-700 dark:text-indigo-300">
                Consistent data presentation with badges, progress indicators, status displays, and loading states
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">

              {/* Badges and Labels */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Badges & Status Indicators</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                  {/* Status Badges */}
                  <div className="space-y-3">
                    <h4 className="font-medium">Status Badges</h4>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="default">Active</Badge>
                      <Badge variant="secondary">Pending</Badge>
                      <Badge variant="destructive">Inactive</Badge>
                      <Badge variant="outline">Draft</Badge>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-1.5"></div>
                        Online
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                        <Clock className="w-3 h-3 mr-1" />
                        Processing
                      </span>
                    </div>
                  </div>

                  {/* Priority Indicators */}
                  <div className="space-y-3">
                    <h4 className="font-medium">Priority Indicators</h4>
                    <div className="flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                        <span className="w-1.5 h-1.5 bg-red-500 rounded-full mr-1.5"></span>
                        High Priority
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                        <span className="w-1.5 h-1.5 bg-orange-500 rounded-full mr-1.5"></span>
                        Medium Priority
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-1.5"></span>
                        Low Priority
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Progress Indicators */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Progress Indicators</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                  {/* Progress Bars */}
                  <div className="space-y-4">
                    <h4 className="font-medium">Progress Bars</h4>

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Project Completion</span>
                        <span className="text-muted-foreground">75%</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div className="bg-primary h-2 rounded-full transition-all duration-300" style={{width: '75%'}}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Storage Used</span>
                        <span className="text-muted-foreground">45%</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div className="bg-blue-500 h-2 rounded-full transition-all duration-300" style={{width: '45%'}}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Memory Usage</span>
                        <span className="text-red-600">92%</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div className="bg-red-500 h-2 rounded-full transition-all duration-300" style={{width: '92%'}}></div>
                      </div>
                    </div>
                  </div>

                  {/* Loading States */}
                  <div className="space-y-4">
                    <h4 className="font-medium">Loading States</h4>

                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                        <span className="text-sm">Loading...</span>
                      </div>

                      <div className="space-y-2">
                        <div className="h-4 bg-muted rounded animate-pulse"></div>
                        <div className="h-4 bg-muted rounded animate-pulse w-3/4"></div>
                        <div className="h-4 bg-muted rounded animate-pulse w-1/2"></div>
                      </div>

                      <div className="flex items-center gap-2">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                        </div>
                        <span className="text-sm">Processing...</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Empty States */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Empty States</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                  <div className="p-8 border-2 border-dashed border-muted rounded-lg text-center">
                    <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No items found</h3>
                    <p className="text-muted-foreground mb-4">Get started by creating your first item.</p>
                    <button className="group relative inline-flex items-center overflow-hidden rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                      <span className="absolute -start-full transition-all group-hover:start-2">
                        <PlusCircle className="size-4" />
                      </span>
                      <span className="text-sm font-medium transition-all group-hover:ms-2">Create Item</span>
                    </button>
                  </div>

                  <div className="p-8 border-2 border-dashed border-muted rounded-lg text-center">
                    <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No results found</h3>
                    <p className="text-muted-foreground mb-4">Try adjusting your search or filter criteria.</p>
                    <button className="group relative inline-flex items-center overflow-hidden rounded-md border border-input px-4 py-2 text-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden">
                      <span className="absolute -start-full transition-all group-hover:start-2">
                        <RefreshCw className="size-4" />
                      </span>
                      <span className="text-sm font-medium transition-all group-hover:ms-2">Clear Filters</span>
                    </button>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-semibold mb-2">Data Display Features:</h4>
                <ul className="text-sm space-y-1">
                  <li>• <strong>Enhanced buttons</strong>: Sliding icon animations in empty state actions</li>
                  <li>• <strong>Status indicators</strong>: Color-coded badges with consistent styling</li>
                  <li>• <strong>Progress visualization</strong>: Animated progress bars with smooth transitions</li>
                  <li>• <strong>Loading states</strong>: Multiple loading patterns for different contexts</li>
                  <li>• <strong>Empty states</strong>: Helpful guidance when no data is available</li>
                  <li>• <strong>Priority indicators</strong>: Visual hierarchy with color and icons</li>
                  <li>• <strong>Theme integration</strong>: All components adapt to current theme</li>
                  <li>• <strong>Accessibility</strong>: Proper contrast ratios and screen reader support</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="fixes" className="space-y-6">
          {/* UI Consistency Fixes */}
          <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
            <CardHeader>
              <CardTitle className="text-green-800 dark:text-green-200">
                🔧 UI Consistency Fixes (NEW)
              </CardTitle>
              <CardDescription className="text-green-700 dark:text-green-300">
                Centralized system that fixes sidebar alignment, button behaviors, input consistency, and theme issues
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Sidebar Alignment Demo */}
              <div>
                <h3 className="text-lg font-semibold mb-4 text-green-800 dark:text-green-200">
                  ✅ Sidebar Alignment Fixed
                </h3>
                <div className="p-4 border border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950 rounded-lg">
                  <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">🎯 Root Cause Found & Fixed</h4>
                  <p className="text-sm text-green-700 dark:text-green-300 mb-3">
                    The issue was <strong>Framer Motion</strong> applying inline styles that overrode CSS.
                    Inline styles have the highest specificity (1000) and override all CSS rules including <code>!important</code>.
                  </p>
                  <div className="space-y-2">
                    <div className="text-xs font-mono bg-green-100 dark:bg-green-900 p-2 rounded">
                      Fixed: Added <code>style={`{ display: 'contents' }`}</code> to motion wrappers
                    </div>
                    <div className="text-xs text-green-600 dark:text-green-400">
                      ↳ This prevents Framer Motion from interfering with flexbox layout
                    </div>
                    <div className="text-xs font-mono bg-green-100 dark:bg-green-900 p-2 rounded mt-2">
                      Also: button:not([data-sidebar]) {`{ justify-content: center !important; }`}
                    </div>
                    <div className="text-xs text-green-600 dark:text-green-400">
                      ↳ Excluded sidebar buttons from global center alignment
                    </div>
                  </div>
                </div>

                <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 border border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950 rounded-lg">
                    <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">❌ Current Issue</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-center gap-2 p-2 bg-white dark:bg-gray-800 rounded border">
                        <Settings className="h-4 w-4" />
                        <span>Settings (centered)</span>
                      </div>
                      <div className="flex items-center justify-center gap-2 p-2 bg-white dark:bg-gray-800 rounded border">
                        <User className="h-4 w-4" />
                        <span>Profile (centered)</span>
                      </div>
                    </div>
                  </div>
                  <div className="p-4 border border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950 rounded-lg">
                    <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">✅ Target Result</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-start gap-2 p-2 bg-white dark:bg-gray-800 rounded border">
                        <Settings className="h-4 w-4" />
                        <span>Settings (left-aligned)</span>
                      </div>
                      <div className="flex items-center justify-start gap-2 p-2 bg-white dark:bg-gray-800 rounded border">
                        <User className="h-4 w-4" />
                        <span>Profile (left-aligned)</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Button Consistency Demo */}
              <div>
                <h3 className="text-lg font-semibold mb-4 text-green-800 dark:text-green-200">
                  ✅ Enhanced Buttons with Sliding Animation Fixed
                </h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Enhanced Button Variants (Now Working in Tasks Page)</h4>
                    <div className="flex flex-wrap gap-2">
                      <CentralizedButton variant="default">Default</CentralizedButton>
                      <CentralizedButton variant="secondary">Secondary</CentralizedButton>
                      <CentralizedButton variant="outline">Outline</CentralizedButton>
                      <CentralizedButton variant="ghost">Ghost</CentralizedButton>
                      <CentralizedButton variant="destructive">Destructive</CentralizedButton>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Sliding Icon Animation (Like in Tasks Page)</h4>
                    <div className="flex flex-wrap gap-2">
                      <button className="group relative inline-flex items-center overflow-hidden rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                        <span className="absolute -start-full transition-all group-hover:start-2">
                          <PlusCircle className="size-4" />
                        </span>
                        <span className="text-sm font-medium transition-all group-hover:ms-2">Create Task</span>
                      </button>

                      <button className="group relative inline-flex items-center overflow-hidden rounded-md border border-purple-600 px-4 py-2 text-purple-600 hover:bg-purple-50 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden">
                        <span className="absolute -start-full transition-all group-hover:start-2">
                          <Bot className="size-4" />
                        </span>
                        <span className="text-sm font-medium transition-all group-hover:ms-2">AI Create</span>
                      </button>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Button Content Alignment</h4>
                    <div className="space-y-3">
                      <div className="text-xs text-muted-foreground p-2 bg-blue-50 dark:bg-blue-950 rounded border">
                        💡 <strong>Important:</strong> This controls how content is aligned INSIDE each button, not the button's position on the page.
                      </div>

                      <div className="space-y-2">
                        <div className="text-xs font-medium text-muted-foreground">alignment="left" - Icon and text aligned to left edge:</div>
                        <CentralizedButton alignment="left" fullWidth icon={<PlusCircle className="h-4 w-4" />}>
                          Content starts from left edge
                        </CentralizedButton>
                      </div>

                      <div className="space-y-2">
                        <div className="text-xs font-medium text-muted-foreground">alignment="center" - Icon and text centered (default):</div>
                        <CentralizedButton alignment="center" fullWidth icon={<Settings className="h-4 w-4" />}>
                          Content centered in button
                        </CentralizedButton>
                      </div>

                      <div className="space-y-2">
                        <div className="text-xs font-medium text-muted-foreground">alignment="right" - Icon and text aligned to right edge:</div>
                        <CentralizedButton alignment="right" fullWidth>
                          Content aligned to right edge
                          <ArrowRight className="h-4 w-4" />
                        </CentralizedButton>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Input Consistency Demo */}
              <div>
                <h3 className="text-lg font-semibold mb-4 text-green-800 dark:text-green-200">
                  ✅ Input Field Consistency Fixed
                </h3>
                <CentralizedGrid cols={2} gap="md">
                  <CentralizedInput
                    label="Standard Input"
                    placeholder="Consistent styling"
                    description="All inputs now have uniform height and padding"
                  />
                  <CentralizedInput
                    label="Input with Icon"
                    placeholder="Search..."
                    prefixIcon={<Search className="h-4 w-4" />}
                  />
                  <CentralizedInput
                    label="Success State"
                    placeholder="Valid input"
                    success="This field is valid!"
                    suffixIcon={<CheckCircle className="h-4 w-4 text-green-500" />}
                  />
                  <CentralizedInput
                    label="Error State"
                    placeholder="Invalid input"
                    error="This field is required"
                    suffixIcon={<X className="h-4 w-4 text-red-500" />}
                  />
                </CentralizedGrid>
              </div>

              {/* Layout System Demo */}
              <div>
                <h3 className="text-lg font-semibold mb-4 text-green-800 dark:text-green-200">
                  ✅ Layout System Standardized
                </h3>
                <CentralizedCard
                  title="Centralized Card Component"
                  description="Consistent card layouts with proper spacing"
                  variant="elevated"
                  actions={
                    <div className="flex items-center gap-2">
                      <CentralizedButton size="sm" variant="ghost">
                        <Settings className="h-4 w-4" />
                      </CentralizedButton>
                      <CentralizedButton size="sm" variant="outline">
                        Edit
                      </CentralizedButton>
                    </div>
                  }
                >
                  <p className="text-sm text-muted-foreground">
                    This card uses the centralized component system with consistent spacing,
                    typography, and interaction patterns.
                  </p>
                  <CentralizedGrid cols={3} gap="sm" className="mt-4">
                    {Array.from({ length: 3 }).map((_, i) => (
                      <div key={i} className="h-16 bg-muted rounded-md flex items-center justify-center text-sm">
                        Item {i + 1}
                      </div>
                    ))}
                  </CentralizedGrid>
                </CentralizedCard>
              </div>

              {/* Technical Implementation */}
              <div className="p-4 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg">
                <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">🔧 Technical Implementation</h4>
                <ul className="text-sm space-y-1 text-blue-700 dark:text-blue-300">
                  <li>• <strong>Sidebar Text Alignment</strong>: Fixed flexbox layout where text was centered between icon and chevron</li>
                  <li>• <strong>Enhanced Button Integration</strong>: Replaced custom buttons with SlidingIconButton component</li>
                  <li>• <strong>Animation Preservation</strong>: Excluded animated buttons from global CSS fixes</li>
                  <li>• <strong>Sliding Icon Animation</strong>: Added icon-slide animation to Enhanced Button component</li>
                  <li>• <strong>CSS Specificity Management</strong>: Used targeted selectors to avoid breaking existing animations</li>
                  <li>• <strong>Component Consistency</strong>: Tasks page now uses Enhanced Buttons with proper animations</li>
                  <li>• <strong>Flexbox Layout Fix</strong>: Sidebar spans now use flex: none to prevent centering</li>
                  <li>• <strong>Animation System</strong>: Preserved existing hover effects while fixing layout issues</li>
                </ul>
              </div>

              <div className="p-4 bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">📚 Usage</h4>
                <p className="text-sm text-yellow-700 dark:text-yellow-300 mb-2">
                  Use the centralized components for new development:
                </p>
                <pre className="text-xs bg-yellow-100 dark:bg-yellow-900 p-2 rounded border overflow-x-auto">
{`import {
  CentralizedButton,
  CentralizedInput,
  CentralizedCard
} from "@/components/ui/centralized-components";

<CentralizedButton variant="default" alignment="left">
  Consistent Button
</CentralizedButton>`}
                </pre>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
