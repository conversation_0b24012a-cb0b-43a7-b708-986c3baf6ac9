"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';

// Import UX & Accessibility components
import { AccessibleButton, AccessibleButtonGroup, FloatingActionButton } from '@/components/ui/accessible-button';
import { AccessibleForm, AccessibleFormField, AccessibleFormSection, FormMessage } from '@/components/ui/accessible-form';
import { ThemeProvider, ThemeToggle, ThemeSelector, ThemePreview } from '@/components/providers/theme-provider';
import { I18nProvider, LanguageSelector } from '@/components/providers/i18n-provider';
import { 
  LoadingSpinner, 
  Skeleton, 
  ProgressBar, 
  LoadingOverlay, 
  ActionLoading, 
  StatusIndicator,
  LoadingCard 
} from '@/components/ui/loading-states';
import { 
  HoverScale, 
  FadeIn, 
  StaggerChildren, 
  Bounce, 
  Pulse, 
  Shake, 
  SlideIn, 
  MorphingButton,
  Floating 
} from '@/components/ui/micro-interactions';
import { useBreakpoint, useDeviceType, useTouchDevice, useOrientation } from '@/lib/responsive';

// Import icons
import { 
  Save, 
  Download, 
  Upload, 
  Heart, 
  Star, 
  ThumbsUp, 
  Plus,
  Settings,
  Moon,
  Sun,
  Globe,
  Smartphone,
  Tablet,
  Monitor
} from 'lucide-react';

function UXAccessibilityDemoContent() {
  const { toast } = useToast();
  const breakpoint = useBreakpoint();
  const deviceType = useDeviceType();
  const isTouch = useTouchDevice();
  const orientation = useOrientation();
  
  // Component states
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [showShake, setShowShake] = useState(false);
  const [showBounce, setShowBounce] = useState(false);
  const [isSlideVisible, setIsSlideVisible] = useState(true);
  const [isMorphed, setIsMorphed] = useState(false);

  const handleSubmit = async () => {
    setIsLoading(true);
    setProgress(0);
    
    // Simulate progress
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsLoading(false);
          setShowSuccess(true);
          setTimeout(() => setShowSuccess(false), 3000);
          return 100;
        }
        return prev + 10;
      });
    }, 200);

    toast({
      title: "Form Submitted!",
      description: "Your message has been sent successfully.",
    });
  };

  const triggerError = () => {
    setShowError(true);
    setShowShake(true);
    setTimeout(() => {
      setShowError(false);
      setShowShake(false);
    }, 3000);
  };

  const triggerBounce = () => {
    setShowBounce(true);
    setTimeout(() => setShowBounce(false), 1000);
  };

  const getDeviceIcon = () => {
    switch (deviceType) {
      case 'mobile': return <Smartphone className="h-4 w-4" />;
      case 'tablet': return <Tablet className="h-4 w-4" />;
      case 'desktop': return <Monitor className="h-4 w-4" />;
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">UX & Accessibility Demo</h1>
        <p className="text-muted-foreground">
          Advanced user experience features with WCAG 2.1 AA compliance
        </p>
        <div className="flex justify-center gap-2 flex-wrap">
          <Badge variant="secondary">WCAG 2.1 AA</Badge>
          <Badge variant="secondary">Responsive Design</Badge>
          <Badge variant="secondary">Touch Friendly</Badge>
          <Badge variant="secondary">Micro-interactions</Badge>
          <Badge variant="secondary">Dark/Light Mode</Badge>
          <Badge variant="secondary">Internationalization</Badge>
        </div>
      </div>

      {/* Device Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getDeviceIcon()}
            Device Information
          </CardTitle>
          <CardDescription>Current device and screen information</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium">Device Type</div>
              <div className="text-muted-foreground capitalize">{deviceType}</div>
            </div>
            <div>
              <div className="font-medium">Breakpoint</div>
              <div className="text-muted-foreground uppercase">{breakpoint.current}</div>
            </div>
            <div>
              <div className="font-medium">Touch Device</div>
              <div className="text-muted-foreground">{isTouch ? 'Yes' : 'No'}</div>
            </div>
            <div>
              <div className="font-medium">Orientation</div>
              <div className="text-muted-foreground capitalize">{orientation}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="accessibility" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 lg:grid-cols-5">
          <TabsTrigger value="accessibility">Accessibility</TabsTrigger>
          <TabsTrigger value="theming">Theming</TabsTrigger>
          <TabsTrigger value="loading">Loading States</TabsTrigger>
          <TabsTrigger value="interactions">Micro-interactions</TabsTrigger>
          <TabsTrigger value="responsive">Responsive</TabsTrigger>
        </TabsList>

        <TabsContent value="accessibility" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Accessible Form</CardTitle>
                <CardDescription>WCAG 2.1 AA compliant form with proper ARIA labels</CardDescription>
              </CardHeader>
              <CardContent>
                <AccessibleForm
                  title="Contact Form"
                  description="Send us a message with proper accessibility features"
                  onSubmit={handleSubmit}
                >
                  <AccessibleFormSection
                    title="Personal Information"
                    description="Your contact details"
                  >
                    <AccessibleFormField
                      label="Full Name"
                      name="name"
                      description="Enter your complete name"
                      required
                    >
                      <Input
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="John Doe"
                      />
                    </AccessibleFormField>

                    <AccessibleFormField
                      label="Email Address"
                      name="email"
                      description="We'll use this to respond to your message"
                      required
                    >
                      <Input
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                        placeholder="<EMAIL>"
                      />
                    </AccessibleFormField>
                  </AccessibleFormSection>

                  <AccessibleFormSection
                    title="Message"
                    description="What would you like to tell us?"
                  >
                    <AccessibleFormField
                      label="Your Message"
                      name="message"
                      description="Please provide details about your inquiry"
                      required
                    >
                      <Textarea
                        value={formData.message}
                        onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                        placeholder="Type your message here..."
                        rows={4}
                      />
                    </AccessibleFormField>
                  </AccessibleFormSection>

                  <div className="flex gap-3">
                    <AccessibleButton
                      type="submit"
                      loading={isLoading}
                      loadingText="Sending..."
                      successMessage="Message sent successfully!"
                      announceOnClick
                      announceMessage="Submitting contact form"
                    >
                      <Save className="h-4 w-4" />
                      Send Message
                    </AccessibleButton>
                    
                    <AccessibleButton
                      variant="destructive"
                      onClick={triggerError}
                      errorMessage="This is an error demonstration"
                    >
                      Trigger Error
                    </AccessibleButton>
                  </div>
                </AccessibleForm>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Accessible Buttons</CardTitle>
                <CardDescription>Touch-friendly buttons with proper focus management</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <h4 className="font-medium">Button Variants</h4>
                  <div className="flex flex-wrap gap-2">
                    <AccessibleButton>Default</AccessibleButton>
                    <AccessibleButton variant="secondary">Secondary</AccessibleButton>
                    <AccessibleButton variant="outline">Outline</AccessibleButton>
                    <AccessibleButton variant="ghost">Ghost</AccessibleButton>
                    <AccessibleButton variant="link">Link</AccessibleButton>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium">Button Sizes</h4>
                  <div className="flex flex-wrap items-center gap-2">
                    <AccessibleButton size="sm">Small</AccessibleButton>
                    <AccessibleButton size="default">Default</AccessibleButton>
                    <AccessibleButton size="lg">Large</AccessibleButton>
                    <AccessibleButton size="icon">
                      <Settings className="h-4 w-4" />
                    </AccessibleButton>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium">Button Group</h4>
                  <AccessibleButtonGroup>
                    <AccessibleButton variant="outline">
                      <Download className="h-4 w-4" />
                      Download
                    </AccessibleButton>
                    <AccessibleButton variant="outline">
                      <Upload className="h-4 w-4" />
                      Upload
                    </AccessibleButton>
                    <AccessibleButton variant="outline">
                      <Save className="h-4 w-4" />
                      Save
                    </AccessibleButton>
                  </AccessibleButtonGroup>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium">Floating Action Button</h4>
                  <div className="relative h-20 bg-muted rounded-lg">
                    <FloatingActionButton
                      label="Add new item"
                      position="bottom-right"
                      className="relative bottom-2 right-2"
                    >
                      <Plus className="h-6 w-6" />
                    </FloatingActionButton>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Status Messages */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormMessage type="success" title="Success">
              Your form has been submitted successfully!
            </FormMessage>
            <FormMessage type="error" title="Error">
              There was an error processing your request.
            </FormMessage>
            <FormMessage type="warning" title="Warning">
              Please review your information before submitting.
            </FormMessage>
            <FormMessage type="info" title="Information">
              This form uses advanced accessibility features.
            </FormMessage>
          </div>
        </TabsContent>

        <TabsContent value="theming" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sun className="h-5 w-5" />
                  Theme Controls
                </CardTitle>
                <CardDescription>Switch between light and dark modes</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4">
                  <span className="text-sm font-medium">Theme Mode:</span>
                  <ThemeToggle />
                </div>
                
                <ThemeSelector />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Theme Previews</CardTitle>
                <CardDescription>Preview different theme variants</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  <ThemePreview variant="default" mode="light" />
                  <ThemePreview variant="corporate" mode="light" />
                  <ThemePreview variant="modern" mode="dark" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="loading" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Loading Spinners</CardTitle>
                <CardDescription>Different sizes and colors</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4">
                  <LoadingSpinner size="sm" />
                  <LoadingSpinner size="md" />
                  <LoadingSpinner size="lg" />
                  <LoadingSpinner size="xl" />
                </div>
                <div className="flex items-center gap-4">
                  <LoadingSpinner color="primary" />
                  <LoadingSpinner color="secondary" />
                  <LoadingSpinner color="muted" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Skeleton Loaders</CardTitle>
                <CardDescription>Content placeholders</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton variant="text" lines={3} />
                <div className="flex items-center gap-3">
                  <Skeleton variant="circular" width={40} height={40} />
                  <div className="flex-1">
                    <Skeleton variant="text" width="60%" />
                    <Skeleton variant="text" width="40%" />
                  </div>
                </div>
                <Skeleton variant="rectangular" height={100} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Progress Bars</CardTitle>
                <CardDescription>Progress indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ProgressBar value={progress} showLabel label="Upload Progress" />
                <ProgressBar value={75} variant="success" showLabel label="Success" />
                <ProgressBar value={45} variant="warning" showLabel label="Warning" />
                <ProgressBar value={25} variant="error" showLabel label="Error" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Action Loading</CardTitle>
                <CardDescription>Contextual loading states</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <ActionLoading action="saving" />
                <ActionLoading action="uploading" />
                <ActionLoading action="downloading" />
                <ActionLoading action="searching" />
                <ActionLoading action="processing" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Status Indicators</CardTitle>
                <CardDescription>Status feedback</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <StatusIndicator status="loading" message="Processing..." />
                <StatusIndicator status="success" message="Completed successfully" />
                <StatusIndicator status="error" message="Something went wrong" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Loading Overlay</CardTitle>
                <CardDescription>Overlay loading state</CardDescription>
              </CardHeader>
              <CardContent>
                <LoadingOverlay isLoading={isLoading} message="Submitting form...">
                  <div className="h-32 bg-muted rounded-lg flex items-center justify-center">
                    <p className="text-muted-foreground">Content behind overlay</p>
                  </div>
                </LoadingOverlay>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="interactions" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Hover Effects</CardTitle>
                <CardDescription>Interactive hover animations</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <HoverScale>
                  <div className="p-4 bg-primary text-primary-foreground rounded-lg text-center">
                    Hover to scale
                  </div>
                </HoverScale>
                
                <div className="flex gap-2">
                  <HoverScale>
                    <AccessibleButton variant="outline">
                      <Heart className="h-4 w-4" />
                    </AccessibleButton>
                  </HoverScale>
                  <HoverScale>
                    <AccessibleButton variant="outline">
                      <Star className="h-4 w-4" />
                    </AccessibleButton>
                  </HoverScale>
                  <HoverScale>
                    <AccessibleButton variant="outline">
                      <ThumbsUp className="h-4 w-4" />
                    </AccessibleButton>
                  </HoverScale>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Fade Animations</CardTitle>
                <CardDescription>Smooth fade in effects</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <StaggerChildren staggerDelay={150}>
                  <div className="p-3 bg-muted rounded">Item 1</div>
                  <div className="p-3 bg-muted rounded">Item 2</div>
                  <div className="p-3 bg-muted rounded">Item 3</div>
                  <div className="p-3 bg-muted rounded">Item 4</div>
                </StaggerChildren>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Action Animations</CardTitle>
                <CardDescription>Trigger-based animations</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <AccessibleButton onClick={triggerBounce}>
                    Trigger Bounce
                  </AccessibleButton>
                  <AccessibleButton onClick={() => setIsSlideVisible(!isSlideVisible)}>
                    Toggle Slide
                  </AccessibleButton>
                </div>
                
                <Bounce trigger={showBounce}>
                  <div className="p-4 bg-green-100 dark:bg-green-900 rounded-lg text-center">
                    Bouncing element
                  </div>
                </Bounce>
                
                <Shake trigger={showShake}>
                  <div className="p-4 bg-red-100 dark:bg-red-900 rounded-lg text-center">
                    Shaking element
                  </div>
                </Shake>
                
                <SlideIn direction="left" isVisible={isSlideVisible}>
                  <div className="p-4 bg-blue-100 dark:bg-blue-900 rounded-lg text-center">
                    Sliding element
                  </div>
                </SlideIn>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Morphing Button</CardTitle>
                <CardDescription>Button that transforms</CardDescription>
              </CardHeader>
              <CardContent>
                <MorphingButton
                  trigger={isMorphed}
                  onClick={() => setIsMorphed(!isMorphed)}
                  morphTo={
                    <div className="flex items-center gap-2">
                      <LoadingSpinner size="sm" />
                      Processing...
                    </div>
                  }
                >
                  <div className="flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    Save Changes
                  </div>
                </MorphingButton>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Floating Animation</CardTitle>
                <CardDescription>Subtle floating effect</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-center">
                  <Floating intensity="normal">
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-white font-bold">
                      Float
                    </div>
                  </Floating>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Pulse Effect</CardTitle>
                <CardDescription>Attention-grabbing pulse</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-center">
                  <Pulse active={true}>
                    <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                  </Pulse>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="responsive" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Responsive Grid</CardTitle>
                <CardDescription>Adaptive layout based on screen size</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <div key={i} className="p-4 bg-muted rounded-lg text-center">
                      Item {i + 1}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Loading Cards</CardTitle>
                <CardDescription>Skeleton loading for cards</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <LoadingCard showAvatar lines={2} />
                <LoadingCard showAvatar={false} lines={3} />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default function UXAccessibilityDemo() {
  return (
    <ThemeProvider defaultMode="dark" defaultVariant="professional">
      <I18nProvider defaultLanguage="en">
        <UXAccessibilityDemoContent />
      </I18nProvider>
    </ThemeProvider>
  );
}
