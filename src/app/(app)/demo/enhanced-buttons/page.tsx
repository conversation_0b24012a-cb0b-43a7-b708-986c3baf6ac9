"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  PlusCircle, 
  Download, 
  Upload, 
  Save, 
  Edit, 
  Trash2, 
  Send, 
  Star, 
  Heart,
  Zap,
  Sparkles,
  ArrowRight,
  Check,
  X,
  RefreshCw,
  Play,
  Pause,
  Settings,
  Filter,
  Search,
  Bell,
  User,
  Calendar,
  FileText,
  Database,
  Shield,
  Globe,
  Rocket,
  Target,
  TrendingUp,
  Award,
  Crown,
  Gem
} from 'lucide-react';

// Enhanced Button Components
const AnimatedButton = ({ 
  children, 
  variant = "default", 
  size = "default", 
  className = "", 
  animation = "hover-lift",
  icon: Icon,
  ...props 
}: any) => {
  const animations = {
    "hover-lift": "transition-all duration-200 hover:scale-105 hover:shadow-lg active:scale-95",
    "hover-glow": "transition-all duration-300 hover:shadow-lg hover:shadow-primary/25 hover:scale-102",
    "hover-slide": "transition-all duration-200 hover:translate-x-1 hover:shadow-md",
    "hover-bounce": "transition-all duration-200 hover:animate-bounce hover:shadow-lg",
    "hover-pulse": "transition-all duration-200 hover:animate-pulse hover:shadow-lg",
    "hover-spin": "transition-all duration-200 hover:rotate-3 hover:shadow-lg",
    "gradient-shift": "bg-gradient-to-r from-primary to-primary/80 hover:from-primary/80 hover:to-primary transition-all duration-300 hover:shadow-lg",
    "neon-glow": "relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:shadow-primary/50 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/10 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-700",
    "morphing": "transition-all duration-300 hover:rounded-full hover:px-8 hover:shadow-lg",
    "elastic": "transition-all duration-300 hover:scale-110 active:scale-90 hover:shadow-xl"
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={`${animations[animation]} ${className}`}
      {...props}
    >
      {Icon && <Icon className="mr-2 h-4 w-4" />}
      {children}
    </Button>
  );
};

const GlowButton = ({ children, color = "blue", ...props }: any) => {
  const colors = {
    blue: "bg-blue-500 hover:bg-blue-600 shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40",
    green: "bg-green-500 hover:bg-green-600 shadow-lg shadow-green-500/25 hover:shadow-green-500/40",
    purple: "bg-purple-500 hover:bg-purple-600 shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40",
    pink: "bg-pink-500 hover:bg-pink-600 shadow-lg shadow-pink-500/25 hover:shadow-pink-500/40",
    orange: "bg-orange-500 hover:bg-orange-600 shadow-lg shadow-orange-500/25 hover:shadow-orange-500/40",
    red: "bg-red-500 hover:bg-red-600 shadow-lg shadow-red-500/25 hover:shadow-red-500/40"
  };

  return (
    <button
      className={`px-6 py-3 rounded-lg text-white font-medium transition-all duration-300 transform hover:scale-105 ${colors[color]}`}
      {...props}
    >
      {children}
    </button>
  );
};

const NeumorphismButton = ({ children, ...props }: any) => {
  return (
    <button
      className="px-6 py-3 bg-gray-100 dark:bg-gray-800 rounded-xl shadow-[inset_-2px_-2px_6px_rgba(255,255,255,0.7),inset_2px_2px_6px_rgba(0,0,0,0.15)] hover:shadow-[inset_2px_2px_6px_rgba(255,255,255,0.7),inset_-2px_-2px_6px_rgba(0,0,0,0.15)] transition-all duration-200 active:scale-95 font-medium text-gray-700 dark:text-gray-300"
      {...props}
    >
      {children}
    </button>
  );
};

const GradientButton = ({ children, gradient = "sunset", ...props }: any) => {
  const gradients = {
    sunset: "bg-gradient-to-r from-orange-400 via-red-500 to-pink-500",
    ocean: "bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600",
    forest: "bg-gradient-to-r from-green-400 via-green-500 to-emerald-600",
    royal: "bg-gradient-to-r from-purple-400 via-purple-500 to-indigo-600",
    gold: "bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500",
    cyber: "bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600"
  };

  return (
    <button
      className={`px-6 py-3 ${gradients[gradient]} text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 hover:rotate-1 active:scale-95`}
      {...props}
    >
      {children}
    </button>
  );
};

export default function EnhancedButtonsDemo() {
  const [activeDemo, setActiveDemo] = useState("animations");

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
          Enhanced Button Showcase
        </h1>
        <p className="text-muted-foreground text-lg">
          Choose your preferred button style for EVEXA standardization
        </p>
      </div>

      <Tabs value={activeDemo} onValueChange={setActiveDemo} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="animations">Hover Animations</TabsTrigger>
          <TabsTrigger value="glow">Glow Effects</TabsTrigger>
          <TabsTrigger value="gradients">Gradients</TabsTrigger>
          <TabsTrigger value="neumorphism">Neumorphism</TabsTrigger>
          <TabsTrigger value="combinations">Combinations</TabsTrigger>
        </TabsList>

        <TabsContent value="animations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Hover Animation Styles</CardTitle>
              <CardDescription>Different animation effects on hover</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Badge variant="secondary">Hover Lift</Badge>
                  <AnimatedButton animation="hover-lift" icon={PlusCircle}>
                    Create Task
                  </AnimatedButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Hover Glow</Badge>
                  <AnimatedButton animation="hover-glow" icon={Save}>
                    Save Changes
                  </AnimatedButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Hover Slide</Badge>
                  <AnimatedButton animation="hover-slide" icon={ArrowRight}>
                    Continue
                  </AnimatedButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Hover Bounce</Badge>
                  <AnimatedButton animation="hover-bounce" icon={Star}>
                    Favorite
                  </AnimatedButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Hover Pulse</Badge>
                  <AnimatedButton animation="hover-pulse" icon={Bell}>
                    Notify
                  </AnimatedButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Hover Spin</Badge>
                  <AnimatedButton animation="hover-spin" icon={RefreshCw}>
                    Refresh
                  </AnimatedButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Gradient Shift</Badge>
                  <AnimatedButton animation="gradient-shift" icon={Zap}>
                    Quick Action
                  </AnimatedButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Neon Glow</Badge>
                  <AnimatedButton animation="neon-glow" icon={Sparkles}>
                    Premium
                  </AnimatedButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Morphing</Badge>
                  <AnimatedButton animation="morphing" icon={Settings}>
                    Settings
                  </AnimatedButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Elastic</Badge>
                  <AnimatedButton animation="elastic" icon={Rocket}>
                    Launch
                  </AnimatedButton>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="glow" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Glow Effect Buttons</CardTitle>
              <CardDescription>Buttons with colored glow effects</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Badge variant="secondary">Blue Glow</Badge>
                  <GlowButton color="blue">
                    <Database className="mr-2 h-4 w-4" />
                    Database
                  </GlowButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Green Glow</Badge>
                  <GlowButton color="green">
                    <Check className="mr-2 h-4 w-4" />
                    Success
                  </GlowButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Purple Glow</Badge>
                  <GlowButton color="purple">
                    <Crown className="mr-2 h-4 w-4" />
                    Premium
                  </GlowButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Pink Glow</Badge>
                  <GlowButton color="pink">
                    <Heart className="mr-2 h-4 w-4" />
                    Favorite
                  </GlowButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Orange Glow</Badge>
                  <GlowButton color="orange">
                    <Award className="mr-2 h-4 w-4" />
                    Achievement
                  </GlowButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Red Glow</Badge>
                  <GlowButton color="red">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </GlowButton>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="gradients" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Gradient Buttons</CardTitle>
              <CardDescription>Beautiful gradient color combinations</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Badge variant="secondary">Sunset</Badge>
                  <GradientButton gradient="sunset">
                    <Upload className="mr-2 h-4 w-4" />
                    Upload
                  </GradientButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Ocean</Badge>
                  <GradientButton gradient="ocean">
                    <Globe className="mr-2 h-4 w-4" />
                    Global
                  </GradientButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Forest</Badge>
                  <GradientButton gradient="forest">
                    <TrendingUp className="mr-2 h-4 w-4" />
                    Growth
                  </GradientButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Royal</Badge>
                  <GradientButton gradient="royal">
                    <Shield className="mr-2 h-4 w-4" />
                    Security
                  </GradientButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Gold</Badge>
                  <GradientButton gradient="gold">
                    <Gem className="mr-2 h-4 w-4" />
                    Premium
                  </GradientButton>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">Cyber</Badge>
                  <GradientButton gradient="cyber">
                    <Target className="mr-2 h-4 w-4" />
                    Target
                  </GradientButton>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="neumorphism" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Neumorphism Style</CardTitle>
              <CardDescription>Soft, modern neumorphic design</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <NeumorphismButton>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </NeumorphismButton>
                
                <NeumorphismButton>
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </NeumorphismButton>
                
                <NeumorphismButton>
                  <Send className="mr-2 h-4 w-4" />
                  Send
                </NeumorphismButton>
                
                <NeumorphismButton>
                  <Filter className="mr-2 h-4 w-4" />
                  Filter
                </NeumorphismButton>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="combinations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Combined Effects</CardTitle>
              <CardDescription>Multiple effects combined for maximum impact</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold">Primary Actions</h4>
                  <div className="space-y-3">
                    <button className="w-full px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium rounded-lg shadow-lg hover:shadow-xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 active:scale-95 flex items-center justify-center">
                      <PlusCircle className="mr-2 h-5 w-5" />
                      Create New Task
                    </button>
                    
                    <button className="w-full px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-medium rounded-lg shadow-lg hover:shadow-xl hover:shadow-green-500/25 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 active:scale-95 flex items-center justify-center">
                      <Save className="mr-2 h-5 w-5" />
                      Save Changes
                    </button>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-semibold">Secondary Actions</h4>
                  <div className="space-y-3">
                    <button className="w-full px-6 py-3 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 font-medium rounded-lg shadow-sm hover:shadow-lg hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-300 transform hover:scale-102 active:scale-98 flex items-center justify-center">
                      <Search className="mr-2 h-5 w-5" />
                      Search Tasks
                    </button>
                    
                    <button className="w-full px-6 py-3 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 font-medium rounded-lg shadow-sm hover:shadow-lg hover:border-orange-300 dark:hover:border-orange-600 transition-all duration-300 transform hover:scale-102 active:scale-98 flex items-center justify-center">
                      <Filter className="mr-2 h-5 w-5" />
                      Filter Results
                    </button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Recommendation</CardTitle>
          <CardDescription>Based on EVEXA's professional design requirements</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              For EVEXA's professional exhibition management platform, I recommend:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border rounded-lg space-y-2">
                <h4 className="font-semibold text-green-600">✅ Primary Actions</h4>
                <p className="text-sm text-muted-foreground">Hover Lift + Subtle Glow</p>
                <AnimatedButton animation="hover-glow" icon={PlusCircle} className="w-full">
                  Create Task
                </AnimatedButton>
              </div>
              <div className="p-4 border rounded-lg space-y-2">
                <h4 className="font-semibold text-blue-600">✅ Secondary Actions</h4>
                <p className="text-sm text-muted-foreground">Hover Lift + Border Highlight</p>
                <AnimatedButton animation="hover-lift" variant="outline" icon={Filter} className="w-full">
                  Filter
                </AnimatedButton>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
