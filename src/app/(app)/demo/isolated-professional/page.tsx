"use client";

import React from 'react';

export default function IsolatedProfessionalTheme() {
  React.useEffect(() => {
    // Create completely isolated professional theme
    const style = document.createElement('style');
    style.textContent = `
      .isolated-professional {
        /* Reset and override everything */
        --background: 0 0% 8% !important;
        --foreground: 0 0% 95% !important;
        --primary: 60 100% 50% !important;
        --primary-foreground: 0 0% 0% !important;
        --secondary: 45 80% 40% !important;
        --secondary-foreground: 0 0% 0% !important;
        --accent: 51 100% 60% !important;
        --accent-foreground: 0 0% 0% !important;
        --card: 0 0% 12% !important;
        --card-foreground: 0 0% 95% !important;
        --muted: 0 0% 15% !important;
        --muted-foreground: 45 20% 70% !important;
        --border: 45 30% 25% !important;
        --input: 0 0% 15% !important;
        --ring: 60 100% 50% !important;
        
        /* Force colors directly */
        background-color: hsl(0 0% 8%) !important;
        color: hsl(0 0% 95%) !important;
      }
      
      .isolated-professional * {
        border-color: hsl(45 30% 25%) !important;
      }
      
      .isolated-professional .btn-primary {
        background-color: hsl(60 100% 50%) !important;
        color: hsl(0 0% 0%) !important;
        border: none !important;
        padding: 0.5rem 1rem !important;
        border-radius: 0.375rem !important;
        font-weight: 600 !important;
        cursor: pointer !important;
      }
      
      .isolated-professional .btn-primary:hover {
        background-color: hsl(51 100% 45%) !important;
      }
      
      .isolated-professional .card {
        background-color: hsl(0 0% 12%) !important;
        color: hsl(0 0% 95%) !important;
        border: 1px solid hsl(45 30% 25%) !important;
        border-radius: 0.5rem !important;
        padding: 1.5rem !important;
        margin: 1rem 0 !important;
      }
      
      .isolated-professional .card-title {
        color: hsl(60 100% 50%) !important;
        font-size: 1.25rem !important;
        font-weight: 600 !important;
        margin-bottom: 0.5rem !important;
      }
      
      .isolated-professional .badge {
        background-color: hsl(60 100% 50%) !important;
        color: hsl(0 0% 0%) !important;
        padding: 0.25rem 0.75rem !important;
        border-radius: 9999px !important;
        font-size: 0.75rem !important;
        font-weight: 500 !important;
      }
      
      .isolated-professional .text-yellow {
        color: hsl(60 100% 50%) !important;
      }
      
      .isolated-professional .bg-dark {
        background-color: hsl(0 0% 8%) !important;
      }
      
      .isolated-professional .bg-card {
        background-color: hsl(0 0% 12%) !important;
      }
      
      .isolated-professional .bg-yellow {
        background-color: hsl(60 100% 50%) !important;
        color: hsl(0 0% 0%) !important;
      }
      
      .isolated-professional .bg-gold {
        background-color: hsl(51 100% 60%) !important;
        color: hsl(0 0% 0%) !important;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <div className="isolated-professional min-h-screen p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-yellow">
            Isolated Professional Theme Test
          </h1>
          <p className="text-lg">
            This page uses completely isolated CSS to test the professional black & yellow theme
          </p>
          <div className="badge">
            Isolated Professional Theme
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="card">
            <div className="card-title">Primary Yellow Test</div>
            <p className="mb-4">This card should have a dark background with yellow title</p>
            <button className="btn-primary w-full">
              Yellow Primary Button
            </button>
          </div>

          <div className="card">
            <div className="card-title">Background Test</div>
            <p className="mb-4">Dark card background with light text</p>
            <div className="bg-yellow p-3 rounded text-center">
              Yellow Background
            </div>
          </div>

          <div className="card">
            <div className="card-title">Color Palette</div>
            <div className="space-y-2">
              <div className="bg-dark p-2 rounded">Dark Background</div>
              <div className="bg-card p-2 rounded">Card Background</div>
              <div className="bg-yellow p-2 rounded">Yellow Primary</div>
              <div className="bg-gold p-2 rounded">Gold Accent</div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-title">Expected vs Actual Colors</div>
          <div className="grid grid-cols-2 gap-8">
            <div>
              <h3 className="text-yellow font-semibold mb-4">Expected Professional Colors:</h3>
              <div className="space-y-2">
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 rounded" style={{ backgroundColor: '#FFFF00' }}></div>
                  <span>Primary: #FFFF00 (Bright Yellow)</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 rounded" style={{ backgroundColor: '#141414' }}></div>
                  <span>Background: #141414 (Very Dark)</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 rounded" style={{ backgroundColor: '#FFD700' }}></div>
                  <span>Accent: #FFD700 (Gold)</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 rounded" style={{ backgroundColor: '#1F1F1F' }}></div>
                  <span>Card: #1F1F1F (Dark Gray)</span>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-yellow font-semibold mb-4">Isolated Theme Colors:</h3>
              <div className="space-y-2">
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 rounded bg-yellow"></div>
                  <span>Primary (Should be Yellow)</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 rounded bg-dark border"></div>
                  <span>Background (Should be Dark)</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 rounded bg-gold"></div>
                  <span>Accent (Should be Gold)</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 rounded bg-card border"></div>
                  <span>Card (Should be Dark Gray)</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-title">Success Indicators</div>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-yellow">✓</span>
              <span>Page background is very dark (almost black)</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-yellow">✓</span>
              <span>Primary button is bright yellow with black text</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-yellow">✓</span>
              <span>Card backgrounds are dark gray</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-yellow">✓</span>
              <span>Text is light gray/white on dark backgrounds</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-yellow">✓</span>
              <span>Titles and accents are yellow/gold</span>
            </div>
          </div>
        </div>

        <div className="text-center">
          <p className="text-lg">
            If you see yellow buttons and dark backgrounds, the professional theme is working! 🎉
          </p>
          <p className="text-sm mt-2 opacity-75">
            This isolated test bypasses all existing CSS to show pure professional theme colors.
          </p>
        </div>
      </div>
    </div>
  );
}
