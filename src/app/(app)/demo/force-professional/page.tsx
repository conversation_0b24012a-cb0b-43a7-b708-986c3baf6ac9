"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export default function ForceProfessionalTheme() {
  React.useEffect(() => {
    // Force professional theme colors and classes
    const root = document.documentElement;
    const body = document.body;

    // Remove any existing theme classes first
    root.classList.remove('light', 'dark', 'theme-default', 'theme-corporate', 'theme-modern', 'theme-minimal', 'theme-vibrant', 'theme-professional');
    body.classList.remove('light', 'dark', 'theme-default', 'theme-corporate', 'theme-modern', 'theme-minimal', 'theme-vibrant', 'theme-professional');

    // Add professional theme classes in correct order
    root.classList.add('dark', 'theme-professional');
    body.classList.add('dark', 'theme-professional');

    // Force professional theme colors directly with highest priority
    root.style.setProperty('--background', '0 0% 8%', 'important');
    root.style.setProperty('--foreground', '0 0% 95%', 'important');
    root.style.setProperty('--primary', '60 100% 50%', 'important');
    root.style.setProperty('--primary-foreground', '0 0% 0%', 'important');
    root.style.setProperty('--secondary', '45 80% 40%', 'important');
    root.style.setProperty('--secondary-foreground', '0 0% 0%', 'important');
    root.style.setProperty('--accent', '51 100% 60%', 'important');
    root.style.setProperty('--accent-foreground', '0 0% 0%', 'important');
    root.style.setProperty('--card', '0 0% 12%', 'important');
    root.style.setProperty('--card-foreground', '0 0% 95%', 'important');
    root.style.setProperty('--muted', '0 0% 15%', 'important');
    root.style.setProperty('--muted-foreground', '45 20% 70%', 'important');
    root.style.setProperty('--border', '45 30% 25%', 'important');
    root.style.setProperty('--input', '0 0% 15%', 'important');
    root.style.setProperty('--ring', '60 100% 50%', 'important');

    console.log('Forced professional theme colors and classes');
    console.log('HTML classes:', root.className);
    console.log('Body classes:', body.className);
    console.log('Primary color:', getComputedStyle(root).getPropertyValue('--primary'));
  }, []);

  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto py-8 space-y-8">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold text-primary">Professional Theme Test</h1>
          <p className="text-muted-foreground">
            This page forces professional theme colors directly
          </p>
          <Badge className="bg-primary text-primary-foreground">Forced Professional Theme</Badge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-card text-card-foreground border-border">
            <CardHeader>
              <CardTitle className="text-primary">Primary Color Test</CardTitle>
              <CardDescription>Should be bright yellow</CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full bg-primary text-primary-foreground hover:bg-primary/90">
                Primary Button (Yellow)
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-card text-card-foreground border-border">
            <CardHeader>
              <CardTitle className="text-secondary">Secondary Color Test</CardTitle>
              <CardDescription>Should be golden yellow</CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="secondary" className="w-full bg-secondary text-secondary-foreground">
                Secondary Button (Gold)
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-card text-card-foreground border-border">
            <CardHeader>
              <CardTitle className="text-accent">Accent Color Test</CardTitle>
              <CardDescription>Should be bright gold</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-3 bg-accent text-accent-foreground rounded">
                Accent Background
              </div>
            </CardContent>
          </Card>
        </div>

        <Card className="bg-card text-card-foreground border-border">
          <CardHeader>
            <CardTitle>Color Verification</CardTitle>
            <CardDescription>Check if colors match professional theme</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <div className="w-full h-12 bg-background border border-border rounded"></div>
                <div className="text-xs text-center">Background</div>
              </div>
              <div className="space-y-2">
                <div className="w-full h-12 bg-primary rounded"></div>
                <div className="text-xs text-center">Primary (Yellow)</div>
              </div>
              <div className="space-y-2">
                <div className="w-full h-12 bg-secondary rounded"></div>
                <div className="text-xs text-center">Secondary (Gold)</div>
              </div>
              <div className="space-y-2">
                <div className="w-full h-12 bg-accent rounded"></div>
                <div className="text-xs text-center">Accent (Bright Gold)</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card text-card-foreground border-border">
          <CardHeader>
            <CardTitle>CSS Variables Check</CardTitle>
            <CardDescription>Current CSS variable values</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm font-mono">
              <div>
                <div>--background: {getComputedStyle(document.documentElement).getPropertyValue('--background')}</div>
                <div>--foreground: {getComputedStyle(document.documentElement).getPropertyValue('--foreground')}</div>
                <div>--primary: {getComputedStyle(document.documentElement).getPropertyValue('--primary')}</div>
                <div>--primary-foreground: {getComputedStyle(document.documentElement).getPropertyValue('--primary-foreground')}</div>
              </div>
              <div>
                <div>--card: {getComputedStyle(document.documentElement).getPropertyValue('--card')}</div>
                <div>--muted: {getComputedStyle(document.documentElement).getPropertyValue('--muted')}</div>
                <div>--border: {getComputedStyle(document.documentElement).getPropertyValue('--border')}</div>
                <div>--ring: {getComputedStyle(document.documentElement).getPropertyValue('--ring')}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="text-center">
          <p className="text-muted-foreground">
            If you see yellow buttons and dark backgrounds, the professional theme is working!
          </p>
        </div>
      </div>
    </div>
  );
}
