"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

// Import all standardized components
import { CountrySelector } from '@/components/ui/country-selector';
import { CurrencySelector } from '@/components/ui/currency-selector';
import { DateTimePicker } from '@/components/ui/datetime-picker';
import { FileUpload } from '@/components/ui/file-upload';
import { ModernForm, ModernFormField, ModernFormSection } from '@/components/ui/modern-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export default function StandardizedComponentsDemo() {
  const { toast } = useToast();

  // Form state
  const [formData, setFormData] = useState({
    country: '',
    currency: '',
    startDate: undefined as Date | undefined,
    endDate: undefined as Date | undefined,
    documents: null as File | File[] | null,
    companyName: '',
    description: '',
    industry: '',
    budget: '',
    timezone: 'America/New_York'
  });

  // Demo component states
  const [demoCountry, setDemoCountry] = useState('');
  const [demoCurrency, setDemoCurrency] = useState('');
  const [demoDate, setDemoDate] = useState<Date | undefined>(undefined);
  const [demoFile, setDemoFile] = useState<File | null>(null);

  // Variant demo states
  const [variantCountry1, setVariantCountry1] = useState('');
  const [variantCountry2, setVariantCountry2] = useState('');
  const [variantDate1, setVariantDate1] = useState<Date | undefined>(undefined);
  const [variantDate2, setVariantDate2] = useState<Date | undefined>(undefined);
  const [variantFile1, setVariantFile1] = useState<File | null>(null);
  const [variantFile2, setVariantFile2] = useState<File | null>(null);
  const [variantFile3, setVariantFile3] = useState<File | null>(null);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setLastSaved(new Date());
    
    toast({
      title: "Form Submitted Successfully!",
      description: "All standardized components are working perfectly.",
    });
  };

  const handleFieldChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Standardized Components Demo</h1>
        <p className="text-muted-foreground">
          Modern, sleek, and consistent components across the entire EVEXA application
        </p>
        <div className="flex justify-center gap-2 flex-wrap">
          <Badge variant="secondary">All Countries</Badge>
          <Badge variant="secondary">Multi-Currency</Badge>
          <Badge variant="secondary">Enhanced Search</Badge>
          <Badge variant="secondary">Enhanced Date Picker</Badge>
          <Badge variant="secondary">Modern Forms</Badge>
          <Badge variant="secondary">File Upload</Badge>
          <Badge variant="secondary">Sleek Design</Badge>
        </div>
      </div>

      <Tabs defaultValue="form-demo" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="form-demo">Complete Form</TabsTrigger>
          <TabsTrigger value="components">Individual Components</TabsTrigger>
          <TabsTrigger value="variants">Component Variants</TabsTrigger>
          <TabsTrigger value="integration">Integration Guide</TabsTrigger>
        </TabsList>

        <TabsContent value="form-demo" className="space-y-6">
          <ModernForm
            title="Exhibition Registration Form"
            description="Complete form showcasing all standardized components"
            variant="card"
            showProgress={true}
            progress={65}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
            submitLabel="Register Exhibition"
            showCancel={true}
            autoSave={true}
            lastSaved={lastSaved}
            badges={[
              { label: 'Required', variant: 'destructive' },
              { label: 'Auto-save', variant: 'secondary' }
            ]}
          >
            <ModernFormSection
              title="Basic Information"
              description="Enter the basic details for your exhibition"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ModernFormField
                  label="Company Name"
                  description="Your organization's legal name"
                  required={true}
                >
                  <Input
                    value={formData.companyName}
                    onChange={(e) => handleFieldChange('companyName', e.target.value)}
                    placeholder="Enter company name..."
                  />
                </ModernFormField>

                <ModernFormField
                  label="Industry"
                  description="Select your primary industry"
                  required={true}
                >
                  <Select
                    value={formData.industry}
                    onValueChange={(value) => handleFieldChange('industry', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select industry..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="technology">Technology</SelectItem>
                      <SelectItem value="healthcare">Healthcare</SelectItem>
                      <SelectItem value="finance">Finance</SelectItem>
                      <SelectItem value="manufacturing">Manufacturing</SelectItem>
                      <SelectItem value="retail">Retail</SelectItem>
                      <SelectItem value="education">Education</SelectItem>
                    </SelectContent>
                  </Select>
                </ModernFormField>
              </div>

              <ModernFormField
                label="Description"
                description="Brief description of your exhibition or company"
              >
                <Textarea
                  value={formData.description}
                  onChange={(e) => handleFieldChange('description', e.target.value)}
                  placeholder="Describe your exhibition..."
                  rows={3}
                />
              </ModernFormField>
            </ModernFormSection>

            <ModernFormSection
              title="Location & Currency"
              description="Select your country and preferred currency"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ModernFormField
                  label="Country"
                  description="Select your country of operation"
                  required={true}
                >
                  <CountrySelector
                    value={formData.country}
                    onChange={(value) => handleFieldChange('country', value)}
                    showFlag={true}
                    showDialCode={true}
                    showPopular={true}
                  />
                </ModernFormField>

                <ModernFormField
                  label="Currency"
                  description="Select your preferred currency"
                  required={true}
                >
                  <CurrencySelector
                    value={formData.currency}
                    onChange={(value) => handleFieldChange('currency', value)}
                    showSymbol={true}
                    showFlag={true}
                    showPopular={true}
                  />
                </ModernFormField>
              </div>

              <ModernFormField
                label="Budget Range"
                description="Estimated budget for the exhibition"
              >
                <Input
                  type="number"
                  value={formData.budget}
                  onChange={(e) => handleFieldChange('budget', e.target.value)}
                  placeholder="Enter budget amount..."
                />
              </ModernFormField>
            </ModernFormSection>

            <ModernFormSection
              title="Schedule"
              description="Set your exhibition dates and timezone"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ModernFormField
                  label="Start Date & Time"
                  description="When does your exhibition begin?"
                  required={true}
                >
                  <DateTimePicker
                    value={formData.startDate}
                    onChange={(date) => handleFieldChange('startDate', date)}
                    showTime={true}
                    showTimezone={true}
                    timezone={formData.timezone}
                    showPresets={true}
                    placeholder="Select start date and time..."
                  />
                </ModernFormField>

                <ModernFormField
                  label="End Date & Time"
                  description="When does your exhibition end?"
                  required={true}
                >
                  <DateTimePicker
                    value={formData.endDate}
                    onChange={(date) => handleFieldChange('endDate', date)}
                    showTime={true}
                    showTimezone={true}
                    timezone={formData.timezone}
                    showPresets={true}
                    placeholder="Select end date and time..."
                    minDate={formData.startDate}
                  />
                </ModernFormField>
              </div>
            </ModernFormSection>

            <ModernFormSection
              title="Documents"
              description="Upload supporting documents"
            >
              <ModernFormField
                label="Supporting Documents"
                description="Upload brochures, certificates, or other relevant documents"
              >
                <FileUpload
                  value={formData.documents}
                  onChange={(files) => handleFieldChange('documents', files)}
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  maxSize={10}
                  multiple={true}
                  maxFiles={5}
                  placeholder="Drop your documents here"
                />
              </ModernFormField>
            </ModernFormSection>
          </ModernForm>
        </TabsContent>

        <TabsContent value="components" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Country Selector</CardTitle>
                <CardDescription>All countries with flags and dial codes</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <CountrySelector
                  value={demoCountry}
                  onChange={setDemoCountry}
                  showFlag={true}
                  showDialCode={true}
                  showPopular={true}
                />

                <div className="text-xs text-muted-foreground">
                  Features: 195+ countries, real-time search, flags, dial codes, popular countries
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Currency Selector</CardTitle>
                <CardDescription>Multi-currency support with symbols</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <CurrencySelector
                  value={demoCurrency}
                  onChange={setDemoCurrency}
                  showSymbol={true}
                  showFlag={true}
                  showPopular={true}
                />

                <div className="text-xs text-muted-foreground">
                  Features: 150+ currencies, real-time search, symbols, flags, popular currencies
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Enhanced Date Picker</CardTitle>
                <CardDescription>Modern date/time picker with presets</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <DateTimePicker
                  value={demoDate}
                  onChange={setDemoDate}
                  showTime={true}
                  showTimezone={true}
                  showPresets={true}
                  placeholder="Select date and time..."
                />

                <div className="text-xs text-muted-foreground">
                  Features: Time zones, presets, time selection, modern UI
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>File Upload</CardTitle>
                <CardDescription>Drag & drop with validation</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FileUpload
                  value={demoFile}
                  onChange={(file) => setDemoFile(file as File | null)}
                  accept="image/*"
                  maxSize={5}
                  variant="compact"
                  placeholder="Upload image..."
                />

                <div className="text-xs text-muted-foreground">
                  Features: Drag & drop, validation, previews, multiple files
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="variants" className="space-y-6">
          <div className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Component Variants</CardTitle>
                <CardDescription>Different styles for different use cases</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Country Selector Variants</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Default</label>
                      <CountrySelector
                        value={variantCountry1}
                        onChange={setVariantCountry1}
                        variant="default"
                        showFlag={true}
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Compact</label>
                      <CountrySelector
                        value={variantCountry2}
                        onChange={setVariantCountry2}
                        variant="compact"
                        showFlag={true}
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Date Picker Variants</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">With Presets</label>
                      <DateTimePicker
                        value={variantDate1}
                        onChange={setVariantDate1}
                        showPresets={true}
                        placeholder="Select date..."
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Compact</label>
                      <DateTimePicker
                        value={variantDate2}
                        onChange={setVariantDate2}
                        variant="compact"
                        placeholder="Select date..."
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">File Upload Variants</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Default</label>
                      <FileUpload
                        value={variantFile1}
                        onChange={(file) => setVariantFile1(file as File | null)}
                        variant="default"
                        accept="image/*"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Compact</label>
                      <FileUpload
                        value={variantFile2}
                        onChange={(file) => setVariantFile2(file as File | null)}
                        variant="compact"
                        accept="image/*"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Image Only</label>
                      <FileUpload
                        value={variantFile3}
                        onChange={(file) => setVariantFile3(file as File | null)}
                        variant="image-only"
                        accept="image/*"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="integration" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Integration Guide</CardTitle>
              <CardDescription>How to use these components across EVEXA</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h4 className="font-medium">Implementation Status</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Country Selector</span>
                      <Badge variant="secondary">✅ Ready</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Currency Selector</span>
                      <Badge variant="secondary">✅ Ready</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Enhanced Date Picker</span>
                      <Badge variant="secondary">✅ Ready</Badge>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">File Upload</span>
                      <Badge variant="secondary">✅ Ready</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Modern Forms</span>
                      <Badge variant="secondary">✅ Ready</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Smart Form Integration</span>
                      <Badge variant="secondary">✅ Ready</Badge>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">Next Steps</h4>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <p>1. ✅ Replace all country dropdowns with CountrySelector</p>
                  <p>2. ✅ Replace all currency fields with CurrencySelector</p>
                  <p>3. ✅ Replace all date pickers with enhanced DateTimePicker</p>
                  <p>4. ✅ Replace all file uploads with standardized FileUpload</p>
                  <p>5. ✅ Wrap all forms with ModernForm components</p>
                  <p>6. 🔄 Update all existing forms across modules</p>
                  <p>7. 🔄 Test all components in production scenarios</p>
                </div>
              </div>

              <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Ready for Implementation</h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  All standardized components are production-ready and can be implemented across 
                  the entire EVEXA application. They provide consistent styling, behavior, and 
                  user experience while maintaining high performance and accessibility standards.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
