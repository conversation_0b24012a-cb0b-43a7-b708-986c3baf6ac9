"use client";

import React from 'react';

export default function MinimalProfessionalTest() {
  return (
    <div 
      style={{
        minHeight: '100vh',
        backgroundColor: '#141414', // Dark background
        color: '#F2F2F2', // Light text
        padding: '2rem',
        fontFamily: 'Inter, system-ui, sans-serif'
      }}
    >
      <div style={{ maxWidth: '800px', margin: '0 auto', textAlign: 'center' }}>
        <h1 
          style={{
            fontSize: '3rem',
            fontWeight: 'bold',
            color: '#FFFF00', // Bright yellow
            marginBottom: '1rem'
          }}
        >
          Minimal Professional Theme Test
        </h1>
        
        <p style={{ fontSize: '1.2rem', marginBottom: '2rem', opacity: 0.8 }}>
          This page uses only inline styles to test the professional black & yellow colors
        </p>

        <div 
          style={{
            display: 'inline-block',
            backgroundColor: '#FFFF00', // Yellow background
            color: '#000000', // Black text
            padding: '0.5rem 1rem',
            borderRadius: '20px',
            fontSize: '0.875rem',
            fontWeight: '600',
            marginBottom: '3rem'
          }}
        >
          Pure CSS Professional Theme
        </div>

        <div 
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1.5rem',
            marginBottom: '3rem'
          }}
        >
          {/* Card 1 */}
          <div 
            style={{
              backgroundColor: '#1F1F1F', // Dark card background
              border: '1px solid #3A3A3A',
              borderRadius: '12px',
              padding: '1.5rem',
              textAlign: 'left'
            }}
          >
            <h3 
              style={{
                color: '#FFFF00', // Yellow title
                fontSize: '1.25rem',
                fontWeight: '600',
                marginBottom: '0.5rem'
              }}
            >
              Primary Yellow
            </h3>
            <p style={{ marginBottom: '1rem', opacity: 0.8 }}>
              This card should have a dark background with yellow title
            </p>
            <button 
              style={{
                width: '100%',
                backgroundColor: '#FFFF00', // Yellow button
                color: '#000000', // Black text
                border: 'none',
                padding: '0.75rem 1rem',
                borderRadius: '8px',
                fontSize: '0.875rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = '#FFD700'; // Gold on hover
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = '#FFFF00'; // Back to yellow
              }}
            >
              Yellow Primary Button
            </button>
          </div>

          {/* Card 2 */}
          <div 
            style={{
              backgroundColor: '#1F1F1F', // Dark card background
              border: '1px solid #3A3A3A',
              borderRadius: '12px',
              padding: '1.5rem',
              textAlign: 'left'
            }}
          >
            <h3 
              style={{
                color: '#FFD700', // Gold title
                fontSize: '1.25rem',
                fontWeight: '600',
                marginBottom: '0.5rem'
              }}
            >
              Gold Accent
            </h3>
            <p style={{ marginBottom: '1rem', opacity: 0.8 }}>
              Secondary colors using gold variations
            </p>
            <div 
              style={{
                backgroundColor: '#FFD700', // Gold background
                color: '#000000', // Black text
                padding: '0.75rem',
                borderRadius: '8px',
                textAlign: 'center',
                fontWeight: '600'
              }}
            >
              Gold Background
            </div>
          </div>

          {/* Card 3 */}
          <div 
            style={{
              backgroundColor: '#1F1F1F', // Dark card background
              border: '1px solid #3A3A3A',
              borderRadius: '12px',
              padding: '1.5rem',
              textAlign: 'left'
            }}
          >
            <h3 
              style={{
                color: '#FFFF00', // Yellow title
                fontSize: '1.25rem',
                fontWeight: '600',
                marginBottom: '0.5rem'
              }}
            >
              Color Palette
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
              <div 
                style={{
                  backgroundColor: '#141414', // Very dark
                  padding: '0.5rem',
                  borderRadius: '4px',
                  border: '1px solid #3A3A3A'
                }}
              >
                Background: #141414
              </div>
              <div 
                style={{
                  backgroundColor: '#FFFF00', // Yellow
                  color: '#000000',
                  padding: '0.5rem',
                  borderRadius: '4px'
                }}
              >
                Primary: #FFFF00
              </div>
              <div 
                style={{
                  backgroundColor: '#FFD700', // Gold
                  color: '#000000',
                  padding: '0.5rem',
                  borderRadius: '4px'
                }}
              >
                Accent: #FFD700
              </div>
            </div>
          </div>
        </div>

        <div 
          style={{
            backgroundColor: '#1F1F1F',
            border: '1px solid #3A3A3A',
            borderRadius: '12px',
            padding: '2rem',
            textAlign: 'left'
          }}
        >
          <h2 
            style={{
              color: '#FFFF00',
              fontSize: '1.5rem',
              fontWeight: '600',
              marginBottom: '1rem'
            }}
          >
            Professional Theme Success Indicators
          </h2>
          
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '2rem' }}>
            <div>
              <h4 style={{ color: '#FFD700', marginBottom: '1rem' }}>✅ What You Should See:</h4>
              <ul style={{ listStyle: 'none', padding: 0, lineHeight: '1.6' }}>
                <li style={{ marginBottom: '0.5rem' }}>
                  <span style={{ color: '#FFFF00' }}>●</span> Very dark background (almost black)
                </li>
                <li style={{ marginBottom: '0.5rem' }}>
                  <span style={{ color: '#FFFF00' }}>●</span> Bright yellow buttons and titles
                </li>
                <li style={{ marginBottom: '0.5rem' }}>
                  <span style={{ color: '#FFFF00' }}>●</span> Gold accent elements
                </li>
                <li style={{ marginBottom: '0.5rem' }}>
                  <span style={{ color: '#FFFF00' }}>●</span> Light gray text on dark cards
                </li>
                <li style={{ marginBottom: '0.5rem' }}>
                  <span style={{ color: '#FFFF00' }}>●</span> High contrast, professional look
                </li>
              </ul>
            </div>
            
            <div>
              <h4 style={{ color: '#FFD700', marginBottom: '1rem' }}>❌ What You Should NOT See:</h4>
              <ul style={{ listStyle: 'none', padding: 0, lineHeight: '1.6' }}>
                <li style={{ marginBottom: '0.5rem' }}>
                  <span style={{ color: '#FF6B6B' }}>●</span> Any blue colors
                </li>
                <li style={{ marginBottom: '0.5rem' }}>
                  <span style={{ color: '#FF6B6B' }}>●</span> Light backgrounds
                </li>
                <li style={{ marginBottom: '0.5rem' }}>
                  <span style={{ color: '#FF6B6B' }}>●</span> Purple accents
                </li>
                <li style={{ marginBottom: '0.5rem' }}>
                  <span style={{ color: '#FF6B6B' }}>●</span> Default gray buttons
                </li>
                <li style={{ marginBottom: '0.5rem' }}>
                  <span style={{ color: '#FF6B6B' }}>●</span> Low contrast text
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div style={{ marginTop: '2rem', textAlign: 'center' }}>
          <p style={{ fontSize: '1.2rem', fontWeight: '600', color: '#FFFF00' }}>
            🎉 If you see yellow and black, the professional theme colors are working!
          </p>
          <p style={{ marginTop: '0.5rem', opacity: 0.7 }}>
            This test uses pure inline CSS to bypass any theme system conflicts.
          </p>
        </div>
      </div>
    </div>
  );
}
