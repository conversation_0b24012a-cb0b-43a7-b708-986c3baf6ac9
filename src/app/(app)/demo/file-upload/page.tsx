"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { FileUpload } from '@/components/ui/file-upload';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';

export default function FileUploadDemo() {
  const { toast } = useToast();
  const [singleFile, setSingleFile] = useState<File | null>(null);
  const [multipleFiles, setMultipleFiles] = useState<File[]>([]);
  const [imageFile, setImageFile] = useState<File | null>(null);

  const handleSingleFileChange = (file: File | File[] | null) => {
    if (file instanceof File) {
      setSingleFile(file);
      toast({
        title: "File Selected",
        description: `Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`
      });
    } else {
      setSingleFile(null);
    }
  };

  const handleMultipleFilesChange = (files: File | File[] | null) => {
    if (Array.isArray(files)) {
      setMultipleFiles(files);
      toast({
        title: "Files Selected",
        description: `Selected ${files.length} files`
      });
    } else {
      setMultipleFiles([]);
    }
  };

  const handleImageFileChange = (file: File | File[] | null) => {
    if (file instanceof File) {
      setImageFile(file);
      toast({
        title: "Image Selected",
        description: `Selected: ${file.name}`
      });
    } else {
      setImageFile(null);
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">File Upload Components Demo</h1>
        <p className="text-muted-foreground">
          Standardized file upload components with drag & drop, validation, and preview
        </p>
        <div className="flex justify-center gap-2">
          <Badge variant="secondary">Drag & Drop</Badge>
          <Badge variant="secondary">File Validation</Badge>
          <Badge variant="secondary">Progress Tracking</Badge>
          <Badge variant="secondary">Image Preview</Badge>
          <Badge variant="secondary">Multiple Files</Badge>
        </div>
      </div>

      <Tabs defaultValue="single" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="single">Single File</TabsTrigger>
          <TabsTrigger value="multiple">Multiple Files</TabsTrigger>
          <TabsTrigger value="image">Image Only</TabsTrigger>
          <TabsTrigger value="compact">Compact</TabsTrigger>
        </TabsList>

        <TabsContent value="single" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Single File Upload</CardTitle>
              <CardDescription>
                Upload a single file with drag & drop support and file validation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <FileUpload
                value={singleFile}
                onChange={handleSingleFileChange}
                accept="*/*"
                maxSize={10}
                label="Document Upload"
                description="Upload any document up to 10MB"
                placeholder="Drop your document here"
              />

              {singleFile && (
                <div className="p-4 bg-muted/50 rounded-lg">
                  <h4 className="font-medium mb-2">Selected File:</h4>
                  <div className="text-sm space-y-1">
                    <p><strong>Name:</strong> {singleFile.name}</p>
                    <p><strong>Size:</strong> {(singleFile.size / 1024 / 1024).toFixed(2)} MB</p>
                    <p><strong>Type:</strong> {singleFile.type || 'Unknown'}</p>
                    <p><strong>Last Modified:</strong> {new Date(singleFile.lastModified).toLocaleString()}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="multiple" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Multiple File Upload</CardTitle>
              <CardDescription>
                Upload multiple files at once with individual file management
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <FileUpload
                value={multipleFiles}
                onChange={handleMultipleFilesChange}
                accept=".pdf,.doc,.docx,.txt"
                maxSize={5}
                maxFiles={5}
                multiple={true}
                label="Document Collection"
                description="Upload up to 5 documents (PDF, DOC, DOCX, TXT) - 5MB each"
                placeholder="Drop your documents here"
              />

              {multipleFiles.length > 0 && (
                <div className="p-4 bg-muted/50 rounded-lg">
                  <h4 className="font-medium mb-2">Selected Files ({multipleFiles.length}):</h4>
                  <div className="space-y-2">
                    {multipleFiles.map((file, index) => (
                      <div key={index} className="text-sm p-2 bg-background rounded border">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium">{file.name}</p>
                            <p className="text-muted-foreground">
                              {(file.size / 1024 / 1024).toFixed(2)} MB • {file.type || 'Unknown type'}
                            </p>
                          </div>
                          <Badge variant="outline">{index + 1}</Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="image" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Image Upload with Preview</CardTitle>
              <CardDescription>
                Upload images with instant preview and image-specific validation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <FileUpload
                value={imageFile}
                onChange={handleImageFileChange}
                accept="image/svg+xml,image/png,image/jpeg,image/jpg,image/gif"
                maxSize={2}
                label="Profile Picture"
                description="Upload an image file (SVG, PNG, JPG, GIF) up to 2MB"
                placeholder="Drop your image here"
                variant="image-only"
              />

              {imageFile && (
                <div className="p-4 bg-muted/50 rounded-lg">
                  <h4 className="font-medium mb-2">Image Details:</h4>
                  <div className="text-sm space-y-1">
                    <p><strong>Name:</strong> {imageFile.name}</p>
                    <p><strong>Size:</strong> {(imageFile.size / 1024 / 1024).toFixed(2)} MB</p>
                    <p><strong>Type:</strong> {imageFile.type}</p>
                    <p><strong>Dimensions:</strong> Loading...</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="compact" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Compact File Upload</CardTitle>
              <CardDescription>
                Space-efficient file upload for forms and tight layouts
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FileUpload
                  value={null}
                  onChange={() => {}}
                  accept=".pdf"
                  maxSize={5}
                  label="Resume"
                  description="PDF only, max 5MB"
                  variant="compact"
                />

                <FileUpload
                  value={null}
                  onChange={() => {}}
                  accept="image/*"
                  maxSize={2}
                  label="Avatar"
                  description="Image files, max 2MB"
                  variant="compact"
                />
              </div>

              <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Usage in Forms</h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  The compact variant is perfect for forms where space is limited. It provides the same 
                  functionality as the default variant but with a more condensed interface.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Implementation Notes</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Key Features</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Drag & drop file selection</li>
                <li>• File type and size validation</li>
                <li>• Image preview generation</li>
                <li>• Multiple file support</li>
                <li>• Progress tracking (when integrated)</li>
                <li>• Accessible keyboard navigation</li>
                <li>• Mobile-friendly touch interface</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Form Integration</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Works with React Hook Form</li>
                <li>• Returns File objects (not URLs)</li>
                <li>• Supports validation schemas</li>
                <li>• Handles single and multiple files</li>
                <li>• Provides error states</li>
                <li>• Integrates with form libraries</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
