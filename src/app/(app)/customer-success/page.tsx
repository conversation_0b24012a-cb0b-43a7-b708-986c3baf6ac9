'use client';

import React from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useTenant } from '@/hooks/useTenant';
import CustomerSuccessDashboard from '@/components/customer-success/CustomerSuccessDashboard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertTriangle } from 'lucide-react';

export default function CustomerSuccessPage() {
  const { user, isLoading: authLoading } = useAuth();
  const { tenant, isLoading: tenantLoading } = useTenant();

  if (authLoading || tenantLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!user) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          You must be logged in to access customer success features.
        </AlertDescription>
      </Alert>
    );
  }

  if (!tenant) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No tenant information found. Please contact your administrator.
        </AlertDescription>
      </Alert>
    );
  }

  // Check if user has access to customer success features
  if (user.role !== 'super_admin' && user.role !== 'admin' && user.role !== 'management') {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          You don't have permission to access customer success features. Contact your administrator for access.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <CustomerSuccessDashboard
        tenantId={tenant.id || ''}
        userRole={user.role || 'user'}
      />
    </div>
  );
}
