"use client";

import React, { useState } from 'react';
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function FilterTestPage() {
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);

  const filterOptions = [
    { value: 'technology', label: 'Technology' },
    { value: 'healthcare', label: 'Healthcare' },
    { value: 'finance', label: 'Finance' },
    { value: 'manufacturing', label: 'Manufacturing' }
  ];

  const toggleFilter = (value: string) => {
    console.log('Toggling filter:', value);
    console.log('Current filters:', selectedFilters);
    
    setSelectedFilters(prev => {
      const isSelected = prev.includes(value);
      const newFilters = isSelected
        ? prev.filter(item => item !== value)
        : [...prev, value];
      
      console.log('New filters:', newFilters);
      return newFilters;
    });
  };

  const clearAllFilters = () => {
    setSelectedFilters([]);
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Filter Test Page</h1>
        <p className="text-muted-foreground">
          Simple test to verify filter selection/deselection works properly.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Filter Test</CardTitle>
          <CardDescription>
            Click badges to select/deselect filters. Check console for debug info.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Filter Badges */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Industries</h4>
            <div className="grid grid-cols-2 gap-2">
              {filterOptions.map((option) => (
                <Badge
                  key={option.value}
                  variant={selectedFilters.includes(option.value) ? "default" : "outline"}
                  className="cursor-pointer justify-center py-2 px-3 hover:scale-105 transition-all"
                  onClick={() => toggleFilter(option.value)}
                >
                  {option.label}
                </Badge>
              ))}
            </div>
          </div>

          {/* Clear Button */}
          <Button onClick={clearAllFilters} variant="outline">
            Clear All Filters
          </Button>

          {/* Debug Info */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Debug Info:</h4>
            <div className="p-3 bg-muted rounded-md">
              <p className="text-sm">
                <strong>Selected Filters:</strong> {JSON.stringify(selectedFilters)}
              </p>
              <p className="text-sm">
                <strong>Count:</strong> {selectedFilters.length}
              </p>
            </div>
          </div>

          {/* Active Filters Display */}
          {selectedFilters.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Active Filters:</h4>
              <div className="flex flex-wrap gap-2">
                {selectedFilters.map(filter => (
                  <Badge key={filter} variant="secondary" className="gap-1">
                    {filter}
                    <button
                      onClick={() => toggleFilter(filter)}
                      className="ml-1 hover:text-destructive"
                    >
                      ×
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
