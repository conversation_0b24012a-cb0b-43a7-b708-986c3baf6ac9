"use client";

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableAction } from '@/components/ui/advanced-data-table';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Edit2, Trash2, Eye, Image as ImageIcon, Calendar, User, FolderOpen, Plus } from "lucide-react";
import { useRouter } from 'next/navigation';
import { useToast } from "@/hooks/use-toast";
import { getMediaAlbumsAction, deleteMediaAlbumAction } from '../../actions';
import type { MediaAlbum } from '@/types/firestore';
import MediaAlbumFormDialog from './MediaAlbumFormDialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface MediaAlbumTableProps {
  onActionComplete?: () => void;
}

export default function MediaAlbumTable({ onActionComplete }: MediaAlbumTableProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [albums, setAlbums] = useState<MediaAlbum[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingAlbum, setEditingAlbum] = useState<MediaAlbum | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [albumToDelete, setAlbumToDelete] = useState<MediaAlbum | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchAlbums = useCallback(async () => {
    setIsLoading(true);
    try {
      const fetchedAlbums = await getMediaAlbumsAction();
      setAlbums(fetchedAlbums);
    } catch (error) {
      console.error("Error fetching albums:", error);
      toast({ title: "Error", description: "Failed to fetch media albums.", variant: "destructive" });
    }
    setIsLoading(false);
  }, [toast]);

  useEffect(() => {
    fetchAlbums();
  }, [fetchAlbums]);

  const handleEdit = useCallback((album: MediaAlbum) => {
    setEditingAlbum(album);
    setIsFormOpen(true);
  }, []);

  const handleDelete = useCallback((album: MediaAlbum) => {
    setAlbumToDelete(album);
    setIsDeleteDialogOpen(true);
  }, []);

  const handleDeleteConfirm = useCallback(async () => {
    if (!albumToDelete?.id) return;
    
    setIsDeleting(true);
    try {
      await deleteMediaAlbumAction(albumToDelete.id);
      toast({ title: "Success", description: "Album deleted successfully." });
      fetchAlbums();
      onActionComplete?.();
    } catch (error) {
      console.error("Error deleting album:", error);
      toast({ title: "Error", description: "Failed to delete album.", variant: "destructive" });
    }
    setIsDeleting(false);
    setIsDeleteDialogOpen(false);
    setAlbumToDelete(null);
  }, [albumToDelete, toast, fetchAlbums, onActionComplete]);

  const handleFormSuccess = useCallback(() => {
    fetchAlbums();
    onActionComplete?.();
    setIsFormOpen(false);
    setEditingAlbum(null);
  }, [fetchAlbums, onActionComplete]);

  const columns: AdvancedTableColumn<MediaAlbum>[] = useMemo(() => [
    {
      accessorKey: 'name',
      header: 'Album Name',
      cell: ({ row }) => (
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
            {row.original.coverImageUrl ? (
              <img 
                src={row.original.coverImageUrl} 
                alt={row.original.name}
                className="w-full h-full object-cover rounded-lg"
              />
            ) : (
              <FolderOpen className="h-6 w-6 text-muted-foreground" />
            )}
          </div>
          <div>
            <div className="font-medium">{row.original.name}</div>
            <div className="text-sm text-muted-foreground truncate max-w-[200px]">
              {row.original.description || 'No description'}
            </div>
          </div>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      accessorKey: 'exhibitionName',
      header: 'Exhibition',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <ImageIcon className="h-4 w-4 text-muted-foreground" />
          <span>{row.original.exhibitionName || 'N/A'}</span>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      accessorKey: 'eventName',
      header: 'Event',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span>{row.original.eventName || 'N/A'}</span>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      accessorKey: 'createdBy',
      header: 'Created By',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span>{row.original.createdBy || 'Unknown'}</span>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      accessorKey: 'createdAt',
      header: 'Created',
      cell: ({ row }) => (
        <div className="flex items-center gap-1 text-sm text-muted-foreground">
          <Calendar className="h-4 w-4" />
          <span>
            {row.original.createdAt 
              ? new Date(row.original.createdAt.seconds * 1000).toLocaleDateString()
              : 'N/A'
            }
          </span>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: false,
    },
  ], []);

  const getRowActions = useCallback((album: MediaAlbum): AdvancedTableAction<MediaAlbum>[] => [
    {
      label: 'View Album',
      icon: Eye,
      onClick: (album) => router.push(`/marketing/media-gallery/albums/${album.id}`),
      variant: 'ghost',
    },
    {
      label: 'Edit Album',
      icon: Edit2,
      onClick: handleEdit,
      variant: 'ghost',
    },
    {
      label: 'Delete Album',
      icon: Trash2,
      onClick: handleDelete,
      variant: 'ghost',
      className: 'text-destructive hover:text-destructive',
    },
  ], [router, handleEdit, handleDelete]);

  const bulkActions: AdvancedTableAction<MediaAlbum>[] = useMemo(() => [
    {
      label: 'Delete Selected',
      icon: Trash2,
      onClick: (albums) => {
        // Handle bulk delete
        console.log('Bulk delete albums:', albums);
      },
      variant: 'destructive',
    },
  ], []);

  const mobileCardRenderer = useCallback((album: MediaAlbum) => (
    <div className="p-4 space-y-3">
      <div className="flex items-center gap-3">
        <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
          {album.coverImageUrl ? (
            <img 
              src={album.coverImageUrl} 
              alt={album.name}
              className="w-full h-full object-cover rounded-lg"
            />
          ) : (
            <FolderOpen className="h-6 w-6 text-muted-foreground" />
          )}
        </div>
        <div className="flex-1">
          <div className="font-medium">{album.name}</div>
          <div className="text-sm text-muted-foreground">
            {album.description || 'No description'}
          </div>
        </div>
      </div>
      <div className="space-y-1 text-sm">
        <div className="flex items-center gap-2">
          <ImageIcon className="h-4 w-4 text-muted-foreground" />
          <span>{album.exhibitionName || 'N/A'}</span>
        </div>
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span>{album.eventName || 'N/A'}</span>
        </div>
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span>{album.createdBy || 'Unknown'}</span>
        </div>
      </div>
    </div>
  ), []);

  return (
    <>
      <AdvancedDataTable
        data={albums}
        columns={columns}
        loading={isLoading}
        enableVirtualization={albums.length > 100}
        enableGlobalSearch={true}
        searchPlaceholder="Search albums by name, exhibition, or event..."
        enableColumnFilters={true}
        enableRowSelection={true}
        enableColumnResizing={true}
        enableColumnVisibility={true}
        bulkActions={bulkActions}
        enableRowActions={true}
        rowActions={getRowActions}
        maxVisibleRowActions={3}
        enableExport={true}
        exportFormats={['csv', 'excel']}
        exportFileName="media-albums-export"
        mobileCardRenderer={mobileCardRenderer}
        onRowClick={(album) => router.push(`/marketing/media-gallery/albums/${album.id}`)}
        onRefresh={fetchAlbums}
        variant="default"
        className="w-full"
        emptyMessage="No media albums found. Click 'Create New Album' to create one."
      />

      {isFormOpen && (
        <MediaAlbumFormDialog
          isOpen={isFormOpen}
          setIsOpen={setIsFormOpen}
          album={editingAlbum}
          onSuccess={handleFormSuccess}
        />
      )}

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the album "{albumToDelete?.name}" and all its media items.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteConfirm} 
              disabled={isDeleting}
              className="bg-destructive hover:bg-destructive/90"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
