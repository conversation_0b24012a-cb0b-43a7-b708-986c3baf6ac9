"use client";

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableAction } from '@/components/ui/advanced-data-table';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Edit2, Trash2, Eye, Image as ImageIcon, Video, FileText, Calendar, User, Download, Expand } from "lucide-react";
import { useRouter } from 'next/navigation';
import { useToast } from "@/hooks/use-toast";
import { getMediaItemsByAlbum } from '@/services/firestoreService';
import { deleteMediaItemAction } from '../../actions';
import type { MediaItem } from '@/types/firestore';
import MediaItemFormDialog from './MediaItemFormDialog';
import MediaViewerDialog from '@/components/media-viewer/media-viewer-dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface MediaItemTableProps {
  albumId: string;
  onActionComplete?: () => void;
}

export default function MediaItemTable({ albumId, onActionComplete }: MediaItemTableProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [items, setItems] = useState<MediaItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<MediaItem | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [selectedItemForViewer, setSelectedItemForViewer] = useState<MediaItem | null>(null);

  const fetchItems = useCallback(async () => {
    setIsLoading(true);
    try {
      const fetchedItems = await getMediaItemsByAlbum(albumId);
      setItems(fetchedItems);
    } catch (error) {
      console.error("Error fetching media items:", error);
      toast({ title: "Error", description: "Failed to fetch media items.", variant: "destructive" });
    }
    setIsLoading(false);
  }, [albumId, toast]);

  useEffect(() => {
    fetchItems();
  }, [fetchItems]);

  // Edit functionality can be added later when MediaItemFormDialog supports editing

  const handleDelete = useCallback((item: MediaItem) => {
    setItemToDelete(item);
    setIsDeleteDialogOpen(true);
  }, []);

  const handleView = useCallback((item: MediaItem) => {
    setSelectedItemForViewer(item);
    setIsViewerOpen(true);
  }, []);

  const handleDeleteConfirm = useCallback(async () => {
    if (!itemToDelete?.id) return;
    
    setIsDeleting(true);
    try {
      await deleteMediaItemAction(itemToDelete.id);
      toast({ title: "Success", description: "Media item deleted successfully." });
      fetchItems();
      onActionComplete?.();
    } catch (error) {
      console.error("Error deleting media item:", error);
      toast({ title: "Error", description: "Failed to delete media item.", variant: "destructive" });
    }
    setIsDeleting(false);
    setIsDeleteDialogOpen(false);
    setItemToDelete(null);
  }, [itemToDelete, toast, fetchItems, onActionComplete]);

  const handleFormSuccess = useCallback(() => {
    fetchItems();
    onActionComplete?.();
    setIsFormOpen(false);
  }, [fetchItems, onActionComplete]);

  const getFileTypeIcon = (fileType: string) => {
    switch (fileType) {
      case 'image': return ImageIcon;
      case 'video': return Video;
      default: return FileText;
    }
  };

  const getFileTypeBadgeVariant = (fileType: string) => {
    switch (fileType) {
      case 'image': return 'default';
      case 'video': return 'secondary';
      default: return 'outline';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const columns: AdvancedTableColumn<MediaItem>[] = useMemo(() => [
    {
      accessorKey: 'title',
      header: 'Media Item',
      cell: ({ row }) => {
        const FileIcon = getFileTypeIcon(row.original.fileType);
        return (
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
              {row.original.fileType === 'image' && row.original.thumbnailUrl ? (
                <img 
                  src={row.original.thumbnailUrl} 
                  alt={row.original.title || 'Media item'}
                  className="w-full h-full object-cover rounded-lg"
                />
              ) : (
                <FileIcon className="h-6 w-6 text-muted-foreground" />
              )}
            </div>
            <div>
              <div className="font-medium">{row.original.title || 'Untitled'}</div>
              <div className="text-sm text-muted-foreground">
                {row.original.description || 'No description'}
              </div>
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      accessorKey: 'fileType',
      header: 'Type',
      cell: ({ row }) => (
        <Badge variant={getFileTypeBadgeVariant(row.original.fileType)}>
          {row.original.fileType.toUpperCase()}
        </Badge>
      ),
      enableSorting: true,
      enableColumnFilter: true,
      filterFn: 'equals',
    },
    {
      accessorKey: 'fileSize',
      header: 'Size',
      cell: ({ row }) => (
        <span className="text-sm">
          {row.original.fileSize ? formatFileSize(row.original.fileSize) : 'N/A'}
        </span>
      ),
      enableSorting: true,
      enableColumnFilter: false,
    },
    {
      accessorKey: 'tags',
      header: 'Tags',
      cell: ({ row }) => (
        <div className="flex flex-wrap gap-1">
          {row.original.tags?.slice(0, 3).map((tag, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {row.original.tags && row.original.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{row.original.tags.length - 3}
            </Badge>
          )}
        </div>
      ),
      enableSorting: false,
      enableColumnFilter: true,
    },
    {
      accessorKey: 'uploadedBy',
      header: 'Uploaded By',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span>{row.original.uploadedBy || 'Unknown'}</span>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      accessorKey: 'uploadedAt',
      header: 'Uploaded',
      cell: ({ row }) => (
        <div className="flex items-center gap-1 text-sm text-muted-foreground">
          <Calendar className="h-4 w-4" />
          <span>
            {row.original.uploadedAt 
              ? new Date(row.original.uploadedAt.seconds * 1000).toLocaleDateString()
              : 'N/A'
            }
          </span>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: false,
    },
  ], []);

  const getRowActions = useCallback((item: MediaItem): AdvancedTableAction<MediaItem>[] => [
    {
      label: 'View Item',
      icon: Expand,
      onClick: handleView,
      variant: 'ghost',
    },
    {
      label: 'Download',
      icon: Download,
      onClick: (item) => {
        if (item.fileUrl) {
          window.open(item.fileUrl, '_blank');
        }
      },
      variant: 'ghost',
    },
    {
      label: 'Delete Item',
      icon: Trash2,
      onClick: handleDelete,
      variant: 'ghost',
      className: 'text-destructive hover:text-destructive',
    },
  ], [handleView, handleDelete]);

  const bulkActions: AdvancedTableAction<MediaItem>[] = useMemo(() => [
    {
      label: 'Delete Selected',
      icon: Trash2,
      onClick: (items) => {
        // Handle bulk delete
        console.log('Bulk delete items:', items);
      },
      variant: 'destructive',
    },
  ], []);

  const mobileCardRenderer = useCallback((item: MediaItem) => {
    const FileIcon = getFileTypeIcon(item.fileType);
    return (
      <div className="p-4 space-y-3">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
            {item.fileType === 'image' && item.thumbnailUrl ? (
              <img 
                src={item.thumbnailUrl} 
                alt={item.title || 'Media item'}
                className="w-full h-full object-cover rounded-lg"
              />
            ) : (
              <FileIcon className="h-6 w-6 text-muted-foreground" />
            )}
          </div>
          <div className="flex-1">
            <div className="font-medium">{item.title || 'Untitled'}</div>
            <div className="text-sm text-muted-foreground">
              {item.description || 'No description'}
            </div>
          </div>
          <Badge variant={getFileTypeBadgeVariant(item.fileType)}>
            {item.fileType.toUpperCase()}
          </Badge>
        </div>
        <div className="space-y-1 text-sm">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-muted-foreground" />
            <span>{item.uploadedBy || 'Unknown'}</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span>
              {item.uploadedAt 
                ? new Date(item.uploadedAt.seconds * 1000).toLocaleDateString()
                : 'N/A'
              }
            </span>
          </div>
          {item.tags && item.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {item.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {item.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{item.tags.length - 3}
                </Badge>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }, []);

  return (
    <>
      <AdvancedDataTable
        data={items}
        columns={columns}
        loading={isLoading}
        enableVirtualization={items.length > 100}
        enableGlobalSearch={true}
        searchPlaceholder="Search media items by title, type, or tags..."
        enableColumnFilters={true}
        enableRowSelection={true}
        enableColumnResizing={true}
        enableColumnVisibility={true}
        bulkActions={bulkActions}
        enableRowActions={true}
        rowActions={getRowActions}
        maxVisibleRowActions={3}
        enableExport={true}
        exportFormats={['csv', 'excel']}
        exportFileName="media-items-export"
        mobileCardRenderer={mobileCardRenderer}
        onRowClick={handleView}
        onRefresh={fetchItems}
        variant="default"
        className="w-full"
        emptyMessage="No media items found in this album. Click 'Add Media Items' to upload some."
      />

      {isFormOpen && (
        <MediaItemFormDialog
          isOpen={isFormOpen}
          setIsOpen={setIsFormOpen}
          albumId={albumId}
          onItemAdded={handleFormSuccess}
        />
      )}

      {selectedItemForViewer && (
        <MediaViewerDialog
          isOpen={isViewerOpen}
          setIsOpen={setIsViewerOpen}
          item={selectedItemForViewer}
        />
      )}

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the media item "{itemToDelete?.title || 'this item'}".
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteConfirm} 
              disabled={isDeleting}
              className="bg-destructive hover:bg-destructive/90"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
