"use client";

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableAction } from '@/components/ui/advanced-data-table';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Edit2, Trash2, Eye, Download, Share2, Tag, FileImage, FileVideo, FileText, File, Calendar, User, Star } from "lucide-react";
import { useRouter } from 'next/navigation';
import { useToast } from "@/hooks/use-toast";
import { collection, getDocs, deleteDoc, doc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface DigitalAsset {
  id: string;
  name: string;
  type: 'image' | 'video' | 'document' | 'other';
  format: string;
  size: string;
  dimensions?: string;
  duration?: string;
  tags: string[];
  createdAt: string;
  createdBy: string;
  lastModified: string;
  downloads: number;
  views: number;
  rating: number;
  version: string;
  status: 'approved' | 'pending' | 'rejected' | 'draft';
  thumbnail?: string;
  fileUrl?: string;
}

interface DigitalAssetsTableProps {
  onActionComplete?: () => void;
}

export default function DigitalAssetsTable({ onActionComplete }: DigitalAssetsTableProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [assets, setAssets] = useState<DigitalAsset[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [assetToDelete, setAssetToDelete] = useState<DigitalAsset | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchAssets = useCallback(async () => {
    setIsLoading(true);
    try {
      // Use existing marketing_materials collection for now
      const assetsSnapshot = await getDocs(collection(db, 'marketing_materials'));

      if (assetsSnapshot.docs.length > 0) {
        const assetsData = assetsSnapshot.docs.map(doc => {
          const data = doc.data();
          // Map marketing material to digital asset format
          return {
            id: doc.id,
            name: data.title || 'Untitled',
            type: data.type === 'Image' ? 'image' : data.type === 'Video' ? 'video' : 'document',
            format: data.format || 'Unknown',
            size: '0 KB', // Default since marketing materials don't have size
            tags: data.tags || [],
            createdAt: data.createdAt || new Date().toISOString(),
            createdBy: data.createdBy || 'Unknown',
            lastModified: data.updatedAt || data.createdAt || new Date().toISOString(),
            downloads: 0, // Default
            views: 0, // Default
            rating: 0, // Default
            version: data.version || '1.0',
            status: data.status?.toLowerCase() || 'draft',
            fileUrl: data.fileUrl
          };
        }) as DigitalAsset[];
        setAssets(assetsData);
      } else {
        setAssets([]);
      }
    } catch (error) {
      console.error('Error loading digital assets:', error);
      toast({
        title: "Error",
        description: "Failed to load digital assets. Please try again.",
        variant: "destructive"
      });
      setAssets([]);
    }
    setIsLoading(false);
  }, [toast]);

  useEffect(() => {
    fetchAssets();
  }, [fetchAssets]);

  const handleDelete = useCallback((asset: DigitalAsset) => {
    setAssetToDelete(asset);
    setIsDeleteDialogOpen(true);
  }, []);

  const handleDeleteConfirm = useCallback(async () => {
    if (!assetToDelete?.id) return;
    
    setIsDeleting(true);
    try {
      await deleteDoc(doc(db, 'marketing_materials', assetToDelete.id));
      toast({ title: "Success", description: "Digital asset deleted successfully." });
      fetchAssets();
      onActionComplete?.();
    } catch (error) {
      console.error("Error deleting digital asset:", error);
      toast({ title: "Error", description: "Failed to delete digital asset.", variant: "destructive" });
    }
    setIsDeleting(false);
    setIsDeleteDialogOpen(false);
    setAssetToDelete(null);
  }, [assetToDelete, toast, fetchAssets, onActionComplete]);

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return FileImage;
      case 'video': return FileVideo;
      case 'document': return FileText;
      default: return File;
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'approved': return 'default';
      case 'pending': return 'secondary';
      case 'rejected': return 'destructive';
      case 'draft': return 'outline';
      default: return 'outline';
    }
  };

  const getStatusBadgeClassName = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-700';
      case 'pending': return 'bg-yellow-100 text-yellow-700';
      case 'rejected': return 'bg-red-100 text-red-700';
      case 'draft': return 'bg-gray-100 text-gray-700';
      default: return '';
    }
  };

  const columns: AdvancedTableColumn<DigitalAsset>[] = useMemo(() => [
    {
      accessorKey: 'name',
      header: 'Asset',
      cell: ({ row }) => {
        const FileIcon = getFileIcon(row.original.type);
        return (
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
              {row.original.thumbnail ? (
                <img 
                  src={row.original.thumbnail} 
                  alt={row.original.name}
                  className="w-full h-full object-cover rounded-lg"
                />
              ) : (
                <FileIcon className="h-6 w-6 text-muted-foreground" />
              )}
            </div>
            <div>
              <div className="font-medium">{row.original.name}</div>
              <div className="text-sm text-muted-foreground">
                {row.original.format} • {row.original.size}
              </div>
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      accessorKey: 'type',
      header: 'Type',
      cell: ({ row }) => (
        <Badge variant="outline">
          {row.original.type.toUpperCase()}
        </Badge>
      ),
      enableSorting: true,
      enableColumnFilter: true,
      filterFn: 'equals',
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => (
        <Badge 
          variant={getStatusBadgeVariant(row.original.status)}
          className={getStatusBadgeClassName(row.original.status)}
        >
          {row.original.status}
        </Badge>
      ),
      enableSorting: true,
      enableColumnFilter: true,
      filterFn: 'equals',
    },
    {
      accessorKey: 'version',
      header: 'Version',
      cell: ({ row }) => (
        <Badge variant="secondary">
          v{row.original.version}
        </Badge>
      ),
      enableSorting: true,
      enableColumnFilter: false,
    },
    {
      accessorKey: 'downloads',
      header: 'Downloads',
      cell: ({ row }) => (
        <div className="text-center">
          {row.original.downloads.toLocaleString()}
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: false,
    },
    {
      accessorKey: 'rating',
      header: 'Rating',
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <Star className="h-4 w-4 text-yellow-500 fill-current" />
          <span>{row.original.rating.toFixed(1)}</span>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: false,
    },
    {
      accessorKey: 'createdBy',
      header: 'Created By',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span>{row.original.createdBy}</span>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      accessorKey: 'lastModified',
      header: 'Modified',
      cell: ({ row }) => (
        <div className="flex items-center gap-1 text-sm text-muted-foreground">
          <Calendar className="h-4 w-4" />
          <span>{row.original.lastModified}</span>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: false,
    },
  ], []);

  const getRowActions = useCallback((asset: DigitalAsset): AdvancedTableAction<DigitalAsset>[] => [
    {
      label: 'Preview',
      icon: Eye,
      onClick: (asset) => {
        // Handle preview
        console.log('Preview asset:', asset.id);
      },
      variant: 'ghost',
    },
    {
      label: 'Download',
      icon: Download,
      onClick: (asset) => {
        if (asset.fileUrl) {
          window.open(asset.fileUrl, '_blank');
        }
      },
      variant: 'ghost',
    },
    {
      label: 'Share',
      icon: Share2,
      onClick: (asset) => {
        // Handle share
        console.log('Share asset:', asset.id);
      },
      variant: 'ghost',
    },
    {
      label: 'Edit',
      icon: Edit2,
      onClick: (asset) => {
        // Handle edit
        console.log('Edit asset:', asset.id);
      },
      variant: 'ghost',
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: handleDelete,
      variant: 'ghost',
      className: 'text-destructive hover:text-destructive',
    },
  ], [handleDelete]);

  const bulkActions: AdvancedTableAction<DigitalAsset>[] = useMemo(() => [
    {
      label: 'Download Selected',
      icon: Download,
      onClick: (assets) => {
        console.log('Bulk download assets:', assets);
      },
      variant: 'default',
    },
    {
      label: 'Tag Selected',
      icon: Tag,
      onClick: (assets) => {
        console.log('Bulk tag assets:', assets);
      },
      variant: 'outline',
    },
    {
      label: 'Delete Selected',
      icon: Trash2,
      onClick: (assets) => {
        console.log('Bulk delete assets:', assets);
      },
      variant: 'destructive',
    },
  ], []);

  const mobileCardRenderer = useCallback((asset: DigitalAsset) => {
    const FileIcon = getFileIcon(asset.type);
    return (
      <div className="p-4 space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
              {asset.thumbnail ? (
                <img 
                  src={asset.thumbnail} 
                  alt={asset.name}
                  className="w-full h-full object-cover rounded-lg"
                />
              ) : (
                <FileIcon className="h-6 w-6 text-muted-foreground" />
              )}
            </div>
            <div className="flex-1">
              <div className="font-medium">{asset.name}</div>
              <div className="text-sm text-muted-foreground">
                {asset.format} • {asset.size}
              </div>
            </div>
          </div>
          <Badge 
            variant={getStatusBadgeVariant(asset.status)}
            className={getStatusBadgeClassName(asset.status)}
          >
            {asset.status}
          </Badge>
        </div>
        <div className="space-y-2 text-sm">
          <div className="flex items-center justify-between">
            <span>Downloads: {asset.downloads}</span>
            <div className="flex items-center gap-1">
              <Star className="h-4 w-4 text-yellow-500 fill-current" />
              <span>{asset.rating.toFixed(1)}</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-muted-foreground" />
            <span>{asset.createdBy}</span>
          </div>
        </div>
      </div>
    );
  }, []);

  return (
    <>
      <AdvancedDataTable
        data={assets}
        columns={columns}
        loading={isLoading}
        enableVirtualization={assets.length > 100}
        enableGlobalSearch={true}
        searchPlaceholder="Search assets by name, type, or creator..."
        enableColumnFilters={true}
        enableRowSelection={true}
        enableColumnResizing={true}
        enableColumnVisibility={true}
        bulkActions={bulkActions}
        enableRowActions={true}
        rowActions={getRowActions}
        maxVisibleRowActions={3}
        enableExport={true}
        exportFormats={['csv', 'excel']}
        exportFileName="digital-assets-export"
        mobileCardRenderer={mobileCardRenderer}
        onRefresh={fetchAssets}
        variant="default"
        className="w-full"
        emptyMessage="No digital assets found. Upload some assets to get started."
      />

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the digital asset "{assetToDelete?.name}".
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteConfirm} 
              disabled={isDeleting}
              className="bg-destructive hover:bg-destructive/90"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
