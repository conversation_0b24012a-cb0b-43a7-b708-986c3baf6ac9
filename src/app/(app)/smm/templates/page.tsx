"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, PlusCircle, Library } from "lucide-react";
import Link from 'next/link';
import ContentTemplatesTable from '../components/ContentTemplatesTable';

export default function ContentTemplatesPage() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight font-headline flex items-center">
            <FileText className="mr-3 h-8 w-8 text-primary" />
            Content Templates
          </h1>
          <p className="text-muted-foreground">
            Create and manage reusable content templates for consistent social media posting.
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href="/smm/composer">
              <Library className="mr-2 h-4 w-4" /> Use Template
            </Link>
          </Button>
          <Button asChild>
            <Link href="/smm/templates/create">
              <PlusCircle className="mr-2 h-4 w-4" /> New Template
            </Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Content Template Library</CardTitle>
        </CardHeader>
        <CardContent>
          <ContentTemplatesTable selectionMode={false} />
        </CardContent>
      </Card>
    </div>
  );
}
