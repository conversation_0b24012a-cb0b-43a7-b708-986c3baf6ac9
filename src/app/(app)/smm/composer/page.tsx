"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Share2, Construction } from "lucide-react";

export default function SocialMediaComposerPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight font-headline flex items-center">
            <Share2 className="mr-3 h-8 w-8 text-primary" />
            Social Media Composer
          </h1>
          <p className="text-muted-foreground">
            Create, schedule, and manage social media content across platforms
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Construction className="h-5 w-5 text-orange-500" />
            Under Maintenance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            The Social Media Composer is currently being updated. Please check back later.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
