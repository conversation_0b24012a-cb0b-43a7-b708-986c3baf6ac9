"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { 
  Search,
  Edit,
  Trash2,
  Play,
  Pause,
  Plus,
  Settings,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { MonitoringStream } from '@/types/firestore';
import { getMonitoringStreamsAction, createMonitoringStreamAction, deleteMonitoringStreamAction } from '../actions';
import { formatDistanceToNow } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

const statusConfig = {
  active: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
  paused: { color: 'bg-yellow-100 text-yellow-800', icon: Pause },
  error: { color: 'bg-red-100 text-red-800', icon: AlertTriangle },
};

interface MonitoringStreamsTableProps {
  onStreamSelect?: (stream: MonitoringStream) => void;
}

export default function MonitoringStreamsTable({ onStreamSelect }: MonitoringStreamsTableProps) {
  const { toast } = useToast();
  const [streams, setStreams] = useState<MonitoringStream[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load monitoring streams
  useEffect(() => {
    loadStreams();
  }, []);

  const loadStreams = async () => {
    setIsLoading(true);
    try {
      const fetchedStreams = await getMonitoringStreamsAction();
      setStreams(fetchedStreams);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load monitoring streams.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const handleDelete = async (streamId: string) => {
    if (!confirm('Are you sure you want to delete this monitoring stream?')) return;
    
    try {
      await deleteMonitoringStreamAction(streamId);
      await loadStreams();
      toast({
        title: "Success",
        description: "Monitoring stream deleted successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete monitoring stream.",
        variant: "destructive"
      });
    }
  };

  const handleToggleStatus = async (streamId: string, currentStatus: boolean) => {
    try {
      // This would be implemented in actions
      console.log('Toggle stream status:', streamId, !currentStatus);
      toast({
        title: "Feature Coming Soon",
        description: "Stream status toggle will be available soon."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update stream status.",
        variant: "destructive"
      });
    }
  };

  const formatDate = (date: any) => {
    if (!date) return 'Never';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const columns: ColumnDef<MonitoringStream>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: "Stream Name",
      cell: ({ row }) => {
        const stream = row.original;
        
        return (
          <div className="flex flex-col">
            <span className="font-medium">{stream.name}</span>
            {stream.description && (
              <span className="text-sm text-gray-500 truncate max-w-xs">
                {stream.description}
              </span>
            )}
          </div>
        );
      },
    },
    {
      id: "keywords",
      header: "Keywords",
      cell: ({ row }) => {
        const keywords = row.original.keywords || [];
        
        if (keywords.length === 0) {
          return <span className="text-gray-400">No keywords</span>;
        }
        
        return (
          <div className="flex gap-1 flex-wrap">
            {keywords.slice(0, 3).map(keyword => (
              <Badge key={keyword} variant="outline" className="text-xs">
                {keyword}
              </Badge>
            ))}
            {keywords.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{keywords.length - 3}
              </Badge>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "platforms",
      header: "Platforms",
      cell: ({ row }) => {
        const platforms = row.original.platforms || [];
        
        return (
          <div className="flex gap-1">
            {platforms.map(platform => (
              <Badge key={platform} variant="secondary" className="text-xs capitalize">
                {platform}
              </Badge>
            ))}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "isActive",
      header: "Status",
      cell: ({ row }) => {
        const isActive = row.original.isActive;
        const status = isActive ? 'active' : 'paused';
        const config = statusConfig[status];
        const StatusIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <StatusIcon className="h-3 w-3" />
            {status}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        const isActive = row.getValue(id);
        return value.includes(isActive ? 'active' : 'paused');
      },
    },
    {
      id: "mentionsCount",
      header: "Mentions",
      cell: ({ row }) => {
        // This would come from aggregated data
        const mentionsCount = Math.floor(Math.random() * 100); // Placeholder
        
        return (
          <div className="text-center">
            <span className="font-medium">{mentionsCount}</span>
            <span className="text-sm text-gray-500 ml-1">mentions</span>
          </div>
        );
      },
    },
    {
      accessorKey: "lastRunAt",
      header: "Last Run",
      cell: ({ row }) => {
        const lastRunAt = row.original.lastRunAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(lastRunAt)}
          </div>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(createdAt)}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const stream = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleToggleStatus(stream.id!, stream.isActive)}
              title={stream.isActive ? 'Pause stream' : 'Resume stream'}
            >
              {stream.isActive ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open stream edit dialog
                console.log('Edit stream:', stream);
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDelete(stream.id!)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], []);

  const tableActions = [
    {
      label: "Create Stream",
      icon: Plus,
      onClick: () => {
        // Open stream creation dialog
        console.log('Create new monitoring stream');
      },
      variant: "default" as const,
    },
    {
      label: "Pause Selected",
      icon: Pause,
      onClick: async (selectedRows: MonitoringStream[]) => {
        console.log('Pause streams:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Bulk stream management will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Delete Selected",
      icon: Trash2,
      onClick: async (selectedRows: MonitoringStream[]) => {
        if (confirm(`Delete ${selectedRows.length} selected streams?`)) {
          try {
            await Promise.all(selectedRows.map(stream => deleteMonitoringStreamAction(stream.id!)));
            await loadStreams();
            toast({
              title: "Success",
              description: `${selectedRows.length} streams deleted successfully.`
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to delete streams.",
              variant: "destructive"
            });
          }
        }
      },
      variant: "destructive" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    {
      key: 'isActive',
      label: 'Status',
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Paused', value: 'paused' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={streams}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadStreams}
      tableActions={tableActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search streams by name, keywords..."
      emptyStateMessage="No monitoring streams found"
      emptyStateDescription="Create your first monitoring stream to start tracking mentions."
    />
  );
}
