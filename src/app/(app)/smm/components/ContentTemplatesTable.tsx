"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { 
  FileText,
  Edit,
  Copy,
  Trash2,
  Star,
  Megaphone,
  Users,
  GraduationCap,
  Calendar,
  Linkedin,
  Twitter,
  Facebook,
  Instagram,
  Plus,
  Eye
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { ContentTemplate, SocialPlatform } from '@/types/firestore';
import { getContentTemplatesAction, saveContentTemplateAction, deleteContentTemplateAction } from '../actions';
import { formatDistanceToNow } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

const platformIcons = {
  linkedin: Linkedin,
  twitter: Twitter,
  facebook: Facebook,
  instagram: Instagram,
};

const categoryIcons = {
  announcement: Megaphone,
  promotion: Star,
  engagement: Users,
  educational: GraduationCap,
  event: Calendar,
  custom: FileText,
};

const categoryColors = {
  announcement: 'bg-blue-100 text-blue-800',
  promotion: 'bg-green-100 text-green-800',
  engagement: 'bg-purple-100 text-purple-800',
  educational: 'bg-orange-100 text-orange-800',
  event: 'bg-pink-100 text-pink-800',
  custom: 'bg-gray-100 text-gray-800',
};

interface ContentTemplatesTableProps {
  onTemplateSelect?: (template: ContentTemplate) => void;
  selectionMode?: boolean;
}

export default function ContentTemplatesTable({ onTemplateSelect, selectionMode = false }: ContentTemplatesTableProps) {
  const { toast } = useToast();
  const [templates, setTemplates] = useState<ContentTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load content templates
  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    setIsLoading(true);
    try {
      const fetchedTemplates = await getContentTemplatesAction();
      setTemplates(fetchedTemplates);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load content templates.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const handleDelete = async (templateId: string) => {
    if (!confirm('Are you sure you want to delete this template?')) return;
    
    try {
      await deleteContentTemplateAction(templateId);
      await loadTemplates();
      toast({
        title: "Success",
        description: "Template deleted successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete template.",
        variant: "destructive"
      });
    }
  };

  const handleDuplicate = async (template: ContentTemplate) => {
    try {
      const duplicatedTemplate = {
        ...template,
        name: `${template.name} (Copy)`,
        id: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        usageCount: 0,
      };
      
      await saveContentTemplateAction(duplicatedTemplate);
      await loadTemplates();
      toast({
        title: "Success",
        description: "Template duplicated successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate template.",
        variant: "destructive"
      });
    }
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const columns: ColumnDef<ContentTemplate>[] = useMemo(() => [
    ...(selectionMode ? [] : [{
      id: "select" as const,
      header: ({ table }: any) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e: any) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }: any) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e: any) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    }]),
    {
      accessorKey: "name",
      header: "Template Name",
      cell: ({ row }) => {
        const template = row.original;
        const CategoryIcon = categoryIcons[template.category];
        
        return (
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gray-100">
              <CategoryIcon className="h-4 w-4" />
            </div>
            <div className="flex flex-col">
              <span className="font-medium">{template.name}</span>
              {template.description && (
                <span className="text-sm text-gray-500 truncate max-w-xs">
                  {template.description}
                </span>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: ({ row }) => {
        const category = row.original.category;
        const CategoryIcon = categoryIcons[category];
        const colorClass = categoryColors[category];
        
        return (
          <Badge className={`${colorClass} flex items-center gap-1 w-fit`}>
            <CategoryIcon className="h-3 w-3" />
            {category}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: "platforms",
      header: "Platforms",
      cell: ({ row }) => {
        const platforms = row.original.platforms;
        
        return (
          <div className="flex gap-1">
            {platforms.map(platform => {
              const PlatformIcon = platformIcons[platform];
              return (
                <div key={platform} className="p-1 rounded bg-gray-100">
                  <PlatformIcon className="h-4 w-4" />
                </div>
              );
            })}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "usageCount",
      header: "Usage",
      cell: ({ row }) => {
        const usageCount = row.original.usageCount || 0;
        return (
          <div className="text-center">
            <span className="font-medium">{usageCount}</span>
            <span className="text-sm text-gray-500 ml-1">times</span>
          </div>
        );
      },
    },
    {
      id: "tags",
      header: "Tags",
      cell: ({ row }) => {
        const tags = row.original.tags || [];
        
        if (tags.length === 0) {
          return <span className="text-gray-400">No tags</span>;
        }
        
        return (
          <div className="flex gap-1 flex-wrap">
            {tags.slice(0, 2).map(tag => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {tags.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{tags.length - 2}
              </Badge>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "isActive",
      header: "Status",
      cell: ({ row }) => {
        const isActive = row.original.isActive;
        
        return (
          <Badge variant={isActive ? "default" : "secondary"}>
            {isActive ? "Active" : "Inactive"}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        const isActive = row.getValue(id);
        return value.includes(isActive ? 'active' : 'inactive');
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(createdAt)}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const template = row.original;
        
        if (selectionMode) {
          return (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onTemplateSelect?.(template)}
            >
              Select
            </Button>
          );
        }
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open template preview/edit
                console.log('Edit template:', template);
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDuplicate(template)}
            >
              <Copy className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDelete(template.id!)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], [selectionMode, onTemplateSelect]);

  const tableActions = [
    {
      label: "Create Template",
      icon: Plus,
      onClick: () => {
        // Open template creation dialog
        console.log('Create new template');
      },
      variant: "default" as const,
    },
    {
      label: "Duplicate Selected",
      icon: Copy,
      onClick: async (selectedRows: ContentTemplate[]) => {
        try {
          await Promise.all(selectedRows.map(template => handleDuplicate(template)));
          toast({
            title: "Success",
            description: `${selectedRows.length} templates duplicated successfully.`
          });
        } catch (error) {
          toast({
            title: "Error",
            description: "Failed to duplicate templates.",
            variant: "destructive"
          });
        }
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Delete Selected",
      icon: Trash2,
      onClick: async (selectedRows: ContentTemplate[]) => {
        if (confirm(`Delete ${selectedRows.length} selected templates?`)) {
          try {
            await Promise.all(selectedRows.map(template => deleteContentTemplateAction(template.id!)));
            await loadTemplates();
            toast({
              title: "Success",
              description: `${selectedRows.length} templates deleted successfully.`
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to delete templates.",
              variant: "destructive"
            });
          }
        }
      },
      variant: "destructive" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    {
      key: 'category',
      label: 'Category',
      options: [
        { label: 'Announcement', value: 'announcement' },
        { label: 'Promotion', value: 'promotion' },
        { label: 'Engagement', value: 'engagement' },
        { label: 'Educational', value: 'educational' },
        { label: 'Event', value: 'event' },
        { label: 'Custom', value: 'custom' },
      ]
    },
    {
      key: 'isActive',
      label: 'Status',
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={templates}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadTemplates}
      tableActions={selectionMode ? [] : tableActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search templates by name, description..."
      emptyStateMessage="No content templates found"
      emptyStateDescription="Create your first content template to get started."
    />
  );
}
