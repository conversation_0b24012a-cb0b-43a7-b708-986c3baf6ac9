"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { 
  Calendar,
  Edit,
  Eye,
  Trash2,
  Send,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Linkedin,
  Twitter,
  Facebook,
  Instagram,
  Copy,
  Share2,
  MoreHorizontal
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { SocialPost, SocialPlatform } from '@/types/firestore';
import { getSocialPostsAction, deleteSocialPostAction, updateSocialPostScheduleAction } from '../actions';
import { formatDistanceToNow, format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

const platformIcons = {
  linkedin: Linkedin,
  twitter: Twitter,
  facebook: Facebook,
  instagram: Instagram,
};

const statusConfig = {
  'draft': { color: 'bg-gray-100 text-gray-800', icon: Edit },
  'pending-approval': { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
  'approved': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
  'rejected': { color: 'bg-red-100 text-red-800', icon: XCircle },
  'scheduled': { color: 'bg-blue-100 text-blue-800', icon: Calendar },
  'queued': { color: 'bg-purple-100 text-purple-800', icon: Clock },
  'sent': { color: 'bg-green-100 text-green-800', icon: Send },
  'error': { color: 'bg-red-100 text-red-800', icon: AlertCircle },
};

interface SocialPostsTableProps {
  activityId?: string;
  showActivityFilter?: boolean;
}

export default function SocialPostsTable({ activityId, showActivityFilter = true }: SocialPostsTableProps) {
  const { toast } = useToast();
  const [posts, setPosts] = useState<SocialPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load social posts
  useEffect(() => {
    loadPosts();
  }, [activityId]);

  const loadPosts = async () => {
    setIsLoading(true);
    try {
      const fetchedPosts = await getSocialPostsAction();
      
      // Filter by activity if specified
      const filteredPosts = activityId 
        ? fetchedPosts.filter(post => post.linkedActivity.id === activityId)
        : fetchedPosts;
      
      setPosts(filteredPosts);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load social posts.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const handleDelete = async (postId: string) => {
    try {
      await deleteSocialPostAction(postId);
      await loadPosts();
      toast({
        title: "Success",
        description: "Social post deleted successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete social post.",
        variant: "destructive"
      });
    }
  };

  const handleReschedule = async (postId: string, newDate: Date) => {
    try {
      await updateSocialPostScheduleAction(postId, newDate);
      await loadPosts();
      toast({
        title: "Success",
        description: "Post rescheduled successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reschedule post.",
        variant: "destructive"
      });
    }
  };

  const formatDate = (date: any) => {
    if (!date) return 'Not scheduled';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return format(dateObj, 'MMM dd, yyyy HH:mm');
  };

  const columns: ColumnDef<SocialPost>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "linkedActivity.name",
      header: "Activity",
      cell: ({ row }) => {
        const activity = row.original.linkedActivity;
        return (
          <div className="flex flex-col">
            <span className="font-medium">{activity.name}</span>
            <Badge variant="outline" className="w-fit text-xs">
              {activity.type}
            </Badge>
          </div>
        );
      },
      enableHiding: !activityId, // Hide if filtering by specific activity
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.status;
        const config = statusConfig[status];
        const StatusIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <StatusIcon className="h-3 w-3" />
            {status.replace('-', ' ')}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: "platforms",
      header: "Platforms",
      cell: ({ row }) => {
        const content = row.original.content;
        const platforms = Object.keys(content) as SocialPlatform[];
        
        return (
          <div className="flex gap-1">
            {platforms.map(platform => {
              const PlatformIcon = platformIcons[platform];
              return (
                <div key={platform} className="p-1 rounded bg-gray-100">
                  <PlatformIcon className="h-4 w-4" />
                </div>
              );
            })}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "content",
      header: "Content Preview",
      cell: ({ row }) => {
        const content = row.original.content;
        const firstPlatform = Object.keys(content)[0] as SocialPlatform;
        const text = content[firstPlatform]?.text || '';
        
        return (
          <div className="max-w-xs">
            <p className="text-sm text-gray-600 truncate">
              {text.length > 50 ? `${text.substring(0, 50)}...` : text}
            </p>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "publishAt",
      header: "Scheduled For",
      cell: ({ row }) => {
        const publishAt = row.original.publishAt;
        return (
          <div className="text-sm">
            {formatDate(publishAt)}
          </div>
        );
      },
      sortingFn: (rowA, rowB) => {
        const dateA = rowA.original.publishAt;
        const dateB = rowB.original.publishAt;
        
        if (!dateA && !dateB) return 0;
        if (!dateA) return 1;
        if (!dateB) return -1;
        
        const timeA = dateA instanceof Timestamp ? dateA.toMillis() : new Date(dateA).getTime();
        const timeB = dateB instanceof Timestamp ? dateB.toMillis() : new Date(dateB).getTime();
        
        return timeA - timeB;
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        if (!createdAt) return 'Unknown';
        
        let dateObj: Date;
        if (createdAt instanceof Timestamp) {
          dateObj = createdAt.toDate();
        } else if (typeof createdAt === 'string') {
          dateObj = new Date(createdAt);
        } else {
          dateObj = createdAt;
        }
        
        return (
          <div className="text-sm text-gray-600">
            {formatDistanceToNow(dateObj, { addSuffix: true })}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const post = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.open(`/smm/composer?id=${post.id}`, '_blank')}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.open(`/smm/posts/${post.id}`, '_blank')}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDelete(post.id!)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], [activityId]);

  const tableActions = [
    {
      label: "Create Post",
      icon: Share2,
      onClick: () => window.open('/smm/composer', '_blank'),
      variant: "default" as const,
    },
    {
      label: "Bulk Schedule",
      icon: Calendar,
      onClick: (selectedRows: SocialPost[]) => {
        console.log('Bulk schedule:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Bulk scheduling will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Duplicate",
      icon: Copy,
      onClick: (selectedRows: SocialPost[]) => {
        console.log('Duplicate posts:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Post duplication will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Delete Selected",
      icon: Trash2,
      onClick: async (selectedRows: SocialPost[]) => {
        if (confirm(`Delete ${selectedRows.length} selected posts?`)) {
          try {
            await Promise.all(selectedRows.map(post => deleteSocialPostAction(post.id!)));
            await loadPosts();
            toast({
              title: "Success",
              description: `${selectedRows.length} posts deleted successfully.`
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to delete selected posts.",
              variant: "destructive"
            });
          }
        }
      },
      variant: "destructive" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    {
      key: 'status',
      label: 'Status',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Pending Approval', value: 'pending-approval' },
        { label: 'Approved', value: 'approved' },
        { label: 'Rejected', value: 'rejected' },
        { label: 'Scheduled', value: 'scheduled' },
        { label: 'Queued', value: 'queued' },
        { label: 'Sent', value: 'sent' },
        { label: 'Error', value: 'error' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={posts}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadPosts}
      tableActions={tableActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search posts by content, activity..."
      emptyStateMessage="No social posts found"
      emptyStateDescription="Create your first social media post to get started."
    />
  );
}
