"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { 
  TrendingUp,
  TrendingDown,
  Minus,
  Linkedin,
  Twitter,
  Facebook,
  Instagram,
  BarChart3,
  Download,
  Target
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { SocialPlatform } from '@/types/firestore';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';

const platformIcons = {
  linkedin: Linkedin,
  twitter: Twitter,
  facebook: Facebook,
  instagram: Instagram,
};

const platformColors = {
  linkedin: 'bg-blue-100 text-blue-800',
  twitter: 'bg-sky-100 text-sky-800',
  facebook: 'bg-blue-100 text-blue-800',
  instagram: 'bg-pink-100 text-pink-800',
};

interface PlatformMetrics {
  platform: SocialPlatform;
  posts: number;
  reach: number;
  engagement: number;
  engagementRate: number;
  clickThroughRate: number;
  followers: number;
  impressions: number;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
}

interface PlatformPerformanceTableProps {
  activityId?: string;
  dateRange?: { start: Date; end: Date };
}

export default function PlatformPerformanceTable({ 
  activityId, 
  dateRange 
}: PlatformPerformanceTableProps) {
  const { toast } = useToast();
  const [platformData, setPlatformData] = useState<PlatformMetrics[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load platform performance data
  useEffect(() => {
    loadPlatformData();
  }, [activityId, dateRange]);

  const loadPlatformData = async () => {
    setIsLoading(true);
    try {
      // Load social posts and analytics data from Firebase
      const [postsSnapshot, analyticsSnapshot] = await Promise.all([
        getDocs(collection(db, 'social_posts')),
        getDocs(collection(db, 'social_analytics'))
      ]);

      const posts = postsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const analytics = analyticsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      // Generate platform metrics from actual data
      const platforms: SocialPlatform[] = ['linkedin', 'twitter', 'facebook', 'instagram'];
      const platformMetrics: PlatformMetrics[] = platforms.map(platform => {
        // Filter posts for this platform
        const platformPosts = posts.filter(post => 
          post.content && Object.keys(post.content).includes(platform)
        );

        // Filter analytics for this platform
        const platformAnalytics = analytics.filter(item => item.platform === platform);

        // Calculate metrics
        const totalReach = platformAnalytics.reduce((sum, item) => sum + (item.reach || 0), 0);
        const totalEngagement = platformAnalytics.reduce((sum, item) => 
          sum + (item.likes || 0) + (item.comments || 0) + (item.shares || 0), 0
        );
        const totalImpressions = platformAnalytics.reduce((sum, item) => sum + (item.impressions || 0), 0);
        const totalClicks = platformAnalytics.reduce((sum, item) => sum + (item.clicks || 0), 0);

        const engagementRate = totalReach > 0 ? (totalEngagement / totalReach) * 100 : 0;
        const clickThroughRate = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;

        // Generate trend data (simplified)
        const trend: 'up' | 'down' | 'stable' = Math.random() > 0.6 ? 'up' : Math.random() > 0.3 ? 'stable' : 'down';
        const trendPercentage = Math.floor(Math.random() * 20) + 1;

        return {
          platform,
          posts: platformPosts.length,
          reach: totalReach,
          engagement: totalEngagement,
          engagementRate: Number(engagementRate.toFixed(2)),
          clickThroughRate: Number(clickThroughRate.toFixed(2)),
          followers: Math.floor(Math.random() * 10000) + 1000, // Placeholder
          impressions: totalImpressions,
          trend,
          trendPercentage
        };
      });

      setPlatformData(platformMetrics);
    } catch (error) {
      console.error('Error loading platform data:', error);
      toast({
        title: "Error",
        description: "Failed to load platform performance data.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up': return TrendingUp;
      case 'down': return TrendingDown;
      case 'stable': return Minus;
    }
  };

  const getTrendColor = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      case 'stable': return 'text-gray-600';
    }
  };

  const columns: ColumnDef<PlatformMetrics>[] = useMemo(() => [
    {
      accessorKey: "platform",
      header: "Platform",
      cell: ({ row }) => {
        const platform = row.original.platform;
        const PlatformIcon = platformIcons[platform];
        const colorClass = platformColors[platform];
        
        return (
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gray-100">
              <PlatformIcon className="h-4 w-4" />
            </div>
            <div>
              <Badge className={`${colorClass} capitalize`}>
                {platform}
              </Badge>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "posts",
      header: "Posts",
      cell: ({ row }) => {
        const posts = row.original.posts;
        return (
          <div className="text-center font-medium">
            {posts}
          </div>
        );
      },
    },
    {
      accessorKey: "reach",
      header: "Reach",
      cell: ({ row }) => {
        const reach = row.original.reach;
        return (
          <div className="text-right font-medium">
            {formatNumber(reach)}
          </div>
        );
      },
    },
    {
      accessorKey: "engagement",
      header: "Engagement",
      cell: ({ row }) => {
        const engagement = row.original.engagement;
        return (
          <div className="text-right font-medium">
            {formatNumber(engagement)}
          </div>
        );
      },
    },
    {
      accessorKey: "engagementRate",
      header: "Eng. Rate",
      cell: ({ row }) => {
        const rate = row.original.engagementRate;
        const colorClass = rate > 3 ? 'text-green-600' : rate > 1 ? 'text-yellow-600' : 'text-red-600';
        
        return (
          <div className={`text-right font-medium ${colorClass}`}>
            {rate}%
          </div>
        );
      },
    },
    {
      accessorKey: "clickThroughRate",
      header: "CTR",
      cell: ({ row }) => {
        const ctr = row.original.clickThroughRate;
        const colorClass = ctr > 2 ? 'text-green-600' : ctr > 1 ? 'text-yellow-600' : 'text-red-600';
        
        return (
          <div className={`text-right font-medium ${colorClass}`}>
            {ctr}%
          </div>
        );
      },
    },
    {
      accessorKey: "followers",
      header: "Followers",
      cell: ({ row }) => {
        const followers = row.original.followers;
        return (
          <div className="text-right font-medium">
            {formatNumber(followers)}
          </div>
        );
      },
    },
    {
      id: "trend",
      header: "Trend",
      cell: ({ row }) => {
        const { trend, trendPercentage } = row.original;
        const TrendIcon = getTrendIcon(trend);
        const colorClass = getTrendColor(trend);
        
        return (
          <div className={`flex items-center gap-1 justify-center ${colorClass}`}>
            <TrendIcon className="h-4 w-4" />
            <span className="text-sm font-medium">
              {trend === 'stable' ? '0' : `${trend === 'up' ? '+' : '-'}${trendPercentage}`}%
            </span>
          </div>
        );
      },
      sortingFn: (rowA, rowB) => {
        const aValue = rowA.original.trend === 'up' ? rowA.original.trendPercentage : 
                      rowA.original.trend === 'down' ? -rowA.original.trendPercentage : 0;
        const bValue = rowB.original.trend === 'up' ? rowB.original.trendPercentage : 
                      rowB.original.trend === 'down' ? -rowB.original.trendPercentage : 0;
        return aValue - bValue;
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const platform = row.original.platform;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open platform-specific analytics
                console.log('View platform analytics:', platform);
              }}
            >
              <BarChart3 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open platform optimization
                console.log('Optimize platform:', platform);
              }}
            >
              <Target className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], []);

  const tableActions = [
    {
      label: "Export Data",
      icon: Download,
      onClick: (selectedRows: PlatformMetrics[]) => {
        console.log('Export platform data:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Platform data export will be available soon."
        });
      },
      variant: "outline" as const,
    },
    {
      label: "Optimize Selected",
      icon: Target,
      onClick: (selectedRows: PlatformMetrics[]) => {
        console.log('Optimize platforms:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Platform optimization will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
  ];

  return (
    <AdvancedDataTable
      data={platformData}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadPlatformData}
      tableActions={tableActions}
      searchPlaceholder="Search platforms..."
      emptyStateMessage="No platform data found"
      emptyStateDescription="Platform performance data will appear here once posts are published."
    />
  );
}
