"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { 
  TrendingUp,
  Heart,
  MessageSquare,
  Share,
  Eye,
  BarChart3,
  Linkedin,
  Twitter,
  Facebook,
  Instagram,
  ExternalLink,
  Download
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { SocialPost, SocialPlatform } from '@/types/firestore';
import { getSocialPostsAction } from '../actions';
import { formatDistanceToNow, format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

const platformIcons = {
  linkedin: Linkedin,
  twitter: Twitter,
  facebook: Facebook,
  instagram: Instagram,
};

const platformColors = {
  linkedin: 'bg-blue-100 text-blue-800',
  twitter: 'bg-sky-100 text-sky-800',
  facebook: 'bg-blue-100 text-blue-800',
  instagram: 'bg-pink-100 text-pink-800',
};

interface PerformancePostsTableProps {
  activityId?: string;
  limit?: number;
  sortBy?: 'engagement' | 'reach' | 'likes' | 'shares';
}

export default function PerformancePostsTable({ 
  activityId, 
  limit = 50, 
  sortBy = 'engagement' 
}: PerformancePostsTableProps) {
  const { toast } = useToast();
  const [posts, setPosts] = useState<SocialPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load social posts with performance data
  useEffect(() => {
    loadPosts();
  }, [activityId, sortBy]);

  const loadPosts = async () => {
    setIsLoading(true);
    try {
      const fetchedPosts = await getSocialPostsAction();
      
      // Filter by activity if specified
      let filteredPosts = activityId 
        ? fetchedPosts.filter(post => post.linkedActivity.id === activityId)
        : fetchedPosts;

      // Filter only sent posts with performance data
      filteredPosts = filteredPosts.filter(post => 
        post.status === 'sent' && post.performanceMetrics
      );

      // Sort by performance metric
      filteredPosts.sort((a, b) => {
        const aMetric = getMetricValue(a, sortBy);
        const bMetric = getMetricValue(b, sortBy);
        return bMetric - aMetric;
      });

      // Apply limit
      if (limit > 0) {
        filteredPosts = filteredPosts.slice(0, limit);
      }
      
      setPosts(filteredPosts);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load performance data.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const getMetricValue = (post: SocialPost, metric: string): number => {
    if (!post.performanceMetrics) return 0;
    
    switch (metric) {
      case 'engagement':
        return (post.performanceMetrics.likes || 0) + 
               (post.performanceMetrics.comments || 0) + 
               (post.performanceMetrics.shares || 0);
      case 'reach':
        return post.performanceMetrics.reach || 0;
      case 'likes':
        return post.performanceMetrics.likes || 0;
      case 'shares':
        return post.performanceMetrics.shares || 0;
      default:
        return 0;
    }
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return format(dateObj, 'MMM dd, yyyy');
  };

  const formatNumber = (num: number | undefined): string => {
    if (!num) return '0';
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const calculateEngagementRate = (post: SocialPost): number => {
    if (!post.performanceMetrics || !post.performanceMetrics.reach) return 0;
    
    const totalEngagement = (post.performanceMetrics.likes || 0) + 
                           (post.performanceMetrics.comments || 0) + 
                           (post.performanceMetrics.shares || 0);
    
    return (totalEngagement / post.performanceMetrics.reach) * 100;
  };

  const columns: ColumnDef<SocialPost>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "content",
      header: "Post Content",
      cell: ({ row }) => {
        const post = row.original;
        const content = post.content;
        const firstPlatform = Object.keys(content)[0] as SocialPlatform;
        const text = content[firstPlatform]?.text || '';
        const PlatformIcon = platformIcons[firstPlatform];
        const platformColor = platformColors[firstPlatform];
        
        return (
          <div className="max-w-sm">
            <div className="flex items-center gap-2 mb-2">
              <Badge className={`${platformColor} flex items-center gap-1`}>
                <PlatformIcon className="h-3 w-3" />
                {firstPlatform}
              </Badge>
            </div>
            <p className="text-sm line-clamp-3">
              {text}
            </p>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "engagement",
      header: "Engagement",
      cell: ({ row }) => {
        const metrics = row.original.performanceMetrics;
        if (!metrics) return <span className="text-gray-400">No data</span>;
        
        const totalEngagement = (metrics.likes || 0) + 
                               (metrics.comments || 0) + 
                               (metrics.shares || 0);
        
        return (
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-1">
              <Heart className="h-3 w-3 text-red-500" />
              <span>{formatNumber(metrics.likes || 0)}</span>
            </div>
            <div className="flex items-center gap-1">
              <MessageSquare className="h-3 w-3 text-blue-500" />
              <span>{formatNumber(metrics.comments || 0)}</span>
            </div>
            <div className="flex items-center gap-1">
              <Share className="h-3 w-3 text-green-500" />
              <span>{formatNumber(metrics.shares || 0)}</span>
            </div>
          </div>
        );
      },
      sortingFn: (rowA, rowB) => {
        const aTotal = getMetricValue(rowA.original, 'engagement');
        const bTotal = getMetricValue(rowB.original, 'engagement');
        return aTotal - bTotal;
      },
    },
    {
      id: "reach",
      header: "Reach",
      cell: ({ row }) => {
        const reach = row.original.performanceMetrics?.reach;
        
        return (
          <div className="flex items-center gap-1">
            <Eye className="h-3 w-3 text-gray-500" />
            <span className="font-medium">{formatNumber(reach || 0)}</span>
          </div>
        );
      },
      sortingFn: (rowA, rowB) => {
        const aReach = rowA.original.performanceMetrics?.reach || 0;
        const bReach = rowB.original.performanceMetrics?.reach || 0;
        return aReach - bReach;
      },
    },
    {
      id: "engagementRate",
      header: "Eng. Rate",
      cell: ({ row }) => {
        const rate = calculateEngagementRate(row.original);
        
        return (
          <div className="text-center">
            <span className={`font-medium ${rate > 3 ? 'text-green-600' : rate > 1 ? 'text-yellow-600' : 'text-red-600'}`}>
              {rate.toFixed(1)}%
            </span>
          </div>
        );
      },
      sortingFn: (rowA, rowB) => {
        const aRate = calculateEngagementRate(rowA.original);
        const bRate = calculateEngagementRate(rowB.original);
        return aRate - bRate;
      },
    },
    {
      accessorKey: "publishedAt",
      header: "Published",
      cell: ({ row }) => {
        const publishedAt = row.original.publishedAt || row.original.publishAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(publishedAt)}
          </div>
        );
      },
    },
    {
      id: "activity",
      header: "Activity",
      cell: ({ row }) => {
        const activity = row.original.linkedActivity;
        return (
          <div className="text-sm">
            <div className="font-medium">{activity.name}</div>
            <Badge variant="outline" className="text-xs">
              {activity.type}
            </Badge>
          </div>
        );
      },
      enableHiding: !!activityId, // Hide if filtering by specific activity
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const post = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open post analytics
                console.log('View analytics:', post);
              }}
            >
              <BarChart3 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open original post
                console.log('View post:', post);
              }}
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], [activityId]);

  const tableActions = [
    {
      label: "Export Data",
      icon: Download,
      onClick: (selectedRows: SocialPost[]) => {
        console.log('Export performance data:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Performance data export will be available soon."
        });
      },
      variant: "outline" as const,
    },
    {
      label: "Analyze Selected",
      icon: BarChart3,
      onClick: (selectedRows: SocialPost[]) => {
        console.log('Analyze posts:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Detailed analytics will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
  ];

  return (
    <AdvancedDataTable
      data={posts}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadPosts}
      tableActions={tableActions}
      searchPlaceholder="Search posts by content..."
      emptyStateMessage="No performance data found"
      emptyStateDescription="Posts need to be published to show performance metrics."
    />
  );
}
