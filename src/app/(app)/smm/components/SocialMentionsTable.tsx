"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import {
  MessageSquare,
  Reply,
  Heart,
  Share,
  Eye,
  AlertTriangle,
  CheckCircle,
  Archive,
  Star,
  Linkedin,
  Twitter,
  Facebook,
  Instagram,
  TrendingUp,
  TrendingDown,
  Minus,
  Clock,
  User,
  Hash
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { SocialMention, SocialPlatform } from '@/types/firestore';
import { getSocialMentionsAction, respondToMentionAction, updateMentionStatusAction } from '../actions';
import { formatDistanceToNow } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

const platformIcons = {
  linkedin: Linkedin,
  twitter: Twitter,
  facebook: Facebook,
  instagram: Instagram,
};

const sentimentConfig = {
  'positive': { color: 'bg-green-100 text-green-800', icon: TrendingUp },
  'negative': { color: 'bg-red-100 text-red-800', icon: TrendingDown },
  'neutral': { color: 'bg-gray-100 text-gray-800', icon: Minus },
};

const priorityConfig = {
  'low': { color: 'bg-blue-100 text-blue-800' },
  'medium': { color: 'bg-yellow-100 text-yellow-800' },
  'high': { color: 'bg-orange-100 text-orange-800' },
  'urgent': { color: 'bg-red-100 text-red-800' },
};

const statusConfig = {
  'unread': { color: 'bg-gray-100 text-gray-800', icon: Eye },
  'read': { color: 'bg-blue-100 text-blue-800', icon: CheckCircle },
  'responded': { color: 'bg-green-100 text-green-800', icon: Reply },
  'escalated': { color: 'bg-orange-100 text-orange-800', icon: AlertTriangle },
  'archived': { color: 'bg-gray-100 text-gray-600', icon: Archive },
};

const typeConfig = {
  'mention': { label: 'Mention', icon: User },
  'hashtag': { label: 'Hashtag', icon: Hash },
  'comment': { label: 'Comment', icon: MessageSquare },
  'dm': { label: 'Direct Message', icon: MessageSquare },
  'review': { label: 'Review', icon: Star },
};

interface SocialMentionsTableProps {
  streamId?: string;
  showStreamFilter?: boolean;
}

export default function SocialMentionsTable({ streamId, showStreamFilter = true }: SocialMentionsTableProps) {
  const { toast } = useToast();
  const [mentions, setMentions] = useState<SocialMention[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const loadMentions = React.useCallback(async () => {
    setIsLoading(true);
    try {
      const filters = streamId ? { streamId } : undefined;
      const fetchedMentions = await getSocialMentionsAction(filters);
      setMentions(fetchedMentions);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load social mentions.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  }, [streamId, toast]);

  // Load social mentions
  useEffect(() => {
    loadMentions();
  }, [loadMentions]);

  const handleStatusUpdate = async (mentionId: string, status: SocialMention['status']) => {
    try {
      await updateMentionStatusAction(mentionId, status);
      await loadMentions();
      toast({
        title: "Success",
        description: "Mention status updated successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update mention status.",
        variant: "destructive"
      });
    }
  };

  const handleRespond = async (mentionId: string, response: string) => {
    try {
      await respondToMentionAction(mentionId, response);
      await loadMentions();
      toast({
        title: "Success",
        description: "Response sent successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send response.",
        variant: "destructive"
      });
    }
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const columns: ColumnDef<SocialMention>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "platform",
      header: "Platform",
      cell: ({ row }) => {
        const platform = row.original.platform;
        const PlatformIcon = platformIcons[platform];
        
        return (
          <div className="flex items-center gap-2">
            <PlatformIcon className="h-4 w-4" />
            <span className="capitalize">{platform}</span>
          </div>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }) => {
        const type = row.original.type;
        const config = typeConfig[type];
        const TypeIcon = config.icon;
        
        return (
          <Badge variant="outline" className="flex items-center gap-1 w-fit">
            <TypeIcon className="h-3 w-3" />
            {config.label}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: "author",
      header: "Author",
      cell: ({ row }) => {
        const author = row.original.author;
        
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={author.avatarUrl} />
              <AvatarFallback>
                {author.displayName.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="font-medium text-sm">{author.displayName}</span>
              <span className="text-xs text-gray-500">@{author.username}</span>
            </div>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "content",
      header: "Content",
      cell: ({ row }) => {
        const content = row.original.content;
        
        return (
          <div className="max-w-xs">
            <p className="text-sm text-gray-600 line-clamp-2">
              {content.text}
            </p>
            {content.imageUrls && content.imageUrls.length > 0 && (
              <Badge variant="outline" className="mt-1 text-xs">
                {content.imageUrls.length} image(s)
              </Badge>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "sentiment",
      header: "Sentiment",
      cell: ({ row }) => {
        const sentiment = row.original.sentiment;
        const config = sentimentConfig[sentiment];
        const SentimentIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <SentimentIcon className="h-3 w-3" />
            {sentiment}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "priority",
      header: "Priority",
      cell: ({ row }) => {
        const priority = row.original.priority;
        const config = priorityConfig[priority];
        
        return (
          <Badge className={`${config.color} capitalize`}>
            {priority}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.status;
        const config = statusConfig[status];
        const StatusIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <StatusIcon className="h-3 w-3" />
            {status.replace('-', ' ')}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: "engagement",
      header: "Engagement",
      cell: ({ row }) => {
        const engagement = row.original.engagement;
        
        return (
          <div className="flex items-center gap-3 text-sm">
            <div className="flex items-center gap-1">
              <Heart className="h-3 w-3" />
              <span>{engagement.likes}</span>
            </div>
            <div className="flex items-center gap-1">
              <MessageSquare className="h-3 w-3" />
              <span>{engagement.comments}</span>
            </div>
            <div className="flex items-center gap-1">
              <Share className="h-3 w-3" />
              <span>{engagement.shares}</span>
            </div>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "createdAt",
      header: "Date",
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(createdAt)}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const mention = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleStatusUpdate(mention.id!, 'read')}
              disabled={mention.status === 'read'}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open response dialog
                const response = prompt('Enter your response:');
                if (response) {
                  handleRespond(mention.id!, response);
                }
              }}
            >
              <Reply className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleStatusUpdate(mention.id!, 'archived')}
            >
              <Archive className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], []);

  const tableActions = [
    {
      label: "Mark as Read",
      icon: Eye,
      onClick: async (selectedRows: SocialMention[]) => {
        try {
          await Promise.all(selectedRows.map(mention =>
            updateMentionStatusAction(mention.id!, 'read')
          ));
          await loadMentions();
          toast({
            title: "Success",
            description: `${selectedRows.length} mentions marked as read.`
          });
        } catch (error) {
          toast({
            title: "Error",
            description: "Failed to update mention status.",
            variant: "destructive"
          });
        }
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Archive Selected",
      icon: Archive,
      onClick: async (selectedRows: SocialMention[]) => {
        try {
          await Promise.all(selectedRows.map(mention =>
            updateMentionStatusAction(mention.id!, 'archived')
          ));
          await loadMentions();
          toast({
            title: "Success",
            description: `${selectedRows.length} mentions archived.`
          });
        } catch (error) {
          toast({
            title: "Error",
            description: "Failed to archive mentions.",
            variant: "destructive"
          });
        }
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    {
      key: 'platform',
      label: 'Platform',
      options: [
        { label: 'LinkedIn', value: 'linkedin' },
        { label: 'Twitter', value: 'twitter' },
        { label: 'Facebook', value: 'facebook' },
        { label: 'Instagram', value: 'instagram' },
      ]
    },
    {
      key: 'sentiment',
      label: 'Sentiment',
      options: [
        { label: 'Positive', value: 'positive' },
        { label: 'Negative', value: 'negative' },
        { label: 'Neutral', value: 'neutral' },
      ]
    },
    {
      key: 'priority',
      label: 'Priority',
      options: [
        { label: 'Low', value: 'low' },
        { label: 'Medium', value: 'medium' },
        { label: 'High', value: 'high' },
        { label: 'Urgent', value: 'urgent' },
      ]
    },
    {
      key: 'status',
      label: 'Status',
      options: [
        { label: 'Unread', value: 'unread' },
        { label: 'Read', value: 'read' },
        { label: 'Responded', value: 'responded' },
        { label: 'Escalated', value: 'escalated' },
        { label: 'Archived', value: 'archived' },
      ]
    },
    {
      key: 'type',
      label: 'Type',
      options: [
        { label: 'Mention', value: 'mention' },
        { label: 'Hashtag', value: 'hashtag' },
        { label: 'Comment', value: 'comment' },
        { label: 'Direct Message', value: 'dm' },
        { label: 'Review', value: 'review' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={mentions}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadMentions}
      tableActions={tableActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search mentions by content, author..."
      emptyStateMessage="No social mentions found"
      emptyStateDescription="Set up monitoring streams to start tracking mentions."
    />
  );
}
