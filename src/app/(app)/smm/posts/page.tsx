"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Share2, PlusCircle, BarChart3 } from "lucide-react";
import Link from 'next/link';
import SocialPostsTable from '../components/SocialPostsTable';

export default function SocialPostsPage() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight font-headline flex items-center">
            <Share2 className="mr-3 h-8 w-8 text-primary" />
            Social Media Posts
          </h1>
          <p className="text-muted-foreground">
            Manage all your social media posts across platforms in one place.
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href="/smm/analytics">
              <BarChart3 className="mr-2 h-4 w-4" /> Analytics
            </Link>
          </Button>
          <Button asChild>
            <Link href="/smm/composer">
              <PlusCircle className="mr-2 h-4 w-4" /> New Post
            </Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Social Posts</CardTitle>
        </CardHeader>
        <CardContent>
          <SocialPostsTable showActivityFilter={true} />
        </CardContent>
      </Card>
    </div>
  );
}
