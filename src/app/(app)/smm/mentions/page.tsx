"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { MessageSquare, Settings, PlusCircle } from "lucide-react";
import Link from 'next/link';
import SocialMentionsTable from '../components/SocialMentionsTable';

export default function SocialMentionsPage() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight font-headline flex items-center">
            <MessageSquare className="mr-3 h-8 w-8 text-primary" />
            Social Mentions & Engagement
          </h1>
          <p className="text-muted-foreground">
            Monitor and respond to mentions, comments, and conversations about your brand.
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href="/smm?tab=monitoring">
              <Settings className="mr-2 h-4 w-4" /> Monitoring Settings
            </Link>
          </Button>
          <Button asChild>
            <Link href="/smm/composer">
              <PlusCircle className="mr-2 h-4 w-4" /> Create Post
            </Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Social Mentions</CardTitle>
        </CardHeader>
        <CardContent>
          <SocialMentionsTable showStreamFilter={true} />
        </CardContent>
      </Card>
    </div>
  );
}
