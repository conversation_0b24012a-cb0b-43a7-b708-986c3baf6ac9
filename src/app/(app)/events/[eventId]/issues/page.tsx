"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertCircle, Plus, ArrowLeft, AlertTriangle } from "lucide-react";
import Link from 'next/link';
import { useParams } from 'next/navigation';
import EventIssuesTable from '../../components/EventIssuesTable';

export default function EventIssuesPage() {
  const params = useParams();
  const eventId = params.eventId as string;

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button asChild variant="ghost" size="sm">
              <Link href={`/events/${eventId}`}>
                <ArrowLeft className="mr-2 h-4 w-4" /> Back to Event
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold tracking-tight font-headline flex items-center">
            <AlertCircle className="mr-3 h-8 w-8 text-primary" />
            Issue Tracking
          </h1>
          <p className="text-muted-foreground">
            Track and resolve issues that arise during event planning and execution.
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href={`/events/${eventId}/risks`}>
              <AlertTriangle className="mr-2 h-4 w-4" /> View Risks
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/events/${eventId}/issues/report`}>
              <Plus className="mr-2 h-4 w-4" /> Report Issue
            </Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Event Issue Tracker</CardTitle>
        </CardHeader>
        <CardContent>
          <EventIssuesTable eventId={eventId} />
        </CardContent>
      </Card>
    </div>
  );
}
