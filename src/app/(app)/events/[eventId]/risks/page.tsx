"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle, Plus, ArrowLeft, Shield } from "lucide-react";
import Link from 'next/link';
import { useParams } from 'next/navigation';
import EventRisksTable from '../../components/EventRisksTable';

export default function EventRisksPage() {
  const params = useParams();
  const eventId = params.eventId as string;

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button asChild variant="ghost" size="sm">
              <Link href={`/events/${eventId}`}>
                <ArrowLeft className="mr-2 h-4 w-4" /> Back to Event
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold tracking-tight font-headline flex items-center">
            <AlertTriangle className="mr-3 h-8 w-8 text-primary" />
            Risk Management
          </h1>
          <p className="text-muted-foreground">
            Identify, assess, and mitigate potential risks for this event.
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href={`/events/${eventId}/issues`}>
              <Shield className="mr-2 h-4 w-4" /> View Issues
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/events/${eventId}/risks/add`}>
              <Plus className="mr-2 h-4 w-4" /> Add Risk
            </Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Event Risk Register</CardTitle>
        </CardHeader>
        <CardContent>
          <EventRisksTable eventId={eventId} />
        </CardContent>
      </Card>
    </div>
  );
}
