"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Users, UserPlus, Download, ArrowLeft } from "lucide-react";
import Link from 'next/link';
import { useParams } from 'next/navigation';
import EventAttendeesTable from '../../components/EventAttendeesTable';

export default function EventAttendeesPage() {
  const params = useParams();
  const eventId = params.eventId as string;

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button asChild variant="ghost" size="sm">
              <Link href={`/events/${eventId}`}>
                <ArrowLeft className="mr-2 h-4 w-4" /> Back to Event
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold tracking-tight font-headline flex items-center">
            <Users className="mr-3 h-8 w-8 text-primary" />
            Event Attendees
          </h1>
          <p className="text-muted-foreground">
            Manage attendees, invitations, and participant information for this event.
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href={`/events/${eventId}/attendees/import`}>
              <Download className="mr-2 h-4 w-4" /> Import
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/events/${eventId}/attendees/add`}>
              <UserPlus className="mr-2 h-4 w-4" /> Add Attendee
            </Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Attendee Management</CardTitle>
        </CardHeader>
        <CardContent>
          <EventAttendeesTable eventId={eventId} />
        </CardContent>
      </Card>
    </div>
  );
}
