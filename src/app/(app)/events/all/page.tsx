"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar, PlusCircle, BarChart3, Users, ArrowLeft } from "lucide-react";
import Link from 'next/link';
import EventsTable from '../components/EventsTable';

export default function AllEventsPage() {
  return (
    <div className="flex flex-col gap-6">
      {/* Clean Page Header - Option 2 with Back Button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="text-primary">
            <Calendar className="h-8 w-8" />
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">All Events</h1>
            <p className="text-muted-foreground">Manage all your events across exhibitions and venues in one place.</p>
          </div>
        </div>

        {/* Primary Action Only - No Filter */}
        <div className="flex items-center gap-2">
          <button
            onClick={() => window.location.href = '/events/add'}
            className="group relative inline-flex items-center overflow-hidden rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden"
          >
            <span className="absolute -start-full transition-all group-hover:start-2">
              <PlusCircle className="size-4" />
            </span>
            <span className="text-sm font-medium transition-all group-hover:ms-2">New Event</span>
          </button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Event Management</CardTitle>
        </CardHeader>
        <CardContent>
          <EventsTable showExhibitionFilter={true} />
        </CardContent>
      </Card>
    </div>
  );
}
