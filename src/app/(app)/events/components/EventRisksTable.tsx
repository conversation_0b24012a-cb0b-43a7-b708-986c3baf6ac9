"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { 
  AlertTriangle,
  Edit,
  Trash2,
  Plus,
  CheckCircle,
  Clock,
  Play,
  XCircle,
  TrendingUp,
  TrendingDown,
  Minus,
  Eye,
  User
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { EventRisk } from '@/types/firestore';
import { getEventRisks, deleteEventRisk } from '@/services/firestoreService';
import { formatDistanceToNow } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

const statusConfig = {
  'Open': { color: 'bg-red-100 text-red-800', icon: AlertTriangle },
  'In Progress': { color: 'bg-yellow-100 text-yellow-800', icon: Play },
  'Mitigated': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
  'Closed': { color: 'bg-gray-100 text-gray-800', icon: XCircle },
};

const likelihoodConfig = {
  'Low': { color: 'bg-green-100 text-green-800', icon: TrendingDown },
  'Medium': { color: 'bg-yellow-100 text-yellow-800', icon: Minus },
  'High': { color: 'bg-red-100 text-red-800', icon: TrendingUp },
};

const impactConfig = {
  'Low': { color: 'bg-green-100 text-green-800', icon: TrendingDown },
  'Medium': { color: 'bg-yellow-100 text-yellow-800', icon: Minus },
  'High': { color: 'bg-red-100 text-red-800', icon: TrendingUp },
};

interface EventRisksTableProps {
  eventId: string;
}

export default function EventRisksTable({ eventId }: EventRisksTableProps) {
  const { toast } = useToast();
  const [risks, setRisks] = useState<EventRisk[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load event risks
  useEffect(() => {
    loadRisks();
  }, [eventId]);

  const loadRisks = async () => {
    setIsLoading(true);
    try {
      const fetchedRisks = await getEventRisks(eventId);
      setRisks(fetchedRisks);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load event risks.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const handleDelete = async (riskId: string) => {
    if (!confirm('Are you sure you want to delete this risk?')) return;
    
    try {
      await deleteEventRisk(eventId, riskId);
      await loadRisks();
      toast({
        title: "Success",
        description: "Risk deleted successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete risk.",
        variant: "destructive"
      });
    }
  };

  const calculateRiskScore = (likelihood: string, impact: string) => {
    const likelihoodScore = { 'Low': 1, 'Medium': 2, 'High': 3 }[likelihood] || 1;
    const impactScore = { 'Low': 1, 'Medium': 2, 'High': 3 }[impact] || 1;
    return likelihoodScore * impactScore;
  };

  const getRiskScoreColor = (score: number) => {
    if (score <= 2) return 'bg-green-100 text-green-800';
    if (score <= 4) return 'bg-yellow-100 text-yellow-800';
    if (score <= 6) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const columns: ColumnDef<EventRisk>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "riskDescription",
      header: "Risk Description",
      cell: ({ row }) => {
        const risk = row.original;
        
        return (
          <div className="max-w-xs">
            <p className="font-medium line-clamp-2">
              {risk.riskDescription}
            </p>
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.status;
        const config = statusConfig[status];
        const StatusIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <StatusIcon className="h-3 w-3" />
            {status}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "likelihood",
      header: "Likelihood",
      cell: ({ row }) => {
        const likelihood = row.original.likelihood;
        const config = likelihoodConfig[likelihood];
        const LikelihoodIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <LikelihoodIcon className="h-3 w-3" />
            {likelihood}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "impact",
      header: "Impact",
      cell: ({ row }) => {
        const impact = row.original.impact;
        const config = impactConfig[impact];
        const ImpactIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <ImpactIcon className="h-3 w-3" />
            {impact}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: "riskScore",
      header: "Risk Score",
      cell: ({ row }) => {
        const risk = row.original;
        const score = calculateRiskScore(risk.likelihood, risk.impact);
        const colorClass = getRiskScoreColor(score);
        
        return (
          <Badge className={`${colorClass} font-bold`}>
            {score}/9
          </Badge>
        );
      },
      sortingFn: (rowA, rowB) => {
        const scoreA = calculateRiskScore(rowA.original.likelihood, rowA.original.impact);
        const scoreB = calculateRiskScore(rowB.original.likelihood, rowB.original.impact);
        return scoreA - scoreB;
      },
    },
    {
      id: "mitigation",
      header: "Mitigation Plan",
      cell: ({ row }) => {
        const mitigationPlan = row.original.mitigationPlan;
        
        return (
          <div className="max-w-xs">
            <p className="text-sm text-gray-600 line-clamp-2">
              {mitigationPlan}
            </p>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "owner",
      header: "Owner",
      cell: ({ row }) => {
        const risk = row.original;
        const ownerName = risk.ownerName;
        
        if (!ownerName || ownerName === '_UNASSIGNED_') {
          return <span className="text-gray-400">Unassigned</span>;
        }
        
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-6 w-6">
              <AvatarFallback className="text-xs">
                {ownerName.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm">{ownerName}</span>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(createdAt)}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const risk = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open risk details
                console.log('View risk:', risk);
              }}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open edit dialog
                console.log('Edit risk:', risk);
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDelete(risk.id!)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], [eventId]);

  const tableActions = [
    {
      label: "Add Risk",
      icon: Plus,
      onClick: () => {
        // Open add risk dialog
        console.log('Add new risk');
      },
      variant: "default" as const,
    },
    {
      label: "Close Selected",
      icon: CheckCircle,
      onClick: async (selectedRows: EventRisk[]) => {
        console.log('Close risks:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Bulk risk status updates will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Delete Selected",
      icon: Trash2,
      onClick: async (selectedRows: EventRisk[]) => {
        if (confirm(`Delete ${selectedRows.length} selected risks?`)) {
          try {
            await Promise.all(selectedRows.map(risk => deleteEventRisk(eventId, risk.id!)));
            await loadRisks();
            toast({
              title: "Success",
              description: `${selectedRows.length} risks deleted successfully.`
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to delete selected risks.",
              variant: "destructive"
            });
          }
        }
      },
      variant: "destructive" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    {
      key: 'status',
      label: 'Status',
      options: [
        { label: 'Open', value: 'Open' },
        { label: 'In Progress', value: 'In Progress' },
        { label: 'Mitigated', value: 'Mitigated' },
        { label: 'Closed', value: 'Closed' },
      ]
    },
    {
      key: 'likelihood',
      label: 'Likelihood',
      options: [
        { label: 'Low', value: 'Low' },
        { label: 'Medium', value: 'Medium' },
        { label: 'High', value: 'High' },
      ]
    },
    {
      key: 'impact',
      label: 'Impact',
      options: [
        { label: 'Low', value: 'Low' },
        { label: 'Medium', value: 'Medium' },
        { label: 'High', value: 'High' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={risks}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadRisks}
      tableActions={tableActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search risks by description, mitigation plan..."
      emptyStateMessage="No risks identified"
      emptyStateDescription="Add risks to start managing potential event issues."
    />
  );
}
