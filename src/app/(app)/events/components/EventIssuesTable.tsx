"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { 
  AlertCircle,
  Edit,
  Trash2,
  Plus,
  CheckCircle,
  Clock,
  Play,
  XCircle,
  TrendingUp,
  TrendingDown,
  Minus,
  Eye,
  MessageSquare,
  User
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { EventIssue } from '@/types/firestore';
import { getEventIssues, deleteEventIssue } from '@/services/firestoreService';
import { formatDistanceToNow, format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

const statusConfig = {
  'Open': { color: 'bg-red-100 text-red-800', icon: AlertCircle },
  'In Progress': { color: 'bg-yellow-100 text-yellow-800', icon: Play },
  'Resolved': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
  'Closed': { color: 'bg-gray-100 text-gray-800', icon: XCircle },
};

const priorityConfig = {
  'Low': { color: 'bg-green-100 text-green-800', icon: TrendingDown },
  'Medium': { color: 'bg-yellow-100 text-yellow-800', icon: Minus },
  'High': { color: 'bg-orange-100 text-orange-800', icon: TrendingUp },
  'Critical': { color: 'bg-red-100 text-red-800', icon: AlertCircle },
};

const categoryConfig = {
  'Technical': { color: 'bg-blue-100 text-blue-800' },
  'Logistics': { color: 'bg-purple-100 text-purple-800' },
  'Venue': { color: 'bg-green-100 text-green-800' },
  'Catering': { color: 'bg-orange-100 text-orange-800' },
  'Security': { color: 'bg-red-100 text-red-800' },
  'Communication': { color: 'bg-indigo-100 text-indigo-800' },
  'Other': { color: 'bg-gray-100 text-gray-800' },
};

interface EventIssuesTableProps {
  eventId: string;
}

export default function EventIssuesTable({ eventId }: EventIssuesTableProps) {
  const { toast } = useToast();
  const [issues, setIssues] = useState<EventIssue[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load event issues
  useEffect(() => {
    loadIssues();
  }, [eventId]);

  const loadIssues = async () => {
    setIsLoading(true);
    try {
      const fetchedIssues = await getEventIssues(eventId);
      setIssues(fetchedIssues);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load event issues.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const handleDelete = async (issueId: string) => {
    if (!confirm('Are you sure you want to delete this issue?')) return;
    
    try {
      await deleteEventIssue(eventId, issueId);
      await loadIssues();
      toast({
        title: "Success",
        description: "Issue deleted successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete issue.",
        variant: "destructive"
      });
    }
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const formatDateTime = (date: any) => {
    if (!date) return 'Not set';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return format(dateObj, 'MMM dd, yyyy HH:mm');
  };

  const columns: ColumnDef<EventIssue>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "title",
      header: "Issue Title",
      cell: ({ row }) => {
        const issue = row.original;
        
        return (
          <div className="max-w-xs">
            <p className="font-medium line-clamp-2">
              {issue.title}
            </p>
            {issue.description && (
              <p className="text-sm text-gray-500 line-clamp-1 mt-1">
                {issue.description}
              </p>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.status;
        const config = statusConfig[status];
        const StatusIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <StatusIcon className="h-3 w-3" />
            {status}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "priority",
      header: "Priority",
      cell: ({ row }) => {
        const priority = row.original.priority;
        const config = priorityConfig[priority];
        const PriorityIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <PriorityIcon className="h-3 w-3" />
            {priority}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: ({ row }) => {
        const category = row.original.category;
        const config = categoryConfig[category] || categoryConfig['Other'];
        
        return (
          <Badge className={`${config.color} capitalize`}>
            {category}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: "assignee",
      header: "Assignee",
      cell: ({ row }) => {
        const issue = row.original;
        const assigneeName = issue.assignedTo;
        
        if (!assigneeName || assigneeName === '_UNASSIGNED_') {
          return <span className="text-gray-400">Unassigned</span>;
        }
        
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-6 w-6">
              <AvatarFallback className="text-xs">
                {assigneeName.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm">{assigneeName}</span>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "dueDate",
      header: "Due Date",
      cell: ({ row }) => {
        const dueDate = row.original.dueDate;
        
        if (!dueDate) {
          return <span className="text-gray-400">Not set</span>;
        }
        
        const isOverdue = new Date(dueDate) < new Date();
        
        return (
          <div className={`text-sm ${isOverdue ? 'text-red-600' : 'text-gray-600'}`}>
            {formatDateTime(dueDate)}
            {isOverdue && (
              <Badge variant="destructive" className="ml-2 text-xs">
                Overdue
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "reportedBy",
      header: "Reporter",
      cell: ({ row }) => {
        const reportedBy = row.original.reportedBy;
        
        if (!reportedBy) {
          return <span className="text-gray-400">Unknown</span>;
        }
        
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-6 w-6">
              <AvatarFallback className="text-xs">
                {reportedBy.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm">{reportedBy}</span>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "createdAt",
      header: "Reported",
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(createdAt)}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const issue = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open issue details
                console.log('View issue:', issue);
              }}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open edit dialog
                console.log('Edit issue:', issue);
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open comments/updates
                console.log('View comments:', issue);
              }}
            >
              <MessageSquare className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDelete(issue.id!)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], [eventId]);

  const tableActions = [
    {
      label: "Report Issue",
      icon: Plus,
      onClick: () => {
        // Open add issue dialog
        console.log('Report new issue');
      },
      variant: "default" as const,
    },
    {
      label: "Resolve Selected",
      icon: CheckCircle,
      onClick: async (selectedRows: EventIssue[]) => {
        console.log('Resolve issues:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Bulk issue resolution will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Assign Selected",
      icon: User,
      onClick: async (selectedRows: EventIssue[]) => {
        console.log('Assign issues:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Bulk issue assignment will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Delete Selected",
      icon: Trash2,
      onClick: async (selectedRows: EventIssue[]) => {
        if (confirm(`Delete ${selectedRows.length} selected issues?`)) {
          try {
            await Promise.all(selectedRows.map(issue => deleteEventIssue(eventId, issue.id!)));
            await loadIssues();
            toast({
              title: "Success",
              description: `${selectedRows.length} issues deleted successfully.`
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to delete selected issues.",
              variant: "destructive"
            });
          }
        }
      },
      variant: "destructive" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    {
      key: 'status',
      label: 'Status',
      options: [
        { label: 'Open', value: 'Open' },
        { label: 'In Progress', value: 'In Progress' },
        { label: 'Resolved', value: 'Resolved' },
        { label: 'Closed', value: 'Closed' },
      ]
    },
    {
      key: 'priority',
      label: 'Priority',
      options: [
        { label: 'Low', value: 'Low' },
        { label: 'Medium', value: 'Medium' },
        { label: 'High', value: 'High' },
        { label: 'Critical', value: 'Critical' },
      ]
    },
    {
      key: 'category',
      label: 'Category',
      options: [
        { label: 'Technical', value: 'Technical' },
        { label: 'Logistics', value: 'Logistics' },
        { label: 'Venue', value: 'Venue' },
        { label: 'Catering', value: 'Catering' },
        { label: 'Security', value: 'Security' },
        { label: 'Communication', value: 'Communication' },
        { label: 'Other', value: 'Other' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={issues}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadIssues}
      tableActions={tableActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search issues by title, description, assignee..."
      emptyStateMessage="No issues reported"
      emptyStateDescription="Report issues to track and resolve event problems."
    />
  );
}
