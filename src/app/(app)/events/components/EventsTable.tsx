"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { 
  Calendar,
  Edit,
  Eye,
  Trash2,
  Users,
  MapPin,
  Clock,
  DollarSign,
  Target,
  CheckCircle,
  AlertTriangle,
  Play,
  Pause,
  Copy,
  Plus,
  BarChart3
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { Event } from '@/types/firestore';
import { getEvents, deleteDocument } from '@/services/firestoreService';
import { formatDistanceToNow, format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import { useRouter } from 'next/navigation';

const statusConfig = {
  'Planning': { color: 'bg-blue-100 text-blue-800', icon: Clock },
  'Confirmed': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
  'In Progress': { color: 'bg-yellow-100 text-yellow-800', icon: Play },
  'Completed': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
  'Cancelled': { color: 'bg-red-100 text-red-800', icon: AlertTriangle },
  '': { color: 'bg-gray-100 text-gray-800', icon: Clock },
};

const locationTypeConfig = {
  'Physical': { color: 'bg-blue-100 text-blue-800', icon: MapPin },
  'Virtual': { color: 'bg-purple-100 text-purple-800', icon: Users },
  'Hybrid': { color: 'bg-orange-100 text-orange-800', icon: Users },
  '': { color: 'bg-gray-100 text-gray-800', icon: MapPin },
};

interface EventsTableProps {
  exhibitionId?: string;
  showExhibitionFilter?: boolean;
}

export default function EventsTable({ exhibitionId, showExhibitionFilter = true }: EventsTableProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [events, setEvents] = useState<Event[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load events
  useEffect(() => {
    loadEvents();
  }, [exhibitionId]);

  const loadEvents = async () => {
    setIsLoading(true);
    try {
      const fetchedEvents = await getEvents();
      
      // Filter by exhibition if specified
      const filteredEvents = exhibitionId 
        ? fetchedEvents.filter(event => event.linkedExhibitionId === exhibitionId || event.parentExhibitionId === exhibitionId)
        : fetchedEvents;
      
      setEvents(filteredEvents);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load events.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const handleDelete = async (eventId: string) => {
    if (!confirm('Are you sure you want to delete this event?')) return;
    
    try {
      await deleteDocument('events', eventId);
      await loadEvents();
      toast({
        title: "Success",
        description: "Event deleted successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete event.",
        variant: "destructive"
      });
    }
  };

  const handleDuplicate = async (event: Event) => {
    try {
      // This would be implemented in actions
      console.log('Duplicate event:', event);
      toast({
        title: "Feature Coming Soon",
        description: "Event duplication will be available soon."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate event.",
        variant: "destructive"
      });
    }
  };

  const formatDate = (date: any) => {
    if (!date) return 'Not set';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return format(dateObj, 'MMM dd, yyyy');
  };

  const formatCurrency = (amount: number | undefined, currency: string = 'USD') => {
    if (!amount) return 'Not set';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const columns: ColumnDef<Event>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "eventName",
      header: "Event Name",
      cell: ({ row }) => {
        const event = row.original;
        
        return (
          <div className="flex flex-col">
            <span className="font-medium">{event.eventName}</span>
            {event.eventType && (
              <Badge variant="outline" className="w-fit text-xs mt-1">
                {event.eventType}
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "eventStatus",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.eventStatus || '';
        const config = statusConfig[status];
        const StatusIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <StatusIcon className="h-3 w-3" />
            {status || 'Not Set'}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id) || '');
      },
    },
    {
      accessorKey: "locationType",
      header: "Type",
      cell: ({ row }) => {
        const locationType = row.original.locationType || '';
        const config = locationTypeConfig[locationType];
        const LocationIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <LocationIcon className="h-3 w-3" />
            {locationType || 'Not Set'}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id) || '');
      },
    },
    {
      id: "dateRange",
      header: "Date Range",
      cell: ({ row }) => {
        const event = row.original;
        const startDate = formatDate(event.startDate);
        const endDate = formatDate(event.endDate);
        
        return (
          <div className="text-sm">
            <div>{startDate}</div>
            {startDate !== endDate && (
              <div className="text-gray-500">to {endDate}</div>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "location",
      header: "Location",
      cell: ({ row }) => {
        const event = row.original;
        
        if (event.locationType === 'Virtual') {
          return (
            <div className="text-sm text-blue-600">
              Virtual Event
            </div>
          );
        }
        
        return (
          <div className="text-sm max-w-xs truncate">
            {event.venueName || event.address || 'Not specified'}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "attendance",
      header: "Attendance",
      cell: ({ row }) => {
        const event = row.original;
        const estimated = event.estimatedAttendance || 0;
        const actual = event.actualAttendance || 0;
        
        return (
          <div className="text-sm">
            <div className="flex items-center gap-1">
              <Users className="h-3 w-3" />
              <span>{actual > 0 ? actual : estimated}</span>
            </div>
            {actual > 0 && estimated > 0 && (
              <div className="text-xs text-gray-500">
                {Math.round((actual / estimated) * 100)}% of target
              </div>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "budget",
      header: "Budget",
      cell: ({ row }) => {
        const event = row.original;
        const estimated = event.estimatedBudget;
        const actual = event.actualSpending;
        const currency = event.currency || 'USD';
        
        return (
          <div className="text-sm">
            <div className="flex items-center gap-1">
              <DollarSign className="h-3 w-3" />
              <span>{formatCurrency(actual || estimated, currency)}</span>
            </div>
            {actual && estimated && actual !== estimated && (
              <div className={`text-xs ${actual > estimated ? 'text-red-500' : 'text-green-500'}`}>
                {actual > estimated ? 'Over' : 'Under'} budget
              </div>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "eventOwner",
      header: "Owner",
      cell: ({ row }) => {
        const owner = row.original.eventOwner;
        
        if (!owner) {
          return <span className="text-gray-400">Unassigned</span>;
        }
        
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-6 w-6">
              <AvatarFallback className="text-xs">
                {owner.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm">{owner}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        if (!createdAt) return 'Unknown';
        
        let dateObj: Date;
        if (createdAt instanceof Timestamp) {
          dateObj = createdAt.toDate();
        } else if (typeof createdAt === 'string') {
          dateObj = new Date(createdAt);
        } else {
          dateObj = createdAt;
        }
        
        return (
          <div className="text-sm text-gray-600">
            {formatDistanceToNow(dateObj, { addSuffix: true })}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const event = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push(`/events/${event.id}`)}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push(`/events/${event.id}/edit`)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDuplicate(event)}
            >
              <Copy className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDelete(event.id!)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], [router]);

  const tableActions = [
    {
      label: "Create Event",
      icon: Plus,
      onClick: () => router.push('/events/add'),
      variant: "default" as const,
    },
    {
      label: "Duplicate Selected",
      icon: Copy,
      onClick: async (selectedRows: Event[]) => {
        console.log('Duplicate events:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Bulk event duplication will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "View Analytics",
      icon: BarChart3,
      onClick: (selectedRows: Event[]) => {
        if (selectedRows.length === 1) {
          router.push(`/events/${selectedRows[0].id}/analytics`);
        } else {
          router.push('/events/analytics');
        }
      },
      variant: "outline" as const,
    },
    {
      label: "Delete Selected",
      icon: Trash2,
      onClick: async (selectedRows: Event[]) => {
        if (confirm(`Delete ${selectedRows.length} selected events?`)) {
          try {
            await Promise.all(selectedRows.map(event => deleteDocument('events', event.id!)));
            await loadEvents();
            toast({
              title: "Success",
              description: `${selectedRows.length} events deleted successfully.`
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to delete selected events.",
              variant: "destructive"
            });
          }
        }
      },
      variant: "destructive" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    {
      key: 'eventStatus',
      label: 'Status',
      options: [
        { label: 'Planning', value: 'Planning' },
        { label: 'Confirmed', value: 'Confirmed' },
        { label: 'In Progress', value: 'In Progress' },
        { label: 'Completed', value: 'Completed' },
        { label: 'Cancelled', value: 'Cancelled' },
        { label: 'Not Set', value: '' },
      ]
    },
    {
      key: 'locationType',
      label: 'Location Type',
      options: [
        { label: 'Physical', value: 'Physical' },
        { label: 'Virtual', value: 'Virtual' },
        { label: 'Hybrid', value: 'Hybrid' },
        { label: 'Not Set', value: '' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={events}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadEvents}
      tableActions={tableActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search events by name, type, owner..."
      emptyStateMessage="No events found"
      emptyStateDescription="Create your first event to get started."
    />
  );
}
