"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { 
  Mail,
  Phone,
  Edit,
  Trash2,
  MessageSquare,
  UserPlus,
  Users,
  Building,
  CheckCircle,
  Clock,
  XCircle,
  AlertTriangle,
  Send,
  Eye,
  Download
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { EventAttendee, InvitationStatus, AttendeeCategory } from '@/types/firestore';
import { getEventAttendees, deleteEventAttendeeFromFirestore } from '@/services/firestoreService';
import { deleteEventAttendeeAction } from '../actions';
import { formatDistanceToNow } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

const statusConfig = {
  'Invited': { color: 'bg-blue-100 text-blue-800', icon: Send },
  'Confirmed': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
  'Declined': { color: 'bg-red-100 text-red-800', icon: XCircle },
  'Tentative': { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
  'No Response': { color: 'bg-gray-100 text-gray-800', icon: AlertTriangle },
};

const categoryConfig = {
  'VIP': { color: 'bg-purple-100 text-purple-800' },
  'Speaker': { color: 'bg-blue-100 text-blue-800' },
  'Sponsor': { color: 'bg-green-100 text-green-800' },
  'Media': { color: 'bg-orange-100 text-orange-800' },
  'Staff': { color: 'bg-gray-100 text-gray-800' },
  'General': { color: 'bg-gray-100 text-gray-800' },
  'Partner': { color: 'bg-indigo-100 text-indigo-800' },
  'Vendor': { color: 'bg-yellow-100 text-yellow-800' },
};

interface EventAttendeesTableProps {
  eventId: string;
  showEventFilter?: boolean;
}

export default function EventAttendeesTable({ eventId, showEventFilter = false }: EventAttendeesTableProps) {
  const { toast } = useToast();
  const [attendees, setAttendees] = useState<EventAttendee[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load event attendees
  useEffect(() => {
    loadAttendees();
  }, [eventId]);

  const loadAttendees = async () => {
    setIsLoading(true);
    try {
      const fetchedAttendees = await getEventAttendees(eventId);
      setAttendees(fetchedAttendees);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load event attendees.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const handleDelete = async (attendeeId: string) => {
    if (!confirm('Are you sure you want to remove this attendee?')) return;
    
    try {
      await deleteEventAttendeeAction(eventId, attendeeId);
      await loadAttendees();
      toast({
        title: "Success",
        description: "Attendee removed successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove attendee.",
        variant: "destructive"
      });
    }
  };

  const handleSendInvitation = async (attendeeId: string) => {
    try {
      // This would be implemented in actions
      console.log('Send invitation to:', attendeeId);
      toast({
        title: "Feature Coming Soon",
        description: "Invitation sending will be available soon."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send invitation.",
        variant: "destructive"
      });
    }
  };

  const formatDate = (date: any) => {
    if (!date) return 'Never';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const columns: ColumnDef<EventAttendee>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "attendee",
      header: "Attendee",
      cell: ({ row }) => {
        const attendee = row.original;
        
        return (
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={attendee.profilePictureUrl} />
              <AvatarFallback>
                {attendee.firstName?.[0]}{attendee.lastName?.[0]}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="font-medium">
                {attendee.firstName} {attendee.lastName}
              </span>
              <span className="text-sm text-gray-500">{attendee.email}</span>
            </div>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "company",
      header: "Company",
      cell: ({ row }) => {
        const company = row.original.company;
        const jobTitle = row.original.jobTitle;
        
        return (
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-gray-400" />
              <span className="font-medium">{company || 'Not specified'}</span>
            </div>
            {jobTitle && (
              <span className="text-sm text-gray-500 ml-6">{jobTitle}</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: ({ row }) => {
        const category = row.original.category as AttendeeCategory;
        const config = categoryConfig[category] || categoryConfig['General'];
        
        return (
          <Badge className={`${config.color} capitalize`}>
            {category || 'General'}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id) || 'General');
      },
    },
    {
      accessorKey: "invitationStatus",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.invitationStatus as InvitationStatus;
        const config = statusConfig[status] || statusConfig['No Response'];
        const StatusIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <StatusIcon className="h-3 w-3" />
            {status || 'No Response'}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id) || 'No Response');
      },
    },
    {
      id: "contact",
      header: "Contact",
      cell: ({ row }) => {
        const attendee = row.original;
        
        return (
          <div className="flex items-center gap-2">
            {attendee.email && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open(`mailto:${attendee.email}`, '_blank')}
                title="Send Email"
              >
                <Mail className="h-4 w-4" />
              </Button>
            )}
            {attendee.phoneNumber && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open(`tel:${attendee.phoneNumber}`, '_blank')}
                title="Call"
              >
                <Phone className="h-4 w-4" />
              </Button>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "lastCommunication",
      header: "Last Comm.",
      cell: ({ row }) => {
        const attendee = row.original;
        // This would come from communications data
        const lastComm = attendee.lastCommunicationAt;
        
        return (
          <div className="text-sm text-gray-600">
            {formatDate(lastComm)}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "createdAt",
      header: "Added",
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(createdAt)}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const attendee = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open attendee details
                console.log('View attendee:', attendee);
              }}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open edit dialog
                console.log('Edit attendee:', attendee);
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleSendInvitation(attendee.id!)}
            >
              <Send className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open communication log
                console.log('Log communication:', attendee);
              }}
            >
              <MessageSquare className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDelete(attendee.id!)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], [eventId]);

  const tableActions = [
    {
      label: "Add Attendee",
      icon: UserPlus,
      onClick: () => {
        // Open add attendee dialog
        console.log('Add new attendee');
      },
      variant: "default" as const,
    },
    {
      label: "Import Attendees",
      icon: Download,
      onClick: () => {
        // Open import dialog
        console.log('Import attendees');
      },
      variant: "outline" as const,
    },
    {
      label: "Send Invitations",
      icon: Send,
      onClick: async (selectedRows: EventAttendee[]) => {
        console.log('Send invitations to:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Bulk invitation sending will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Export List",
      icon: Download,
      onClick: (selectedRows: EventAttendee[]) => {
        console.log('Export attendees:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Attendee list export will be available soon."
        });
      },
      variant: "outline" as const,
    },
    {
      label: "Remove Selected",
      icon: Trash2,
      onClick: async (selectedRows: EventAttendee[]) => {
        if (confirm(`Remove ${selectedRows.length} selected attendees?`)) {
          try {
            await Promise.all(selectedRows.map(attendee =>
              deleteEventAttendeeAction(eventId, attendee.id!)
            ));
            await loadAttendees();
            toast({
              title: "Success",
              description: `${selectedRows.length} attendees removed successfully.`
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to remove selected attendees.",
              variant: "destructive"
            });
          }
        }
      },
      variant: "destructive" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    {
      key: 'category',
      label: 'Category',
      options: [
        { label: 'VIP', value: 'VIP' },
        { label: 'Speaker', value: 'Speaker' },
        { label: 'Sponsor', value: 'Sponsor' },
        { label: 'Media', value: 'Media' },
        { label: 'Staff', value: 'Staff' },
        { label: 'General', value: 'General' },
        { label: 'Partner', value: 'Partner' },
        { label: 'Vendor', value: 'Vendor' },
      ]
    },
    {
      key: 'invitationStatus',
      label: 'Status',
      options: [
        { label: 'Invited', value: 'Invited' },
        { label: 'Confirmed', value: 'Confirmed' },
        { label: 'Declined', value: 'Declined' },
        { label: 'Tentative', value: 'Tentative' },
        { label: 'No Response', value: 'No Response' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={attendees}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadAttendees}
      tableActions={tableActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search attendees by name, company, email..."
      emptyStateMessage="No attendees found"
      emptyStateDescription="Add attendees to start managing your event participants."
    />
  );
}
