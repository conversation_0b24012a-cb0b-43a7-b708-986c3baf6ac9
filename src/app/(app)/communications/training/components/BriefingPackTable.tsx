"use client";

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableAction } from '@/components/ui/advanced-data-table';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Edit2, Trash2, Eye, FileText, Calendar, User, Download, BookOpen, Hash } from "lucide-react";
import { useRouter } from 'next/navigation';
import { useToast } from "@/hooks/use-toast";
import { getBriefingPacksAction, deleteBriefingPackAction } from '../../actions';
import type { BriefingPack } from '@/types/firestore';
import BriefingPackFormDialog from './BriefingPackFormDialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface BriefingPackTableProps {
  onActionComplete?: () => void;
}

export default function BriefingPackTable({ onActionComplete }: BriefingPackTableProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [briefingPacks, setBriefingPacks] = useState<BriefingPack[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingPack, setEditingPack] = useState<BriefingPack | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [packToDelete, setPackToDelete] = useState<BriefingPack | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchBriefingPacks = useCallback(async () => {
    setIsLoading(true);
    try {
      const fetchedPacks = await getBriefingPacksAction();
      setBriefingPacks(fetchedPacks);
    } catch (error) {
      console.error("Error fetching briefing packs:", error);
      toast({ title: "Error", description: "Failed to fetch briefing packs.", variant: "destructive" });
    }
    setIsLoading(false);
  }, [toast]);

  useEffect(() => {
    fetchBriefingPacks();
  }, [fetchBriefingPacks]);

  const handleEdit = useCallback((pack: BriefingPack) => {
    setEditingPack(pack);
    setIsFormOpen(true);
  }, []);

  const handleDelete = useCallback((pack: BriefingPack) => {
    setPackToDelete(pack);
    setIsDeleteDialogOpen(true);
  }, []);

  const handleDeleteConfirm = useCallback(async () => {
    if (!packToDelete?.id) return;
    
    setIsDeleting(true);
    try {
      await deleteBriefingPackAction(packToDelete.id);
      toast({ title: "Success", description: "Briefing pack deleted successfully." });
      fetchBriefingPacks();
      onActionComplete?.();
    } catch (error) {
      console.error("Error deleting briefing pack:", error);
      toast({ title: "Error", description: "Failed to delete briefing pack.", variant: "destructive" });
    }
    setIsDeleting(false);
    setIsDeleteDialogOpen(false);
    setPackToDelete(null);
  }, [packToDelete, toast, fetchBriefingPacks, onActionComplete]);

  const handleFormSuccess = useCallback(() => {
    fetchBriefingPacks();
    onActionComplete?.();
    setIsFormOpen(false);
    setEditingPack(null);
  }, [fetchBriefingPacks, onActionComplete]);

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'Published': return 'default';
      case 'Draft': return 'secondary';
      case 'Archived': return 'outline';
      default: return 'outline';
    }
  };

  const getStatusBadgeClassName = (status: string) => {
    switch (status) {
      case 'Published': return 'bg-green-100 text-green-700';
      case 'Draft': return 'bg-yellow-100 text-yellow-700';
      case 'Archived': return 'bg-gray-100 text-gray-700';
      default: return '';
    }
  };

  const columns: AdvancedTableColumn<BriefingPack>[] = useMemo(() => [
    {
      accessorKey: 'title',
      header: 'Briefing Pack',
      cell: ({ row }) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
            <BookOpen className="h-5 w-5 text-muted-foreground" />
          </div>
          <div>
            <div className="font-medium">{row.original.title}</div>
            <div className="text-sm text-muted-foreground truncate max-w-[300px]">
              {row.original.description || 'No description'}
            </div>
          </div>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      accessorKey: 'exhibitionName',
      header: 'Exhibition',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4 text-muted-foreground" />
          <span>{row.original.exhibitionName || 'N/A'}</span>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => (
        <Badge 
          variant={getStatusBadgeVariant(row.original.status)}
          className={getStatusBadgeClassName(row.original.status)}
        >
          {row.original.status}
        </Badge>
      ),
      enableSorting: true,
      enableColumnFilter: true,
      filterFn: 'equals',
    },
    {
      accessorKey: 'sections',
      header: 'Sections',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Hash className="h-4 w-4 text-muted-foreground" />
          <Badge variant="outline">
            {row.original.sections?.length || 0}
          </Badge>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: false,
    },
    {
      accessorKey: 'createdBy',
      header: 'Created By',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span>{row.original.createdBy || 'Unknown'}</span>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      accessorKey: 'createdAt',
      header: 'Created',
      cell: ({ row }) => (
        <div className="flex items-center gap-1 text-sm text-muted-foreground">
          <Calendar className="h-4 w-4" />
          <span>
            {row.original.createdAt 
              ? new Date(row.original.createdAt.seconds * 1000).toLocaleDateString()
              : 'N/A'
            }
          </span>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: false,
    },
  ], []);

  const getRowActions = useCallback((pack: BriefingPack): AdvancedTableAction<BriefingPack>[] => [
    {
      label: 'View Pack',
      icon: Eye,
      onClick: (pack) => router.push(`/communications/training/briefing-packs/${pack.id}`),
      variant: 'ghost',
    },
    {
      label: 'Download PDF',
      icon: Download,
      onClick: (pack) => {
        // Handle PDF download
        console.log('Download PDF for pack:', pack.id);
        toast({ title: "Info", description: "PDF download functionality coming soon." });
      },
      variant: 'ghost',
    },
    {
      label: 'Edit Pack',
      icon: Edit2,
      onClick: handleEdit,
      variant: 'ghost',
    },
    {
      label: 'Delete Pack',
      icon: Trash2,
      onClick: handleDelete,
      variant: 'ghost',
      className: 'text-destructive hover:text-destructive',
    },
  ], [router, handleEdit, handleDelete, toast]);

  const bulkActions: AdvancedTableAction<BriefingPack>[] = useMemo(() => [
    {
      label: 'Delete Selected',
      icon: Trash2,
      onClick: (packs) => {
        // Handle bulk delete
        console.log('Bulk delete packs:', packs);
      },
      variant: 'destructive',
    },
  ], []);

  const mobileCardRenderer = useCallback((pack: BriefingPack) => (
    <div className="p-4 space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
            <BookOpen className="h-5 w-5 text-muted-foreground" />
          </div>
          <div className="flex-1">
            <div className="font-medium">{pack.title}</div>
            <div className="text-sm text-muted-foreground">
              {pack.description || 'No description'}
            </div>
          </div>
        </div>
        <Badge 
          variant={getStatusBadgeVariant(pack.status)}
          className={getStatusBadgeClassName(pack.status)}
        >
          {pack.status}
        </Badge>
      </div>
      <div className="space-y-2 text-sm">
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4 text-muted-foreground" />
          <span>{pack.exhibitionName || 'N/A'}</span>
        </div>
        <div className="flex items-center gap-2">
          <Hash className="h-4 w-4 text-muted-foreground" />
          <span>{pack.sections?.length || 0} sections</span>
        </div>
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span>{pack.createdBy || 'Unknown'}</span>
        </div>
      </div>
    </div>
  ), []);

  return (
    <>
      <AdvancedDataTable
        data={briefingPacks}
        columns={columns}
        loading={isLoading}
        enableVirtualization={briefingPacks.length > 100}
        enableGlobalSearch={true}
        searchPlaceholder="Search briefing packs by title, exhibition, or creator..."
        enableColumnFilters={true}
        enableRowSelection={true}
        enableColumnResizing={true}
        enableColumnVisibility={true}
        bulkActions={bulkActions}
        enableRowActions={true}
        rowActions={getRowActions}
        maxVisibleRowActions={3}
        enableExport={true}
        exportFormats={['csv', 'excel']}
        exportFileName="briefing-packs-export"
        mobileCardRenderer={mobileCardRenderer}
        onRowClick={(pack) => router.push(`/communications/training/briefing-packs/${pack.id}`)}
        onRefresh={fetchBriefingPacks}
        variant="default"
        className="w-full"
        emptyMessage="No briefing packs found. Click 'Create New Pack' to create one."
      />

      {isFormOpen && (
        <BriefingPackFormDialog
          isOpen={isFormOpen}
          setIsOpen={setIsFormOpen}
          pack={editingPack}
          onSuccess={handleFormSuccess}
        />
      )}

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the briefing pack "{packToDelete?.title}" and all its sections.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteConfirm} 
              disabled={isDeleting}
              className="bg-destructive hover:bg-destructive/90"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
