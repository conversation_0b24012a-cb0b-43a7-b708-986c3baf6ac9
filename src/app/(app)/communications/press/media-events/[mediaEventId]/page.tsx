"use client";

import React, { useEffect, useState, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft, Loader2, FileText, Edit2, User, Users, CalendarDays, MapPin, MessageSquare, BookOpenCheck } from "lucide-react";
import { getMediaEventByIdAction, getMediaContactsAction, getMarketingMaterialsForSelectAction } from '@/app/(app)/communications/actions';
import type { MediaEvent, MediaContact, MarketingMaterial } from '@/types/firestore';
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { format, parseISO } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import { useParams, notFound } from 'next/navigation';

interface MediaEventDetailPageProps {}

function formatDateTimeDisplay(dateInput?: Timestamp | Date | string): string {
  if (!dateInput) return 'N/A';
  let date: Date;
  if (dateInput instanceof Timestamp) date = dateInput.toDate();
  else if (typeof dateInput === 'string') date = parseISO(dateInput);
  else if (dateInput instanceof Date) date = dateInput;
  else return 'Invalid Date';
  if (isNaN(date.getTime())) return 'Invalid Date';
  return format(date, 'PPP');
}

export default function MediaEventDetailPage(props: MediaEventDetailPageProps) {
  const params = useParams();
  const eventId = params.mediaEventId as string;
  const { toast } = useToast();
  const [event, setEvent] = useState<MediaEvent | null>(null);
  const [attendees, setAttendees] = useState<MediaContact[]>([]);
  const [materials, setMaterials] = useState<MarketingMaterial[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchEventData() {
        if (!eventId) {
          setIsLoading(false);
          notFound();
          return;
        }
        setIsLoading(true);
        try {
          const [fetchedEvent, fetchedContacts, fetchedMaterials] = await Promise.all([
            getMediaEventByIdAction(eventId),
            getMediaContactsAction(),
            getMarketingMaterialsForSelectAction(),
          ]);
          
          if (!fetchedEvent) {
            notFound();
            return;
          }
          
          setEvent(fetchedEvent);
          
          // Filter attendees based on event's attendee IDs
          const eventAttendees = fetchedEvent.attendeeIds 
            ? fetchedContacts.filter(contact => fetchedEvent.attendeeIds!.includes(contact.id!))
            : [];
          setAttendees(eventAttendees);
          
          // Filter materials based on event's material IDs
          const eventMaterials = fetchedEvent.materialIds
            ? fetchedMaterials.filter(material => fetchedEvent.materialIds!.includes(material.id!))
            : [];
          setMaterials(eventMaterials);
          
        } catch (error) {
          console.error('Error fetching media event data:', error);
          toast({ title: "Error", description: "Failed to load media event details.", variant: "destructive" });
        } finally {
          setIsLoading(false);
        }
    }
    fetchEventData();
  }, [eventId, toast]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  if (!event) {
    return (
      <div className="container mx-auto p-4 md:p-6 lg:p-8 text-center">
        <FileText className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
        <h1 className="text-2xl font-bold">Media Event Not Found</h1>
        <p className="text-muted-foreground mb-4">The requested media event could not be loaded.</p>
        <Button asChild variant="outline">
          <Link href="/communications/press/media-events">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Media Events
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-6 lg:p-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button asChild variant="outline" size="icon">
            <Link href="/communications/press/media-events">
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">Back to Media Events</span>
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight font-headline">{event.eventName}</h1>
            <p className="text-muted-foreground">{event.description || "No description available."}</p>
          </div>
        </div>
        <Button asChild>
          <Link href={`/communications/press/media-events/${eventId}/edit`}>
            <Edit2 className="mr-2 h-4 w-4" /> Edit Event
          </Link>
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CalendarDays className="h-5 w-5" />
              Event Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Event Date</label>
              <p className="text-sm">{formatDateTimeDisplay(event.eventDate)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Location</label>
              <p className="text-sm">{event.location || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Type</label>
              <p className="text-sm">{event.eventType || 'Not specified'}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Attendees ({attendees.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {attendees.length > 0 ? (
              <div className="space-y-2">
                {attendees.map((attendee) => (
                  <div key={attendee.id} className="flex items-center gap-3 p-2 rounded-lg border">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">{attendee.name}</p>
                      <p className="text-xs text-muted-foreground">{attendee.organization}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No attendees registered</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
