"use client";

import React, { useEffect, useState } from 'react';
import MediaEventFormDialog from '../../../components/MediaEventFormDialog';
import type { MediaEvent } from '@/types/firestore';
import { getMediaEventByIdAction } from '@/app/(app)/communications/actions';
import { useToast } from "@/hooks/use-toast";
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowLeft, Loader2, FileText } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';

export default function EditMediaEventPage() {
  const params = useParams();
  const eventId = params.mediaEventId as string;
  const { toast } = useToast();
  const router = useRouter();
  const [event, setEvent] = useState<MediaEvent | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!eventId) return;
    setIsLoading(true);
    getMediaEventByIdAction(eventId)
      .then(data => {
        if (data) {
          setEvent(data);
        } else {
          toast({ title: "Error", description: "Media event not found.", variant: "destructive" });
          router.push('/communications/press/media-events');
        }
      })
      .catch(error => {
        console.error('Error fetching media event:', error);
        toast({ title: "Error", description: "Failed to load media event.", variant: "destructive" });
        router.push('/communications/press/media-events');
      })
      .finally(() => setIsLoading(false));
  }, [eventId, toast, router]);

  const handleSuccess = () => {
    router.push(`/communications/press/media-events/${eventId}`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  if (!event) {
    return (
      <div className="container mx-auto p-4 md:p-6 lg:p-8 text-center">
        <FileText className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
        <h1 className="text-2xl font-bold">Media Event Not Found</h1>
        <p className="text-muted-foreground mb-4">The requested media event could not be loaded.</p>
        <Button asChild variant="outline">
          <Link href="/communications/press/media-events">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Media Events
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-6 lg:p-8">
      <div className="flex items-center gap-4 mb-6">
        <Button asChild variant="outline" size="icon">
          <Link href={`/communications/press/media-events/${eventId}`}>
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">Back to Media Event</span>
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight font-headline">Edit Media Event</h1>
          <p className="text-muted-foreground">Update the details for "{event.eventName}"</p>
        </div>
      </div>

      <MediaEventFormDialog
        isOpen={true}
        onClose={() => router.push(`/communications/press/media-events/${eventId}`)}
        onSuccess={handleSuccess}
        initialData={event}
        mode="edit"
      />
    </div>
  );
}
