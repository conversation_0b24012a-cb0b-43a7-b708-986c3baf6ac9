"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Users, 
  UserPlus,
  Settings,
  BarChart3,
  Calendar,
  Award,
  TrendingUp,
  ArrowLeft,
  Building,
  Clock,
  Target,
  Activity
} from "lucide-react";
import Link from 'next/link';
import { useToast } from '@/hooks/use-toast';
import { getUsers, getExhibitions, getTemporaryStaff, getTasks } from '@/services/firestoreService';
import type { EvexUser, Exhibition, TemporaryStaff, Task } from '@/types/firestore';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface TeamMember {
  id: string;
  name: string;
  email: string;
  type: 'Employee' | 'Temporary';
  role?: string;
  department?: string;
  status: 'active' | 'inactive';
  profileImageUrl?: string;
  assignedExhibitions: number;
  completedTasks: number;
  pendingTasks: number;
  performanceScore: number;
  lastActivity?: Date;
}

interface TeamMetrics {
  totalMembers: number;
  activeMembers: number;
  totalExhibitions: number;
  averagePerformance: number;
  completionRate: number;
}

export default function TeamManagementPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [metrics, setMetrics] = useState<TeamMetrics>({
    totalMembers: 0,
    activeMembers: 0,
    totalExhibitions: 0,
    averagePerformance: 0,
    completionRate: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchTeamData = async () => {
      try {
        setIsLoading(true);
        
        const [users, exhibitions, tempStaff, tasks] = await Promise.all([
          getUsers(),
          getExhibitions(),
          getTemporaryStaff(),
          getTasks(),
        ]);

        // Transform users and temp staff into unified team members
        const employeeMembers: TeamMember[] = users.map(user => {
          const userTasks = tasks.filter(task => task.assignedToId === user.id);
          const completedTasks = userTasks.filter(task => task.status === 'completed').length;
          const pendingTasks = userTasks.filter(task => task.status !== 'completed').length;
          const assignedExhibitions = exhibitions.filter(ex => 
            ex.staffingPlan?.some(staff => staff.assignedToUserId === user.id)
          ).length;

          return {
            id: user.id!,
            name: user.displayName || user.email,
            email: user.email,
            type: 'Employee',
            role: user.jobTitle,
            department: user.department,
            status: user.status as 'active' | 'inactive',
            profileImageUrl: user.profileImageUrl,
            assignedExhibitions,
            completedTasks,
            pendingTasks,
            performanceScore: completedTasks > 0 ? Math.round((completedTasks / (completedTasks + pendingTasks)) * 100) : 0,
            lastActivity: user.lastLogin ? new Date(user.lastLogin) : undefined,
          };
        });

        const tempMembers: TeamMember[] = tempStaff.map(temp => {
          const assignedExhibitions = exhibitions.filter(ex => 
            ex.staffingPlan?.some(staff => staff.assignedToTempStaffId === temp.id)
          ).length;

          return {
            id: temp.id!,
            name: temp.name,
            email: temp.contactEmail || '',
            type: 'Temporary',
            role: temp.role,
            department: 'Temporary Staff',
            status: 'active',
            assignedExhibitions,
            completedTasks: 0,
            pendingTasks: 0,
            performanceScore: 85, // Mock score for temp staff
            lastActivity: new Date(),
          };
        });

        const allMembers = [...employeeMembers, ...tempMembers];
        setTeamMembers(allMembers);

        // Calculate metrics
        const activeMembers = allMembers.filter(m => m.status === 'active').length;
        const totalTasks = allMembers.reduce((sum, m) => sum + m.completedTasks + m.pendingTasks, 0);
        const completedTasks = allMembers.reduce((sum, m) => sum + m.completedTasks, 0);
        const avgPerformance = allMembers.length > 0 
          ? Math.round(allMembers.reduce((sum, m) => sum + m.performanceScore, 0) / allMembers.length)
          : 0;

        setMetrics({
          totalMembers: allMembers.length,
          activeMembers,
          totalExhibitions: exhibitions.length,
          averagePerformance: avgPerformance,
          completionRate: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
        });

      } catch (error) {
        console.error('Error fetching team data:', error);
        toast({
          title: "Error",
          description: "Failed to load team data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchTeamData();
  }, [toast]);

  const teamColumns: AdvancedTableColumn<TeamMember>[] = [
    {
      accessorKey: 'name',
      title: 'Team Member',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const member = row.original;
        return (
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={member.profileImageUrl} alt={member.name} />
              <AvatarFallback>
                {member.name.split(' ').map(n => n[0]).join('').toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{member.name}</div>
              <div className="text-xs text-muted-foreground">{member.email}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'type',
      title: 'Type',
      sortable: true,
      filterable: true,
      cell: ({ row }) => (
        <Badge variant={row.original.type === 'Employee' ? 'default' : 'secondary'}>
          {row.original.type}
        </Badge>
      ),
    },
    {
      accessorKey: 'role',
      title: 'Role',
      sortable: true,
      filterable: true,
      cell: ({ row }) => (
        <div className="flex items-center">
          <Building className="h-4 w-4 mr-2 text-muted-foreground" />
          {row.original.role || 'N/A'}
        </div>
      ),
    },
    {
      accessorKey: 'department',
      title: 'Department',
      sortable: true,
      filterable: true,
      cell: ({ row }) => (
        <Badge variant="outline">{row.original.department || 'N/A'}</Badge>
      ),
    },
    {
      accessorKey: 'assignedExhibitions',
      title: 'Exhibitions',
      sortable: true,
      cell: ({ row }) => (
        <div className="flex items-center">
          <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
          {row.original.assignedExhibitions}
        </div>
      ),
    },
    {
      accessorKey: 'performanceScore',
      title: 'Performance',
      sortable: true,
      cell: ({ row }) => {
        const score = row.original.performanceScore;
        const variant = score >= 80 ? 'default' : score >= 60 ? 'secondary' : 'destructive';
        return (
          <div className="flex items-center">
            <Award className="h-4 w-4 mr-2 text-muted-foreground" />
            <Badge variant={variant}>{score}%</Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      title: 'Status',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const status = row.original.status;
        return (
          <Badge 
            variant={status === 'active' ? 'default' : 'secondary'}
            className={status === 'active' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}
          >
            {status === 'active' ? 'Active' : 'Inactive'}
          </Badge>
        );
      },
    },
  ];

  const teamRowActions = (row: TeamMember): AdvancedTableRowAction<TeamMember>[] => [
    {
      type: 'view',
      label: 'View Profile',
      href: row.type === 'Employee' ? `/team/employees/${row.id}` : `/team/temporary-staff/${row.id}`,
      icon: <Users className="h-4 w-4" />,
    },
    {
      type: 'email',
      label: 'Send Email',
      onClick: () => window.open(`mailto:${row.email}`, '_blank'),
      icon: <Users className="h-4 w-4" />,
    },
  ];

  const teamFilters = [
    {
      id: 'type',
      label: 'Staff Type',
      type: 'select' as const,
      options: [
        { label: 'Employee', value: 'Employee' },
        { label: 'Temporary', value: 'Temporary' },
      ],
    },
    {
      id: 'department',
      label: 'Department',
      type: 'select' as const,
      options: Array.from(new Set(teamMembers.map(m => m.department).filter(Boolean))).map(dept => ({
        label: dept!,
        value: dept!,
      })),
    },
    {
      id: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' },
      ],
    },
  ];

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" asChild>
            <Link href="/team">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight font-headline flex items-center">
              <Users className="mr-3 h-8 w-8 text-primary" />
              Team Management
            </h1>
            <p className="text-muted-foreground">
              Comprehensive team oversight and performance management
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/team/employees/add">
              <UserPlus className="mr-2 h-4 w-4" />
              Add Employee
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/team/temporary-staff/add">
              <UserPlus className="mr-2 h-4 w-4" />
              Add Temp Staff
            </Link>
          </Button>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalMembers}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.activeMembers} active
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Exhibitions</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalExhibitions}</div>
            <p className="text-xs text-muted-foreground">
              Active projects
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Performance</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.averagePerformance}%</div>
            <p className="text-xs text-muted-foreground">
              Team average
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.completionRate}%</div>
            <p className="text-xs text-muted-foreground">
              Task completion
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Activity</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">High</div>
            <p className="text-xs text-muted-foreground">
              Team engagement
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-3">
          <TabsTrigger value="overview">Team Overview</TabsTrigger>
          <TabsTrigger value="assignments">Assignments</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Team Members</CardTitle>
              <CardDescription>
                All team members across employees and temporary staff
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AdvancedDataTable
                data={teamMembers}
                columns={teamColumns}
                enableGlobalSearch={true}
                enableColumnFilters={true}
                filters={teamFilters}
                enableSorting={true}
                enableRowSelection={true}
                enableMultiRowSelection={true}
                enableRowActions={true}
                rowActions={teamRowActions}
                enableExport={true}
                exportFormats={['csv', 'excel']}
                exportFileName="team-members"
                searchPlaceholder="Search team members..."
                emptyMessage="No team members found."
                loading={isLoading}
                className="w-full"
                variant="default"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="assignments" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Team Assignments</CardTitle>
              <CardDescription>
                Current team assignments across all exhibitions and events
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-10 text-muted-foreground">
                <Calendar className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p className="text-lg font-medium">Assignment management coming soon</p>
                <p className="text-sm">This will show detailed assignment tracking and management</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Team Analytics</CardTitle>
              <CardDescription>
                Performance metrics and insights for team management
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-10 text-muted-foreground">
                <BarChart3 className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p className="text-lg font-medium">Analytics dashboard coming soon</p>
                <p className="text-sm">This will show detailed performance analytics and insights</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
