"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  MessageSquare, 
  FileText, 
  Video, 
  Users, 
  Clock, 
  Eye,
  Edit2,
  Share2,
  Calendar,
  CheckCircle,
  AlertCircle,
  Play
} from 'lucide-react';
import { getUsers } from '@/services/firestoreService';
import type { EvexUser } from '@/types/firestore';
import { useToast } from '@/hooks/use-toast';

interface CollaborationSession {
  id: string;
  type: 'chat' | 'document' | 'whiteboard' | 'video_conference' | 'meeting';
  title: string;
  description?: string;
  status: 'active' | 'scheduled' | 'completed' | 'cancelled';
  participants: string[]; // User IDs
  createdBy: string;
  createdAt: Date;
  lastActivity: Date;
  exhibitionId?: string;
  exhibitionName?: string;
  duration?: number; // in minutes
  messageCount?: number;
  documentCount?: number;
  isPrivate: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

interface TeamCollaborationTableProps {
  exhibitionId?: string;
  className?: string;
}

export default function TeamCollaborationTable({ exhibitionId, className }: TeamCollaborationTableProps) {
  const [collaborationSessions, setCollaborationSessions] = useState<CollaborationSession[]>([]);
  const [users, setUsers] = useState<EvexUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchCollaborationData = async () => {
      try {
        setIsLoading(true);
        
        // Fetch users for participant mapping
        const usersData = await getUsers();
        setUsers(usersData);

        // Generate mock collaboration sessions (in production, this would come from Firebase)
        const mockSessions: CollaborationSession[] = [
          {
            id: '1',
            type: 'chat',
            title: 'Exhibition Planning Discussion',
            description: 'Main planning channel for upcoming exhibition',
            status: 'active',
            participants: usersData.slice(0, 5).map(u => u.id!),
            createdBy: usersData[0]?.id || '',
            createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000),
            exhibitionId: 'ex1',
            exhibitionName: 'Tech Expo 2024',
            messageCount: 127,
            isPrivate: false,
            priority: 'high',
          },
          {
            id: '2',
            type: 'document',
            title: 'Booth Design Specifications',
            description: 'Collaborative document for booth design requirements',
            status: 'active',
            participants: usersData.slice(1, 4).map(u => u.id!),
            createdBy: usersData[1]?.id || '',
            createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
            lastActivity: new Date(Date.now() - 30 * 60 * 1000),
            exhibitionId: 'ex1',
            exhibitionName: 'Tech Expo 2024',
            documentCount: 3,
            isPrivate: false,
            priority: 'medium',
          },
          {
            id: '3',
            type: 'video_conference',
            title: 'Weekly Team Standup',
            description: 'Regular team sync meeting',
            status: 'scheduled',
            participants: usersData.slice(0, 8).map(u => u.id!),
            createdBy: usersData[0]?.id || '',
            createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
            lastActivity: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
            duration: 60,
            isPrivate: false,
            priority: 'medium',
          },
          {
            id: '4',
            type: 'whiteboard',
            title: 'Marketing Strategy Brainstorm',
            description: 'Visual collaboration for marketing ideas',
            status: 'completed',
            participants: usersData.slice(2, 6).map(u => u.id!),
            createdBy: usersData[2]?.id || '',
            createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
            lastActivity: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
            exhibitionId: 'ex2',
            exhibitionName: 'Innovation Summit',
            isPrivate: true,
            priority: 'low',
          },
        ];

        setCollaborationSessions(mockSessions);
      } catch (error) {
        console.error('Error fetching collaboration data:', error);
        toast({
          title: "Error",
          description: "Failed to load collaboration data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchCollaborationData();
  }, [toast]);

  const getUsersMap = () => {
    return users.reduce((acc, user) => {
      acc[user.id!] = user;
      return acc;
    }, {} as Record<string, EvexUser>);
  };

  const columns: AdvancedTableColumn<CollaborationSession>[] = [
    {
      accessorKey: 'title',
      title: 'Session',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const session = row.original;
        const typeIcons = {
          chat: MessageSquare,
          document: FileText,
          whiteboard: Edit2,
          video_conference: Video,
          meeting: Calendar,
        };
        const IconComponent = typeIcons[session.type];
        
        return (
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <IconComponent className="h-4 w-4 text-primary" />
            </div>
            <div>
              <div className="font-medium">{session.title}</div>
              <div className="text-xs text-muted-foreground">{session.description}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'type',
      title: 'Type',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const typeLabels = {
          chat: 'Chat',
          document: 'Document',
          whiteboard: 'Whiteboard',
          video_conference: 'Video Call',
          meeting: 'Meeting',
        };
        return (
          <Badge variant="outline">
            {typeLabels[row.original.type]}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'status',
      title: 'Status',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const status = row.original.status;
        const statusConfig = {
          active: { variant: 'default' as const, label: 'Active', className: 'bg-green-100 text-green-700' },
          scheduled: { variant: 'secondary' as const, label: 'Scheduled', className: 'bg-blue-100 text-blue-700' },
          completed: { variant: 'outline' as const, label: 'Completed', className: 'bg-gray-100 text-gray-700' },
          cancelled: { variant: 'destructive' as const, label: 'Cancelled', className: 'bg-red-100 text-red-700' },
        };
        const config = statusConfig[status];
        
        return (
          <Badge variant={config.variant} className={config.className}>
            {config.label}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'participants',
      title: 'Participants',
      sortable: true,
      cell: ({ row }) => {
        const session = row.original;
        const usersMap = getUsersMap();
        const participantUsers = session.participants.map(id => usersMap[id]).filter(Boolean);
        
        return (
          <div className="flex items-center space-x-2">
            <div className="flex -space-x-2">
              {participantUsers.slice(0, 3).map((user, index) => (
                <Avatar key={user.id} className="h-6 w-6 border-2 border-background">
                  <AvatarImage src={user.profileImageUrl} alt={user.displayName} />
                  <AvatarFallback className="text-xs">
                    {user.displayName?.split(' ').map(n => n[0]).join('').toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
              ))}
              {session.participants.length > 3 && (
                <div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                  <span className="text-xs font-medium">+{session.participants.length - 3}</span>
                </div>
              )}
            </div>
            <span className="text-sm text-muted-foreground">
              {session.participants.length} member{session.participants.length !== 1 ? 's' : ''}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'priority',
      title: 'Priority',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const priority = row.original.priority;
        const priorityConfig = {
          low: { variant: 'outline' as const, label: 'Low', className: 'text-gray-600' },
          medium: { variant: 'secondary' as const, label: 'Medium', className: 'text-yellow-600' },
          high: { variant: 'default' as const, label: 'High', className: 'text-orange-600' },
          urgent: { variant: 'destructive' as const, label: 'Urgent', className: 'text-red-600' },
        };
        const config = priorityConfig[priority];
        
        return (
          <Badge variant={config.variant} className={config.className}>
            {config.label}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'lastActivity',
      title: 'Last Activity',
      sortable: true,
      cell: ({ row }) => {
        const lastActivity = row.original.lastActivity;
        const now = new Date();
        const diffMs = now.getTime() - lastActivity.getTime();
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffHours / 24);
        
        let timeAgo = '';
        if (diffDays > 0) {
          timeAgo = `${diffDays}d ago`;
        } else if (diffHours > 0) {
          timeAgo = `${diffHours}h ago`;
        } else {
          timeAgo = 'Just now';
        }
        
        return (
          <div className="flex items-center text-sm">
            <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
            {timeAgo}
          </div>
        );
      },
    },
    {
      accessorKey: 'activity',
      title: 'Activity',
      sortable: false,
      cell: ({ row }) => {
        const session = row.original;
        return (
          <div className="text-sm text-muted-foreground">
            {session.messageCount && `${session.messageCount} messages`}
            {session.documentCount && `${session.documentCount} documents`}
            {session.duration && `${session.duration} min`}
          </div>
        );
      },
    },
  ];

  const rowActions = (row: CollaborationSession): AdvancedTableRowAction<CollaborationSession>[] => {
    const actions: AdvancedTableRowAction<CollaborationSession>[] = [
      {
        type: 'view',
        label: 'View Session',
        onClick: () => {
          // In production, this would navigate to the collaboration session
          toast({
            title: "Opening Session",
            description: `Opening ${row.title}...`,
          });
        },
        icon: <Eye className="h-4 w-4" />,
      },
    ];

    if (row.status === 'scheduled' && row.type === 'video_conference') {
      actions.push({
        type: 'custom',
        label: 'Join Meeting',
        onClick: () => {
          toast({
            title: "Joining Meeting",
            description: `Joining ${row.title}...`,
          });
        },
        icon: <Play className="h-4 w-4" />,
      });
    }

    if (row.status === 'active') {
      actions.push({
        type: 'share',
        label: 'Share Session',
        onClick: () => {
          navigator.clipboard.writeText(`${window.location.origin}/collaboration/${row.id}`);
          toast({
            title: "Link Copied",
            description: "Session link copied to clipboard",
          });
        },
        icon: <Share2 className="h-4 w-4" />,
      });
    }

    return actions;
  };

  const filters = [
    {
      id: 'type',
      label: 'Session Type',
      type: 'select' as const,
      options: [
        { label: 'Chat', value: 'chat' },
        { label: 'Document', value: 'document' },
        { label: 'Whiteboard', value: 'whiteboard' },
        { label: 'Video Conference', value: 'video_conference' },
        { label: 'Meeting', value: 'meeting' },
      ],
    },
    {
      id: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Scheduled', value: 'scheduled' },
        { label: 'Completed', value: 'completed' },
        { label: 'Cancelled', value: 'cancelled' },
      ],
    },
    {
      id: 'priority',
      label: 'Priority',
      type: 'select' as const,
      options: [
        { label: 'Low', value: 'low' },
        { label: 'Medium', value: 'medium' },
        { label: 'High', value: 'high' },
        { label: 'Urgent', value: 'urgent' },
      ],
    },
  ];

  return (
    <AdvancedDataTable
      data={collaborationSessions}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="team-collaboration-sessions"
      searchPlaceholder="Search collaboration sessions..."
      emptyMessage="No collaboration sessions found. Start a new session to begin collaborating."
      loading={isLoading}
      className={className}
      variant="default"
    />
  );
}
