"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Users, 
  Shield, 
  Eye,
  Edit2,
  Trash2,
  UserPlus,
  Settings,
  Calendar,
  Clock,
  Building
} from 'lucide-react';
import { getGroups, getDelegations, getUsers } from '@/services/firestoreService';
import type { EvexGroup, EvexDelegation, EvexUser } from '@/types/firestore';
import { useToast } from '@/hooks/use-toast';

interface TeamGroup {
  id: string;
  name: string;
  description?: string;
  type: 'group' | 'delegation';
  memberCount: number;
  permissions: string[];
  createdAt: Date;
  createdBy: string;
  isActive: boolean;
  members: EvexUser[];
  // Delegation specific fields
  startDate?: Date;
  endDate?: Date;
  delegatedBy?: string;
  delegatedTo?: string;
  purpose?: string;
  status?: 'active' | 'pending' | 'completed' | 'cancelled';
}

interface TeamGroupsTableProps {
  className?: string;
  onEditGroup?: (group: TeamGroup) => void;
  onDeleteGroup?: (groupId: string) => void;
}

export default function TeamGroupsTable({ className, onEditGroup, onDeleteGroup }: TeamGroupsTableProps) {
  const [teamGroups, setTeamGroups] = useState<TeamGroup[]>([]);
  const [users, setUsers] = useState<EvexUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchTeamGroupsData = async () => {
      try {
        setIsLoading(true);
        
        const [groups, delegations, usersData] = await Promise.all([
          getGroups(),
          getDelegations(),
          getUsers(),
        ]);

        setUsers(usersData);
        const usersMap = usersData.reduce((acc, user) => {
          acc[user.id!] = user;
          return acc;
        }, {} as Record<string, EvexUser>);

        // Transform groups
        const groupItems: TeamGroup[] = groups.map(group => ({
          id: group.id!,
          name: group.name,
          description: group.description,
          type: 'group',
          memberCount: group.memberIds?.length || 0,
          permissions: group.permissions || [],
          createdAt: group.createdAt ? new Date(group.createdAt) : new Date(),
          createdBy: group.createdBy || '',
          isActive: true,
          members: (group.memberIds || []).map(id => usersMap[id]).filter(Boolean),
        }));

        // Transform delegations
        const delegationItems: TeamGroup[] = delegations.map(delegation => ({
          id: delegation.id!,
          name: `Delegation: ${delegation.purpose || 'Unnamed'}`,
          description: delegation.notes,
          type: 'delegation',
          memberCount: 2, // delegatedBy + delegatedTo
          permissions: delegation.permissions || [],
          createdAt: delegation.createdAt ? new Date(delegation.createdAt) : new Date(),
          createdBy: delegation.delegatedBy || '',
          isActive: delegation.status === 'active',
          members: [
            delegation.delegatedBy ? usersMap[delegation.delegatedBy] : null,
            delegation.delegatedTo ? usersMap[delegation.delegatedTo] : null,
          ].filter(Boolean),
          startDate: delegation.startDate ? new Date(delegation.startDate) : undefined,
          endDate: delegation.endDate ? new Date(delegation.endDate) : undefined,
          delegatedBy: delegation.delegatedBy,
          delegatedTo: delegation.delegatedTo,
          purpose: delegation.purpose,
          status: delegation.status as 'active' | 'pending' | 'completed' | 'cancelled',
        }));

        setTeamGroups([...groupItems, ...delegationItems]);
      } catch (error) {
        console.error('Error fetching team groups data:', error);
        toast({
          title: "Error",
          description: "Failed to load team groups data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchTeamGroupsData();
  }, [toast]);

  const columns: AdvancedTableColumn<TeamGroup>[] = [
    {
      accessorKey: 'name',
      title: 'Group/Delegation',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const group = row.original;
        const typeIcons = {
          group: Users,
          delegation: Shield,
        };
        const IconComponent = typeIcons[group.type];
        
        return (
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <IconComponent className="h-4 w-4 text-primary" />
            </div>
            <div>
              <div className="font-medium">{group.name}</div>
              {group.description && (
                <div className="text-xs text-muted-foreground">{group.description}</div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'type',
      title: 'Type',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const type = row.original.type;
        return (
          <Badge variant={type === 'group' ? 'default' : 'secondary'}>
            {type === 'group' ? 'Group' : 'Delegation'}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'members',
      title: 'Members',
      sortable: true,
      cell: ({ row }) => {
        const group = row.original;
        return (
          <div className="flex items-center space-x-2">
            <div className="flex -space-x-2">
              {group.members.slice(0, 3).map((member, index) => (
                <Avatar key={member.id} className="h-6 w-6 border-2 border-background">
                  <AvatarImage src={member.profileImageUrl} alt={member.displayName} />
                  <AvatarFallback className="text-xs">
                    {member.displayName?.split(' ').map(n => n[0]).join('').toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
              ))}
              {group.memberCount > 3 && (
                <div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                  <span className="text-xs font-medium">+{group.memberCount - 3}</span>
                </div>
              )}
            </div>
            <span className="text-sm text-muted-foreground">
              {group.memberCount} member{group.memberCount !== 1 ? 's' : ''}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'permissions',
      title: 'Permissions',
      sortable: false,
      cell: ({ row }) => {
        const permissions = row.original.permissions;
        const displayCount = Math.min(permissions.length, 2);
        
        return (
          <div className="flex flex-wrap gap-1">
            {permissions.slice(0, displayCount).map((permission, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {permission}
              </Badge>
            ))}
            {permissions.length > displayCount && (
              <Badge variant="outline" className="text-xs">
                +{permissions.length - displayCount} more
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      title: 'Status',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const group = row.original;
        
        if (group.type === 'delegation' && group.status) {
          const statusConfig = {
            active: { variant: 'default' as const, label: 'Active', className: 'bg-green-100 text-green-700' },
            pending: { variant: 'secondary' as const, label: 'Pending', className: 'bg-yellow-100 text-yellow-700' },
            completed: { variant: 'outline' as const, label: 'Completed', className: 'bg-gray-100 text-gray-700' },
            cancelled: { variant: 'destructive' as const, label: 'Cancelled', className: 'bg-red-100 text-red-700' },
          };
          const config = statusConfig[group.status];
          
          return (
            <Badge variant={config.variant} className={config.className}>
              {config.label}
            </Badge>
          );
        }
        
        return (
          <Badge variant={group.isActive ? 'default' : 'secondary'}>
            {group.isActive ? 'Active' : 'Inactive'}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'duration',
      title: 'Duration',
      sortable: false,
      cell: ({ row }) => {
        const group = row.original;
        
        if (group.type === 'delegation' && group.startDate && group.endDate) {
          const start = group.startDate;
          const end = group.endDate;
          const diffMs = end.getTime() - start.getTime();
          const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
          
          return (
            <div className="flex items-center text-sm">
              <Calendar className="h-3 w-3 mr-1 text-muted-foreground" />
              {diffDays} day{diffDays !== 1 ? 's' : ''}
            </div>
          );
        }
        
        return (
          <div className="flex items-center text-sm text-muted-foreground">
            <Clock className="h-3 w-3 mr-1" />
            Permanent
          </div>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      title: 'Created',
      sortable: true,
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        return (
          <div className="text-sm text-muted-foreground">
            {createdAt.toLocaleDateString()}
          </div>
        );
      },
    },
  ];

  const rowActions = (row: TeamGroup): AdvancedTableRowAction<TeamGroup>[] => {
    const actions: AdvancedTableRowAction<TeamGroup>[] = [
      {
        type: 'view',
        label: 'View Details',
        onClick: () => {
          toast({
            title: "View Group",
            description: `Viewing details for ${row.name}...`,
          });
        },
        icon: <Eye className="h-4 w-4" />,
      },
    ];

    if (onEditGroup) {
      actions.push({
        type: 'edit',
        label: 'Edit',
        onClick: () => onEditGroup(row),
        icon: <Edit2 className="h-4 w-4" />,
      });
    }

    if (onDeleteGroup) {
      actions.push({
        type: 'delete',
        label: 'Delete',
        onClick: () => onDeleteGroup(row.id),
        icon: <Trash2 className="h-4 w-4" />,
        variant: 'destructive',
      });
    }

    actions.push({
      type: 'custom',
      label: 'Manage Members',
      onClick: () => {
        toast({
          title: "Manage Members",
          description: `Managing members for ${row.name}...`,
        });
      },
      icon: <UserPlus className="h-4 w-4" />,
    });

    return actions;
  };

  const filters = [
    {
      id: 'type',
      label: 'Type',
      type: 'select' as const,
      options: [
        { label: 'Group', value: 'group' },
        { label: 'Delegation', value: 'delegation' },
      ],
    },
    {
      id: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Pending', value: 'pending' },
        { label: 'Completed', value: 'completed' },
        { label: 'Cancelled', value: 'cancelled' },
      ],
    },
  ];

  return (
    <AdvancedDataTable
      data={teamGroups}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="team-groups-delegations"
      searchPlaceholder="Search groups and delegations..."
      emptyMessage="No groups or delegations found. Create a new group to get started."
      loading={isLoading}
      className={className}
      variant="default"
    />
  );
}
