"use client";

import React, { useMemo } from 'react';
import type { Exhibition, EvexUser, TemporaryStaff, StaffingPlanItem } from '@/types/firestore';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Eye, Edit2, UserMinus, Clock, Calendar, Users, Building, Phone, Mail } from 'lucide-react';

interface TeamAssignmentTableProps {
  exhibition: Exhibition;
  users: EvexUser[];
  tempStaff: TemporaryStaff[];
  onEditAssignment?: (assignment: EnhancedStaffAssignment) => void;
  onRemoveAssignment?: (assignmentId: string) => void;
}

interface EnhancedStaffAssignment {
  id: string;
  type: 'Employee' | 'Temporary';
  name: string;
  email?: string;
  phone?: string;
  department?: string;
  role: string;
  shift?: string;
  shiftNotes?: string;
  profileImageUrl?: string;
  userId?: string;
  tempStaffId?: string;
  availability?: 'available' | 'busy' | 'unavailable';
  assignmentDate?: Date;
}

export default function TeamAssignmentTable({ 
  exhibition, 
  users, 
  tempStaff, 
  onEditAssignment, 
  onRemoveAssignment 
}: TeamAssignmentTableProps) {
  
  const enhancedAssignments = useMemo(() => {
    const staffingPlan = exhibition.staffingPlan || [];
    const assignments: EnhancedStaffAssignment[] = [];

    staffingPlan.forEach((item, index) => {
      let assignment: EnhancedStaffAssignment | null = null;

      if (item.assignedToUserId) {
        const user = users.find(u => u.id === item.assignedToUserId);
        if (user) {
          assignment = {
            id: item.id || `assignment-${index}`,
            type: 'Employee',
            name: user.displayName || user.email,
            email: user.email,
            phone: user.phone,
            department: user.department,
            role: item.role,
            shift: item.shift,
            shiftNotes: item.shiftNotes,
            profileImageUrl: user.profileImageUrl,
            userId: user.id,
            availability: 'available', // This would come from real-time data in production
            assignmentDate: new Date(), // This would be the actual assignment date
          };
        }
      } else if (item.assignedToTempStaffId) {
        const temp = tempStaff.find(t => t.id === item.assignedToTempStaffId);
        if (temp) {
          assignment = {
            id: item.id || `assignment-${index}`,
            type: 'Temporary',
            name: temp.name,
            email: temp.contactEmail,
            phone: temp.contactPhone,
            department: 'Temporary Staff',
            role: item.role,
            shift: item.shift,
            shiftNotes: item.shiftNotes,
            tempStaffId: temp.id,
            availability: 'available',
            assignmentDate: new Date(),
          };
        }
      } else if (item.assignedToPlaceholder) {
        assignment = {
          id: item.id || `assignment-${index}`,
          type: 'Temporary',
          name: item.assignedToPlaceholder,
          department: 'Temporary Staff',
          role: item.role,
          shift: item.shift,
          shiftNotes: item.shiftNotes,
          availability: 'available',
          assignmentDate: new Date(),
        };
      }

      if (assignment) {
        assignments.push(assignment);
      }
    });

    return assignments;
  }, [exhibition.staffingPlan, users, tempStaff]);

  const columns: AdvancedTableColumn<EnhancedStaffAssignment>[] = [
    {
      accessorKey: 'name',
      title: 'Team Member',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const assignment = row.original;
        return (
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={assignment.profileImageUrl} alt={assignment.name} />
              <AvatarFallback>
                {assignment.name.split(' ').map(n => n[0]).join('').toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{assignment.name}</div>
              <div className="text-xs text-muted-foreground flex items-center">
                <Badge variant={assignment.type === 'Employee' ? 'default' : 'secondary'} className="mr-2">
                  {assignment.type}
                </Badge>
                {assignment.email && (
                  <>
                    <Mail className="h-3 w-3 mr-1" />
                    {assignment.email}
                  </>
                )}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'role',
      title: 'Role',
      sortable: true,
      filterable: true,
      cell: ({ row }) => (
        <Badge variant="outline" className="font-medium">
          {row.original.role}
        </Badge>
      ),
    },
    {
      accessorKey: 'department',
      title: 'Department',
      sortable: true,
      filterable: true,
      cell: ({ row }) => (
        <div className="flex items-center">
          <Building className="h-4 w-4 mr-2 text-muted-foreground" />
          {row.original.department || 'N/A'}
        </div>
      ),
    },
    {
      accessorKey: 'shift',
      title: 'Shift',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const shift = row.original.shift;
        return shift ? (
          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
            {shift}
          </div>
        ) : (
          <span className="text-muted-foreground">Not specified</span>
        );
      },
    },
    {
      accessorKey: 'shiftNotes',
      title: 'Notes',
      sortable: false,
      filterable: true,
      cell: ({ row }) => {
        const notes = row.original.shiftNotes;
        return notes ? (
          <div className="max-w-xs truncate" title={notes}>
            {notes}
          </div>
        ) : (
          <span className="text-muted-foreground">No notes</span>
        );
      },
    },
    {
      accessorKey: 'availability',
      title: 'Status',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const availability = row.original.availability;
        const statusConfig = {
          available: { variant: 'default' as const, label: 'Available', className: 'bg-green-100 text-green-700' },
          busy: { variant: 'secondary' as const, label: 'Busy', className: 'bg-yellow-100 text-yellow-700' },
          unavailable: { variant: 'destructive' as const, label: 'Unavailable', className: 'bg-red-100 text-red-700' },
        };
        const config = statusConfig[availability || 'available'];
        
        return (
          <Badge variant={config.variant} className={config.className}>
            {config.label}
          </Badge>
        );
      },
    },
  ];

  const rowActions = (row: EnhancedStaffAssignment): AdvancedTableRowAction<EnhancedStaffAssignment>[] => {
    const actions: AdvancedTableRowAction<EnhancedStaffAssignment>[] = [];

    // View profile action
    if (row.type === 'Employee' && row.userId) {
      actions.push({
        type: 'view',
        label: 'View Profile',
        href: `/team/employees/${row.userId}`,
        icon: <Eye className="h-4 w-4" />,
      });
    } else if (row.type === 'Temporary' && row.tempStaffId) {
      actions.push({
        type: 'view',
        label: 'View Profile',
        href: `/team/temporary-staff/${row.tempStaffId}`,
        icon: <Eye className="h-4 w-4" />,
      });
    }

    // Edit assignment action
    if (onEditAssignment) {
      actions.push({
        type: 'edit',
        label: 'Edit Assignment',
        onClick: () => onEditAssignment(row),
        icon: <Edit2 className="h-4 w-4" />,
      });
    }

    // Remove assignment action
    if (onRemoveAssignment) {
      actions.push({
        type: 'delete',
        label: 'Remove Assignment',
        onClick: () => onRemoveAssignment(row.id),
        icon: <UserMinus className="h-4 w-4" />,
        variant: 'destructive',
      });
    }

    // Contact actions
    if (row.email) {
      actions.push({
        type: 'email',
        label: 'Send Email',
        onClick: () => window.open(`mailto:${row.email}`, '_blank'),
        icon: <Mail className="h-4 w-4" />,
      });
    }

    if (row.phone) {
      actions.push({
        type: 'call',
        label: 'Call',
        onClick: () => window.open(`tel:${row.phone}`, '_blank'),
        icon: <Phone className="h-4 w-4" />,
      });
    }

    return actions;
  };

  const filters = [
    {
      id: 'type',
      label: 'Staff Type',
      type: 'select' as const,
      options: [
        { label: 'Employee', value: 'Employee' },
        { label: 'Temporary', value: 'Temporary' },
      ],
    },
    {
      id: 'department',
      label: 'Department',
      type: 'select' as const,
      options: Array.from(new Set(enhancedAssignments.map(a => a.department).filter(Boolean))).map(dept => ({
        label: dept!,
        value: dept!,
      })),
    },
    {
      id: 'role',
      label: 'Role',
      type: 'select' as const,
      options: Array.from(new Set(enhancedAssignments.map(a => a.role))).map(role => ({
        label: role,
        value: role,
      })),
    },
    {
      id: 'availability',
      label: 'Availability',
      type: 'select' as const,
      options: [
        { label: 'Available', value: 'available' },
        { label: 'Busy', value: 'busy' },
        { label: 'Unavailable', value: 'unavailable' },
      ],
    },
  ];

  return (
    <AdvancedDataTable
      data={enhancedAssignments}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName={`team-assignments-${exhibition.name}`}
      searchPlaceholder="Search team assignments..."
      emptyMessage="No staff assigned yet. Click 'Edit Team' to make assignments."
      className="w-full"
      variant="default"
    />
  );
}
