'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  <PERSON><PERSON><PERSON>, Bar, LineChart, Line, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend
} from 'recharts';
import {
  TrendingUp, TrendingDown, DollarSign, Users, AlertTriangle,
  CheckCircle, Clock, Star, Building2, FileText, Calendar,
  ArrowLeft, Download, Filter, RefreshCw
} from 'lucide-react';
import Link from 'next/link';
import { useToast } from "@/hooks/use-toast";
import { getVendorsAction } from '../../actions';
import {
  getPurchaseOrders,
  getCollection,
  getAllExpenses,
  getDocumentsByQuery
} from '@/services/firestoreService';
import { COLLECTIONS } from '@/lib/collections';
import type {
  Vendor,
  PurchaseOrder,
  Invoice,
  Expense,
  VendorPerformanceReview,
  VendorDocument
} from '@/types/firestore';

interface VendorAnalytics {
  totalVendors: number;
  activeVendors: number;
  preferredVendors: number;
  averageRating: number;
  totalSpending: number;
  contractsExpiring: number;
  performanceScore: number;
  riskScore: number;
}

interface VendorPerformanceData {
  vendorName: string;
  rating: number;
  spending: number;
  contracts: number;
  riskLevel: 'Low' | 'Medium' | 'High';
  lastReview: string;
}

interface SpendingData {
  month: string;
  amount: number;
  vendors: number;
}

interface SpecializationData {
  name: string;
  value: number;
  color: string;
}

interface VendorFinancialData {
  vendorId: string;
  totalSpending: number;
  contractCount: number;
  lastOrderDate?: string;
  averageOrderValue: number;
}

const COLORS = ['#2563eb', '#dc2626', '#059669', '#d97706', '#7c3aed', '#db2777'];

export default function VendorAnalyticsPage() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [analytics, setAnalytics] = useState<VendorAnalytics | null>(null);
  const [performanceData, setPerformanceData] = useState<VendorPerformanceData[]>([]);
  const [spendingData, setSpendingData] = useState<SpendingData[]>([]);
  const [specializationData, setSpecializationData] = useState<SpecializationData[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState('6months');
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [vendorReviews, setVendorReviews] = useState<VendorPerformanceReview[]>([]);

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedPeriod]);

  const loadAnalyticsData = async () => {
    setIsLoading(true);
    try {
      // Load all required data from Firebase
      const [
        vendorsResult,
        purchaseOrdersResult,
        invoicesResult,
        expensesResult,
        vendorReviewsResult
      ] = await Promise.all([
        getVendorsAction(),
        getPurchaseOrders(),
        getCollection<Invoice>(COLLECTIONS.PURCHASE_INVOICES),
        getAllExpenses(),
        getCollection<VendorPerformanceReview>(COLLECTIONS.VENDOR_REVIEWS)
      ]);

      if (vendorsResult) {
        setVendors(vendorsResult);
        setPurchaseOrders(purchaseOrdersResult);
        setInvoices(invoicesResult);
        setExpenses(expensesResult);
        setVendorReviews(vendorReviewsResult);

        await generateRealAnalytics(
          vendorsResult,
          purchaseOrdersResult,
          invoicesResult,
          expensesResult,
          vendorReviewsResult
        );
      }
    } catch (error) {
      console.error('Error loading vendor analytics:', error);
      toast({
        title: "Error",
        description: "Failed to load vendor analytics data",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const generateRealAnalytics = async (
    vendorList: Vendor[],
    purchaseOrdersList: PurchaseOrder[],
    invoicesList: Invoice[],
    expensesList: Expense[],
    reviewsList: VendorPerformanceReview[]
  ) => {
    // Calculate basic analytics
    const totalVendors = vendorList.length;
    const activeVendors = vendorList.filter(v => v.status === 'Active').length;
    const preferredVendors = vendorList.filter(v => v.isPreferred).length;

    // Calculate average rating from actual reviews
    const ratingsSum = reviewsList.reduce((sum, review) => {
      return sum + (review.overallRating || 0);
    }, 0);
    const averageRating = reviewsList.length > 0 ? ratingsSum / reviewsList.length : 0;

    // Calculate real spending from purchase orders and invoices
    const totalSpending = purchaseOrdersList.reduce((sum, po) => {
      return sum + (po.totalAmount || 0);
    }, 0) + invoicesList.reduce((sum, invoice) => {
      return sum + (invoice.totalAmount || 0);
    }, 0);

    // Calculate contracts expiring from vendor documents
    const contractsExpiring = await calculateContractsExpiring(vendorList);

    const performanceScore = Math.floor(averageRating * 20); // Convert 5-star to 100-point scale
    const riskScore = Math.max(10, 100 - performanceScore); // Risk inversely related to performance

    setAnalytics({
      totalVendors,
      activeVendors,
      preferredVendors,
      averageRating,
      totalSpending,
      contractsExpiring,
      performanceScore,
      riskScore
    });

    // Generate real performance data from actual vendor data
    const vendorFinancialData = calculateVendorFinancialData(vendorList, purchaseOrdersList, invoicesList);
    const performanceList: VendorPerformanceData[] = vendorList.slice(0, 10).map(vendor => {
      const financialData = vendorFinancialData.find(vf => vf.vendorId === vendor.id);
      const vendorReviews = reviewsList.filter(r => r.vendorId === vendor.id);
      const latestReview = vendorReviews.sort((a, b) =>
        new Date(b.reviewDate).getTime() - new Date(a.reviewDate).getTime()
      )[0];

      return {
        vendorName: vendor.name,
        rating: latestReview?.overallRating || parseFloat(String(vendor.internalOverallRating || 0)) || 0,
        spending: financialData?.totalSpending || 0,
        contracts: financialData?.contractCount || 0,
        riskLevel: calculateRiskLevel(vendor, vendorReviews),
        lastReview: latestReview?.reviewDate ?
          new Date(latestReview.reviewDate).toISOString().split('T')[0] :
          'No reviews'
      };
    });
    setPerformanceData(performanceList);

    // Generate real spending data from purchase orders and invoices
    const spendingList = generateRealSpendingData(purchaseOrdersList, invoicesList, selectedPeriod);
    setSpendingData(spendingList);

    // Generate specialization data from actual vendor data
    const specializations = [...new Set(vendorList.map(v => v.specialization).filter(Boolean))];
    const specializationList: SpecializationData[] = specializations.map((spec, index) => ({
      name: spec!,
      value: vendorList.filter(v => v.specialization === spec).length,
      color: COLORS[index % COLORS.length]
    }));
    setSpecializationData(specializationList);
  };

  // Helper function to calculate vendor financial data
  const calculateVendorFinancialData = (
    vendors: Vendor[],
    purchaseOrders: PurchaseOrder[],
    invoices: Invoice[]
  ): VendorFinancialData[] => {
    return vendors.map(vendor => {
      const vendorPOs = purchaseOrders.filter(po => po.vendorId === vendor.id);
      const vendorInvoices = invoices.filter(inv => inv.vendorId === vendor.id);

      const totalSpending = vendorPOs.reduce((sum, po) => sum + (po.totalAmount || 0), 0) +
                           vendorInvoices.reduce((sum, inv) => sum + (inv.totalAmount || 0), 0);

      const contractCount = vendorPOs.length + vendorInvoices.length;
      const averageOrderValue = contractCount > 0 ? totalSpending / contractCount : 0;

      const lastOrderDate = [...vendorPOs, ...vendorInvoices]
        .map(item => new Date(item.createdAt || ''))
        .sort((a, b) => b.getTime() - a.getTime())[0]?.toISOString().split('T')[0];

      return {
        vendorId: vendor.id!,
        totalSpending,
        contractCount,
        lastOrderDate,
        averageOrderValue
      };
    });
  };

  // Helper function to calculate risk level based on real data
  const calculateRiskLevel = (vendor: Vendor, reviews: VendorPerformanceReview[]): 'Low' | 'Medium' | 'High' => {
    if (reviews.length === 0) return 'Medium';

    const avgRating = reviews.reduce((sum, r) => sum + (r.overallRating || 0), 0) / reviews.length;
    const recentReviews = reviews.filter(r =>
      new Date(r.reviewDate).getTime() > Date.now() - (90 * 24 * 60 * 60 * 1000)
    );

    if (avgRating >= 4 && recentReviews.length > 0) return 'Low';
    if (avgRating >= 3 && recentReviews.length > 0) return 'Medium';
    return 'High';
  };

  // Helper function to calculate contracts expiring
  const calculateContractsExpiring = async (vendors: Vendor[]): Promise<number> => {
    try {
      const now = new Date();
      const threeMonthsFromNow = new Date(now.getTime() + (90 * 24 * 60 * 60 * 1000));

      let expiringCount = 0;
      for (const vendor of vendors) {
        if (vendor.documents) {
          const expiringDocs = vendor.documents.filter(doc => {
            if (!doc.expiryDate) return false;
            const expiryDate = new Date(doc.expiryDate);
            return expiryDate <= threeMonthsFromNow && expiryDate > now;
          });
          if (expiringDocs.length > 0) expiringCount++;
        }
      }
      return expiringCount;
    } catch (error) {
      console.error('Error calculating expiring contracts:', error);
      return 0;
    }
  };

  // Helper function to generate real spending data
  const generateRealSpendingData = (
    purchaseOrders: PurchaseOrder[],
    invoices: Invoice[],
    period: string
  ): SpendingData[] => {
    const now = new Date();
    const months = [];
    const monthCount = period === '3months' ? 3 : period === '6months' ? 6 : 12;

    // Generate month labels
    for (let i = monthCount - 1; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      months.push({
        month: date.toLocaleDateString('en-US', { month: 'short' }),
        year: date.getFullYear(),
        monthIndex: date.getMonth()
      });
    }

    return months.map(({ month, year, monthIndex }) => {
      // Filter orders and invoices for this month
      const monthPOs = purchaseOrders.filter(po => {
        const orderDate = new Date(po.orderDate);
        return orderDate.getFullYear() === year && orderDate.getMonth() === monthIndex;
      });

      const monthInvoices = invoices.filter(inv => {
        const invoiceDate = new Date(inv.invoiceDate);
        return invoiceDate.getFullYear() === year && invoiceDate.getMonth() === monthIndex;
      });

      const amount = monthPOs.reduce((sum, po) => sum + (po.totalAmount || 0), 0) +
                    monthInvoices.reduce((sum, inv) => sum + (inv.totalAmount || 0), 0);

      const vendorIds = new Set([
        ...monthPOs.map(po => po.vendorId),
        ...monthInvoices.map(inv => inv.vendorId)
      ]);

      return {
        month,
        amount,
        vendors: vendorIds.size
      };
    });
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  const exportAnalytics = () => {
    // Mock export functionality
    toast({
      title: "Export Started",
      description: "Analytics report will be downloaded shortly"
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading vendor analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/procurement/vendors">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Vendors
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Vendor Analytics</h1>
            <p className="text-muted-foreground">
              Comprehensive insights into vendor performance and relationships
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3months">Last 3 Months</SelectItem>
              <SelectItem value="6months">Last 6 Months</SelectItem>
              <SelectItem value="1year">Last Year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={exportAnalytics}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" onClick={loadAnalyticsData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Vendors</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.totalVendors}</div>
              <p className="text-xs text-muted-foreground">
                {analytics.activeVendors} active, {analytics.preferredVendors} preferred
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Spending</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${analytics.totalSpending.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 inline mr-1" />
                +12% from last period
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Performance</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.averageRating.toFixed(1)}/5.0</div>
              <Progress value={analytics.performanceScore} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Risk Score</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{analytics.riskScore}%</div>
              <p className="text-xs text-muted-foreground">
                {analytics.contractsExpiring} contracts expiring soon
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Charts and Analysis */}
      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="spending">Spending</TabsTrigger>
          <TabsTrigger value="distribution">Distribution</TabsTrigger>
          <TabsTrigger value="risk">Risk Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Vendors</CardTitle>
                <CardDescription>Based on ratings and contract performance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {performanceData.slice(0, 5).map((vendor, index) => (
                    <div key={vendor.vendorName} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium">{vendor.vendorName}</p>
                          <p className="text-sm text-muted-foreground">
                            {vendor.contracts} contracts • ${vendor.spending.toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          <span className="font-medium">{vendor.rating.toFixed(1)}</span>
                        </div>
                        <Badge variant={vendor.riskLevel === 'Low' ? 'default' : vendor.riskLevel === 'Medium' ? 'secondary' : 'destructive'}>
                          {vendor.riskLevel} Risk
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Trends</CardTitle>
                <CardDescription>Vendor performance over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={spendingData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="vendors" stroke="#2563eb" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="spending" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Spending Analysis</CardTitle>
              <CardDescription>Monthly spending trends and vendor costs</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={spendingData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${Number(value).toLocaleString()}`, 'Spending']} />
                  <Bar dataKey="amount" fill="#2563eb" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="distribution" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Vendor Distribution</CardTitle>
              <CardDescription>Breakdown by specialization and regions</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <PieChart>
                  <Pie
                    data={specializationData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {specializationData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="risk" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Risk Assessment</CardTitle>
              <CardDescription>Vendor risk analysis and mitigation recommendations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {performanceData.filter(v => v.riskLevel === 'Low').length}
                    </div>
                    <p className="text-sm text-muted-foreground">Low Risk Vendors</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">
                      {performanceData.filter(v => v.riskLevel === 'Medium').length}
                    </div>
                    <p className="text-sm text-muted-foreground">Medium Risk Vendors</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {performanceData.filter(v => v.riskLevel === 'High').length}
                    </div>
                    <p className="text-sm text-muted-foreground">High Risk Vendors</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
