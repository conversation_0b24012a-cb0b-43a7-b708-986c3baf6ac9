'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Star, TrendingUp, TrendingDown, Clock, CheckCircle, AlertTriangle,
  DollarSign, Shield, Award, Target, Calendar, FileText, Save
} from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import { getCollection, getPurchaseOrders } from '@/services/firestoreService';
import { COLLECTIONS } from '@/lib/collections';
import type { Vendor, VendorPerformanceReview, PurchaseOrder, Invoice } from '@/types/firestore';

interface PerformanceMetric {
  id: string;
  name: string;
  category: 'quality' | 'delivery' | 'cost' | 'service' | 'compliance';
  weight: number;
  score: number;
  maxScore: number;
  description: string;
  lastUpdated: string;
  trend: 'up' | 'down' | 'stable';
}

interface VendorPerformanceScore {
  vendorId: string;
  overallScore: number;
  categoryScores: Record<string, number>;
  metrics: PerformanceMetric[];
  lastReviewDate: string;
  nextReviewDate: string;
  reviewNotes: string;
  riskLevel: 'low' | 'medium' | 'high';
  recommendations: string[];
}

const defaultMetrics: Omit<PerformanceMetric, 'score' | 'lastUpdated' | 'trend'>[] = [
  {
    id: 'quality_standards',
    name: 'Quality Standards',
    category: 'quality',
    weight: 0.25,
    maxScore: 100,
    description: 'Adherence to quality standards and specifications'
  },
  {
    id: 'delivery_timeliness',
    name: 'Delivery Timeliness',
    category: 'delivery',
    weight: 0.20,
    maxScore: 100,
    description: 'On-time delivery performance'
  },
  {
    id: 'cost_competitiveness',
    name: 'Cost Competitiveness',
    category: 'cost',
    weight: 0.15,
    maxScore: 100,
    description: 'Pricing competitiveness and value for money'
  },
  {
    id: 'customer_service',
    name: 'Customer Service',
    category: 'service',
    weight: 0.15,
    maxScore: 100,
    description: 'Responsiveness and communication quality'
  },
  {
    id: 'compliance_adherence',
    name: 'Compliance Adherence',
    category: 'compliance',
    weight: 0.10,
    maxScore: 100,
    description: 'Regulatory and contractual compliance'
  },
  {
    id: 'innovation_capability',
    name: 'Innovation Capability',
    category: 'service',
    weight: 0.10,
    maxScore: 100,
    description: 'Ability to provide innovative solutions'
  },
  {
    id: 'risk_management',
    name: 'Risk Management',
    category: 'compliance',
    weight: 0.05,
    maxScore: 100,
    description: 'Risk mitigation and business continuity'
  }
];

// Helper function to calculate real metrics from vendor data
const calculateRealMetrics = async (vendor: Vendor): Promise<PerformanceMetric[]> => {
  try {
    // Fetch real data from Firebase
    const [reviews, purchaseOrders, invoices] = await Promise.all([
      getCollection<VendorPerformanceReview>(COLLECTIONS.VENDOR_REVIEWS),
      getPurchaseOrders(),
      getCollection<Invoice>(COLLECTIONS.PURCHASE_INVOICES)
    ]);

    // Filter data for this vendor
    const vendorReviews = reviews.filter(r => r.vendorId === vendor.id);
    const vendorPOs = purchaseOrders.filter(po => po.vendorId === vendor.id);
    const vendorInvoices = invoices.filter(inv => inv.vendorId === vendor.id);

    return defaultMetrics.map(metric => {
      let score = 0;
      let trend: 'up' | 'down' | 'stable' = 'stable';

      switch (metric.id) {
        case 'quality_standards':
          if (vendorReviews.length > 0) {
            score = vendorReviews.reduce((sum, r) => sum + (r.qualityRating || 0), 0) / vendorReviews.length * 20;
          } else {
            score = parseFloat(String(vendor.internalOverallRating || 0)) * 20;
          }
          break;

        case 'delivery_timeliness':
          if (vendorReviews.length > 0) {
            score = vendorReviews.reduce((sum, r) => sum + (r.timelinessRating || 0), 0) / vendorReviews.length * 20;
          } else {
            score = 75; // Default score if no reviews
          }
          break;

        case 'cost_competitiveness':
          if (vendorReviews.length > 0) {
            score = vendorReviews.reduce((sum, r) => sum + (r.costEffectivenessRating || 0), 0) / vendorReviews.length * 20;
          } else {
            score = 70; // Default score if no reviews
          }
          break;

        case 'communication_quality':
          if (vendorReviews.length > 0) {
            score = vendorReviews.reduce((sum, r) => sum + (r.communicationRating || 0), 0) / vendorReviews.length * 20;
          } else {
            score = 80; // Default score if no reviews
          }
          break;

        case 'contract_compliance':
          // Calculate based on contract fulfillment
          const fulfilledOrders = vendorPOs.filter(po => po.status === 'Fulfilled').length;
          const totalOrders = vendorPOs.length;
          score = totalOrders > 0 ? (fulfilledOrders / totalOrders) * 100 : 85;
          break;

        default:
          score = 75; // Default score
      }

      // Calculate trend based on recent vs older reviews
      if (vendorReviews.length >= 2) {
        const recentReviews = vendorReviews
          .filter(r => new Date(r.reviewDate).getTime() > Date.now() - (90 * 24 * 60 * 60 * 1000))
          .slice(0, 3);
        const olderReviews = vendorReviews
          .filter(r => new Date(r.reviewDate).getTime() <= Date.now() - (90 * 24 * 60 * 60 * 1000))
          .slice(0, 3);

        if (recentReviews.length > 0 && olderReviews.length > 0) {
          const recentAvg = recentReviews.reduce((sum, r) => sum + (r.overallRating || 0), 0) / recentReviews.length;
          const olderAvg = olderReviews.reduce((sum, r) => sum + (r.overallRating || 0), 0) / olderReviews.length;

          if (recentAvg > olderAvg + 0.2) trend = 'up';
          else if (recentAvg < olderAvg - 0.2) trend = 'down';
        }
      }

      return {
        ...metric,
        score: Math.min(100, Math.max(0, score)),
        lastUpdated: new Date().toISOString().split('T')[0],
        trend
      };
    });
  } catch (error) {
    console.error('Error calculating real metrics:', error);
    // Return default metrics with basic scores if error occurs
    return defaultMetrics.map(metric => ({
      ...metric,
      score: 75,
      lastUpdated: new Date().toISOString().split('T')[0],
      trend: 'stable' as const
    }));
  }
};

interface VendorPerformanceScoringProps {
  vendor: Vendor;
  onScoreUpdate?: (score: VendorPerformanceScore) => void;
}

export const VendorPerformanceScoring: React.FC<VendorPerformanceScoringProps> = ({
  vendor,
  onScoreUpdate
}) => {
  const { toast } = useToast();
  const [performanceScore, setPerformanceScore] = useState<VendorPerformanceScore | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    loadPerformanceScore();
  }, [vendor.id]);

  const loadPerformanceScore = async () => {
    try {
      // Calculate real performance score from vendor data and reviews
      const realScore: VendorPerformanceScore = {
        vendorId: vendor.id!,
        overallScore: 0,
        categoryScores: {},
        metrics: await calculateRealMetrics(vendor),
        lastReviewDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        nextReviewDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        reviewNotes: 'Regular quarterly performance review. Overall performance is satisfactory with room for improvement in delivery timeliness.',
        riskLevel: 'low',
        recommendations: [
          'Improve delivery scheduling and communication',
          'Enhance quality control processes',
          'Consider cost optimization opportunities'
        ]
      };

      // Calculate overall score and category scores
      const categoryTotals: Record<string, { score: number; weight: number }> = {};
      let totalWeightedScore = 0;
      let totalWeight = 0;

      mockScore.metrics.forEach(metric => {
        const weightedScore = (metric.score / metric.maxScore) * metric.weight * 100;
        totalWeightedScore += weightedScore;
        totalWeight += metric.weight;

        if (!categoryTotals[metric.category]) {
          categoryTotals[metric.category] = { score: 0, weight: 0 };
        }
        categoryTotals[metric.category].score += weightedScore;
        categoryTotals[metric.category].weight += metric.weight;
      });

      mockScore.overallScore = totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
      
      Object.keys(categoryTotals).forEach(category => {
        const categoryData = categoryTotals[category];
        mockScore.categoryScores[category] = categoryData.weight > 0 ? 
          categoryData.score / categoryData.weight : 0;
      });

      // Determine risk level based on overall score
      if (mockScore.overallScore >= 80) {
        mockScore.riskLevel = 'low';
      } else if (mockScore.overallScore >= 60) {
        mockScore.riskLevel = 'medium';
      } else {
        mockScore.riskLevel = 'high';
      }

      setPerformanceScore(mockScore);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load performance score",
        variant: "destructive"
      });
    }
  };

  const updateMetricScore = (metricId: string, newScore: number) => {
    if (!performanceScore) return;

    const updatedMetrics = performanceScore.metrics.map(metric =>
      metric.id === metricId 
        ? { ...metric, score: Math.max(0, Math.min(metric.maxScore, newScore)) }
        : metric
    );

    // Recalculate scores
    const categoryTotals: Record<string, { score: number; weight: number }> = {};
    let totalWeightedScore = 0;
    let totalWeight = 0;

    updatedMetrics.forEach(metric => {
      const weightedScore = (metric.score / metric.maxScore) * metric.weight * 100;
      totalWeightedScore += weightedScore;
      totalWeight += metric.weight;

      if (!categoryTotals[metric.category]) {
        categoryTotals[metric.category] = { score: 0, weight: 0 };
      }
      categoryTotals[metric.category].score += weightedScore;
      categoryTotals[metric.category].weight += metric.weight;
    });

    const newOverallScore = totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
    const newCategoryScores: Record<string, number> = {};
    
    Object.keys(categoryTotals).forEach(category => {
      const categoryData = categoryTotals[category];
      newCategoryScores[category] = categoryData.weight > 0 ? 
        categoryData.score / categoryData.weight : 0;
    });

    // Determine new risk level
    let newRiskLevel: 'low' | 'medium' | 'high' = 'low';
    if (newOverallScore < 60) {
      newRiskLevel = 'high';
    } else if (newOverallScore < 80) {
      newRiskLevel = 'medium';
    }

    setPerformanceScore({
      ...performanceScore,
      metrics: updatedMetrics,
      overallScore: newOverallScore,
      categoryScores: newCategoryScores,
      riskLevel: newRiskLevel
    });
  };

  const savePerformanceScore = async () => {
    if (!performanceScore) return;

    setIsSaving(true);
    try {
      // Mock save - in real app, this would save to API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Performance Score Saved",
        description: "Vendor performance score has been updated successfully"
      });
      
      setIsEditing(false);
      onScoreUpdate?.(performanceScore);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save performance score",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <div className="h-4 w-4" />;
    }
  };

  const getRiskBadge = (riskLevel: 'low' | 'medium' | 'high') => {
    const variants = {
      low: { variant: 'default' as const, className: 'bg-green-100 text-green-800', label: 'Low Risk' },
      medium: { variant: 'secondary' as const, className: 'bg-yellow-100 text-yellow-800', label: 'Medium Risk' },
      high: { variant: 'destructive' as const, className: 'bg-red-100 text-red-800', label: 'High Risk' }
    };

    const config = variants[riskLevel];
    return (
      <Badge variant={config.variant} className={config.className}>
        {config.label}
      </Badge>
    );
  };

  if (!performanceScore) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <Clock className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading performance score...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overall Score Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Performance Score
              </CardTitle>
              <CardDescription>
                Overall vendor performance assessment
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {getRiskBadge(performanceScore.riskLevel)}
              <Button
                variant={isEditing ? "default" : "outline"}
                size="sm"
                onClick={() => setIsEditing(!isEditing)}
              >
                {isEditing ? "Cancel" : "Edit Scores"}
              </Button>
              {isEditing && (
                <Button onClick={savePerformanceScore} disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className={`text-4xl font-bold ${getScoreColor(performanceScore.overallScore)}`}>
                {Math.round(performanceScore.overallScore)}
              </div>
              <p className="text-sm text-muted-foreground">Overall Score</p>
              <Progress value={performanceScore.overallScore} className="mt-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Last Review:</span>
                <span className="text-sm font-medium">
                  {new Date(performanceScore.lastReviewDate).toLocaleDateString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Next Review:</span>
                <span className="text-sm font-medium">
                  {new Date(performanceScore.nextReviewDate).toLocaleDateString()}
                </span>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-semibold text-sm">Category Scores</h4>
              {Object.entries(performanceScore.categoryScores).map(([category, score]) => (
                <div key={category} className="flex items-center justify-between">
                  <span className="text-sm capitalize">{category}</span>
                  <Badge variant={getScoreBadgeVariant(score)}>
                    {Math.round(score)}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Metrics */}
      <Tabs defaultValue="metrics" className="space-y-4">
        <TabsList>
          <TabsTrigger value="metrics">Performance Metrics</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="history">Review History</TabsTrigger>
        </TabsList>

        <TabsContent value="metrics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {performanceScore.metrics.map((metric) => (
              <Card key={metric.id}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">{metric.name}</CardTitle>
                    <div className="flex items-center gap-2">
                      {getTrendIcon(metric.trend)}
                      <Badge variant="outline" className="text-xs">
                        {Math.round(metric.weight * 100)}%
                      </Badge>
                    </div>
                  </div>
                  <CardDescription className="text-sm">
                    {metric.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Score:</span>
                      <div className="flex items-center gap-2">
                        {isEditing ? (
                          <Input
                            type="number"
                            min="0"
                            max={metric.maxScore}
                            value={metric.score}
                            onChange={(e) => updateMetricScore(metric.id, parseInt(e.target.value) || 0)}
                            className="w-20 h-8"
                          />
                        ) : (
                          <span className={`font-bold ${getScoreColor((metric.score / metric.maxScore) * 100)}`}>
                            {metric.score}
                          </span>
                        )}
                        <span className="text-sm text-muted-foreground">/ {metric.maxScore}</span>
                      </div>
                    </div>
                    <Progress value={(metric.score / metric.maxScore) * 100} />
                    <div className="text-xs text-muted-foreground">
                      Last updated: {new Date(metric.lastUpdated).toLocaleDateString()}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Recommendations</CardTitle>
              <CardDescription>
                Suggested improvements based on current performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {performanceScore.recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                    <Target className="h-5 w-5 text-blue-600 mt-0.5" />
                    <p className="text-sm">{recommendation}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Review Notes</CardTitle>
            </CardHeader>
            <CardContent>
              {isEditing ? (
                <Textarea
                  value={performanceScore.reviewNotes}
                  onChange={(e) => setPerformanceScore({
                    ...performanceScore,
                    reviewNotes: e.target.value
                  })}
                  rows={4}
                  placeholder="Add review notes..."
                />
              ) : (
                <p className="text-sm">{performanceScore.reviewNotes}</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
