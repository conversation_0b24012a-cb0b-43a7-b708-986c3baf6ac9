'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  ArrowLeft, Search, Plus, X, Star, DollarSign, MapPin, 
  Building2, Users, Calendar, FileText, Shield, Award,
  TrendingUp, TrendingDown, Minus, Check, AlertTriangle
} from 'lucide-react';
import Link from 'next/link';
import { useToast } from "@/hooks/use-toast";
import { getVendorsAction } from '../../actions';
import type { Vendor } from '@/types/firestore';

interface ComparisonCriteria {
  id: string;
  label: string;
  category: 'basic' | 'performance' | 'financial' | 'compliance';
  weight: number;
}

const comparisonCriteria: ComparisonCriteria[] = [
  { id: 'rating', label: 'Overall Rating', category: 'performance', weight: 0.25 },
  { id: 'experience', label: 'Years of Experience', category: 'basic', weight: 0.15 },
  { id: 'pricing', label: 'Pricing Competitiveness', category: 'financial', weight: 0.20 },
  { id: 'reliability', label: 'Delivery Reliability', category: 'performance', weight: 0.20 },
  { id: 'compliance', label: 'Compliance Score', category: 'compliance', weight: 0.10 },
  { id: 'innovation', label: 'Innovation Capability', category: 'performance', weight: 0.10 }
];

interface VendorScore {
  vendorId: string;
  scores: Record<string, number>;
  totalScore: number;
  rank: number;
}

export default function VendorComparePage() {
  const { toast } = useToast();
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [selectedVendors, setSelectedVendors] = useState<Vendor[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [comparisonScores, setComparisonScores] = useState<VendorScore[]>([]);
  const [selectedCriteria, setSelectedCriteria] = useState<string[]>(
    comparisonCriteria.map(c => c.id)
  );

  useEffect(() => {
    loadVendors();
  }, []);

  useEffect(() => {
    if (selectedVendors.length > 0) {
      calculateComparisonScores();
    }
  }, [selectedVendors, selectedCriteria]);

  const loadVendors = async () => {
    setIsLoading(true);
    try {
      const result = await getVendorsAction();
      if (result) {
        setVendors(result.filter(v => v.status === 'Active'));
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load vendors",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const addVendorToComparison = (vendor: Vendor) => {
    if (selectedVendors.length >= 4) {
      toast({
        title: "Comparison Limit",
        description: "You can compare up to 4 vendors at a time",
        variant: "destructive"
      });
      return;
    }

    if (selectedVendors.find(v => v.id === vendor.id)) {
      toast({
        title: "Already Added",
        description: "This vendor is already in the comparison",
        variant: "destructive"
      });
      return;
    }

    setSelectedVendors(prev => [...prev, vendor]);
  };

  const removeVendorFromComparison = (vendorId: string) => {
    setSelectedVendors(prev => prev.filter(v => v.id !== vendorId));
  };

  const calculateComparisonScores = () => {
    const scores: VendorScore[] = selectedVendors.map(vendor => {
      const vendorScores: Record<string, number> = {};
      let totalScore = 0;

      selectedCriteria.forEach(criteriaId => {
        const criteria = comparisonCriteria.find(c => c.id === criteriaId);
        if (!criteria) return;

        let score = 0;
        
        // Calculate scores based on real vendor data
        switch (criteriaId) {
          case 'rating':
            score = parseFloat(String(vendor.internalOverallRating || 0)) * 20; // Convert 5-star to 100-point
            break;
          case 'experience':
            // Calculate experience based on vendor creation date and past projects
            const createdDate = vendor.createdAt ? new Date(vendor.createdAt) : new Date();
            const yearsActive = Math.max(0, (Date.now() - createdDate.getTime()) / (365 * 24 * 60 * 60 * 1000));
            const projectsCount = vendor.pastProjects ? vendor.pastProjects.split(',').length : 0;
            score = Math.min(100, (yearsActive * 10) + (projectsCount * 5));
            break;
          case 'pricing':
            // Calculate pricing competitiveness based on preferred status and rating
            const baseScore = vendor.isPreferred ? 85 : 70;
            const ratingBonus = parseFloat(String(vendor.internalOverallRating || 0)) * 5;
            score = Math.min(100, baseScore + ratingBonus);
            break;
          case 'reliability':
            // Calculate reliability based on status, preferred status, and rating
            let reliabilityScore = vendor.status === 'Active' ? 80 : 40;
            if (vendor.isPreferred) reliabilityScore += 15;
            reliabilityScore += parseFloat(String(vendor.internalOverallRating || 0)) * 2;
            score = Math.min(100, reliabilityScore);
            break;
          case 'compliance':
            // Calculate compliance based on document status and certifications
            const hasActiveDocuments = vendor.documents?.some(doc => doc.status === 'Active') || false;
            const hasCertifications = vendor.documents?.some(doc => doc.documentType === 'Certification') || false;
            score = (hasActiveDocuments ? 50 : 0) + (hasCertifications ? 30 : 0) + 20; // Base 20 points
            break;
          case 'innovation':
            // Calculate innovation based on services offered and specialization
            const servicesCount = vendor.services?.length || 0;
            const isInnovativeSpecialization = ['Software Provider', 'Marketing Agency', 'AV Equipment'].includes(vendor.specialization);
            score = Math.min(100, (servicesCount * 10) + (isInnovativeSpecialization ? 40 : 20));
            break;
        }

        vendorScores[criteriaId] = score;
        totalScore += score * criteria.weight;
      });

      return {
        vendorId: vendor.id!,
        scores: vendorScores,
        totalScore,
        rank: 0
      };
    });

    // Assign ranks
    scores.sort((a, b) => b.totalScore - a.totalScore);
    scores.forEach((score, index) => {
      score.rank = index + 1;
    });

    setComparisonScores(scores);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 80) return <TrendingUp className="h-4 w-4 text-green-600" />;
    if (score >= 60) return <Minus className="h-4 w-4 text-yellow-600" />;
    return <TrendingDown className="h-4 w-4 text-red-600" />;
  };

  const filteredVendors = vendors.filter(vendor =>
    vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.specialization?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/procurement/vendors">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Vendors
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Vendor Comparison</h1>
            <p className="text-muted-foreground">
              Compare vendors side-by-side across multiple criteria
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Vendor Selection */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Select Vendors</CardTitle>
              <CardDescription>Choose up to 4 vendors to compare</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search vendors..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9"
                />
              </div>

              <div className="space-y-2 max-h-96 overflow-y-auto">
                {filteredVendors.map(vendor => (
                  <div
                    key={vendor.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50"
                  >
                    <div className="flex-1">
                      <h4 className="font-medium">{vendor.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        {vendor.specialization}
                      </p>
                      <div className="flex items-center gap-2 mt-1">
                        <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                        <span className="text-xs">{vendor.internalOverallRating || 'N/A'}</span>
                        {vendor.isPreferred && (
                          <Badge variant="secondary" className="text-xs">Preferred</Badge>
                        )}
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => addVendorToComparison(vendor)}
                      disabled={selectedVendors.length >= 4 || selectedVendors.some(v => v.id === vendor.id)}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Comparison Criteria */}
          <Card className="mt-4">
            <CardHeader>
              <CardTitle>Comparison Criteria</CardTitle>
              <CardDescription>Select criteria for comparison</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {comparisonCriteria.map(criteria => (
                  <div key={criteria.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={criteria.id}
                      checked={selectedCriteria.includes(criteria.id)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedCriteria(prev => [...prev, criteria.id]);
                        } else {
                          setSelectedCriteria(prev => prev.filter(id => id !== criteria.id));
                        }
                      }}
                    />
                    <label htmlFor={criteria.id} className="text-sm font-medium">
                      {criteria.label}
                    </label>
                    <Badge variant="outline" className="text-xs">
                      {Math.round(criteria.weight * 100)}%
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Comparison Table */}
        <div className="lg:col-span-2">
          {selectedVendors.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Vendors Selected</h3>
                <p className="text-muted-foreground text-center">
                  Select vendors from the left panel to start comparing them
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {/* Selected Vendors Overview */}
              <Card>
                <CardHeader>
                  <CardTitle>Selected Vendors ({selectedVendors.length}/4)</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {selectedVendors.map(vendor => (
                      <Badge key={vendor.id} variant="secondary" className="flex items-center gap-2">
                        {vendor.name}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-4 w-4 p-0"
                          onClick={() => removeVendorFromComparison(vendor.id!)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Comparison Results */}
              <Card>
                <CardHeader>
                  <CardTitle>Comparison Results</CardTitle>
                  <CardDescription>Vendor performance across selected criteria</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-2">Criteria</th>
                          {selectedVendors.map(vendor => (
                            <th key={vendor.id} className="text-center p-2 min-w-[120px]">
                              <div>
                                <div className="font-semibold">{vendor.name}</div>
                                {comparisonScores.find(s => s.vendorId === vendor.id) && (
                                  <Badge variant="outline" className="mt-1">
                                    Rank #{comparisonScores.find(s => s.vendorId === vendor.id)?.rank}
                                  </Badge>
                                )}
                              </div>
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {/* Overall Score */}
                        <tr className="border-b bg-muted/50">
                          <td className="p-2 font-semibold">Overall Score</td>
                          {selectedVendors.map(vendor => {
                            const vendorScore = comparisonScores.find(s => s.vendorId === vendor.id);
                            return (
                              <td key={vendor.id} className="text-center p-2">
                                <div className="flex items-center justify-center gap-2">
                                  <span className={`font-bold ${getScoreColor(vendorScore?.totalScore || 0)}`}>
                                    {Math.round(vendorScore?.totalScore || 0)}
                                  </span>
                                  {getScoreIcon(vendorScore?.totalScore || 0)}
                                </div>
                              </td>
                            );
                          })}
                        </tr>

                        {/* Individual Criteria */}
                        {selectedCriteria.map(criteriaId => {
                          const criteria = comparisonCriteria.find(c => c.id === criteriaId);
                          if (!criteria) return null;

                          return (
                            <tr key={criteriaId} className="border-b">
                              <td className="p-2">
                                <div>
                                  <span className="font-medium">{criteria.label}</span>
                                  <Badge variant="outline" className="ml-2 text-xs">
                                    {Math.round(criteria.weight * 100)}%
                                  </Badge>
                                </div>
                              </td>
                              {selectedVendors.map(vendor => {
                                const vendorScore = comparisonScores.find(s => s.vendorId === vendor.id);
                                const score = vendorScore?.scores[criteriaId] || 0;
                                return (
                                  <td key={vendor.id} className="text-center p-2">
                                    <div className="flex items-center justify-center gap-2">
                                      <span className={getScoreColor(score)}>
                                        {Math.round(score)}
                                      </span>
                                      {getScoreIcon(score)}
                                    </div>
                                  </td>
                                );
                              })}
                            </tr>
                          );
                        })}

                        {/* Basic Information */}
                        <tr className="border-b">
                          <td className="p-2 font-medium">Contact Person</td>
                          {selectedVendors.map(vendor => (
                            <td key={vendor.id} className="text-center p-2">
                              {vendor.contactPerson}
                            </td>
                          ))}
                        </tr>

                        <tr className="border-b">
                          <td className="p-2 font-medium">Location</td>
                          {selectedVendors.map(vendor => (
                            <td key={vendor.id} className="text-center p-2">
                              {vendor.city}, {vendor.country}
                            </td>
                          ))}
                        </tr>

                        <tr className="border-b">
                          <td className="p-2 font-medium">Preferred Vendor</td>
                          {selectedVendors.map(vendor => (
                            <td key={vendor.id} className="text-center p-2">
                              {vendor.isPreferred ? (
                                <Check className="h-4 w-4 text-green-600 mx-auto" />
                              ) : (
                                <X className="h-4 w-4 text-red-600 mx-auto" />
                              )}
                            </td>
                          ))}
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>

              {/* Recommendations */}
              {comparisonScores.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Recommendations</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {comparisonScores.slice(0, 2).map((score, index) => {
                        const vendor = selectedVendors.find(v => v.id === score.vendorId);
                        if (!vendor) return null;

                        return (
                          <div key={score.vendorId} className="flex items-center gap-3 p-3 border rounded-lg">
                            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-bold">
                              {index + 1}
                            </div>
                            <div className="flex-1">
                              <h4 className="font-semibold">{vendor.name}</h4>
                              <p className="text-sm text-muted-foreground">
                                Overall score: {Math.round(score.totalScore)} • {vendor.specialization}
                              </p>
                            </div>
                            <Badge variant={index === 0 ? 'default' : 'secondary'}>
                              {index === 0 ? 'Recommended' : 'Alternative'}
                            </Badge>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
