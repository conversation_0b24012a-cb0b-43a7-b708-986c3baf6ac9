"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { 
  Settings,
  User,
  Edit2,
  Eye,
  Download,
  Upload,
  Bell,
  Palette,
  Globe,
  Shield,
  Monitor,
  Smartphone,
  Mail,
  MessageSquare
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { formatDistanceToNow } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

interface UserSettings {
  id?: string;
  userId: string;
  userEmail?: string;
  userName?: string;
  dashboardLayout?: string;
  theme?: 'light' | 'dark' | 'system';
  language?: string;
  timezone?: string;
  notificationPreferences?: {
    email: boolean;
    push: boolean;
    sms: boolean;
    inApp: boolean;
  };
  privacySettings?: {
    profileVisibility: 'public' | 'private' | 'team';
    activityTracking: boolean;
    dataSharing: boolean;
  };
  accessibilitySettings?: {
    fontSize: 'small' | 'medium' | 'large';
    highContrast: boolean;
    screenReader: boolean;
  };
  createdAt?: Timestamp | Date | string;
  updatedAt?: Timestamp | Date | string;
}

const themeConfig = {
  'light': { color: 'bg-yellow-100 text-yellow-800', icon: Monitor },
  'dark': { color: 'bg-gray-100 text-gray-800', icon: Monitor },
  'system': { color: 'bg-blue-100 text-blue-800', icon: Monitor },
};

const privacyConfig = {
  'public': { color: 'bg-green-100 text-green-800' },
  'private': { color: 'bg-red-100 text-red-800' },
  'team': { color: 'bg-blue-100 text-blue-800' },
};

interface UserSettingsTableProps {
  userId?: string;
  showUserFilter?: boolean;
}

export default function UserSettingsTable({ 
  userId, 
  showUserFilter = true 
}: UserSettingsTableProps) {
  const { toast } = useToast();
  const [userSettings, setUserSettings] = useState<UserSettings[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load user settings from Firebase
  useEffect(() => {
    loadUserSettings();
  }, [userId]);

  const loadUserSettings = async () => {
    setIsLoading(true);
    try {
      let settingsQuery = query(
        collection(db, COLLECTIONS.USER_SETTINGS),
        orderBy('updatedAt', 'desc')
      );

      const settingsSnapshot = await getDocs(settingsQuery);
      const fetchedSettings = settingsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(doc.data().createdAt),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date(doc.data().updatedAt)
      } as UserSettings));

      // Filter by userId if specified
      const filteredSettings = userId 
        ? fetchedSettings.filter(setting => setting.userId === userId)
        : fetchedSettings;

      setUserSettings(filteredSettings);
    } catch (error) {
      console.error('Error loading user settings:', error);
      toast({
        title: "Error",
        description: "Failed to load user settings.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const handleEdit = (settings: UserSettings) => {
    // Open settings edit dialog
    console.log('Edit user settings:', settings);
    toast({
      title: "Feature Coming Soon",
      description: "Settings editor will be available soon."
    });
  };

  const handleExportSettings = (settings: UserSettings) => {
    // Export user settings
    console.log('Export settings:', settings);
    toast({
      title: "Feature Coming Soon",
      description: "Settings export will be available soon."
    });
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const columns: ColumnDef<UserSettings>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "userEmail",
      header: "User",
      cell: ({ row }) => {
        const settings = row.original;
        const userEmail = settings.userEmail || settings.userId;
        const userName = settings.userName || 'Unknown User';
        
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-xs">
                {userName.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="text-sm">
              <div className="font-medium">{userName}</div>
              <div className="text-gray-500">{userEmail}</div>
            </div>
          </div>
        );
      },
      enableHiding: !!userId, // Hide if filtering by specific user
    },
    {
      accessorKey: "theme",
      header: "Theme",
      cell: ({ row }) => {
        const theme = row.original.theme || 'system';
        const config = themeConfig[theme];
        const ThemeIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <ThemeIcon className="h-3 w-3" />
            {theme}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        const theme = row.original.theme || 'system';
        return value.includes(theme);
      },
    },
    {
      accessorKey: "language",
      header: "Language",
      cell: ({ row }) => {
        const language = row.original.language || 'en';
        
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <Globe className="h-3 w-3" />
            {language.toUpperCase()}
          </Badge>
        );
      },
    },
    {
      id: "notifications",
      header: "Notifications",
      cell: ({ row }) => {
        const prefs = row.original.notificationPreferences;
        const notifications = [];
        
        if (prefs?.email) notifications.push('Email');
        if (prefs?.push) notifications.push('Push');
        if (prefs?.sms) notifications.push('SMS');
        if (prefs?.inApp) notifications.push('In-App');
        
        return (
          <div className="flex flex-wrap gap-1">
            {notifications.length > 0 ? (
              notifications.map(type => (
                <Badge key={type} variant="secondary" className="text-xs">
                  {type}
                </Badge>
              ))
            ) : (
              <span className="text-gray-400 text-xs">None</span>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "privacy",
      header: "Privacy",
      cell: ({ row }) => {
        const privacy = row.original.privacySettings;
        const visibility = privacy?.profileVisibility || 'team';
        const config = privacyConfig[visibility];
        
        return (
          <div className="space-y-1">
            <Badge className={`${config.color} text-xs`}>
              {visibility}
            </Badge>
            {privacy?.activityTracking && (
              <Badge variant="outline" className="text-xs">
                Tracking
              </Badge>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "dashboardLayout",
      header: "Dashboard",
      cell: ({ row }) => {
        const layout = row.original.dashboardLayout || 'default';
        
        return (
          <Badge variant="outline" className="text-xs">
            {layout}
          </Badge>
        );
      },
    },
    {
      accessorKey: "updatedAt",
      header: "Last Updated",
      cell: ({ row }) => {
        const updatedAt = row.original.updatedAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(updatedAt)}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const settings = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // View settings details
                console.log('View settings:', settings);
              }}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEdit(settings)}
            >
              <Edit2 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleExportSettings(settings)}
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], [userId]);

  const tableActions = [
    {
      label: "Export Selected",
      icon: Download,
      onClick: (selectedRows: UserSettings[]) => {
        console.log('Export user settings:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Settings export will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Import Settings",
      icon: Upload,
      onClick: () => {
        console.log('Import user settings');
        toast({
          title: "Feature Coming Soon",
          description: "Settings import will be available soon."
        });
      },
      variant: "outline" as const,
    },
    {
      label: "Reset Selected",
      icon: Settings,
      onClick: (selectedRows: UserSettings[]) => {
        if (confirm(`Reset settings for ${selectedRows.length} selected users?`)) {
          console.log('Reset settings:', selectedRows);
          toast({
            title: "Feature Coming Soon",
            description: "Settings reset will be available soon."
          });
        }
      },
      variant: "destructive" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    {
      key: 'theme',
      label: 'Theme',
      options: [
        { label: 'Light', value: 'light' },
        { label: 'Dark', value: 'dark' },
        { label: 'System', value: 'system' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={userSettings}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadUserSettings}
      tableActions={tableActions}
      filterOptions={showUserFilter ? filterOptions : []}
      searchPlaceholder="Search user settings by email, name..."
      emptyStateMessage="No user settings found"
      emptyStateDescription="User settings will appear here as users customize their preferences."
    />
  );
}
