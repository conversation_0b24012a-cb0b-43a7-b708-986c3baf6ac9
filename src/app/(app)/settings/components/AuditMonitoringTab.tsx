"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Activity, 
  Shield, 
  Eye, 
  AlertTriangle, 
  BarChart3,
  Download,
  Settings,
  Clock,
  Users,
  Database
} from "lucide-react";
import AuditLogsTable from './AuditLogsTable';

export default function AuditMonitoringTab() {
  const [selectedDateRange, setSelectedDateRange] = useState<{ start: Date; end: Date } | undefined>();

  // Mock system metrics - in real implementation, these would come from Firebase
  const systemMetrics = {
    totalLogs: 15420,
    criticalEvents: 3,
    warningEvents: 28,
    activeUsers: 142,
    systemUptime: '99.9%',
    lastBackup: '2 hours ago',
  };

  return (
    <div className="space-y-6">
      {/* System Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Audit Logs</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemMetrics.totalLogs.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Events</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{systemMetrics.criticalEvents}</div>
            <p className="text-xs text-muted-foreground">
              Requires immediate attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Warning Events</CardTitle>
            <Eye className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{systemMetrics.warningEvents}</div>
            <p className="text-xs text-muted-foreground">
              Monitor closely
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{systemMetrics.activeUsers}</div>
            <p className="text-xs text-muted-foreground">
              Currently online
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Uptime</CardTitle>
            <Activity className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{systemMetrics.systemUptime}</div>
            <p className="text-xs text-muted-foreground">
              Last 30 days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Backup</CardTitle>
            <Shield className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{systemMetrics.lastBackup}</div>
            <p className="text-xs text-muted-foreground">
              Automated backup
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Audit Logs Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>System Audit Logs</CardTitle>
              <CardDescription>
                Monitor all system activities, user actions, and security events
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Download className="mr-2 h-4 w-4" />
                Export Logs
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="mr-2 h-4 w-4" />
                Configure
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="all">All Logs</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
              <TabsTrigger value="user">User Actions</TabsTrigger>
              <TabsTrigger value="system">System</TabsTrigger>
              <TabsTrigger value="errors">Errors</TabsTrigger>
            </TabsList>
            
            <TabsContent value="all" className="mt-4">
              <AuditLogsTable maxResults={50} />
            </TabsContent>
            
            <TabsContent value="security" className="mt-4">
              <AuditLogsTable actionType="security" maxResults={50} />
            </TabsContent>
            
            <TabsContent value="user" className="mt-4">
              <AuditLogsTable actionType="user" maxResults={50} />
            </TabsContent>
            
            <TabsContent value="system" className="mt-4">
              <AuditLogsTable actionType="system" maxResults={50} />
            </TabsContent>
            
            <TabsContent value="errors" className="mt-4">
              <AuditLogsTable actionType="error" maxResults={50} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common audit and monitoring tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 md:gap-4">
            <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2 p-2">
              <BarChart3 className="h-12 w-12" />
              <span className="text-xs font-medium text-center leading-tight">Generate Report</span>
            </Button>

            <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2 p-2">
              <AlertTriangle className="h-12 w-12" />
              <span className="text-xs font-medium text-center leading-tight">Security Alerts</span>
            </Button>

            <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2 p-2">
              <Users className="h-12 w-12" />
              <span className="text-xs font-medium text-center leading-tight">User Activity</span>
            </Button>

            <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2 p-2">
              <Settings className="h-12 w-12" />
              <span className="text-xs font-medium text-center leading-tight">System Config</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
