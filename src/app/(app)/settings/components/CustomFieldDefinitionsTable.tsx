"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { 
  ListFilter,
  Edit2,
  Trash2,
  Plus,
  Download,
  Upload,
  ToggleLeft,
  ToggleRight,
  Type,
  Hash,
  Calendar,
  FileText,
  CheckSquare,
  List,
  Upload as UploadIcon
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { CustomFieldDefinition, CustomFieldType, CustomFieldAppliesTo } from '@/types/firestore';
import { getCustomFieldDefinitionsAction, deleteCustomFieldDefinitionAction } from '../actions';
import { formatDistanceToNow } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import EnhancedCustomFieldFormDialog from './EnhancedCustomFieldFormDialog';

const fieldTypeIcons = {
  'text': Type,
  'textarea': FileText,
  'number': Hash,
  'date': Calendar,
  'checkbox': CheckSquare,
  'select': List,
  'file': UploadIcon,
};

const fieldTypeColors = {
  'text': 'bg-blue-100 text-blue-800',
  'textarea': 'bg-green-100 text-green-800',
  'number': 'bg-purple-100 text-purple-800',
  'date': 'bg-orange-100 text-orange-800',
  'checkbox': 'bg-pink-100 text-pink-800',
  'select': 'bg-indigo-100 text-indigo-800',
  'file': 'bg-gray-100 text-gray-800',
};

const appliesToColors = {
  'exhibitions': 'bg-blue-100 text-blue-800',
  'events': 'bg-green-100 text-green-800',
  'leads': 'bg-purple-100 text-purple-800',
  'vendors': 'bg-orange-100 text-orange-800',
  'tasks': 'bg-pink-100 text-pink-800',
  'users': 'bg-indigo-100 text-indigo-800',
  'budgets': 'bg-yellow-100 text-yellow-800',
  'expenses': 'bg-red-100 text-red-800',
};

interface CustomFieldDefinitionsTableProps {
  onAdd?: () => void;
  onEdit?: (definition: CustomFieldDefinition) => void;
}

export default function CustomFieldDefinitionsTable({
  onAdd,
  onEdit
}: CustomFieldDefinitionsTableProps) {
  const { toast } = useToast();
  const [definitions, setDefinitions] = useState<CustomFieldDefinition[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingDefinition, setEditingDefinition] = useState<CustomFieldDefinition | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  // Track component mount state
  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  // Load custom field definitions
  useEffect(() => {
    if (isMounted) {
      loadDefinitions();
    }
  }, [isMounted]);

  const loadDefinitions = async () => {
    if (!isMounted) return;

    setIsLoading(true);
    try {
      const fetchedDefinitions = await getCustomFieldDefinitionsAction();
      if (isMounted) {
        setDefinitions(fetchedDefinitions);
      }
    } catch (error) {
      if (isMounted) {
        toast({
          title: "Error",
          description: "Failed to load custom field definitions.",
          variant: "destructive"
        });
      }
    }
    if (isMounted) {
      setIsLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingDefinition(null);
    setIsFormOpen(true);
    if (onAdd) onAdd();
  };

  const handleEdit = (definition: CustomFieldDefinition) => {
    setEditingDefinition(definition);
    setIsFormOpen(true);
    if (onEdit) onEdit(definition);
  };

  const handleDelete = async (definition: CustomFieldDefinition) => {
    if (!confirm(`Are you sure you want to delete "${definition.name}"?`)) return;
    
    try {
      await deleteCustomFieldDefinitionAction(definition.id!);
      await loadDefinitions();
      toast({
        title: "Success",
        description: "Custom field definition deleted successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete custom field definition.",
        variant: "destructive"
      });
    }
  };

  const handleToggleStatus = async (definition: CustomFieldDefinition) => {
    // This would need to be implemented in actions
    console.log('Toggle status for:', definition);
    toast({
      title: "Feature Coming Soon",
      description: "Status toggle will be available soon."
    });
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const columns: ColumnDef<CustomFieldDefinition>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: "Field Name",
      cell: ({ row }) => {
        const definition = row.original;
        
        return (
          <div className="flex flex-col">
            <span className="font-medium">{definition.name}</span>
            <span className="text-xs text-gray-500">{definition.fieldKey}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "type",
      header: "Field Type",
      cell: ({ row }) => {
        const type = row.original.type;
        const TypeIcon = fieldTypeIcons[type] || Type;
        const colorClass = fieldTypeColors[type] || fieldTypeColors.text;
        
        return (
          <Badge className={`${colorClass} flex items-center gap-1 w-fit`}>
            <TypeIcon className="h-3 w-3" />
            {type}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "appliesTo",
      header: "Applies To",
      cell: ({ row }) => {
        const appliesTo = row.original.appliesTo;
        const colorClass = appliesToColors[appliesTo] || appliesToColors.exhibitions;
        
        return (
          <Badge className={`${colorClass} capitalize`}>
            {appliesTo}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: ({ row }) => {
        const description = row.original.description;
        
        return (
          <div className="max-w-sm">
            <p className="text-sm line-clamp-2">
              {description || 'No description provided'}
            </p>
          </div>
        );
      },
    },
    {
      id: "properties",
      header: "Properties",
      cell: ({ row }) => {
        const definition = row.original;
        const properties = [];
        
        if (definition.required) {
          properties.push(
            <Badge key="required" variant="destructive" className="text-xs">
              Required
            </Badge>
          );
        }
        
        if (definition.options) {
          properties.push(
            <Badge key="options" variant="secondary" className="text-xs">
              {definition.options.split(',').length} Options
            </Badge>
          );
        }
        
        if (definition.defaultValue) {
          properties.push(
            <Badge key="default" variant="outline" className="text-xs">
              Has Default
            </Badge>
          );
        }
        
        return (
          <div className="flex flex-wrap gap-1">
            {properties.length > 0 ? properties : (
              <span className="text-gray-400 text-xs">No properties</span>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "status",
      header: "Status",
      cell: ({ row }) => {
        const definition = row.original;
        const isActive = definition.isActive !== false; // Default to true if undefined
        
        return (
          <Badge variant={isActive ? "default" : "secondary"}>
            {isActive ? "Active" : "Inactive"}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        const isActive = row.original.isActive !== false;
        return value.includes(isActive ? 'active' : 'inactive');
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(createdAt)}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const definition = row.original;
        const isActive = definition.isActive !== false;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEdit(definition)}
            >
              <Edit2 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleToggleStatus(definition)}
              className={isActive ? "text-orange-600" : "text-green-600"}
            >
              {isActive ? <ToggleRight className="h-4 w-4" /> : <ToggleLeft className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDelete(definition)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], []);

  const tableActions = [
    {
      label: "Add Custom Field",
      icon: Plus,
      onClick: () => handleAdd(),
      variant: "default" as const,
    },
    {
      label: "Export Selected",
      icon: Download,
      onClick: (selectedRows: CustomFieldDefinition[]) => {
        console.log('Export custom fields:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Export functionality will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Import Fields",
      icon: Upload,
      onClick: () => {
        console.log('Import custom fields');
        toast({
          title: "Feature Coming Soon",
          description: "Import functionality will be available soon."
        });
      },
      variant: "outline" as const,
    },
    {
      label: "Delete Selected",
      icon: Trash2,
      onClick: async (selectedRows: CustomFieldDefinition[]) => {
        if (confirm(`Delete ${selectedRows.length} selected custom fields?`)) {
          try {
            await Promise.all(selectedRows.map(def => deleteCustomFieldDefinitionAction(def.id!)));
            await loadDefinitions();
            toast({
              title: "Success",
              description: `${selectedRows.length} custom fields deleted successfully.`
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to delete selected custom fields.",
              variant: "destructive"
            });
          }
        }
      },
      variant: "destructive" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    {
      key: 'type',
      label: 'Field Type',
      options: [
        { label: 'Text', value: 'text' },
        { label: 'Textarea', value: 'textarea' },
        { label: 'Number', value: 'number' },
        { label: 'Date', value: 'date' },
        { label: 'Checkbox', value: 'checkbox' },
        { label: 'Select', value: 'select' },
        { label: 'File', value: 'file' },
      ]
    },
    {
      key: 'appliesTo',
      label: 'Applies To',
      options: [
        { label: 'Exhibitions', value: 'exhibitions' },
        { label: 'Events', value: 'events' },
        { label: 'Leads', value: 'leads' },
        { label: 'Vendors', value: 'vendors' },
        { label: 'Tasks', value: 'tasks' },
        { label: 'Users', value: 'users' },
        { label: 'Budgets', value: 'budgets' },
        { label: 'Expenses', value: 'expenses' },
      ]
    },
    {
      key: 'status',
      label: 'Status',
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' },
      ]
    },
  ];

  return (
    <>
      <AdvancedDataTable
        data={definitions}
        columns={columns}
        isLoading={isLoading}
        onRefresh={loadDefinitions}
        tableActions={tableActions}
        filterOptions={filterOptions}
        searchPlaceholder="Search custom fields by name, description..."
        emptyStateMessage="No custom field definitions found"
        emptyStateDescription="Create your first custom field definition to get started."
      />

      {isFormOpen && (
        <EnhancedCustomFieldFormDialog
          isOpen={isFormOpen}
          onClose={() => {
            setIsFormOpen(false);
            setEditingDefinition(null);
          }}
          onSuccess={() => {
            loadDefinitions();
            setIsFormOpen(false);
            setEditingDefinition(null);
          }}
          editingDefinition={editingDefinition}
        />
      )}
    </>
  );
}
