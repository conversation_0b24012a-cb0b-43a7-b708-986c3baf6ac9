"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { 
  Activity,
  Eye,
  Download,
  Filter,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  Shield,
  User,
  Database,
  Settings,
  FileText,
  Trash2
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { AuditLogEntry } from '@/types/firestore';
import { collection, getDocs, query, orderBy, limit, where } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { formatDistanceToNow, format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

const actionTypeIcons = {
  'create': CheckCircle,
  'update': Settings,
  'delete': Trash2,
  'view': Eye,
  'login': User,
  'logout': User,
  'export': Download,
  'import': Database,
  'approve': CheckCircle,
  'reject': XCircle,
  'error': AlertTriangle,
  'warning': AlertTriangle,
  'info': Info,
  'security': Shield,
};

const actionTypeColors = {
  'create': 'bg-green-100 text-green-800',
  'update': 'bg-blue-100 text-blue-800',
  'delete': 'bg-red-100 text-red-800',
  'view': 'bg-gray-100 text-gray-800',
  'login': 'bg-purple-100 text-purple-800',
  'logout': 'bg-purple-100 text-purple-800',
  'export': 'bg-orange-100 text-orange-800',
  'import': 'bg-orange-100 text-orange-800',
  'approve': 'bg-green-100 text-green-800',
  'reject': 'bg-red-100 text-red-800',
  'error': 'bg-red-100 text-red-800',
  'warning': 'bg-yellow-100 text-yellow-800',
  'info': 'bg-blue-100 text-blue-800',
  'security': 'bg-red-100 text-red-800',
};

const severityConfig = {
  'low': { color: 'bg-gray-100 text-gray-800', icon: Info },
  'medium': { color: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle },
  'high': { color: 'bg-orange-100 text-orange-800', icon: AlertTriangle },
  'critical': { color: 'bg-red-100 text-red-800', icon: Shield },
};

interface AuditLogsTableProps {
  userId?: string;
  actionType?: string;
  dateRange?: { start: Date; end: Date };
  maxResults?: number;
}

export default function AuditLogsTable({ 
  userId, 
  actionType, 
  dateRange, 
  maxResults = 100 
}: AuditLogsTableProps) {
  const { toast } = useToast();
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load audit logs from Firebase
  useEffect(() => {
    loadAuditLogs();
  }, [userId, actionType, dateRange]);

  const loadAuditLogs = async () => {
    setIsLoading(true);
    try {
      let auditQuery = query(
        collection(db, COLLECTIONS.AUDIT_LOGS),
        orderBy('timestamp', 'desc'),
        limit(maxResults)
      );

      // Add filters if specified
      if (userId) {
        auditQuery = query(auditQuery, where('userId', '==', userId));
      }
      
      if (actionType) {
        auditQuery = query(auditQuery, where('action', '==', actionType));
      }

      if (dateRange) {
        auditQuery = query(
          auditQuery, 
          where('timestamp', '>=', Timestamp.fromDate(dateRange.start)),
          where('timestamp', '<=', Timestamp.fromDate(dateRange.end))
        );
      }

      const auditSnapshot = await getDocs(auditQuery);
      const fetchedLogs = auditSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate?.() || new Date(doc.data().timestamp)
      } as AuditLogEntry));

      setAuditLogs(fetchedLogs);
    } catch (error) {
      console.error('Error loading audit logs:', error);
      toast({
        title: "Error",
        description: "Failed to load audit logs.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const formatDateTime = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return format(dateObj, 'MMM dd, yyyy HH:mm:ss');
  };

  const columns: ColumnDef<AuditLogEntry>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "timestamp",
      header: "Timestamp",
      cell: ({ row }) => {
        const timestamp = row.original.timestamp;
        return (
          <div className="text-sm">
            <div className="font-medium">{formatDateTime(timestamp)}</div>
            <div className="text-gray-500">{formatDate(timestamp)}</div>
          </div>
        );
      },
    },
    {
      accessorKey: "action",
      header: "Action",
      cell: ({ row }) => {
        const action = row.original.action;
        const ActionIcon = actionTypeIcons[action] || Activity;
        const colorClass = actionTypeColors[action] || actionTypeColors.info;
        
        return (
          <Badge className={`${colorClass} flex items-center gap-1 w-fit`}>
            <ActionIcon className="h-3 w-3" />
            {action}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "userId",
      header: "User",
      cell: ({ row }) => {
        const userId = row.original.userId;
        const userEmail = row.original.userEmail;
        
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-xs">
                {userEmail ? userEmail.substring(0, 2).toUpperCase() : 'SY'}
              </AvatarFallback>
            </Avatar>
            <div className="text-sm">
              <div className="font-medium">{userEmail || 'System'}</div>
              <div className="text-gray-500 text-xs">{userId}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "resource",
      header: "Resource",
      cell: ({ row }) => {
        const resource = row.original.resource;
        const resourceId = row.original.resourceId;
        
        return (
          <div className="text-sm">
            <div className="font-medium">{resource}</div>
            {resourceId && (
              <div className="text-gray-500 text-xs font-mono">{resourceId}</div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: ({ row }) => {
        const description = row.original.description;
        
        return (
          <div className="max-w-sm">
            <p className="text-sm line-clamp-2">
              {description}
            </p>
          </div>
        );
      },
    },
    {
      id: "severity",
      header: "Severity",
      cell: ({ row }) => {
        const severity = row.original.severity || 'low';
        const config = severityConfig[severity];
        const SeverityIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <SeverityIcon className="h-3 w-3" />
            {severity}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        const severity = row.original.severity || 'low';
        return value.includes(severity);
      },
    },
    {
      id: "metadata",
      header: "Details",
      cell: ({ row }) => {
        const metadata = row.original.metadata;
        const ipAddress = row.original.ipAddress;
        const userAgent = row.original.userAgent;
        
        return (
          <div className="text-xs text-gray-500">
            {ipAddress && <div>IP: {ipAddress}</div>}
            {metadata && Object.keys(metadata).length > 0 && (
              <div>{Object.keys(metadata).length} metadata fields</div>
            )}
            {userAgent && (
              <div className="truncate max-w-32" title={userAgent}>
                {userAgent}
              </div>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const log = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Open detailed view
                console.log('View audit log details:', log);
              }}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Export single log
                console.log('Export audit log:', log);
              }}
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], []);

  const tableActions = [
    {
      label: "Export Selected",
      icon: Download,
      onClick: (selectedRows: AuditLogEntry[]) => {
        console.log('Export audit logs:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Audit log export will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Export All",
      icon: Download,
      onClick: () => {
        console.log('Export all audit logs');
        toast({
          title: "Feature Coming Soon",
          description: "Full audit log export will be available soon."
        });
      },
      variant: "outline" as const,
    },
    {
      label: "Clear Old Logs",
      icon: Trash2,
      onClick: () => {
        console.log('Clear old audit logs');
        toast({
          title: "Feature Coming Soon",
          description: "Log cleanup will be available soon."
        });
      },
      variant: "destructive" as const,
    },
  ];

  const filterOptions = [
    {
      key: 'action',
      label: 'Action Type',
      options: [
        { label: 'Create', value: 'create' },
        { label: 'Update', value: 'update' },
        { label: 'Delete', value: 'delete' },
        { label: 'View', value: 'view' },
        { label: 'Login', value: 'login' },
        { label: 'Logout', value: 'logout' },
        { label: 'Export', value: 'export' },
        { label: 'Import', value: 'import' },
        { label: 'Approve', value: 'approve' },
        { label: 'Reject', value: 'reject' },
        { label: 'Error', value: 'error' },
        { label: 'Warning', value: 'warning' },
        { label: 'Security', value: 'security' },
      ]
    },
    {
      key: 'severity',
      label: 'Severity',
      options: [
        { label: 'Low', value: 'low' },
        { label: 'Medium', value: 'medium' },
        { label: 'High', value: 'high' },
        { label: 'Critical', value: 'critical' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={auditLogs}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadAuditLogs}
      tableActions={tableActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search audit logs by description, user, resource..."
      emptyStateMessage="No audit logs found"
      emptyStateDescription="System audit logs will appear here as activities occur."
    />
  );
}
