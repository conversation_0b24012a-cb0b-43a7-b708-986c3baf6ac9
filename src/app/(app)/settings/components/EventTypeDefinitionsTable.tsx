"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { 
  Type,
  Edit2,
  Trash2,
  Plus,
  Download,
  Upload,
  ToggleLeft,
  ToggleRight
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { EventTypeDefinition } from '@/types/firestore';
import { getEventTypeDefinitionsAction, deleteEventTypeDefinitionAction } from '../actions';
import { formatDistanceToNow } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import EventTypeDefinitionFormDialog from './EventTypeDefinitionFormDialog';

interface EventTypeDefinitionsTableProps {
  onAdd?: () => void;
  onEdit?: (definition: EventTypeDefinition) => void;
}

export default function EventTypeDefinitionsTable({ 
  onAdd, 
  onEdit 
}: EventTypeDefinitionsTableProps) {
  const { toast } = useToast();
  const [definitions, setDefinitions] = useState<EventTypeDefinition[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingDefinition, setEditingDefinition] = useState<EventTypeDefinition | null>(null);

  // Load event type definitions
  useEffect(() => {
    loadDefinitions();
  }, []);

  const loadDefinitions = async () => {
    setIsLoading(true);
    try {
      const fetchedDefinitions = await getEventTypeDefinitionsAction();
      setDefinitions(fetchedDefinitions);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load event type definitions.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const handleAdd = () => {
    setEditingDefinition(null);
    setIsFormOpen(true);
    if (onAdd) onAdd();
  };

  const handleEdit = (definition: EventTypeDefinition) => {
    setEditingDefinition(definition);
    setIsFormOpen(true);
    if (onEdit) onEdit(definition);
  };

  const handleDelete = async (definition: EventTypeDefinition) => {
    if (!confirm(`Are you sure you want to delete "${definition.name}"?`)) return;
    
    try {
      await deleteEventTypeDefinitionAction(definition.id!);
      await loadDefinitions();
      toast({
        title: "Success",
        description: "Event type definition deleted successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete event type definition.",
        variant: "destructive"
      });
    }
  };

  const handleToggleStatus = async (definition: EventTypeDefinition) => {
    // This would need to be implemented in actions
    console.log('Toggle status for:', definition);
    toast({
      title: "Feature Coming Soon",
      description: "Status toggle will be available soon."
    });
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const columns: ColumnDef<EventTypeDefinition>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: "Event Type Name",
      cell: ({ row }) => {
        const definition = row.original;
        
        return (
          <div className="flex flex-col">
            <span className="font-medium">{definition.name}</span>
            {definition.category && (
              <Badge variant="outline" className="w-fit text-xs mt-1">
                {definition.category}
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: ({ row }) => {
        const description = row.original.description;
        
        return (
          <div className="max-w-sm">
            <p className="text-sm line-clamp-2">
              {description || 'No description provided'}
            </p>
          </div>
        );
      },
    },
    {
      id: "properties",
      header: "Properties",
      cell: ({ row }) => {
        const definition = row.original;
        const properties = [];
        
        if (definition.color) {
          properties.push(
            <div key="color" className="flex items-center gap-1">
              <div 
                className="w-3 h-3 rounded-full border" 
                style={{ backgroundColor: definition.color }}
              />
              <span className="text-xs">{definition.color}</span>
            </div>
          );
        }
        
        if (definition.icon) {
          properties.push(
            <Badge key="icon" variant="secondary" className="text-xs">
              {definition.icon}
            </Badge>
          );
        }
        
        return (
          <div className="flex flex-wrap gap-1">
            {properties.length > 0 ? properties : (
              <span className="text-gray-400 text-xs">No properties</span>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "status",
      header: "Status",
      cell: ({ row }) => {
        const definition = row.original;
        const isActive = definition.isActive !== false; // Default to true if undefined
        
        return (
          <Badge variant={isActive ? "default" : "secondary"}>
            {isActive ? "Active" : "Inactive"}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        const isActive = row.original.isActive !== false;
        return value.includes(isActive ? 'active' : 'inactive');
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(createdAt)}
          </div>
        );
      },
    },
    {
      accessorKey: "updatedAt",
      header: "Last Updated",
      cell: ({ row }) => {
        const updatedAt = row.original.updatedAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(updatedAt)}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const definition = row.original;
        const isActive = definition.isActive !== false;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEdit(definition)}
            >
              <Edit2 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleToggleStatus(definition)}
              className={isActive ? "text-orange-600" : "text-green-600"}
            >
              {isActive ? <ToggleRight className="h-4 w-4" /> : <ToggleLeft className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDelete(definition)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], []);

  const tableActions = [
    {
      label: "Add Event Type",
      icon: Plus,
      onClick: () => handleAdd(),
      variant: "default" as const,
    },
    {
      label: "Export Selected",
      icon: Download,
      onClick: (selectedRows: EventTypeDefinition[]) => {
        console.log('Export event types:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Export functionality will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Import Event Types",
      icon: Upload,
      onClick: () => {
        console.log('Import event types');
        toast({
          title: "Feature Coming Soon",
          description: "Import functionality will be available soon."
        });
      },
      variant: "outline" as const,
    },
    {
      label: "Delete Selected",
      icon: Trash2,
      onClick: async (selectedRows: EventTypeDefinition[]) => {
        if (confirm(`Delete ${selectedRows.length} selected event types?`)) {
          try {
            await Promise.all(selectedRows.map(def => deleteEventTypeDefinitionAction(def.id!)));
            await loadDefinitions();
            toast({
              title: "Success",
              description: `${selectedRows.length} event types deleted successfully.`
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to delete selected event types.",
              variant: "destructive"
            });
          }
        }
      },
      variant: "destructive" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    {
      key: 'status',
      label: 'Status',
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' },
      ]
    },
  ];

  return (
    <>
      <AdvancedDataTable
        data={definitions}
        columns={columns}
        isLoading={isLoading}
        onRefresh={loadDefinitions}
        tableActions={tableActions}
        filterOptions={filterOptions}
        searchPlaceholder="Search event types by name, description..."
        emptyStateMessage="No event type definitions found"
        emptyStateDescription="Create your first event type definition to get started."
      />
      
      {isFormOpen && (
        <EventTypeDefinitionFormDialog
          isOpen={isFormOpen}
          onClose={() => {
            setIsFormOpen(false);
            setEditingDefinition(null);
          }}
          onSuccess={() => {
            loadDefinitions();
            setIsFormOpen(false);
            setEditingDefinition(null);
          }}
          editingDefinition={editingDefinition}
        />
      )}
    </>
  );
}
