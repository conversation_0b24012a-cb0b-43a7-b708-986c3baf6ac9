/**
 * Email Notifications Management Page
 * Comprehensive email and notification management interface
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Mail, 
  Send, 
  Settings, 
  History, 
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  Plus
} from 'lucide-react';
import { SystemPermissionGate } from "@/components/permissions/PermissionGate";
import { UpgradePrompt } from "@/components/billing/UpgradePrompt";
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import { notificationService, type NotificationEvent, type NotificationTemplate } from '@/lib/notification-service';
import { emailService, type EmailTemplate } from '@/lib/email-service';

export default function EmailNotificationsPage() {
  return (
    <SystemPermissionGate 
      systemPermission="canManageSettings"
      fallback={
        <div className="container mx-auto py-8">
          <UpgradePrompt 
            trigger="feature_locked"
            feature="Email Notifications"
            description="Access to email notification management requires administrative permissions."
          />
        </div>
      }
    >
      <EmailNotificationsContent />
    </SystemPermissionGate>
  );
}

function EmailNotificationsContent() {
  const { user, tenant } = useAuth();
  const { toast } = useToast();
  const [notifications, setNotifications] = useState<NotificationEvent[]>([]);
  const [templates, setTemplates] = useState<NotificationTemplate[]>([]);
  const [emailTemplates, setEmailTemplates] = useState<EmailTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedNotification, setSelectedNotification] = useState<NotificationEvent | null>(null);

  // Email settings state
  const [emailSettings, setEmailSettings] = useState({
    enableNotifications: true,
    enableUserInvitations: true,
    enablePasswordResets: true,
    enableWelcomeEmails: true,
    enableTaskNotifications: true,
    enableExhibitionReminders: true,
    fromName: 'EVEXA',
    fromEmail: '<EMAIL>',
    replyToEmail: '<EMAIL>',
    customDomain: '',
    trackOpens: true,
    trackClicks: true
  });

  useEffect(() => {
    loadData();
  }, [tenant?.id]);

  const loadData = async () => {
    if (!tenant?.id) return;

    setIsLoading(true);
    try {
      // Load notification history
      const notificationHistory = await notificationService.getNotificationHistory(tenant.id, {
        limit: 50
      });
      setNotifications(notificationHistory);

      // Load templates
      const notificationTemplates = notificationService.getTemplates();
      setTemplates(notificationTemplates);

      const emailTemplateList = emailService.getTemplates();
      setEmailTemplates(emailTemplateList);

    } catch (error) {
      toast({
        title: "Error Loading Data",
        description: "Failed to load email notification data.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    try {
      // TODO: Implement actual settings save
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast({
        title: "Settings Saved",
        description: "Email notification settings have been updated.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save settings. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleRetryFailedNotifications = async () => {
    if (!tenant?.id) return;

    try {
      const retriedCount = await notificationService.retryFailedNotifications(tenant.id);
      toast({
        title: "Notifications Retried",
        description: `${retriedCount} failed notifications have been retried.`,
      });
      loadData(); // Refresh the list
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to retry notifications.",
        variant: "destructive",
      });
    }
  };

  const getStatusIcon = (status: NotificationEvent['status']) => {
    switch (status) {
      case 'sent':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-orange-600" />;
      case 'bounced':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: NotificationEvent['status']) => {
    switch (status) {
      case 'sent':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-orange-100 text-orange-800';
      case 'bounced':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <RefreshCw className="h-6 w-6 animate-spin" />
        <span className="ml-2">Loading email notifications...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <Mail className="h-6 w-6" />
          Email Notifications
        </h2>
        <p className="text-muted-foreground mt-1">
          Manage email templates, notification settings, and delivery history
        </p>
      </div>

      <Tabs defaultValue="history" className="w-full">
        <TabsList>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="test">Test</TabsTrigger>
        </TabsList>

        {/* History Tab */}
        <TabsContent value="history" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <History className="h-5 w-5" />
                  Notification History
                </span>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={loadData}>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Refresh
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleRetryFailedNotifications}>
                    <Send className="mr-2 h-4 w-4" />
                    Retry Failed
                  </Button>
                </div>
              </CardTitle>
              <CardDescription>
                View and manage all email notifications sent from your tenant
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Status</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Recipient</TableHead>
                    <TableHead>Subject</TableHead>
                    <TableHead>Sent</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {notifications.map((notification) => (
                    <TableRow key={notification.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(notification.status)}
                          <Badge className={getStatusColor(notification.status)}>
                            {notification.status}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {notification.type.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{notification.recipientName || 'Unknown'}</div>
                          <div className="text-sm text-muted-foreground">{notification.recipientEmail}</div>
                        </div>
                      </TableCell>
                      <TableCell className="max-w-xs truncate">
                        {notification.subject}
                      </TableCell>
                      <TableCell>
                        {notification.sentAt 
                          ? new Date(notification.sentAt).toLocaleString()
                          : 'Not sent'
                        }
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSelectedNotification(notification)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          {notification.status === 'failed' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                // TODO: Implement retry single notification
                              }}
                            >
                              <RefreshCw className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              {notifications.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No notifications found
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Templates Tab */}
        <TabsContent value="templates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Email Templates
                </span>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Template
                </Button>
              </CardTitle>
              <CardDescription>
                Manage email templates for different notification types
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {emailTemplates.map((template) => (
                  <Card key={template.id}>
                    <CardHeader>
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <CardDescription className="text-sm">
                        {template.subject}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex justify-between items-center">
                        <Badge variant="outline">
                          {template.variables.length} variables
                        </Badge>
                        <div className="flex gap-1">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Email Settings
              </CardTitle>
              <CardDescription>
                Configure email notification preferences and settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Notification Types */}
              <div>
                <h4 className="font-medium mb-4">Notification Types</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>User Invitations</Label>
                      <p className="text-sm text-muted-foreground">Send emails when users are invited</p>
                    </div>
                    <Switch 
                      checked={emailSettings.enableUserInvitations}
                      onCheckedChange={(checked) => 
                        setEmailSettings(prev => ({ ...prev, enableUserInvitations: checked }))
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Welcome Emails</Label>
                      <p className="text-sm text-muted-foreground">Send welcome emails to new users</p>
                    </div>
                    <Switch 
                      checked={emailSettings.enableWelcomeEmails}
                      onCheckedChange={(checked) => 
                        setEmailSettings(prev => ({ ...prev, enableWelcomeEmails: checked }))
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Password Resets</Label>
                      <p className="text-sm text-muted-foreground">Send password reset emails</p>
                    </div>
                    <Switch 
                      checked={emailSettings.enablePasswordResets}
                      onCheckedChange={(checked) => 
                        setEmailSettings(prev => ({ ...prev, enablePasswordResets: checked }))
                      }
                    />
                  </div>
                </div>
              </div>

              {/* Email Configuration */}
              <div>
                <h4 className="font-medium mb-4">Email Configuration</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="from-name">From Name</Label>
                    <Input 
                      id="from-name"
                      value={emailSettings.fromName}
                      onChange={(e) => 
                        setEmailSettings(prev => ({ ...prev, fromName: e.target.value }))
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor="from-email">From Email</Label>
                    <Input 
                      id="from-email"
                      value={emailSettings.fromEmail}
                      onChange={(e) => 
                        setEmailSettings(prev => ({ ...prev, fromEmail: e.target.value }))
                      }
                    />
                  </div>
                </div>
              </div>

              <Button onClick={handleSaveSettings}>
                Save Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Test Tab */}
        <TabsContent value="test" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Send className="h-5 w-5" />
                Test Email
              </CardTitle>
              <CardDescription>
                Send test emails to verify your configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center text-muted-foreground">
                Test email interface will be implemented here
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
