/**
 * Tenant Admin Controls Page
 * Comprehensive tenant administration interface
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { 
  Building2, 
  Users, 
  CreditCard, 
  Settings, 
  Crown,
  UserPlus,
  Mail,
  Shield,
  BarChart3,
  Database,
  Palette,
  Globe,
  Lock,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { SystemPermissionGate } from "@/components/permissions/PermissionGate";
import { UpgradePrompt } from "@/components/billing/UpgradePrompt";
import { EnhancedUserInvitation, BulkUserInvitation } from '@/components/admin/EnhancedUserInvitation';
import { UserRoleManagement } from '@/components/admin/UserRoleManagement';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import { usePersonaManagement } from '@/hooks/usePersonaPermissions';

interface TenantSettings {
  name: string;
  slug: string;
  domain?: string;
  branding: {
    logo?: string;
    primaryColor: string;
    secondaryColor: string;
    customCSS?: string;
  };
  features: {
    customBranding: boolean;
    apiAccess: boolean;
    advancedAnalytics: boolean;
    whiteLabel: boolean;
    sso: boolean;
  };
  limits: {
    users: number;
    exhibitions: number;
    storage: number; // in MB
  };
  usage: {
    users: number;
    exhibitions: number;
    storage: number; // in MB
  };
}

export default function TenantAdminPage() {
  return (
    <SystemPermissionGate 
      systemPermission="canManageSettings"
      fallback={
        <div className="container mx-auto py-8">
          <UpgradePrompt 
            trigger="feature_locked"
            feature="Tenant Administration"
            description="Access to tenant administration requires administrative permissions."
          />
        </div>
      }
    >
      <TenantAdminContent />
    </SystemPermissionGate>
  );
}

function TenantAdminContent() {
  const { user, tenant } = useAuth();
  const { toast } = useToast();
  const { personas, isLoading: personasLoading } = usePersonaManagement();
  const [settings, setSettings] = useState<TenantSettings>({
    name: tenant?.name || '',
    slug: tenant?.slug || '',
    domain: tenant?.domain || '',
    branding: {
      primaryColor: '#3b82f6',
      secondaryColor: '#64748b',
    },
    features: {
      customBranding: false,
      apiAccess: false,
      advancedAnalytics: false,
      whiteLabel: false,
      sso: false,
    },
    limits: {
      users: 10,
      exhibitions: 50,
      storage: 1000,
    },
    usage: {
      users: 3,
      exhibitions: 12,
      storage: 245,
    }
  });

  const [isLoading, setIsLoading] = useState(false);

  const handleSaveSettings = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement actual save functionality
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast({
        title: "Settings Saved",
        description: "Tenant settings have been updated successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getUsagePercentage = (used: number, limit: number) => {
    return Math.round((used / limit) * 100);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 75) return 'text-orange-600';
    return 'text-green-600';
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <Crown className="h-6 w-6" />
          Tenant Administration
        </h2>
        <p className="text-muted-foreground mt-1">
          Manage tenant settings, users, subscription, and advanced configuration
        </p>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="personas">Personas</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="branding">Branding</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Usage Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Users
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {settings.usage.users} / {settings.limits.users}
                </div>
                <div className={`text-sm ${getUsageColor(getUsagePercentage(settings.usage.users, settings.limits.users))}`}>
                  {getUsagePercentage(settings.usage.users, settings.limits.users)}% used
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Exhibitions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {settings.usage.exhibitions} / {settings.limits.exhibitions}
                </div>
                <div className={`text-sm ${getUsageColor(getUsagePercentage(settings.usage.exhibitions, settings.limits.exhibitions))}`}>
                  {getUsagePercentage(settings.usage.exhibitions, settings.limits.exhibitions)}% used
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Storage
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {settings.usage.storage} / {settings.limits.storage} MB
                </div>
                <div className={`text-sm ${getUsageColor(getUsagePercentage(settings.usage.storage, settings.limits.storage))}`}>
                  {getUsagePercentage(settings.usage.storage, settings.limits.storage)}% used
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Common administrative tasks
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                <EnhancedUserInvitation 
                  onInvitationSent={(invitation) => {
                    toast({ 
                      title: "Invitation Sent", 
                      description: `Invitation sent to ${invitation.email}` 
                    });
                  }}
                />
                <BulkUserInvitation 
                  onInvitationsSent={(invitations) => {
                    toast({ 
                      title: "Invitations Sent", 
                      description: `${invitations.length} invitations sent successfully` 
                    });
                  }}
                />
                <Button variant="outline">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  View Analytics
                </Button>
                <Button variant="outline">
                  <Settings className="mr-2 h-4 w-4" />
                  System Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
              <CardDescription>
                Manage users, invitations, and permissions for your tenant
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-medium">Active Users</h4>
                    <p className="text-sm text-muted-foreground">
                      {settings.usage.users} of {settings.limits.users} users
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <EnhancedUserInvitation />
                    <BulkUserInvitation />
                  </div>
                </div>
                <Separator />
                <div className="text-sm text-muted-foreground">
                  <Button variant="outline" asChild>
                    <a href="/access-control/users">
                      <Users className="mr-2 h-4 w-4" />
                      Manage Users
                    </a>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Personas Tab */}
        <TabsContent value="personas" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Persona Management</CardTitle>
              <CardDescription>
                Manage user personas and permissions for your organization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-medium">Available Personas</h4>
                    <p className="text-sm text-muted-foreground">
                      {personas.length} personas configured
                    </p>
                  </div>
                  <Button variant="outline" asChild>
                    <a href="/access-control?tab=persona-dashboard">
                      <Crown className="mr-2 h-4 w-4" />
                      Manage Personas
                    </a>
                  </Button>
                </div>
                <Separator />
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {personas.slice(0, 6).map(persona => (
                    <Card key={persona.id} className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h5 className="font-medium">{persona.name}</h5>
                        <Badge variant={persona.category === 'default' ? 'secondary' : 'default'}>
                          {persona.category}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {persona.description}
                      </p>
                      <div className="text-xs text-muted-foreground">
                        {persona.permissions.modules.length} modules
                      </div>
                    </Card>
                  ))}
                </div>
                {personas.length > 6 && (
                  <div className="text-center">
                    <Button variant="outline" asChild>
                      <a href="/access-control?tab=persona-dashboard">
                        View All Personas
                      </a>
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Basic Settings</CardTitle>
              <CardDescription>
                Configure basic tenant information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="tenant-name">Tenant Name</Label>
                  <Input 
                    id="tenant-name" 
                    value={settings.name}
                    onChange={(e) => setSettings(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="tenant-slug">Slug</Label>
                  <Input 
                    id="tenant-slug" 
                    value={settings.slug}
                    onChange={(e) => setSettings(prev => ({ ...prev, slug: e.target.value }))}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="tenant-domain">Custom Domain (Optional)</Label>
                <Input 
                  id="tenant-domain" 
                  value={settings.domain || ''}
                  onChange={(e) => setSettings(prev => ({ ...prev, domain: e.target.value }))}
                  placeholder="your-domain.com"
                />
              </div>
              <Button onClick={handleSaveSettings} disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save Settings'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Branding Tab */}
        <TabsContent value="branding" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Branding Settings
              </CardTitle>
              <CardDescription>Customize your tenant's appearance and branding</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="primary-color">Primary Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="primary-color"
                      type="color"
                      value={settings.branding.primaryColor}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        branding: { ...prev.branding, primaryColor: e.target.value }
                      }))}
                      className="w-16 h-10"
                    />
                    <Input
                      value={settings.branding.primaryColor}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        branding: { ...prev.branding, primaryColor: e.target.value }
                      }))}
                      className="flex-1"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="secondary-color">Secondary Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="secondary-color"
                      type="color"
                      value={settings.branding.secondaryColor}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        branding: { ...prev.branding, secondaryColor: e.target.value }
                      }))}
                      className="w-16 h-10"
                    />
                    <Input
                      value={settings.branding.secondaryColor}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        branding: { ...prev.branding, secondaryColor: e.target.value }
                      }))}
                      className="flex-1"
                    />
                  </div>
                </div>
              </div>
              <div>
                <Label htmlFor="logo-url">Logo URL</Label>
                <Input
                  id="logo-url"
                  value={settings.branding.logo || ''}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    branding: { ...prev.branding, logo: e.target.value }
                  }))}
                  placeholder="https://your-domain.com/logo.png"
                />
              </div>
              <Button onClick={handleSaveSettings} disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save Branding'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Features Tab */}
        <TabsContent value="features" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Feature Management
              </CardTitle>
              <CardDescription>Enable or disable features for your tenant</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="custom-branding">Custom Branding</Label>
                    <p className="text-sm text-muted-foreground">Allow custom colors, logos, and styling</p>
                  </div>
                  <Switch
                    id="custom-branding"
                    checked={settings.features.customBranding}
                    onCheckedChange={(checked) => setSettings(prev => ({
                      ...prev,
                      features: { ...prev.features, customBranding: checked }
                    }))}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="api-access">API Access</Label>
                    <p className="text-sm text-muted-foreground">Enable REST API access for integrations</p>
                  </div>
                  <Switch
                    id="api-access"
                    checked={settings.features.apiAccess}
                    onCheckedChange={(checked) => setSettings(prev => ({
                      ...prev,
                      features: { ...prev.features, apiAccess: checked }
                    }))}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="advanced-analytics">Advanced Analytics</Label>
                    <p className="text-sm text-muted-foreground">Access to detailed analytics and reporting</p>
                  </div>
                  <Switch
                    id="advanced-analytics"
                    checked={settings.features.advancedAnalytics}
                    onCheckedChange={(checked) => setSettings(prev => ({
                      ...prev,
                      features: { ...prev.features, advancedAnalytics: checked }
                    }))}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="white-label">White Label</Label>
                    <p className="text-sm text-muted-foreground">Remove EVEXA branding from the interface</p>
                  </div>
                  <Switch
                    id="white-label"
                    checked={settings.features.whiteLabel}
                    onCheckedChange={(checked) => setSettings(prev => ({
                      ...prev,
                      features: { ...prev.features, whiteLabel: checked }
                    }))}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="sso">Single Sign-On (SSO)</Label>
                    <p className="text-sm text-muted-foreground">Enable SAML/OAuth SSO integration</p>
                  </div>
                  <Switch
                    id="sso"
                    checked={settings.features.sso}
                    onCheckedChange={(checked) => setSettings(prev => ({
                      ...prev,
                      features: { ...prev.features, sso: checked }
                    }))}
                  />
                </div>
              </div>
              <Button onClick={handleSaveSettings} disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save Features'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security Settings
              </CardTitle>
              <CardDescription>Configure security and access controls</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium flex items-center gap-2">
                    <Lock className="h-4 w-4" />
                    Access Control
                  </h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    Configure how users can access your tenant
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm">Two-factor authentication enabled</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm">Password policy enforced</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-orange-600" />
                      <span className="text-sm">Session timeout: 24 hours</span>
                    </div>
                  </div>
                </div>
                <Separator />
                <div>
                  <h4 className="font-medium flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    IP Restrictions
                  </h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    Restrict access to specific IP addresses or ranges
                  </p>
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-orange-600" />
                    <span className="text-sm">No IP restrictions configured</span>
                  </div>
                </div>
                <Separator />
                <div>
                  <h4 className="font-medium">Audit Logging</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    Track user actions and system events
                  </p>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm">Audit logging enabled</span>
                  </div>
                </div>
              </div>
              <Button variant="outline">
                <Settings className="mr-2 h-4 w-4" />
                Configure Security
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
