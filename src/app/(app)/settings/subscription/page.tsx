/**
 * Subscription Management Page
 * Comprehensive subscription and billing management
 */

"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CreditCard, 
  Crown, 
  Users, 
  Building2, 
  Database,
  Zap,
  CheckCircle,
  AlertTriangle,
  Calendar,
  TrendingUp,
  Settings,
  Download,
  RefreshCw
} from 'lucide-react';
import { useSubscriptionLimits } from '@/hooks/useSubscriptionLimits';
import { getTierComparison, formatPrice, type SubscriptionTier } from '@/lib/subscription-limits';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';

export default function SubscriptionPage() {
  const { user, tenant } = useAuth();
  const { toast } = useToast();
  const {
    subscriptionTier,
    usage,
    limits,
    isLoading,
    refreshUsage,
    getUsagePercentage,
    isNearLimit,
    isAtLimit
  } = useSubscriptionLimits();

  const [isYearly, setIsYearly] = useState(false);
  const [isUpgrading, setIsUpgrading] = useState(false);
  const tiers = getTierComparison();

  const handleUpgrade = async (tierId: string) => {
    setIsUpgrading(true);
    try {
      // TODO: Implement actual Stripe integration
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast({
        title: "Upgrade Successful",
        description: `Successfully upgraded to ${tierId} plan.`,
      });
    } catch (error) {
      toast({
        title: "Upgrade Failed",
        description: "Failed to upgrade subscription. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpgrading(false);
    }
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 75) return 'text-orange-600';
    if (percentage >= 50) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-orange-500';
    if (percentage >= 50) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <RefreshCw className="h-6 w-6 animate-spin" />
        <span className="ml-2">Loading subscription data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <CreditCard className="h-6 w-6" />
          Subscription & Billing
        </h2>
        <p className="text-muted-foreground mt-1">
          Manage your subscription, view usage, and upgrade your plan
        </p>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
          <TabsTrigger value="plans">Plans</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Current Plan */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Crown className="h-5 w-5" />
                  Current Plan
                </span>
                {subscriptionTier && (
                  <Badge variant={subscriptionTier.popular ? "default" : "secondary"}>
                    {subscriptionTier.name}
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                {subscriptionTier?.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold">
                    {subscriptionTier ? formatPrice(subscriptionTier.price) : '$0'}
                    <span className="text-sm font-normal text-muted-foreground">/month</span>
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Next billing: {new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}
                  </p>
                </div>
                <Button variant="outline">
                  <Settings className="mr-2 h-4 w-4" />
                  Manage Plan
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Usage Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  <span className={`text-sm font-medium ${getUsageColor(getUsagePercentage('users'))}`}>
                    {getUsagePercentage('users')}%
                  </span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Users</span>
                    <span>{usage.users} / {subscriptionTier?.limits.users === -1 ? '∞' : subscriptionTier?.limits.users}</span>
                  </div>
                  <Progress 
                    value={getUsagePercentage('users')} 
                    className="h-2"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <Building2 className="h-5 w-5 text-green-600" />
                  <span className={`text-sm font-medium ${getUsageColor(getUsagePercentage('exhibitions'))}`}>
                    {getUsagePercentage('exhibitions')}%
                  </span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Exhibitions</span>
                    <span>{usage.exhibitions} / {subscriptionTier?.limits.exhibitions === -1 ? '∞' : subscriptionTier?.limits.exhibitions}</span>
                  </div>
                  <Progress 
                    value={getUsagePercentage('exhibitions')} 
                    className="h-2"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <Calendar className="h-5 w-5 text-purple-600" />
                  <span className={`text-sm font-medium ${getUsageColor(getUsagePercentage('events'))}`}>
                    {getUsagePercentage('events')}%
                  </span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Events</span>
                    <span>{usage.events} / {subscriptionTier?.limits.events === -1 ? '∞' : subscriptionTier?.limits.events}</span>
                  </div>
                  <Progress 
                    value={getUsagePercentage('events')} 
                    className="h-2"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <Database className="h-5 w-5 text-orange-600" />
                  <span className={`text-sm font-medium ${getUsageColor(getUsagePercentage('storage'))}`}>
                    {getUsagePercentage('storage')}%
                  </span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Storage</span>
                    <span>{usage.storage} / {subscriptionTier?.limits.storage === -1 ? '∞' : subscriptionTier?.limits.storage} MB</span>
                  </div>
                  <Progress 
                    value={getUsagePercentage('storage')} 
                    className="h-2"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Warnings */}
          {(isNearLimit('users') || isNearLimit('exhibitions') || isNearLimit('events') || isNearLimit('storage')) && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                You're approaching your plan limits. Consider upgrading to avoid service interruptions.
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        {/* Usage Tab */}
        <TabsContent value="usage" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Detailed Usage
                </span>
                <Button variant="outline" size="sm" onClick={refreshUsage}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Refresh
                </Button>
              </CardTitle>
              <CardDescription>
                Monitor your usage across all plan limits
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Detailed usage metrics would go here */}
              <div className="text-center text-muted-foreground">
                Detailed usage analytics will be implemented here
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Plans Tab */}
        <TabsContent value="plans" className="space-y-6">
          {/* Billing Toggle */}
          <div className="flex items-center justify-center gap-4">
            <span className={!isYearly ? 'font-medium' : 'text-muted-foreground'}>Monthly</span>
            <Switch checked={isYearly} onCheckedChange={setIsYearly} />
            <span className={isYearly ? 'font-medium' : 'text-muted-foreground'}>
              Yearly <Badge variant="secondary" className="ml-1">Save 20%</Badge>
            </span>
          </div>

          {/* Plan Comparison */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {tiers.map((tier) => (
              <Card key={tier.id} className={`relative ${tier.popular ? 'border-primary' : ''}`}>
                {tier.popular && (
                  <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                    Most Popular
                  </Badge>
                )}
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    {tier.name}
                    {subscriptionTier?.id === tier.id && (
                      <Badge variant="outline">Current</Badge>
                    )}
                  </CardTitle>
                  <CardDescription>{tier.description}</CardDescription>
                  <div className="text-3xl font-bold">
                    {isYearly && tier.yearlyPrice 
                      ? formatPrice(Math.floor(tier.yearlyPrice / 12))
                      : tier.formattedPrice
                    }
                    <span className="text-sm font-normal text-muted-foreground">/month</span>
                  </div>
                  {isYearly && tier.yearlySavings > 0 && (
                    <p className="text-sm text-green-600">
                      Save {tier.formattedYearlySavings} yearly
                    </p>
                  )}
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 mb-6">
                    {tier.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button 
                    className="w-full" 
                    variant={subscriptionTier?.id === tier.id ? "outline" : "default"}
                    disabled={subscriptionTier?.id === tier.id || isUpgrading}
                    onClick={() => handleUpgrade(tier.id)}
                  >
                    {subscriptionTier?.id === tier.id ? 'Current Plan' : 'Upgrade'}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Billing Tab */}
        <TabsContent value="billing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Billing Information
              </CardTitle>
              <CardDescription>
                Manage your payment methods and billing history
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center text-muted-foreground">
                Billing management interface will be implemented here
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
