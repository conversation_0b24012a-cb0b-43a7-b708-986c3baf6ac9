"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { 
  Target,
  TrendingUp,
  TrendingDown,
  Minus,
  Eye,
  Download,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Zap,
  Award,
  Activity
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import { collection, getDocs, query, orderBy, where, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { formatDistanceToNow, format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

interface PerformanceMetric {
  id?: string;
  kpiName: string;
  kpiType: 'revenue' | 'efficiency' | 'quality' | 'customer' | 'employee' | 'operational';
  category: string;
  currentValue: number;
  targetValue: number;
  previousValue?: number;
  unit: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
  performanceScore: number; // 0-100
  status: 'excellent' | 'good' | 'warning' | 'critical';
  owner: string;
  ownerEmail?: string;
  department: string;
  description?: string;
  calculationMethod?: string;
  dataSource: string;
  lastCalculated?: Timestamp | Date | string;
  nextUpdate?: Timestamp | Date | string;
  isActive: boolean;
  createdAt?: Timestamp | Date | string;
  updatedAt?: Timestamp | Date | string;
}

const kpiTypeConfig = {
  'revenue': { color: 'bg-green-100 text-green-800', icon: Target },
  'efficiency': { color: 'bg-blue-100 text-blue-800', icon: Zap },
  'quality': { color: 'bg-purple-100 text-purple-800', icon: Award },
  'customer': { color: 'bg-orange-100 text-orange-800', icon: Target },
  'employee': { color: 'bg-pink-100 text-pink-800', icon: Target },
  'operational': { color: 'bg-gray-100 text-gray-800', icon: Activity },
};

const statusConfig = {
  'excellent': { color: 'bg-green-100 text-green-800', icon: CheckCircle, scoreRange: [90, 100] },
  'good': { color: 'bg-blue-100 text-blue-800', icon: CheckCircle, scoreRange: [70, 89] },
  'warning': { color: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle, scoreRange: [50, 69] },
  'critical': { color: 'bg-red-100 text-red-800', icon: AlertTriangle, scoreRange: [0, 49] },
};

const trendConfig = {
  'up': { color: 'text-green-600', icon: TrendingUp },
  'down': { color: 'text-red-600', icon: TrendingDown },
  'stable': { color: 'text-gray-600', icon: Minus },
};

interface PerformanceMetricsTableProps {
  kpiType?: string;
  department?: string;
  owner?: string;
  showTypeFilter?: boolean;
  showDepartmentFilter?: boolean;
  showOwnerFilter?: boolean;
}

export default function PerformanceMetricsTable({ 
  kpiType,
  department,
  owner,
  showTypeFilter = true,
  showDepartmentFilter = true,
  showOwnerFilter = true
}: PerformanceMetricsTableProps) {
  const { toast } = useToast();
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetric[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load performance metrics
  useEffect(() => {
    loadPerformanceMetrics();
  }, [kpiType, department, owner]);

  const loadPerformanceMetrics = async () => {
    setIsLoading(true);
    try {
      let metricsQuery = query(
        collection(db, COLLECTIONS.PERFORMANCE_METRICS),
        orderBy('performanceScore', 'desc'),
        limit(100)
      );

      // Add filters if specified
      if (kpiType) {
        metricsQuery = query(metricsQuery, where('kpiType', '==', kpiType));
      }
      
      if (department) {
        metricsQuery = query(metricsQuery, where('department', '==', department));
      }

      if (owner) {
        metricsQuery = query(metricsQuery, where('owner', '==', owner));
      }

      // Filter only active metrics
      metricsQuery = query(metricsQuery, where('isActive', '==', true));

      const metricsSnapshot = await getDocs(metricsQuery);
      const fetchedMetrics = metricsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        lastCalculated: doc.data().lastCalculated?.toDate?.() || new Date(doc.data().lastCalculated),
        nextUpdate: doc.data().nextUpdate?.toDate?.() || new Date(doc.data().nextUpdate),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(doc.data().createdAt),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date(doc.data().updatedAt)
      } as PerformanceMetric));

      setPerformanceMetrics(fetchedMetrics);
    } catch (error) {
      console.error('Error loading performance metrics:', error);
      toast({
        title: "Error",
        description: "Failed to load performance metrics.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const formatValue = (value: number, unit: string) => {
    if (unit === '%') {
      return `${value.toFixed(1)}%`;
    } else if (unit === '$' || unit === 'USD') {
      return `$${value.toLocaleString()}`;
    } else if (unit === 'count' || unit === '#') {
      return value.toLocaleString();
    } else {
      return `${value.toLocaleString()} ${unit}`;
    }
  };

  const calculateProgress = (current: number, target: number) => {
    if (target === 0) return 0;
    return Math.min((current / target) * 100, 100);
  };

  const getStatusFromScore = (score: number): keyof typeof statusConfig => {
    if (score >= 90) return 'excellent';
    if (score >= 70) return 'good';
    if (score >= 50) return 'warning';
    return 'critical';
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const columns: ColumnDef<PerformanceMetric>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "kpiName",
      header: "KPI Name",
      cell: ({ row }) => {
        const metric = row.original;
        const typeConfig = kpiTypeConfig[metric.kpiType];
        const TypeIcon = typeConfig.icon;
        
        return (
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gray-100">
              <TypeIcon className="h-4 w-4" />
            </div>
            <div>
              <div className="font-medium">{metric.kpiName}</div>
              <Badge className={`${typeConfig.color} text-xs mt-1`}>
                {metric.kpiType}
              </Badge>
            </div>
          </div>
        );
      },
    },
    {
      id: "performance",
      header: "Performance",
      cell: ({ row }) => {
        const metric = row.original;
        const progress = calculateProgress(metric.currentValue, metric.targetValue);
        const status = getStatusFromScore(metric.performanceScore);
        const statusConfig_ = statusConfig[status];
        
        return (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="font-mono font-bold">
                {formatValue(metric.currentValue, metric.unit)}
              </span>
              <Badge className={`${statusConfig_.color} text-xs`}>
                {metric.performanceScore}/100
              </Badge>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between text-xs text-gray-500">
                <span>Target: {formatValue(metric.targetValue, metric.unit)}</span>
                <span>{progress.toFixed(0)}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          </div>
        );
      },
      sortingFn: (rowA, rowB) => {
        return rowA.original.performanceScore - rowB.original.performanceScore;
      },
    },
    {
      id: "trend",
      header: "Trend",
      cell: ({ row }) => {
        const metric = row.original;
        const trendConfig_ = trendConfig[metric.trend];
        const TrendIcon = trendConfig_.icon;
        
        return (
          <div className={`flex items-center gap-1 ${trendConfig_.color}`}>
            <TrendIcon className="h-4 w-4" />
            <span className="font-medium">
              {metric.trend === 'stable' ? '0' : `${metric.trend === 'up' ? '+' : '-'}${Math.abs(metric.trendPercentage)}`}%
            </span>
          </div>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.original.trend);
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const metric = row.original;
        const status = getStatusFromScore(metric.performanceScore);
        const config = statusConfig[status];
        const StatusIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <StatusIcon className="h-3 w-3" />
            {status}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        const status = getStatusFromScore(row.original.performanceScore);
        return value.includes(status);
      },
    },
    {
      accessorKey: "owner",
      header: "Owner",
      cell: ({ row }) => {
        const metric = row.original;
        
        return (
          <div className="text-sm">
            <div className="font-medium">{metric.owner}</div>
            <div className="text-gray-500">{metric.department}</div>
          </div>
        );
      },
      enableHiding: !!owner, // Hide if filtering by specific owner
    },
    {
      accessorKey: "frequency",
      header: "Frequency",
      cell: ({ row }) => {
        const frequency = row.original.frequency;
        
        return (
          <Badge variant="outline" className="capitalize">
            {frequency}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: "lastUpdate",
      header: "Last Updated",
      cell: ({ row }) => {
        const metric = row.original;
        const lastCalculated = metric.lastCalculated;
        const nextUpdate = metric.nextUpdate;
        
        return (
          <div className="text-sm">
            <div className="text-gray-600">{formatDate(lastCalculated)}</div>
            {nextUpdate && (
              <div className="text-xs text-gray-500">
                Next: {formatDate(nextUpdate)}
              </div>
            )}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const metric = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // View detailed KPI analytics
                console.log('View KPI details:', metric);
              }}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Refresh KPI data
                console.log('Refresh KPI:', metric);
              }}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], [kpiType, department, owner]);

  const tableActions = [
    {
      label: "Refresh All",
      icon: RefreshCw,
      onClick: () => {
        loadPerformanceMetrics();
        toast({
          title: "Refreshed",
          description: "Performance metrics have been refreshed."
        });
      },
      variant: "default" as const,
    },
    {
      label: "Export Selected",
      icon: Download,
      onClick: (selectedRows: PerformanceMetric[]) => {
        console.log('Export performance metrics:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Metrics export will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "View Dashboard",
      icon: BarChart3,
      onClick: (selectedRows: PerformanceMetric[]) => {
        console.log('View dashboard for:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "KPI dashboard will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    ...(showTypeFilter ? [{
      key: 'kpiType',
      label: 'KPI Type',
      options: [
        { label: 'Revenue', value: 'revenue' },
        { label: 'Efficiency', value: 'efficiency' },
        { label: 'Quality', value: 'quality' },
        { label: 'Customer', value: 'customer' },
        { label: 'Employee', value: 'employee' },
        { label: 'Operational', value: 'operational' },
      ]
    }] : []),
    ...(showDepartmentFilter ? [{
      key: 'department',
      label: 'Department',
      options: [
        { label: 'Sales', value: 'sales' },
        { label: 'Marketing', value: 'marketing' },
        { label: 'Operations', value: 'operations' },
        { label: 'Finance', value: 'finance' },
        { label: 'HR', value: 'hr' },
        { label: 'IT', value: 'it' },
      ]
    }] : []),
    {
      key: 'status',
      label: 'Status',
      options: [
        { label: 'Excellent', value: 'excellent' },
        { label: 'Good', value: 'good' },
        { label: 'Warning', value: 'warning' },
        { label: 'Critical', value: 'critical' },
      ]
    },
    {
      key: 'trend',
      label: 'Trend',
      options: [
        { label: 'Trending Up', value: 'up' },
        { label: 'Trending Down', value: 'down' },
        { label: 'Stable', value: 'stable' },
      ]
    },
    {
      key: 'frequency',
      label: 'Frequency',
      options: [
        { label: 'Daily', value: 'daily' },
        { label: 'Weekly', value: 'weekly' },
        { label: 'Monthly', value: 'monthly' },
        { label: 'Quarterly', value: 'quarterly' },
        { label: 'Yearly', value: 'yearly' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={performanceMetrics}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadPerformanceMetrics}
      tableActions={tableActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search KPIs by name, owner, department..."
      emptyStateMessage="No performance metrics found"
      emptyStateDescription="KPI data will appear here as metrics are configured and calculated."
    />
  );
}
