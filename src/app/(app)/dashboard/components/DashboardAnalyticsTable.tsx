"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { 
  BarChart3,
  TrendingUp,
  TrendingDown,
  Minus,
  Eye,
  Download,
  RefreshCw,
  Calendar,
  Target,
  Users,
  DollarSign,
  Activity,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import { collection, getDocs, query, orderBy, where, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { formatDistanceToNow, format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

interface AnalyticsData {
  id?: string;
  metricName: string;
  metricType: 'kpi' | 'performance' | 'engagement' | 'financial' | 'operational';
  category: string;
  value: number;
  previousValue?: number;
  target?: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
  period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  dateRange: {
    start: Timestamp | Date | string;
    end: Timestamp | Date | string;
  };
  status: 'on_track' | 'at_risk' | 'critical' | 'excellent';
  relatedEntityType?: 'exhibition' | 'event' | 'task' | 'lead' | 'budget';
  relatedEntityId?: string;
  relatedEntityName?: string;
  createdAt?: Timestamp | Date | string;
  updatedAt?: Timestamp | Date | string;
}

const metricTypeConfig = {
  'kpi': { color: 'bg-blue-100 text-blue-800', icon: Target },
  'performance': { color: 'bg-green-100 text-green-800', icon: BarChart3 },
  'engagement': { color: 'bg-purple-100 text-purple-800', icon: Users },
  'financial': { color: 'bg-orange-100 text-orange-800', icon: DollarSign },
  'operational': { color: 'bg-gray-100 text-gray-800', icon: Activity },
};

const statusConfig = {
  'excellent': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
  'on_track': { color: 'bg-blue-100 text-blue-800', icon: CheckCircle },
  'at_risk': { color: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle },
  'critical': { color: 'bg-red-100 text-red-800', icon: AlertTriangle },
};

const trendConfig = {
  'up': { color: 'text-green-600', icon: TrendingUp },
  'down': { color: 'text-red-600', icon: TrendingDown },
  'stable': { color: 'text-gray-600', icon: Minus },
};

interface DashboardAnalyticsTableProps {
  category?: string;
  metricType?: string;
  dateRange?: { start: Date; end: Date };
  showCategoryFilter?: boolean;
  showTypeFilter?: boolean;
}

export default function DashboardAnalyticsTable({ 
  category,
  metricType,
  dateRange,
  showCategoryFilter = true,
  showTypeFilter = true
}: DashboardAnalyticsTableProps) {
  const { toast } = useToast();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load analytics data
  useEffect(() => {
    loadAnalyticsData();
  }, [category, metricType, dateRange]);

  const loadAnalyticsData = async () => {
    setIsLoading(true);
    try {
      let analyticsQuery = query(
        collection(db, COLLECTIONS.ANALYTICS_DATA),
        orderBy('updatedAt', 'desc'),
        limit(100)
      );

      // Add filters if specified
      if (category) {
        analyticsQuery = query(analyticsQuery, where('category', '==', category));
      }
      
      if (metricType) {
        analyticsQuery = query(analyticsQuery, where('metricType', '==', metricType));
      }

      if (dateRange) {
        analyticsQuery = query(
          analyticsQuery,
          where('dateRange.start', '>=', Timestamp.fromDate(dateRange.start)),
          where('dateRange.end', '<=', Timestamp.fromDate(dateRange.end))
        );
      }

      const analyticsSnapshot = await getDocs(analyticsQuery);
      const fetchedAnalytics = analyticsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        dateRange: {
          start: doc.data().dateRange?.start?.toDate?.() || new Date(doc.data().dateRange?.start),
          end: doc.data().dateRange?.end?.toDate?.() || new Date(doc.data().dateRange?.end)
        },
        createdAt: doc.data().createdAt?.toDate?.() || new Date(doc.data().createdAt),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date(doc.data().updatedAt)
      } as AnalyticsData));

      setAnalyticsData(fetchedAnalytics);
    } catch (error) {
      console.error('Error loading analytics data:', error);
      toast({
        title: "Error",
        description: "Failed to load analytics data.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const formatValue = (value: number, unit: string) => {
    if (unit === '%') {
      return `${value.toFixed(1)}%`;
    } else if (unit === '$' || unit === 'USD') {
      return `$${value.toLocaleString()}`;
    } else if (unit === 'count' || unit === '#') {
      return value.toLocaleString();
    } else {
      return `${value.toLocaleString()} ${unit}`;
    }
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return format(dateObj, 'MMM dd, yyyy');
  };

  const formatDateRelative = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const calculateTargetProgress = (value: number, target?: number) => {
    if (!target || target === 0) return null;
    return Math.min((value / target) * 100, 100);
  };

  const columns: ColumnDef<AnalyticsData>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "metricName",
      header: "Metric",
      cell: ({ row }) => {
        const metric = row.original;
        const typeConfig = metricTypeConfig[metric.metricType];
        const TypeIcon = typeConfig.icon;
        
        return (
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gray-100">
              <TypeIcon className="h-4 w-4" />
            </div>
            <div>
              <div className="font-medium">{metric.metricName}</div>
              <Badge className={`${typeConfig.color} text-xs mt-1`}>
                {metric.metricType}
              </Badge>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: ({ row }) => {
        const category = row.original.category;
        
        return (
          <Badge variant="outline" className="capitalize">
            {category}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
      enableHiding: !!category, // Hide if filtering by specific category
    },
    {
      id: "value",
      header: "Current Value",
      cell: ({ row }) => {
        const metric = row.original;
        const progress = calculateTargetProgress(metric.value, metric.target);
        
        return (
          <div className="space-y-1">
            <div className="font-mono font-bold text-lg">
              {formatValue(metric.value, metric.unit)}
            </div>
            {progress !== null && (
              <div className="w-full bg-gray-200 rounded-full h-1">
                <div 
                  className="bg-blue-600 h-1 rounded-full" 
                  style={{ width: `${progress}%` }}
                />
              </div>
            )}
          </div>
        );
      },
      sortingFn: (rowA, rowB) => {
        return rowA.original.value - rowB.original.value;
      },
    },
    {
      id: "trend",
      header: "Trend",
      cell: ({ row }) => {
        const metric = row.original;
        const trendConfig_ = trendConfig[metric.trend];
        const TrendIcon = trendConfig_.icon;
        
        return (
          <div className={`flex items-center gap-1 ${trendConfig_.color}`}>
            <TrendIcon className="h-4 w-4" />
            <span className="font-medium">
              {metric.trend === 'stable' ? '0' : `${metric.trend === 'up' ? '+' : '-'}${Math.abs(metric.trendPercentage)}`}%
            </span>
          </div>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.original.trend);
      },
    },
    {
      id: "target",
      header: "Target",
      cell: ({ row }) => {
        const metric = row.original;
        
        if (!metric.target) {
          return <span className="text-gray-400">No target</span>;
        }
        
        const progress = calculateTargetProgress(metric.value, metric.target);
        const isOnTrack = progress && progress >= 80;
        
        return (
          <div className="space-y-1">
            <div className="font-mono text-sm">
              {formatValue(metric.target, metric.unit)}
            </div>
            {progress !== null && (
              <Badge variant={isOnTrack ? "default" : "secondary"} className="text-xs">
                {progress.toFixed(0)}% of target
              </Badge>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.status;
        const config = statusConfig[status];
        const StatusIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <StatusIcon className="h-3 w-3" />
            {status.replace('_', ' ')}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "period",
      header: "Period",
      cell: ({ row }) => {
        const metric = row.original;
        
        return (
          <div className="text-sm">
            <div className="font-medium capitalize">{metric.period}</div>
            <div className="text-gray-500">
              {formatDate(metric.dateRange.start)} - {formatDate(metric.dateRange.end)}
            </div>
          </div>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: "relatedEntity",
      header: "Related To",
      cell: ({ row }) => {
        const metric = row.original;
        
        if (!metric.relatedEntityType || !metric.relatedEntityName) {
          return <span className="text-gray-400">General</span>;
        }
        
        return (
          <div className="text-sm">
            <div className="font-medium">{metric.relatedEntityName}</div>
            <Badge variant="outline" className="text-xs capitalize">
              {metric.relatedEntityType}
            </Badge>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "updatedAt",
      header: "Last Updated",
      cell: ({ row }) => {
        const updatedAt = row.original.updatedAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDateRelative(updatedAt)}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const metric = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // View detailed analytics
                console.log('View metric details:', metric);
              }}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Refresh metric data
                console.log('Refresh metric:', metric);
              }}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], [category, metricType]);

  const tableActions = [
    {
      label: "Refresh All",
      icon: RefreshCw,
      onClick: () => {
        loadAnalyticsData();
        toast({
          title: "Refreshed",
          description: "Analytics data has been refreshed."
        });
      },
      variant: "default" as const,
    },
    {
      label: "Export Selected",
      icon: Download,
      onClick: (selectedRows: AnalyticsData[]) => {
        console.log('Export analytics data:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Analytics export will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "View Trends",
      icon: BarChart3,
      onClick: (selectedRows: AnalyticsData[]) => {
        console.log('View trends for:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Trend analysis will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    ...(showTypeFilter ? [{
      key: 'metricType',
      label: 'Metric Type',
      options: [
        { label: 'KPI', value: 'kpi' },
        { label: 'Performance', value: 'performance' },
        { label: 'Engagement', value: 'engagement' },
        { label: 'Financial', value: 'financial' },
        { label: 'Operational', value: 'operational' },
      ]
    }] : []),
    ...(showCategoryFilter ? [{
      key: 'category',
      label: 'Category',
      options: [
        { label: 'Exhibitions', value: 'exhibitions' },
        { label: 'Events', value: 'events' },
        { label: 'Tasks', value: 'tasks' },
        { label: 'Leads', value: 'leads' },
        { label: 'Budget', value: 'budget' },
        { label: 'Marketing', value: 'marketing' },
      ]
    }] : []),
    {
      key: 'status',
      label: 'Status',
      options: [
        { label: 'Excellent', value: 'excellent' },
        { label: 'On Track', value: 'on_track' },
        { label: 'At Risk', value: 'at_risk' },
        { label: 'Critical', value: 'critical' },
      ]
    },
    {
      key: 'trend',
      label: 'Trend',
      options: [
        { label: 'Trending Up', value: 'up' },
        { label: 'Trending Down', value: 'down' },
        { label: 'Stable', value: 'stable' },
      ]
    },
    {
      key: 'period',
      label: 'Period',
      options: [
        { label: 'Daily', value: 'daily' },
        { label: 'Weekly', value: 'weekly' },
        { label: 'Monthly', value: 'monthly' },
        { label: 'Quarterly', value: 'quarterly' },
        { label: 'Yearly', value: 'yearly' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={analyticsData}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadAnalyticsData}
      tableActions={tableActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search analytics by metric name, category..."
      emptyStateMessage="No analytics data found"
      emptyStateDescription="Analytics data will appear here as metrics are collected."
    />
  );
}
