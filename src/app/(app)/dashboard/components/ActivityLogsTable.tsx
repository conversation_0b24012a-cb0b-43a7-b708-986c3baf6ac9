"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { 
  Activity,
  Eye,
  Download,
  User,
  Settings,
  BarChart3,
  RefreshCw,
  Clock,
  MousePointer,
  LogIn,
  LogOut,
  Edit,
  Trash2,
  Plus,
  Filter,
  Search
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import { collection, getDocs, query, orderBy, where, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { formatDistanceToNow, format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

interface ActivityLog {
  id?: string;
  userId: string;
  userEmail?: string;
  userName?: string;
  action: string;
  actionType: 'view' | 'create' | 'update' | 'delete' | 'login' | 'logout' | 'export' | 'filter' | 'search';
  entityType?: 'dashboard' | 'widget' | 'analytics' | 'kpi' | 'report' | 'user' | 'system';
  entityId?: string;
  entityName?: string;
  description: string;
  metadata?: {
    [key: string]: any;
  };
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  duration?: number; // in milliseconds
  success: boolean;
  errorMessage?: string;
  timestamp: Timestamp | Date | string;
}

const actionTypeConfig = {
  'view': { color: 'bg-blue-100 text-blue-800', icon: Eye },
  'create': { color: 'bg-green-100 text-green-800', icon: Plus },
  'update': { color: 'bg-yellow-100 text-yellow-800', icon: Edit },
  'delete': { color: 'bg-red-100 text-red-800', icon: Trash2 },
  'login': { color: 'bg-purple-100 text-purple-800', icon: LogIn },
  'logout': { color: 'bg-purple-100 text-purple-800', icon: LogOut },
  'export': { color: 'bg-orange-100 text-orange-800', icon: Download },
  'filter': { color: 'bg-gray-100 text-gray-800', icon: Filter },
  'search': { color: 'bg-indigo-100 text-indigo-800', icon: Search },
};

const entityTypeConfig = {
  'dashboard': { color: 'bg-blue-100 text-blue-800', icon: BarChart3 },
  'widget': { color: 'bg-green-100 text-green-800', icon: Settings },
  'analytics': { color: 'bg-purple-100 text-purple-800', icon: BarChart3 },
  'kpi': { color: 'bg-orange-100 text-orange-800', icon: Activity },
  'report': { color: 'bg-yellow-100 text-yellow-800', icon: BarChart3 },
  'user': { color: 'bg-pink-100 text-pink-800', icon: User },
  'system': { color: 'bg-gray-100 text-gray-800', icon: Settings },
};

interface ActivityLogsTableProps {
  userId?: string;
  actionType?: string;
  entityType?: string;
  dateRange?: { start: Date; end: Date };
  showUserFilter?: boolean;
  showActionFilter?: boolean;
  showEntityFilter?: boolean;
  maxResults?: number;
}

export default function ActivityLogsTable({ 
  userId,
  actionType,
  entityType,
  dateRange,
  showUserFilter = true,
  showActionFilter = true,
  showEntityFilter = true,
  maxResults = 100
}: ActivityLogsTableProps) {
  const { toast } = useToast();
  const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load activity logs
  useEffect(() => {
    loadActivityLogs();
  }, [userId, actionType, entityType, dateRange]);

  const loadActivityLogs = async () => {
    setIsLoading(true);
    try {
      let logsQuery = query(
        collection(db, COLLECTIONS.USER_ACTIVITY_LOGS),
        orderBy('timestamp', 'desc'),
        limit(maxResults)
      );

      // Add filters if specified
      if (userId) {
        logsQuery = query(logsQuery, where('userId', '==', userId));
      }
      
      if (actionType) {
        logsQuery = query(logsQuery, where('actionType', '==', actionType));
      }

      if (entityType) {
        logsQuery = query(logsQuery, where('entityType', '==', entityType));
      }

      if (dateRange) {
        logsQuery = query(
          logsQuery,
          where('timestamp', '>=', Timestamp.fromDate(dateRange.start)),
          where('timestamp', '<=', Timestamp.fromDate(dateRange.end))
        );
      }

      const logsSnapshot = await getDocs(logsQuery);
      const fetchedLogs = logsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate?.() || new Date(doc.data().timestamp)
      } as ActivityLog));

      setActivityLogs(fetchedLogs);
    } catch (error) {
      console.error('Error loading activity logs:', error);
      toast({
        title: "Error",
        description: "Failed to load activity logs.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const formatDateTime = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return format(dateObj, 'MMM dd, yyyy HH:mm:ss');
  };

  const formatDuration = (duration?: number) => {
    if (!duration) return 'N/A';
    
    if (duration < 1000) {
      return `${duration}ms`;
    } else if (duration < 60000) {
      return `${(duration / 1000).toFixed(1)}s`;
    } else {
      return `${(duration / 60000).toFixed(1)}m`;
    }
  };

  const columns: ColumnDef<ActivityLog>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "timestamp",
      header: "Timestamp",
      cell: ({ row }) => {
        const timestamp = row.original.timestamp;
        
        return (
          <div className="text-sm">
            <div className="font-medium">{formatDateTime(timestamp)}</div>
            <div className="text-gray-500">{formatDate(timestamp)}</div>
          </div>
        );
      },
    },
    {
      accessorKey: "userId",
      header: "User",
      cell: ({ row }) => {
        const log = row.original;
        const userName = log.userName || log.userEmail || log.userId;
        
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-xs">
                {userName.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="text-sm">
              <div className="font-medium">{userName}</div>
              {log.userEmail && (
                <div className="text-gray-500">{log.userEmail}</div>
              )}
            </div>
          </div>
        );
      },
      enableHiding: !!userId, // Hide if filtering by specific user
    },
    {
      accessorKey: "actionType",
      header: "Action",
      cell: ({ row }) => {
        const log = row.original;
        const config = actionTypeConfig[log.actionType] || actionTypeConfig.view;
        const ActionIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <ActionIcon className="h-3 w-3" />
            {log.actionType}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: ({ row }) => {
        const log = row.original;
        
        return (
          <div className="max-w-md">
            <p className="text-sm line-clamp-2">
              {log.description}
            </p>
            {log.entityType && log.entityName && (
              <div className="flex items-center gap-1 mt-1">
                <Badge className={`${entityTypeConfig[log.entityType]?.color || 'bg-gray-100 text-gray-800'} text-xs`}>
                  {log.entityType}
                </Badge>
                <span className="text-xs text-gray-500">{log.entityName}</span>
              </div>
            )}
          </div>
        );
      },
    },
    {
      id: "status",
      header: "Status",
      cell: ({ row }) => {
        const log = row.original;
        
        return (
          <div className="space-y-1">
            <Badge variant={log.success ? "default" : "destructive"}>
              {log.success ? "Success" : "Failed"}
            </Badge>
            {log.duration && (
              <div className="text-xs text-gray-500">
                {formatDuration(log.duration)}
              </div>
            )}
          </div>
        );
      },
      filterFn: (row, id, value) => {
        const success = row.original.success;
        return value.includes(success ? 'success' : 'failed');
      },
    },
    {
      id: "metadata",
      header: "Details",
      cell: ({ row }) => {
        const log = row.original;
        const metadata = log.metadata;
        const ipAddress = log.ipAddress;
        
        return (
          <div className="text-xs text-gray-500">
            {ipAddress && <div>IP: {ipAddress}</div>}
            {metadata && Object.keys(metadata).length > 0 && (
              <div>{Object.keys(metadata).length} metadata fields</div>
            )}
            {log.sessionId && (
              <div className="font-mono">Session: {log.sessionId.substring(0, 8)}...</div>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const log = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // View detailed log information
                console.log('View log details:', log);
              }}
            >
              <Eye className="h-4 w-4" />
            </Button>
            {!log.success && log.errorMessage && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  // Show error details
                  toast({
                    title: "Error Details",
                    description: log.errorMessage,
                    variant: "destructive"
                  });
                }}
              >
                <Activity className="h-4 w-4 text-red-500" />
              </Button>
            )}
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], [userId, actionType, entityType]);

  const tableActions = [
    {
      label: "Refresh Logs",
      icon: RefreshCw,
      onClick: () => {
        loadActivityLogs();
        toast({
          title: "Refreshed",
          description: "Activity logs have been refreshed."
        });
      },
      variant: "default" as const,
    },
    {
      label: "Export Selected",
      icon: Download,
      onClick: (selectedRows: ActivityLog[]) => {
        console.log('Export activity logs:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Activity log export will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Clear Old Logs",
      icon: Trash2,
      onClick: () => {
        console.log('Clear old activity logs');
        toast({
          title: "Feature Coming Soon",
          description: "Log cleanup will be available soon."
        });
      },
      variant: "destructive" as const,
    },
  ];

  const filterOptions = [
    ...(showActionFilter ? [{
      key: 'actionType',
      label: 'Action Type',
      options: [
        { label: 'View', value: 'view' },
        { label: 'Create', value: 'create' },
        { label: 'Update', value: 'update' },
        { label: 'Delete', value: 'delete' },
        { label: 'Login', value: 'login' },
        { label: 'Logout', value: 'logout' },
        { label: 'Export', value: 'export' },
        { label: 'Filter', value: 'filter' },
        { label: 'Search', value: 'search' },
      ]
    }] : []),
    ...(showEntityFilter ? [{
      key: 'entityType',
      label: 'Entity Type',
      options: [
        { label: 'Dashboard', value: 'dashboard' },
        { label: 'Widget', value: 'widget' },
        { label: 'Analytics', value: 'analytics' },
        { label: 'KPI', value: 'kpi' },
        { label: 'Report', value: 'report' },
        { label: 'User', value: 'user' },
        { label: 'System', value: 'system' },
      ]
    }] : []),
    {
      key: 'status',
      label: 'Status',
      options: [
        { label: 'Success', value: 'success' },
        { label: 'Failed', value: 'failed' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={activityLogs}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadActivityLogs}
      tableActions={tableActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search activity logs by description, user, action..."
      emptyStateMessage="No activity logs found"
      emptyStateDescription="User activity logs will appear here as actions are performed."
    />
  );
}
