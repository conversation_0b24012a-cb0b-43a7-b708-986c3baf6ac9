"use client";

import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  DragMoveEvent,
  closestCenter,
  PointerSensor,
  TouchSensor,
  KeyboardSensor,
  useSensor,
  useSensors,
  DragOverEvent,
  rectIntersection,
  getFirstCollision,
  pointerWithin,
  DragCancelEvent,
} from '@dnd-kit/core';
import {
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
  sortableKeyboardCoordinates,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useDroppable } from '@dnd-kit/core';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import {
  Calendar,
  User,
  Flag,
  MoreHorizontal,
  Clock,
  CheckCircle,
  Circle,
  AlertCircle,
  Pause,
  Settings,
  Plus,
  Minus,
  Copy,
  Trash2,
  Edit,
  MousePointer2,
  Move3D,
  Layers,
  Zap
} from 'lucide-react';
import { format } from 'date-fns';
import Link from 'next/link';
import type { Task, TaskStatus } from '@/types/firestore';
import type { TaskBoardLayout, TaskBoardColumn, TaskCardTemplate } from './TaskBoardLayoutManager';

interface NewKanbanBoardProps {
  tasks: Task[];
  usersMap: Record<string, string>;
  onTaskStatusChange: (taskId: string, newStatus: TaskStatus) => void;
  onBatchTaskStatusChange?: (taskIds: string[], newStatus: TaskStatus) => void;
  layout?: TaskBoardLayout;
  onLayoutChange?: (layout: TaskBoardLayout) => void;
}

// Enhanced drag state interface
interface DragState {
  activeId: string | null;
  draggedTask: Task | null;
  selectedTasks: Set<string>;
  isDraggingMultiple: boolean;
  dragStartPosition: { x: number; y: number } | null;
}

// Task statuses and their configurations
const COLUMNS: { status: TaskStatus; label: string; color: string; icon: any }[] = [
  { status: 'To Do', label: 'To Do', color: 'bg-slate-50 border-slate-200', icon: Circle },
  { status: 'In Progress', label: 'In Progress', color: 'bg-blue-50 border-blue-200', icon: Clock },
  { status: 'In Review', label: 'In Review', color: 'bg-yellow-50 border-yellow-200', icon: AlertCircle },
  { status: 'Awaiting Approval', label: 'Awaiting Approval', color: 'bg-orange-50 border-orange-200', icon: Pause },
  { status: 'Done', label: 'Done', color: 'bg-green-50 border-green-200', icon: CheckCircle },
];

// Priority colors
const PRIORITY_COLORS = {
  'Low': 'bg-gray-100 text-gray-700',
  'Medium': 'bg-blue-100 text-blue-700',
  'High': 'bg-orange-100 text-orange-700',
  'Urgent': 'bg-red-100 text-red-700',
};

interface TaskCardProps {
  task: Task;
  usersMap: Record<string, string>;
  cardTemplate?: TaskCardTemplate;
  isSelected?: boolean;
  isMultiSelectMode?: boolean;
  onSelect?: (taskId: string, selected: boolean) => void;
  onMultiSelectToggle?: () => void;
}

function TaskCard({
  task,
  usersMap,
  cardTemplate,
  isSelected = false,
  isMultiSelectMode = false,
  onSelect,
  onMultiSelectToggle
}: TaskCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: task.id || 'no-id',
    disabled: !task.id || isMultiSelectMode,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: isDragging ? 'none' : transition,
  };

  // Enhanced click handling for multi-select
  const handleCardClick = useCallback((e: React.MouseEvent) => {
    if (e.ctrlKey || e.metaKey) {
      e.preventDefault();
      e.stopPropagation();
      if (onSelect && task.id) {
        onSelect(task.id, !isSelected);
      }
    } else if (e.shiftKey && onMultiSelectToggle) {
      e.preventDefault();
      e.stopPropagation();
      onMultiSelectToggle();
    }
  }, [isSelected, onSelect, onMultiSelectToggle, task.id]);

  if (!task.id) {
    return (
      <Card className="p-3 bg-red-50 border-red-200">
        <p className="text-red-600 text-sm">Task missing ID: {task.title}</p>
      </Card>
    );
  }

  const assigneeName = task.assigneeId ? usersMap[task.assigneeId] || 'Unknown' : null;
  const dueDate = task.dueDate ? new Date(task.dueDate) : null;
  const isOverdue = dueDate && dueDate < new Date() && task.status !== 'Done';

  // Use card template or default configuration
  const template = cardTemplate || {
    fields: {
      title: true,
      description: true,
      assignee: true,
      priority: true,
      dueDate: true,
      tags: true,
      progress: false,
      attachments: false,
      comments: false,
      customFields: []
    },
    layout: 'detailed' as const,
    colorScheme: 'default'
  };

  // Dynamic border color based on priority or color scheme
  const borderColor = template.colorScheme === 'priority' ? {
    'Low': 'border-l-gray-400',
    'Medium': 'border-l-blue-500',
    'High': 'border-l-orange-500',
    'Urgent': 'border-l-red-500',
  }[task.priority] || 'border-l-gray-400' : 'border-l-blue-500';

  // Card size based on layout and screen size
  const cardSize = template.layout === 'compact' ? 'p-2 sm:p-3' :
                   template.layout === 'minimal' ? 'p-1.5 sm:p-2' :
                   'p-3 sm:p-4';

  return (
    <motion.div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...(isMultiSelectMode ? {} : listeners)}
      onClick={handleCardClick}
      className={`
        ${isMultiSelectMode ? 'cursor-pointer' : 'cursor-grab active:cursor-grabbing'}
        touch-manipulation relative group
        ${isDragging ? 'opacity-50 scale-105 rotate-2 z-50' : ''}
        ${isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''}
      `}
      initial={{ opacity: 0, y: 20 }}
      animate={{
        opacity: 1,
        y: 0,
        scale: isSelected ? 1.02 : 1,
        boxShadow: isSelected ? '0 8px 25px rgba(59, 130, 246, 0.15)' : '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}
      exit={{ opacity: 0, y: -20 }}
      transition={{
        duration: 0.2,
        type: "spring",
        stiffness: 300,
        damping: 30
      }}
      whileHover={{
        scale: 1.02,
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        transition: { duration: 0.15 }
      }}
      whileTap={{ scale: 0.98 }}
    >
      {/* Multi-select checkbox */}
      {isMultiSelectMode && (
        <div className="absolute top-2 left-2 z-10">
          <Checkbox
            checked={isSelected}
            onCheckedChange={(checked) => {
              if (onSelect && task.id) {
                onSelect(task.id, !!checked);
              }
            }}
            className="bg-white border-2 shadow-sm"
          />
        </div>
      )}

      <Card className={`
        hover:shadow-md transition-all duration-200 border-l-4 ${borderColor}
        ${isDragging ? 'shadow-lg' : ''}
        ${isSelected ? 'bg-blue-50 border-blue-200' : ''}
        ${isMultiSelectMode ? 'pl-8' : ''}
      `}>
        <CardContent className={cardSize}>
          {/* Task Title */}
          {template.fields.title && (
            <Link
              href={`/tasks/${task.id}`}
              className={`block hover:text-blue-600 transition-colors ${template.layout === 'minimal' ? 'mb-1' : 'mb-2 sm:mb-3'}`}
              onClick={(e) => e.stopPropagation()}
            >
              <h4 className={`font-medium leading-tight line-clamp-2 ${
                template.layout === 'compact' ? 'text-xs sm:text-sm' :
                template.layout === 'minimal' ? 'text-xs' : 'text-sm'
              }`}>
                {task.title}
              </h4>
            </Link>
          )}

          {/* Task Description */}
          {template.fields.description && task.description && template.layout === 'detailed' && (
            <p className="text-xs text-gray-600 mb-2 line-clamp-2">
              {task.description}
            </p>
          )}

          {/* Task Meta */}
          <div className={`space-y-1 ${template.layout === 'minimal' ? 'space-y-0.5' : 'space-y-2'}`}>
            {/* Priority */}
            {template.fields.priority && (
              <Badge
                variant="secondary"
                className={`text-xs ${PRIORITY_COLORS[task.priority]} border-0 ${
                  template.layout === 'minimal' ? 'text-[10px] px-1 py-0' : ''
                }`}
              >
                <Flag className={`mr-1 ${template.layout === 'minimal' ? 'w-2 h-2' : 'w-3 h-3'}`} />
                {template.layout === 'minimal' ? task.priority.charAt(0) : task.priority}
              </Badge>
            )}

            {/* Due Date */}
            {template.fields.dueDate && dueDate && (
              <div className={`flex items-center ${
                template.layout === 'minimal' ? 'text-[10px]' : 'text-xs'
              } ${isOverdue ? 'text-red-600' : 'text-gray-600'}`}>
                <Calendar className={`mr-1 ${template.layout === 'minimal' ? 'w-2 h-2' : 'w-3 h-3'}`} />
                {format(dueDate, template.layout === 'minimal' ? 'M/d' : 'MMM d')}
                {isOverdue && template.layout !== 'minimal' && <span className="ml-1 font-medium">(Overdue)</span>}
              </div>
            )}

            {/* Assignee */}
            {template.fields.assignee && assigneeName && (
              <div className={`flex items-center ${
                template.layout === 'minimal' ? 'text-[10px]' : 'text-xs'
              } text-gray-600`}>
                <User className={`mr-1 ${template.layout === 'minimal' ? 'w-2 h-2' : 'w-3 h-3'}`} />
                <span className="truncate">
                  {template.layout === 'minimal' ? assigneeName.split(' ')[0] : assigneeName}
                </span>
              </div>
            )}

            {/* Tags */}
            {template.fields.tags && task.tags && task.tags.length > 0 && template.layout === 'detailed' && (
              <div className="flex flex-wrap gap-1 mt-2">
                {task.tags.slice(0, 2).map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-[10px] px-1 py-0">
                    {tag}
                  </Badge>
                ))}
                {task.tags.length > 2 && (
                  <Badge variant="outline" className="text-[10px] px-1 py-0">
                    +{task.tags.length - 2}
                  </Badge>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

interface ColumnProps {
  column: TaskBoardColumn;
  tasks: Task[];
  usersMap: Record<string, string>;
  cardTemplate?: TaskCardTemplate;
  showWipLimits?: boolean;
  onWipLimitChange?: (columnId: string, newLimit: number | undefined) => void;
  selectedTasks?: Set<string>;
  isMultiSelectMode?: boolean;
  onTaskSelect?: (taskId: string, selected: boolean) => void;
  onMultiSelectToggle?: () => void;
}

function Column({
  column,
  tasks,
  usersMap,
  cardTemplate,
  showWipLimits,
  onWipLimitChange,
  selectedTasks = new Set(),
  isMultiSelectMode = false,
  onTaskSelect,
  onMultiSelectToggle
}: ColumnProps) {
  const { setNodeRef, isOver } = useDroppable({
    id: column.status || column.id,
  });

  // Get icon from status or use default
  const statusConfig = COLUMNS.find(c => c.status === column.status);
  const Icon = statusConfig?.icon || Circle;
  const taskIds = tasks.map(task => task.id || 'no-id');

  // WIP limit checking
  const isOverWipLimit = column.wipLimit && tasks.length > column.wipLimit;
  const wipLimitColor = isOverWipLimit ? 'text-red-600' : 'text-orange-600';

  const handleWipLimitChange = (increment: boolean) => {
    if (!onWipLimitChange) return;
    const currentLimit = column.wipLimit || 0;
    const newLimit = increment ? currentLimit + 1 : Math.max(0, currentLimit - 1);
    onWipLimitChange(column.id, newLimit === 0 ? undefined : newLimit);
  };

  return (
    <div
      ref={setNodeRef}
      className={`flex flex-col h-full transition-all duration-200 ${
        isOver ? 'scale-105' : ''
      }`}
    >
      <Card className={`group flex flex-col h-full ${
        statusConfig?.color || 'bg-gray-50 border-gray-200'
      } ${isOver ? 'ring-2 ring-blue-400 ring-offset-2 shadow-lg' : ''} ${
        isOverWipLimit ? 'ring-2 ring-red-400' : ''
      }`} style={{ backgroundColor: column.color + '20', borderColor: column.color }}>
        {/* Column Header */}
        <CardHeader className="pb-2 sm:pb-3">
          <CardTitle className="flex items-center justify-between text-xs sm:text-sm">
            <div className="flex items-center gap-1 sm:gap-2">
              <Icon className="w-3 h-3 sm:w-4 sm:h-4" style={{ color: column.color }} />
              <span className="truncate">{column.name}</span>
            </div>
            <div className="flex items-center gap-1 sm:gap-2">
              <Badge variant="outline" className={`text-[10px] sm:text-xs ${isOverWipLimit ? 'border-red-400 text-red-600' : ''}`}>
                {tasks.length}
                {showWipLimits && column.wipLimit && (
                  <span className={wipLimitColor}>/{column.wipLimit}</span>
                )}
              </Badge>
              {isOverWipLimit && (
                <Badge variant="destructive" className="text-[10px] sm:text-xs px-1">
                  WIP!
                </Badge>
              )}
              {/* WIP Limit Controls */}
              {showWipLimits && onWipLimitChange && (
                <div className="flex items-center gap-0.5 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 w-5 p-0"
                    onClick={() => handleWipLimitChange(false)}
                  >
                    <Minus className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 w-5 p-0"
                    onClick={() => handleWipLimitChange(true)}
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
              )}
            </div>
          </CardTitle>
        </CardHeader>

        {/* Column Content */}
        <CardContent className="flex-1 pt-0">
          <SortableContext items={taskIds} strategy={verticalListSortingStrategy}>
            <div className="space-y-2 sm:space-y-3 min-h-[300px] sm:min-h-[400px]">
              {tasks.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-6 sm:py-8 text-gray-400">
                  <Icon className="w-6 h-6 sm:w-8 sm:h-8 mb-2" />
                  <p className="text-xs sm:text-sm">No tasks</p>
                  {isOver && <p className="text-xs mt-1">Drop here</p>}
                </div>
              ) : (
                <AnimatePresence>
                  {tasks.map((task) => (
                    <TaskCard
                      key={task.id || 'no-id'}
                      task={task}
                      usersMap={usersMap}
                      cardTemplate={cardTemplate}
                      isSelected={selectedTasks.has(task.id || '')}
                      isMultiSelectMode={isMultiSelectMode}
                      onSelect={onTaskSelect}
                      onMultiSelectToggle={onMultiSelectToggle}
                    />
                  ))}
                </AnimatePresence>
              )}
            </div>
          </SortableContext>
        </CardContent>
      </Card>
    </div>
  );
}

export default function NewKanbanBoard({
  tasks,
  usersMap,
  onTaskStatusChange,
  onBatchTaskStatusChange,
  layout,
  onLayoutChange
}: NewKanbanBoardProps) {
  const { toast } = useToast();

  // Enhanced drag state
  const [dragState, setDragState] = useState<DragState>({
    activeId: null,
    draggedTask: null,
    selectedTasks: new Set(),
    isDraggingMultiple: false,
    dragStartPosition: null
  });

  // Multi-select mode
  const [isMultiSelectMode, setIsMultiSelectMode] = useState(false);

  // Enhanced sensors with better touch support
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 150,
        tolerance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Use layout columns or default columns
  const boardColumns = layout?.columns.filter(col => col.visible).sort((a, b) => a.position - b.position) ||
    COLUMNS.map(col => ({
      id: col.status.toLowerCase().replace(' ', '-'),
      name: col.label,
      status: col.status,
      color: col.status === 'To Do' ? '#64748b' :
             col.status === 'In Progress' ? '#3b82f6' :
             col.status === 'In Review' ? '#f59e0b' :
             col.status === 'Awaiting Approval' ? '#f97316' : '#10b981',
      position: COLUMNS.findIndex(c => c.status === col.status),
      visible: true,
      isCustom: false
    }));

  // Multi-select helper functions
  const handleTaskSelect = useCallback((taskId: string, selected: boolean) => {
    setDragState(prev => {
      const newSelected = new Set(prev.selectedTasks);
      if (selected) {
        newSelected.add(taskId);
      } else {
        newSelected.delete(taskId);
      }
      return { ...prev, selectedTasks: newSelected };
    });
  }, []);

  const handleSelectAll = useCallback(() => {
    const allTaskIds = tasks.map(t => t.id).filter(Boolean) as string[];
    setDragState(prev => ({ ...prev, selectedTasks: new Set(allTaskIds) }));
  }, [tasks]);

  const handleDeselectAll = useCallback(() => {
    setDragState(prev => ({ ...prev, selectedTasks: new Set() }));
  }, []);

  const toggleMultiSelectMode = useCallback(() => {
    setIsMultiSelectMode(prev => !prev);
    if (isMultiSelectMode) {
      handleDeselectAll();
    }
  }, [isMultiSelectMode, handleDeselectAll]);

  // Group tasks by status
  const tasksByColumn = boardColumns.reduce((acc, column) => {
    const columnTasks = tasks.filter(task => task.status === column.status);
    acc[column.id] = columnTasks;
    return acc;
  }, {} as Record<string, Task[]>);

  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    const taskId = active.id as string;
    const task = tasks.find(t => t.id === taskId);

    if (!task) return;

    const isSelectedTask = dragState.selectedTasks.has(taskId);
    const isDraggingMultiple = isSelectedTask && dragState.selectedTasks.size > 1;

    setDragState(prev => ({
      ...prev,
      activeId: taskId,
      draggedTask: task,
      isDraggingMultiple,
      dragStartPosition: { x: event.activatorEvent.clientX, y: event.activatorEvent.clientY }
    }));

    // Add haptic feedback for mobile
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  }, [tasks, dragState.selectedTasks]);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    // Reset drag state
    setDragState(prev => ({
      ...prev,
      activeId: null,
      draggedTask: null,
      isDraggingMultiple: false,
      dragStartPosition: null
    }));

    if (!over || !active.id) return;

    const taskId = active.id as string;
    const targetColumn = boardColumns.find(col => col.id === over.id || col.status === over.id);

    if (!targetColumn?.status) return;

    const task = tasks.find(t => t.id === taskId);
    if (!task) return;

    // Handle batch operations for multiple selected tasks
    if (dragState.isDraggingMultiple && onBatchTaskStatusChange) {
      const selectedTaskIds = Array.from(dragState.selectedTasks);
      const tasksToMove = selectedTaskIds.filter(id => {
        const t = tasks.find(task => task.id === id);
        return t && t.status !== targetColumn.status;
      });

      if (tasksToMove.length > 0) {
        onBatchTaskStatusChange(tasksToMove, targetColumn.status);
        toast({
          title: "Tasks moved",
          description: `${tasksToMove.length} tasks moved to ${targetColumn.name}`,
        });

        // Clear selection after batch move
        setDragState(prev => ({ ...prev, selectedTasks: new Set() }));
      }
    } else if (task.status !== targetColumn.status) {
      // Single task move
      onTaskStatusChange(taskId, targetColumn.status);
    }
  }, [tasks, boardColumns, dragState.isDraggingMultiple, dragState.selectedTasks, onTaskStatusChange, onBatchTaskStatusChange, toast]);

  const handleWipLimitChange = (columnId: string, newLimit: number | undefined) => {
    if (!layout || !onLayoutChange) return;

    const updatedLayout = {
      ...layout,
      columns: layout.columns.map(col =>
        col.id === columnId ? { ...col, wipLimit: newLimit } : col
      ),
      updatedAt: new Date()
    };

    onLayoutChange(updatedLayout);
  };

  return (
    <div className="p-3 sm:p-6">
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        {/* Board Header */}
        <div className="mb-4 sm:mb-6">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-lg sm:text-xl font-semibold">
              {layout?.name || 'Task Board'}
            </h2>

            {/* Multi-select toggle */}
            <div className="flex items-center gap-2">
              <Button
                variant={isMultiSelectMode ? "default" : "outline"}
                size="sm"
                onClick={toggleMultiSelectMode}
                className="gap-2"
              >
                <MousePointer2 className="h-4 w-4" />
                {isMultiSelectMode ? 'Exit Multi-Select' : 'Multi-Select'}
              </Button>
            </div>
          </div>

          <p className="text-gray-600 text-xs sm:text-sm">
            <span className="hidden sm:inline">
              {layout?.description || 'Drag tasks between columns to update their status'} •
            </span>
            {tasks.length} total tasks
            {dragState.selectedTasks.size > 0 && (
              <span className="ml-2 text-blue-600 font-medium">
                • {dragState.selectedTasks.size} selected
              </span>
            )}
          </p>
        </div>

        {/* Multi-select toolbar */}
        <AnimatePresence>
          {isMultiSelectMode && dragState.selectedTasks.size > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Layers className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">
                    {dragState.selectedTasks.size} tasks selected
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSelectAll}
                    className="text-xs"
                  >
                    Select All
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDeselectAll}
                    className="text-xs"
                  >
                    Clear
                  </Button>
                </div>
              </div>

              <p className="text-xs text-blue-700 mt-1">
                Drag any selected task to move all {dragState.selectedTasks.size} tasks to a new column
              </p>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Kanban Columns */}
        {boardColumns.length > 3 ? (
          // Horizontal scroll for many columns on mobile
          <div className="overflow-x-auto pb-4">
            <div className="flex gap-2 sm:gap-4 min-w-max">
              {boardColumns.map(column => (
                <div key={column.id} className="w-72 sm:w-80 flex-shrink-0">
                  <Column
                    column={column}
                    tasks={tasksByColumn[column.id] || []}
                    usersMap={usersMap}
                    cardTemplate={layout?.cardTemplate}
                    showWipLimits={layout?.viewSettings.showWipLimits}
                    onWipLimitChange={handleWipLimitChange}
                    selectedTasks={dragState.selectedTasks}
                    isMultiSelectMode={isMultiSelectMode}
                    onTaskSelect={handleTaskSelect}
                    onMultiSelectToggle={toggleMultiSelectMode}
                  />
                </div>
              ))}
            </div>
          </div>
        ) : (
          // Grid layout for fewer columns
          <div className={`
            grid gap-2 sm:gap-4
            ${boardColumns.length <= 2 ? 'grid-cols-1 sm:grid-cols-2' :
              'grid-cols-1 sm:grid-cols-2 md:grid-cols-3'}
          `}>
            {boardColumns.map(column => (
              <Column
                key={column.id}
                column={column}
                tasks={tasksByColumn[column.id] || []}
                usersMap={usersMap}
                cardTemplate={layout?.cardTemplate}
                showWipLimits={layout?.viewSettings.showWipLimits}
                onWipLimitChange={handleWipLimitChange}
                selectedTasks={dragState.selectedTasks}
                isMultiSelectMode={isMultiSelectMode}
                onTaskSelect={handleTaskSelect}
                onMultiSelectToggle={toggleMultiSelectMode}
              />
            ))}
          </div>
        )}

        {/* Enhanced Drag Overlay */}
        <DragOverlay>
          {dragState.activeId && dragState.draggedTask ? (
            <motion.div
              initial={{ scale: 1, rotate: 0 }}
              animate={{
                scale: 1.05,
                rotate: dragState.isDraggingMultiple ? 2 : 3,
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)'
              }}
              className="relative"
            >
              {/* Main dragged task */}
              <div className="relative z-10">
                <TaskCard
                  task={dragState.draggedTask}
                  usersMap={usersMap}
                  cardTemplate={layout?.cardTemplate}
                />
              </div>

              {/* Multiple task indicator */}
              {dragState.isDraggingMultiple && (
                <>
                  {/* Shadow cards to show multiple selection */}
                  <div className="absolute top-1 left-1 z-0 opacity-60">
                    <TaskCard
                      task={dragState.draggedTask}
                      usersMap={usersMap}
                      cardTemplate={layout?.cardTemplate}
                    />
                  </div>
                  <div className="absolute top-2 left-2 z-0 opacity-30">
                    <TaskCard
                      task={dragState.draggedTask}
                      usersMap={usersMap}
                      cardTemplate={layout?.cardTemplate}
                    />
                  </div>

                  {/* Count badge */}
                  <div className="absolute -top-2 -right-2 z-20 bg-blue-600 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center shadow-lg">
                    {dragState.selectedTasks.size}
                  </div>
                </>
              )}
            </motion.div>
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
}
