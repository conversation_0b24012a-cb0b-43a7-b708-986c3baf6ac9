"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { 
  FileTemplate,
  Copy,
  Edit2,
  Trash2,
  Plus,
  Download,
  Upload,
  Eye,
  Play,
  Star,
  Clock,
  Users,
  Target,
  Folder
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { formatDistanceToNow } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

interface TaskTemplate {
  id?: string;
  name: string;
  description?: string;
  category: string;
  tags?: string[];
  tasks: {
    title: string;
    description?: string;
    estimatedDuration?: number; // in hours
    priority: 'Low' | 'Medium' | 'High' | 'Urgent';
    assignedToRole?: string;
    dependsOn?: number[]; // indices of other tasks in template
  }[];
  defaultAssigneeRole?: string;
  estimatedTotalDuration?: number; // in hours
  usageCount?: number;
  isPublic?: boolean;
  isActive?: boolean;
  createdBy?: string;
  createdByName?: string;
  createdAt?: Timestamp | Date | string;
  updatedAt?: Timestamp | Date | string;
}

const categoryConfig = {
  'exhibition': { color: 'bg-blue-100 text-blue-800', icon: Target },
  'event': { color: 'bg-green-100 text-green-800', icon: Calendar },
  'marketing': { color: 'bg-purple-100 text-purple-800', icon: Megaphone },
  'logistics': { color: 'bg-orange-100 text-orange-800', icon: Truck },
  'setup': { color: 'bg-yellow-100 text-yellow-800', icon: Settings },
  'teardown': { color: 'bg-red-100 text-red-800', icon: Package },
  'general': { color: 'bg-gray-100 text-gray-800', icon: Folder },
};

const priorityConfig = {
  'Low': { color: 'bg-gray-100 text-gray-800' },
  'Medium': { color: 'bg-yellow-100 text-yellow-800' },
  'High': { color: 'bg-orange-100 text-orange-800' },
  'Urgent': { color: 'bg-red-100 text-red-800' },
};

interface TaskTemplatesTableProps {
  category?: string;
  showCategoryFilter?: boolean;
  onCreateFromTemplate?: (template: TaskTemplate) => void;
  onEditTemplate?: (template: TaskTemplate) => void;
}

export default function TaskTemplatesTable({ 
  category,
  showCategoryFilter = true,
  onCreateFromTemplate,
  onEditTemplate
}: TaskTemplatesTableProps) {
  const { toast } = useToast();
  const [templates, setTemplates] = useState<TaskTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load task templates
  useEffect(() => {
    loadTemplates();
  }, [category]);

  const loadTemplates = async () => {
    setIsLoading(true);
    try {
      let templatesQuery = query(
        collection(db, 'task_templates'),
        orderBy('usageCount', 'desc')
      );

      const templatesSnapshot = await getDocs(templatesQuery);
      let fetchedTemplates = templatesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(doc.data().createdAt),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date(doc.data().updatedAt)
      } as TaskTemplate));

      // Filter by category if specified
      if (category) {
        fetchedTemplates = fetchedTemplates.filter(template => template.category === category);
      }

      // Filter only active templates
      fetchedTemplates = fetchedTemplates.filter(template => template.isActive !== false);

      setTemplates(fetchedTemplates);
    } catch (error) {
      console.error('Error loading task templates:', error);
      toast({
        title: "Error",
        description: "Failed to load task templates.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const handleCreateFromTemplate = (template: TaskTemplate) => {
    if (onCreateFromTemplate) {
      onCreateFromTemplate(template);
    } else {
      console.log('Create tasks from template:', template);
      toast({
        title: "Feature Coming Soon",
        description: "Task creation from templates will be available soon."
      });
    }
  };

  const handleEdit = (template: TaskTemplate) => {
    if (onEditTemplate) {
      onEditTemplate(template);
    } else {
      console.log('Edit template:', template);
      toast({
        title: "Feature Coming Soon",
        description: "Template editing will be available soon."
      });
    }
  };

  const handleDelete = async (template: TaskTemplate) => {
    if (!confirm(`Are you sure you want to delete "${template.name}"?`)) return;
    
    try {
      // Delete template logic would go here
      console.log('Delete template:', template);
      
      await loadTemplates();
      toast({
        title: "Success",
        description: "Task template deleted successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete task template.",
        variant: "destructive"
      });
    }
  };

  const handleDuplicate = async (template: TaskTemplate) => {
    try {
      // Duplicate template logic would go here
      console.log('Duplicate template:', template);
      
      await loadTemplates();
      toast({
        title: "Success",
        description: "Task template duplicated successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate task template.",
        variant: "destructive"
      });
    }
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const formatDuration = (hours: number) => {
    if (hours < 1) {
      return `${Math.round(hours * 60)}m`;
    } else if (hours < 24) {
      return `${hours}h`;
    } else {
      const days = Math.floor(hours / 24);
      const remainingHours = hours % 24;
      return `${days}d ${remainingHours}h`;
    }
  };

  const columns: ColumnDef<TaskTemplate>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: "Template Name",
      cell: ({ row }) => {
        const template = row.original;
        
        return (
          <div className="flex items-center gap-3">
            <FileTemplate className="h-5 w-5 text-blue-500" />
            <div>
              <div className="font-medium">{template.name}</div>
              <div className="text-sm text-gray-500 line-clamp-1">
                {template.description || 'No description'}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: ({ row }) => {
        const category = row.original.category;
        const config = categoryConfig[category] || categoryConfig.general;
        const CategoryIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <CategoryIcon className="h-3 w-3" />
            {category}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
      enableHiding: !!category, // Hide if filtering by specific category
    },
    {
      id: "tasks",
      header: "Tasks",
      cell: ({ row }) => {
        const template = row.original;
        const taskCount = template.tasks.length;
        const priorityCounts = template.tasks.reduce((acc, task) => {
          acc[task.priority] = (acc[task.priority] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <Target className="h-3 w-3 text-gray-400" />
              <span className="font-medium">{taskCount} tasks</span>
            </div>
            <div className="flex flex-wrap gap-1">
              {Object.entries(priorityCounts).map(([priority, count]) => {
                const config = priorityConfig[priority];
                return (
                  <Badge key={priority} className={`${config.color} text-xs`}>
                    {count} {priority}
                  </Badge>
                );
              })}
            </div>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "duration",
      header: "Est. Duration",
      cell: ({ row }) => {
        const duration = row.original.estimatedTotalDuration;
        
        if (!duration) {
          return <span className="text-gray-400">Not set</span>;
        }
        
        return (
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-gray-400" />
            <span className="font-mono">{formatDuration(duration)}</span>
          </div>
        );
      },
      sortingFn: (rowA, rowB) => {
        const durationA = rowA.original.estimatedTotalDuration || 0;
        const durationB = rowB.original.estimatedTotalDuration || 0;
        return durationA - durationB;
      },
    },
    {
      id: "usage",
      header: "Usage",
      cell: ({ row }) => {
        const usageCount = row.original.usageCount || 0;
        
        return (
          <div className="flex items-center gap-1">
            <Copy className="h-3 w-3 text-gray-400" />
            <span className="font-medium">{usageCount}</span>
            <span className="text-xs text-gray-500">times</span>
          </div>
        );
      },
    },
    {
      id: "visibility",
      header: "Visibility",
      cell: ({ row }) => {
        const isPublic = row.original.isPublic;
        
        return (
          <Badge variant={isPublic ? "default" : "secondary"}>
            {isPublic ? "Public" : "Private"}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        const isPublic = row.original.isPublic;
        return value.includes(isPublic ? 'public' : 'private');
      },
    },
    {
      accessorKey: "createdByName",
      header: "Created By",
      cell: ({ row }) => {
        const createdBy = row.original.createdByName || row.original.createdBy || 'Unknown';
        
        return (
          <div className="flex items-center gap-2">
            <Users className="h-3 w-3 text-gray-400" />
            <span className="text-sm">{createdBy}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "updatedAt",
      header: "Last Updated",
      cell: ({ row }) => {
        const updatedAt = row.original.updatedAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(updatedAt)}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const template = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleCreateFromTemplate(template)}
              className="text-green-600 hover:text-green-700"
            >
              <Play className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDuplicate(template)}
            >
              <Copy className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEdit(template)}
            >
              <Edit2 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDelete(template)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], [category]);

  const tableActions = [
    {
      label: "Create Template",
      icon: Plus,
      onClick: () => {
        console.log('Create new template');
        toast({
          title: "Feature Coming Soon",
          description: "Template creation will be available soon."
        });
      },
      variant: "default" as const,
    },
    {
      label: "Create Tasks from Selected",
      icon: Play,
      onClick: (selectedRows: TaskTemplate[]) => {
        if (selectedRows.length === 1) {
          handleCreateFromTemplate(selectedRows[0]);
        } else {
          console.log('Create tasks from multiple templates:', selectedRows);
          toast({
            title: "Feature Coming Soon",
            description: "Bulk task creation will be available soon."
          });
        }
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Export Selected",
      icon: Download,
      onClick: (selectedRows: TaskTemplate[]) => {
        console.log('Export templates:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Template export will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Import Templates",
      icon: Upload,
      onClick: () => {
        console.log('Import templates');
        toast({
          title: "Feature Coming Soon",
          description: "Template import will be available soon."
        });
      },
      variant: "outline" as const,
    },
    {
      label: "Delete Selected",
      icon: Trash2,
      onClick: async (selectedRows: TaskTemplate[]) => {
        if (confirm(`Delete ${selectedRows.length} selected templates?`)) {
          try {
            await Promise.all(selectedRows.map(template => handleDelete(template)));
            toast({
              title: "Success",
              description: `${selectedRows.length} templates deleted successfully.`
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to delete selected templates.",
              variant: "destructive"
            });
          }
        }
      },
      variant: "destructive" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    {
      key: 'category',
      label: 'Category',
      options: [
        { label: 'Exhibition', value: 'exhibition' },
        { label: 'Event', value: 'event' },
        { label: 'Marketing', value: 'marketing' },
        { label: 'Logistics', value: 'logistics' },
        { label: 'Setup', value: 'setup' },
        { label: 'Teardown', value: 'teardown' },
        { label: 'General', value: 'general' },
      ]
    },
    {
      key: 'visibility',
      label: 'Visibility',
      options: [
        { label: 'Public', value: 'public' },
        { label: 'Private', value: 'private' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={templates}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadTemplates}
      tableActions={tableActions}
      filterOptions={showCategoryFilter ? filterOptions : filterOptions.slice(1)}
      searchPlaceholder="Search templates by name, description, category..."
      emptyStateMessage="No task templates found"
      emptyStateDescription="Create reusable task templates to streamline your workflow."
    />
  );
}
