"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Search, 
  Tag, 
  User, 
  Calendar, 
  Flag, 
  Target,
  Clock,
  Sparkles,
  TrendingUp,
  History,
  Zap
} from 'lucide-react';
import type { Task } from '@/types/firestore';

interface SearchSuggestion {
  type: 'text' | 'tag' | 'assignee' | 'priority' | 'status' | 'recent' | 'popular';
  value: string;
  display: string;
  icon: React.ReactNode;
  count?: number;
  description?: string;
}

interface SmartSearchSuggestionsProps {
  query: string;
  tasks: Task[];
  usersMap: Record<string, string>;
  recentSearches: string[];
  onSuggestionSelect: (suggestion: string) => void;
  onClose: () => void;
  className?: string;
}

export default function SmartSearchSuggestions({
  query,
  tasks,
  usersMap,
  recentSearches,
  onSuggestionSelect,
  onClose,
  className
}: SmartSearchSuggestionsProps) {
  const [selectedIndex, setSelectedIndex] = useState(0);

  // Generate smart suggestions based on query and task data
  const suggestions = useMemo(() => {
    const lowerQuery = query.toLowerCase().trim();
    const suggestions: SearchSuggestion[] = [];

    if (!lowerQuery) {
      // Show recent searches when no query
      recentSearches.slice(0, 5).forEach(search => {
        suggestions.push({
          type: 'recent',
          value: search,
          display: search,
          icon: <History className="h-4 w-4" />,
          description: 'Recent search'
        });
      });

      // Show popular/trending searches
      const popularTags = getPopularTags(tasks).slice(0, 3);
      popularTags.forEach(({ tag, count }) => {
        suggestions.push({
          type: 'popular',
          value: `tag:${tag}`,
          display: `tag:${tag}`,
          icon: <TrendingUp className="h-4 w-4" />,
          count,
          description: 'Popular tag'
        });
      });

      return suggestions;
    }

    // Text-based suggestions
    const matchingTasks = tasks.filter(task => 
      task.title.toLowerCase().includes(lowerQuery) ||
      task.description?.toLowerCase().includes(lowerQuery)
    );

    matchingTasks.slice(0, 5).forEach(task => {
      suggestions.push({
        type: 'text',
        value: task.title,
        display: task.title,
        icon: <Search className="h-4 w-4" />,
        description: `Task: ${task.status}`
      });
    });

    // Tag suggestions
    const matchingTags = getMatchingTags(tasks, lowerQuery);
    matchingTags.slice(0, 5).forEach(({ tag, count }) => {
      suggestions.push({
        type: 'tag',
        value: `tag:${tag}`,
        display: `tag:${tag}`,
        icon: <Tag className="h-4 w-4" />,
        count,
        description: `${count} tasks with this tag`
      });
    });

    // Assignee suggestions
    const matchingAssignees = getMatchingAssignees(usersMap, lowerQuery);
    matchingAssignees.slice(0, 3).forEach(({ id, name, count }) => {
      suggestions.push({
        type: 'assignee',
        value: `assignee:${name}`,
        display: `assignee:${name}`,
        icon: <User className="h-4 w-4" />,
        count,
        description: `${count} tasks assigned`
      });
    });

    // Priority suggestions
    if ('urgent'.includes(lowerQuery) || 'high'.includes(lowerQuery) || 
        'medium'.includes(lowerQuery) || 'low'.includes(lowerQuery)) {
      const priorities = ['Urgent', 'High', 'Medium', 'Low'];
      priorities.forEach(priority => {
        if (priority.toLowerCase().includes(lowerQuery)) {
          const count = tasks.filter(t => t.priority === priority).length;
          suggestions.push({
            type: 'priority',
            value: `priority:${priority}`,
            display: `priority:${priority}`,
            icon: <Flag className="h-4 w-4" />,
            count,
            description: `${count} ${priority.toLowerCase()} priority tasks`
          });
        }
      });
    }

    // Status suggestions
    const statuses = ['To Do', 'In Progress', 'Awaiting Approval', 'Done'];
    statuses.forEach(status => {
      if (status.toLowerCase().includes(lowerQuery)) {
        const count = tasks.filter(t => t.status === status).length;
        suggestions.push({
          type: 'status',
          value: `status:"${status}"`,
          display: `status:${status}`,
          icon: <Target className="h-4 w-4" />,
          count,
          description: `${count} tasks in ${status.toLowerCase()}`
        });
      }
    });

    // Date-based suggestions
    if (['today', 'week', 'month', 'overdue'].some(term => term.includes(lowerQuery))) {
      const dateFilters = [
        { term: 'today', display: 'due:today', description: 'Tasks due today' },
        { term: 'week', display: 'due:week', description: 'Tasks due this week' },
        { term: 'month', display: 'due:month', description: 'Tasks due this month' },
        { term: 'overdue', display: 'due:overdue', description: 'Overdue tasks' }
      ];

      dateFilters.forEach(({ term, display, description }) => {
        if (term.includes(lowerQuery)) {
          suggestions.push({
            type: 'status',
            value: display,
            display,
            icon: <Calendar className="h-4 w-4" />,
            description
          });
        }
      });
    }

    return suggestions.slice(0, 12); // Limit total suggestions
  }, [query, tasks, usersMap, recentSearches]);

  // Helper functions
  function getPopularTags(tasks: Task[]) {
    const tagCounts: Record<string, number> = {};
    tasks.forEach(task => {
      task.tags?.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
    });
    
    return Object.entries(tagCounts)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count);
  }

  function getMatchingTags(tasks: Task[], query: string) {
    const tagCounts: Record<string, number> = {};
    tasks.forEach(task => {
      task.tags?.forEach(tag => {
        if (tag.toLowerCase().includes(query)) {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1;
        }
      });
    });
    
    return Object.entries(tagCounts)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count);
  }

  function getMatchingAssignees(usersMap: Record<string, string>, query: string) {
    const assigneeCounts: Record<string, { name: string; count: number }> = {};
    
    Object.entries(usersMap).forEach(([id, name]) => {
      if (name.toLowerCase().includes(query)) {
        const count = tasks.filter(t => t.assigneeId === id).length;
        assigneeCounts[id] = { name, count };
      }
    });
    
    return Object.entries(assigneeCounts)
      .map(([id, { name, count }]) => ({ id, name, count }))
      .sort((a, b) => b.count - a.count);
  }

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedIndex(prev => Math.min(prev + 1, suggestions.length - 1));
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedIndex(prev => Math.max(prev - 1, 0));
      } else if (e.key === 'Enter') {
        e.preventDefault();
        if (suggestions[selectedIndex]) {
          onSuggestionSelect(suggestions[selectedIndex].value);
        }
      } else if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [selectedIndex, suggestions, onSuggestionSelect, onClose]);

  // Reset selected index when suggestions change
  useEffect(() => {
    setSelectedIndex(0);
  }, [suggestions]);

  if (suggestions.length === 0) {
    return null;
  }

  // Group suggestions by type
  const groupedSuggestions = suggestions.reduce((groups, suggestion) => {
    const type = suggestion.type;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(suggestion);
    return groups;
  }, {} as Record<string, SearchSuggestion[]>);

  const typeLabels = {
    recent: 'Recent Searches',
    popular: 'Popular',
    text: 'Tasks',
    tag: 'Tags',
    assignee: 'Assignees',
    priority: 'Priority',
    status: 'Status'
  };

  const typeIcons = {
    recent: <History className="h-4 w-4" />,
    popular: <TrendingUp className="h-4 w-4" />,
    text: <Search className="h-4 w-4" />,
    tag: <Tag className="h-4 w-4" />,
    assignee: <User className="h-4 w-4" />,
    priority: <Flag className="h-4 w-4" />,
    status: <Target className="h-4 w-4" />
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className={`absolute top-full left-0 right-0 z-50 mt-1 bg-background border rounded-lg shadow-lg max-h-96 overflow-y-auto ${className}`}
    >
      <div className="p-2">
        {Object.entries(groupedSuggestions).map(([type, typeSuggestions], groupIndex) => (
          <div key={type}>
            {groupIndex > 0 && <Separator className="my-2" />}
            
            {/* Group Header */}
            <div className="flex items-center gap-2 px-2 py-1 text-xs font-medium text-muted-foreground">
              {typeIcons[type as keyof typeof typeIcons]}
              {typeLabels[type as keyof typeof typeLabels]}
            </div>
            
            {/* Suggestions */}
            <div className="space-y-1">
              {typeSuggestions.map((suggestion, index) => {
                const globalIndex = suggestions.indexOf(suggestion);
                return (
                  <motion.button
                    key={`${suggestion.type}-${suggestion.value}`}
                    className={`w-full text-left px-3 py-2 rounded-md transition-colors flex items-center justify-between group ${
                      selectedIndex === globalIndex 
                        ? 'bg-primary text-primary-foreground' 
                        : 'hover:bg-muted'
                    }`}
                    onClick={() => onSuggestionSelect(suggestion.value)}
                    whileHover={{ scale: 1.01 }}
                    whileTap={{ scale: 0.99 }}
                  >
                    <div className="flex items-center gap-3 min-w-0 flex-1">
                      <div className={`flex-shrink-0 ${
                        selectedIndex === globalIndex ? 'text-primary-foreground' : 'text-muted-foreground'
                      }`}>
                        {suggestion.icon}
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="font-medium truncate">
                          {suggestion.display}
                        </div>
                        {suggestion.description && (
                          <div className={`text-xs truncate ${
                            selectedIndex === globalIndex 
                              ? 'text-primary-foreground/70' 
                              : 'text-muted-foreground'
                          }`}>
                            {suggestion.description}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {suggestion.count !== undefined && (
                      <Badge 
                        variant={selectedIndex === globalIndex ? "secondary" : "outline"}
                        className="ml-2 text-xs"
                      >
                        {suggestion.count}
                      </Badge>
                    )}
                  </motion.button>
                );
              })}
            </div>
          </div>
        ))}
        
        {/* Search Tips */}
        <Separator className="my-2" />
        <div className="px-2 py-1">
          <div className="flex items-center gap-2 text-xs text-muted-foreground mb-1">
            <Sparkles className="h-3 w-3" />
            Search Tips
          </div>
          <div className="text-xs text-muted-foreground space-y-1">
            <div>• Use <code className="bg-muted px-1 rounded">tag:urgent</code> to search by tags</div>
            <div>• Use <code className="bg-muted px-1 rounded">assignee:john</code> to filter by assignee</div>
            <div>• Use <code className="bg-muted px-1 rounded">due:today</code> for date filters</div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
