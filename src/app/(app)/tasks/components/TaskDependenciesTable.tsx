"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { 
  GitMerge,
  ArrowRight,
  ArrowLeft,
  Plus,
  Trash2,
  Eye,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Link as LinkIcon,
  Unlink,
  Download
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { Task } from '@/types/firestore';
import { getDependentTasks } from '@/services/firestoreService';
import { formatDistanceToNow } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import Link from 'next/link';

interface TaskDependency {
  id?: string;
  taskId: string;
  dependsOnTaskId: string;
  dependencyType: 'finish-to-start' | 'start-to-start' | 'finish-to-finish' | 'start-to-finish';
  lag?: number; // in days
  isBlocking: boolean;
  createdAt?: Timestamp | Date | string;
  updatedAt?: Timestamp | Date | string;
  
  // Denormalized data for performance
  taskTitle?: string;
  dependsOnTaskTitle?: string;
  taskStatus?: string;
  dependsOnTaskStatus?: string;
  taskPriority?: string;
  dependsOnTaskPriority?: string;
}

const dependencyTypeConfig = {
  'finish-to-start': { 
    color: 'bg-blue-100 text-blue-800', 
    label: 'Finish → Start',
    description: 'Task must finish before dependent task can start'
  },
  'start-to-start': { 
    color: 'bg-green-100 text-green-800', 
    label: 'Start → Start',
    description: 'Tasks must start at the same time'
  },
  'finish-to-finish': { 
    color: 'bg-purple-100 text-purple-800', 
    label: 'Finish → Finish',
    description: 'Tasks must finish at the same time'
  },
  'start-to-finish': { 
    color: 'bg-orange-100 text-orange-800', 
    label: 'Start → Finish',
    description: 'Task must start before dependent task can finish'
  },
};

const statusConfig = {
  'Not Started': { color: 'bg-gray-100 text-gray-800', icon: Clock },
  'In Progress': { color: 'bg-blue-100 text-blue-800', icon: Clock },
  'Completed': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
  'On Hold': { color: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle },
  'Cancelled': { color: 'bg-red-100 text-red-800', icon: AlertTriangle },
};

const priorityConfig = {
  'Low': { color: 'bg-gray-100 text-gray-800' },
  'Medium': { color: 'bg-yellow-100 text-yellow-800' },
  'High': { color: 'bg-orange-100 text-orange-800' },
  'Urgent': { color: 'bg-red-100 text-red-800' },
};

interface TaskDependenciesTableProps {
  taskId?: string;
  showTaskFilter?: boolean;
  onAddDependency?: () => void;
  onRemoveDependency?: (dependency: TaskDependency) => void;
}

export default function TaskDependenciesTable({ 
  taskId, 
  showTaskFilter = true,
  onAddDependency,
  onRemoveDependency
}: TaskDependenciesTableProps) {
  const { toast } = useToast();
  const [dependencies, setDependencies] = useState<TaskDependency[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load task dependencies
  useEffect(() => {
    loadDependencies();
  }, [taskId]);

  const loadDependencies = async () => {
    setIsLoading(true);
    try {
      if (taskId) {
        // Load dependencies for specific task
        const dependentTasks = await getDependentTasks(taskId);
        
        // Convert to dependency format (simplified for demo)
        const taskDependencies: TaskDependency[] = dependentTasks.map(task => ({
          id: `${taskId}-${task.id}`,
          taskId: taskId,
          dependsOnTaskId: task.id!,
          dependencyType: 'finish-to-start',
          isBlocking: true,
          taskTitle: 'Current Task',
          dependsOnTaskTitle: task.title,
          taskStatus: 'In Progress',
          dependsOnTaskStatus: task.status,
          taskPriority: 'Medium',
          dependsOnTaskPriority: task.priority,
          createdAt: new Date(),
        }));
        
        setDependencies(taskDependencies);
      } else {
        // Load all dependencies - would need to be implemented
        setDependencies([]);
      }
    } catch (error) {
      console.error('Error loading task dependencies:', error);
      toast({
        title: "Error",
        description: "Failed to load task dependencies.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const handleRemoveDependency = async (dependency: TaskDependency) => {
    if (!confirm('Are you sure you want to remove this dependency?')) return;
    
    try {
      // Remove dependency logic would go here
      console.log('Remove dependency:', dependency);
      
      if (onRemoveDependency) {
        onRemoveDependency(dependency);
      }
      
      await loadDependencies();
      toast({
        title: "Success",
        description: "Task dependency removed successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove task dependency.",
        variant: "destructive"
      });
    }
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const columns: ColumnDef<TaskDependency>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "relationship",
      header: "Dependency Relationship",
      cell: ({ row }) => {
        const dependency = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <div className="text-sm">
              <Link 
                href={`/tasks/${dependency.dependsOnTaskId}`}
                className="font-medium hover:underline text-blue-600"
              >
                {dependency.dependsOnTaskTitle}
              </Link>
            </div>
            <ArrowRight className="h-4 w-4 text-gray-400" />
            <div className="text-sm">
              <Link 
                href={`/tasks/${dependency.taskId}`}
                className="font-medium hover:underline text-blue-600"
              >
                {dependency.taskTitle}
              </Link>
            </div>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "dependencyType",
      header: "Type",
      cell: ({ row }) => {
        const type = row.original.dependencyType;
        const config = dependencyTypeConfig[type];
        
        return (
          <div className="space-y-1">
            <Badge className={`${config.color} text-xs`}>
              {config.label}
            </Badge>
            <p className="text-xs text-gray-500">{config.description}</p>
          </div>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: "status",
      header: "Status",
      cell: ({ row }) => {
        const dependency = row.original;
        const dependsOnStatus = dependency.dependsOnTaskStatus || 'Not Started';
        const taskStatus = dependency.taskStatus || 'Not Started';
        const dependsOnConfig = statusConfig[dependsOnStatus] || statusConfig['Not Started'];
        const taskConfig = statusConfig[taskStatus] || statusConfig['Not Started'];
        
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Badge className={`${dependsOnConfig.color} text-xs`}>
                {dependsOnStatus}
              </Badge>
              <ArrowRight className="h-3 w-3 text-gray-400" />
              <Badge className={`${taskConfig.color} text-xs`}>
                {taskStatus}
              </Badge>
            </div>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "priority",
      header: "Priority",
      cell: ({ row }) => {
        const dependency = row.original;
        const dependsOnPriority = dependency.dependsOnTaskPriority || 'Medium';
        const taskPriority = dependency.taskPriority || 'Medium';
        const dependsOnConfig = priorityConfig[dependsOnPriority];
        const taskConfig = priorityConfig[taskPriority];
        
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Badge className={`${dependsOnConfig.color} text-xs`}>
                {dependsOnPriority}
              </Badge>
              <ArrowRight className="h-3 w-3 text-gray-400" />
              <Badge className={`${taskConfig.color} text-xs`}>
                {taskPriority}
              </Badge>
            </div>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: "blocking",
      header: "Blocking",
      cell: ({ row }) => {
        const isBlocking = row.original.isBlocking;
        
        return (
          <Badge variant={isBlocking ? "destructive" : "secondary"}>
            {isBlocking ? "Blocking" : "Non-blocking"}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        const isBlocking = row.original.isBlocking;
        return value.includes(isBlocking ? 'blocking' : 'non-blocking');
      },
    },
    {
      id: "lag",
      header: "Lag",
      cell: ({ row }) => {
        const lag = row.original.lag;
        
        if (!lag || lag === 0) {
          return <span className="text-gray-400">None</span>;
        }
        
        return (
          <Badge variant="outline" className="text-xs">
            {lag} day{lag !== 1 ? 's' : ''}
          </Badge>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(createdAt)}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const dependency = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // View dependency details
                console.log('View dependency:', dependency);
              }}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleRemoveDependency(dependency)}
              className="text-red-600 hover:text-red-700"
            >
              <Unlink className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], []);

  const tableActions = [
    {
      label: "Add Dependency",
      icon: Plus,
      onClick: () => {
        if (onAddDependency) {
          onAddDependency();
        } else {
          console.log('Add new dependency');
          toast({
            title: "Feature Coming Soon",
            description: "Dependency creation will be available soon."
          });
        }
      },
      variant: "default" as const,
    },
    {
      label: "Remove Selected",
      icon: Unlink,
      onClick: async (selectedRows: TaskDependency[]) => {
        if (confirm(`Remove ${selectedRows.length} selected dependencies?`)) {
          try {
            await Promise.all(selectedRows.map(dep => handleRemoveDependency(dep)));
            toast({
              title: "Success",
              description: `${selectedRows.length} dependencies removed successfully.`
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to remove selected dependencies.",
              variant: "destructive"
            });
          }
        }
      },
      variant: "destructive" as const,
      requiresSelection: true,
    },
    {
      label: "Export Dependencies",
      icon: Download,
      onClick: (selectedRows: TaskDependency[]) => {
        console.log('Export dependencies:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Dependency export will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    {
      key: 'dependencyType',
      label: 'Dependency Type',
      options: [
        { label: 'Finish → Start', value: 'finish-to-start' },
        { label: 'Start → Start', value: 'start-to-start' },
        { label: 'Finish → Finish', value: 'finish-to-finish' },
        { label: 'Start → Finish', value: 'start-to-finish' },
      ]
    },
    {
      key: 'blocking',
      label: 'Blocking Status',
      options: [
        { label: 'Blocking', value: 'blocking' },
        { label: 'Non-blocking', value: 'non-blocking' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={dependencies}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadDependencies}
      tableActions={tableActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search dependencies by task name..."
      emptyStateMessage="No task dependencies found"
      emptyStateDescription="Create dependencies to establish task relationships and workflow order."
    />
  );
}
