"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { 
  MessageSquare,
  Reply,
  Edit2,
  Trash2,
  Plus,
  Download,
  Eye,
  Heart,
  Pin,
  Flag,
  AtSign,
  Paperclip,
  Clock
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { formatDistanceToNow } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import Link from 'next/link';

interface TaskComment {
  id?: string;
  taskId: string;
  userId: string;
  userName?: string;
  userEmail?: string;
  content: string;
  commentType: 'comment' | 'status_update' | 'mention' | 'system';
  parentCommentId?: string; // For replies
  mentions?: string[]; // User IDs mentioned in comment
  attachments?: {
    fileName: string;
    fileUrl: string;
    fileSize: number;
    fileType: string;
  }[];
  isPinned?: boolean;
  isEdited?: boolean;
  reactions?: {
    userId: string;
    type: 'like' | 'love' | 'laugh' | 'angry' | 'sad';
  }[];
  createdAt?: Timestamp | Date | string;
  updatedAt?: Timestamp | Date | string;
  
  // Denormalized data
  taskTitle?: string;
  replyCount?: number;
}

const commentTypeConfig = {
  'comment': { color: 'bg-blue-100 text-blue-800', icon: MessageSquare },
  'status_update': { color: 'bg-green-100 text-green-800', icon: Clock },
  'mention': { color: 'bg-purple-100 text-purple-800', icon: AtSign },
  'system': { color: 'bg-gray-100 text-gray-800', icon: MessageSquare },
};

interface TaskCommentsTableProps {
  taskId?: string;
  userId?: string;
  showTaskFilter?: boolean;
  showUserFilter?: boolean;
  onAddComment?: () => void;
  onReplyToComment?: (comment: TaskComment) => void;
}

export default function TaskCommentsTable({ 
  taskId, 
  userId,
  showTaskFilter = true,
  showUserFilter = true,
  onAddComment,
  onReplyToComment
}: TaskCommentsTableProps) {
  const { toast } = useToast();
  const [comments, setComments] = useState<TaskComment[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load task comments
  useEffect(() => {
    loadComments();
  }, [taskId, userId]);

  const loadComments = async () => {
    setIsLoading(true);
    try {
      let commentsQuery = query(
        collection(db, 'task_comments'),
        orderBy('createdAt', 'desc')
      );

      // Filter by task if specified
      if (taskId) {
        commentsQuery = query(commentsQuery, where('taskId', '==', taskId));
      }

      // Filter by user if specified
      if (userId) {
        commentsQuery = query(commentsQuery, where('userId', '==', userId));
      }

      const commentsSnapshot = await getDocs(commentsQuery);
      const fetchedComments = commentsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(doc.data().createdAt),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date(doc.data().updatedAt)
      } as TaskComment));

      setComments(fetchedComments);
    } catch (error) {
      console.error('Error loading task comments:', error);
      toast({
        title: "Error",
        description: "Failed to load task comments.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const handleEdit = (comment: TaskComment) => {
    console.log('Edit comment:', comment);
    toast({
      title: "Feature Coming Soon",
      description: "Comment editing will be available soon."
    });
  };

  const handleDelete = async (comment: TaskComment) => {
    if (!confirm('Are you sure you want to delete this comment?')) return;
    
    try {
      // Delete comment logic would go here
      console.log('Delete comment:', comment);
      
      await loadComments();
      toast({
        title: "Success",
        description: "Comment deleted successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete comment.",
        variant: "destructive"
      });
    }
  };

  const handleReply = (comment: TaskComment) => {
    if (onReplyToComment) {
      onReplyToComment(comment);
    } else {
      console.log('Reply to comment:', comment);
      toast({
        title: "Feature Coming Soon",
        description: "Comment replies will be available soon."
      });
    }
  };

  const handlePin = async (comment: TaskComment) => {
    try {
      // Pin/unpin comment logic would go here
      console.log('Toggle pin for comment:', comment);
      
      await loadComments();
      toast({
        title: "Success",
        description: `Comment ${comment.isPinned ? 'unpinned' : 'pinned'} successfully.`
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update comment pin status.",
        variant: "destructive"
      });
    }
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const columns: ColumnDef<TaskComment>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "taskId",
      header: "Task",
      cell: ({ row }) => {
        const comment = row.original;
        const taskTitle = comment.taskTitle || `Task ${comment.taskId}`;
        
        return (
          <div className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4 text-gray-400" />
            <Link 
              href={`/tasks/${comment.taskId}`}
              className="font-medium hover:underline text-blue-600"
            >
              {taskTitle}
            </Link>
          </div>
        );
      },
      enableHiding: !!taskId, // Hide if filtering by specific task
    },
    {
      accessorKey: "userId",
      header: "Author",
      cell: ({ row }) => {
        const comment = row.original;
        const userName = comment.userName || comment.userEmail || comment.userId;
        
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-xs">
                {userName.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium text-sm">{userName}</div>
              {comment.userEmail && (
                <div className="text-xs text-gray-500">{comment.userEmail}</div>
              )}
            </div>
          </div>
        );
      },
      enableHiding: !!userId, // Hide if filtering by specific user
    },
    {
      accessorKey: "content",
      header: "Comment",
      cell: ({ row }) => {
        const comment = row.original;
        
        return (
          <div className="max-w-md">
            <div className="flex items-start gap-2 mb-2">
              {comment.isPinned && (
                <Pin className="h-3 w-3 text-yellow-500 mt-1" />
              )}
              <p className="text-sm line-clamp-3">
                {comment.content}
              </p>
            </div>
            
            {comment.attachments && comment.attachments.length > 0 && (
              <div className="flex items-center gap-1 mt-1">
                <Paperclip className="h-3 w-3 text-gray-400" />
                <span className="text-xs text-gray-500">
                  {comment.attachments.length} attachment{comment.attachments.length !== 1 ? 's' : ''}
                </span>
              </div>
            )}
            
            {comment.mentions && comment.mentions.length > 0 && (
              <div className="flex items-center gap-1 mt-1">
                <AtSign className="h-3 w-3 text-purple-500" />
                <span className="text-xs text-purple-600">
                  {comment.mentions.length} mention{comment.mentions.length !== 1 ? 's' : ''}
                </span>
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "commentType",
      header: "Type",
      cell: ({ row }) => {
        const type = row.original.commentType;
        const config = commentTypeConfig[type] || commentTypeConfig.comment;
        const TypeIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <TypeIcon className="h-3 w-3" />
            {type.replace('_', ' ')}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: "engagement",
      header: "Engagement",
      cell: ({ row }) => {
        const comment = row.original;
        const reactionCount = comment.reactions?.length || 0;
        const replyCount = comment.replyCount || 0;
        
        return (
          <div className="flex items-center gap-3 text-sm">
            {reactionCount > 0 && (
              <div className="flex items-center gap-1">
                <Heart className="h-3 w-3 text-red-500" />
                <span>{reactionCount}</span>
              </div>
            )}
            {replyCount > 0 && (
              <div className="flex items-center gap-1">
                <Reply className="h-3 w-3 text-blue-500" />
                <span>{replyCount}</span>
              </div>
            )}
            {reactionCount === 0 && replyCount === 0 && (
              <span className="text-gray-400">No engagement</span>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "createdAt",
      header: "Posted",
      cell: ({ row }) => {
        const comment = row.original;
        const createdAt = comment.createdAt;
        
        return (
          <div className="text-sm">
            <div className="text-gray-600">{formatDate(createdAt)}</div>
            {comment.isEdited && (
              <Badge variant="outline" className="text-xs mt-1">
                Edited
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const comment = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleReply(comment)}
            >
              <Reply className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handlePin(comment)}
              className={comment.isPinned ? "text-yellow-600" : ""}
            >
              <Pin className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEdit(comment)}
            >
              <Edit2 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDelete(comment)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], [taskId, userId]);

  const tableActions = [
    {
      label: "Add Comment",
      icon: Plus,
      onClick: () => {
        if (onAddComment) {
          onAddComment();
        } else {
          console.log('Add new comment');
          toast({
            title: "Feature Coming Soon",
            description: "Comment creation will be available soon."
          });
        }
      },
      variant: "default" as const,
    },
    {
      label: "Export Selected",
      icon: Download,
      onClick: (selectedRows: TaskComment[]) => {
        console.log('Export comments:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Comment export will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Pin Selected",
      icon: Pin,
      onClick: async (selectedRows: TaskComment[]) => {
        try {
          await Promise.all(selectedRows.map(comment => handlePin(comment)));
          toast({
            title: "Success",
            description: `${selectedRows.length} comments pinned successfully.`
          });
        } catch (error) {
          toast({
            title: "Error",
            description: "Failed to pin selected comments.",
            variant: "destructive"
          });
        }
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Delete Selected",
      icon: Trash2,
      onClick: async (selectedRows: TaskComment[]) => {
        if (confirm(`Delete ${selectedRows.length} selected comments?`)) {
          try {
            await Promise.all(selectedRows.map(comment => handleDelete(comment)));
            toast({
              title: "Success",
              description: `${selectedRows.length} comments deleted successfully.`
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to delete selected comments.",
              variant: "destructive"
            });
          }
        }
      },
      variant: "destructive" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    {
      key: 'commentType',
      label: 'Comment Type',
      options: [
        { label: 'Comment', value: 'comment' },
        { label: 'Status Update', value: 'status_update' },
        { label: 'Mention', value: 'mention' },
        { label: 'System', value: 'system' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={comments}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadComments}
      tableActions={tableActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search comments by content, author..."
      emptyStateMessage="No task comments found"
      emptyStateDescription="Start a conversation by adding the first comment."
    />
  );
}
