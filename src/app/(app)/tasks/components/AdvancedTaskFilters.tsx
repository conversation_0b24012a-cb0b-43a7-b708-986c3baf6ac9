"use client";

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  Search, 
  Filter, 
  X, 
  Plus, 
  Save, 
  Star,
  Calendar,
  User,
  Flag,
  Tag,
  Clock,
  Target,
  ChevronDown,
  Settings,
  Sparkles,
  History
} from 'lucide-react';
import { TASK_STATUSES, TASK_PRIORITIES } from '@/lib/constants';
import type { Task, TaskStatus } from '@/types/firestore';
import { format, isAfter, isBefore, isToday, isThisWeek, isThisMonth } from 'date-fns';

export interface FilterCriteria {
  search: string;
  statuses: TaskStatus[];
  priorities: string[];
  assignees: string[];
  tags: string[];
  dateRange: {
    type: 'all' | 'today' | 'week' | 'month' | 'overdue' | 'custom';
    startDate?: Date;
    endDate?: Date;
  };
  hasAttachments?: boolean;
  hasComments?: boolean;
}

export interface FilterPreset {
  id: string;
  name: string;
  criteria: FilterCriteria;
  isDefault?: boolean;
  createdAt: Date;
  usageCount: number;
}

interface AdvancedTaskFiltersProps {
  tasks: Task[];
  usersMap: Record<string, string>;
  onFiltersChange: (filteredTasks: Task[], criteria: FilterCriteria) => void;
  className?: string;
}

const DEFAULT_CRITERIA: FilterCriteria = {
  search: '',
  statuses: [],
  priorities: [],
  assignees: [],
  tags: [],
  dateRange: { type: 'all' },
  hasAttachments: undefined,
  hasComments: undefined
};

const DEFAULT_PRESETS: FilterPreset[] = [
  {
    id: 'my-tasks',
    name: 'My Tasks',
    criteria: { ...DEFAULT_CRITERIA, assignees: ['current-user'] },
    isDefault: true,
    createdAt: new Date(),
    usageCount: 0
  },
  {
    id: 'urgent-tasks',
    name: 'Urgent Tasks',
    criteria: { ...DEFAULT_CRITERIA, priorities: ['Urgent'] },
    isDefault: true,
    createdAt: new Date(),
    usageCount: 0
  },
  {
    id: 'overdue-tasks',
    name: 'Overdue Tasks',
    criteria: { ...DEFAULT_CRITERIA, dateRange: { type: 'overdue' } },
    isDefault: true,
    createdAt: new Date(),
    usageCount: 0
  },
  {
    id: 'this-week',
    name: 'This Week',
    criteria: { ...DEFAULT_CRITERIA, dateRange: { type: 'week' } },
    isDefault: true,
    createdAt: new Date(),
    usageCount: 0
  }
];

export default function AdvancedTaskFilters({ 
  tasks, 
  usersMap, 
  onFiltersChange, 
  className 
}: AdvancedTaskFiltersProps) {
  const [criteria, setCriteria] = useState<FilterCriteria>(DEFAULT_CRITERIA);
  const [presets, setPresets] = useState<FilterPreset[]>(DEFAULT_PRESETS);
  const [activePreset, setActivePreset] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Extract unique values for suggestions
  const suggestions = useMemo(() => {
    const allTitles = tasks.map(t => t.title.toLowerCase());
    const allDescriptions = tasks.map(t => t.description?.toLowerCase() || '');
    const allTags = tasks.flatMap(t => t.tags || []);
    const allAssignees = Object.values(usersMap);
    
    return {
      titles: [...new Set(allTitles)],
      descriptions: [...new Set(allDescriptions.filter(Boolean))],
      tags: [...new Set(allTags)],
      assignees: allAssignees,
      priorities: TASK_PRIORITIES,
      statuses: TASK_STATUSES
    };
  }, [tasks, usersMap]);

  // Smart search suggestions based on current input
  const getSearchSuggestions = useCallback((query: string) => {
    if (!query || query.length < 2) return [];
    
    const lowerQuery = query.toLowerCase();
    const suggestions: string[] = [];
    
    // Add matching titles
    tasks.forEach(task => {
      if (task.title.toLowerCase().includes(lowerQuery)) {
        suggestions.push(task.title);
      }
    });
    
    // Add matching tags
    const matchingTags = [...new Set(tasks.flatMap(t => t.tags || []))]
      .filter(tag => tag.toLowerCase().includes(lowerQuery));
    suggestions.push(...matchingTags.map(tag => `tag:${tag}`));
    
    // Add matching assignees
    Object.values(usersMap).forEach(name => {
      if (name.toLowerCase().includes(lowerQuery)) {
        suggestions.push(`assignee:${name}`);
      }
    });
    
    return suggestions.slice(0, 8);
  }, [tasks, usersMap]);

  // Update search suggestions when search query changes
  useEffect(() => {
    const suggestions = getSearchSuggestions(criteria.search);
    setSearchSuggestions(suggestions);
    setShowSuggestions(suggestions.length > 0 && criteria.search.length >= 2);
  }, [criteria.search, getSearchSuggestions]);

  // Filter tasks based on criteria
  const filteredTasks = useMemo(() => {
    let filtered = [...tasks];

    // Search filter with full-text search
    if (criteria.search.trim()) {
      const query = criteria.search.toLowerCase();
      filtered = filtered.filter(task => {
        // Check if it's a special search (tag:, assignee:, etc.)
        if (query.startsWith('tag:')) {
          const tagQuery = query.replace('tag:', '');
          return task.tags?.some(tag => tag.toLowerCase().includes(tagQuery));
        }
        if (query.startsWith('assignee:')) {
          const assigneeQuery = query.replace('assignee:', '');
          const assigneeName = task.assigneeId ? usersMap[task.assigneeId] : '';
          return assigneeName.toLowerCase().includes(assigneeQuery);
        }
        
        // Full-text search
        return (
          task.title.toLowerCase().includes(query) ||
          task.description?.toLowerCase().includes(query) ||
          task.tags?.some(tag => tag.toLowerCase().includes(query)) ||
          (task.assigneeId && usersMap[task.assigneeId]?.toLowerCase().includes(query))
        );
      });
    }

    // Status filter
    if (criteria.statuses.length > 0) {
      filtered = filtered.filter(task => criteria.statuses.includes(task.status));
    }

    // Priority filter
    if (criteria.priorities.length > 0) {
      filtered = filtered.filter(task => criteria.priorities.includes(task.priority));
    }

    // Assignee filter
    if (criteria.assignees.length > 0) {
      filtered = filtered.filter(task => {
        if (criteria.assignees.includes('current-user')) {
          // In a real app, this would check against the actual current user
          return task.assigneeId === 'current-user-id';
        }
        return task.assigneeId && criteria.assignees.includes(task.assigneeId);
      });
    }

    // Tags filter
    if (criteria.tags.length > 0) {
      filtered = filtered.filter(task => 
        task.tags?.some(tag => criteria.tags.includes(tag))
      );
    }

    // Date range filter
    if (criteria.dateRange.type !== 'all') {
      const now = new Date();
      filtered = filtered.filter(task => {
        const dueDate = task.dueDate ? new Date(task.dueDate) : null;
        if (!dueDate) return false;

        switch (criteria.dateRange.type) {
          case 'today':
            return isToday(dueDate);
          case 'week':
            return isThisWeek(dueDate);
          case 'month':
            return isThisMonth(dueDate);
          case 'overdue':
            return isBefore(dueDate, now) && task.status !== 'Done';
          case 'custom':
            if (criteria.dateRange.startDate && criteria.dateRange.endDate) {
              return isAfter(dueDate, criteria.dateRange.startDate) && 
                     isBefore(dueDate, criteria.dateRange.endDate);
            }
            return true;
          default:
            return true;
        }
      });
    }

    // Attachments filter
    if (criteria.hasAttachments !== undefined) {
      filtered = filtered.filter(task => 
        criteria.hasAttachments ? (task.attachments?.length || 0) > 0 : (task.attachments?.length || 0) === 0
      );
    }

    // Comments filter
    if (criteria.hasComments !== undefined) {
      filtered = filtered.filter(task => 
        criteria.hasComments ? (task.comments?.length || 0) > 0 : (task.comments?.length || 0) === 0
      );
    }

    return filtered;
  }, [tasks, criteria, usersMap]);

  // Notify parent component when filters change
  useEffect(() => {
    onFiltersChange(filteredTasks, criteria);
  }, [filteredTasks, criteria, onFiltersChange]);

  const updateCriteria = (updates: Partial<FilterCriteria>) => {
    setCriteria(prev => ({ ...prev, ...updates }));
    setActivePreset(null); // Clear active preset when manually changing filters
  };

  const clearAllFilters = () => {
    setCriteria(DEFAULT_CRITERIA);
    setActivePreset(null);
  };

  const applyPreset = (preset: FilterPreset) => {
    setCriteria(preset.criteria);
    setActivePreset(preset.id);
    
    // Update usage count
    setPresets(prev => prev.map(p => 
      p.id === preset.id ? { ...p, usageCount: p.usageCount + 1 } : p
    ));
  };

  const saveCurrentAsPreset = () => {
    const name = prompt('Enter preset name:');
    if (!name) return;

    const newPreset: FilterPreset = {
      id: `preset-${Date.now()}`,
      name,
      criteria: { ...criteria },
      createdAt: new Date(),
      usageCount: 0
    };

    setPresets(prev => [...prev, newPreset]);
  };

  const hasActiveFilters = useMemo(() => {
    return criteria.search || 
           criteria.statuses.length > 0 || 
           criteria.priorities.length > 0 || 
           criteria.assignees.length > 0 || 
           criteria.tags.length > 0 || 
           criteria.dateRange.type !== 'all' ||
           criteria.hasAttachments !== undefined ||
           criteria.hasComments !== undefined;
  }, [criteria]);

  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (criteria.search) count++;
    if (criteria.statuses.length > 0) count++;
    if (criteria.priorities.length > 0) count++;
    if (criteria.assignees.length > 0) count++;
    if (criteria.tags.length > 0) count++;
    if (criteria.dateRange.type !== 'all') count++;
    if (criteria.hasAttachments !== undefined) count++;
    if (criteria.hasComments !== undefined) count++;
    return count;
  }, [criteria]);

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Advanced Filters
            {hasActiveFilters && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount} active
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="gap-1 text-muted-foreground hover:text-foreground"
              >
                <X className="h-3 w-3" />
                Clear
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <ChevronDown className={`h-4 w-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Quick Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search tasks, tags, assignees... (try 'tag:urgent' or 'assignee:john')"
            value={criteria.search}
            onChange={(e) => updateCriteria({ search: e.target.value })}
            className="pl-10 h-10"
          />

          {/* Search Suggestions */}
          <AnimatePresence>
            {showSuggestions && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="absolute top-full left-0 right-0 z-50 mt-1 bg-background border rounded-md shadow-lg"
              >
                <div className="p-2 space-y-1">
                  {searchSuggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      className="w-full text-left px-2 py-1 text-sm hover:bg-muted rounded flex items-center gap-2"
                      onClick={() => {
                        updateCriteria({ search: suggestion });
                        setShowSuggestions(false);
                      }}
                    >
                      <Sparkles className="h-3 w-3 text-muted-foreground" />
                      {suggestion}
                    </button>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Filter Presets */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Quick Filters</Label>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
            {presets.slice(0, 4).map(preset => (
              <Button
                key={preset.id}
                variant={activePreset === preset.id ? "default" : "outline"}
                size="sm"
                onClick={() => applyPreset(preset)}
                className="gap-2 justify-center items-center"
              >
                {preset.isDefault && <Star className="h-4 w-4" />}
                {preset.name}
                {preset.usageCount > 0 && (
                  <Badge variant="secondary" className="ml-1 text-xs">
                    {preset.usageCount}
                  </Badge>
                )}
              </Button>
            ))}
          </div>
        </div>

        {/* Expanded Filters */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-4"
            >
              <Separator />

              {/* Status Filter */}
              <div className="space-y-3">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Status
                </Label>
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                  {TASK_STATUSES.map(status => (
                    <div key={status} className="flex items-center space-x-2">
                      <Checkbox
                        id={`status-${status}`}
                        checked={criteria.statuses.includes(status)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            updateCriteria({ statuses: [...criteria.statuses, status] });
                          } else {
                            updateCriteria({ statuses: criteria.statuses.filter(s => s !== status) });
                          }
                        }}
                      />
                      <Label htmlFor={`status-${status}`} className="text-sm cursor-pointer">
                        {status}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Priority Filter */}
              <div className="space-y-3">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <Flag className="h-4 w-4" />
                  Priority
                </Label>
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                  {TASK_PRIORITIES.map(priority => (
                    <div key={priority} className="flex items-center space-x-2">
                      <Checkbox
                        id={`priority-${priority}`}
                        checked={criteria.priorities.includes(priority)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            updateCriteria({ priorities: [...criteria.priorities, priority] });
                          } else {
                            updateCriteria({ priorities: criteria.priorities.filter(p => p !== priority) });
                          }
                        }}
                      />
                      <Label htmlFor={`priority-${priority}`} className="text-sm cursor-pointer">
                        {priority}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Assignee Filter */}
              <div className="space-y-3">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Assignee
                </Label>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="current-user"
                      checked={criteria.assignees.includes('current-user')}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          updateCriteria({ assignees: [...criteria.assignees, 'current-user'] });
                        } else {
                          updateCriteria({ assignees: criteria.assignees.filter(a => a !== 'current-user') });
                        }
                      }}
                    />
                    <Label htmlFor="current-user" className="text-sm cursor-pointer font-medium">
                      My Tasks
                    </Label>
                  </div>
                  {Object.entries(usersMap).slice(0, 5).map(([id, name]) => (
                    <div key={id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`assignee-${id}`}
                        checked={criteria.assignees.includes(id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            updateCriteria({ assignees: [...criteria.assignees, id] });
                          } else {
                            updateCriteria({ assignees: criteria.assignees.filter(a => a !== id) });
                          }
                        }}
                      />
                      <Label htmlFor={`assignee-${id}`} className="text-sm cursor-pointer">
                        {name}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Date Range Filter */}
              <div className="space-y-2">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Due Date
                </Label>
                <Select
                  value={criteria.dateRange.type}
                  onValueChange={(value: any) => updateCriteria({ dateRange: { type: value } })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All dates</SelectItem>
                    <SelectItem value="today">Due today</SelectItem>
                    <SelectItem value="week">Due this week</SelectItem>
                    <SelectItem value="month">Due this month</SelectItem>
                    <SelectItem value="overdue">Overdue</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Additional Filters */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Additional Filters</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="has-attachments"
                      checked={criteria.hasAttachments === true}
                      onCheckedChange={(checked) => {
                        updateCriteria({ hasAttachments: checked ? true : undefined });
                      }}
                    />
                    <Label htmlFor="has-attachments" className="text-sm">
                      Has attachments
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="has-comments"
                      checked={criteria.hasComments === true}
                      onCheckedChange={(checked) => {
                        updateCriteria({ hasComments: checked ? true : undefined });
                      }}
                    />
                    <Label htmlFor="has-comments" className="text-sm">
                      Has comments
                    </Label>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Active Filters & Actions */}
        {hasActiveFilters && (
          <div className="flex items-center justify-between pt-2 border-t">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {filteredTasks.length} of {tasks.length} tasks
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={saveCurrentAsPreset}>
                <Save className="h-4 w-4 mr-2" />
                Save Preset
              </Button>
              <Button variant="outline" size="sm" onClick={clearAllFilters}>
                <X className="h-4 w-4 mr-2" />
                Clear All
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
