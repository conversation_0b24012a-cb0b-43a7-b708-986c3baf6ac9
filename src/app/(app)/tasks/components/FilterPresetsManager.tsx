"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Save, 
  Star, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Copy, 
  Share2,
  TrendingUp,
  Clock,
  Users,
  Settings,
  Plus,
  BookmarkPlus
} from 'lucide-react';
import { format } from 'date-fns';
import type { FilterCriteria, FilterPreset } from './AdvancedTaskFilters';

interface FilterPresetsManagerProps {
  presets: FilterPreset[];
  currentCriteria: FilterCriteria;
  activePreset: string | null;
  onPresetSelect: (preset: FilterPreset) => void;
  onPresetSave: (preset: Omit<FilterPreset, 'id' | 'createdAt' | 'usageCount'>) => void;
  onPresetUpdate: (id: string, updates: Partial<FilterPreset>) => void;
  onPresetDelete: (id: string) => void;
  className?: string;
}

interface PresetFormData {
  name: string;
  description?: string;
  isDefault?: boolean;
}

export default function FilterPresetsManager({
  presets,
  currentCriteria,
  activePreset,
  onPresetSelect,
  onPresetSave,
  onPresetUpdate,
  onPresetDelete,
  className
}: FilterPresetsManagerProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingPreset, setEditingPreset] = useState<FilterPreset | null>(null);
  const [formData, setFormData] = useState<PresetFormData>({
    name: '',
    description: '',
    isDefault: false
  });

  // Sort presets by usage and creation date
  const sortedPresets = [...presets].sort((a, b) => {
    // Default presets first
    if (a.isDefault && !b.isDefault) return -1;
    if (!a.isDefault && b.isDefault) return 1;
    
    // Then by usage count
    if (a.usageCount !== b.usageCount) {
      return b.usageCount - a.usageCount;
    }
    
    // Finally by creation date
    return b.createdAt.getTime() - a.createdAt.getTime();
  });

  const handleSavePreset = () => {
    if (!formData.name.trim()) return;

    const presetData = {
      name: formData.name.trim(),
      criteria: currentCriteria,
      isDefault: formData.isDefault || false
    };

    if (editingPreset) {
      onPresetUpdate(editingPreset.id, presetData);
    } else {
      onPresetSave(presetData);
    }

    setIsDialogOpen(false);
    setEditingPreset(null);
    setFormData({ name: '', description: '', isDefault: false });
  };

  const handleEditPreset = (preset: FilterPreset) => {
    setEditingPreset(preset);
    setFormData({
      name: preset.name,
      description: '',
      isDefault: preset.isDefault || false
    });
    setIsDialogOpen(true);
  };

  const handleDuplicatePreset = (preset: FilterPreset) => {
    onPresetSave({
      name: `${preset.name} (Copy)`,
      criteria: preset.criteria,
      isDefault: false
    });
  };

  const getCriteriaDescription = (criteria: FilterCriteria): string => {
    const parts: string[] = [];
    
    if (criteria.search) parts.push(`Search: "${criteria.search}"`);
    if (criteria.statuses.length > 0) parts.push(`Status: ${criteria.statuses.join(', ')}`);
    if (criteria.priorities.length > 0) parts.push(`Priority: ${criteria.priorities.join(', ')}`);
    if (criteria.assignees.length > 0) parts.push(`Assignees: ${criteria.assignees.length} selected`);
    if (criteria.tags.length > 0) parts.push(`Tags: ${criteria.tags.join(', ')}`);
    if (criteria.dateRange.type !== 'all') parts.push(`Due: ${criteria.dateRange.type}`);
    if (criteria.hasAttachments !== undefined) parts.push('Has attachments');
    if (criteria.hasComments !== undefined) parts.push('Has comments');
    
    return parts.length > 0 ? parts.join(' • ') : 'No filters';
  };

  const hasUnsavedChanges = () => {
    if (!activePreset) return false;
    
    const currentPreset = presets.find(p => p.id === activePreset);
    if (!currentPreset) return false;
    
    return JSON.stringify(currentPreset.criteria) !== JSON.stringify(currentCriteria);
  };

  return (
    <div className={className}>
      {/* Quick Preset Buttons */}
      <div className="flex flex-wrap gap-2 mb-4">
        {sortedPresets.slice(0, 6).map(preset => (
          <Button
            key={preset.id}
            variant={activePreset === preset.id ? "default" : "outline"}
            size="sm"
            onClick={() => onPresetSelect(preset)}
            className="gap-2 relative"
          >
            {preset.isDefault && <Star className="h-3 w-3" />}
            {preset.name}
            {preset.usageCount > 0 && (
              <Badge variant="secondary" className="ml-1 text-xs">
                {preset.usageCount}
              </Badge>
            )}
            {activePreset === preset.id && hasUnsavedChanges() && (
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-orange-500 rounded-full" />
            )}
          </Button>
        ))}
        
        {/* Save Current Filters Button */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="gap-2">
              <Plus className="h-3 w-3" />
              Save Preset
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingPreset ? 'Edit Filter Preset' : 'Save Filter Preset'}
              </DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="preset-name">Preset Name</Label>
                <Input
                  id="preset-name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter preset name..."
                />
              </div>
              
              <div>
                <Label>Current Filters</Label>
                <div className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
                  {getCriteriaDescription(currentCriteria)}
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="is-default"
                  checked={formData.isDefault}
                  onChange={(e) => setFormData(prev => ({ ...prev, isDefault: e.target.checked }))}
                />
                <Label htmlFor="is-default">Mark as default preset</Label>
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSavePreset} disabled={!formData.name.trim()}>
                  {editingPreset ? 'Update' : 'Save'} Preset
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Preset Management Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <BookmarkPlus className="h-5 w-5" />
              Saved Presets
              <Badge variant="outline">{presets.length}</Badge>
            </div>
            {hasUnsavedChanges() && (
              <Badge variant="outline" className="text-orange-600 border-orange-600">
                Unsaved Changes
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-2">
            <AnimatePresence>
              {sortedPresets.map(preset => (
                <motion.div
                  key={preset.id}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className={`p-3 rounded-lg border transition-colors ${
                    activePreset === preset.id 
                      ? 'border-primary bg-primary/5' 
                      : 'border-border hover:bg-muted/50'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        {preset.isDefault && <Star className="h-4 w-4 text-yellow-500" />}
                        <span className="font-medium">{preset.name}</span>
                        {preset.usageCount > 0 && (
                          <Badge variant="secondary" className="text-xs">
                            {preset.usageCount} uses
                          </Badge>
                        )}
                      </div>
                      
                      <div className="text-sm text-muted-foreground mb-2">
                        {getCriteriaDescription(preset.criteria)}
                      </div>
                      
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {format(preset.createdAt, 'MMM d, yyyy')}
                        </div>
                        {preset.usageCount > 0 && (
                          <div className="flex items-center gap-1">
                            <TrendingUp className="h-3 w-3" />
                            {preset.usageCount} times used
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onPresetSelect(preset)}
                        className="h-8 px-2"
                      >
                        Apply
                      </Button>
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditPreset(preset)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDuplicatePreset(preset)}>
                            <Copy className="h-4 w-4 mr-2" />
                            Duplicate
                          </DropdownMenuItem>
                          {!preset.isDefault && (
                            <DropdownMenuItem 
                              onClick={() => onPresetDelete(preset.id)}
                              className="text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
            
            {presets.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <BookmarkPlus className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No saved presets yet</p>
                <p className="text-sm">Save your current filters as a preset to reuse them later</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
