"use client";

import React, { useMemo } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableFilter, type AdvancedTableAction } from '@/components/ui/advanced-data-table';
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import {
  Edit2,
  Trash2,
  Eye,
  UserCircle,
  Clock,
  Calendar,
  CheckCircle,
  Star,
  Archive,
  Download,
  MessageSquare,
  Phone,
  Mail,
  Play,
  Pause,
  RotateCcw,
  AlertTriangle,
  Target,
  PlusCircle
} from "lucide-react";
import type { Task, EvexUser } from '@/types/firestore';
import { format, parseISO } from 'date-fns';
import { Timestamp } from "firebase/firestore";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from 'next/navigation';

interface TaskTableProps {
  tasks: Task[];
  usersMap: Record<string, string>;
}

function formatDateDisplay(dateInput?: Timestamp | Date | string): string {
  if (!dateInput) return 'N/A';
  let date: Date;
  if (dateInput instanceof Timestamp) {
    date = dateInput.toDate();
  } else if (typeof dateInput === 'string') {
    date = new Date(dateInput);
  } else if (dateInput instanceof Date) {
    date = dateInput;
  } else {
    return 'N/A';
  }
  if (isNaN(date.getTime())) return 'Invalid Date';
  return format(date, 'MMM dd, yyyy');
}

function formatDuration(totalSeconds: number | undefined): string {
  if (totalSeconds === undefined || totalSeconds < 0) return 'N/A';
  if (totalSeconds === 0) return '0m';

  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);

  let result = '';
  if (hours > 0) result += `${hours}h `;
  if (minutes > 0 || (hours === 0 && totalSeconds > 0)) {
    result += `${minutes}m`;
  }

  return result.trim() || '0m';
}

const getPriorityBadgeVariant = (priority: Task['priority']): "default" | "secondary" | "destructive" | "outline" => {
  switch (priority) {
    case "Urgent":
      return "destructive";
    case "High":
      return "default";
    case "Medium":
      return "secondary";
    case "Low":
      return "outline";
    default:
      return "outline";
  }
};

const getStatusBadgeVariant = (status: Task['status']): "default" | "secondary" | "destructive" | "outline" => {
  switch (status) {
    case "Done":
      return "default";
    case "In Progress":
      return "secondary";
    case "To Do":
      return "outline";
    case "Blocked":
      return "destructive";
    default:
      return "outline";
  }
};

const getInitials = (name: string) => {
  if (!name) return "U";
  const parts = name.split(" ");
  if (parts.length > 1 && parts[0] && parts[parts.length-1]) {
    return (parts[0][0] + parts[parts.length-1][0]).toUpperCase();
  }
  return name.substring(0, 2).toUpperCase();
}

export default function TaskTable({ tasks, usersMap }: TaskTableProps) {
  const { toast } = useToast();
  const router = useRouter();

  // Define columns
  const columns: AdvancedTableColumn<Task>[] = useMemo(() => [
    {
      accessorKey: 'title',
      title: 'Task',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const task = row.original;
        return (
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarFallback>
                <CheckCircle className="h-4 w-4" />
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium max-w-xs truncate">
                <Link href={`/tasks/${task.id}`} className="hover:underline">
                  {task.title}
                </Link>
              </div>
              <div className="text-sm text-muted-foreground">{task.description?.substring(0, 50) || 'No description'}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'assigneeId',
      title: 'Assignee',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const task = row.original;
        const assigneeName = task.assigneeId ? usersMap[task.assigneeId] || 'Unassigned' : 'Unassigned';
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-6 w-6">
              <AvatarFallback className="text-xs">
                {getInitials(assigneeName)}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm">{assigneeName}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'dueDate',
      title: 'Due Date',
      sortable: true,
      cell: ({ row }) => (
        <div className="flex items-center gap-2 text-sm">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          {formatDateDisplay(row.original.dueDate)}
        </div>
      ),
    },
    {
      accessorKey: 'timeLogged',
      title: 'Time Logged',
      sortable: true,
      cell: ({ row }) => (
        <div className="flex items-center gap-2 text-sm">
          <Clock className="h-4 w-4 text-muted-foreground" />
          {formatDuration(row.original.timeLogged)}
        </div>
      ),
    },
    {
      accessorKey: 'progress',
      title: 'Progress',
      sortable: true,
      cell: ({ row }) => {
        const progress = row.original.progress || 0;
        return (
          <div className="flex items-center gap-2 min-w-[120px]">
            <Progress value={progress} className="flex-1" />
            <span className="text-xs text-muted-foreground w-8">{progress}%</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'priority',
      title: 'Priority',
      sortable: true,
      filterable: true,
      cell: ({ row }) => (
        <Badge variant={getPriorityBadgeVariant(row.original.priority)}>
          {row.original.priority || 'None'}
        </Badge>
      ),
    },
    {
      accessorKey: 'status',
      title: 'Status',
      sortable: true,
      filterable: true,
      cell: ({ row }) => (
        <Badge variant={getStatusBadgeVariant(row.original.status)}>
          {row.original.status || 'Unknown'}
        </Badge>
      ),
    },
  ], [usersMap]);

  // Define filters
  const filters: AdvancedTableFilter[] = [
    {
      id: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { label: 'To Do', value: 'To Do' },
        { label: 'In Progress', value: 'In Progress' },
        { label: 'Done', value: 'Done' },
        { label: 'Blocked', value: 'Blocked' },
      ],
      placeholder: 'All statuses',
    },
    {
      id: 'priority',
      label: 'Priority',
      type: 'select',
      options: [
        { label: 'Urgent', value: 'Urgent' },
        { label: 'High', value: 'High' },
        { label: 'Medium', value: 'Medium' },
        { label: 'Low', value: 'Low' },
      ],
      placeholder: 'All priorities',
    },
    {
      id: 'assigneeId',
      label: 'Assignee',
      type: 'select',
      options: Object.entries(usersMap).map(([id, name]) => ({ label: name, value: id })),
      placeholder: 'All assignees',
    },
  ];

  // Define bulk actions
  const bulkActions: AdvancedTableAction<Task>[] = [
    {
      label: 'Export Selected',
      icon: Download,
      onClick: (selectedTasks) => {
        console.log('Exporting tasks:', selectedTasks);
        toast({
          title: "Export Started",
          description: `Exporting ${selectedTasks.length} tasks...`,
        });
      },
      variant: 'default',
    },
    {
      label: 'Mark as In Progress',
      icon: Play,
      onClick: (selectedTasks) => {
        console.log('Marking as in progress:', selectedTasks);
        toast({
          title: "Status Updated",
          description: `${selectedTasks.length} tasks marked as in progress`,
        });
      },
      variant: 'default',
    },
    {
      label: 'Mark as Done',
      icon: CheckCircle,
      onClick: (selectedTasks) => {
        console.log('Marking as done:', selectedTasks);
        toast({
          title: "Status Updated",
          description: `${selectedTasks.length} tasks marked as done`,
        });
      },
      variant: 'default',
    },
    {
      label: 'Archive Selected',
      icon: Archive,
      onClick: (selectedTasks) => {
        console.log('Archiving tasks:', selectedTasks);
        toast({
          title: "Tasks Archived",
          description: `${selectedTasks.length} tasks archived`,
        });
      },
      variant: 'secondary',
    },
    {
      label: 'Delete Selected',
      icon: Trash2,
      onClick: async (selectedTasks) => {
        if (confirm(`Are you sure you want to delete ${selectedTasks.length} tasks?`)) {
          // Implement bulk delete
          console.log('Deleting tasks:', selectedTasks);
          toast({
            title: "Tasks Deleted",
            description: `${selectedTasks.length} tasks deleted`,
            variant: "destructive",
          });
        }
      },
      variant: 'destructive',
    },
  ];

  // Row actions
  const getRowActions = (task: Task) => [
    {
      type: 'view' as const,
      label: 'View Details',
      href: `/tasks/${task.id}`,
    },
    {
      type: 'edit' as const,
      label: 'Edit Task',
      href: `/tasks/${task.id}/edit`,
    },
    {
      type: 'message' as const,
      label: 'Add Comment',
      onClick: (taskData: Task) => alert(`Adding comment to ${taskData.title}`),
    },
    {
      type: 'schedule' as const,
      label: 'Log Time',
      onClick: (taskData: Task) => alert(`Logging time for ${taskData.title}`),
    },
    {
      type: 'favorite' as const,
      label: 'Mark Priority',
      onClick: (taskData: Task) => alert(`Marked ${taskData.title} as priority`),
    },
    {
      type: 'copy' as const,
      label: 'Duplicate Task',
      onClick: (taskData: Task) => alert(`Duplicating ${taskData.title}`),
    },
    {
      type: 'archive' as const,
      label: 'Archive Task',
      onClick: (taskData: Task) => alert(`Archived ${taskData.title}`),
      variant: 'secondary' as const,
    },
    {
      type: 'delete' as const,
      label: 'Delete Task',
      onClick: async (taskData: Task) => {
        if (confirm(`Are you sure you want to delete ${taskData.title}?`)) {
          // Implement delete functionality
          console.log('Deleting task:', taskData);
          toast({
            title: "Task Deleted",
            description: `${taskData.title} has been deleted`,
            variant: "destructive",
          });
        }
      },
      variant: 'destructive' as const,
    },
  ];

  // Mobile card renderer
  const mobileCardRenderer = (task: Task) => (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarFallback>
              <CheckCircle className="h-4 w-4" />
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{task.title}</div>
            <div className="text-sm text-muted-foreground">{task.description?.substring(0, 30) || 'No description'}</div>
          </div>
        </div>
        <Badge variant={getStatusBadgeVariant(task.status)}>
          {task.status || 'Unknown'}
        </Badge>
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span className="text-muted-foreground">Assignee:</span>
          <span className="ml-2">{task.assigneeId ? usersMap[task.assigneeId] || 'Unassigned' : 'Unassigned'}</span>
        </div>
        <div>
          <span className="text-muted-foreground">Priority:</span>
          <Badge variant={getPriorityBadgeVariant(task.priority)} className="ml-2">
            {task.priority || 'None'}
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span className="text-muted-foreground">Due:</span>
          <span className="ml-2">{formatDateDisplay(task.dueDate)}</span>
        </div>
        <div>
          <span className="text-muted-foreground">Time:</span>
          <span className="ml-2">{formatDuration(task.timeLogged)}</span>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <span className="text-muted-foreground text-sm">Progress:</span>
        <Progress value={task.progress || 0} className="flex-1" />
        <span className="text-xs text-muted-foreground">{task.progress || 0}%</span>
      </div>
    </div>
  );

  return (
    <AdvancedDataTable
      data={tasks}
      columns={columns}
      loading={false}
      enableVirtualization={tasks.length > 100}
      enableGlobalSearch={true}
      searchPlaceholder="Search tasks by title, description, or assignee..."
      enableColumnFilters={true}
      filters={filters}
      enableRowSelection={true}
      enableColumnResizing={true}
      enableColumnVisibility={true}
      bulkActions={bulkActions}
      primaryAction={{
        label: "Create Task",
        onClick: () => router.push('/tasks/add'),
        icon: PlusCircle
      }}
      enableRowActions={true}
      rowActions={getRowActions}
      maxVisibleRowActions={3}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="tasks-export"
      mobileCardRenderer={mobileCardRenderer}
      onRefresh={() => window.location.reload()}
      variant="default"
      className="w-full"
      emptyMessage="No tasks found. Create your first task to get started!"
    />
  );
}