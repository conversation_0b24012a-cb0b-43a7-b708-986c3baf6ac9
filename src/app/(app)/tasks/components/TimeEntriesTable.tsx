"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { 
  Clock,
  Play,
  Pause,
  Square,
  Edit2,
  Trash2,
  Plus,
  Download,
  Timer,
  Calendar,
  User,
  Target,
  BarChart3
} from 'lucide-react';
import type { ColumnDef } from '@tanstack/react-table';
import type { TimeEntry } from '@/types/firestore';
import { getTimeEntriesForTask } from '@/services/firestoreService';
import { formatDistanceToNow, format, differenceInMinutes } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import Link from 'next/link';

const entryTypeConfig = {
  'manual': { color: 'bg-blue-100 text-blue-800', icon: Edit2 },
  'timer': { color: 'bg-green-100 text-green-800', icon: Timer },
  'imported': { color: 'bg-purple-100 text-purple-800', icon: Download },
};

const statusConfig = {
  'active': { color: 'bg-green-100 text-green-800', icon: Play },
  'paused': { color: 'bg-yellow-100 text-yellow-800', icon: Pause },
  'completed': { color: 'bg-blue-100 text-blue-800', icon: Square },
  'cancelled': { color: 'bg-red-100 text-red-800', icon: Square },
};

interface TimeEntriesTableProps {
  taskId?: string;
  userId?: string;
  dateRange?: { start: Date; end: Date };
  showTaskFilter?: boolean;
  showUserFilter?: boolean;
  onAddTimeEntry?: () => void;
  onEditTimeEntry?: (entry: TimeEntry) => void;
}

export default function TimeEntriesTable({ 
  taskId, 
  userId, 
  dateRange,
  showTaskFilter = true,
  showUserFilter = true,
  onAddTimeEntry,
  onEditTimeEntry
}: TimeEntriesTableProps) {
  const { toast } = useToast();
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load time entries
  useEffect(() => {
    loadTimeEntries();
  }, [taskId, userId, dateRange]);

  const loadTimeEntries = async () => {
    setIsLoading(true);
    try {
      if (taskId) {
        // Load time entries for specific task
        const entries = await getTimeEntriesForTask(taskId);
        setTimeEntries(entries);
      } else {
        // Load all time entries - would need to be implemented
        setTimeEntries([]);
      }
    } catch (error) {
      console.error('Error loading time entries:', error);
      toast({
        title: "Error",
        description: "Failed to load time entries.",
        variant: "destructive"
      });
    }
    setIsLoading(false);
  };

  const handleEdit = (entry: TimeEntry) => {
    if (onEditTimeEntry) {
      onEditTimeEntry(entry);
    } else {
      console.log('Edit time entry:', entry);
      toast({
        title: "Feature Coming Soon",
        description: "Time entry editing will be available soon."
      });
    }
  };

  const handleDelete = async (entry: TimeEntry) => {
    if (!confirm('Are you sure you want to delete this time entry?')) return;
    
    try {
      // Delete time entry logic would go here
      console.log('Delete time entry:', entry);
      
      await loadTimeEntries();
      toast({
        title: "Success",
        description: "Time entry deleted successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete time entry.",
        variant: "destructive"
      });
    }
  };

  const formatDuration = (startTime: any, endTime: any) => {
    if (!startTime || !endTime) return 'N/A';
    
    let startDate: Date;
    let endDate: Date;
    
    if (startTime instanceof Timestamp) {
      startDate = startTime.toDate();
    } else if (typeof startTime === 'string') {
      startDate = new Date(startTime);
    } else {
      startDate = startTime;
    }
    
    if (endTime instanceof Timestamp) {
      endDate = endTime.toDate();
    } else if (typeof endTime === 'string') {
      endDate = new Date(endTime);
    } else {
      endDate = endTime;
    }
    
    const minutes = differenceInMinutes(endDate, startDate);
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${remainingMinutes}m`;
    }
    return `${minutes}m`;
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return format(dateObj, 'MMM dd, yyyy HH:mm');
  };

  const formatDateRelative = (date: any) => {
    if (!date) return 'Unknown';
    
    let dateObj: Date;
    if (date instanceof Timestamp) {
      dateObj = date.toDate();
    } else if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  const columns: ColumnDef<TimeEntry>[] = useMemo(() => [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(e.target.checked)}
          className="rounded border-gray-300"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "taskId",
      header: "Task",
      cell: ({ row }) => {
        const entry = row.original;
        const taskTitle = entry.taskTitle || `Task ${entry.taskId}`;
        
        return (
          <div className="flex items-center gap-2">
            <Target className="h-4 w-4 text-gray-400" />
            <div>
              <Link 
                href={`/tasks/${entry.taskId}`}
                className="font-medium hover:underline text-blue-600"
              >
                {taskTitle}
              </Link>
              {entry.description && (
                <p className="text-xs text-gray-500 line-clamp-1">
                  {entry.description}
                </p>
              )}
            </div>
          </div>
        );
      },
      enableHiding: !!taskId, // Hide if filtering by specific task
    },
    {
      accessorKey: "userId",
      header: "User",
      cell: ({ row }) => {
        const entry = row.original;
        const userName = entry.userName || entry.userId;
        
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-xs">
                {userName.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm font-medium">{userName}</span>
          </div>
        );
      },
      enableHiding: !!userId, // Hide if filtering by specific user
    },
    {
      id: "duration",
      header: "Duration",
      cell: ({ row }) => {
        const entry = row.original;
        const duration = formatDuration(entry.startTime, entry.endTime);
        
        return (
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-gray-400" />
            <span className="font-mono font-medium">{duration}</span>
          </div>
        );
      },
      sortingFn: (rowA, rowB) => {
        const durationA = differenceInMinutes(
          new Date(rowA.original.endTime || new Date()),
          new Date(rowA.original.startTime || new Date())
        );
        const durationB = differenceInMinutes(
          new Date(rowB.original.endTime || new Date()),
          new Date(rowB.original.startTime || new Date())
        );
        return durationA - durationB;
      },
    },
    {
      accessorKey: "startTime",
      header: "Start Time",
      cell: ({ row }) => {
        const startTime = row.original.startTime;
        return (
          <div className="text-sm">
            <div className="font-medium">{formatDate(startTime)}</div>
            <div className="text-gray-500">{formatDateRelative(startTime)}</div>
          </div>
        );
      },
    },
    {
      accessorKey: "endTime",
      header: "End Time",
      cell: ({ row }) => {
        const endTime = row.original.endTime;
        
        if (!endTime) {
          return (
            <Badge className="bg-green-100 text-green-800">
              <Play className="h-3 w-3 mr-1" />
              Active
            </Badge>
          );
        }
        
        return (
          <div className="text-sm">
            <div className="font-medium">{formatDate(endTime)}</div>
            <div className="text-gray-500">{formatDateRelative(endTime)}</div>
          </div>
        );
      },
    },
    {
      id: "type",
      header: "Type",
      cell: ({ row }) => {
        const entry = row.original;
        const type = entry.entryType || 'manual';
        const config = entryTypeConfig[type] || entryTypeConfig.manual;
        const TypeIcon = config.icon;
        
        return (
          <Badge className={`${config.color} flex items-center gap-1 w-fit`}>
            <TypeIcon className="h-3 w-3" />
            {type}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        const type = row.original.entryType || 'manual';
        return value.includes(type);
      },
    },
    {
      id: "billable",
      header: "Billable",
      cell: ({ row }) => {
        const billable = row.original.billable;
        
        return (
          <Badge variant={billable ? "default" : "secondary"}>
            {billable ? "Billable" : "Non-billable"}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        const billable = row.original.billable;
        return value.includes(billable ? 'billable' : 'non-billable');
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const entry = row.original;
        
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEdit(entry)}
            >
              <Edit2 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDelete(entry)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ], [taskId, userId]);

  const tableActions = [
    {
      label: "Add Time Entry",
      icon: Plus,
      onClick: () => {
        if (onAddTimeEntry) {
          onAddTimeEntry();
        } else {
          console.log('Add new time entry');
          toast({
            title: "Feature Coming Soon",
            description: "Time entry creation will be available soon."
          });
        }
      },
      variant: "default" as const,
    },
    {
      label: "Start Timer",
      icon: Play,
      onClick: () => {
        console.log('Start timer');
        toast({
          title: "Feature Coming Soon",
          description: "Timer functionality will be available soon."
        });
      },
      variant: "outline" as const,
    },
    {
      label: "Export Selected",
      icon: Download,
      onClick: (selectedRows: TimeEntry[]) => {
        console.log('Export time entries:', selectedRows);
        toast({
          title: "Feature Coming Soon",
          description: "Time entry export will be available soon."
        });
      },
      variant: "outline" as const,
      requiresSelection: true,
    },
    {
      label: "Delete Selected",
      icon: Trash2,
      onClick: async (selectedRows: TimeEntry[]) => {
        if (confirm(`Delete ${selectedRows.length} selected time entries?`)) {
          try {
            await Promise.all(selectedRows.map(entry => handleDelete(entry)));
            toast({
              title: "Success",
              description: `${selectedRows.length} time entries deleted successfully.`
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to delete selected time entries.",
              variant: "destructive"
            });
          }
        }
      },
      variant: "destructive" as const,
      requiresSelection: true,
    },
  ];

  const filterOptions = [
    {
      key: 'type',
      label: 'Entry Type',
      options: [
        { label: 'Manual', value: 'manual' },
        { label: 'Timer', value: 'timer' },
        { label: 'Imported', value: 'imported' },
      ]
    },
    {
      key: 'billable',
      label: 'Billable Status',
      options: [
        { label: 'Billable', value: 'billable' },
        { label: 'Non-billable', value: 'non-billable' },
      ]
    },
  ];

  return (
    <AdvancedDataTable
      data={timeEntries}
      columns={columns}
      isLoading={isLoading}
      onRefresh={loadTimeEntries}
      tableActions={tableActions}
      filterOptions={filterOptions}
      searchPlaceholder="Search time entries by task, user, description..."
      emptyStateMessage="No time entries found"
      emptyStateDescription="Start tracking time on tasks to see entries here."
    />
  );
}
