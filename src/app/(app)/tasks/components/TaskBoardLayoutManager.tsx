"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { 
  Layout, 
  Plus, 
  Edit, 
  Trash2, 
  Copy, 
  Share2, 
  Save,
  Settings,
  Columns,
  Rows,
  Grid,
  List,
  MoreHorizontal,
  Palette,
  Eye,
  EyeOff,
  Star,
  Download,
  Upload,
  RefreshCw
} from 'lucide-react';
import { TASK_STATUSES, TASK_PRIORITIES } from '@/lib/constants';
import type { TaskStatus } from '@/types/firestore';

export interface TaskBoardColumn {
  id: string;
  name: string;
  status?: TaskStatus;
  color: string;
  icon?: string;
  position: number;
  visible: boolean;
  wipLimit?: number;
  isCustom: boolean;
}

export interface TaskBoardSwimlane {
  id: string;
  name: string;
  criteria: {
    type: 'assignee' | 'priority' | 'tag' | 'custom';
    value: string;
  };
  color: string;
  position: number;
  visible: boolean;
  collapsed: boolean;
}

export interface TaskCardTemplate {
  id: string;
  name: string;
  description: string;
  fields: {
    title: boolean;
    description: boolean;
    assignee: boolean;
    priority: boolean;
    dueDate: boolean;
    tags: boolean;
    progress: boolean;
    attachments: boolean;
    comments: boolean;
    customFields: string[];
  };
  layout: 'compact' | 'detailed' | 'minimal';
  colorScheme: string;
  isDefault: boolean;
}

export interface TaskBoardLayout {
  id: string;
  name: string;
  description: string;
  type: 'kanban' | 'swimlane' | 'hybrid';
  columns: TaskBoardColumn[];
  swimlanes: TaskBoardSwimlane[];
  cardTemplate: TaskCardTemplate;
  viewSettings: {
    showColumnHeaders: boolean;
    showTaskCounts: boolean;
    showWipLimits: boolean;
    compactMode: boolean;
    groupBy?: 'none' | 'assignee' | 'priority' | 'tag';
    sortBy: 'created' | 'updated' | 'priority' | 'dueDate';
    sortOrder: 'asc' | 'desc';
  };
  isDefault: boolean;
  isPublic: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  usageCount: number;
}

interface TaskBoardLayoutManagerProps {
  currentLayout: TaskBoardLayout;
  availableLayouts: TaskBoardLayout[];
  onLayoutChange: (layout: TaskBoardLayout) => void;
  onLayoutSave: (layout: Omit<TaskBoardLayout, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>) => void;
  onLayoutDelete: (layoutId: string) => void;
  onLayoutDuplicate: (layout: TaskBoardLayout) => void;
  className?: string;
}

const DEFAULT_COLUMNS: TaskBoardColumn[] = [
  { id: 'todo', name: 'To Do', status: 'To Do', color: '#64748b', position: 0, visible: true, isCustom: false },
  { id: 'progress', name: 'In Progress', status: 'In Progress', color: '#3b82f6', position: 1, visible: true, isCustom: false },
  { id: 'review', name: 'In Review', status: 'In Review', color: '#f59e0b', position: 2, visible: true, isCustom: false },
  { id: 'done', name: 'Done', status: 'Done', color: '#10b981', position: 3, visible: true, isCustom: false }
];

const DEFAULT_CARD_TEMPLATE: TaskCardTemplate = {
  id: 'default',
  name: 'Default Card',
  description: 'Standard task card with all essential information',
  fields: {
    title: true,
    description: true,
    assignee: true,
    priority: true,
    dueDate: true,
    tags: true,
    progress: true,
    attachments: true,
    comments: true,
    customFields: []
  },
  layout: 'detailed',
  colorScheme: 'default',
  isDefault: true
};

export default function TaskBoardLayoutManager({
  currentLayout,
  availableLayouts,
  onLayoutChange,
  onLayoutSave,
  onLayoutDelete,
  onLayoutDuplicate,
  className
}: TaskBoardLayoutManagerProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingLayout, setEditingLayout] = useState<TaskBoardLayout | null>(null);
  const [activeTab, setActiveTab] = useState('columns');
  
  // Form state for layout editing
  const [layoutForm, setLayoutForm] = useState({
    name: '',
    description: '',
    type: 'kanban' as const,
    columns: DEFAULT_COLUMNS,
    swimlanes: [] as TaskBoardSwimlane[],
    cardTemplate: DEFAULT_CARD_TEMPLATE,
    viewSettings: {
      showColumnHeaders: true,
      showTaskCounts: true,
      showWipLimits: false,
      compactMode: false,
      groupBy: 'none' as const,
      sortBy: 'created' as const,
      sortOrder: 'asc' as const
    },
    isPublic: false
  });

  // Initialize form when editing
  useEffect(() => {
    if (editingLayout) {
      setLayoutForm({
        name: editingLayout.name,
        description: editingLayout.description,
        type: editingLayout.type,
        columns: editingLayout.columns,
        swimlanes: editingLayout.swimlanes,
        cardTemplate: editingLayout.cardTemplate,
        viewSettings: editingLayout.viewSettings,
        isPublic: editingLayout.isPublic
      });
    } else {
      // Reset to defaults for new layout
      setLayoutForm({
        name: '',
        description: '',
        type: 'kanban',
        columns: DEFAULT_COLUMNS,
        swimlanes: [],
        cardTemplate: DEFAULT_CARD_TEMPLATE,
        viewSettings: {
          showColumnHeaders: true,
          showTaskCounts: true,
          showWipLimits: false,
          compactMode: false,
          groupBy: 'none',
          sortBy: 'created',
          sortOrder: 'asc'
        },
        isPublic: false
      });
    }
  }, [editingLayout]);

  const handleSaveLayout = () => {
    if (!layoutForm.name.trim()) return;

    const layoutData = {
      name: layoutForm.name.trim(),
      description: layoutForm.description.trim(),
      type: layoutForm.type,
      columns: layoutForm.columns,
      swimlanes: layoutForm.swimlanes,
      cardTemplate: layoutForm.cardTemplate,
      viewSettings: layoutForm.viewSettings,
      isDefault: false,
      isPublic: layoutForm.isPublic,
      createdBy: 'current-user' // In real app, get from auth
    };

    onLayoutSave(layoutData);
    setIsDialogOpen(false);
    setEditingLayout(null);
  };

  const handleEditLayout = (layout: TaskBoardLayout) => {
    setEditingLayout(layout);
    setIsDialogOpen(true);
  };

  const handleDuplicateLayout = (layout: TaskBoardLayout) => {
    onLayoutDuplicate(layout);
  };

  const handleDeleteLayout = (layoutId: string) => {
    if (confirm('Are you sure you want to delete this layout?')) {
      onLayoutDelete(layoutId);
    }
  };

  const addColumn = () => {
    const newColumn: TaskBoardColumn = {
      id: `custom-${Date.now()}`,
      name: 'New Column',
      color: '#6b7280',
      position: layoutForm.columns.length,
      visible: true,
      isCustom: true
    };
    setLayoutForm(prev => ({
      ...prev,
      columns: [...prev.columns, newColumn]
    }));
  };

  const updateColumn = (columnId: string, updates: Partial<TaskBoardColumn>) => {
    setLayoutForm(prev => ({
      ...prev,
      columns: prev.columns.map(col => 
        col.id === columnId ? { ...col, ...updates } : col
      )
    }));
  };

  const removeColumn = (columnId: string) => {
    setLayoutForm(prev => ({
      ...prev,
      columns: prev.columns.filter(col => col.id !== columnId)
    }));
  };

  const addSwimlane = () => {
    const newSwimlane: TaskBoardSwimlane = {
      id: `swimlane-${Date.now()}`,
      name: 'New Swimlane',
      criteria: { type: 'assignee', value: '' },
      color: '#6b7280',
      position: layoutForm.swimlanes.length,
      visible: true,
      collapsed: false
    };
    setLayoutForm(prev => ({
      ...prev,
      swimlanes: [...prev.swimlanes, newSwimlane]
    }));
  };

  const updateSwimlane = (swimlaneId: string, updates: Partial<TaskBoardSwimlane>) => {
    setLayoutForm(prev => ({
      ...prev,
      swimlanes: prev.swimlanes.map(lane => 
        lane.id === swimlaneId ? { ...lane, ...updates } : lane
      )
    }));
  };

  const removeSwimlane = (swimlaneId: string) => {
    setLayoutForm(prev => ({
      ...prev,
      swimlanes: prev.swimlanes.filter(lane => lane.id !== swimlaneId)
    }));
  };

  return (
    <div className={className}>
      {/* Layout Selection */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Layout className="h-5 w-5" />
              Board Layouts
            </CardTitle>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm" onClick={() => setEditingLayout(null)}>
                  <Plus className="h-4 w-4 mr-2" />
                  New Layout
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>
                    {editingLayout ? 'Edit Layout' : 'Create New Layout'}
                  </DialogTitle>
                </DialogHeader>
                
                {/* Layout Editor */}
                <div className="space-y-6">
                  {/* Basic Info */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="layout-name">Layout Name</Label>
                      <Input
                        id="layout-name"
                        value={layoutForm.name}
                        onChange={(e) => setLayoutForm(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter layout name..."
                      />
                    </div>
                    <div>
                      <Label htmlFor="layout-type">Layout Type</Label>
                      <Select
                        value={layoutForm.type}
                        onValueChange={(value: any) => setLayoutForm(prev => ({ ...prev, type: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="kanban">Kanban Board</SelectItem>
                          <SelectItem value="swimlane">Swimlane View</SelectItem>
                          <SelectItem value="hybrid">Hybrid Layout</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="layout-description">Description</Label>
                    <Textarea
                      id="layout-description"
                      value={layoutForm.description}
                      onChange={(e) => setLayoutForm(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Describe this layout..."
                      rows={2}
                    />
                  </div>

                  {/* Layout Configuration Tabs */}
                  <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid w-full grid-cols-4">
                      <TabsTrigger value="columns">Columns</TabsTrigger>
                      <TabsTrigger value="swimlanes">Swimlanes</TabsTrigger>
                      <TabsTrigger value="cards">Card Template</TabsTrigger>
                      <TabsTrigger value="settings">View Settings</TabsTrigger>
                    </TabsList>

                    {/* Columns Tab */}
                    <TabsContent value="columns" className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">Board Columns</h4>
                        <Button size="sm" onClick={addColumn}>
                          <Plus className="h-4 w-4 mr-2" />
                          Add Column
                        </Button>
                      </div>

                      <div className="space-y-3">
                        {layoutForm.columns.map((column, index) => (
                          <div key={column.id} className="p-4 border rounded-lg">
                            <div className="grid grid-cols-12 gap-3 items-center">
                              <div className="col-span-3">
                                <Input
                                  value={column.name}
                                  onChange={(e) => updateColumn(column.id, { name: e.target.value })}
                                  placeholder="Column name"
                                />
                              </div>
                              <div className="col-span-2">
                                <Select
                                  value={column.status || ''}
                                  onValueChange={(value) => updateColumn(column.id, { status: value as TaskStatus })}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Status" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="">Custom</SelectItem>
                                    {TASK_STATUSES.map(status => (
                                      <SelectItem key={status} value={status}>{status}</SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                              <div className="col-span-2">
                                <Input
                                  type="color"
                                  value={column.color}
                                  onChange={(e) => updateColumn(column.id, { color: e.target.value })}
                                  className="h-10"
                                />
                              </div>
                              <div className="col-span-2">
                                <Input
                                  type="number"
                                  value={column.wipLimit || ''}
                                  onChange={(e) => updateColumn(column.id, { wipLimit: e.target.value ? parseInt(e.target.value) : undefined })}
                                  placeholder="WIP Limit"
                                />
                              </div>
                              <div className="col-span-1">
                                <Switch
                                  checked={column.visible}
                                  onCheckedChange={(checked) => updateColumn(column.id, { visible: checked })}
                                />
                              </div>
                              <div className="col-span-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeColumn(column.id)}
                                  disabled={!column.isCustom}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </TabsContent>

                    {/* Swimlanes Tab */}
                    <TabsContent value="swimlanes" className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">Swimlanes</h4>
                        <Button size="sm" onClick={addSwimlane}>
                          <Plus className="h-4 w-4 mr-2" />
                          Add Swimlane
                        </Button>
                      </div>

                      <div className="space-y-3">
                        {layoutForm.swimlanes.map((swimlane) => (
                          <div key={swimlane.id} className="p-4 border rounded-lg">
                            <div className="grid grid-cols-12 gap-3 items-center">
                              <div className="col-span-3">
                                <Input
                                  value={swimlane.name}
                                  onChange={(e) => updateSwimlane(swimlane.id, { name: e.target.value })}
                                  placeholder="Swimlane name"
                                />
                              </div>
                              <div className="col-span-2">
                                <Select
                                  value={swimlane.criteria.type}
                                  onValueChange={(value: any) => updateSwimlane(swimlane.id, {
                                    criteria: { ...swimlane.criteria, type: value }
                                  })}
                                >
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="assignee">Assignee</SelectItem>
                                    <SelectItem value="priority">Priority</SelectItem>
                                    <SelectItem value="tag">Tag</SelectItem>
                                    <SelectItem value="custom">Custom</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              <div className="col-span-3">
                                <Input
                                  value={swimlane.criteria.value}
                                  onChange={(e) => updateSwimlane(swimlane.id, {
                                    criteria: { ...swimlane.criteria, value: e.target.value }
                                  })}
                                  placeholder="Criteria value"
                                />
                              </div>
                              <div className="col-span-2">
                                <Input
                                  type="color"
                                  value={swimlane.color}
                                  onChange={(e) => updateSwimlane(swimlane.id, { color: e.target.value })}
                                  className="h-10"
                                />
                              </div>
                              <div className="col-span-1">
                                <Switch
                                  checked={swimlane.visible}
                                  onCheckedChange={(checked) => updateSwimlane(swimlane.id, { visible: checked })}
                                />
                              </div>
                              <div className="col-span-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeSwimlane(swimlane.id)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}

                        {layoutForm.swimlanes.length === 0 && (
                          <div className="text-center py-8 text-muted-foreground">
                            <Rows className="h-12 w-12 mx-auto mb-3 opacity-50" />
                            <p>No swimlanes configured</p>
                            <p className="text-sm">Add swimlanes to group tasks by criteria</p>
                          </div>
                        )}
                      </div>
                    </TabsContent>

                    {/* Card Template Tab */}
                    <TabsContent value="cards" className="space-y-4">
                      <h4 className="font-medium">Task Card Template</h4>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>Card Layout</Label>
                          <Select
                            value={layoutForm.cardTemplate.layout}
                            onValueChange={(value: any) => setLayoutForm(prev => ({
                              ...prev,
                              cardTemplate: { ...prev.cardTemplate, layout: value }
                            }))}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="minimal">Minimal</SelectItem>
                              <SelectItem value="compact">Compact</SelectItem>
                              <SelectItem value="detailed">Detailed</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label>Color Scheme</Label>
                          <Select
                            value={layoutForm.cardTemplate.colorScheme}
                            onValueChange={(value) => setLayoutForm(prev => ({
                              ...prev,
                              cardTemplate: { ...prev.cardTemplate, colorScheme: value }
                            }))}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="default">Default</SelectItem>
                              <SelectItem value="priority">Priority Based</SelectItem>
                              <SelectItem value="status">Status Based</SelectItem>
                              <SelectItem value="assignee">Assignee Based</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div>
                        <Label>Visible Fields</Label>
                        <div className="grid grid-cols-3 gap-3 mt-2">
                          {Object.entries(layoutForm.cardTemplate.fields).map(([field, visible]) => {
                            if (field === 'customFields') return null;
                            return (
                              <div key={field} className="flex items-center space-x-2">
                                <Switch
                                  checked={visible as boolean}
                                  onCheckedChange={(checked) => setLayoutForm(prev => ({
                                    ...prev,
                                    cardTemplate: {
                                      ...prev.cardTemplate,
                                      fields: { ...prev.cardTemplate.fields, [field]: checked }
                                    }
                                  }))}
                                />
                                <Label className="text-sm capitalize">{field.replace(/([A-Z])/g, ' $1')}</Label>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </TabsContent>

                    {/* View Settings Tab */}
                    <TabsContent value="settings" className="space-y-4">
                      <h4 className="font-medium">View Settings</h4>

                      <div className="grid grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <Label>Show Column Headers</Label>
                            <Switch
                              checked={layoutForm.viewSettings.showColumnHeaders}
                              onCheckedChange={(checked) => setLayoutForm(prev => ({
                                ...prev,
                                viewSettings: { ...prev.viewSettings, showColumnHeaders: checked }
                              }))}
                            />
                          </div>
                          <div className="flex items-center justify-between">
                            <Label>Show Task Counts</Label>
                            <Switch
                              checked={layoutForm.viewSettings.showTaskCounts}
                              onCheckedChange={(checked) => setLayoutForm(prev => ({
                                ...prev,
                                viewSettings: { ...prev.viewSettings, showTaskCounts: checked }
                              }))}
                            />
                          </div>
                          <div className="flex items-center justify-between">
                            <Label>Show WIP Limits</Label>
                            <Switch
                              checked={layoutForm.viewSettings.showWipLimits}
                              onCheckedChange={(checked) => setLayoutForm(prev => ({
                                ...prev,
                                viewSettings: { ...prev.viewSettings, showWipLimits: checked }
                              }))}
                            />
                          </div>
                          <div className="flex items-center justify-between">
                            <Label>Compact Mode</Label>
                            <Switch
                              checked={layoutForm.viewSettings.compactMode}
                              onCheckedChange={(checked) => setLayoutForm(prev => ({
                                ...prev,
                                viewSettings: { ...prev.viewSettings, compactMode: checked }
                              }))}
                            />
                          </div>
                        </div>

                        <div className="space-y-4">
                          <div>
                            <Label>Group By</Label>
                            <Select
                              value={layoutForm.viewSettings.groupBy}
                              onValueChange={(value: any) => setLayoutForm(prev => ({
                                ...prev,
                                viewSettings: { ...prev.viewSettings, groupBy: value }
                              }))}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">None</SelectItem>
                                <SelectItem value="assignee">Assignee</SelectItem>
                                <SelectItem value="priority">Priority</SelectItem>
                                <SelectItem value="tag">Tag</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label>Sort By</Label>
                            <Select
                              value={layoutForm.viewSettings.sortBy}
                              onValueChange={(value: any) => setLayoutForm(prev => ({
                                ...prev,
                                viewSettings: { ...prev.viewSettings, sortBy: value }
                              }))}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="created">Created Date</SelectItem>
                                <SelectItem value="updated">Updated Date</SelectItem>
                                <SelectItem value="priority">Priority</SelectItem>
                                <SelectItem value="dueDate">Due Date</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label>Sort Order</Label>
                            <Select
                              value={layoutForm.viewSettings.sortOrder}
                              onValueChange={(value: any) => setLayoutForm(prev => ({
                                ...prev,
                                viewSettings: { ...prev.viewSettings, sortOrder: value }
                              }))}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="asc">Ascending</SelectItem>
                                <SelectItem value="desc">Descending</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={layoutForm.isPublic}
                        onCheckedChange={(checked) => setLayoutForm(prev => ({ ...prev, isPublic: checked }))}
                      />
                      <Label>Make this layout public</Label>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleSaveLayout} disabled={!layoutForm.name.trim()}>
                        <Save className="h-4 w-4 mr-2" />
                        {editingLayout ? 'Update' : 'Create'} Layout
                      </Button>
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Quick Layout Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {availableLayouts.map(layout => (
              <motion.div
                key={layout.id}
                className={`p-4 rounded-lg border cursor-pointer transition-all ${
                  currentLayout.id === layout.id 
                    ? 'border-primary bg-primary/5' 
                    : 'border-border hover:border-primary/50'
                }`}
                onClick={() => onLayoutChange(layout)}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h4 className="font-medium">{layout.name}</h4>
                    <p className="text-sm text-muted-foreground">{layout.description}</p>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleEditLayout(layout)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleDuplicateLayout(layout)}>
                        <Copy className="h-4 w-4 mr-2" />
                        Duplicate
                      </DropdownMenuItem>
                      {!layout.isDefault && (
                        <DropdownMenuItem 
                          onClick={() => handleDeleteLayout(layout.id)}
                          className="text-destructive"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Badge variant="outline" className="text-xs">
                    {layout.type}
                  </Badge>
                  <span>{layout.columns.length} columns</span>
                  {layout.swimlanes.length > 0 && (
                    <span>{layout.swimlanes.length} swimlanes</span>
                  )}
                  {layout.isDefault && <Star className="h-3 w-3 text-yellow-500" />}
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
