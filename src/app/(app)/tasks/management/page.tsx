"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft,
  GitMerge,
  Clock,
  MessageSquare,
  FileTemplate,
  Target,
  Users,
  BarChart3,
  Settings
} from "lucide-react";
import Link from 'next/link';
import TaskDependenciesTable from '../components/TaskDependenciesTable';
import TimeEntriesTable from '../components/TimeEntriesTable';
import TaskCommentsTable from '../components/TaskCommentsTable';
import TaskTemplatesTable from '../components/TaskTemplatesTable';

export default function TaskManagementPage() {
  const [selectedTaskId, setSelectedTaskId] = useState<string | undefined>();

  // Mock statistics - in real implementation, these would come from Firebase
  const managementStats = {
    totalDependencies: 45,
    blockedTasks: 8,
    totalTimeLogged: 156.5, // hours
    activeTimers: 3,
    totalComments: 234,
    unreadComments: 12,
    totalTemplates: 18,
    popularTemplates: 5,
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button asChild variant="ghost" size="sm">
              <Link href="/tasks">
                <ArrowLeft className="mr-2 h-4 w-4" /> Back to Tasks
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold tracking-tight font-headline flex items-center">
            <Settings className="mr-3 h-8 w-8 text-primary" />
            Task Management
          </h1>
          <p className="text-muted-foreground">
            Advanced task management tools for dependencies, time tracking, collaboration, and templates.
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href="/tasks/analytics">
              <BarChart3 className="mr-2 h-4 w-4" /> Analytics
            </Link>
          </Button>
          <Button asChild>
            <Link href="/tasks/create">
              <Target className="mr-2 h-4 w-4" /> Create Task
            </Link>
          </Button>
        </div>
      </div>

      {/* Management Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dependencies</CardTitle>
            <GitMerge className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{managementStats.totalDependencies}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-red-600">{managementStats.blockedTasks} blocked</span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Time Logged</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{managementStats.totalTimeLogged}h</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">{managementStats.activeTimers} active timers</span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Comments</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{managementStats.totalComments}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-blue-600">{managementStats.unreadComments} unread</span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Templates</CardTitle>
            <FileTemplate className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{managementStats.totalTemplates}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-purple-600">{managementStats.popularTemplates} popular</span>
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Advanced Task Management Tables */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Advanced Task Management
          </CardTitle>
          <CardDescription>
            Comprehensive task management tools for complex project workflows.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="dependencies" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="dependencies">Dependencies</TabsTrigger>
              <TabsTrigger value="time-tracking">Time Tracking</TabsTrigger>
              <TabsTrigger value="comments">Comments</TabsTrigger>
              <TabsTrigger value="templates">Templates</TabsTrigger>
            </TabsList>
            
            <TabsContent value="dependencies" className="mt-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">Task Dependencies</h4>
                  <div className="flex gap-2">
                    <Badge variant="outline">
                      <GitMerge className="h-3 w-3 mr-1" />
                      {managementStats.totalDependencies} Total
                    </Badge>
                    <Badge variant="destructive">
                      <Target className="h-3 w-3 mr-1" />
                      {managementStats.blockedTasks} Blocked
                    </Badge>
                  </div>
                </div>
                <TaskDependenciesTable 
                  taskId={selectedTaskId}
                  showTaskFilter={!selectedTaskId}
                  onAddDependency={() => {
                    console.log('Add dependency');
                  }}
                  onRemoveDependency={(dependency) => {
                    console.log('Remove dependency:', dependency);
                  }}
                />
              </div>
            </TabsContent>
            
            <TabsContent value="time-tracking" className="mt-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">Time Tracking</h4>
                  <div className="flex gap-2">
                    <Badge variant="outline">
                      <Clock className="h-3 w-3 mr-1" />
                      {managementStats.totalTimeLogged}h Logged
                    </Badge>
                    <Badge variant="default">
                      <Users className="h-3 w-3 mr-1" />
                      {managementStats.activeTimers} Active
                    </Badge>
                  </div>
                </div>
                <TimeEntriesTable 
                  taskId={selectedTaskId}
                  showTaskFilter={!selectedTaskId}
                  showUserFilter={true}
                  onAddTimeEntry={() => {
                    console.log('Add time entry');
                  }}
                  onEditTimeEntry={(entry) => {
                    console.log('Edit time entry:', entry);
                  }}
                />
              </div>
            </TabsContent>
            
            <TabsContent value="comments" className="mt-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">Task Comments & Collaboration</h4>
                  <div className="flex gap-2">
                    <Badge variant="outline">
                      <MessageSquare className="h-3 w-3 mr-1" />
                      {managementStats.totalComments} Comments
                    </Badge>
                    <Badge variant="secondary">
                      <Users className="h-3 w-3 mr-1" />
                      {managementStats.unreadComments} Unread
                    </Badge>
                  </div>
                </div>
                <TaskCommentsTable 
                  taskId={selectedTaskId}
                  showTaskFilter={!selectedTaskId}
                  showUserFilter={true}
                  onAddComment={() => {
                    console.log('Add comment');
                  }}
                  onReplyToComment={(comment) => {
                    console.log('Reply to comment:', comment);
                  }}
                />
              </div>
            </TabsContent>
            
            <TabsContent value="templates" className="mt-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">Task Templates</h4>
                  <div className="flex gap-2">
                    <Badge variant="outline">
                      <FileTemplate className="h-3 w-3 mr-1" />
                      {managementStats.totalTemplates} Templates
                    </Badge>
                    <Badge variant="secondary">
                      <Target className="h-3 w-3 mr-1" />
                      {managementStats.popularTemplates} Popular
                    </Badge>
                  </div>
                </div>
                <TaskTemplatesTable 
                  showCategoryFilter={true}
                  onCreateFromTemplate={(template) => {
                    console.log('Create tasks from template:', template);
                  }}
                  onEditTemplate={(template) => {
                    console.log('Edit template:', template);
                  }}
                />
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Task Selection Helper */}
      {!selectedTaskId && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Task Selection
            </CardTitle>
            <CardDescription>
              Select a specific task to filter dependencies, time entries, and comments.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <Button 
                variant="outline" 
                onClick={() => setSelectedTaskId('demo-task-1')}
              >
                Select Demo Task
              </Button>
              <Button 
                variant="ghost" 
                onClick={() => setSelectedTaskId(undefined)}
              >
                Show All Tasks
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
