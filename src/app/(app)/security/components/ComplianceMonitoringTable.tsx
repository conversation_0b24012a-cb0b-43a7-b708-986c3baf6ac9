"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { 
  Shield, 
  Eye, 
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  FileText,
  Settings,
  Award,
  Activity,
  Calendar,
  Target,
  TrendingUp,
  Users,
  Building
} from 'lucide-react';

interface ComplianceFramework {
  id: string;
  name: string;
  description: string;
  type: 'gdpr' | 'hipaa' | 'sox' | 'pci_dss' | 'iso27001' | 'custom';
  status: 'compliant' | 'non_compliant' | 'partial' | 'under_review';
  complianceScore: number;
  lastAssessment: Date;
  nextAssessment: Date;
  requirements: {
    total: number;
    met: number;
    pending: number;
    failed: number;
  };
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  assignedTo: string;
  evidence: string[];
  violations: number;
  createdAt: Date;
  updatedAt: Date;
}

interface ComplianceMonitoringTableProps {
  className?: string;
  onFrameworkView?: (framework: ComplianceFramework) => void;
  onFrameworkAssess?: (frameworkId: string) => void;
  onFrameworkUpdate?: (frameworkId: string) => void;
}

export default function ComplianceMonitoringTable({ 
  className, 
  onFrameworkView,
  onFrameworkAssess,
  onFrameworkUpdate
}: ComplianceMonitoringTableProps) {
  const [frameworks, setFrameworks] = useState<ComplianceFramework[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchFrameworks = async () => {
      try {
        setIsLoading(true);
        
        // Mock compliance frameworks data
        const frameworksData: ComplianceFramework[] = [
          {
            id: '1',
            name: 'GDPR Compliance',
            description: 'General Data Protection Regulation compliance framework',
            type: 'gdpr',
            status: 'compliant',
            complianceScore: 92,
            lastAssessment: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
            nextAssessment: new Date(Date.now() + 85 * 24 * 60 * 60 * 1000), // 85 days from now
            requirements: {
              total: 25,
              met: 23,
              pending: 1,
              failed: 1,
            },
            riskLevel: 'low',
            assignedTo: '<EMAIL>',
            evidence: ['privacy_policy.pdf', 'data_mapping.xlsx', 'consent_records.json'],
            violations: 0,
            createdAt: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000),
            updatedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          },
          {
            id: '2',
            name: 'ISO 27001',
            description: 'Information Security Management System standard',
            type: 'iso27001',
            status: 'partial',
            complianceScore: 78,
            lastAssessment: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 14 days ago
            nextAssessment: new Date(Date.now() + 76 * 24 * 60 * 60 * 1000), // 76 days from now
            requirements: {
              total: 114,
              met: 89,
              pending: 15,
              failed: 10,
            },
            riskLevel: 'medium',
            assignedTo: '<EMAIL>',
            evidence: ['isms_policy.pdf', 'risk_assessment.xlsx', 'audit_report.pdf'],
            violations: 3,
            createdAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
            updatedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
          },
          {
            id: '3',
            name: 'SOX Compliance',
            description: 'Sarbanes-Oxley Act financial reporting compliance',
            type: 'sox',
            status: 'under_review',
            complianceScore: 85,
            lastAssessment: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
            nextAssessment: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
            requirements: {
              total: 18,
              met: 15,
              pending: 2,
              failed: 1,
            },
            riskLevel: 'medium',
            assignedTo: '<EMAIL>',
            evidence: ['financial_controls.pdf', 'audit_trail.xlsx'],
            violations: 1,
            createdAt: new Date(Date.now() - 200 * 24 * 60 * 60 * 1000),
            updatedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          },
          {
            id: '4',
            name: 'PCI DSS',
            description: 'Payment Card Industry Data Security Standard',
            type: 'pci_dss',
            status: 'non_compliant',
            complianceScore: 45,
            lastAssessment: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000), // 45 days ago
            nextAssessment: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
            requirements: {
              total: 12,
              met: 5,
              pending: 3,
              failed: 4,
            },
            riskLevel: 'high',
            assignedTo: '<EMAIL>',
            evidence: ['network_diagram.pdf', 'vulnerability_scan.pdf'],
            violations: 8,
            createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
            updatedAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
          },
          {
            id: '5',
            name: 'HIPAA Compliance',
            description: 'Health Insurance Portability and Accountability Act',
            type: 'hipaa',
            status: 'compliant',
            complianceScore: 96,
            lastAssessment: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000), // 21 days ago
            nextAssessment: new Date(Date.now() + 69 * 24 * 60 * 60 * 1000), // 69 days from now
            requirements: {
              total: 20,
              met: 19,
              pending: 1,
              failed: 0,
            },
            riskLevel: 'low',
            assignedTo: '<EMAIL>',
            evidence: ['phi_policy.pdf', 'access_controls.xlsx', 'training_records.pdf'],
            violations: 0,
            createdAt: new Date(Date.now() - 150 * 24 * 60 * 60 * 1000),
            updatedAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000),
          },
        ];

        setFrameworks(frameworksData);
      } catch (error) {
        console.error('Error fetching compliance frameworks:', error);
        toast({
          title: "Error",
          description: "Failed to load compliance frameworks. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchFrameworks();
  }, [toast]);

  const getStatusBadge = (status: ComplianceFramework['status']) => {
    const statusConfig = {
      compliant: { variant: 'default' as const, label: 'Compliant', className: 'bg-green-100 text-green-700', icon: CheckCircle },
      non_compliant: { variant: 'destructive' as const, label: 'Non-Compliant', className: 'bg-red-100 text-red-700', icon: XCircle },
      partial: { variant: 'secondary' as const, label: 'Partial', className: 'bg-yellow-100 text-yellow-700', icon: AlertTriangle },
      under_review: { variant: 'outline' as const, label: 'Under Review', className: 'bg-blue-100 text-blue-700', icon: Eye },
    };
    const config = statusConfig[status];
    const IconComponent = config.icon;
    return (
      <Badge variant={config.variant} className={`${config.className} flex items-center w-fit`}>
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getRiskBadge = (riskLevel: ComplianceFramework['riskLevel']) => {
    const riskConfig = {
      low: { variant: 'outline' as const, label: 'Low', className: 'text-green-700 border-green-300' },
      medium: { variant: 'secondary' as const, label: 'Medium', className: 'bg-yellow-100 text-yellow-700' },
      high: { variant: 'destructive' as const, label: 'High', className: 'bg-orange-100 text-orange-700' },
      critical: { variant: 'destructive' as const, label: 'Critical', className: 'bg-red-100 text-red-700' },
    };
    const config = riskConfig[riskLevel];
    return (
      <Badge variant={config.variant} className={config.className}>
        {config.label}
      </Badge>
    );
  };

  const getFrameworkIcon = (type: ComplianceFramework['type']) => {
    const iconMap = {
      gdpr: <Shield className="h-4 w-4 text-blue-500" />,
      hipaa: <FileText className="h-4 w-4 text-green-500" />,
      sox: <Building className="h-4 w-4 text-purple-500" />,
      pci_dss: <Target className="h-4 w-4 text-orange-500" />,
      iso27001: <Award className="h-4 w-4 text-blue-600" />,
      custom: <Settings className="h-4 w-4 text-gray-500" />,
    };
    return iconMap[type] || <Shield className="h-4 w-4 text-gray-500" />;
  };

  const columns: AdvancedTableColumn<ComplianceFramework>[] = [
    {
      accessorKey: 'name',
      title: 'Framework',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const framework = row.original;
        return (
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-primary/10">
              {getFrameworkIcon(framework.type)}
            </div>
            <div className="min-w-0 flex-1">
              <div className="font-medium">{framework.name}</div>
              <div className="text-xs text-muted-foreground truncate">
                {framework.description}
              </div>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline" className="text-xs uppercase">
                  {framework.type.replace('_', ' ')}
                </Badge>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      title: 'Status',
      sortable: true,
      filterable: true,
      cell: ({ row }) => getStatusBadge(row.original.status),
    },
    {
      accessorKey: 'complianceScore',
      title: 'Score',
      sortable: true,
      cell: ({ row }) => {
        const score = row.original.complianceScore;
        return (
          <div className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
            <Badge variant={score >= 90 ? 'default' : score >= 70 ? 'secondary' : 'destructive'}>
              {score}%
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'requirements',
      title: 'Requirements',
      sortable: false,
      cell: ({ row }) => {
        const req = row.original.requirements;
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{req.met}/{req.total}</span>
            </div>
            <div className="flex gap-1 text-xs">
              <Badge variant="outline" className="bg-green-50 text-green-700">
                {req.met} met
              </Badge>
              {req.pending > 0 && (
                <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
                  {req.pending} pending
                </Badge>
              )}
              {req.failed > 0 && (
                <Badge variant="outline" className="bg-red-50 text-red-700">
                  {req.failed} failed
                </Badge>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'riskLevel',
      title: 'Risk Level',
      sortable: true,
      filterable: true,
      cell: ({ row }) => getRiskBadge(row.original.riskLevel),
    },
    {
      accessorKey: 'violations',
      title: 'Violations',
      sortable: true,
      cell: ({ row }) => {
        const violations = row.original.violations;
        return (
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            <Badge variant={violations === 0 ? 'outline' : violations < 5 ? 'secondary' : 'destructive'}>
              {violations}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'assignedTo',
      title: 'Assigned To',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const assignedTo = row.original.assignedTo;
        return (
          <div className="flex items-center gap-1">
            <Users className="h-3 w-3 text-muted-foreground" />
            <span className="text-sm">{assignedTo}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'nextAssessment',
      title: 'Next Assessment',
      sortable: true,
      cell: ({ row }) => {
        const nextAssessment = row.original.nextAssessment;
        const daysUntil = Math.ceil((nextAssessment.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3 text-muted-foreground" />
              <span className="text-sm">{format(nextAssessment, 'MMM dd, yyyy')}</span>
            </div>
            <Badge variant={daysUntil < 30 ? 'destructive' : daysUntil < 60 ? 'secondary' : 'outline'} className="text-xs">
              {daysUntil} days
            </Badge>
          </div>
        );
      },
    },
  ];

  const rowActions = (row: ComplianceFramework): AdvancedTableRowAction<ComplianceFramework>[] => {
    const actions: AdvancedTableRowAction<ComplianceFramework>[] = [
      {
        type: 'view',
        label: 'View Details',
        onClick: () => onFrameworkView?.(row),
        icon: <Eye className="h-4 w-4" />,
      },
    ];

    if (onFrameworkAssess) {
      actions.push({
        type: 'custom',
        label: 'Run Assessment',
        onClick: () => onFrameworkAssess(row.id),
        icon: <Activity className="h-4 w-4" />,
        variant: 'default',
      });
    }

    if (onFrameworkUpdate) {
      actions.push({
        type: 'edit',
        label: 'Update Framework',
        onClick: () => onFrameworkUpdate(row.id),
        icon: <Settings className="h-4 w-4" />,
      });
    }

    actions.push({
      type: 'custom',
      label: 'Generate Report',
      onClick: () => {
        toast({
          title: "Generate Report",
          description: `Generating compliance report for ${row.name}...`,
        });
      },
      icon: <FileText className="h-4 w-4" />,
    });

    return actions;
  };

  const filters = [
    {
      id: 'type',
      label: 'Framework Type',
      type: 'select' as const,
      options: [
        { label: 'GDPR', value: 'gdpr' },
        { label: 'HIPAA', value: 'hipaa' },
        { label: 'SOX', value: 'sox' },
        { label: 'PCI DSS', value: 'pci_dss' },
        { label: 'ISO 27001', value: 'iso27001' },
        { label: 'Custom', value: 'custom' },
      ],
    },
    {
      id: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { label: 'Compliant', value: 'compliant' },
        { label: 'Non-Compliant', value: 'non_compliant' },
        { label: 'Partial', value: 'partial' },
        { label: 'Under Review', value: 'under_review' },
      ],
    },
    {
      id: 'riskLevel',
      label: 'Risk Level',
      type: 'select' as const,
      options: [
        { label: 'Critical', value: 'critical' },
        { label: 'High', value: 'high' },
        { label: 'Medium', value: 'medium' },
        { label: 'Low', value: 'low' },
      ],
    },
  ];

  const bulkActions = [
    {
      id: 'assess',
      label: 'Run Assessment',
      icon: <Activity className="h-4 w-4" />,
      onClick: (selectedRows: ComplianceFramework[]) => {
        toast({
          title: "Bulk Assessment",
          description: `Running assessment for ${selectedRows.length} framework(s)...`,
        });
      },
    },
    {
      id: 'report',
      label: 'Generate Reports',
      icon: <FileText className="h-4 w-4" />,
      onClick: (selectedRows: ComplianceFramework[]) => {
        toast({
          title: "Generate Reports",
          description: `Generating reports for ${selectedRows.length} framework(s)...`,
        });
      },
    },
  ];

  return (
    <AdvancedDataTable
      data={frameworks}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      bulkActions={bulkActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="compliance-monitoring"
      searchPlaceholder="Search compliance frameworks..."
      emptyMessage="No compliance frameworks configured. Add frameworks to monitor compliance."
      loading={isLoading}
      className={className}
      variant="default"
      defaultSorting={[{ id: 'nextAssessment', desc: false }]}
    />
  );
}
