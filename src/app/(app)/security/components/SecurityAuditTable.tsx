"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { 
  Shield, 
  Eye, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Globe,
  User,
  Lock,
  Unlock,
  UserCheck,
  UserX,
  Key,
  Settings,
  Database,
  FileText,
  Zap,
  Activity,
  Calendar
} from 'lucide-react';
// import { getSecurityEvents } from '@/services/firestoreService';

interface SecurityEvent {
  id: string;
  timestamp: Date;
  type: 'login_attempt' | 'login_success' | 'login_failure' | 'mfa_enabled' | 'mfa_disabled' | 'password_change' | 'suspicious_activity' | 'account_locked' | 'permission_change' | 'data_access';
  user: {
    id: string;
    name: string;
    email: string;
  };
  details: string;
  ipAddress: string;
  userAgent: string;
  location: string;
  riskScore: number;
  status: 'success' | 'failure' | 'warning' | 'blocked';
  metadata?: Record<string, any>;
}

interface SecurityAuditTableProps {
  className?: string;
  onEventView?: (event: SecurityEvent) => void;
  maxResults?: number;
}

export default function SecurityAuditTable({ 
  className, 
  onEventView,
  maxResults = 100
}: SecurityAuditTableProps) {
  const [events, setEvents] = useState<SecurityEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setIsLoading(true);
        
        // Mock security events data
        const eventsData: SecurityEvent[] = [
          {
            id: '1',
            timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
            type: 'login_success',
            user: {
              id: 'user1',
              name: 'John Smith',
              email: '<EMAIL>',
            },
            details: 'Successful login from trusted device',
            ipAddress: '*************',
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            location: 'New York, US',
            riskScore: 15,
            status: 'success',
          },
          {
            id: '2',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
            type: 'login_failure',
            user: {
              id: 'user2',
              name: 'Unknown User',
              email: '<EMAIL>',
            },
            details: 'Failed login attempt - invalid credentials',
            ipAddress: '************',
            userAgent: 'curl/7.68.0',
            location: 'Unknown, RU',
            riskScore: 85,
            status: 'blocked',
          },
          {
            id: '3',
            timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
            type: 'permission_change',
            user: {
              id: 'admin1',
              name: 'Admin User',
              email: '<EMAIL>',
            },
            details: 'User permissions <NAME_EMAIL>',
            ipAddress: '************',
            userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
            location: 'San Francisco, US',
            riskScore: 25,
            status: 'success',
          },
          {
            id: '4',
            timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
            type: 'suspicious_activity',
            user: {
              id: 'user3',
              name: 'Jane Doe',
              email: '<EMAIL>',
            },
            details: 'Unusual data access pattern detected',
            ipAddress: '*************',
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
            location: 'Chicago, US',
            riskScore: 65,
            status: 'warning',
          },
          {
            id: '5',
            timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
            type: 'mfa_enabled',
            user: {
              id: 'user4',
              name: 'Bob Wilson',
              email: '<EMAIL>',
            },
            details: 'Multi-factor authentication enabled',
            ipAddress: '*************',
            userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0)',
            location: 'Los Angeles, US',
            riskScore: 10,
            status: 'success',
          },
        ];

        setEvents(eventsData.slice(0, maxResults));
      } catch (error) {
        console.error('Error fetching security events:', error);
        toast({
          title: "Error",
          description: "Failed to load security events. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchEvents();
  }, [maxResults, toast]);

  const getEventIcon = (type: SecurityEvent['type']) => {
    const iconMap = {
      login_attempt: <User className="h-4 w-4 text-blue-500" />,
      login_success: <CheckCircle className="h-4 w-4 text-green-500" />,
      login_failure: <XCircle className="h-4 w-4 text-red-500" />,
      mfa_enabled: <Shield className="h-4 w-4 text-green-500" />,
      mfa_disabled: <Shield className="h-4 w-4 text-yellow-500" />,
      password_change: <Key className="h-4 w-4 text-blue-500" />,
      suspicious_activity: <AlertTriangle className="h-4 w-4 text-orange-500" />,
      account_locked: <Lock className="h-4 w-4 text-red-500" />,
      permission_change: <Settings className="h-4 w-4 text-purple-500" />,
      data_access: <Database className="h-4 w-4 text-blue-500" />,
    };
    return iconMap[type] || <Activity className="h-4 w-4 text-gray-500" />;
  };

  const getRiskScoreColor = (score: number) => {
    if (score >= 80) return 'text-red-600';
    if (score >= 60) return 'text-orange-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getStatusBadge = (status: SecurityEvent['status']) => {
    const statusConfig = {
      success: { variant: 'default' as const, label: 'Success', className: 'bg-green-100 text-green-700' },
      failure: { variant: 'destructive' as const, label: 'Failure', className: 'bg-red-100 text-red-700' },
      warning: { variant: 'secondary' as const, label: 'Warning', className: 'bg-yellow-100 text-yellow-700' },
      blocked: { variant: 'destructive' as const, label: 'Blocked', className: 'bg-red-100 text-red-700' },
    };
    const config = statusConfig[status];
    return (
      <Badge variant={config.variant} className={config.className}>
        {config.label}
      </Badge>
    );
  };

  const columns: AdvancedTableColumn<SecurityEvent>[] = [
    {
      accessorKey: 'type',
      title: 'Event Type',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const event = row.original;
        return (
          <div className="flex items-center gap-2">
            {getEventIcon(event.type)}
            <span className="font-medium capitalize">
              {event.type.replace('_', ' ')}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'user',
      title: 'User',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const user = row.original.user;
        return (
          <div className="space-y-1">
            <div className="font-medium">{user.name}</div>
            <div className="text-xs text-muted-foreground">{user.email}</div>
          </div>
        );
      },
    },
    {
      accessorKey: 'details',
      title: 'Details',
      sortable: false,
      cell: ({ row }) => (
        <div className="max-w-xs truncate" title={row.original.details}>
          {row.original.details}
        </div>
      ),
    },
    {
      accessorKey: 'location',
      title: 'Location',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const event = row.original;
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <Globe className="h-3 w-3 text-muted-foreground" />
              <span className="text-sm">{event.location}</span>
            </div>
            <div className="text-xs text-muted-foreground">{event.ipAddress}</div>
          </div>
        );
      },
    },
    {
      accessorKey: 'riskScore',
      title: 'Risk Score',
      sortable: true,
      cell: ({ row }) => {
        const score = row.original.riskScore;
        return (
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4 text-muted-foreground" />
            <span className={`font-medium ${getRiskScoreColor(score)}`}>
              {score}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      title: 'Status',
      sortable: true,
      filterable: true,
      cell: ({ row }) => getStatusBadge(row.original.status),
    },
    {
      accessorKey: 'timestamp',
      title: 'Time',
      sortable: true,
      cell: ({ row }) => {
        const timestamp = row.original.timestamp;
        return (
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-muted-foreground" />
            <span className="text-sm">{format(timestamp, 'MMM dd, HH:mm')}</span>
          </div>
        );
      },
    },
  ];

  const rowActions = (row: SecurityEvent): AdvancedTableRowAction<SecurityEvent>[] => [
    {
      type: 'view',
      label: 'View Details',
      onClick: () => onEventView?.(row),
      icon: <Eye className="h-4 w-4" />,
    },
    {
      type: 'custom',
      label: 'Investigate',
      onClick: () => {
        toast({
          title: "Investigation Started",
          description: `Starting investigation for event ${row.id}...`,
        });
      },
      icon: <AlertTriangle className="h-4 w-4" />,
    },
  ];

  const filters = [
    {
      id: 'type',
      label: 'Event Type',
      type: 'select' as const,
      options: [
        { label: 'Login Success', value: 'login_success' },
        { label: 'Login Failure', value: 'login_failure' },
        { label: 'MFA Events', value: 'mfa_enabled' },
        { label: 'Password Changes', value: 'password_change' },
        { label: 'Suspicious Activity', value: 'suspicious_activity' },
        { label: 'Permission Changes', value: 'permission_change' },
        { label: 'Data Access', value: 'data_access' },
      ],
    },
    {
      id: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { label: 'Success', value: 'success' },
        { label: 'Failure', value: 'failure' },
        { label: 'Warning', value: 'warning' },
        { label: 'Blocked', value: 'blocked' },
      ],
    },
    {
      id: 'riskScore',
      label: 'Risk Level',
      type: 'select' as const,
      options: [
        { label: 'Low (0-39)', value: 'low' },
        { label: 'Medium (40-59)', value: 'medium' },
        { label: 'High (60-79)', value: 'high' },
        { label: 'Critical (80+)', value: 'critical' },
      ],
    },
  ];

  const bulkActions = [
    {
      id: 'investigate',
      label: 'Investigate Selected',
      icon: <AlertTriangle className="h-4 w-4" />,
      onClick: (selectedRows: SecurityEvent[]) => {
        toast({
          title: "Bulk Investigation",
          description: `Starting investigation for ${selectedRows.length} event(s)...`,
        });
      },
    },
    {
      id: 'export',
      label: 'Export Selected',
      icon: <FileText className="h-4 w-4" />,
      onClick: (selectedRows: SecurityEvent[]) => {
        toast({
          title: "Export Events",
          description: `Exporting ${selectedRows.length} security event(s)...`,
        });
      },
    },
  ];

  return (
    <AdvancedDataTable
      data={events}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      bulkActions={bulkActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="security-audit-log"
      searchPlaceholder="Search security events..."
      emptyMessage="No security events found. Events will appear here as they occur."
      loading={isLoading}
      className={className}
      variant="default"
      defaultSorting={[{ id: 'timestamp', desc: true }]}
    />
  );
}
