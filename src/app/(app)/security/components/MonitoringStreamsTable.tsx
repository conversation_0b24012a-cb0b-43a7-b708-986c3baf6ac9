"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { 
  Activity, 
  Eye, 
  Play,
  Pause,
  Settings,
  Zap,
  Clock,
  Database,
  Server,
  Network,
  Shield,
  AlertTriangle,
  CheckCircle,
  Calendar,
  BarChart3,
  TrendingUp
} from 'lucide-react';

interface MonitoringStream {
  id: string;
  name: string;
  description: string;
  type: 'network' | 'application' | 'database' | 'system' | 'user_activity' | 'file_integrity';
  status: 'active' | 'inactive' | 'error' | 'maintenance';
  source: string;
  eventsPerMinute: number;
  totalEvents: number;
  lastEvent: Date;
  alertsGenerated: number;
  healthScore: number;
  retentionDays: number;
  configuration: {
    filters: string[];
    thresholds: Record<string, number>;
    alerting: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

interface MonitoringStreamsTableProps {
  className?: string;
  onStreamView?: (stream: MonitoringStream) => void;
  onStreamToggle?: (streamId: string, enabled: boolean) => void;
  onStreamConfigure?: (streamId: string) => void;
}

export default function MonitoringStreamsTable({ 
  className, 
  onStreamView,
  onStreamToggle,
  onStreamConfigure
}: MonitoringStreamsTableProps) {
  const [streams, setStreams] = useState<MonitoringStream[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchStreams = async () => {
      try {
        setIsLoading(true);
        
        // Mock monitoring streams data
        const streamsData: MonitoringStream[] = [
          {
            id: '1',
            name: 'Network Traffic Monitor',
            description: 'Real-time monitoring of network traffic and connections',
            type: 'network',
            status: 'active',
            source: 'firewall.company.com',
            eventsPerMinute: 1250,
            totalEvents: 2847392,
            lastEvent: new Date(Date.now() - 30 * 1000), // 30 seconds ago
            alertsGenerated: 15,
            healthScore: 98,
            retentionDays: 90,
            configuration: {
              filters: ['tcp', 'udp', 'icmp'],
              thresholds: { bandwidth: 1000, connections: 500 },
              alerting: true,
            },
            createdAt: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000),
            updatedAt: new Date(Date.now() - 30 * 1000),
          },
          {
            id: '2',
            name: 'Application Security Log',
            description: 'Application-level security events and authentication logs',
            type: 'application',
            status: 'active',
            source: 'app-server-01.company.com',
            eventsPerMinute: 450,
            totalEvents: 892456,
            lastEvent: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
            alertsGenerated: 8,
            healthScore: 95,
            retentionDays: 365,
            configuration: {
              filters: ['auth', 'error', 'warning'],
              thresholds: { failed_logins: 10, errors: 50 },
              alerting: true,
            },
            createdAt: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000),
            updatedAt: new Date(Date.now() - 2 * 60 * 1000),
          },
          {
            id: '3',
            name: 'Database Access Monitor',
            description: 'Database queries, access patterns, and security events',
            type: 'database',
            status: 'error',
            source: 'db-primary.company.com',
            eventsPerMinute: 0,
            totalEvents: 1456789,
            lastEvent: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
            alertsGenerated: 3,
            healthScore: 25,
            retentionDays: 180,
            configuration: {
              filters: ['select', 'insert', 'update', 'delete'],
              thresholds: { queries_per_second: 100, failed_queries: 20 },
              alerting: true,
            },
            createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
            updatedAt: new Date(Date.now() - 15 * 60 * 1000),
          },
          {
            id: '4',
            name: 'System Performance Monitor',
            description: 'System resources, performance metrics, and health indicators',
            type: 'system',
            status: 'active',
            source: 'monitoring.company.com',
            eventsPerMinute: 850,
            totalEvents: 5234567,
            lastEvent: new Date(Date.now() - 1 * 60 * 1000), // 1 minute ago
            alertsGenerated: 22,
            healthScore: 88,
            retentionDays: 30,
            configuration: {
              filters: ['cpu', 'memory', 'disk', 'network'],
              thresholds: { cpu_usage: 80, memory_usage: 85, disk_usage: 90 },
              alerting: true,
            },
            createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
            updatedAt: new Date(Date.now() - 1 * 60 * 1000),
          },
          {
            id: '5',
            name: 'User Activity Tracker',
            description: 'User behavior analysis and anomaly detection',
            type: 'user_activity',
            status: 'maintenance',
            source: 'analytics.company.com',
            eventsPerMinute: 0,
            totalEvents: 3456789,
            lastEvent: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
            alertsGenerated: 5,
            healthScore: 0,
            retentionDays: 365,
            configuration: {
              filters: ['login', 'logout', 'access', 'download'],
              thresholds: { unusual_activity: 5, failed_attempts: 3 },
              alerting: false,
            },
            createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
            updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          },
        ];

        setStreams(streamsData);
      } catch (error) {
        console.error('Error fetching monitoring streams:', error);
        toast({
          title: "Error",
          description: "Failed to load monitoring streams. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchStreams();
  }, [toast]);

  const getStatusBadge = (status: MonitoringStream['status']) => {
    const statusConfig = {
      active: { variant: 'default' as const, label: 'Active', className: 'bg-green-100 text-green-700', icon: CheckCircle },
      inactive: { variant: 'secondary' as const, label: 'Inactive', className: 'bg-gray-100 text-gray-700', icon: Pause },
      error: { variant: 'destructive' as const, label: 'Error', className: 'bg-red-100 text-red-700', icon: AlertTriangle },
      maintenance: { variant: 'outline' as const, label: 'Maintenance', className: 'bg-yellow-100 text-yellow-700', icon: Settings },
    };
    const config = statusConfig[status];
    const IconComponent = config.icon;
    return (
      <Badge variant={config.variant} className={`${config.className} flex items-center w-fit`}>
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getTypeIcon = (type: MonitoringStream['type']) => {
    const iconMap = {
      network: <Network className="h-4 w-4 text-blue-500" />,
      application: <Server className="h-4 w-4 text-green-500" />,
      database: <Database className="h-4 w-4 text-purple-500" />,
      system: <Activity className="h-4 w-4 text-orange-500" />,
      user_activity: <Shield className="h-4 w-4 text-red-500" />,
      file_integrity: <CheckCircle className="h-4 w-4 text-teal-500" />,
    };
    return iconMap[type] || <Activity className="h-4 w-4 text-gray-500" />;
  };

  const columns: AdvancedTableColumn<MonitoringStream>[] = [
    {
      accessorKey: 'name',
      title: 'Monitoring Stream',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const stream = row.original;
        return (
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-primary/10">
              {getTypeIcon(stream.type)}
            </div>
            <div className="min-w-0 flex-1">
              <div className="font-medium">{stream.name}</div>
              <div className="text-xs text-muted-foreground truncate">
                {stream.description}
              </div>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline" className="text-xs capitalize">
                  {stream.type.replace('_', ' ')}
                </Badge>
                <span className="text-xs text-muted-foreground">{stream.source}</span>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      title: 'Status',
      sortable: true,
      filterable: true,
      cell: ({ row }) => getStatusBadge(row.original.status),
    },
    {
      accessorKey: 'eventsPerMinute',
      title: 'Events/Min',
      sortable: true,
      cell: ({ row }) => {
        const epm = row.original.eventsPerMinute;
        return (
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">{epm.toLocaleString()}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'totalEvents',
      title: 'Total Events',
      sortable: true,
      cell: ({ row }) => {
        const total = row.original.totalEvents;
        return (
          <div className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">{total.toLocaleString()}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'alertsGenerated',
      title: 'Alerts',
      sortable: true,
      cell: ({ row }) => {
        const alerts = row.original.alertsGenerated;
        return (
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            <Badge variant={alerts === 0 ? 'outline' : alerts < 10 ? 'secondary' : 'destructive'}>
              {alerts}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'healthScore',
      title: 'Health',
      sortable: true,
      cell: ({ row }) => {
        const health = row.original.healthScore;
        return (
          <div className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
            <Badge variant={health >= 90 ? 'default' : health >= 70 ? 'secondary' : 'destructive'}>
              {health}%
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'lastEvent',
      title: 'Last Event',
      sortable: true,
      cell: ({ row }) => {
        const lastEvent = row.original.lastEvent;
        const minutesAgo = Math.floor((Date.now() - lastEvent.getTime()) / (1000 * 60));
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3 text-muted-foreground" />
              <span className="text-sm">{format(lastEvent, 'HH:mm:ss')}</span>
            </div>
            <Badge variant="outline" className="text-xs">
              {minutesAgo < 1 ? 'Just now' : `${minutesAgo}m ago`}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'retentionDays',
      title: 'Retention',
      sortable: true,
      cell: ({ row }) => {
        const retention = row.original.retentionDays;
        return (
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3 text-muted-foreground" />
            <span className="text-sm">{retention} days</span>
          </div>
        );
      },
    },
  ];

  const rowActions = (row: MonitoringStream): AdvancedTableRowAction<MonitoringStream>[] => {
    const actions: AdvancedTableRowAction<MonitoringStream>[] = [
      {
        type: 'view',
        label: 'View Stream',
        onClick: () => onStreamView?.(row),
        icon: <Eye className="h-4 w-4" />,
      },
    ];

    if (onStreamConfigure) {
      actions.push({
        type: 'edit',
        label: 'Configure',
        onClick: () => onStreamConfigure(row.id),
        icon: <Settings className="h-4 w-4" />,
      });
    }

    if (onStreamToggle) {
      const isActive = row.status === 'active';
      actions.push({
        type: 'custom',
        label: isActive ? 'Pause Stream' : 'Start Stream',
        onClick: () => onStreamToggle(row.id, !isActive),
        icon: isActive ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />,
        variant: isActive ? 'secondary' : 'default',
      });
    }

    actions.push({
      type: 'custom',
      label: 'View Logs',
      onClick: () => {
        toast({
          title: "View Logs",
          description: `Opening logs for ${row.name}...`,
        });
      },
      icon: <BarChart3 className="h-4 w-4" />,
    });

    return actions;
  };

  const filters = [
    {
      id: 'type',
      label: 'Stream Type',
      type: 'select' as const,
      options: [
        { label: 'Network', value: 'network' },
        { label: 'Application', value: 'application' },
        { label: 'Database', value: 'database' },
        { label: 'System', value: 'system' },
        { label: 'User Activity', value: 'user_activity' },
        { label: 'File Integrity', value: 'file_integrity' },
      ],
    },
    {
      id: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' },
        { label: 'Error', value: 'error' },
        { label: 'Maintenance', value: 'maintenance' },
      ],
    },
  ];

  const bulkActions = [
    {
      id: 'start',
      label: 'Start Selected',
      icon: <Play className="h-4 w-4" />,
      onClick: (selectedRows: MonitoringStream[]) => {
        toast({
          title: "Start Streams",
          description: `Starting ${selectedRows.length} monitoring stream(s)...`,
        });
      },
    },
    {
      id: 'pause',
      label: 'Pause Selected',
      icon: <Pause className="h-4 w-4" />,
      onClick: (selectedRows: MonitoringStream[]) => {
        toast({
          title: "Pause Streams",
          description: `Pausing ${selectedRows.length} monitoring stream(s)...`,
        });
      },
    },
  ];

  return (
    <AdvancedDataTable
      data={streams}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      bulkActions={bulkActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="monitoring-streams"
      searchPlaceholder="Search monitoring streams..."
      emptyMessage="No monitoring streams configured. Add streams to start monitoring security events."
      loading={isLoading}
      className={className}
      variant="default"
      defaultSorting={[{ id: 'lastEvent', desc: true }]}
    />
  );
}
