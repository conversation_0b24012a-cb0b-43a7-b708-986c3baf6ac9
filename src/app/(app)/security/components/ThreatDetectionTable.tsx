"use client";

import React, { useState, useEffect } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { 
  Shield, 
  Eye, 
  AlertTriangle,
  Zap,
  Target,
  Activity,
  Clock,
  Globe,
  User,
  Ban,
  CheckCircle,
  XCircle,
  TrendingUp,
  Calendar,
  FileText,
  Settings
} from 'lucide-react';

interface ThreatPattern {
  id: string;
  name: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'brute_force' | 'anomaly' | 'malware' | 'phishing' | 'data_exfiltration' | 'privilege_escalation';
  detectionCount: number;
  lastDetected: Date;
  status: 'active' | 'resolved' | 'investigating' | 'false_positive';
  confidence: number;
  affectedUsers: string[];
  sourceIPs: string[];
  mitigationActions: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface ThreatDetectionTableProps {
  className?: string;
  onThreatView?: (threat: ThreatPattern) => void;
  onThreatResolve?: (threatId: string) => void;
  onThreatInvestigate?: (threatId: string) => void;
}

export default function ThreatDetectionTable({ 
  className, 
  onThreatView,
  onThreatResolve,
  onThreatInvestigate
}: ThreatDetectionTableProps) {
  const [threats, setThreats] = useState<ThreatPattern[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchThreats = async () => {
      try {
        setIsLoading(true);
        
        // Mock threat patterns data
        const threatsData: ThreatPattern[] = [
          {
            id: '1',
            name: 'Brute Force Login Attempts',
            description: 'Multiple failed login attempts from same IP address',
            severity: 'high',
            category: 'brute_force',
            detectionCount: 15,
            lastDetected: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
            status: 'active',
            confidence: 95,
            affectedUsers: ['<EMAIL>', '<EMAIL>'],
            sourceIPs: ['************', '************0'],
            mitigationActions: ['IP blocking', 'Account lockout'],
            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
            updatedAt: new Date(Date.now() - 30 * 60 * 1000),
          },
          {
            id: '2',
            name: 'Unusual Data Access Pattern',
            description: 'User accessing large amounts of sensitive data outside normal hours',
            severity: 'medium',
            category: 'anomaly',
            detectionCount: 3,
            lastDetected: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
            status: 'investigating',
            confidence: 78,
            affectedUsers: ['<EMAIL>'],
            sourceIPs: ['*************'],
            mitigationActions: ['User notification', 'Access monitoring'],
            createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
            updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
          },
          {
            id: '3',
            name: 'Privilege Escalation Attempt',
            description: 'User attempting to access resources beyond their permission level',
            severity: 'critical',
            category: 'privilege_escalation',
            detectionCount: 8,
            lastDetected: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
            status: 'active',
            confidence: 92,
            affectedUsers: ['<EMAIL>'],
            sourceIPs: ['*********'],
            mitigationActions: ['Account suspension', 'Admin notification'],
            createdAt: new Date(Date.now() - 45 * 60 * 1000),
            updatedAt: new Date(Date.now() - 15 * 60 * 1000),
          },
          {
            id: '4',
            name: 'Suspicious Email Activity',
            description: 'Potential phishing attempt detected in email communications',
            severity: 'medium',
            category: 'phishing',
            detectionCount: 12,
            lastDetected: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
            status: 'resolved',
            confidence: 85,
            affectedUsers: ['<EMAIL>', '<EMAIL>'],
            sourceIPs: ['external'],
            mitigationActions: ['Email quarantine', 'User training'],
            createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
            updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
          },
          {
            id: '5',
            name: 'Data Exfiltration Pattern',
            description: 'Large file downloads detected from sensitive directories',
            severity: 'high',
            category: 'data_exfiltration',
            detectionCount: 5,
            lastDetected: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
            status: 'false_positive',
            confidence: 65,
            affectedUsers: ['<EMAIL>'],
            sourceIPs: ['************'],
            mitigationActions: ['File access logging', 'DLP monitoring'],
            createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000),
            updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          },
        ];

        setThreats(threatsData);
      } catch (error) {
        console.error('Error fetching threat patterns:', error);
        toast({
          title: "Error",
          description: "Failed to load threat patterns. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchThreats();
  }, [toast]);

  const getSeverityBadge = (severity: ThreatPattern['severity']) => {
    const severityConfig = {
      low: { variant: 'outline' as const, label: 'Low', className: 'text-green-700 border-green-300' },
      medium: { variant: 'secondary' as const, label: 'Medium', className: 'bg-yellow-100 text-yellow-700' },
      high: { variant: 'destructive' as const, label: 'High', className: 'bg-orange-100 text-orange-700' },
      critical: { variant: 'destructive' as const, label: 'Critical', className: 'bg-red-100 text-red-700' },
    };
    const config = severityConfig[severity];
    return (
      <Badge variant={config.variant} className={config.className}>
        {config.label}
      </Badge>
    );
  };

  const getStatusBadge = (status: ThreatPattern['status']) => {
    const statusConfig = {
      active: { variant: 'destructive' as const, label: 'Active', icon: AlertTriangle },
      resolved: { variant: 'default' as const, label: 'Resolved', icon: CheckCircle },
      investigating: { variant: 'secondary' as const, label: 'Investigating', icon: Eye },
      false_positive: { variant: 'outline' as const, label: 'False Positive', icon: XCircle },
    };
    const config = statusConfig[status];
    const IconComponent = config.icon;
    return (
      <Badge variant={config.variant} className="flex items-center w-fit">
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getCategoryIcon = (category: ThreatPattern['category']) => {
    const iconMap = {
      brute_force: <Target className="h-4 w-4 text-red-500" />,
      anomaly: <TrendingUp className="h-4 w-4 text-orange-500" />,
      malware: <Zap className="h-4 w-4 text-purple-500" />,
      phishing: <Globe className="h-4 w-4 text-blue-500" />,
      data_exfiltration: <FileText className="h-4 w-4 text-yellow-500" />,
      privilege_escalation: <Shield className="h-4 w-4 text-red-600" />,
    };
    return iconMap[category] || <Activity className="h-4 w-4 text-gray-500" />;
  };

  const columns: AdvancedTableColumn<ThreatPattern>[] = [
    {
      accessorKey: 'name',
      title: 'Threat Pattern',
      sortable: true,
      filterable: true,
      cell: ({ row }) => {
        const threat = row.original;
        return (
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-primary/10">
              {getCategoryIcon(threat.category)}
            </div>
            <div className="min-w-0 flex-1">
              <div className="font-medium">{threat.name}</div>
              <div className="text-xs text-muted-foreground truncate">
                {threat.description}
              </div>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline" className="text-xs capitalize">
                  {threat.category.replace('_', ' ')}
                </Badge>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'severity',
      title: 'Severity',
      sortable: true,
      filterable: true,
      cell: ({ row }) => getSeverityBadge(row.original.severity),
    },
    {
      accessorKey: 'detectionCount',
      title: 'Detections',
      sortable: true,
      cell: ({ row }) => {
        const count = row.original.detectionCount;
        return (
          <div className="flex items-center gap-2">
            <Activity className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">{count}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'confidence',
      title: 'Confidence',
      sortable: true,
      cell: ({ row }) => {
        const confidence = row.original.confidence;
        return (
          <div className="flex items-center gap-2">
            <Target className="h-4 w-4 text-muted-foreground" />
            <Badge variant={confidence >= 90 ? 'default' : confidence >= 70 ? 'secondary' : 'outline'}>
              {confidence}%
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'affectedUsers',
      title: 'Affected Users',
      sortable: false,
      cell: ({ row }) => {
        const userCount = row.original.affectedUsers.length;
        return (
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-muted-foreground" />
            <Badge variant="outline">
              {userCount} user{userCount !== 1 ? 's' : ''}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      title: 'Status',
      sortable: true,
      filterable: true,
      cell: ({ row }) => getStatusBadge(row.original.status),
    },
    {
      accessorKey: 'lastDetected',
      title: 'Last Detected',
      sortable: true,
      cell: ({ row }) => {
        const lastDetected = row.original.lastDetected;
        return (
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-muted-foreground" />
            <span className="text-sm">{format(lastDetected, 'MMM dd, HH:mm')}</span>
          </div>
        );
      },
    },
  ];

  const rowActions = (row: ThreatPattern): AdvancedTableRowAction<ThreatPattern>[] => {
    const actions: AdvancedTableRowAction<ThreatPattern>[] = [
      {
        type: 'view',
        label: 'View Details',
        onClick: () => onThreatView?.(row),
        icon: <Eye className="h-4 w-4" />,
      },
    ];

    if (row.status === 'active' && onThreatInvestigate) {
      actions.push({
        type: 'custom',
        label: 'Investigate',
        onClick: () => onThreatInvestigate(row.id),
        icon: <AlertTriangle className="h-4 w-4" />,
        variant: 'secondary',
      });
    }

    if ((row.status === 'active' || row.status === 'investigating') && onThreatResolve) {
      actions.push({
        type: 'custom',
        label: 'Mark Resolved',
        onClick: () => onThreatResolve(row.id),
        icon: <CheckCircle className="h-4 w-4" />,
        variant: 'default',
      });
    }

    actions.push({
      type: 'custom',
      label: 'Block Sources',
      onClick: () => {
        toast({
          title: "Block Sources",
          description: `Blocking ${row.sourceIPs.length} source IP(s)...`,
        });
      },
      icon: <Ban className="h-4 w-4" />,
      variant: 'destructive',
    });

    return actions;
  };

  const filters = [
    {
      id: 'severity',
      label: 'Severity',
      type: 'select' as const,
      options: [
        { label: 'Critical', value: 'critical' },
        { label: 'High', value: 'high' },
        { label: 'Medium', value: 'medium' },
        { label: 'Low', value: 'low' },
      ],
    },
    {
      id: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Investigating', value: 'investigating' },
        { label: 'Resolved', value: 'resolved' },
        { label: 'False Positive', value: 'false_positive' },
      ],
    },
    {
      id: 'category',
      label: 'Category',
      type: 'select' as const,
      options: [
        { label: 'Brute Force', value: 'brute_force' },
        { label: 'Anomaly', value: 'anomaly' },
        { label: 'Malware', value: 'malware' },
        { label: 'Phishing', value: 'phishing' },
        { label: 'Data Exfiltration', value: 'data_exfiltration' },
        { label: 'Privilege Escalation', value: 'privilege_escalation' },
      ],
    },
  ];

  const bulkActions = [
    {
      id: 'investigate',
      label: 'Investigate Selected',
      icon: <AlertTriangle className="h-4 w-4" />,
      onClick: (selectedRows: ThreatPattern[]) => {
        toast({
          title: "Bulk Investigation",
          description: `Starting investigation for ${selectedRows.length} threat(s)...`,
        });
      },
    },
    {
      id: 'resolve',
      label: 'Mark as Resolved',
      icon: <CheckCircle className="h-4 w-4" />,
      onClick: (selectedRows: ThreatPattern[]) => {
        toast({
          title: "Bulk Resolution",
          description: `Marking ${selectedRows.length} threat(s) as resolved...`,
        });
      },
    },
    {
      id: 'block',
      label: 'Block Sources',
      icon: <Ban className="h-4 w-4" />,
      onClick: (selectedRows: ThreatPattern[]) => {
        const totalIPs = selectedRows.reduce((acc, threat) => acc + threat.sourceIPs.length, 0);
        toast({
          title: "Block Sources",
          description: `Blocking ${totalIPs} source IP(s) from ${selectedRows.length} threat(s)...`,
        });
      },
    },
  ];

  return (
    <AdvancedDataTable
      data={threats}
      columns={columns}
      enableGlobalSearch={true}
      enableColumnFilters={true}
      filters={filters}
      enableSorting={true}
      enableRowSelection={true}
      enableMultiRowSelection={true}
      enableRowActions={true}
      rowActions={rowActions}
      bulkActions={bulkActions}
      enableExport={true}
      exportFormats={['csv', 'excel']}
      exportFileName="threat-detection-log"
      searchPlaceholder="Search threat patterns..."
      emptyMessage="No threat patterns detected. Your system is secure."
      loading={isLoading}
      className={className}
      variant="default"
      defaultSorting={[{ id: 'lastDetected', desc: true }]}
    />
  );
}
