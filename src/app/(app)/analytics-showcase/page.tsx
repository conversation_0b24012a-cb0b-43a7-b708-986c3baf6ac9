"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { InteractiveChart, type ChartDataPoint, type ComparisonData } from '@/components/charts/interactive-chart';
import { useAuth } from '@/contexts/auth-context';
import { getExhibitions, getEvents, getLeads, getTasks, getAllBudgets } from '@/services/firestoreService';
import type { Exhibition, Event as EvexEvent, Lead, Task, Budget } from '@/types/firestore';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  LineChart, 
  PieChart, 
  TrendingUp, 
  Users, 
  DollarSign, 
  Calendar,
  RefreshCw,
  Zap
} from 'lucide-react';

// Real Firebase data processors
const processEventsTimeSeriesData = (events: EvexEvent[]): ChartDataPoint[] => {
  const eventsByMonth: Record<string, number> = {};
  const now = new Date();

  // Initialize last 12 months
  for (let i = 11; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
    const monthKey = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    eventsByMonth[monthKey] = 0;
  }

  // Count events by month
  events.forEach(event => {
    if (event.startDate) {
      const eventDate = new Date(event.startDate);
      const monthKey = eventDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
      if (eventsByMonth.hasOwnProperty(monthKey)) {
        eventsByMonth[monthKey]++;
      }
    }
  });

  return Object.entries(eventsByMonth).map(([month, count]) => ({
    name: month,
    value: count,
    date: new Date(month + ' 1').toISOString(),
    category: count > 5 ? 'High' : count > 2 ? 'Medium' : 'Low',
    trend: Math.random() * 10 - 5, // Could be calculated from historical data
    target: 8,
    benchmark: 10,
    metadata: {
      source: 'Firebase Events Collection',
      totalEvents: events.length
    }
  }));
};

const processEventsCategoryData = (events: EvexEvent[]): ChartDataPoint[] => {
  const categoryCount: Record<string, number> = {};

  events.forEach(event => {
    const category = event.type || 'Other';
    categoryCount[category] = (categoryCount[category] || 0) + 1;
  });

  return Object.entries(categoryCount).map(([category, count]) => ({
    name: category,
    value: count,
    category: 'Events',
    trend: Math.random() * 20 - 10 // Could be calculated from historical data
  }));
};

const processLeadsData = (leads: Lead[]): ChartDataPoint[] => {
  const statusCount: Record<string, number> = {};

  leads.forEach(lead => {
    const status = lead.status || 'Unknown';
    statusCount[status] = (statusCount[status] || 0) + 1;
  });

  return Object.entries(statusCount).map(([status, count]) => ({
    name: status,
    value: count,
    category: 'Leads',
    trend: Math.random() * 15 - 7.5
  }));
};

const processBudgetData = (budgets: Budget[]): ChartDataPoint[] => {
  const budgetByCategory: Record<string, { allocated: number, spent: number }> = {};

  budgets.forEach(budget => {
    const category = budget.category || 'General';
    if (!budgetByCategory[category]) {
      budgetByCategory[category] = { allocated: 0, spent: 0 };
    }
    budgetByCategory[category].allocated += budget.allocatedAmount || 0;
    budgetByCategory[category].spent += budget.spentAmount || 0;
  });

  return Object.entries(budgetByCategory).map(([category, data]) => ({
    name: category,
    value: data.spent,
    target: data.allocated,
    category: 'Budget',
    trend: data.allocated > 0 ? ((data.spent / data.allocated) - 1) * 100 : 0,
    metadata: {
      allocated: data.allocated,
      spent: data.spent,
      remaining: data.allocated - data.spent
    }
  }));
};

export default function AnalyticsShowcasePage() {
  const { user } = useAuth();
  const [timeSeriesData, setTimeSeriesData] = useState<ChartDataPoint[]>([]);
  const [categoryData, setCategoryData] = useState<ChartDataPoint[]>([]);
  const [leadsData, setLeadsData] = useState<ChartDataPoint[]>([]);
  const [budgetData, setBudgetData] = useState<ChartDataPoint[]>([]);
  const [comparisonData, setComparisonData] = useState<ComparisonData | undefined>();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      setIsLoading(false);
      setError('Please log in to view analytics data');
      return;
    }

    const loadRealData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch real data from Firebase
        const [exhibitions, events, leads, tasks, budgets] = await Promise.all([
          getExhibitions(),
          getEvents(),
          getLeads(),
          getTasks(),
          getAllBudgets()
        ]);

        // Process real data into chart format
        const eventsTimeSeries = processEventsTimeSeriesData(events);
        const eventsCategories = processEventsCategoryData(events);
        const leadsAnalysis = processLeadsData(leads);
        const budgetAnalysis = processBudgetData(budgets);

        setTimeSeriesData(eventsTimeSeries);
        setCategoryData(eventsCategories);
        setLeadsData(leadsAnalysis);
        setBudgetData(budgetAnalysis);

        // Create comparison data from historical events
        const currentPeriodEvents = events.filter(event => {
          const eventDate = new Date(event.startDate || '');
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          return eventDate >= thirtyDaysAgo;
        });

        const previousPeriodEvents = events.filter(event => {
          const eventDate = new Date(event.startDate || '');
          const sixtyDaysAgo = new Date();
          const thirtyDaysAgo = new Date();
          sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          return eventDate >= sixtyDaysAgo && eventDate < thirtyDaysAgo;
        });

        setComparisonData({
          current: processEventsTimeSeriesData(currentPeriodEvents),
          previous: processEventsTimeSeriesData(previousPeriodEvents),
          label: 'Previous 30 Days'
        });

      } catch (error) {
        console.error('Failed to load analytics data:', error);
        setError('Failed to load analytics data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    loadRealData();
  }, [user]);

  const handleRefresh = async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      const [exhibitions, events, leads, tasks, budgets] = await Promise.all([
        getExhibitions(),
        getEvents(),
        getLeads(),
        getTasks(),
        getAllBudgets()
      ]);

      setTimeSeriesData(processEventsTimeSeriesData(events));
      setCategoryData(processEventsCategoryData(events));
      setLeadsData(processLeadsData(leads));
      setBudgetData(processBudgetData(budgets));
    } catch (error) {
      console.error('Failed to refresh data:', error);
      setError('Failed to refresh data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDataPointClick = (data: ChartDataPoint) => {
    console.log('Data point clicked:', data);
  };

  const handleExport = (format: string) => {
    console.log(`Exporting as ${format}...`);
  };

  const handleTimeRangeChange = (range: string) => {
    console.log('Time range changed:', range);
  };

  // Show login prompt if not authenticated
  if (!user) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center space-y-4">
            <h2 className="text-2xl font-bold">Authentication Required</h2>
            <p className="text-muted-foreground">
              Please log in to view your analytics data from Firebase.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-4"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Interactive Analytics Charts</h1>
            <p className="text-muted-foreground">
              Real-time data visualization from your Firebase collections with advanced interactions
            </p>
          </div>
          <Button onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh Data
          </Button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* Feature Highlights */}
        <div className="flex flex-wrap gap-2">
          <Badge variant="secondary"><Zap className="h-3 w-3 mr-1" />Real-time Updates</Badge>
          <Badge variant="secondary"><TrendingUp className="h-3 w-3 mr-1" />Trend Analysis</Badge>
          <Badge variant="secondary"><BarChart3 className="h-3 w-3 mr-1" />Multiple Chart Types</Badge>
          <Badge variant="secondary"><Users className="h-3 w-3 mr-1" />Interactive Filtering</Badge>
        </div>
      </motion.div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="timeseries">Time Series</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <InteractiveChart
              title="Event Performance Trends"
              description={`Monthly event metrics from ${timeSeriesData.length} data points`}
              data={timeSeriesData}
              chartType="line"
              allowTypeChange={true}
              allowTimeFilter={true}
              allowZoom={true}
              showTrendLine={true}
              showBenchmarks={true}
              enableCrosshairs={true}
              height={300}
              onDataPointClick={handleDataPointClick}
              onExport={handleExport}
              onRefresh={handleRefresh}
              onTimeRangeChange={handleTimeRangeChange}
              loading={isLoading}
            />

            <InteractiveChart
              title="Lead Status Distribution"
              description={`Current lead pipeline from ${leadsData.reduce((sum, item) => sum + item.value, 0)} total leads`}
              data={leadsData}
              chartType="pie"
              allowTypeChange={true}
              allowDrillDown={true}
              height={300}
              onDataPointClick={handleDataPointClick}
              onExport={handleExport}
              loading={isLoading}
            />
          </div>
        </TabsContent>

        <TabsContent value="timeseries" className="space-y-6">
          <InteractiveChart
            title="Advanced Time Series Analysis"
            description="Interactive time series with real-time updates and comparison"
            data={timeSeriesData}
            chartType="area"
            allowTypeChange={true}
            allowTimeFilter={true}
            allowZoom={true}
            allowPan={true}
            realTimeUpdates={true}
            updateInterval={10}
            comparisonData={comparisonData}
            showTrendLine={true}
            showBenchmarks={true}
            enableCrosshairs={true}
            enableBrush={true}
            height={400}
            onDataPointClick={handleDataPointClick}
            onExport={handleExport}
            onRefresh={handleRefresh}
            onTimeRangeChange={handleTimeRangeChange}
            loading={isLoading}
          />
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <InteractiveChart
              title="Event Categories"
              description={`Event distribution across ${categoryData.length} categories`}
              data={categoryData}
              chartType="bar"
              allowTypeChange={true}
              allowDrillDown={true}
              drillDownConfig={{
                enabled: true,
                levels: ['category', 'subcategory'],
                onDrillDown: (level, data) => console.log('Drill down:', level, data)
              }}
              height={350}
              onDataPointClick={handleDataPointClick}
              onExport={handleExport}
              loading={isLoading}
            />

            <InteractiveChart
              title="Budget Analysis"
              description={`Budget allocation vs spending across ${budgetData.length} categories`}
              data={budgetData}
              chartType="bar"
              allowTypeChange={true}
              showBenchmarks={true}
              comparisonData={comparisonData}
              height={350}
              onDataPointClick={handleDataPointClick}
              onExport={handleExport}
              loading={isLoading}
            />
          </div>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-6">
          <InteractiveChart
            title="Advanced Interactive Dashboard"
            description="Full-featured chart with all interactive capabilities"
            data={timeSeriesData}
            chartType="bar"
            allowTypeChange={true}
            allowTimeFilter={true}
            allowDrillDown={true}
            allowZoom={true}
            allowPan={true}
            realTimeUpdates={true}
            updateInterval={15}
            comparisonData={comparisonData}
            showTrendLine={true}
            showBenchmarks={true}
            enableCrosshairs={true}
            enableBrush={true}
            height={500}
            annotations={[
              { x: 15, y: 800, label: 'Target Goal', type: 'line', color: '#ff6b6b' },
              { x: 25, y: 600, label: 'Milestone', type: 'point', color: '#4ecdc4' }
            ]}
            onDataPointClick={handleDataPointClick}
            onExport={handleExport}
            onRefresh={handleRefresh}
            onTimeRangeChange={handleTimeRangeChange}
            onFilterChange={(filters) => console.log('Filters changed:', filters)}
            loading={isLoading}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
