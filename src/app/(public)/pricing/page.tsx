"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  Check, 
  X, 
  Star, 
  Crown, 
  Zap, 
  Users, 
  Shield,
  ArrowRight,
  Building,
  Globe,
  Headphones,
  Database,
  Smartphone,
  Palette
} from 'lucide-react';
import { subscriptionService, type SubscriptionPlan } from '@/services/subscriptionService';

export default function PricingPage() {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [isYearly, setIsYearly] = useState(false);

  useEffect(() => {
    loadPlans();
  }, []);

  const loadPlans = async () => {
    try {
      setLoading(true);
      const plansData = await subscriptionService.getAllPlans();
      setPlans(plansData);
    } catch (error) {
      console.error('Failed to load plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTierIcon = (tier: SubscriptionPlan['tier']) => {
    switch (tier) {
      case 'free': return <Users className="h-6 w-6" />;
      case 'starter': return <Zap className="h-6 w-6" />;
      case 'professional': return <Star className="h-6 w-6" />;
      case 'enterprise': return <Crown className="h-6 w-6" />;
      case 'custom': return <Shield className="h-6 w-6" />;
      default: return <Users className="h-6 w-6" />;
    }
  };

  const getTierColor = (tier: SubscriptionPlan['tier']) => {
    switch (tier) {
      case 'free': return 'bg-gray-100 text-gray-800';
      case 'starter': return 'bg-blue-100 text-blue-800';
      case 'professional': return 'bg-purple-100 text-purple-800';
      case 'enterprise': return 'bg-yellow-100 text-yellow-800';
      case 'custom': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(amount);
  };

  const formatFeatureValue = (value: number | boolean, unit?: string) => {
    if (typeof value === 'boolean') {
      return value;
    }
    if (value === -1) {
      return 'Unlimited';
    }
    return `${value.toLocaleString()}${unit ? ` ${unit}` : ''}`;
  };

  const getPrice = (plan: SubscriptionPlan) => {
    return isYearly ? plan.pricing.yearly : plan.pricing.monthly;
  };

  const getYearlySavings = (plan: SubscriptionPlan) => {
    if (plan.pricing.yearly === 0) return 0;
    const monthlyTotal = plan.pricing.monthly * 12;
    return monthlyTotal - plan.pricing.yearly;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Choose Your EVEXA Plan</h1>
          <p className="text-xl text-muted-foreground mb-8">
            Transform your exhibition management with the right plan for your business
          </p>
          
          {/* Billing Toggle */}
          <div className="flex items-center justify-center gap-4 mb-8">
            <span className={`text-sm ${!isYearly ? 'font-semibold' : 'text-muted-foreground'}`}>
              Monthly
            </span>
            <Switch
              checked={isYearly}
              onCheckedChange={setIsYearly}
              className="data-[state=checked]:bg-primary"
            />
            <span className={`text-sm ${isYearly ? 'font-semibold' : 'text-muted-foreground'}`}>
              Yearly
            </span>
            <Badge className="bg-green-100 text-green-800 ml-2">
              Save up to 17%
            </Badge>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {plans.map((plan) => (
            <Card key={plan.id} className={`relative ${plan.isPopular ? 'ring-2 ring-primary scale-105' : ''} transition-all duration-300 hover:shadow-lg`}>
              {plan.isPopular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-primary text-primary-foreground px-4 py-1">
                    Most Popular
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center pb-4">
                <div className="flex items-center justify-center mb-4">
                  <div className={`p-3 rounded-full ${plan.tier === 'professional' ? 'bg-purple-100' : plan.tier === 'enterprise' ? 'bg-yellow-100' : plan.tier === 'starter' ? 'bg-blue-100' : 'bg-gray-100'}`}>
                    {getTierIcon(plan.tier)}
                  </div>
                </div>
                <CardTitle className="text-2xl mb-2">{plan.displayName}</CardTitle>
                <CardDescription className="text-sm">{plan.description}</CardDescription>
                
                <div className="mt-6">
                  <div className="text-4xl font-bold">
                    {formatCurrency(getPrice(plan))}
                    <span className="text-lg font-normal text-muted-foreground">
                      /{isYearly ? 'year' : 'month'}
                    </span>
                  </div>
                  {isYearly && getYearlySavings(plan) > 0 && (
                    <div className="text-sm text-green-600 mt-1">
                      Save {formatCurrency(getYearlySavings(plan))} per year
                    </div>
                  )}
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <Button 
                  className={`w-full mb-6 ${plan.isPopular ? 'bg-primary hover:bg-primary/90' : ''}`}
                  variant={plan.isPopular ? 'default' : 'outline'}
                >
                  {plan.tier === 'free' ? 'Get Started Free' : 'Start Free Trial'}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
                
                <div className="space-y-3">
                  <h4 className="font-semibold text-sm">What's included:</h4>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        <Building className="h-3 w-3" />
                        Exhibitions
                      </span>
                      <span className="font-medium">{formatFeatureValue(plan.features.maxExhibitions)}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        <Users className="h-3 w-3" />
                        Team Members
                      </span>
                      <span className="font-medium">{formatFeatureValue(plan.features.maxUsers)}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        <Database className="h-3 w-3" />
                        Storage
                      </span>
                      <span className="font-medium">{formatFeatureValue(plan.features.storageGB, 'GB')}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        <Globe className="h-3 w-3" />
                        API Calls
                      </span>
                      <span className="font-medium">{formatFeatureValue(plan.features.apiCallsPerMonth, '/mo')}</span>
                    </div>
                  </div>
                  
                  <div className="border-t pt-3 space-y-2">
                    <div className="flex items-center gap-2">
                      {plan.features.customBranding ? <Check className="h-4 w-4 text-green-600" /> : <X className="h-4 w-4 text-gray-400" />}
                      <span className={`text-sm ${plan.features.customBranding ? '' : 'text-gray-400'}`}>
                        Custom Branding
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {plan.features.advancedAnalytics ? <Check className="h-4 w-4 text-green-600" /> : <X className="h-4 w-4 text-gray-400" />}
                      <span className={`text-sm ${plan.features.advancedAnalytics ? '' : 'text-gray-400'}`}>
                        Advanced Analytics
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {plan.features.prioritySupport ? <Check className="h-4 w-4 text-green-600" /> : <X className="h-4 w-4 text-gray-400" />}
                      <span className={`text-sm ${plan.features.prioritySupport ? '' : 'text-gray-400'}`}>
                        Priority Support
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {plan.features.whiteLabeling ? <Check className="h-4 w-4 text-green-600" /> : <X className="h-4 w-4 text-gray-400" />}
                      <span className={`text-sm ${plan.features.whiteLabeling ? '' : 'text-gray-400'}`}>
                        White Labeling
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {plan.features.apiAccess ? <Check className="h-4 w-4 text-green-600" /> : <X className="h-4 w-4 text-gray-400" />}
                      <span className={`text-sm ${plan.features.apiAccess ? '' : 'text-gray-400'}`}>
                        API Access
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {plan.features.aiFeatures ? <Check className="h-4 w-4 text-green-600" /> : <X className="h-4 w-4 text-gray-400" />}
                      <span className={`text-sm ${plan.features.aiFeatures ? '' : 'text-gray-400'}`}>
                        AI Features
                      </span>
                    </div>
                  </div>
                  
                  {plan.tier === 'enterprise' && (
                    <div className="border-t pt-3">
                      <div className="flex items-center gap-2 text-sm">
                        <Crown className="h-4 w-4 text-yellow-600" />
                        <span>Dedicated Account Manager</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm mt-1">
                        <Headphones className="h-4 w-4 text-blue-600" />
                        <span>24/7 Phone Support</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm mt-1">
                        <Palette className="h-4 w-4 text-purple-600" />
                        <span>Custom Development</span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Feature Comparison Table */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="text-center">Feature Comparison</CardTitle>
            <CardDescription className="text-center">
              Compare all features across our subscription plans
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Features</th>
                    {plans.map((plan) => (
                      <th key={plan.id} className="text-center py-3 px-4">
                        <Badge className={getTierColor(plan.tier)}>
                          {plan.displayName}
                        </Badge>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {[
                    { label: 'Exhibitions', key: 'maxExhibitions' },
                    { label: 'Events', key: 'maxEvents' },
                    { label: 'Team Members', key: 'maxUsers' },
                    { label: 'Tasks', key: 'maxTasks' },
                    { label: 'Leads', key: 'maxLeads' },
                    { label: 'Storage (GB)', key: 'storageGB' },
                    { label: 'API Calls/Month', key: 'apiCallsPerMonth' },
                    { label: 'Custom Branding', key: 'customBranding' },
                    { label: 'Advanced Analytics', key: 'advancedAnalytics' },
                    { label: 'Priority Support', key: 'prioritySupport' },
                    { label: 'White Labeling', key: 'whiteLabeling' },
                    { label: 'API Access', key: 'apiAccess' },
                    { label: 'AI Features', key: 'aiFeatures' },
                    { label: 'SSO Integration', key: 'ssoIntegration' },
                    { label: 'Audit Logs', key: 'auditLogs' }
                  ].map((feature) => (
                    <tr key={feature.key} className="border-b">
                      <td className="py-3 px-4 font-medium">{feature.label}</td>
                      {plans.map((plan) => (
                        <td key={plan.id} className="text-center py-3 px-4">
                          {typeof plan.features[feature.key as keyof typeof plan.features] === 'boolean' ? (
                            plan.features[feature.key as keyof typeof plan.features] ? (
                              <Check className="h-5 w-5 text-green-600 mx-auto" />
                            ) : (
                              <X className="h-5 w-5 text-gray-400 mx-auto" />
                            )
                          ) : (
                            <span className="font-medium">
                              {formatFeatureValue(plan.features[feature.key as keyof typeof plan.features] as number)}
                            </span>
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* FAQ Section */}
        <Card>
          <CardHeader>
            <CardTitle className="text-center">Frequently Asked Questions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Can I change plans anytime?</h4>
                <p className="text-sm text-muted-foreground">
                  Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Is there a free trial?</h4>
                <p className="text-sm text-muted-foreground">
                  Yes, all paid plans come with a 14-day free trial. No credit card required.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">What payment methods do you accept?</h4>
                <p className="text-sm text-muted-foreground">
                  We accept all major credit cards, PayPal, and bank transfers for enterprise plans.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Can I cancel anytime?</h4>
                <p className="text-sm text-muted-foreground">
                  Yes, you can cancel your subscription at any time. Your data will be retained for 30 days.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
