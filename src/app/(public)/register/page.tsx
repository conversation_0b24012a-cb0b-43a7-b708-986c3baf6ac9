"use client";

import React from 'react';
import CustomerRegistration from '@/components/marketing/CustomerRegistration';
import { useSearchParams } from 'next/navigation';

export default function RegisterPage() {
  const searchParams = useSearchParams();
  const selectedPlan = searchParams.get('plan') || undefined;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-6">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">
            Join EVEXA Today
          </h1>
          <p className="text-xl text-muted-foreground">
            Start your exhibition management journey with a 14-day free trial
          </p>
        </div>
        
        <CustomerRegistration 
          selectedPlan={selectedPlan}
          onSuccess={() => {
            // Redirect to success page or dashboard
            window.location.href = '/dashboard';
          }}
        />
      </div>
    </div>
  );
}
