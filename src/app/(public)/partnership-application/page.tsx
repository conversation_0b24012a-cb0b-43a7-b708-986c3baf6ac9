"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { 
  Building2, 
  Mail, 
  Phone, 
  Globe, 
  Users, 
  Calendar, 
  DollarSign,
  CheckCircle,
  ArrowRight,
  Star,
  Handshake
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { exhibitionOrganizerPartnershipService } from '@/services/exhibitionOrganizerPartnershipService';

export default function PartnershipApplicationPage() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const [application, setApplication] = useState({
    organizationName: '',
    contactInfo: {
      primaryContact: '',
      email: '',
      phone: '',
      website: ''
    },
    businessInfo: {
      yearsInBusiness: 0,
      numberOfEvents: 0,
      averageEventSize: 0,
      targetMarkets: [] as string[],
      currentSoftware: ''
    },
    partnershipInterest: {
      desiredTier: 'bronze' as const,
      expectedClients: 0,
      revenueExpectation: 0,
      whiteLabelInterest: false,
      apiIntegrationNeeds: false
    }
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      await exhibitionOrganizerPartnershipService.submitApplication(application);
      setSubmitted(true);
      toast({
        title: "Application Submitted!",
        description: "We'll review your application and get back to you within 5 business days."
      });
    } catch (error) {
      console.error('Failed to submit application:', error);
      toast({
        title: "Error",
        description: "Failed to submit application. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleMarketChange = (market: string, checked: boolean) => {
    setApplication(prev => ({
      ...prev,
      businessInfo: {
        ...prev.businessInfo,
        targetMarkets: checked 
          ? [...prev.businessInfo.targetMarkets, market]
          : prev.businessInfo.targetMarkets.filter(m => m !== market)
      }
    }));
  };

  if (submitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardContent className="text-center py-8">
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-2">Application Submitted!</h2>
            <p className="text-muted-foreground mb-4">
              Thank you for your interest in becoming an EVEXA Exhibition Organizer Partner. 
              We'll review your application and contact you within 5 business days.
            </p>
            <Button onClick={() => window.location.href = '/'}>
              Return to Home
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Building2 className="h-10 w-10 text-primary" />
            <h1 className="text-4xl font-bold">EVEXA Partnership Program</h1>
          </div>
          <p className="text-xl text-muted-foreground mb-6">
            Join our Exhibition Organizer Partnership Program and grow your business with EVEXA
          </p>
          
          {/* Partnership Tiers */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            {[
              { tier: 'Bronze', color: 'bg-orange-100 text-orange-800', features: ['Basic Support', 'Standard Commission'] },
              { tier: 'Silver', color: 'bg-gray-100 text-gray-800', features: ['Priority Support', 'Enhanced Commission'] },
              { tier: 'Gold', color: 'bg-yellow-100 text-yellow-800', features: ['White Labeling', 'Marketing Support'] },
              { tier: 'Platinum', color: 'bg-purple-100 text-purple-800', features: ['Dedicated Account', 'Custom Features'] }
            ].map((tierInfo) => (
              <Card key={tierInfo.tier} className="text-center">
                <CardContent className="p-4">
                  <Badge className={tierInfo.color}>{tierInfo.tier}</Badge>
                  <ul className="text-sm text-muted-foreground mt-2 space-y-1">
                    {tierInfo.features.map((feature, idx) => (
                      <li key={idx}>{feature}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Application Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Handshake className="h-6 w-6" />
              Partnership Application
            </CardTitle>
            <CardDescription>
              Tell us about your organization and partnership goals
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Organization Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Organization Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="organizationName">Organization Name *</Label>
                    <Input
                      id="organizationName"
                      required
                      value={application.organizationName}
                      onChange={(e) => setApplication(prev => ({ ...prev, organizationName: e.target.value }))}
                      placeholder="Your Exhibition Company"
                    />
                  </div>
                  <div>
                    <Label htmlFor="primaryContact">Primary Contact *</Label>
                    <Input
                      id="primaryContact"
                      required
                      value={application.contactInfo.primaryContact}
                      onChange={(e) => setApplication(prev => ({ 
                        ...prev, 
                        contactInfo: { ...prev.contactInfo, primaryContact: e.target.value }
                      }))}
                      placeholder="John Smith"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      required
                      value={application.contactInfo.email}
                      onChange={(e) => setApplication(prev => ({ 
                        ...prev, 
                        contactInfo: { ...prev.contactInfo, email: e.target.value }
                      }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      required
                      value={application.contactInfo.phone}
                      onChange={(e) => setApplication(prev => ({ 
                        ...prev, 
                        contactInfo: { ...prev.contactInfo, phone: e.target.value }
                      }))}
                      placeholder="+****************"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    value={application.contactInfo.website}
                    onChange={(e) => setApplication(prev => ({ 
                      ...prev, 
                      contactInfo: { ...prev.contactInfo, website: e.target.value }
                    }))}
                    placeholder="https://yourcompany.com"
                  />
                </div>
              </div>

              {/* Business Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Business Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="yearsInBusiness">Years in Business *</Label>
                    <Input
                      id="yearsInBusiness"
                      type="number"
                      required
                      value={application.businessInfo.yearsInBusiness}
                      onChange={(e) => setApplication(prev => ({ 
                        ...prev, 
                        businessInfo: { ...prev.businessInfo, yearsInBusiness: Number(e.target.value) }
                      }))}
                      placeholder="5"
                    />
                  </div>
                  <div>
                    <Label htmlFor="numberOfEvents">Events per Year *</Label>
                    <Input
                      id="numberOfEvents"
                      type="number"
                      required
                      value={application.businessInfo.numberOfEvents}
                      onChange={(e) => setApplication(prev => ({ 
                        ...prev, 
                        businessInfo: { ...prev.businessInfo, numberOfEvents: Number(e.target.value) }
                      }))}
                      placeholder="12"
                    />
                  </div>
                  <div>
                    <Label htmlFor="averageEventSize">Avg. Event Size (attendees) *</Label>
                    <Input
                      id="averageEventSize"
                      type="number"
                      required
                      value={application.businessInfo.averageEventSize}
                      onChange={(e) => setApplication(prev => ({ 
                        ...prev, 
                        businessInfo: { ...prev.businessInfo, averageEventSize: Number(e.target.value) }
                      }))}
                      placeholder="500"
                    />
                  </div>
                </div>

                <div>
                  <Label>Target Markets (select all that apply)</Label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
                    {['Technology', 'Healthcare', 'Automotive', 'Finance', 'Manufacturing', 'Retail', 'Education', 'Other'].map((market) => (
                      <div key={market} className="flex items-center space-x-2">
                        <Checkbox
                          id={market}
                          checked={application.businessInfo.targetMarkets.includes(market)}
                          onCheckedChange={(checked) => handleMarketChange(market, checked as boolean)}
                        />
                        <Label htmlFor={market} className="text-sm">{market}</Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label htmlFor="currentSoftware">Current Exhibition Management Software</Label>
                  <Input
                    id="currentSoftware"
                    value={application.businessInfo.currentSoftware}
                    onChange={(e) => setApplication(prev => ({ 
                      ...prev, 
                      businessInfo: { ...prev.businessInfo, currentSoftware: e.target.value }
                    }))}
                    placeholder="Current software or 'None'"
                  />
                </div>
              </div>

              {/* Partnership Interest */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Partnership Interest</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="desiredTier">Desired Partnership Tier</Label>
                    <Select 
                      value={application.partnershipInterest.desiredTier} 
                      onValueChange={(value) => setApplication(prev => ({ 
                        ...prev, 
                        partnershipInterest: { ...prev.partnershipInterest, desiredTier: value as any }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="bronze">Bronze</SelectItem>
                        <SelectItem value="silver">Silver</SelectItem>
                        <SelectItem value="gold">Gold</SelectItem>
                        <SelectItem value="platinum">Platinum</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="expectedClients">Expected Clients (Year 1)</Label>
                    <Input
                      id="expectedClients"
                      type="number"
                      value={application.partnershipInterest.expectedClients}
                      onChange={(e) => setApplication(prev => ({ 
                        ...prev, 
                        partnershipInterest: { ...prev.partnershipInterest, expectedClients: Number(e.target.value) }
                      }))}
                      placeholder="25"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="revenueExpectation">Expected Annual Revenue ($)</Label>
                  <Input
                    id="revenueExpectation"
                    type="number"
                    value={application.partnershipInterest.revenueExpectation}
                    onChange={(e) => setApplication(prev => ({ 
                      ...prev, 
                      partnershipInterest: { ...prev.partnershipInterest, revenueExpectation: Number(e.target.value) }
                    }))}
                    placeholder="100000"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="whiteLabelInterest"
                      checked={application.partnershipInterest.whiteLabelInterest}
                      onCheckedChange={(checked) => setApplication(prev => ({ 
                        ...prev, 
                        partnershipInterest: { ...prev.partnershipInterest, whiteLabelInterest: checked as boolean }
                      }))}
                    />
                    <Label htmlFor="whiteLabelInterest">Interested in white-label solutions</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="apiIntegrationNeeds"
                      checked={application.partnershipInterest.apiIntegrationNeeds}
                      onCheckedChange={(checked) => setApplication(prev => ({ 
                        ...prev, 
                        partnershipInterest: { ...prev.partnershipInterest, apiIntegrationNeeds: checked as boolean }
                      }))}
                    />
                    <Label htmlFor="apiIntegrationNeeds">Need API integration capabilities</Label>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button type="submit" disabled={loading} size="lg">
                  {loading ? 'Submitting...' : 'Submit Application'}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
