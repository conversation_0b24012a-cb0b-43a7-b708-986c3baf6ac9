"use client";

import React, { useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Loader2, User, Shield, Mail } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function VerifyAuthPage() {
  const { user, isAuthenticated, isLoading, signIn, logout } = useAuth();
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSigningIn, setIsSigningIn] = useState(false);
  const [authResult, setAuthResult] = useState<{ success: boolean; message: string } | null>(null);

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || !password) return;

    setIsSigningIn(true);
    setAuthResult(null);

    try {
      const result = await signIn(email, password);
      setAuthResult({
        success: result.success,
        message: result.success 
          ? `Successfully authenticated as ${result.user?.displayName || result.user?.email}` 
          : result.error || 'Authentication failed'
      });
    } catch (error: any) {
      setAuthResult({
        success: false,
        message: error.message || 'Authentication failed'
      });
    } finally {
      setIsSigningIn(false);
    }
  };

  const handleLogout = async () => {
    await logout();
    setAuthResult(null);
    setEmail('');
    setPassword('');
  };

  const goToDashboard = () => {
    router.push('/dashboard');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-muted-foreground">Checking authentication...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <Card className="mx-auto max-w-md w-full">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl flex items-center justify-center gap-2">
            <Shield className="h-6 w-6" />
            Authentication Verification
          </CardTitle>
          <CardDescription>
            Verify that real Firebase authentication is working
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Authentication Status */}
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
              <span className="font-medium">Authentication Status</span>
              <Badge variant={isAuthenticated ? "default" : "secondary"} className="flex items-center gap-1">
                {isAuthenticated ? <CheckCircle className="h-3 w-3" /> : <XCircle className="h-3 w-3" />}
                {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
              </Badge>
            </div>

            {user && (
              <div className="space-y-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2 font-medium text-green-800">
                  <User className="h-4 w-4" />
                  Real User Data (from Firebase)
                </div>
                <div className="space-y-1 text-sm text-green-700">
                  <div><strong>Name:</strong> {user.displayName || 'Not set'}</div>
                  <div><strong>Email:</strong> {user.email}</div>
                  <div><strong>Role:</strong> {user.role}</div>
                  <div><strong>Status:</strong> {user.status}</div>
                  <div><strong>User ID:</strong> {user.id}</div>
                </div>
              </div>
            )}
          </div>

          {!isAuthenticated ? (
            /* Login Form */
            <form onSubmit={handleSignIn} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
              <Button type="submit" className="w-full" disabled={isSigningIn}>
                {isSigningIn ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing In...
                  </>
                ) : (
                  'Sign In with Firebase'
                )}
              </Button>
            </form>
          ) : (
            /* Authenticated Actions */
            <div className="space-y-4">
              <Button onClick={goToDashboard} className="w-full">
                Go to Dashboard
              </Button>
              <Button onClick={handleLogout} variant="outline" className="w-full">
                Sign Out
              </Button>
            </div>
          )}

          {/* Authentication Result */}
          {authResult && (
            <div className={`p-3 rounded-lg border ${
              authResult.success 
                ? 'bg-green-50 border-green-200 text-green-800' 
                : 'bg-red-50 border-red-200 text-red-800'
            }`}>
              <div className="flex items-center gap-2">
                {authResult.success ? <CheckCircle className="h-4 w-4" /> : <XCircle className="h-4 w-4" />}
                <span className="font-medium">
                  {authResult.success ? 'Success' : 'Error'}
                </span>
              </div>
              <p className="text-sm mt-1">{authResult.message}</p>
            </div>
          )}

          {/* Info */}
          <div className="text-xs text-muted-foreground text-center space-y-1">
            <p>✅ No fake data or authentication bypasses</p>
            <p>✅ Real Firebase authentication only</p>
            <p>✅ All user data comes from Firebase</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
