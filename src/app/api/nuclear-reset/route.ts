import { NextRequest, NextResponse } from 'next/server';
import { ProfessionalDataManager } from '@/lib/professionalDataManager';

/**
 * Nuclear Reset API - Delete ALL Firebase collections
 * 
 * ⚠️  WARNING: This will delete ALL DOCUMENTS from ALL COLLECTIONS
 * ⚠️  This is IRREVERSIBLE - use with extreme caution
 * ⚠️  Only works in development mode
 */
export async function POST(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Nuclear reset is not allowed in production' },
        { status: 403 }
      );
    }

    console.log('🚨 API: Nuclear reset requested...');
    
    const dataManager = new ProfessionalDataManager();
    const result = await dataManager.nuclearReset();
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        message: `Nuclear reset completed successfully`,
        data: {
          deletedCollections: result.deletedCollections,
          totalCollectionsDeleted: result.deletedCollections.length,
          errors: result.errors.length > 0 ? result.errors : undefined
        }
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'Nuclear reset completed with errors',
        data: {
          deletedCollections: result.deletedCollections,
          totalCollectionsDeleted: result.deletedCollections.length,
          errors: result.errors
        }
      }, { status: 207 }); // 207 Multi-Status for partial success
    }
    
  } catch (error) {
    console.error('Nuclear reset API error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Nuclear reset failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
