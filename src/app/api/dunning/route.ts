import { NextRequest, NextResponse } from 'next/server';
import { automatedDunningService } from '@/services/automatedDunningService';
import { auth } from '@/lib/firebase-admin';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    switch (action) {
      case 'campaigns':
        const status = searchParams.get('status') as any;
        const campaigns = await automatedDunningService.getDunningCampaigns(tenantId, status);
        return NextResponse.json(campaigns);

      case 'campaign-details':
        const campaignId = searchParams.get('campaignId');
        if (!campaignId) {
          return NextResponse.json({ error: 'Campaign ID is required' }, { status: 400 });
        }
        
        const campaign = await automatedDunningService.getCampaign(campaignId);
        if (!campaign) {
          return NextResponse.json({ error: 'Campaign not found' }, { status: 404 });
        }
        
        return NextResponse.json(campaign);

      case 'analytics':
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        
        if (!startDate || !endDate) {
          return NextResponse.json({ error: 'Start date and end date are required' }, { status: 400 });
        }

        const analytics = await automatedDunningService?.getDunningAnalytics(
          tenantId,
          new Date(startDate),
          new Date(endDate)
        );
        
        return NextResponse.json(analytics);

      case 'templates':
        const templates = await automatedDunningService?.getDunningTemplates();
        return NextResponse.json(templates);

      case 'rules':
        const rules = await automatedDunningService?.getDunningRules(tenantId);
        return NextResponse.json(rules);

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in dunning API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const data = await request.json();
    const { action } = data;

    switch (action) {
      case 'create-campaign':
        const { customerId, subscriptionId, invoiceId, paymentDetails } = data;
        
        const campaign = await automatedDunningService.createDunningCampaign(
          tenantId,
          customerId,
          subscriptionId,
          invoiceId,
          {
            ...paymentDetails,
            dueDate: new Date(paymentDetails.dueDate)
          }
        );
        
        return NextResponse.json(campaign);

      case 'pause-campaign':
        const { campaignId: pauseCampaignId } = data;
        await automatedDunningService?.pauseCampaign(pauseCampaignId);
        return NextResponse.json({ success: true });

      case 'resume-campaign':
        const { campaignId: resumeCampaignId } = data;
        await automatedDunningService?.resumeCampaign(resumeCampaignId);
        return NextResponse.json({ success: true });

      case 'cancel-campaign':
        const { campaignId: cancelCampaignId, reason } = data;
        await automatedDunningService?.cancelCampaign(cancelCampaignId, reason);
        return NextResponse.json({ success: true });

      case 'manual-retry':
        const { campaignId: retryCampaignId } = data;
        const retryResult = await automatedDunningService?.manualRetry(retryCampaignId);
        return NextResponse.json(retryResult);

      case 'send-communication':
        const { campaignId: commCampaignId, stage, customMessage } = data;
        await automatedDunningService.sendManualCommunication(
          commCampaignId,
          stage,
          customMessage
        );
        return NextResponse.json({ success: true });

      case 'create-template':
        const { template } = data;
        const newTemplate = await automatedDunningService.createDunningTemplate(template);
        return NextResponse.json(newTemplate);

      case 'create-rule':
        const { rule } = data;
        const newRule = await automatedDunningService.createDunningRule(tenantId, rule);
        return NextResponse.json(newRule);

      case 'process-campaigns':
        // Manual trigger for processing campaigns (normally done by cron job)
        await automatedDunningService.processDunningCampaigns();
        return NextResponse.json({ success: true });

      case 'process-reactivations':
        // Manual trigger for processing reactivation checks
        await automatedDunningService.processReactivationChecks();
        return NextResponse.json({ success: true });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in dunning API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const campaignId = searchParams.get('campaignId');
    const templateId = searchParams.get('templateId');
    const ruleId = searchParams.get('ruleId');

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const updates = await request.json();

    if (campaignId) {
      await automatedDunningService.updateDunningCampaign(campaignId, updates);
      return NextResponse.json({ success: true });
    }

    if (templateId) {
      await automatedDunningService.updateDunningTemplate(templateId, updates);
      return NextResponse.json({ success: true });
    }

    if (ruleId) {
      await automatedDunningService.updateDunningRule(ruleId, updates);
      return NextResponse.json({ success: true });
    }

    return NextResponse.json({ error: 'Campaign ID, Template ID, or Rule ID is required' }, { status: 400 });
  } catch (error) {
    console.error('Error updating dunning resource:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const campaignId = searchParams.get('campaignId');
    const templateId = searchParams.get('templateId');
    const ruleId = searchParams.get('ruleId');

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    if (campaignId) {
      await automatedDunningService.deleteDunningCampaign(campaignId);
      return NextResponse.json({ success: true });
    }

    if (templateId) {
      await automatedDunningService.deleteDunningTemplate(templateId);
      return NextResponse.json({ success: true });
    }

    if (ruleId) {
      await automatedDunningService.deleteDunningRule(ruleId);
      return NextResponse.json({ success: true });
    }

    return NextResponse.json({ error: 'Campaign ID, Template ID, or Rule ID is required' }, { status: 400 });
  } catch (error) {
    console.error('Error deleting dunning resource:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Webhook endpoint for Stripe payment events
export async function PATCH(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'payment-failed') {
      const { tenantId, customerId, subscriptionId, invoiceId, paymentDetails } = await request.json();
      
      // Create dunning campaign for failed payment
      const campaign = await automatedDunningService.createDunningCampaign(
        tenantId,
        customerId,
        subscriptionId,
        invoiceId,
        {
          ...paymentDetails,
          dueDate: new Date(paymentDetails.dueDate)
        }
      );
      
      return NextResponse.json({ success: true, campaignId: campaign.id });
    }

    if (action === 'payment-succeeded') {
      const { invoiceId } = await request.json();
      
      // Find and complete any active campaigns for this invoice
      await automatedDunningService.completeSuccessfulPayment(invoiceId);
      
      return NextResponse.json({ success: true });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Error in dunning webhook:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
