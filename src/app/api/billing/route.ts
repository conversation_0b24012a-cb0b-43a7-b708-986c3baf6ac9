import { NextRequest, NextResponse } from 'next/server';
import { stripePaymentService } from '@/services/stripePaymentService';
import { subscriptionService } from '@/services/subscriptionService';
import { auth } from '@/lib/firebase-admin';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const tenantId = searchParams.get('tenantId');

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID required' }, { status: 400 });
    }

    switch (action) {
      case 'subscription':
        const subscription = await subscriptionService.getTenantSubscription(tenantId);
        return NextResponse.json(subscription);

      case 'plans':
        const plans = await subscriptionService.getAvailablePlans();
        return NextResponse.json(plans);

      case 'usage':
        const usage = await subscriptionService.getTenantUsage(tenantId);
        return NextResponse.json(usage);

      case 'invoices':
        const invoices = await subscriptionService.getTenantInvoices(tenantId);
        return NextResponse.json(invoices);

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in billing API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, tenantId, ...data } = body;

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID required' }, { status: 400 });
    }

    switch (action) {
      case 'create-checkout-session':
        const { priceId, successUrl, cancelUrl, trialPeriodDays } = data;
        
        // Get or create Stripe customer
        let customerId = data.customerId;
        if (!customerId) {
          const customer = await stripePaymentService.createCustomer(
            decodedToken.email!,
            decodedToken.name,
            tenantId
          );
          customerId = customer.id;
        }

        const checkoutSession = await stripePaymentService.createCheckoutSession(
          priceId,
          tenantId,
          customerId,
          successUrl,
          cancelUrl,
          trialPeriodDays
        );

        return NextResponse.json(checkoutSession);

      case 'create-billing-portal':
        const { customerId: customerIdForPortal, returnUrl } = data;
        
        const portalSession = await stripePaymentService.createBillingPortalSession(
          customerIdForPortal,
          returnUrl
        );

        return NextResponse.json(portalSession);

      case 'update-subscription':
        const { subscriptionId, newPriceId, quantity } = data;
        
        const updatedSubscription = await stripePaymentService.updateSubscription(
          subscriptionId,
          newPriceId,
          quantity
        );

        return NextResponse.json(updatedSubscription);

      case 'cancel-subscription':
        const { subscriptionId: subIdToCancel, cancelAtPeriodEnd = true } = data;
        
        await stripePaymentService.cancelSubscription(subIdToCancel, cancelAtPeriodEnd);
        
        return NextResponse.json({ success: true });

      case 'track-usage':
        const { eventType, resourceId, quantity: usageQuantity = 1, metadata = {} } = data;
        
        await subscriptionService.trackUsage(tenantId, eventType, resourceId, usageQuantity, metadata);
        
        return NextResponse.json({ success: true });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in billing API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
