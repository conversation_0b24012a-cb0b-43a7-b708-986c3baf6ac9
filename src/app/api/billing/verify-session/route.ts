import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { subscriptionService } from '@/services/subscriptionService';
import { auth } from '@/lib/firebase-admin';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('session_id');

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID required' }, { status: 400 });
    }

    // Retrieve the checkout session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId, {
      expand: ['subscription', 'subscription.items.data.price']
    });

    if (!session) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    // Get subscription details
    const subscription = session.subscription as Stripe.Subscription;
    const price = subscription.items.data[0].price;
    
    // Find the plan based on the price ID
    const plans = await subscriptionService.getAvailablePlans();
    const plan = plans.find(p => 
      p.stripePriceId === price.id || p.stripeYearlyPriceId === price.id
    );

    if (!plan) {
      return NextResponse.json({ error: 'Plan not found' }, { status: 404 });
    }

    const billingCycle = price.recurring?.interval === 'year' ? 'yearly' : 'monthly';
    const amount = price.unit_amount ? price.unit_amount / 100 : 0;

    // Return session verification data
    return NextResponse.json({
      sessionId: session.id,
      planName: plan.displayName,
      planId: plan.id,
      amount,
      currency: price.currency,
      billingCycle,
      subscriptionId: subscription.id,
      customerId: subscription.customer,
      status: subscription.status,
      currentPeriodStart: new Date(subscription?.current_period_start * 1000),
      currentPeriodEnd: new Date(subscription?.current_period_end * 1000),
      nextBilling: new Date(subscription?.current_period_end * 1000)
    });
  } catch (error) {
    console.error('Error verifying session:', error);
    return NextResponse.json(
      { error: 'Failed to verify session' },
      { status: 500 }
    );
  }
}
