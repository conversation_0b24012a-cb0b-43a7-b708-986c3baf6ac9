import { NextRequest, NextResponse } from 'next/server';
import {
  collection,
  doc,
  getDocs,
  updateDoc,
  serverTimestamp,
  getDoc
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS, TENANT_ISOLATED_COLLECTIONS, requiresTenantId } from '@/lib/collections';

export async function POST(request: NextRequest) {
  try {
    // Temporarily disabled to fix build issues
    return NextResponse.json(
      { success: false, message: 'Safe tenant isolation fix temporarily disabled' },
      { status: 503 }
    );

    // // Only allow in development
    // if (process.env.NODE_ENV === 'production') {
    //   return NextResponse.json(
    //     { success: false, message: 'Safe tenant isolation fix not available in production' },
    //     { status: 403 }
    //   );
    // }

    // const body = await request.json();
    // const { action, defaultTenantId = 'evexa-super-admin-tenant' } = body;

    // if (action === 'fix') {
    //   console.log('🔧 Starting SAFE tenant isolation fix...');

    //   const results = {
    //     totalViolations: 0,
    //     fixedViolations: 0,
    //     failedFixes: 0,
    //     errors: [] as string[],
    //     fixedCollections: [] as string[]
    //   };

    //   // Process only tenant-isolated collections (skip global collections)
    //   for (const collectionName of TENANT_ISOLATED_COLLECTIONS) {
    //     try {
    //       console.log(`🔍 Processing collection: ${collectionName}`);

    //       const collectionRef = collection(db, collectionName);
    //       const snapshot = await getDocs(collectionRef);

    //       let collectionViolations = 0;
    //       let collectionFixed = 0;

    //       // Process documents one by one to avoid conflicts
    //       for (const docSnapshot of snapshot.docs) {
    //         const data = docSnapshot.data();

    //         // Check if document needs tenantId
    //         if (!data.tenantId || typeof data.tenantId !== 'string') {
    //           results.totalViolations++;
    //           collectionViolations++;

    //           try {
    //             // Verify document still exists before updating
    //             const docRef = doc(db, collectionName, docSnapshot.id);
    //             const currentDoc = await getDoc(docRef);

    //             if (currentDoc.exists()) {
    //               // Safe update - only add tenantId field
    //               await updateDoc(docRef, {
    //                 tenantId: defaultTenantId,
    //                 updatedAt: serverTimestamp(),
    //                 tenantIsolationFixed: true,
    //                 tenantIsolationFixedAt: serverTimestamp()
    //               });

    //               results.fixedViolations++;
    //               collectionFixed++;
    //               console.log(`✅ Fixed document ${docSnapshot.id} in ${collectionName}`);
    //             } else {
    //               console.warn(`⚠️ Document ${docSnapshot.id} no longer exists in ${collectionName}`);
    //             }
    //           } catch (docError) {
    //             results.failedFixes++;
    //             results.errors.push(`Failed to fix ${docSnapshot.id} in ${collectionName}: ${docError}`);
    //             console.error(`❌ Failed to fix ${docSnapshot.id}:`, docError);
    //           }
    //         }
    //       }

    //       if (collectionFixed > 0) {
    //         results.fixedCollections.push(collectionName);
    //         console.log(`✅ Fixed ${collectionFixed}/${collectionViolations} violations in ${collectionName}`);
    //       } else if (collectionViolations > 0) {
    //         console.warn(`⚠️ Found ${collectionViolations} violations in ${collectionName} but couldn't fix any`);
    //       }

    //     } catch (collectionError) {
    //       results.errors.push(`Error processing collection ${collectionName}: ${collectionError}`);
    //       console.error(`❌ Error processing ${collectionName}:`, collectionError);
    //     }
    //   }

    //   const success = results.fixedViolations > 0 && results.failedFixes === 0;

    //   console.log(`🎯 Safe fix complete: ${results.fixedViolations} fixed, ${results.failedFixes} failed`);

    //   return NextResponse.json({
    //     success,
    //     message: success
    //       ? `Successfully fixed ${results.fixedViolations} tenant isolation violations`
    //       : `Fix completed with issues: ${results.errors.join(', ')}`,
    //     data: results
    //   });
    // }

    // // Default action: detect violations
    // console.log('🔍 Detecting tenant isolation violations...');

    // const violations = [];
    // const violatingCollections = [];
    // let totalDocuments = 0;

    // for (const collectionName of TENANT_ISOLATED_COLLECTIONS) {
    //   try {
    //     const collectionRef = collection(db, collectionName);
    //     const snapshot = await getDocs(collectionRef);

    //     let collectionHasViolations = false;

    //     snapshot.docs.forEach(docSnapshot => {
    //       totalDocuments++;
    //       const data = docSnapshot.data();

    //       if (!data.tenantId || typeof data.tenantId !== 'string') {
    //         violations.push({
    //           collectionName,
    //           documentId: docSnapshot.id,
    //           hasData: Object.keys(data).length > 0
    //         });
    //         collectionHasViolations = true;
    //       }
    //     });

    //     if (collectionHasViolations) {
    //       violatingCollections.push(collectionName);
    //     }

    //   } catch (error) {
    //     console.error(`Error scanning ${collectionName}:`, error);
    //   }
    // }

    // return NextResponse.json({
    //   success: violations.length === 0,
    //   message: violations.length === 0
    //     ? 'No tenant isolation violations found'
    //     : `Found ${violations.length} tenant isolation violations`,
    //   data: {
    //     totalViolations: violations.length,
    //     totalDocuments,
    //     violatingCollections,
    //     violations: violations.slice(0, 10), // First 10 for preview
    //     hasMore: violations.length > 10
    //   }
    // });

  } catch (error) {
    console.error('Error in safe tenant isolation fix:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Safe tenant isolation fix failed', 
        error: String(error) 
      },
      { status: 500 }
    );
  }
}
