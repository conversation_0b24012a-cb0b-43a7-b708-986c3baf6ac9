import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, getDocs } from 'firebase/firestore';

/**
 * Firebase Real Discovery API
 * 
 * Checks the ACTUAL list of collections you can see in Firebase console
 */
export async function GET(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Real discovery not available in production' },
        { status: 403 }
      );
    }

    console.log('🔍 Starting REAL Firebase discovery based on console list...');
    
    // The ACTUAL list from your Firebase console
    const REAL_COLLECTIONS = [
      'a_p_i_partnerships',
      'acknowledgments',
      'aiConfiguration',
      'aiUsageRecords',
      'analytics_configs',
      'approval_documents',
      'attendee_profiles',
      'boothAnalytics',
      'booth_analytics_enhanced',
      'booth_attendance_records',
      'booth_meetings',
      'briefingPacks',
      'budgets',
      'budgets_enhanced',
      'competitor_exhibition_presences_enhanced',
      'config',
      'contactSegments',
      'coreTeamMembers',
      'dashboardLayouts',
      'document_signatures',
      'emailSequences',
      'emailTemplates',
      'email_sequences',
      'event_attendance_records',
      'event_workflows',
      'events',
      'exhibitionTeamAssignments',
      'exhibition_chat_channels',
      'exhibition_templates',
      'exhibitions',
      'expenseCategoryDefinitions',
      'expenses',
      'expenses_enhanced',
      'flightItineraries',
      'gift_items',
      'insuranceClaims',
      'internationalCompliance',
      'invoices',
      'leads',
      'legacy_compliance_frameworks',
      'logisticsShipments',
      'mail',
      'marketing_materials',
      'media_contacts',
      'notifications',
      'personalizationProfiles',
      'portfolio_analytics',
      'postShowHubs',
      'press_kits',
      'procurement_metrics',
      'purchaseOrders',
      'purchaseRequests',
      'purchase_orders',
      'purchase_requests',
      'recommendations',
      'release_notes',
      'reportInstanceConfigs',
      'response_templates',
      'routeOptimizations',
      'security-events',
      'shipments',
      'signing_requests',
      'social_posts',
      'subscription_plans',
      'support_tickets',
      'tasks',
      'temporary_staff',
      'tenant-data',
      'tenant-users',
      'tenants',
      'trainingAcknowledgments',
      'trainingMaterials',
      'travelRequests',
      'travel_entries',
      'userGroups',
      'userPreferences',
      'users',
      'vendorPerformanceReviews',
      'vendor_matching_requests',
      'vendors',
      'venue_integrations',
      'visaDetails',
      'visaPassportManagement',
      'workflow_execution_logs',
      'workflow_executions'
    ];
    
    const foundCollections: { [collectionName: string]: number } = {};
    let totalCollections = 0;
    let totalDocuments = 0;
    let checkedCount = 0;
    
    console.log(`🔍 Checking ${REAL_COLLECTIONS.length} collections from your Firebase console...`);
    
    for (const collectionName of REAL_COLLECTIONS) {
      try {
        checkedCount++;
        if (checkedCount % 20 === 0) {
          console.log(`   🔍 Checked ${checkedCount}/${REAL_COLLECTIONS.length} collections...`);
        }
        
        const querySnapshot = await getDocs(collection(db, collectionName));
        const docCount = querySnapshot.docs.length;
        
        if (docCount > 0) {
          foundCollections[collectionName] = docCount;
          totalCollections++;
          totalDocuments += docCount;
          console.log(`   📁 FOUND: ${collectionName} (${docCount} documents)`);
        } else {
          console.log(`   📂 EMPTY: ${collectionName}`);
        }
        
      } catch (error) {
        console.error(`   ❌ ERROR accessing ${collectionName}:`, error);
      }
    }
    
    console.log(`🔍 REAL discovery complete: Found ${totalCollections} collections with ${totalDocuments} total documents`);
    
    // Analyze the chaos
    const standardCollections: { [key: string]: number } = {};
    const chaoticCollections: { [key: string]: number } = {};
    const systemCollections: { [key: string]: number } = {};
    const duplicateGroups: { [key: string]: string[] } = {};
    
    const STANDARD_NAMES = [
      'users', 'exhibitions', 'events', 'tasks', 'leads', 'vendors',
      'budgets', 'expenses', 'purchase_requests', 'purchase_orders', 'invoices',
      'email_campaigns', 'social_posts', 'notifications', 'marketing_materials',
      'travel_entries', 'shipments', 'booth_meetings',
      'release_notes', 'support_tickets', 'audit_logs',
      'gift_items', 'media_contacts', 'press_kits', 'approval_documents', 'signing_requests'
    ];
    
    const SYSTEM_NAMES = ['dashboardLayouts', 'userPreferences', 'config', 'tenants'];
    
    // Find duplicates
    const baseNames: { [key: string]: string[] } = {};
    
    for (const collectionName of Object.keys(foundCollections)) {
      // Convert to base name for duplicate detection
      const baseName = collectionName.toLowerCase().replace(/[_-]/g, '');
      if (!baseNames[baseName]) {
        baseNames[baseName] = [];
      }
      baseNames[baseName].push(collectionName);
      
      // Categorize
      if (STANDARD_NAMES.includes(collectionName)) {
        standardCollections[collectionName] = foundCollections[collectionName];
      } else if (SYSTEM_NAMES.includes(collectionName)) {
        systemCollections[collectionName] = foundCollections[collectionName];
      } else {
        chaoticCollections[collectionName] = foundCollections[collectionName];
      }
    }
    
    // Find actual duplicates
    for (const [baseName, collections] of Object.entries(baseNames)) {
      if (collections.length > 1) {
        duplicateGroups[baseName] = collections;
      }
    }
    
    return NextResponse.json({
      success: true,
      message: `REAL Firebase discovery complete - MASSIVE COLLECTION CHAOS DETECTED!`,
      data: {
        totalCollections,
        totalDocuments,
        checkedCollections: REAL_COLLECTIONS.length,
        foundCollections,
        analysis: {
          standard: {
            collections: standardCollections,
            count: Object.keys(standardCollections).length,
            documents: Object.values(standardCollections).reduce((sum, count) => sum + count, 0)
          },
          chaotic: {
            collections: chaoticCollections,
            count: Object.keys(chaoticCollections).length,
            documents: Object.values(chaoticCollections).reduce((sum, count) => sum + count, 0)
          },
          system: {
            collections: systemCollections,
            count: Object.keys(systemCollections).length,
            documents: Object.values(systemCollections).reduce((sum, count) => sum + count, 0)
          },
          duplicates: duplicateGroups,
          duplicateCount: Object.keys(duplicateGroups).length
        },
        chaos: {
          hasMassiveChaos: Object.keys(chaoticCollections).length > 10,
          chaoticCollectionCount: Object.keys(chaoticCollections).length,
          duplicateGroupCount: Object.keys(duplicateGroups).length,
          totalChaoticDocuments: Object.values(chaoticCollections).reduce((sum, count) => sum + count, 0)
        }
      }
    });
    
  } catch (error) {
    console.error('Real discovery error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Real discovery failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
