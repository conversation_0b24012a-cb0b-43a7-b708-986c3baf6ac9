import { NextRequest, NextResponse } from 'next/server';
import { invoiceManagementService } from '@/services/invoiceManagementService';
import { auth } from '@/lib/firebase-admin';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    switch (action) {
      case 'list':
        const filters = {
          status: searchParams.get('status') as any,
          type: searchParams.get('type') as any,
          customerId: searchParams.get('customerId') || undefined
        };
        const invoices = await invoiceManagementService.getInvoices(tenantId, filters);
        return NextResponse.json(invoices);

      case 'templates':
        const templates = await invoiceManagementService.getTemplates(tenantId);
        return NextResponse.json(templates);

      case 'bulk-operations':
        const bulkOps = await invoiceManagementService.getBulkOperations(tenantId);
        return NextResponse.json(bulkOps);

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in invoices API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const data = await request.json();
    const { action } = data;

    switch (action) {
      case 'create-invoice':
        const { invoice } = data;
        const newInvoice = await invoiceManagementService.createInvoice({
          ...invoice,
          tenantId
        });
        return NextResponse.json(newInvoice);

      case 'create-template':
        const { template } = data;
        const newTemplate = await invoiceManagementService.createTemplate({
          ...template,
          tenantId
        });
        return NextResponse.json(newTemplate);

      case 'send-invoice':
        const { invoiceId } = data;
        await invoiceManagementService.sendInvoiceViaStripe(invoiceId);
        return NextResponse.json({ success: true });

      case 'approve-invoice':
        const { invoiceId: approveId, approvedBy, notes } = data;
        await invoiceManagementService.approveInvoice(approveId, approvedBy, notes);
        return NextResponse.json({ success: true });

      case 'reject-invoice':
        const { invoiceId: rejectId, rejectedBy, reason } = data;
        await invoiceManagementService.rejectInvoice(rejectId, rejectedBy, reason);
        return NextResponse.json({ success: true });

      case 'submit-for-approval':
        const { invoiceId: submitId } = data;
        await invoiceManagementService.submitForApproval(submitId);
        return NextResponse.json({ success: true });

      case 'create-bulk-operation':
        const { operation, invoiceIds } = data;
        const bulkOp = await invoiceManagementService.createBulkOperation(
          tenantId,
          operation,
          invoiceIds
        );
        
        // Process the bulk operation asynchronously
        invoiceManagementService.processBulkOperation(bulkOp.id).catch(console.error);
        
        return NextResponse.json(bulkOp);

      case 'set-default-template':
        const { templateId } = data;
        await invoiceManagementService.setDefaultTemplate(tenantId, templateId);
        return NextResponse.json({ success: true });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in invoices API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const invoiceId = searchParams.get('invoiceId');
    const templateId = searchParams.get('templateId');

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const updates = await request.json();

    if (invoiceId) {
      await invoiceManagementService.updateInvoice(invoiceId, updates);
      return NextResponse.json({ success: true });
    }

    if (templateId) {
      await invoiceManagementService.updateTemplate(templateId, updates);
      return NextResponse.json({ success: true });
    }

    return NextResponse.json({ error: 'Invoice ID or Template ID is required' }, { status: 400 });
  } catch (error) {
    console.error('Error updating invoice/template:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const invoiceId = searchParams.get('invoiceId');
    const templateId = searchParams.get('templateId');

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    if (invoiceId) {
      await invoiceManagementService.deleteInvoice(invoiceId);
      return NextResponse.json({ success: true });
    }

    if (templateId) {
      await invoiceManagementService.deleteTemplate(templateId);
      return NextResponse.json({ success: true });
    }

    return NextResponse.json({ error: 'Invoice ID or Template ID is required' }, { status: 400 });
  } catch (error) {
    console.error('Error deleting invoice/template:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
