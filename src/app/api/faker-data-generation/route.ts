import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * Faker.js Data Generation API
 *
 * DEVELOPMENT ONLY: Generates realistic test data for Firebase collections
 * This API is strictly for development and testing purposes only
 * All generated data is real Firebase data, not mock/fake data in the application
 */
export async function POST(request: NextRequest) {
  try {
    // Strict development-only enforcement
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { success: false, error: 'Data generation is strictly prohibited in production' },
        { status: 403 }
      );
    }

    // Additional safety check
    if (!process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID?.includes('dev') &&
        !process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID?.includes('test')) {
      return NextResponse.json(
        { success: false, error: 'Data generation only allowed on development/test Firebase projects' },
        { status: 403 }
      );
    }

    const { action } = await request.json();

    console.log(`🎲 API: Faker.js data generation requested - ${action}`);
    
    let command: string;
    let successMessage: string;

    switch (action) {
      case 'faker-full':
        command = 'npm run generate-data';
        successMessage = 'Full Faker.js dataset generated successfully! Generated comprehensive realistic data across all collections.';
        break;
        
      case 'faker-sample':
        command = 'npm run generate-data:sample';
        successMessage = 'Sample Faker.js dataset generated successfully! Generated smaller test dataset.';
        break;
        
      case 'faker-verify':
        command = 'npm run verify-data';
        successMessage = 'Faker.js data verification completed successfully!';
        break;
        
      default:
        return NextResponse.json({
          success: false,
          error: `Unknown Faker.js action: ${action}`
        }, { status: 400 });
    }

    // Execute the npm command
    console.log(`🚀 Executing: ${command}`);
    
    const { stdout, stderr } = await execAsync(command, {
      cwd: process.cwd(),
      timeout: 300000, // 5 minutes timeout
      env: { ...process.env }
    });

    // Log output for debugging
    if (stdout) {
      console.log('📊 Faker.js Output:', stdout);
    }
    if (stderr) {
      console.warn('⚠️ Faker.js Warnings:', stderr);
    }

    // Parse output for statistics if available
    let statistics = {};
    try {
      // Extract statistics from stdout
      const lines = stdout.split('\n');
      const statsLines = lines.filter(line => 
        line.includes('Generated') || 
        line.includes('documents') ||
        line.includes('user profiles') ||
        line.includes('exhibitions') ||
        line.includes('events') ||
        line.includes('tasks') ||
        line.includes('leads') ||
        line.includes('budgets')
      );
      
      if (statsLines.length > 0) {
        statistics = {
          output: statsLines,
          summary: stdout.includes('✅') ? 'Success' : 'Completed'
        };
      }
    } catch (parseError) {
      console.warn('Could not parse statistics:', parseError);
    }

    return NextResponse.json({
      success: true,
      message: successMessage,
      data: {
        action,
        command,
        statistics,
        output: stdout,
        warnings: stderr || undefined
      }
    });

  } catch (error: any) {
    console.error('❌ Faker.js data generation failed:', error);
    
    return NextResponse.json({
      success: false,
      message: `Faker.js data generation failed: ${error.message}`,
      error: error.message,
      data: {
        stderr: error.stderr,
        stdout: error.stdout
      }
    }, { status: 500 });
  }
}
