/**
 * EVEXA Validation Statistics API
 * 
 * Provides runtime validation statistics and violation data
 */

import { NextRequest, NextResponse } from 'next/server';
import { runtimeValidator } from '@/lib/runtimeValidator';

export async function GET(request: NextRequest) {
  try {
    // Get validation statistics from runtime validator
    const stats = runtimeValidator.getValidationStats();
    
    return NextResponse.json({
      success: true,
      stats: stats,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to get validation stats:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to retrieve validation statistics',
      message: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'clear-violations':
        runtimeValidator.clearViolationLog();
        return NextResponse.json({
          success: true,
          message: 'Validation violation log cleared successfully',
        });

      case 'update-config':
        const { config } = body;
        runtimeValidator.updateConfig(config);
        return NextResponse.json({
          success: true,
          message: 'Validation configuration updated successfully',
          config: runtimeValidator.getConfig(),
        });

      case 'get-config':
        return NextResponse.json({
          success: true,
          config: runtimeValidator.getConfig(),
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action',
          message: `Action "${action}" is not supported`,
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Failed to process validation stats request:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to process request',
      message: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
