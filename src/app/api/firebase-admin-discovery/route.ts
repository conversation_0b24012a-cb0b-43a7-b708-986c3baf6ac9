import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from '@/lib/firebase-admin';

/**
 * Firebase Admin Discovery API
 * 
 * Uses Firebase Admin SDK to list ALL collections in the database
 * This should show us everything that exists
 */
export async function GET(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Admin discovery not available in production' },
        { status: 403 }
      );
    }

    console.log('🔍 Starting Firebase Admin discovery...');
    
    if (!adminDb) {
      return NextResponse.json(
        { error: 'Firebase Admin not initialized' },
        { status: 500 }
      );
    }

    // List all collections using Admin SDK
    const collections = await adminDb.listCollections();
    
    const collectionData: { [collectionName: string]: number } = {};
    let totalDocuments = 0;
    
    console.log(`📁 Found ${collections.length} collections via Admin SDK`);
    
    for (const collectionRef of collections) {
      try {
        const snapshot = await collectionRef.get();
        const docCount = snapshot.docs.length;
        
        collectionData[collectionRef.id] = docCount;
        totalDocuments += docCount;
        
        console.log(`   📁 ${collectionRef.id}: ${docCount} documents`);
      } catch (error) {
        console.error(`   ❌ Error reading ${collectionRef.id}:`, error);
        collectionData[collectionRef.id] = -1; // Mark as error
      }
    }
    
    // Sort collections by name for easier reading
    const sortedCollections = Object.keys(collectionData)
      .sort()
      .reduce((sorted: { [key: string]: number }, key) => {
        sorted[key] = collectionData[key];
        return sorted;
      }, {});
    
    console.log(`🔍 Admin discovery complete: ${collections.length} collections, ${totalDocuments} total documents`);
    
    return NextResponse.json({
      success: true,
      message: `Firebase Admin discovery complete`,
      data: {
        totalCollections: collections.length,
        totalDocuments,
        collections: sortedCollections,
        collectionNames: Object.keys(sortedCollections),
        analysis: {
          hasData: totalDocuments > 0,
          collectionsWithData: Object.entries(sortedCollections).filter(([_, count]) => count > 0).length,
          emptyCollections: Object.entries(sortedCollections).filter(([_, count]) => count === 0).length,
          errorCollections: Object.entries(sortedCollections).filter(([_, count]) => count === -1).length
        }
      }
    });
    
  } catch (error) {
    console.error('Firebase Admin discovery error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Firebase Admin discovery failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
