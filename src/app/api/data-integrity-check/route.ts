import { NextRequest, NextResponse } from 'next/server';
import { DataIntegrityValidator } from '@/lib/dataIntegrityValidator';

/**
 * Data Integrity Check API
 * 
 * Validates database integrity and prevents collection chaos
 * Checks collection naming, tenant scoping, metadata consistency
 */
export async function GET(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Data integrity check not available in production' },
        { status: 403 }
      );
    }

    console.log('🔍 Starting comprehensive data integrity check...');
    
    const validator = new DataIntegrityValidator();
    const result = await validator.validateDatabase();
    
    // Log results
    if (result.isValid) {
      console.log('✅ Data integrity check PASSED');
      console.log(`📊 Summary: ${result.summary.validCollections}/${result.summary.totalCollections} collections valid, ${result.summary.validDocuments}/${result.summary.totalDocuments} documents valid`);
    } else {
      console.log('❌ Data integrity check FAILED');
      console.log(`📊 Summary: ${result.summary.invalidCollections} invalid collections, ${result.summary.invalidDocuments} invalid documents`);
      console.log('🚨 Errors:', result.errors);
    }
    
    if (result.warnings.length > 0) {
      console.log('⚠️ Warnings:', result.warnings);
    }
    
    return NextResponse.json({
      success: result.isValid,
      message: result.isValid 
        ? 'Data integrity check passed - no collection chaos detected' 
        : 'Data integrity check failed - collection chaos or data issues detected',
      data: {
        validationPassed: result.isValid,
        summary: result.summary,
        errors: result.errors,
        warnings: result.warnings,
        recommendations: result.isValid 
          ? ['Database is clean and follows EVEXA standards']
          : [
              'Run nuclear reset to clean up invalid collections',
              'Use Professional Data Manager for all seeding',
              'Ensure all documents have proper metadata',
              'Use standardized collection names from EVEXA_DATA_SCHEMA'
            ]
      }
    });
    
  } catch (error) {
    console.error('Data integrity check error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Data integrity check failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST endpoint to validate specific collection
 */
export async function POST(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Collection validation not available in production' },
        { status: 403 }
      );
    }

    const { collectionName } = await request.json();
    
    if (!collectionName) {
      return NextResponse.json(
        { error: 'Collection name is required' },
        { status: 400 }
      );
    }

    console.log(`🔍 Validating collection: ${collectionName}`);
    
    const validator = new DataIntegrityValidator();
    const result = await validator.validateCollection(collectionName);
    
    // Log results
    if (result.isValid) {
      console.log(`✅ Collection ${collectionName} validation PASSED`);
      console.log(`📊 ${result.validDocuments}/${result.documentCount} documents valid`);
    } else {
      console.log(`❌ Collection ${collectionName} validation FAILED`);
      console.log(`📊 ${result.invalidDocuments}/${result.documentCount} documents invalid`);
      console.log('🚨 Errors:', result.errors);
    }
    
    return NextResponse.json({
      success: result.isValid,
      message: result.isValid 
        ? `Collection ${collectionName} validation passed` 
        : `Collection ${collectionName} validation failed`,
      data: {
        collectionName: result.collectionName,
        validationPassed: result.isValid,
        documentCount: result.documentCount,
        validDocuments: result.validDocuments,
        invalidDocuments: result.invalidDocuments,
        errors: result.errors,
        warnings: result.warnings,
        documentDetails: result.documents
      }
    });
    
  } catch (error) {
    console.error('Collection validation error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Collection validation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
