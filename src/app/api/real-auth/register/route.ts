/**
 * Real User Registration API
 * 
 * Production-ready user registration with tenant management
 * Creates Firebase Auth user + Firestore profile + tenant assignment
 */

import { NextRequest, NextResponse } from 'next/server';
import { 
  createUserWithEmailAndPassword, 
  updateProfile,
  sendEmailVerification
} from 'firebase/auth';
import { doc, setDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { 
  createTenant, 
  addUserToTenant, 
  createEvexaSuperAdminTenant,
  EVEXA_SUPER_ADMIN_TENANT 
} from '@/services/realTenantService';
import type { EvexUser } from '@/types/firestore';

export interface RegisterRequest {
  // User details
  email: string;
  password: string;
  displayName: string;
  jobTitle?: string;
  department?: string;
  phone?: string;
  
  // Company/Tenant details
  companyName: string;
  companyDomain: string;
  industry?: string;
  companySize?: string;
  country?: string;
  
  // Plan selection
  plan?: 'free' | 'professional' | 'enterprise';
  
  // Special flags
  createTenant?: boolean; // If true, creates new tenant, otherwise joins existing
  inviteCode?: string; // For joining existing tenant
}

export async function POST(request: NextRequest) {
  try {
    const body: RegisterRequest = await request.json();
    
    console.log('🧑‍💼 Real user registration started:', body.email);
    
    // Validate required fields
    if (!body.email || !body.password || !body.displayName || !body.companyName) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: email, password, displayName, companyName'
      }, { status: 400 });
    }

    // Create Firebase Auth user
    console.log('🔐 Creating Firebase Auth user...');
    const userCredential = await createUserWithEmailAndPassword(
      auth, 
      body.email, 
      body.password
    );
    
    const firebaseUser = userCredential.user;
    
    // Update Firebase Auth profile
    await updateProfile(firebaseUser, {
      displayName: body.displayName
    });
    
    // Send email verification
    await sendEmailVerification(firebaseUser);
    
    let tenantId: string;
    let userRole: 'owner' | 'admin' | 'manager' | 'user' = 'user';
    
    // Handle tenant creation or assignment
    if (body.createTenant !== false) { // Default to creating tenant
      console.log('🏢 Creating new tenant...');
      
      const tenantResult = await createTenant({
        name: body.companyName,
        domain: body.companyDomain || body.companyName.toLowerCase().replace(/\s+/g, '-'),
        ownerEmail: body.email,
        plan: body.plan || 'free',
        industry: body.industry,
        companySize: body.companySize,
        country: body.country
      });
      
      if (!tenantResult.success) {
        // If tenant creation fails, clean up the Firebase user
        await firebaseUser.delete();
        return NextResponse.json({
          success: false,
          error: tenantResult.error || 'Failed to create company'
        }, { status: 400 });
      }
      
      tenantId = tenantResult.tenant!.id;
      userRole = 'owner';
      
    } else {
      // TODO: Handle joining existing tenant with invite code
      tenantId = EVEXA_SUPER_ADMIN_TENANT; // Default for now
      userRole = 'user';
    }
    
    // Create user profile in Firestore
    console.log('👤 Creating user profile...');
    const userProfile: Omit<EvexUser, 'id'> = {
      email: body.email,
      displayName: body.displayName,
      role: userRole === 'owner' ? 'admin' : 'user', // Map to EvexUser role
      status: 'active',
      tenantId,
      jobTitle: body.jobTitle || '',
      department: body.department || '',
      phone: body.phone || '',
      profileImageUrl: `https://api.dicebear.com/7.x/avataaars/svg?seed=${body.displayName}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      lastLoginAt: new Date(),
      isEmailVerified: false,
      preferences: {
        theme: 'system',
        notifications: true,
        language: 'en'
      }
    };
    
    // Save user profile to Firestore
    await setDoc(doc(db, COLLECTIONS.USER_PROFILES, firebaseUser.uid), {
      ...userProfile,
      id: firebaseUser.uid
    });
    
    // Add user to tenant
    console.log('🔗 Adding user to tenant...');
    const tenantResult = await addUserToTenant(
      firebaseUser.uid,
      tenantId,
      userRole
    );
    
    if (!tenantResult.success) {
      console.warn('⚠️ Failed to add user to tenant, but user created successfully');
    }
    
    console.log(`✅ User registration completed: ${body.email} (${firebaseUser.uid})`);
    
    return NextResponse.json({
      success: true,
      message: 'Registration successful! Please check your email to verify your account.',
      user: {
        id: firebaseUser.uid,
        email: body.email,
        displayName: body.displayName,
        tenantId,
        role: userRole,
        isEmailVerified: false
      },
      tenant: {
        id: tenantId,
        name: body.companyName,
        role: userRole
      }
    });
    
  } catch (error: any) {
    console.error('❌ Registration failed:', error);
    
    // Handle specific Firebase Auth errors
    let errorMessage = 'Registration failed';
    
    if (error.code === 'auth/email-already-in-use') {
      errorMessage = 'An account with this email already exists';
    } else if (error.code === 'auth/weak-password') {
      errorMessage = 'Password is too weak. Please use at least 6 characters';
    } else if (error.code === 'auth/invalid-email') {
      errorMessage = 'Invalid email address';
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'EVEXA Real User Registration API',
    description: 'POST to register new users with tenant management',
    requiredFields: [
      'email',
      'password', 
      'displayName',
      'companyName'
    ],
    optionalFields: [
      'jobTitle',
      'department', 
      'phone',
      'companyDomain',
      'industry',
      'companySize',
      'country',
      'plan',
      'createTenant',
      'inviteCode'
    ],
    plans: ['free', 'professional', 'enterprise']
  });
}
