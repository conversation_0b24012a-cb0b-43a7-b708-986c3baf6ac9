/**
 * Create Real Users for Testing
 * 
 * Creates production-ready users with proper tenant management
 * This replaces all test/mock user creation
 */

import { NextRequest, NextResponse } from 'next/server';
import { 
  createUserWithEmailAndPassword, 
  updateProfile,
  signOut
} from 'firebase/auth';
import { doc, setDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { 
  createTenant, 
  addUserToTenant, 
  createEvexaSuperAdminTenant,
  EVEXA_SUPER_ADMIN_TENANT 
} from '@/services/realTenantService';
import type { EvexUser } from '@/types/firestore';

// Real users to create for testing
const REAL_USERS = [
  {
    email: '<EMAIL>',
    password: 'Superman123!',
    displayName: 'Superman EVEXA',
    role: 'super_admin' as const,
    jobTitle: 'Master Super Admin',
    department: 'EVEXA Development Company',
    tenantId: EVEXA_SUPER_ADMIN_TENANT,
    tenantRole: 'owner' as const
  },
  {
    email: '<EMAIL>',
    password: 'Admin123!',
    displayName: 'Admin User',
    role: 'admin' as const,
    jobTitle: 'System Administrator',
    department: 'IT Department',
    companyName: 'Demo Company Ltd',
    companyDomain: 'demo-company',
    tenantRole: 'owner' as const
  },
  {
    email: '<EMAIL>',
    password: 'Manager123!',
    displayName: 'Manager User',
    role: 'management' as const,
    jobTitle: 'Exhibition Manager',
    department: 'Marketing Department',
    companyName: 'Demo Company Ltd',
    companyDomain: 'demo-company',
    tenantRole: 'admin' as const
  },
  {
    email: '<EMAIL>',
    password: 'User123!',
    displayName: 'Regular User',
    role: 'user' as const,
    jobTitle: 'Exhibition Coordinator',
    department: 'Marketing Department',
    companyName: 'Demo Company Ltd',
    companyDomain: 'demo-company',
    tenantRole: 'user' as const
  }
];

export async function POST(request: NextRequest) {
  try {
    console.log('🧑‍💼 Creating real users with proper tenant management...');
    
    // First, ensure EVEXA super admin tenant exists
    console.log('🏢 Setting up EVEXA super admin tenant...');
    await createEvexaSuperAdminTenant();
    
    const results = [];
    let demoCompanyTenantId: string | null = null;
    
    for (const userData of REAL_USERS) {
      try {
        console.log(`Creating user: ${userData.email}`);
        
        // Create Firebase Auth user
        const userCredential = await createUserWithEmailAndPassword(
          auth, 
          userData.email, 
          userData.password
        );
        
        const firebaseUser = userCredential.user;
        
        // Update Firebase Auth profile
        await updateProfile(firebaseUser, {
          displayName: userData.displayName
        });
        
        // Handle tenant creation/assignment
        let tenantId: string;
        
        if (userData.tenantId) {
          // Use existing tenant (EVEXA)
          tenantId = userData.tenantId;
        } else if (userData.companyName && !demoCompanyTenantId) {
          // Create demo company tenant (only once)
          console.log(`🏢 Creating tenant: ${userData.companyName}`);
          const tenantResult = await createTenant({
            name: userData.companyName,
            domain: userData.companyDomain!,
            ownerEmail: userData.email,
            plan: 'professional',
            industry: 'Technology',
            companySize: '11-50',
            country: 'United States'
          });
          
          if (tenantResult.success) {
            demoCompanyTenantId = tenantResult.tenant!.id;
            tenantId = demoCompanyTenantId;
          } else {
            throw new Error(`Failed to create tenant: ${tenantResult.error}`);
          }
        } else {
          // Use existing demo company tenant
          tenantId = demoCompanyTenantId!;
        }
        
        // Create user profile in Firestore
        const userProfile: Omit<EvexUser, 'id'> = {
          email: userData.email,
          displayName: userData.displayName,
          role: userData.role,
          status: 'active',
          tenantId,
          jobTitle: userData.jobTitle,
          department: userData.department,
          phone: '******-REAL',
          profileImageUrl: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userData.displayName}`,
          createdAt: new Date(),
          updatedAt: new Date(),
          lastLoginAt: new Date(),
          isEmailVerified: true,
          preferences: {
            theme: 'system',
            notifications: true,
            language: 'en'
          }
        };
        
        // Save to Firestore
        await setDoc(doc(db, COLLECTIONS.USER_PROFILES, firebaseUser.uid), {
          ...userProfile,
          id: firebaseUser.uid
        });
        
        // Add user to tenant
        await addUserToTenant(
          firebaseUser.uid,
          tenantId,
          userData.tenantRole
        );
        
        results.push({
          success: true,
          email: userData.email,
          uid: firebaseUser.uid,
          role: userData.role,
          tenantId,
          tenantRole: userData.tenantRole
        });
        
        console.log(`✅ Created real user: ${userData.email} (${firebaseUser.uid})`);
        
        // Sign out to prevent auth state issues
        await signOut(auth);
        
      } catch (error: any) {
        console.error(`❌ Failed to create user ${userData.email}:`, error.message);
        
        // If user already exists, that's okay
        if (error.code === 'auth/email-already-in-use') {
          results.push({
            success: true,
            email: userData.email,
            message: 'User already exists',
            role: userData.role
          });
        } else {
          results.push({
            success: false,
            email: userData.email,
            error: error.message,
            role: userData.role
          });
        }
      }
    }
    
    const successCount = results.filter(r => r.success).length;
    
    return NextResponse.json({
      success: true,
      message: `Created/verified ${successCount}/${REAL_USERS.length} real users with proper tenant management`,
      users: results,
      loginCredentials: {
        superAdmin: { 
          email: '<EMAIL>', 
          password: 'Superman123!',
          tenant: 'EVEXA Development Company'
        },
        admin: { 
          email: '<EMAIL>', 
          password: 'Admin123!',
          tenant: 'Demo Company Ltd'
        },
        manager: { 
          email: '<EMAIL>', 
          password: 'Manager123!',
          tenant: 'Demo Company Ltd'
        },
        user: { 
          email: '<EMAIL>', 
          password: 'User123!',
          tenant: 'Demo Company Ltd'
        }
      },
      tenants: {
        evexa: EVEXA_SUPER_ADMIN_TENANT,
        demoCompany: demoCompanyTenantId
      }
    });
    
  } catch (error: any) {
    console.error('❌ Real user creation failed:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to create real users'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Create Real Users for Testing',
    description: 'POST to create production-ready users with proper tenant management',
    users: REAL_USERS.map(u => ({
      email: u.email,
      role: u.role,
      displayName: u.displayName,
      company: u.companyName || 'EVEXA Development Company'
    })),
    features: [
      'Real Firebase Authentication',
      'Proper tenant management',
      'Role-based access control',
      'Multi-tenant architecture',
      'Production-ready user profiles'
    ]
  });
}
