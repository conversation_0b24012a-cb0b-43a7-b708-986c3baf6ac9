/**
 * Real User Login API
 * 
 * Production-ready login with tenant validation and session management
 */

import { NextRequest, NextResponse } from 'next/server';
import { signInWithEmailAndPassword } from 'firebase/auth';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { getTenant, getUserTenantRole } from '@/services/realTenantService';
import type { EvexUser } from '@/types/firestore';

export interface LoginRequest {
  email: string;
  password: string;
  tenantDomain?: string; // Optional tenant domain for multi-tenant login
}

export async function POST(request: NextRequest) {
  try {
    const body: LoginRequest = await request.json();
    
    console.log('🔐 Real user login started:', body.email);
    
    // Validate required fields
    if (!body.email || !body.password) {
      return NextResponse.json({
        success: false,
        error: 'Email and password are required'
      }, { status: 400 });
    }

    // Sign in with Firebase Auth
    console.log('🔑 Authenticating with Firebase...');
    const userCredential = await signInWithEmailAndPassword(
      auth, 
      body.email, 
      body.password
    );
    
    const firebaseUser = userCredential.user;
    
    // Get user profile from Firestore
    console.log('👤 Fetching user profile...');
    const userDoc = await getDoc(doc(db, COLLECTIONS.USER_PROFILES, firebaseUser.uid));
    
    if (!userDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'User profile not found. Please contact support.'
      }, { status: 404 });
    }
    
    const userProfile = userDoc.data() as EvexUser;
    
    // Check if user account is active
    if (userProfile.status !== 'active') {
      return NextResponse.json({
        success: false,
        error: 'Account is suspended. Please contact support.'
      }, { status: 403 });
    }
    
    // Get tenant information
    console.log('🏢 Validating tenant access...');
    const tenant = await getTenant(userProfile.tenantId);
    
    if (!tenant) {
      return NextResponse.json({
        success: false,
        error: 'Company not found. Please contact support.'
      }, { status: 404 });
    }
    
    // Check tenant status
    if (tenant.status === 'suspended') {
      return NextResponse.json({
        success: false,
        error: 'Company account is suspended. Please contact support.'
      }, { status: 403 });
    }
    
    if (tenant.status === 'expired') {
      return NextResponse.json({
        success: false,
        error: 'Company subscription has expired. Please renew your subscription.'
      }, { status: 403 });
    }
    
    // Get user's tenant role and permissions
    const tenantUser = await getUserTenantRole(firebaseUser.uid, userProfile.tenantId);
    
    if (!tenantUser || tenantUser.status !== 'active') {
      return NextResponse.json({
        success: false,
        error: 'Access to this company is not authorized.'
      }, { status: 403 });
    }
    
    // Update last login time
    console.log('📝 Updating login timestamp...');
    await updateDoc(doc(db, COLLECTIONS.USER_PROFILES, firebaseUser.uid), {
      lastLoginAt: new Date(),
      updatedAt: new Date()
    });
    
    // Prepare response data
    const responseData = {
      success: true,
      message: 'Login successful',
      user: {
        id: firebaseUser.uid,
        email: userProfile.email,
        displayName: userProfile.displayName,
        role: userProfile.role,
        status: userProfile.status,
        tenantId: userProfile.tenantId,
        jobTitle: userProfile.jobTitle,
        department: userProfile.department,
        profileImageUrl: userProfile.profileImageUrl,
        isEmailVerified: firebaseUser.emailVerified,
        preferences: userProfile.preferences
      },
      tenant: {
        id: tenant.id,
        name: tenant.name,
        domain: tenant.domain,
        status: tenant.status,
        plan: tenant.plan,
        features: tenant.settings.features
      },
      tenantRole: {
        role: tenantUser.role,
        permissions: tenantUser.permissions,
        joinedAt: tenantUser.joinedAt
      },
      session: {
        loginAt: new Date(),
        expiresAt: new Date(Date.now() + 8 * 60 * 60 * 1000) // 8 hours
      }
    };
    
    console.log(`✅ Login successful: ${body.email} (${firebaseUser.uid})`);
    
    return NextResponse.json(responseData);
    
  } catch (error: any) {
    console.error('❌ Login failed:', error);
    
    // Handle specific Firebase Auth errors
    let errorMessage = 'Login failed';
    
    if (error.code === 'auth/user-not-found') {
      errorMessage = 'No account found with this email address';
    } else if (error.code === 'auth/wrong-password') {
      errorMessage = 'Incorrect password';
    } else if (error.code === 'auth/invalid-email') {
      errorMessage = 'Invalid email address';
    } else if (error.code === 'auth/user-disabled') {
      errorMessage = 'This account has been disabled';
    } else if (error.code === 'auth/too-many-requests') {
      errorMessage = 'Too many failed login attempts. Please try again later';
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 401 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'EVEXA Real User Login API',
    description: 'POST to authenticate users with tenant validation',
    requiredFields: [
      'email',
      'password'
    ],
    optionalFields: [
      'tenantDomain'
    ],
    features: [
      'Firebase Authentication',
      'Tenant validation',
      'Role-based access control',
      'Session management',
      'Account status checking'
    ]
  });
}
