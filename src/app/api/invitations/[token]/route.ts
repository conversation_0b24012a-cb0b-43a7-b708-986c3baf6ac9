/**
 * Invitation API Endpoint
 * Public endpoint for fetching invitation details by token
 */

import { NextRequest, NextResponse } from 'next/server';
import { 
  collection, 
  query, 
  where, 
  getDocs, 
  limit 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';

export async function GET(
  request: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const token = params.token;

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      );
    }

    // Query invitation by token
    const q = query(
      collection(db, COLLECTIONS.USER_INVITATIONS),
      where('token', '==', token),
      limit(1)
    );

    const snapshot = await getDocs(q);

    if (snapshot.empty) {
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      );
    }

    const doc = snapshot.docs[0];
    const invitationData = doc.data();

    // Convert Firestore timestamps to ISO strings
    const invitation = {
      id: doc.id,
      ...invitationData,
      sentAt: invitationData.sentAt?.toDate()?.toISOString() || new Date().toISOString(),
      expiresAt: invitationData.expiresAt?.toDate()?.toISOString() || new Date().toISOString(),
      acceptedAt: invitationData.acceptedAt?.toDate()?.toISOString(),
      cancelledAt: invitationData.cancelledAt?.toDate()?.toISOString(),
      lastReminderSent: invitationData.lastReminderSent?.toDate()?.toISOString(),
      createdAt: invitationData.createdAt?.toDate()?.toISOString() || new Date().toISOString(),
      updatedAt: invitationData.updatedAt?.toDate()?.toISOString() || new Date().toISOString()
    };

    // Remove sensitive information
    delete invitation.token; // Don't send token back
    delete invitation.metadata; // Don't expose metadata

    return NextResponse.json(invitation);

  } catch (error) {
    console.error('Error fetching invitation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const token = params.token;
    const body = await request.json();
    const { action, userId } = body;

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      );
    }

    if (action !== 'accept') {
      return NextResponse.json(
        { error: 'Invalid action' },
        { status: 400 }
      );
    }

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get invitation first to determine tenant
    const q = query(
      collection(db, COLLECTIONS.USER_INVITATIONS),
      where('token', '==', token),
      limit(1)
    );

    const snapshot = await getDocs(q);

    if (snapshot.empty) {
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      );
    }

    const invitationDoc = snapshot.docs[0];
    const invitationData = invitationDoc.data();

    // Import and use the invitation service
    const { InvitationManagementService } = await import('@/services/invitationManagementService');
    const invitationService = new InvitationManagementService(invitationData.tenantId);
    
    const result = await invitationService.acceptInvitation(token, userId);

    if (result.success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error accepting invitation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
