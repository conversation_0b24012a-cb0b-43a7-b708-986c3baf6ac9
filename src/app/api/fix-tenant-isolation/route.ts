import { NextRequest, NextResponse } from 'next/server';
// Temporarily commented out to fix build issues
// import {
//   detectTenantIsolationViolations,
//   fixAllTenantIsolationViolations,
//   verifyTenantIsolation
// } from '@/services/tenantIsolationFixService';

export async function POST(request: NextRequest) {
  try {
    // Temporarily disabled to fix build issues
    return NextResponse.json(
      { success: false, message: 'Tenant isolation fix temporarily disabled' },
      { status: 503 }
    );

    // // Only allow in development or for super admin
    // if (process.env.NODE_ENV === 'production') {
    //   return NextResponse.json(
    //     { success: false, message: 'Tenant isolation fix not available in production' },
    //     { status: 403 }
    //   );
    // }

    // const body = await request.json();
    // const { action, defaultTenantId } = body;

    // switch (action) {
    //   case 'detect':
    //     console.log('🔍 Detecting tenant isolation violations...');
    //     const detectionResult = await detectTenantIsolationViolations();

    //     return NextResponse.json({
    //       success: true,
    //       message: `Found ${detectionResult.violations.length} tenant isolation violations`,
    //       data: {
    //         totalViolations: detectionResult.violations.length,
    //         totalDocuments: detectionResult.totalDocuments,
    //         violatingCollections: detectionResult.violatingCollections,
    //         violations: detectionResult.violations.slice(0, 10), // Return first 10 for preview
    //         hasMore: detectionResult.violations.length > 10
    //       }
    //     });

    //   case 'fix':
    //     console.log('🔧 Fixing tenant isolation violations...');
    //     const fixResult = await fixAllTenantIsolationViolations(
    //       defaultTenantId || 'evexa-super-admin-tenant'
    //     );

    //     return NextResponse.json({
    //       success: fixResult.success,
    //       message: fixResult.success
    //         ? `Successfully fixed ${fixResult.fixedViolations} tenant isolation violations`
    //         : `Failed to fix some violations: ${fixResult.errors.join(', ')}`,
    //       data: fixResult
    //     });

    //   case 'verify':
    //     console.log('🔍 Verifying tenant isolation...');
    //     const verificationResult = await verifyTenantIsolation();

    //     return NextResponse.json({
    //       success: verificationResult.isValid,
    //       message: verificationResult.isValid
    //         ? 'All collections have proper tenant isolation'
    //         : `${verificationResult.violationsRemaining} tenant isolation violations still exist`,
    //       data: verificationResult
    //     });

    //   default:
    //     return NextResponse.json(
    //       { success: false, message: 'Invalid action. Use: detect, fix, or verify' },
    //       { status: 400 }
    //     );
    // }

  } catch (error) {
    console.error('Error in tenant isolation fix:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Tenant isolation fix failed',
        error: String(error)
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { success: false, message: 'Tenant isolation fix not available in production' },
        { status: 403 }
      );
    }

    // Default to detection
    console.log('🔍 Quick tenant isolation check...');
    const detectionResult = await detectTenantIsolationViolations();
    
    return NextResponse.json({
      success: detectionResult.violations.length === 0,
      message: detectionResult.violations.length === 0
        ? 'No tenant isolation violations found'
        : `CRITICAL: ${detectionResult.violations.length} tenant isolation violations detected`,
      data: {
        totalViolations: detectionResult.violations.length,
        totalDocuments: detectionResult.totalDocuments,
        violatingCollections: detectionResult.violatingCollections,
        criticalIssue: detectionResult.violations.length > 0,
        recommendedAction: detectionResult.violations.length > 0 
          ? 'Run POST /api/fix-tenant-isolation with action: "fix"'
          : 'No action needed'
      }
    });

  } catch (error) {
    console.error('Error checking tenant isolation:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Tenant isolation check failed', 
        error: String(error) 
      },
      { status: 500 }
    );
  }
}
