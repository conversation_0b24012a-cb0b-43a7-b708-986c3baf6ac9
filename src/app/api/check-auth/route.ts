import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { success: false, message: 'Auth check not available in production' },
        { status: 403 }
      );
    }

    console.log('API: Auth check requested...');

    // Get auth header
    const authHeader = request.headers.get('authorization');
    
    // Try to get user from Firebase Auth
    let authStatus = 'No auth header';
    let userInfo = null;
    let isSuperAdmin = false;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      try {
        const { adminAuth } = await import('@/lib/firebase-admin');
        const decodedToken = await adminAuth.verifyIdToken(token);
        
        authStatus = 'Token verified';
        userInfo = {
          uid: decodedToken.uid,
          email: decodedToken.email,
          emailVerified: decodedToken.email_verified
        };
        
        // Check if super admin
        const { SUPER_ADMIN_USER_IDS } = await import('@/services/superAdminService');
        isSuperAdmin = SUPER_ADMIN_USER_IDS.includes(decodedToken.uid);
        
      } catch (error) {
        authStatus = `Token verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      }
    }

    return NextResponse.json({
      success: true,
      authStatus,
      userInfo,
      isSuperAdmin,
      superAdminIds: process.env.NODE_ENV === 'development' ? ['QRuQpzQJkvgKaC1lACwG3yQBV6g2'] : 'Hidden in production',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('API: Auth check error:', errorMessage);

    return NextResponse.json(
      { success: false, message: `Auth check failed: ${errorMessage}` },
      { status: 500 }
    );
  }
}
