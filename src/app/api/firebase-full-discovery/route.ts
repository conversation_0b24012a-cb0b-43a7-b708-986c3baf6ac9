import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, getDocs, query, limit } from 'firebase/firestore';

/**
 * Firebase Full Discovery API
 * 
 * Discovers ALL collections in Firebase, not just the expected ones
 * This will help us find any hidden or unexpected collections
 */
export async function GET(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Full discovery not available in production' },
        { status: 403 }
      );
    }

    console.log('🔍 Starting full Firebase discovery...');
    
    // Extended list of possible collection names that might exist
    const POSSIBLE_COLLECTIONS = [
      // Standard EVEXA collections
      'users', 'exhibitions', 'events', 'tasks', 'leads', 'vendors',
      'budgets', 'expenses', 'purchase_requests', 'purchase_orders', 'invoices',
      'email_campaigns', 'social_posts', 'notifications', 'marketing_materials',
      'travel_entries', 'shipments', 'booth_meetings',
      'release_notes', 'support_tickets', 'audit_logs',
      'gift_items', 'media_contacts', 'press_kits', 'approval_documents', 'signing_requests',
      
      // Possible chaotic collections that might still exist
      'email_recipients', 'email_lists', 'email_templates', 'media_events', 
      'security_events', 'workflows', 'design_projects',
      'attendee_invitations', 'vip_visits', 'per_diem_requests', 'training_materials',
      'competitor_profiles', 'competitor_exhibition_presences', 'debriefs',
      'success_scorecards', 'insights', 'groups', 'delegations', 'funding_source_definitions',
      'financial_category_definitions', 'launch_tasks', 'pitches', 'media_coverages', 
      'briefing_packs', 'smm_settings', 'media_albums', 'media_items', 
      'flight_bookings', 'hotel_bookings', 'passport_infos', 'visa_infos',
      'post_show_hubs', 'gift_allocations', 'marketing_campaigns', 'secure_assets',
      'restricted_zones', 'asset_custody_logs', 'report_configurations', 'report_instances',
      'contact_segments', 'attendee_communications', 'booth_analytics_data',
      'monitoring_streams', 'exhibition_collaborations', 'exhibition_chat_messages',
      'exhibition_whiteboards', 'exhibition_documents', 'exhibition_video_conferences',
      'user_presence', 'exhibition_collaboration_sessions', 'booth_attendance',
      'business_trip_status', 'attendance_alerts', 'resource_allocations',
      'vendor_requirements', 'schedule_items', 'maintenance_tasks', 'smart_schedule_items',
      'predictive_maintenance_tasks', 'portfolio_optimizations', 'strategic_plans',
      'event_automations', 'event_schedule_optimizations', 'event_resource_allocations',
      'event_sentiment_data', 'event_polls', 'emergency_alerts', 'event_series',
      'event_portfolio_analytics', 'strategic_insights', 'vendor_matching_history',
      'rfqs_generated', 'contract_analysis_history', 'content_intelligence_items',
      'supply_chain_bottlenecks', 'supply_chain_optimization_opportunities',
      'billing_alerts', 'analytics_configurations', 'travel_spend_analytics',
      'policy_compliance_monitoring', 'traveler_safety_tracking', 'company_profiles',
      'api_partnerships', 'global_markets', 'global_compliance_frameworks',
      'evexa_business_metrics', 'system_settings', 'integration_configs',
      
      // UI/System collections
      'dashboardLayouts', 'userPreferences', 'systemConfigs',
      
      // Possible camelCase variations
      'travelEntries', 'releaseNotes', 'supportTickets', 'giftItems', 'mediaContacts',
      'pressKits', 'approvalDocuments', 'signingRequests', 'emailCampaigns',
      'socialPosts', 'marketingMaterials', 'purchaseRequests', 'purchaseOrders',
      'boothMeetings', 'auditLogs',
      
      // Other possible variations
      'Users', 'Exhibitions', 'Events', 'Tasks', 'Leads', 'Vendors',
      'user', 'exhibition', 'event', 'task', 'lead', 'vendor'
    ];
    
    const foundCollections: { [collectionName: string]: number } = {};
    let totalCollections = 0;
    let totalDocuments = 0;
    
    console.log(`🔍 Checking ${POSSIBLE_COLLECTIONS.length} possible collection names...`);
    
    for (const collectionName of POSSIBLE_COLLECTIONS) {
      try {
        const querySnapshot = await getDocs(query(collection(db, collectionName), limit(1)));
        
        if (!querySnapshot.empty) {
          // Collection exists and has documents, get full count
          const fullSnapshot = await getDocs(collection(db, collectionName));
          const docCount = fullSnapshot.docs.length;
          
          foundCollections[collectionName] = docCount;
          totalCollections++;
          totalDocuments += docCount;
          
          console.log(`   📁 Found: ${collectionName} (${docCount} documents)`);
        }
      } catch (error) {
        // Collection doesn't exist or error accessing it
        // This is expected for most collections
      }
    }
    
    console.log(`🔍 Discovery complete: Found ${totalCollections} collections with ${totalDocuments} total documents`);
    
    // Categorize collections
    const standardCollections: { [key: string]: number } = {};
    const chaoticCollections: { [key: string]: number } = {};
    const systemCollections: { [key: string]: number } = {};
    
    const STANDARD_NAMES = [
      'users', 'exhibitions', 'events', 'tasks', 'leads', 'vendors',
      'budgets', 'expenses', 'purchase_requests', 'purchase_orders', 'invoices',
      'email_campaigns', 'social_posts', 'notifications', 'marketing_materials',
      'travel_entries', 'shipments', 'booth_meetings',
      'release_notes', 'support_tickets', 'audit_logs',
      'gift_items', 'media_contacts', 'press_kits', 'approval_documents', 'signing_requests'
    ];
    
    const SYSTEM_NAMES = ['dashboardLayouts', 'userPreferences', 'systemConfigs'];
    
    for (const [collectionName, docCount] of Object.entries(foundCollections)) {
      if (STANDARD_NAMES.includes(collectionName)) {
        standardCollections[collectionName] = docCount;
      } else if (SYSTEM_NAMES.includes(collectionName)) {
        systemCollections[collectionName] = docCount;
      } else {
        chaoticCollections[collectionName] = docCount;
      }
    }
    
    return NextResponse.json({
      success: true,
      message: `Full Firebase discovery complete`,
      data: {
        totalCollections,
        totalDocuments,
        allCollections: foundCollections,
        categorized: {
          standard: {
            collections: standardCollections,
            count: Object.keys(standardCollections).length,
            documents: Object.values(standardCollections).reduce((sum, count) => sum + count, 0)
          },
          chaotic: {
            collections: chaoticCollections,
            count: Object.keys(chaoticCollections).length,
            documents: Object.values(chaoticCollections).reduce((sum, count) => sum + count, 0)
          },
          system: {
            collections: systemCollections,
            count: Object.keys(systemCollections).length,
            documents: Object.values(systemCollections).reduce((sum, count) => sum + count, 0)
          }
        },
        analysis: {
          hasChaoticCollections: Object.keys(chaoticCollections).length > 0,
          needsCleanup: Object.keys(chaoticCollections).length > 0,
          recommendations: Object.keys(chaoticCollections).length > 0 
            ? ['Run nuclear reset to clean up chaotic collections', 'Use only Professional Data Manager for seeding']
            : ['Database is clean and organized']
        }
      }
    });
    
  } catch (error) {
    console.error('Full discovery error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Full discovery failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
