import { NextRequest, NextResponse } from 'next/server';
import { advancedUsageAnalyticsService } from '@/services/advancedUsageAnalyticsService';
import { auth } from '@/lib/firebase-admin';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    switch (action) {
      case 'analytics':
        const periodType = searchParams.get('period') as 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' || 'monthly';
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');

        let start: Date, end: Date;
        
        if (startDate && endDate) {
          start = new Date(startDate);
          end = new Date(endDate);
        } else {
          // Default to current month
          const now = new Date();
          start = new Date(now.getFullYear(), now.getMonth(), 1);
          end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        }

        const analytics = await advancedUsageAnalyticsService.getUsageAnalytics(tenantId, {
          start,
          end,
          type: periodType
        });

        return NextResponse.json(analytics);

      case 'current-usage':
        const currentUsage = await advancedUsageAnalyticsService.getCurrentUsage(tenantId);
        return NextResponse.json(currentUsage);

      case 'overage-billing':
        const billingPeriod = searchParams.get('billingPeriod') || undefined;
        const overages = await advancedUsageAnalyticsService.getOverageBilling(tenantId, billingPeriod);
        return NextResponse.json(overages);

      case 'usage-trends':
        // Get usage trends for the last 6 months
        const trends = [];
        const now = new Date();
        
        for (let i = 5; i >= 0; i--) {
          const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
          const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);
          
          const monthlyAnalytics = await advancedUsageAnalyticsService.getUsageAnalytics(tenantId, {
            start: monthStart,
            end: monthEnd,
            type: 'monthly'
          });
          
          trends.push({
            period: `${monthStart.getFullYear()}-${String(monthStart.getMonth() + 1).padStart(2, '0')}`,
            usage: monthlyAnalytics.totalUsage
          });
        }
        
        return NextResponse.json(trends);

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in usage analytics API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const data = await request.json();
    const { action } = data;

    switch (action) {
      case 'track-usage':
        const { category, actionName, quantity, options } = data;
        
        await advancedUsageAnalyticsService.trackUsage(
          tenantId,
          category,
          actionName,
          quantity || 1,
          {
            userId: decodedToken.uid,
            ...options
          }
        );
        
        return NextResponse.json({ success: true });

      case 'process-overage-billing':
        const { billingPeriod } = data;
        const currentPeriod = billingPeriod || `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, '0')}`;
        
        const overages = await advancedUsageAnalyticsService.processOverageBilling(tenantId, currentPeriod);
        
        return NextResponse.json({
          success: true,
          overages: overages.length,
          totalAmount: overages.reduce((sum, o) => sum + o.overageAmount, 0)
        });

      case 'bulk-track-usage':
        const { usageEvents } = data;
        
        if (!Array.isArray(usageEvents)) {
          return NextResponse.json({ error: 'usageEvents must be an array' }, { status: 400 });
        }

        const results = await Promise.allSettled(
          usageEvents.map((event: any) =>
            advancedUsageAnalyticsService.trackUsage(
              tenantId,
              event.category,
              event.action,
              event.quantity || 1,
              {
                userId: decodedToken.uid,
                ...event.options
              }
            )
          )
        );

        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;

        return NextResponse.json({
          success: true,
          processed: usageEvents.length,
          successful,
          failed
        });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in usage analytics API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Webhook endpoint for automated usage tracking
export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'automated-billing') {
      // This would be called by a cron job or scheduled function
      const { tenantIds, billingPeriod } = await request.json();
      
      const results = [];
      
      for (const tenantId of tenantIds) {
        try {
          const overages = await advancedUsageAnalyticsService.processOverageBilling(
            tenantId,
            billingPeriod
          );
          
          results.push({
            tenantId,
            success: true,
            overages: overages.length,
            totalAmount: overages.reduce((sum, o) => sum + o.overageAmount, 0)
          });
        } catch (error) {
          results.push({
            tenantId,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
      
      return NextResponse.json({
        success: true,
        processed: tenantIds.length,
        results
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Error in automated billing:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
