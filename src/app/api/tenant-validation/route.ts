import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, getDocs } from 'firebase/firestore';
import { COLLECTIONS } from '@/lib/collections';

/**
 * Tenant Validation API - Check tenant ID consistency
 * 
 * Validates that all documents have consistent tenant IDs
 * and proper tenant-aware data structure
 */
export async function GET(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Tenant validation not available in production' },
        { status: 403 }
      );
    }

    console.log('🔍 Starting tenant validation...');
    
    const validationResults: {
      [collection: string]: {
        totalDocuments: number;
        documentsWithTenantId: number;
        documentsWithCorrectTenantId: number;
        documentsWithIncorrectTenantId: number;
        documentsWithoutTenantId: number;
        tenantIdVariations: string[];
      }
    } = {};
    
    const expectedTenantId = 'evexa-development-company';
    let totalDocuments = 0;
    let totalValid = 0;
    let totalInvalid = 0;
    let totalMissing = 0;

    // Check core collections
    const collectionsToCheck = Object.values(COLLECTIONS);
    for (const collectionName of collectionsToCheck) {
      try {
        const querySnapshot = await getDocs(collection(db, collectionName));
        
        if (querySnapshot.empty) {
          console.log(`   ⏭️ Skipping empty collection: ${collectionName}`);
          continue;
        }
        
        const collectionResults = {
          totalDocuments: querySnapshot.docs.length,
          documentsWithTenantId: 0,
          documentsWithCorrectTenantId: 0,
          documentsWithIncorrectTenantId: 0,
          documentsWithoutTenantId: 0,
          tenantIdVariations: [] as string[]
        };
        
        const tenantIdSet = new Set<string>();
        
        querySnapshot.docs.forEach(doc => {
          const data = doc.data();
          totalDocuments++;
          
          if (data.tenantId) {
            collectionResults.documentsWithTenantId++;
            tenantIdSet.add(data.tenantId);
            
            if (data.tenantId === expectedTenantId) {
              collectionResults.documentsWithCorrectTenantId++;
              totalValid++;
            } else {
              collectionResults.documentsWithIncorrectTenantId++;
              totalInvalid++;
            }
          } else {
            collectionResults.documentsWithoutTenantId++;
            totalMissing++;
          }
        });
        
        collectionResults.tenantIdVariations = Array.from(tenantIdSet);
        validationResults[collectionName] = collectionResults;
        
        console.log(`   📊 ${collectionName}: ${collectionResults.documentsWithCorrectTenantId}/${collectionResults.totalDocuments} valid`);
        
      } catch (error) {
        console.error(`   ❌ Error validating ${collectionName}:`, error);
      }
    }
    
    const summary = {
      totalDocuments,
      totalValid,
      totalInvalid,
      totalMissing,
      expectedTenantId,
      validationPassed: totalInvalid === 0 && totalMissing === 0,
      collectionsChecked: Object.keys(validationResults).length
    };
    
    console.log(`🔍 Validation complete: ${totalValid}/${totalDocuments} documents valid`);
    
    return NextResponse.json({
      success: true,
      message: `Tenant validation complete`,
      summary,
      details: validationResults
    });
    
  } catch (error) {
    console.error('Tenant validation error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Tenant validation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
