import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { success: false, message: 'Super admin fix not allowed in production' },
        { status: 403 }
      );
    }

    console.log('API: Fixing super admin user...');

    // Use regular Firebase client SDK
    const { db } = await import('@/lib/firebase');
    const { doc, setDoc, getDoc } = await import('firebase/firestore');
    const { SUPER_ADMIN_USER_IDS, EVEXA_DEV_TENANT_ID } = await import('@/services/superAdminService');
    
    const superAdminId = SUPER_ADMIN_USER_IDS[0]; // 'QRuQpzQJkvgKaC1lACwG3yQBV6g2'
    
    // Create/update the super admin user document
    const superAdminUser = {
      id: superAdminId,
      email: '<EMAIL>',
      firstName: 'Super',
      lastName: 'Admin',
      displayName: 'Super Admin',
      role: 'super_admin',
      tenantId: EVEXA_DEV_TENANT_ID,
      status: 'active',
      emailVerified: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      lastLoginAt: new Date(),
      permissions: {
        canManageUsers: true,
        canManageTenants: true,
        canAccessAllData: true,
        canSeedData: true,
        canResetData: true
      }
    };

    try {
      // Set the user document with the super admin role
      await setDoc(doc(db, 'users', superAdminId), superAdminUser);
      
      console.log('✅ Super admin user document created/updated');
      
      // Verify the document was created
      const userDoc = await getDoc(doc(db, 'users', superAdminId));
      
      if (userDoc.exists()) {
        const userData = userDoc.data();
        return NextResponse.json({
          success: true,
          message: 'Super admin user fixed successfully',
          user: {
            id: userData.id,
            email: userData.email,
            role: userData.role,
            tenantId: userData.tenantId
          }
        });
      } else {
        return NextResponse.json({
          success: false,
          message: 'Failed to verify super admin user creation'
        }, { status: 500 });
      }
      
    } catch (docError) {
      console.error('Error creating super admin user:', docError);
      return NextResponse.json({
        success: false,
        message: `Failed to create super admin user: ${docError instanceof Error ? docError.message : 'Unknown error'}`
      }, { status: 500 });
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('API: Super admin fix error:', errorMessage);

    return NextResponse.json(
      { 
        success: false, 
        message: `Super admin fix failed: ${errorMessage}`
      },
      { status: 500 }
    );
  }
}
