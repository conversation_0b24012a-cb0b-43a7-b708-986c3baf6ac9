import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, getDocs, deleteDoc, doc } from 'firebase/firestore';

/**
 * Comprehensive Nuclear Reset API
 * 
 * Deletes ALL 63 chaotic collections found in Firebase
 * This is the REAL nuclear reset that cleans up the actual chaos
 */
export async function POST(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Comprehensive nuclear reset not allowed in production' },
        { status: 403 }
      );
    }

    console.log('🚨 COMPREHENSIVE NUCLEAR RESET: Starting complete Firebase cleanup...');
    
    // The ACTUAL list of all 63+ collections found in Firebase console
    const ALL_CHAOTIC_COLLECTIONS = [
      'a_p_i_partnerships',
      'acknowledgments',
      'aiConfiguration',
      'aiUsageRecords',
      'analytics_configs',
      'approval_documents',
      'attendee_profiles',
      'boothAnalytics',
      'booth_analytics_enhanced',
      'booth_attendance_records',
      'booth_meetings',
      'briefingPacks',
      'budgets',
      'budgets_enhanced',
      'competitor_exhibition_presences_enhanced',
      'config',
      'contactSegments',
      'coreTeamMembers',
      'dashboardLayouts',
      'document_signatures',
      'emailSequences',
      'emailTemplates',
      'email_sequences',
      'event_attendance_records',
      'event_workflows',
      'events',
      'exhibitionTeamAssignments',
      'exhibition_chat_channels',
      'exhibition_templates',
      'exhibitions',
      'expenseCategoryDefinitions',
      'expenses',
      'expenses_enhanced',
      'flightItineraries',
      'gift_items',
      'insuranceClaims',
      'internationalCompliance',
      'invoices',
      'leads',
      'legacy_compliance_frameworks',
      'logisticsShipments',
      'mail',
      'marketing_materials',
      'media_contacts',
      'notifications',
      'personalizationProfiles',
      'portfolio_analytics',
      'postShowHubs',
      'press_kits',
      'procurement_metrics',
      'purchaseOrders',
      'purchaseRequests',
      'purchase_orders',
      'purchase_requests',
      'recommendations',
      'release_notes',
      'reportInstanceConfigs',
      'response_templates',
      'routeOptimizations',
      'security-events',
      'shipments',
      'signing_requests',
      'social_posts',
      'subscription_plans',
      'support_tickets',
      'tasks',
      'temporary_staff',
      'tenant-data',
      'tenant-users',
      'tenants',
      'trainingAcknowledgments',
      'trainingMaterials',
      'travelRequests',
      'travel_entries',
      'userGroups',
      'userPreferences',
      'users',
      'vendorPerformanceReviews',
      'vendor_matching_requests',
      'vendors',
      'venue_integrations',
      'visaDetails',
      'visaPassportManagement',
      'workflow_execution_logs',
      'workflow_executions'
    ];
    
    const deletedCollections: string[] = [];
    const errors: string[] = [];
    let totalDeletedDocuments = 0;
    let processedCollections = 0;
    
    console.log(`🗑️ Preparing to delete ${ALL_CHAOTIC_COLLECTIONS.length} collections...`);
    
    for (const collectionName of ALL_CHAOTIC_COLLECTIONS) {
      try {
        processedCollections++;
        console.log(`🗑️ [${processedCollections}/${ALL_CHAOTIC_COLLECTIONS.length}] Processing: ${collectionName}`);
        
        const querySnapshot = await getDocs(collection(db, collectionName));
        
        if (querySnapshot.empty) {
          console.log(`   ⏭️ Collection ${collectionName} is already empty`);
          continue;
        }
        
        const docCount = querySnapshot.docs.length;
        console.log(`   🗑️ Deleting ${docCount} documents from ${collectionName}...`);
        
        // Delete all documents in the collection
        const deletePromises = querySnapshot.docs.map(docSnapshot => 
          deleteDoc(doc(db, collectionName, docSnapshot.id))
        );
        
        await Promise.all(deletePromises);
        
        deletedCollections.push(collectionName);
        totalDeletedDocuments += docCount;
        
        console.log(`   ✅ Successfully deleted ${collectionName} (${docCount} documents)`);
        
        // Add small delay to prevent overwhelming Firebase
        if (processedCollections % 10 === 0) {
          console.log(`   ⏸️ Brief pause after ${processedCollections} collections...`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
      } catch (error) {
        const errorMsg = `Failed to delete ${collectionName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        console.error(`   ❌ ${errorMsg}`);
        errors.push(errorMsg);
      }
    }
    
    const success = errors.length === 0;
    
    console.log(`🚨 COMPREHENSIVE NUCLEAR RESET COMPLETE!`);
    console.log(`📊 Results: ${deletedCollections.length} collections deleted, ${totalDeletedDocuments} documents deleted`);
    
    if (errors.length > 0) {
      console.log(`⚠️ Errors encountered: ${errors.length}`);
      errors.forEach(error => console.log(`   ❌ ${error}`));
    }
    
    if (success) {
      return NextResponse.json({
        success: true,
        message: `Comprehensive nuclear reset completed successfully - Firebase should now be completely clean`,
        data: {
          deletedCollections,
          totalDeletedCollections: deletedCollections.length,
          totalDeletedDocuments,
          processedCollections: ALL_CHAOTIC_COLLECTIONS.length,
          cleanupComplete: true
        }
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'Comprehensive nuclear reset completed with some errors',
        data: {
          deletedCollections,
          totalDeletedCollections: deletedCollections.length,
          totalDeletedDocuments,
          processedCollections: ALL_CHAOTIC_COLLECTIONS.length,
          errors,
          partialCleanup: true
        }
      }, { status: 207 }); // 207 Multi-Status for partial success
    }
    
  } catch (error) {
    console.error('Comprehensive nuclear reset error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Comprehensive nuclear reset failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
