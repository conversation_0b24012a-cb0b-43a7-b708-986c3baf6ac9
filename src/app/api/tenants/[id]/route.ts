import { NextRequest, NextResponse } from 'next/server';
import { 
  doc, 
  getDoc, 
  updateDoc, 
  deleteDoc,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

/**
 * Individual Tenant Management API
 * Handles operations on specific tenants
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const tenantId = params.id;
    console.log(`🏢 API: Getting tenant ${tenantId}...`);
    
    const tenantRef = doc(db, 'tenants', tenantId);
    const tenantDoc = await getDoc(tenantRef);
    
    if (!tenantDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'Tenant not found'
      }, { status: 404 });
    }

    const tenant = {
      id: tenantDoc.id,
      ...tenantDoc.data()
    };

    return NextResponse.json({
      success: true,
      tenant
    });

  } catch (error) {
    console.error('❌ Error getting tenant:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get tenant',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const tenantId = params.id;
    const updates = await request.json();
    
    console.log(`🏢 API: Updating tenant ${tenantId}...`);
    
    // Check if tenant exists
    const tenantRef = doc(db, 'tenants', tenantId);
    const tenantDoc = await getDoc(tenantRef);
    
    if (!tenantDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'Tenant not found'
      }, { status: 404 });
    }

    // Update tenant
    await updateDoc(tenantRef, {
      ...updates,
      updatedAt: Timestamp.now()
    });

    console.log(`✅ Tenant updated: ${tenantId}`);

    return NextResponse.json({
      success: true,
      message: 'Tenant updated successfully'
    });

  } catch (error) {
    console.error('❌ Error updating tenant:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update tenant',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const tenantId = params.id;
    console.log(`🏢 API: Deleting tenant ${tenantId}...`);
    
    // Check if tenant exists
    const tenantRef = doc(db, 'tenants', tenantId);
    const tenantDoc = await getDoc(tenantRef);
    
    if (!tenantDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'Tenant not found'
      }, { status: 404 });
    }

    const tenantData = tenantDoc.data();
    
    // Prevent deletion of EVEXA development tenants
    if (tenantData?.slug === 'evexa-dev' || tenantData?.slug === 'evexa-demo') {
      return NextResponse.json({
        success: false,
        error: 'Cannot delete EVEXA development tenants'
      }, { status: 403 });
    }

    // Soft delete by updating status instead of hard delete
    await updateDoc(tenantRef, {
      status: 'deleted',
      deletedAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    console.log(`✅ Tenant soft-deleted: ${tenantId}`);

    return NextResponse.json({
      success: true,
      message: 'Tenant deleted successfully'
    });

  } catch (error) {
    console.error('❌ Error deleting tenant:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to delete tenant',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
