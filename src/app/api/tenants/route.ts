import { NextRequest, NextResponse } from 'next/server';
import { 
  collection, 
  getDocs, 
  addDoc, 
  doc, 
  setDoc,
  Timestamp,
  query,
  orderBy
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

/**
 * Tenant Management API
 * Handles CRUD operations for tenants
 */

export async function GET(request: NextRequest) {
  try {
    console.log('🏢 API: Getting all tenants...');
    
    // Get all tenants from Firebase
    const tenantsQuery = query(
      collection(db, 'tenants'),
      orderBy('createdAt', 'desc')
    );
    
    const snapshot = await getDocs(tenantsQuery);
    const tenants = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    console.log(`📊 Found ${tenants.length} tenants`);

    return NextResponse.json({
      success: true,
      tenants,
      count: tenants.length
    });

  } catch (error) {
    console.error('❌ Error getting tenants:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get tenants',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const tenantData = await request.json();
    
    console.log('🏢 API: Creating new tenant...', tenantData.name);
    
    // Validate required fields
    if (!tenantData.name || !tenantData.slug) {
      return NextResponse.json({
        success: false,
        error: 'Name and slug are required'
      }, { status: 400 });
    }

    // Create tenant document
    const newTenant = {
      ...tenantData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      lastActiveAt: Timestamp.now(),
      // Default settings if not provided
      settings: {
        timezone: 'UTC',
        dateFormat: 'MM/dd/yyyy',
        timeFormat: '12h',
        currency: 'USD',
        language: 'en',
        allowUserRegistration: false,
        requireEmailVerification: true,
        sessionTimeout: 480,
        dataRetentionDays: 365,
        backupEnabled: true,
        auditLogEnabled: true,
        ...tenantData.settings
      },
      // Default branding
      branding: {
        primaryColor: '#2563eb',
        secondaryColor: '#64748b',
        accentColor: '#0ea5e9',
        theme: 'default',
        logoUrl: '',
        faviconUrl: '',
        customCss: '',
        ...tenantData.branding
      },
      // Default features based on plan
      features: {
        exhibitions: true,
        events: true,
        tasks: true,
        leads: true,
        budgets: true,
        analytics: tenantData.plan !== 'starter',
        integrations: tenantData.plan === 'enterprise',
        customBranding: tenantData.plan === 'enterprise',
        apiAccess: tenantData.plan === 'enterprise',
        advancedReporting: tenantData.plan !== 'starter',
        multiTenant: false,
        sso: tenantData.plan === 'enterprise',
        auditLogs: tenantData.plan !== 'starter',
        dataExport: true,
        customFields: tenantData.plan !== 'starter',
        ...tenantData.features
      },
      // Default billing
      billing: {
        currentPeriodStart: Timestamp.now(),
        currentPeriodEnd: Timestamp.fromDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)), // 30 days
        subscriptionId: `sub_${tenantData.slug}_${Date.now()}`,
        customerId: `cus_${tenantData.slug}_${Date.now()}`,
        status: 'active',
        ...tenantData.billing
      },
      // Metadata
      metadata: {
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        createdBy: 'super_admin',
        industry: tenantData.industry || 'General',
        companySize: tenantData.companySize || '1-50',
        country: 'US',
        region: tenantData.region || 'us-central1',
        deploymentType: 'saas',
        ...tenantData.metadata
      }
    };

    // Use the provided ID or generate one
    const tenantId = tenantData.id || `tenant_${tenantData.slug}_${Date.now()}`;
    const tenantRef = doc(db, 'tenants', tenantId);
    await setDoc(tenantRef, newTenant);

    console.log(`✅ Tenant created: ${tenantData.name} (${tenantId})`);

    return NextResponse.json({
      success: true,
      message: `Tenant "${tenantData.name}" created successfully`,
      tenant: {
        id: tenantId,
        ...newTenant
      }
    });

  } catch (error) {
    console.error('❌ Error creating tenant:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to create tenant',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
