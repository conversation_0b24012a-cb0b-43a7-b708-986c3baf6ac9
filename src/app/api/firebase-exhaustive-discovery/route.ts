import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, getDocs, query, limit } from 'firebase/firestore';

/**
 * Firebase Exhaustive Discovery API
 * 
 * Checks an exhaustive list of possible collection names
 * This should catch collections that our previous searches missed
 */
export async function GET(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Exhaustive discovery not available in production' },
        { status: 403 }
      );
    }

    console.log('🔍 Starting exhaustive Firebase discovery...');
    
    // MASSIVE list of possible collection names from mockData.ts and other sources
    const EXHAUSTIVE_COLLECTION_LIST = [
      // Standard EVEXA collections
      'users', 'exhibitions', 'events', 'tasks', 'leads', 'vendors',
      'budgets', 'expenses', 'purchase_requests', 'purchase_orders', 'invoices',
      'email_campaigns', 'social_posts', 'notifications', 'marketing_materials',
      'travel_entries', 'shipments', 'booth_meetings',
      'release_notes', 'support_tickets', 'audit_logs',
      'gift_items', 'media_contacts', 'press_kits', 'approval_documents', 'signing_requests',
      
      // CamelCase variations
      'purchaseRequests', 'purchaseOrders', 'emailCampaigns', 'socialPosts',
      'marketingMaterials', 'travelEntries', 'boothMeetings', 'releaseNotes',
      'supportTickets', 'auditLogs', 'giftItems', 'mediaContacts', 'pressKits',
      'approvalDocuments', 'signingRequests',
      
      // All the chaotic collections from mockData.ts
      'email_recipients', 'email_lists', 'email_templates', 'media_events',
      'security_events', 'workflows', 'design_projects', 'attendee_invitations',
      'vip_visits', 'per_diem_requests', 'training_materials', 'competitor_profiles',
      'competitor_exhibition_presences', 'debriefs', 'success_scorecards', 'insights',
      'groups', 'delegations', 'funding_source_definitions', 'financial_category_definitions',
      'launch_tasks', 'pitches', 'media_coverages', 'briefing_packs', 'smm_settings',
      'media_albums', 'media_items', 'flight_bookings', 'hotel_bookings',
      'passport_infos', 'visa_infos', 'post_show_hubs', 'gift_allocations',
      'marketing_campaigns', 'secure_assets', 'restricted_zones', 'asset_custody_logs',
      'report_configurations', 'report_instances', 'contact_segments',
      'attendee_communications', 'booth_analytics_data', 'monitoring_streams',
      'exhibition_collaborations', 'exhibition_chat_messages', 'exhibition_whiteboards',
      'exhibition_documents', 'exhibition_video_conferences', 'user_presence',
      'exhibition_collaboration_sessions', 'booth_attendance', 'business_trip_status',
      'attendance_alerts', 'resource_allocations', 'vendor_requirements',
      'schedule_items', 'maintenance_tasks', 'smart_schedule_items',
      'predictive_maintenance_tasks', 'portfolio_optimizations', 'strategic_plans',
      'event_automations', 'event_schedule_optimizations', 'event_resource_allocations',
      'event_sentiment_data', 'event_polls', 'emergency_alerts', 'event_series',
      'event_portfolio_analytics', 'strategic_insights', 'vendor_matching_history',
      'rfqs_generated', 'contract_analysis_history', 'content_intelligence_items',
      'supply_chain_bottlenecks', 'supply_chain_optimization_opportunities',
      'billing_alerts', 'analytics_configurations', 'travel_spend_analytics',
      'policy_compliance_monitoring', 'traveler_safety_tracking', 'company_profiles',
      'api_partnerships', 'global_markets', 'global_compliance_frameworks',
      'evexa_business_metrics', 'system_settings', 'integration_configs',
      
      // CamelCase versions of the above
      'emailRecipients', 'emailLists', 'emailTemplates', 'mediaEvents',
      'securityEvents', 'designProjects', 'attendeeInvitations', 'vipVisits',
      'perDiemRequests', 'trainingMaterials', 'competitorProfiles',
      'competitorExhibitionPresences', 'successScorecards', 'fundingSourceDefinitions',
      'financialCategoryDefinitions', 'launchTasks', 'mediaCoverages',
      'briefingPacks', 'smmSettings', 'mediaAlbums', 'mediaItems',
      'flightBookings', 'hotelBookings', 'passportInfos', 'visaInfos',
      'postShowHubs', 'giftAllocations', 'marketingCampaigns', 'secureAssets',
      'restrictedZones', 'assetCustodyLogs', 'reportConfigurations', 'reportInstances',
      'contactSegments', 'attendeeCommunications', 'boothAnalyticsData',
      'monitoringStreams', 'exhibitionCollaborations', 'exhibitionChatMessages',
      'exhibitionWhiteboards', 'exhibitionDocuments', 'exhibitionVideoConferences',
      'userPresence', 'exhibitionCollaborationSessions', 'boothAttendance',
      'businessTripStatus', 'attendanceAlerts', 'resourceAllocations',
      'vendorRequirements', 'scheduleItems', 'maintenanceTasks', 'smartScheduleItems',
      'predictiveMaintenanceTasks', 'portfolioOptimizations', 'strategicPlans',
      'eventAutomations', 'eventScheduleOptimizations', 'eventResourceAllocations',
      'eventSentimentData', 'eventPolls', 'emergencyAlerts', 'eventSeries',
      'eventPortfolioAnalytics', 'strategicInsights', 'vendorMatchingHistory',
      'rfqsGenerated', 'contractAnalysisHistory', 'contentIntelligenceItems',
      'supplyChainBottlenecks', 'supplyChainOptimizationOpportunities',
      'billingAlerts', 'analyticsConfigurations', 'travelSpendAnalytics',
      'policyComplianceMonitoring', 'travelerSafetyTracking', 'companyProfiles',
      'apiPartnerships', 'globalMarkets', 'globalComplianceFrameworks',
      'evexaBusinessMetrics', 'systemSettings', 'integrationConfigs',
      
      // UI/System collections
      'dashboardLayouts', 'userPreferences', 'systemConfigs', 'appSettings',
      
      // Possible variations and typos
      'Users', 'Exhibitions', 'Events', 'Tasks', 'Leads', 'Vendors',
      'user', 'exhibition', 'event', 'task', 'lead', 'vendor',
      'USERS', 'EXHIBITIONS', 'EVENTS', 'TASKS', 'LEADS', 'VENDORS'
    ];
    
    const foundCollections: { [collectionName: string]: number } = {};
    let totalCollections = 0;
    let totalDocuments = 0;
    let checkedCount = 0;
    
    console.log(`🔍 Checking ${EXHAUSTIVE_COLLECTION_LIST.length} possible collection names...`);
    
    for (const collectionName of EXHAUSTIVE_COLLECTION_LIST) {
      try {
        checkedCount++;
        if (checkedCount % 50 === 0) {
          console.log(`   🔍 Checked ${checkedCount}/${EXHAUSTIVE_COLLECTION_LIST.length} collections...`);
        }
        
        const querySnapshot = await getDocs(query(collection(db, collectionName), limit(1)));
        
        if (!querySnapshot.empty) {
          // Collection exists and has documents, get full count
          const fullSnapshot = await getDocs(collection(db, collectionName));
          const docCount = fullSnapshot.docs.length;
          
          foundCollections[collectionName] = docCount;
          totalCollections++;
          totalDocuments += docCount;
          
          console.log(`   📁 FOUND: ${collectionName} (${docCount} documents)`);
        }
      } catch (error) {
        // Collection doesn't exist or error accessing it - this is expected for most
      }
    }
    
    console.log(`🔍 Exhaustive discovery complete: Found ${totalCollections} collections with ${totalDocuments} total documents`);
    
    // Sort collections alphabetically
    const sortedCollections = Object.keys(foundCollections)
      .sort()
      .reduce((sorted: { [key: string]: number }, key) => {
        sorted[key] = foundCollections[key];
        return sorted;
      }, {});
    
    return NextResponse.json({
      success: true,
      message: `Exhaustive Firebase discovery complete`,
      data: {
        totalCollections,
        totalDocuments,
        checkedCollections: EXHAUSTIVE_COLLECTION_LIST.length,
        foundCollections: sortedCollections,
        collectionNames: Object.keys(sortedCollections),
        summary: {
          collectionsFound: totalCollections,
          documentsFound: totalDocuments,
          collectionsChecked: EXHAUSTIVE_COLLECTION_LIST.length,
          foundRate: `${((totalCollections / EXHAUSTIVE_COLLECTION_LIST.length) * 100).toFixed(2)}%`
        }
      }
    });
    
  } catch (error) {
    console.error('Exhaustive discovery error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Exhaustive discovery failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
