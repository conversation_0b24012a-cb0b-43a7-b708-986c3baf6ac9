/**
 * Firebase Collections Audit API
 * 
 * This endpoint attempts to discover what collections actually exist in Firebase
 * by checking for documents in known collection names.
 */

import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, getDocs, limit, query } from 'firebase/firestore';
import { COLLECTIONS, TENANT_ISOLATED_COLLECTIONS, GLOBAL_COLLECTIONS, requiresTenantId } from '@/lib/collections';

export async function GET(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { success: false, message: 'Collections audit not available in production' },
        { status: 403 }
      );
    }

    console.log('🔍 Starting Firebase collections audit...');
    
    const existingCollections: { [key: string]: number } = {};
    const errors: string[] = [];

    // Get all possible collection names from our schema
    const POSSIBLE_COLLECTIONS = Object.values(COLLECTIONS);

    // Check each possible collection
    for (const collectionName of POSSIBLE_COLLECTIONS) {
      try {
        // Try to get a single document to see if collection exists
        const q = query(collection(db, collectionName), limit(1));
        const querySnapshot = await getDocs(q);
        
        if (!querySnapshot.empty) {
          // Collection exists, get full count and check tenant isolation
          const fullQuery = await getDocs(collection(db, collectionName));
          const totalDocs = fullQuery.docs.length;

          existingCollections[collectionName] = totalDocs;

          // ONLY check tenant isolation for collections that require it
          if (requiresTenantId(collectionName)) {
            // CRITICAL: Check tenant isolation for tenant-isolated collections
            let properlyIsolated = 0;
            let violatingDocs = 0;
            const violatingDocIds: string[] = [];

            fullQuery.docs.forEach(doc => {
              const data = doc.data();
              if (data.tenantId && typeof data.tenantId === 'string') {
                properlyIsolated++;
              } else {
                violatingDocs++;
                violatingDocIds.push(doc.id);
                console.error(`🚨 CRITICAL: Document ${doc.id} in ${collectionName} missing tenantId!`);
              }
            });

            if (violatingDocs > 0) {
              const errorMsg = `CRITICAL TENANT ISOLATION VIOLATION: ${collectionName} has ${violatingDocs}/${totalDocs} documents without tenantId`;
              errors.push(errorMsg);
              console.error(`🚨 ${errorMsg}`);
              console.error(`🚨 Violating document IDs: ${violatingDocIds.slice(0, 5).join(', ')}${violatingDocIds.length > 5 ? '...' : ''}`);
            }

            console.log(`✅ Tenant-isolated collection: ${collectionName} (${totalDocs} docs, ${properlyIsolated} isolated, ${violatingDocs} violations)`);
          } else {
            console.log(`✅ Global collection: ${collectionName} (${totalDocs} docs, no tenant isolation required)`);
          }
        }
      } catch (error) {
        // Collection might not exist or have permission issues
        console.log(`⏭️ Skipping ${collectionName}: ${error}`);
      }
    }

    const totalCollections = Object.keys(existingCollections).length;
    const totalDocuments = Object.values(existingCollections).reduce((sum, count) => sum + count, 0);

    console.log(`🔍 Audit complete: Found ${totalCollections} collections with ${totalDocuments} total documents`);

    const hasViolations = errors.some(error => error.includes('CRITICAL TENANT ISOLATION VIOLATION'));

    return NextResponse.json({
      success: !hasViolations, // Mark as failed if there are tenant isolation violations
      message: hasViolations
        ? `CRITICAL: Tenant isolation violations detected in ${errors.length} collections`
        : `Firebase collections audit complete`,
      data: {
        totalCollections,
        totalDocuments,
        collections: existingCollections,
        tenantIsolationViolations: hasViolations,
        errors: errors.length > 0 ? errors : undefined
      },
      warning: hasViolations ? 'IMMEDIATE ACTION REQUIRED: Fix tenant isolation violations' : undefined
    });

  } catch (error) {
    console.error('Error in Firebase collections audit:', error);
    return NextResponse.json(
      { success: false, message: 'Collections audit failed', error: String(error) },
      { status: 500 }
    );
  }
}
