import { NextRequest, NextResponse } from 'next/server';
import { enterpriseBillingService } from '@/services/enterpriseBillingService';
import { auth } from '@/lib/firebase-admin';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    switch (action) {
      case 'currencies':
        const currencies = await enterpriseBillingService.getSupportedCurrencies();
        return NextResponse.json(currencies);

      case 'convert-currency':
        const amount = parseFloat(searchParams.get('amount') || '0');
        const fromCurrency = searchParams.get('from') || 'USD';
        const toCurrency = searchParams.get('to') || 'USD';
        
        const conversion = await enterpriseBillingService.convertCurrency(amount, fromCurrency, toCurrency);
        return NextResponse.json(conversion);

      case 'tax-config':
        const taxConfig = await enterpriseBillingService.getTaxConfiguration(tenantId);
        return NextResponse.json(taxConfig);

      case 'purchase-orders':
        const poStatus = searchParams.get('status') as any;
        const purchaseOrders = await enterpriseBillingService.getPurchaseOrders(tenantId, poStatus);
        return NextResponse.json(purchaseOrders);

      case 'contracts':
        const contractStatus = searchParams.get('status') as any;
        const contracts = await enterpriseBillingService.getEnterpriseContracts(tenantId, contractStatus);
        return NextResponse.json(contracts);

      case 'calculate-tax':
        const taxAmount = parseFloat(searchParams.get('amount') || '0');
        const currency = searchParams.get('currency') || 'USD';
        const country = searchParams.get('country') || 'US';
        const state = searchParams.get('state');
        const city = searchParams.get('city');
        const postalCode = searchParams.get('postalCode');
        const categories = searchParams.get('categories')?.split(',') || [];

        const taxResult = await enterpriseBillingService.calculateTax(
          tenantId,
          taxAmount,
          currency,
          { country, state, city, postalCode },
          categories
        );
        
        return NextResponse.json(taxResult);

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in enterprise billing API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const data = await request.json();
    const { action } = data;

    switch (action) {
      case 'create-purchase-order':
        const { purchaseOrder } = data;
        const newPO = await enterpriseBillingService.createPurchaseOrder({
          ...purchaseOrder,
          tenantId,
          delivery: {
            ...purchaseOrder.delivery,
            expectedDate: new Date(purchaseOrder.delivery.expectedDate)
          },
          approvals: purchaseOrder.approvals.map((approval: any) => ({
            ...approval,
            approvedAt: approval.approvedAt ? new Date(approval.approvedAt) : undefined
          })),
          attachments: purchaseOrder.attachments.map((attachment: any) => ({
            ...attachment,
            uploadedAt: new Date(attachment.uploadedAt)
          })),
          items: purchaseOrder.items.map((item: any) => ({
            ...item,
            deliveryDate: item.deliveryDate ? new Date(item.deliveryDate) : undefined
          }))
        });
        
        return NextResponse.json(newPO);

      case 'approve-purchase-order':
        const { poId, approverLevel, approver, approved, comments } = data;
        await enterpriseBillingService.approvePurchaseOrder(
          poId,
          approverLevel,
          approver,
          approved,
          comments
        );
        
        return NextResponse.json({ success: true });

      case 'create-contract':
        const { contract } = data;
        const newContract = await enterpriseBillingService.createEnterpriseContract({
          ...contract,
          tenantId,
          terms: {
            ...contract.terms,
            startDate: new Date(contract.terms.startDate),
            endDate: new Date(contract.terms.endDate)
          }
        });
        
        return NextResponse.json(newContract);

      case 'generate-contract-invoice':
        const { contractId, billingPeriod, usageData } = data;
        const invoiceId = await enterpriseBillingService.generateContractInvoice(
          contractId,
          {
            start: new Date(billingPeriod.start),
            end: new Date(billingPeriod.end)
          },
          usageData
        );
        
        return NextResponse.json({ invoiceId });

      case 'update-exchange-rates':
        await enterpriseBillingService.updateExchangeRates();
        return NextResponse.json({ success: true });

      case 'bulk-calculate-tax':
        const { calculations } = data;
        const results = await Promise.all(
          calculations.map(async (calc: any) => {
            try {
              const result = await enterpriseBillingService.calculateTax(
                tenantId,
                calc.amount,
                calc.currency,
                calc.customerAddress,
                calc.productCategories
              );
              return { success: true, result, id: calc.id };
            } catch (error) {
              return { 
                success: false, 
                error: error instanceof Error ? error.message : 'Unknown error',
                id: calc.id 
              };
            }
          })
        );
        
        return NextResponse.json(results);

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in enterprise billing API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const resourceType = searchParams.get('type');
    const resourceId = searchParams.get('id');

    if (!resourceType || !resourceId) {
      return NextResponse.json({ error: 'Resource type and ID are required' }, { status: 400 });
    }

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const updates = await request.json();

    switch (resourceType) {
      case 'purchase-order':
        await enterpriseBillingService.updatePurchaseOrder(resourceId, updates);
        return NextResponse.json({ success: true });

      case 'contract':
        await enterpriseBillingService.updateEnterpriseContract(resourceId, updates);
        return NextResponse.json({ success: true });

      case 'tax-config':
        await enterpriseBillingService.updateTaxConfiguration(resourceId, updates);
        return NextResponse.json({ success: true });

      case 'currency':
        await enterpriseBillingService.updateCurrencyConfig(resourceId, updates);
        return NextResponse.json({ success: true });

      default:
        return NextResponse.json({ error: 'Invalid resource type' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error updating enterprise billing resource:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const resourceType = searchParams.get('type');
    const resourceId = searchParams.get('id');

    if (!resourceType || !resourceId) {
      return NextResponse.json({ error: 'Resource type and ID are required' }, { status: 400 });
    }

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await auth.verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    switch (resourceType) {
      case 'purchase-order':
        await enterpriseBillingService.deletePurchaseOrder(resourceId);
        return NextResponse.json({ success: true });

      case 'contract':
        await enterpriseBillingService.deleteEnterpriseContract(resourceId);
        return NextResponse.json({ success: true });

      default:
        return NextResponse.json({ error: 'Invalid resource type' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error deleting enterprise billing resource:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
