import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { success: false, message: 'Debug endpoint not available in production' },
        { status: 403 }
      );
    }

    console.log('API: Firebase debug requested...');

    // Check environment variables
    const envCheck = {
      FIREBASE_PROJECT_ID: !!process.env.FIREBASE_PROJECT_ID,
      FIREBASE_CLIENT_EMAIL: !!process.env.FIREBASE_CLIENT_EMAIL,
      FIREBASE_PRIVATE_KEY: !!process.env.FIREBASE_PRIVATE_KEY,
      NEXT_PUBLIC_FIREBASE_PROJECT_ID: !!process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      NEXT_PUBLIC_FIREBASE_API_KEY: !!process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    };

    // Try to initialize Firebase Admin SDK
    let adminSdkStatus = 'Not tested';
    let adminSdkError = null;

    try {
      const { adminDb } = await import('@/lib/firebase-admin');
      
      // Try a simple read operation
      const testCollection = adminDb.collection('test');
      await testCollection.limit(1).get();
      
      adminSdkStatus = 'Working';
    } catch (error) {
      adminSdkStatus = 'Failed';
      adminSdkError = error instanceof Error ? error.message : 'Unknown error';
    }

    // Try to initialize regular Firebase
    let clientSdkStatus = 'Not tested';
    let clientSdkError = null;

    try {
      const { db } = await import('@/lib/firebase');
      const { collection, getDocs, limit, query } = await import('firebase/firestore');
      
      // Try a simple read operation
      const testQuery = query(collection(db, 'test'), limit(1)); // Using 'test' for debug purposes
      await getDocs(testQuery);
      
      clientSdkStatus = 'Working';
    } catch (error) {
      clientSdkStatus = 'Failed';
      clientSdkError = error instanceof Error ? error.message : 'Unknown error';
    }

    return NextResponse.json({
      success: true,
      environment: process.env.NODE_ENV,
      envVariables: envCheck,
      firebaseAdminSdk: {
        status: adminSdkStatus,
        error: adminSdkError
      },
      firebaseClientSdk: {
        status: clientSdkStatus,
        error: clientSdkError
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('API: Debug error:', errorMessage);

    return NextResponse.json(
      { success: false, message: `Debug failed: ${errorMessage}` },
      { status: 500 }
    );
  }
}
