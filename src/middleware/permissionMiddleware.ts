/**
 * Permission Middleware for API Routes
 * Server-side permission checking for API endpoints
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { EvexaModule, PermissionAction } from '@/types/personas';
import { createPermissionCheckingService } from '@/services/permissionCheckingService';

// Initialize Firebase Admin if not already initialized
if (!getApps().length) {
  initializeApp({
    credential: cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    }),
  });
}

const auth = getAuth();
const db = getFirestore();

// ===== MIDDLEWARE TYPES =====

export interface PermissionRequirement {
  module: EvexaModule;
  action: PermissionAction;
}

export interface PermissionMiddlewareOptions {
  // Required permissions
  permissions?: PermissionRequirement[];
  
  // System permissions
  systemPermissions?: string[];
  
  // Whether all permissions are required (AND) or any permission (OR)
  requireAll?: boolean;
  
  // Whether to check tenant isolation
  checkTenantIsolation?: boolean;
  
  // Custom error messages
  errorMessages?: {
    unauthorized?: string;
    forbidden?: string;
    tenantMismatch?: string;
  };
}

export interface AuthenticatedRequest extends NextApiRequest {
  user: {
    uid: string;
    email?: string;
    tenantId?: string;
  };
  tenantId: string;
}

export type PermissionHandler = (
  req: AuthenticatedRequest,
  res: NextApiResponse
) => Promise<void> | void;

// ===== AUTHENTICATION HELPER =====

async function authenticateRequest(req: NextApiRequest): Promise<{
  uid: string;
  email?: string;
  tenantId?: string;
}> {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  
  try {
    const decodedToken = await auth.verifyIdToken(token);
    
    // Get user's tenant ID from user profile
    const userDoc = await db.collection('user_profiles').doc(decodedToken.uid).get();
    const userData = userDoc.data();
    
    return {
      uid: decodedToken.uid,
      email: decodedToken.email,
      tenantId: userData?.tenantId
    };
  } catch (error) {
    throw new Error('Invalid authentication token');
  }
}

// ===== PERMISSION MIDDLEWARE =====

export function withPermissions(
  options: PermissionMiddlewareOptions = {}
) {
  return function(handler: PermissionHandler) {
    return async function(req: NextApiRequest, res: NextApiResponse) {
      const {
        permissions = [],
        systemPermissions = [],
        requireAll = true,
        checkTenantIsolation = true,
        errorMessages = {}
      } = options;

      try {
        // Authenticate the request
        const user = await authenticateRequest(req);
        
        if (!user.tenantId) {
          return res.status(403).json({
            error: 'Forbidden',
            message: errorMessages.forbidden || 'User is not associated with a tenant'
          });
        }

        // Add user and tenant info to request
        const authenticatedReq = req as AuthenticatedRequest;
        authenticatedReq.user = user;
        authenticatedReq.tenantId = user.tenantId;

        // Check tenant isolation if required
        if (checkTenantIsolation) {
          const tenantIdFromQuery = req.query.tenantId as string;
          const tenantIdFromBody = req.body?.tenantId as string;
          const requestTenantId = tenantIdFromQuery || tenantIdFromBody;

          if (requestTenantId && requestTenantId !== user.tenantId) {
            return res.status(403).json({
              error: 'Forbidden',
              message: errorMessages.tenantMismatch || 'Tenant ID mismatch'
            });
          }
        }

        // Check permissions if any are specified
        if (permissions.length > 0 || systemPermissions.length > 0) {
          const permissionService = createPermissionCheckingService(user.tenantId);

          // Check module permissions
          const moduleChecks = await Promise.all(
            permissions.map(async ({ module, action }) => {
              const result = await permissionService.checkPermission(user.uid, module, action);
              return { module, action, hasPermission: result.hasPermission };
            })
          );

          // Check system permissions
          const systemChecks = await Promise.all(
            systemPermissions.map(async (permission) => {
              const hasPermission = await permissionService.checkSystemPermission(
                user.uid,
                permission as any
              );
              return { permission, hasPermission };
            })
          );

          // Determine overall access
          let hasAccess = true;
          const deniedPermissions: string[] = [];

          if (requireAll) {
            // All permissions must be granted
            moduleChecks.forEach(({ module, action, hasPermission }) => {
              if (!hasPermission) {
                hasAccess = false;
                deniedPermissions.push(`${module}:${action}`);
              }
            });

            systemChecks.forEach(({ permission, hasPermission }) => {
              if (!hasPermission) {
                hasAccess = false;
                deniedPermissions.push(permission);
              }
            });
          } else {
            // At least one permission must be granted
            const hasAnyModuleAccess = moduleChecks.some(({ hasPermission }) => hasPermission);
            const hasAnySystemAccess = systemChecks.some(({ hasPermission }) => hasPermission);
            
            hasAccess = hasAnyModuleAccess || hasAnySystemAccess;
            
            if (!hasAccess) {
              deniedPermissions.push(
                ...moduleChecks.map(({ module, action }) => `${module}:${action}`),
                ...systemChecks.map(({ permission }) => permission)
              );
            }
          }

          if (!hasAccess) {
            return res.status(403).json({
              error: 'Forbidden',
              message: errorMessages.forbidden || `Missing required permissions: ${deniedPermissions.join(', ')}`,
              missingPermissions: deniedPermissions
            });
          }
        }

        // Call the actual handler
        await handler(authenticatedReq, res);

      } catch (error) {
        console.error('Permission middleware error:', error);
        
        if (error.message.includes('authorization') || error.message.includes('token')) {
          return res.status(401).json({
            error: 'Unauthorized',
            message: errorMessages.unauthorized || 'Authentication required'
          });
        }

        return res.status(500).json({
          error: 'Internal Server Error',
          message: 'An error occurred while checking permissions'
        });
      }
    };
  };
}

// ===== CONVENIENCE FUNCTIONS =====

/**
 * Middleware for protecting API routes that require specific module access
 */
export function withModuleAccess(
  module: EvexaModule,
  action: PermissionAction = 'read',
  options: Omit<PermissionMiddlewareOptions, 'permissions'> = {}
) {
  return withPermissions({
    ...options,
    permissions: [{ module, action }]
  });
}

/**
 * Middleware for protecting admin-only API routes
 */
export function withAdminAccess(
  options: Omit<PermissionMiddlewareOptions, 'systemPermissions'> = {}
) {
  return withPermissions({
    ...options,
    systemPermissions: ['canManageUsers', 'canManageSettings']
  });
}

/**
 * Middleware for protecting API routes that require analytics access
 */
export function withAnalyticsAccess(
  options: Omit<PermissionMiddlewareOptions, 'systemPermissions'> = {}
) {
  return withPermissions({
    ...options,
    systemPermissions: ['canViewAnalytics']
  });
}

/**
 * Middleware for protecting API routes that require multiple module access
 */
export function withMultipleModuleAccess(
  permissions: PermissionRequirement[],
  options: Omit<PermissionMiddlewareOptions, 'permissions'> = {}
) {
  return withPermissions({
    ...options,
    permissions
  });
}

/**
 * Basic authentication middleware (no permission checking)
 */
export function withAuth(
  options: Pick<PermissionMiddlewareOptions, 'checkTenantIsolation' | 'errorMessages'> = {}
) {
  return withPermissions({
    ...options,
    permissions: [],
    systemPermissions: []
  });
}

// ===== UTILITY FUNCTIONS =====

/**
 * Extract tenant ID from request
 */
export function getTenantIdFromRequest(req: NextApiRequest): string | null {
  const tenantIdFromQuery = req.query.tenantId as string;
  const tenantIdFromBody = req.body?.tenantId as string;
  return tenantIdFromQuery || tenantIdFromBody || null;
}

/**
 * Validate tenant access for request
 */
export async function validateTenantAccess(
  req: AuthenticatedRequest,
  targetTenantId: string
): Promise<boolean> {
  return req.tenantId === targetTenantId;
}

/**
 * Create error response for permission denied
 */
export function createPermissionDeniedResponse(
  missingPermissions: string[],
  customMessage?: string
) {
  return {
    error: 'Forbidden',
    message: customMessage || `Missing required permissions: ${missingPermissions.join(', ')}`,
    missingPermissions
  };
}

// ===== EXPORTS =====

export default withPermissions;
