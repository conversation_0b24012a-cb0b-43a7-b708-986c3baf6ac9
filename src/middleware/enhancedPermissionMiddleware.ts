/**
 * Enhanced Permission Middleware
 * API middleware using the Module Permission Engine for robust permission checking
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { 
  ModulePermissionEngine, 
  createModulePermissionEngine, 
  createPermissionContext,
  validatePermissions,
  PermissionContext
} from '@/services/modulePermissionEngine';
import { EvexaModule, PermissionAction, TenantPersona } from '@/types/personas';
import { createTenantPersonaService } from '@/services/tenantPersonaService';
import { verifyAuthToken } from '@/lib/auth';

// ===== TYPES =====

interface AuthenticatedRequest extends NextApiRequest {
  user: {
    uid: string;
    email: string;
    tenantId: string;
    persona?: TenantPersona;
  };
  permissionContext: PermissionContext;
  permissionEngine: ModulePermissionEngine;
}

interface PermissionRequirement {
  module: EvexaModule;
  action: PermissionAction;
}

interface EnhancedPermissionOptions {
  permissions?: PermissionRequirement[];
  systemPermissions?: string[];
  requireAll?: boolean;
  allowSuperAdmin?: boolean;
  customValidator?: (context: PermissionContext) => Promise<boolean>;
  errorMessages?: {
    unauthorized?: string;
    forbidden?: string;
    customValidation?: string;
  };
  cachePermissions?: boolean;
  logAccess?: boolean;
}

type EnhancedApiHandler = (
  req: AuthenticatedRequest,
  res: NextApiResponse
) => Promise<void> | void;

// ===== PERMISSION CACHE =====

class MiddlewareCache {
  private cache = new Map<string, { result: boolean; timestamp: number }>();
  private readonly TTL = 2 * 60 * 1000; // 2 minutes

  set(key: string, value: boolean): void {
    this.cache.set(key, { result: value, timestamp: Date.now() });
  }

  get(key: string): boolean | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() - entry.timestamp > this.TTL) {
      this.cache.delete(key);
      return null;
    }

    return entry.result;
  }

  clear(): void {
    this.cache.clear();
  }
}

const middlewareCache = new MiddlewareCache();

// ===== ENHANCED PERMISSION MIDDLEWARE =====

export function withEnhancedPermissions(
  handler: EnhancedApiHandler,
  options: EnhancedPermissionOptions = {}
) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    try {
      // 1. Authenticate user
      const authResult = await verifyAuthToken(req);
      if (!authResult.success || !authResult.user) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: options.errorMessages?.unauthorized || 'Authentication required'
        });
      }

      const { user } = authResult;
      
      // 2. Initialize permission engine
      const permissionEngine = createModulePermissionEngine(user.tenantId);
      
      // 3. Load user persona
      const personaService = createTenantPersonaService(user.tenantId);
      const userPersona = await personaService.getUserPersona(user.uid);
      
      // 4. Create permission context
      const permissionContext = createPermissionContext(
        user.uid,
        user.tenantId,
        userPersona || undefined,
        'professional', // TODO: Get from user subscription
        [] // TODO: Get custom overrides
      );

      // 5. Check super admin bypass
      if (options.allowSuperAdmin && userPersona?.id === 'super_admin') {
        const authenticatedReq = req as AuthenticatedRequest;
        authenticatedReq.user = { ...user, persona: userPersona };
        authenticatedReq.permissionContext = permissionContext;
        authenticatedReq.permissionEngine = permissionEngine;
        
        if (options.logAccess) {
          console.log(`Super admin access: ${user.email} -> ${req.url}`);
        }
        
        return await handler(authenticatedReq, res);
      }

      // 6. Check permissions if specified
      if (options.permissions && options.permissions.length > 0) {
        const cacheKey = options.cachePermissions 
          ? `${user.uid}:${JSON.stringify(options.permissions)}`
          : null;

        let hasAccess = false;

        // Check cache first
        if (cacheKey && options.cachePermissions) {
          const cached = middlewareCache.get(cacheKey);
          if (cached !== null) {
            hasAccess = cached;
          }
        }

        // Perform permission check if not cached
        if (cacheKey === null || middlewareCache.get(cacheKey) === null) {
          try {
            if (options.requireAll !== false) {
              // All permissions must be granted
              await validatePermissions(permissionEngine, permissionContext, options.permissions);
              hasAccess = true;
            } else {
              // At least one permission must be granted
              const results = await permissionEngine.checkBulkPermissions(
                permissionContext, 
                options.permissions
              );
              hasAccess = results.results.some(r => r.hasPermission);
            }

            // Cache the result
            if (cacheKey && options.cachePermissions) {
              middlewareCache.set(cacheKey, hasAccess);
            }

          } catch (error) {
            hasAccess = false;
          }
        }

        if (!hasAccess) {
          const deniedPermissions = options.permissions.map(p => `${p.module}:${p.action}`);
          
          if (options.logAccess) {
            console.log(`Access denied: ${user.email} -> ${req.url} (${deniedPermissions.join(', ')})`);
          }

          return res.status(403).json({
            error: 'Forbidden',
            message: options.errorMessages?.forbidden || `Missing required permissions: ${deniedPermissions.join(', ')}`,
            missingPermissions: deniedPermissions
          });
        }
      }

      // 7. Check system permissions
      if (options.systemPermissions && options.systemPermissions.length > 0) {
        const systemPerms = userPersona?.permissions.systemPermissions || {};
        
        const hasSystemAccess = options.requireAll !== false
          ? options.systemPermissions.every(perm => systemPerms[perm as keyof typeof systemPerms])
          : options.systemPermissions.some(perm => systemPerms[perm as keyof typeof systemPerms]);

        if (!hasSystemAccess) {
          if (options.logAccess) {
            console.log(`System permission denied: ${user.email} -> ${req.url} (${options.systemPermissions.join(', ')})`);
          }

          return res.status(403).json({
            error: 'Forbidden',
            message: options.errorMessages?.forbidden || `Missing system permissions: ${options.systemPermissions.join(', ')}`,
            missingSystemPermissions: options.systemPermissions
          });
        }
      }

      // 8. Custom validation
      if (options.customValidator) {
        const customValid = await options.customValidator(permissionContext);
        if (!customValid) {
          if (options.logAccess) {
            console.log(`Custom validation failed: ${user.email} -> ${req.url}`);
          }

          return res.status(403).json({
            error: 'Forbidden',
            message: options.errorMessages?.customValidation || 'Custom validation failed'
          });
        }
      }

      // 9. All checks passed - call handler
      const authenticatedReq = req as AuthenticatedRequest;
      authenticatedReq.user = { ...user, persona: userPersona };
      authenticatedReq.permissionContext = permissionContext;
      authenticatedReq.permissionEngine = permissionEngine;

      if (options.logAccess) {
        console.log(`Access granted: ${user.email} -> ${req.url}`);
      }

      await handler(authenticatedReq, res);

    } catch (error) {
      console.error('Enhanced permission middleware error:', error);
      
      if (error instanceof Error && error.message.includes('authorization')) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: options.errorMessages?.unauthorized || 'Authentication required'
        });
      }

      return res.status(500).json({
        error: 'Internal Server Error',
        message: 'An error occurred while checking permissions'
      });
    }
  };
}

// ===== CONVENIENCE FUNCTIONS =====

/**
 * Middleware for protecting API routes that require specific module access
 */
export function withModulePermission(
  module: EvexaModule,
  action: PermissionAction = 'read',
  options: Omit<EnhancedPermissionOptions, 'permissions'> = {}
) {
  return function(handler: EnhancedApiHandler) {
    return withEnhancedPermissions(handler, {
      ...options,
      permissions: [{ module, action }]
    });
  };
}

/**
 * Middleware for admin-only routes
 */
export function withAdminPermission(
  options: Omit<EnhancedPermissionOptions, 'systemPermissions'> = {}
) {
  return function(handler: EnhancedApiHandler) {
    return withEnhancedPermissions(handler, {
      ...options,
      systemPermissions: ['canManageUsers', 'canManageSettings']
    });
  };
}

/**
 * Middleware for super admin only routes
 */
export function withSuperAdminOnly(
  options: Omit<EnhancedPermissionOptions, 'customValidator'> = {}
) {
  return function(handler: EnhancedApiHandler) {
    return withEnhancedPermissions(handler, {
      ...options,
      customValidator: async (context) => {
        return context.persona?.id === 'super_admin';
      }
    });
  };
}

/**
 * Clear middleware cache
 */
export function clearMiddlewareCache(): void {
  middlewareCache.clear();
}

// ===== EXPORT TYPES =====

export type { 
  AuthenticatedRequest, 
  PermissionRequirement, 
  EnhancedPermissionOptions,
  EnhancedApiHandler
};
