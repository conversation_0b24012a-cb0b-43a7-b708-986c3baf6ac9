/**
 * Subscription Enforcement UI Components
 * Components for displaying and managing subscription limits
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  Users, 
  AlertTriangle, 
  Crown, 
  TrendingUp, 
  UserPlus, 
  Mail,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react';
import { 
  useUserLimits, 
  useInvitationLimits, 
  useEnforcementActions 
} from '@/hooks/useSubscriptionEnforcement';
import { EnforcementAction } from '@/services/subscriptionEnforcementService';

// ===== TYPES =====

interface UserLimitDisplayProps {
  showDetails?: boolean;
  className?: string;
}

interface InvitationLimitDisplayProps {
  className?: string;
}

interface EnforcementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  action: EnforcementAction;
  onUpgrade?: () => void;
  onContactSupport?: () => void;
}

interface UserLimitGateProps {
  requestedUsers?: number;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
}

// ===== USER LIMIT DISPLAY =====

export const UserLimitDisplay: React.FC<UserLimitDisplayProps> = ({
  showDetails = true,
  className
}) => {
  const { user } = useAuth();

  // SUPER ADMIN BYPASS: Super admin should not see limit displays
  const { isSuperAdmin } = require('@/services/superAdminService');
  if (user && isSuperAdmin(user.id)) {
    return null; // Don't render anything for super admin
  }

  const {
    userLimits,
    usageBreakdown,
    isLoading,
    error,
    usagePercentage,
    isNearLimit,
    isAtLimit
  } = useUserLimits();

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-2 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Failed to load user limits: {error}
        </AlertDescription>
      </Alert>
    );
  }

  if (!userLimits || !usageBreakdown) {
    return null;
  }

  const getStatusColor = () => {
    if (isAtLimit) return 'text-red-600';
    if (isNearLimit) return 'text-amber-600';
    return 'text-green-600';
  };

  const getStatusIcon = () => {
    if (isAtLimit) return <XCircle className="h-5 w-5 text-red-500" />;
    if (isNearLimit) return <AlertTriangle className="h-5 w-5 text-amber-500" />;
    return <CheckCircle className="h-5 w-5 text-green-500" />;
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Users className="h-5 w-5" />
          User Limits
          {getStatusIcon()}
        </CardTitle>
        <CardDescription>
          Current user usage and subscription limits
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Usage Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Users</span>
            <span className={getStatusColor()}>
              {usageBreakdown.activeUsers} / {userLimits.maxUsers === -1 ? '∞' : userLimits.maxUsers}
            </span>
          </div>
          <Progress 
            value={userLimits.maxUsers === -1 ? 0 : usagePercentage} 
            className="h-2"
          />
          <div className="text-xs text-gray-600">
            {userLimits.availableSlots === -1 
              ? 'Unlimited slots available'
              : `${userLimits.availableSlots} slots available`
            }
          </div>
        </div>

        {/* Status Alerts */}
        {isAtLimit && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              You've reached your user limit. Upgrade to add more users.
            </AlertDescription>
          </Alert>
        )}

        {isNearLimit && !isAtLimit && (
          <Alert variant="default" className="border-amber-200 bg-amber-50">
            <AlertTriangle className="h-4 w-4 text-amber-600" />
            <AlertDescription className="text-amber-800">
              You're using {usagePercentage.toFixed(0)}% of your user limit. Consider upgrading soon.
            </AlertDescription>
          </Alert>
        )}

        {/* Detailed Breakdown */}
        {showDetails && (
          <div className="grid grid-cols-2 gap-4 pt-2 border-t">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {usageBreakdown.activeUsers}
              </div>
              <div className="text-xs text-gray-600">Active Users</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-amber-600">
                {usageBreakdown.pendingInvitations}
              </div>
              <div className="text-xs text-gray-600">Pending Invites</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// ===== INVITATION LIMIT DISPLAY =====

export const InvitationLimitDisplay: React.FC<InvitationLimitDisplayProps> = ({
  className
}) => {
  const { 
    invitationLimits, 
    isLoading, 
    error, 
    canSendInvitations, 
    pendingInvitations, 
    maxInvitations 
  } = useInvitationLimits();

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  if (error || !invitationLimits) {
    return null;
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Mail className="h-5 w-5" />
          Invitation Status
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <div className="text-sm text-gray-600">Pending Invitations</div>
            <div className="text-2xl font-bold">{pendingInvitations}</div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-600">Available Slots</div>
            <div className="text-2xl font-bold">
              {maxInvitations === -1 ? '∞' : maxInvitations}
            </div>
          </div>
        </div>
        
        {!canSendInvitations(1) && (
          <Alert variant="destructive" className="mt-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              No invitation slots available. Upgrade to send more invitations.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

// ===== ENFORCEMENT DIALOG =====

export const EnforcementDialog: React.FC<EnforcementDialogProps> = ({
  isOpen,
  onClose,
  action,
  onUpgrade,
  onContactSupport
}) => {
  const getIcon = () => {
    switch (action.type) {
      case 'block':
        return <XCircle className="h-6 w-6 text-red-500" />;
      case 'warn':
        return <AlertTriangle className="h-6 w-6 text-amber-500" />;
      case 'allow':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      default:
        return <Info className="h-6 w-6 text-blue-500" />;
    }
  };

  const getTitle = () => {
    switch (action.type) {
      case 'block':
        return 'Action Blocked';
      case 'warn':
        return 'Usage Warning';
      case 'allow':
        return 'Action Allowed';
      default:
        return 'Subscription Notice';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getIcon()}
            {getTitle()}
          </DialogTitle>
          <DialogDescription>
            {action.message}
          </DialogDescription>
        </DialogHeader>
        
        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          
          {action.upgradeUrl && onUpgrade && (
            <Button onClick={onUpgrade} className="bg-blue-600 hover:bg-blue-700">
              <Crown className="h-4 w-4 mr-2" />
              Upgrade Plan
            </Button>
          )}
          
          {action.contactSupport && onContactSupport && (
            <Button variant="outline" onClick={onContactSupport}>
              Contact Support
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// ===== USER LIMIT GATE =====

export const UserLimitGate: React.FC<UserLimitGateProps> = ({
  requestedUsers = 1,
  children,
  fallback,
  showUpgradePrompt = true
}) => {
  const { user } = useAuth();

  // SUPER ADMIN BYPASS: Super admin has unlimited access
  const { isSuperAdmin } = require('@/services/superAdminService');
  if (user && isSuperAdmin(user.id)) {
    return <>{children}</>;
  }

  const { checkAction } = useEnforcementActions();
  const [action, setAction] = useState<EnforcementAction | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showDialog, setShowDialog] = useState(false);

  useEffect(() => {
    const checkLimits = async () => {
      setIsLoading(true);
      const result = await checkAction(requestedUsers);
      setAction(result);
      setIsLoading(false);

      if (result.type === 'block' && showUpgradePrompt) {
        setShowDialog(true);
      }
    };

    checkLimits();
  }, [requestedUsers, checkAction, showUpgradePrompt]);

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  if (!action) {
    return fallback || null;
  }

  if (action.type === 'allow') {
    return <>{children}</>;
  }

  if (action.type === 'warn') {
    return (
      <div>
        <Alert variant="default" className="border-amber-200 bg-amber-50 mb-4">
          <AlertTriangle className="h-4 w-4 text-amber-600" />
          <AlertDescription className="text-amber-800">
            {action.message}
          </AlertDescription>
        </Alert>
        {children}
      </div>
    );
  }

  // Block case
  if (fallback) {
    return <>{fallback}</>;
  }

  return (
    <>
      <Alert variant="destructive">
        <XCircle className="h-4 w-4" />
        <AlertDescription>
          {action.message}
        </AlertDescription>
      </Alert>
      
      {action && (
        <EnforcementDialog
          isOpen={showDialog}
          onClose={() => setShowDialog(false)}
          action={action}
          onUpgrade={() => {
            if (action.upgradeUrl) {
              window.location.href = action.upgradeUrl;
            }
          }}
          onContactSupport={() => {
            // Handle contact support
            console.log('Contact support requested');
          }}
        />
      )}
    </>
  );
};

// ===== EXPORT ALL COMPONENTS =====

// ===== SUBSCRIPTION USAGE SUMMARY =====

interface SubscriptionUsageSummaryProps {
  className?: string;
}

export const SubscriptionUsageSummary: React.FC<SubscriptionUsageSummaryProps> = ({
  className
}) => {
  const { usageBreakdown, isLoading, error } = useUserLimits();

  if (isLoading || error || !usageBreakdown) {
    return null;
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${className}`}>
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-blue-500" />
            <div>
              <div className="text-2xl font-bold">{usageBreakdown.activeUsers}</div>
              <div className="text-sm text-gray-600">Active Users</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Mail className="h-5 w-5 text-amber-500" />
            <div>
              <div className="text-2xl font-bold">{usageBreakdown.pendingInvitations}</div>
              <div className="text-sm text-gray-600">Pending Invites</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5 text-green-500" />
            <div>
              <div className="text-2xl font-bold">
                {usageBreakdown.availableSlots === -1 ? '∞' : usageBreakdown.availableSlots}
              </div>
              <div className="text-sm text-gray-600">Available Slots</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


