"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Building2,
  Users,
  Crown,
  Plus,
  Settings,
  BarChart3,
  Database,
  Shield,
  Loader2
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useAuth } from '@/contexts/auth-context';
import { useTenant } from '@/contexts/tenant-context';
import { getAllTenants, isSuperAdmin, SUPER_ADMIN_USER_IDS } from '@/services/superAdminService';
import { useToast } from '@/hooks/use-toast';
import TenantCreationDialog from './tenant-creation-dialog';
import type { Tenant } from '@/types/firestore';

interface SuperAdminDashboardProps {
  onTenantSwitch?: (tenantId: string) => void;
}

export default function SuperAdminDashboard({ onTenantSwitch }: SuperAdminDashboardProps) {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateTenantDialog, setShowCreateTenantDialog] = useState(false);
  const [showAnalyticsDialog, setShowAnalyticsDialog] = useState(false);

  const { user } = useAuth();
  const { currentTenant, switchTenant } = useTenant();
  const { toast } = useToast();

  useEffect(() => {
    if (user && isSuperAdmin(user.id)) {
      loadTenants();
    } else {
      setLoading(false);
    }
  }, [user]);

  const loadTenants = async () => {
    try {
      setLoading(true);
      const allTenants = await getAllTenants();
      setTenants(allTenants);
    } catch (error) {
      console.error('Error loading tenants:', error);
      toast({
        title: "Error",
        description: "Failed to load tenants",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateNewTenant = () => {
    setShowCreateTenantDialog(true);
  };

  const handlePlatformAnalytics = () => {
    setShowAnalyticsDialog(true);
  };

  const handleTenantSwitch = async (tenantId: string) => {
    try {
      await switchTenant(tenantId);
      if (onTenantSwitch) {
        onTenantSwitch(tenantId);
      }
      toast({
        title: "Tenant Switched",
        description: "Successfully switched to the selected tenant",
      });
    } catch (error) {
      console.error('Error switching tenant:', error);
      toast({
        title: "Switch Failed",
        description: "Failed to switch tenant",
        variant: "destructive"
      });
    }
  };

  // Security check
  if (!user || !isSuperAdmin(user.id)) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <Shield className="h-12 w-12 mx-auto text-red-500 mb-4" />
          <h2 className="text-2xl font-bold mb-2">Access Denied</h2>
          <p className="text-muted-foreground">You don't have super admin privileges.</p>
          <p className="text-sm text-muted-foreground mt-2">
            Super Admin User IDs: {SUPER_ADMIN_USER_IDS.join(', ')}
          </p>
          <p className="text-sm text-muted-foreground">
            Your User ID: {user?.id || 'Not authenticated'}
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading super admin dashboard...</p>
        </div>
      </div>
    );
  }

  // Individual dev tenants for display
  const devTenant = tenants.find(t =>
    t.slug === 'evexa-dev' ||
    t.id === 'evexa-development-company' ||
    (t.type === 'development' && t.slug !== 'evexa-demo')
  );
  const demoTenant = tenants.find(t => t.slug === 'evexa-demo');

  // Categorized tenant arrays for stats
  const devTenants = tenants.filter(t =>
    t.type === 'development' ||
    t.slug === 'evexa-dev' ||
    t.slug === 'evexa-demo' ||
    t.plan === 'development' ||
    t.id === 'evexa-development-company'
  );
  const clientTenants = tenants.filter(t =>
    t.type !== 'development' &&
    t.slug !== 'evexa-dev' &&
    t.slug !== 'evexa-demo' &&
    t.plan !== 'development' &&
    t.id !== 'evexa-development-company'
  );

  return (
    <div className="container mx-auto py-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold flex items-center gap-2">
              <Crown className="h-6 w-6 text-yellow-500" />
              Superman Dashboard
            </h2>
            <p className="text-muted-foreground">
              EVEXA Development Company - Platform Management
            </p>
          </div>
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
            Super Admin
          </Badge>
        </div>

        {/* Platform Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Tenants</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{tenants.length}</div>
              <p className="text-xs text-muted-foreground">Active clients</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Active Tenants</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {tenants.filter(t => t.status === 'active').length}
              </div>
              <p className="text-xs text-muted-foreground">Currently active</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Client Tenants</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {clientTenants.length}
              </div>
              <p className="text-xs text-muted-foreground">Paying customers</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Dev Tenants</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {devTenants.length}
              </div>
              <p className="text-xs text-muted-foreground">Development & demo</p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Quick Actions
            </CardTitle>
            <CardDescription>
              Common platform management tasks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 md:gap-4 mb-4">
              <Button
                variant="outline"
                onClick={() => window.open('/super-admin/tenant-admin', '_self')}
                className="h-20 flex flex-col items-center justify-center gap-2 p-2"
              >
                <Building2 className="h-12 w-12" />
                <span className="text-xs font-medium text-center leading-tight">Tenant Admin</span>
              </Button>

              <Button
                variant="outline"
                onClick={() => window.open('/super-admin/data-management', '_self')}
                className="h-20 flex flex-col items-center justify-center gap-2 p-2"
              >
                <Database className="h-12 w-12" />
                <span className="text-xs font-medium text-center leading-tight">Data Management</span>
              </Button>

              <Button
                variant="outline"
                onClick={handlePlatformAnalytics}
                className="h-20 flex flex-col items-center justify-center gap-2 p-2"
              >
                <BarChart3 className="h-12 w-12" />
                <span className="text-xs font-medium text-center leading-tight">Platform Analytics</span>
              </Button>

              <Button
                variant="outline"
                onClick={() => window.open('/super-admin/user-management', '_self')}
                className="h-20 flex flex-col items-center justify-center gap-2 p-2"
              >
                <Users className="h-12 w-12" />
                <span className="text-xs font-medium text-center leading-tight">User Management</span>
              </Button>
            </div>

            <div className="flex gap-4 pt-4 border-t">
              <button
                onClick={handleCreateNewTenant}
                className="group relative inline-flex items-center overflow-hidden rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden"
              >
                <span className="absolute -start-full transition-all group-hover:start-2">
                  <Plus className="size-4" />
                </span>
                <span className="text-sm font-medium transition-all group-hover:ms-2">Create New Tenant</span>
              </button>
            </div>
          </CardContent>
        </Card>

        {/* EVEXA Development Tenants */}
        {(devTenant || demoTenant) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                EVEXA Development Tenants
              </CardTitle>
              <CardDescription>
                Development and demo environments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {devTenant && (
                  <Card className="border-blue-200 bg-blue-50">
                    <CardContent className="p-4">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                        <div className="min-w-0 flex-1">
                          <h4 className="font-medium truncate">{devTenant.name}</h4>
                          <p className="text-sm text-muted-foreground truncate">{devTenant.slug}.evexa.com</p>
                        </div>
                        <div className="flex items-center gap-2 flex-shrink-0">
                          <Badge variant="default" className="text-xs">Development</Badge>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleTenantSwitch(devTenant.id)}
                            className="text-xs px-2"
                          >
                            Switch
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {demoTenant && (
                  <Card className="border-green-200 bg-green-50">
                    <CardContent className="p-4">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                        <div className="min-w-0 flex-1">
                          <h4 className="font-medium truncate">{demoTenant.name}</h4>
                          <p className="text-sm text-muted-foreground truncate">{demoTenant.slug}.evexa.com</p>
                        </div>
                        <div className="flex items-center gap-2 flex-shrink-0">
                          <Badge variant="secondary" className="text-xs">Demo</Badge>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleTenantSwitch(demoTenant.id)}
                            className="text-xs px-2"
                          >
                            Switch
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Client Tenants */}
        {clientTenants.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Client Tenants ({clientTenants.length})
              </CardTitle>
              <CardDescription>
                Active client tenants on the platform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {clientTenants.slice(0, 6).map((tenant) => (
                  <Card key={tenant.id}>
                    <CardContent className="p-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">{tenant.name}</h4>
                          <Badge variant={tenant.status === 'active' ? 'default' : 'secondary'}>
                            {tenant.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{tenant.slug}.evexa.com</p>
                        <p className="text-sm text-muted-foreground capitalize">{tenant.plan} plan</p>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleTenantSwitch(tenant.id)}
                          >
                            Switch
                          </Button>
                          <Button size="sm" variant="outline">
                            Manage
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
              {clientTenants.length > 6 && (
                <div className="mt-4 text-center">
                  <Button variant="outline">
                    View All {clientTenants.length} Tenants
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Tenant Creation Dialog */}
      <TenantCreationDialog
        open={showCreateTenantDialog}
        onOpenChange={setShowCreateTenantDialog}
        onTenantCreated={loadTenants}
      />

      {/* Platform Analytics Dialog */}
      <Dialog open={showAnalyticsDialog} onOpenChange={setShowAnalyticsDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Platform Analytics</DialogTitle>
            <DialogDescription>
              Platform-wide analytics dashboard is coming soon. This will show aggregate metrics across all tenants.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end">
            <Button onClick={() => setShowAnalyticsDialog(false)}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
