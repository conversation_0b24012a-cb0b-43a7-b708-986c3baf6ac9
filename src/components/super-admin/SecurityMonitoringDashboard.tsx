"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Shield,
  Lock,
  Eye,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  FileCheck,
  Key,
  Server,
  Globe,
  Zap,
  RefreshCw,
  Activity,
  Brain,
  Container
} from 'lucide-react';
import { securityService } from '@/services/securityService';
import { licenseService } from '@/services/licenseService';
import { secureEnv } from '@/lib/secureEnv';
import { securityMetricsService } from '@/services/securityMetricsService';
import { useAuth } from '@/contexts/auth-context';

interface SecurityStatus {
  component: string;
  status: 'active' | 'inactive' | 'error' | 'warning';
  lastCheck: Date;
  details: string;
  metrics?: Record<string, any>;
}

interface SecurityMetrics {
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  warningChecks: number;
  lastUpdate: Date;
}

export default function SecurityMonitoringDashboard() {
  const [securityStatuses, setSecurityStatuses] = useState<SecurityStatus[]>([]);
  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const { user } = useAuth();

  useEffect(() => {
    performSecurityCheck();
    
    // Set up periodic refresh every 30 seconds
    const interval = setInterval(performSecurityCheck, 30000);
    return () => clearInterval(interval);
  }, []);

  const performSecurityCheck = async () => {
    try {
      setIsLoading(true);

      // Use the new security metrics service for comprehensive real-time checks
      const implementationStatuses = await securityMetricsService.getSecurityImplementationStatus();
      const summary = await securityMetricsService.getSecuritySummary();

      // Convert implementation statuses to SecurityStatus format
      const statuses: SecurityStatus[] = implementationStatuses.map(impl => ({
        component: impl.component,
        status: impl.status,
        lastCheck: impl.lastCheck,
        details: impl.details,
        metrics: impl.metrics
      }));

      setSecurityStatuses(statuses);

      // Set metrics from summary
      setMetrics({
        totalChecks: summary.total,
        passedChecks: summary.active,
        failedChecks: summary.errors,
        warningChecks: summary.warnings,
        lastUpdate: summary.lastUpdate
      });

      setLastRefresh(new Date());
    } catch (error) {
      console.error('Security check failed:', error);

      // Fallback to basic status if metrics service fails
      setSecurityStatuses([{
        component: 'Security Monitoring',
        status: 'error',
        lastCheck: new Date(),
        details: `Security check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        metrics: {}
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  // Security check functions are now handled by securityMetricsService





  const getStatusIcon = (status: SecurityStatus['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: SecurityStatus['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getComponentIcon = (component: string) => {
    switch (component) {
      case 'Security Service':
        return <Shield className="h-4 w-4" />;
      case 'Environment Security':
        return <Key className="h-4 w-4" />;
      case 'Build Security':
        return <FileCheck className="h-4 w-4" />;
      case 'License Validation':
        return <Lock className="h-4 w-4" />;
      case 'Code Obfuscation':
        return <Eye className="h-4 w-4" />;
      case 'Docker Security':
        return <Container className="h-4 w-4" />;
      case 'AI Setup Security':
        return <Brain className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  if (isLoading && securityStatuses.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security Monitoring
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
            <span className="ml-2">Performing security checks...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Security Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security & IP Protection Status
              </CardTitle>
              <CardDescription>
                Real-time monitoring of EVEXA security implementation
              </CardDescription>
            </div>
            <Button 
              onClick={performSecurityCheck} 
              disabled={isLoading}
              variant="outline"
              size="sm"
            >
              {isLoading ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {metrics && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{metrics.passedChecks}</div>
                <div className="text-sm text-gray-600">Active</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{metrics.warningChecks}</div>
                <div className="text-sm text-gray-600">Warnings</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{metrics.failedChecks}</div>
                <div className="text-sm text-gray-600">Errors</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{Math.round((metrics.passedChecks / metrics.totalChecks) * 100)}%</div>
                <div className="text-sm text-gray-600">Security Score</div>
              </div>
            </div>
          )}

          <div className="space-y-4">
            {securityStatuses.map((status, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  {getComponentIcon(status.component)}
                  <div>
                    <div className="font-medium">{status.component}</div>
                    <div className="text-sm text-gray-600">{status.details}</div>
                    {status.metrics && (
                      <div className="text-xs text-gray-500 mt-1">
                        {Object.entries(status.metrics).slice(0, 3).map(([key, value]) => (
                          <span key={key} className="mr-3">
                            {key}: {typeof value === 'boolean' ? (value ? 'Yes' : 'No') : String(value)}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(status.status)}>
                    {status.status.charAt(0).toUpperCase() + status.status.slice(1)}
                  </Badge>
                  {getStatusIcon(status.status)}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-4 text-xs text-gray-500 text-center">
            Last updated: {lastRefresh.toLocaleString()}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
