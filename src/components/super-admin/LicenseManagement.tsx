"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from '@/hooks/use-toast';
import { realLicenseService, type LicenseCreationRequest, type RealLicenseInfo } from '@/services/realLicenseService';
import { Shield, Key, Users, Calendar, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

export default function LicenseManagement() {
  const [licenses, setLicenses] = useState<RealLicenseInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newLicense, setNewLicense] = useState<LicenseCreationRequest>({
    tenantId: '',
    tenantName: '',
    licenseType: 'trial',
    durationDays: 30,
    maxUsers: 5,
    maxTenants: 1,
    features: []
  });

  useEffect(() => {
    loadLicenses();
  }, []);

  const loadLicenses = async () => {
    setIsLoading(true);
    try {
      // In a real implementation, you'd fetch all licenses from Firebase
      // For now, we'll show the structure
      setLicenses([]);
    } catch (error) {
      console.error('Error loading licenses:', error);
      toast({
        title: "Error",
        description: "Failed to load licenses",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateLicense = async () => {
    if (!newLicense.tenantId || !newLicense.tenantName) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      const features = realLicenseService.getLicenseFeatures(newLicense.licenseType);
      const result = await realLicenseService.createLicense({
        ...newLicense,
        features
      });

      if (result.success) {
        toast({
          title: "License Created",
          description: `License key: ${result.licenseKey}`,
        });
        setShowCreateDialog(false);
        setNewLicense({
          tenantId: '',
          tenantName: '',
          licenseType: 'trial',
          durationDays: 30,
          maxUsers: 5,
          maxTenants: 1,
          features: []
        });
        loadLicenses();
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to create license",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error creating license:', error);
      toast({
        title: "Error",
        description: "Failed to create license",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getLicenseTypeConfig = (type: string) => {
    const configs = {
      trial: { maxUsers: 5, maxTenants: 1, duration: 30, color: 'bg-gray-500' },
      basic: { maxUsers: 25, maxTenants: 1, duration: 365, color: 'bg-blue-500' },
      professional: { maxUsers: 100, maxTenants: 3, duration: 365, color: 'bg-green-500' },
      enterprise: { maxUsers: 999, maxTenants: 10, duration: 365, color: 'bg-purple-500' },
      developer: { maxUsers: 10, maxTenants: 5, duration: 365, color: 'bg-orange-500' }
    };
    return configs[type as keyof typeof configs] || configs.trial;
  };

  const handleLicenseTypeChange = (type: string) => {
    const config = getLicenseTypeConfig(type);
    setNewLicense(prev => ({
      ...prev,
      licenseType: type as any,
      maxUsers: config.maxUsers,
      maxTenants: config.maxTenants,
      durationDays: config.duration
    }));
  };

  const getStatusBadge = (license: RealLicenseInfo) => {
    const now = new Date();
    const isExpired = license.expiryDate < now;
    const isExpiringSoon = (license.expiryDate.getTime() - now.getTime()) < (7 * 24 * 60 * 60 * 1000);

    if (!license.isActive) {
      return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Inactive</Badge>;
    }
    if (isExpired) {
      return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Expired</Badge>;
    }
    if (isExpiringSoon) {
      return <Badge variant="secondary"><AlertTriangle className="w-3 h-3 mr-1" />Expiring Soon</Badge>;
    }
    return <Badge variant="default"><CheckCircle className="w-3 h-3 mr-1" />Active</Badge>;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">License Management</h2>
          <p className="text-muted-foreground">Create and manage EVEXA licenses for tenants</p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Key className="w-4 h-4 mr-2" />
              Create License
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Create New License</DialogTitle>
              <DialogDescription>
                Generate a new license for a tenant
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="tenantId">Tenant ID</Label>
                <Input
                  id="tenantId"
                  value={newLicense.tenantId}
                  onChange={(e) => setNewLicense(prev => ({ ...prev, tenantId: e.target.value }))}
                  placeholder="tenant_123"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="tenantName">Tenant Name</Label>
                <Input
                  id="tenantName"
                  value={newLicense.tenantName}
                  onChange={(e) => setNewLicense(prev => ({ ...prev, tenantName: e.target.value }))}
                  placeholder="Company Name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="licenseType">License Type</Label>
                <Select value={newLicense.licenseType} onValueChange={handleLicenseTypeChange}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="trial">Trial (30 days, 5 users)</SelectItem>
                    <SelectItem value="basic">Basic (1 year, 25 users)</SelectItem>
                    <SelectItem value="professional">Professional (1 year, 100 users)</SelectItem>
                    <SelectItem value="enterprise">Enterprise (1 year, unlimited)</SelectItem>
                    <SelectItem value="developer">Developer (1 year, 10 users)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="maxUsers">Max Users</Label>
                  <Input
                    id="maxUsers"
                    type="number"
                    value={newLicense.maxUsers}
                    onChange={(e) => setNewLicense(prev => ({ ...prev, maxUsers: parseInt(e.target.value) }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="durationDays">Duration (Days)</Label>
                  <Input
                    id="durationDays"
                    type="number"
                    value={newLicense.durationDays}
                    onChange={(e) => setNewLicense(prev => ({ ...prev, durationDays: parseInt(e.target.value) }))}
                  />
                </div>
              </div>
              <Button onClick={handleCreateLicense} disabled={isLoading} className="w-full">
                {isLoading ? 'Creating...' : 'Create License'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="active" className="space-y-4">
        <TabsList>
          <TabsTrigger value="active">Active Licenses</TabsTrigger>
          <TabsTrigger value="expired">Expired</TabsTrigger>
          <TabsTrigger value="all">All Licenses</TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Active Licenses
              </CardTitle>
              <CardDescription>
                Currently active licenses across all tenants
              </CardDescription>
            </CardHeader>
            <CardContent>
              {licenses.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Key className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No licenses found</p>
                  <p className="text-sm">Create your first license to get started</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tenant</TableHead>
                      <TableHead>License Type</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Users</TableHead>
                      <TableHead>Expires</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {licenses.map((license) => (
                      <TableRow key={license.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{license.tenantName}</div>
                            <div className="text-sm text-muted-foreground">{license.tenantId}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getLicenseTypeConfig(license.licenseType).color}>
                            {license.licenseType.toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>{getStatusBadge(license)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Users className="w-4 h-4" />
                            {license.maxUsers === 999 ? '∞' : license.maxUsers}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            {license.expiryDate.toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Button variant="outline" size="sm">
                            View Details
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="expired">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8 text-muted-foreground">
                <XCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No expired licenses</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="all">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8 text-muted-foreground">
                <Key className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>All licenses will be displayed here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
