"use client";

import * as React from "react";
import { 
  I18nContext, 
  SupportedLanguage, 
  languages, 
  getStoredLanguage, 
  storeLanguage,
  formatDateWithLocale,
  formatTimeWithLocale,
  formatNumberWithLocale,
  formatCurrencyWithLocale,
  isRTLLanguage
} from "@/lib/i18n";

// Translation data (in a real app, this would be loaded from files)
const translations: Record<SupportedLanguage, any> = {
  en: {
    common: {
      save: "Save",
      cancel: "Cancel",
      delete: "Delete",
      edit: "Edit",
      add: "Add",
      remove: "Remove",
      search: "Search",
      filter: "Filter",
      sort: "Sort",
      loading: "Loading...",
      error: "Error",
      success: "Success",
      warning: "Warning",
      info: "Information",
      yes: "Yes",
      no: "No",
      ok: "OK",
      close: "Close",
      back: "Back",
      next: "Next",
      previous: "Previous",
      submit: "Submit",
      reset: "Reset",
      clear: "Clear",
      select: "Select",
      selectAll: "Select All",
      deselectAll: "Deselect All",
      required: "Required",
      optional: "Optional",
      name: "Name",
      description: "Description",
      date: "Date",
      time: "Time",
      email: "Email",
      phone: "Phone",
      address: "Address",
      country: "Country",
      currency: "Currency",
      language: "Language",
      theme: "Theme",
      settings: "Settings",
      profile: "Profile",
      logout: "Logout",
      login: "Login",
      register: "Register",
      forgotPassword: "Forgot Password",
      resetPassword: "Reset Password",
      changePassword: "Change Password",
      dashboard: "Dashboard",
      home: "Home",
      about: "About",
      contact: "Contact",
      help: "Help",
      support: "Support",
      documentation: "Documentation",
      terms: "Terms of Service",
      privacy: "Privacy Policy",
      cookies: "Cookie Policy",
    },
    navigation: {
      dashboard: "Dashboard",
      tasks: "Tasks",
      events: "Events",
      settings: "Settings",
      profile: "Profile",
      logout: "Logout",
    },
    forms: {
      validation: {
        required: "This field is required",
        email: "Please enter a valid email address",
        minLength: "Must be at least {min} characters",
        maxLength: "Must be no more than {max} characters",
        pattern: "Please enter a valid format",
      },
    },
  },
  es: {
    common: {
      save: "Guardar",
      cancel: "Cancelar",
      delete: "Eliminar",
      edit: "Editar",
      add: "Agregar",
      remove: "Quitar",
      search: "Buscar",
      filter: "Filtrar",
      sort: "Ordenar",
      loading: "Cargando...",
      error: "Error",
      success: "Éxito",
      warning: "Advertencia",
      info: "Información",
      yes: "Sí",
      no: "No",
      ok: "OK",
      close: "Cerrar",
      back: "Atrás",
      next: "Siguiente",
      previous: "Anterior",
      submit: "Enviar",
      reset: "Restablecer",
      clear: "Limpiar",
      select: "Seleccionar",
      selectAll: "Seleccionar Todo",
      deselectAll: "Deseleccionar Todo",
      required: "Requerido",
      optional: "Opcional",
      name: "Nombre",
      description: "Descripción",
      date: "Fecha",
      time: "Hora",
      email: "Correo Electrónico",
      phone: "Teléfono",
      address: "Dirección",
      country: "País",
      currency: "Moneda",
      language: "Idioma",
      theme: "Tema",
      settings: "Configuración",
      profile: "Perfil",
      logout: "Cerrar Sesión",
      login: "Iniciar Sesión",
      register: "Registrarse",
      forgotPassword: "Olvidé mi Contraseña",
      resetPassword: "Restablecer Contraseña",
      changePassword: "Cambiar Contraseña",
      dashboard: "Panel de Control",
      home: "Inicio",
      about: "Acerca de",
      contact: "Contacto",
      help: "Ayuda",
      support: "Soporte",
      documentation: "Documentación",
      terms: "Términos de Servicio",
      privacy: "Política de Privacidad",
      cookies: "Política de Cookies",
    },
    navigation: {
      dashboard: "Panel de Control",
      tasks: "Tareas",
      events: "Eventos",
      settings: "Configuración",
      profile: "Perfil",
      logout: "Cerrar Sesión",
    },
    forms: {
      validation: {
        required: "Este campo es requerido",
        email: "Por favor ingrese un email válido",
        minLength: "Debe tener al menos {min} caracteres",
        maxLength: "No debe tener más de {max} caracteres",
        pattern: "Por favor ingrese un formato válido",
      },
    },
  },
  // Add more languages as needed...
  fr: {
    common: {
      save: "Enregistrer",
      cancel: "Annuler",
      delete: "Supprimer",
      // ... more French translations
    },
  },
  de: {
    common: {
      save: "Speichern",
      cancel: "Abbrechen",
      delete: "Löschen",
      // ... more German translations
    },
  },
  ar: {
    common: {
      save: "حفظ",
      cancel: "إلغاء",
      delete: "حذف",
      // ... more Arabic translations
    },
  },
  zh: {
    common: {
      save: "保存",
      cancel: "取消",
      delete: "删除",
      // ... more Chinese translations
    },
  },
  ja: {
    common: {
      save: "保存",
      cancel: "キャンセル",
      delete: "削除",
      // ... more Japanese translations
    },
  },
  // Add other languages with basic translations
} as const;

interface I18nProviderProps {
  children: React.ReactNode;
  defaultLanguage?: SupportedLanguage;
}

export function I18nProvider({ children, defaultLanguage = 'en' }: I18nProviderProps) {
  const [language, setLanguageState] = React.useState<SupportedLanguage>(defaultLanguage);
  const [mounted, setMounted] = React.useState(false);

  const languageInfo = languages[language];
  const isRTL = isRTLLanguage(language);

  // Initialize language from storage
  React.useEffect(() => {
    const stored = getStoredLanguage();
    setLanguageState(stored);
    setMounted(true);
  }, []);

  // Apply RTL/LTR direction to document
  React.useEffect(() => {
    if (!mounted) return;
    
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = language;
    
    // Store language preference
    storeLanguage(language);
  }, [language, isRTL, mounted]);

  const setLanguage = React.useCallback((newLanguage: SupportedLanguage) => {
    setLanguageState(newLanguage);
  }, []);

  // Translation function with parameter interpolation
  const t = React.useCallback((key: string, params?: Record<string, string | number>): string => {
    const keys = key.split('.');
    let value: any = translations[language];
    
    // Navigate through nested keys
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // Fallback to English if translation not found
        value = translations.en;
        for (const fallbackKey of keys) {
          if (value && typeof value === 'object' && fallbackKey in value) {
            value = value[fallbackKey];
          } else {
            return key; // Return key if no translation found
          }
        }
        break;
      }
    }
    
    if (typeof value !== 'string') {
      return key;
    }
    
    // Replace parameters
    if (params) {
      return value.replace(/\{(\w+)\}/g, (match, paramKey) => {
        return params[paramKey]?.toString() || match;
      });
    }
    
    return value;
  }, [language]);

  const formatDate = React.useCallback((date: Date, format?: string): string => {
    return formatDateWithLocale(date, language, format);
  }, [language]);

  const formatTime = React.useCallback((date: Date, format?: string): string => {
    return formatTimeWithLocale(date, language, format);
  }, [language]);

  const formatNumber = React.useCallback((number: number, options?: Intl.NumberFormatOptions): string => {
    return formatNumberWithLocale(number, language, options);
  }, [language]);

  const formatCurrency = React.useCallback((amount: number, currency?: string): string => {
    return formatCurrencyWithLocale(amount, language, currency);
  }, [language]);

  const value = React.useMemo(() => ({
    language,
    languageInfo,
    setLanguage,
    t,
    formatDate,
    formatTime,
    formatNumber,
    formatCurrency,
    isRTL,
  }), [language, languageInfo, setLanguage, t, formatDate, formatTime, formatNumber, formatCurrency, isRTL]);

  // Prevent flash of unstyled content
  if (!mounted) {
    return null;
  }

  return (
    <I18nContext.Provider value={value}>
      {children}
    </I18nContext.Provider>
  );
}

// Language selector component
interface LanguageSelectorProps {
  className?: string;
  variant?: 'dropdown' | 'list';
  showFlags?: boolean;
  showNativeNames?: boolean;
}

export function LanguageSelector({
  className,
  variant = 'dropdown',
  showFlags = true,
  showNativeNames = true
}: LanguageSelectorProps) {
  const context = React.useContext(I18nContext);

  if (!context) {
    console.warn('LanguageSelector must be used within an I18nProvider');
    return null;
  }

  const { language, setLanguage, t } = context;
  
  const popularLanguages: SupportedLanguage[] = ['en', 'es', 'fr', 'de', 'zh', 'ja', 'ar'];
  const otherLanguages = Object.keys(languages).filter(
    lang => !popularLanguages.includes(lang as SupportedLanguage)
  ) as SupportedLanguage[];

  if (variant === 'list') {
    return (
      <div className={`space-y-2 ${className}`}>
        <label className="text-sm font-medium">{t('common.language')}</label>
        
        {/* Popular Languages */}
        <div className="space-y-1">
          <div className="text-xs font-medium text-muted-foreground">Popular</div>
          {popularLanguages.map((lang) => {
            const langInfo = languages[lang];
            return (
              <button
                key={lang}
                onClick={() => setLanguage(lang)}
                className={`w-full flex items-center gap-3 px-3 py-2 text-left rounded-md transition-colors hover:bg-accent ${
                  language === lang ? 'bg-accent text-accent-foreground' : ''
                }`}
              >
                {showFlags && <span className="text-lg">{langInfo.flag}</span>}
                <div className="flex-1">
                  <div className="font-medium">{langInfo.name}</div>
                  {showNativeNames && langInfo.nativeName !== langInfo.name && (
                    <div className="text-xs text-muted-foreground">{langInfo.nativeName}</div>
                  )}
                </div>
                {language === lang && (
                  <div className="h-2 w-2 rounded-full bg-primary" />
                )}
              </button>
            );
          })}
        </div>

        {/* Other Languages */}
        {otherLanguages.length > 0 && (
          <div className="space-y-1">
            <div className="text-xs font-medium text-muted-foreground">Other Languages</div>
            {otherLanguages.map((lang) => {
              const langInfo = languages[lang];
              return (
                <button
                  key={lang}
                  onClick={() => setLanguage(lang)}
                  className={`w-full flex items-center gap-3 px-3 py-2 text-left rounded-md transition-colors hover:bg-accent ${
                    language === lang ? 'bg-accent text-accent-foreground' : ''
                  }`}
                >
                  {showFlags && <span className="text-lg">{langInfo.flag}</span>}
                  <div className="flex-1">
                    <div className="font-medium">{langInfo.name}</div>
                    {showNativeNames && langInfo.nativeName !== langInfo.name && (
                      <div className="text-xs text-muted-foreground">{langInfo.nativeName}</div>
                    )}
                  </div>
                  {language === lang && (
                    <div className="h-2 w-2 rounded-full bg-primary" />
                  )}
                </button>
              );
            })}
          </div>
        )}
      </div>
    );
  }

  // Dropdown variant would be implemented here
  return (
    <div className={className}>
      <select
        value={language}
        onChange={(e) => setLanguage(e.target.value as SupportedLanguage)}
        className="w-full px-3 py-2 border border-input rounded-md bg-background"
      >
        <optgroup label="Popular">
          {popularLanguages.map((lang) => {
            const langInfo = languages[lang];
            return (
              <option key={lang} value={lang}>
                {showFlags ? `${langInfo.flag} ` : ''}
                {langInfo.name}
                {showNativeNames && langInfo.nativeName !== langInfo.name ? ` (${langInfo.nativeName})` : ''}
              </option>
            );
          })}
        </optgroup>
        {otherLanguages.length > 0 && (
          <optgroup label="Other Languages">
            {otherLanguages.map((lang) => {
              const langInfo = languages[lang];
              return (
                <option key={lang} value={lang}>
                  {showFlags ? `${langInfo.flag} ` : ''}
                  {langInfo.name}
                  {showNativeNames && langInfo.nativeName !== langInfo.name ? ` (${langInfo.nativeName})` : ''}
                </option>
              );
            })}
          </optgroup>
        )}
      </select>
    </div>
  );
}
