/**
 * User Role Management Interface
 * Comprehensive interface for managing user roles and persona assignments
 */

import React, { useState, useEffect, useMemo } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { usePersonaManagement } from '@/hooks/usePersonaPermissions';
import { createTenantPersonaService } from '@/services/tenantPersonaService';
import { 
  TenantPersona, 
  PersonaAssignment, 
  PERSONA_CATEGORIES,
  MODULE_METADATA 
} from '@/types/personas';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Users,
  UserPlus,
  Edit,
  Trash2,
  Shield,
  Search,
  Filter,
  Download,
  Upload,
  Settings,
  Eye,
  AlertTriangle,
  Copy
} from 'lucide-react';

// ===== TYPES =====

interface UserProfile {
  id: string;
  email: string;
  displayName: string;
  role: string;
  status: 'active' | 'inactive' | 'pending';
  lastLogin?: Date;
  createdAt: Date;
  assignedPersona?: TenantPersona;
}

// ===== MAIN COMPONENT =====

export const UserRoleManagement: React.FC = () => {
  const { tenantId, user } = useAuth();
  const {
    personas,
    isLoading: personasLoading,
    canManagePersonas,
    assignPersonaToUser,
    createPersona,
    updatePersona,
    deletePersona
  } = usePersonaManagement();

  const [users, setUsers] = useState<UserProfile[]>([]);
  const [assignments, setAssignments] = useState<PersonaAssignment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPersonaFilter, setSelectedPersonaFilter] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);

  // Load users and assignments
  useEffect(() => {
    if (!tenantId || !canManagePersonas) return;

    const loadData = async () => {
      try {
        setIsLoading(true);
        const personaService = createTenantPersonaService(tenantId);
        
        // Load users (this would typically come from a user service)
        // For now, we'll simulate this
        const mockUsers: UserProfile[] = [
          {
            id: 'user1',
            email: '<EMAIL>',
            displayName: 'Admin User',
            role: 'admin',
            status: 'active',
            lastLogin: new Date(),
            createdAt: new Date()
          },
          {
            id: 'user2',
            email: '<EMAIL>',
            displayName: 'Exhibition Manager',
            role: 'manager',
            status: 'active',
            lastLogin: new Date(),
            createdAt: new Date()
          }
        ];

        // Load persona assignments
        const allAssignments: PersonaAssignment[] = [];
        for (const persona of personas) {
          const personaUsers = await personaService.getPersonaUsers(persona.id);
          allAssignments.push(...personaUsers);
        }

        // Merge user data with persona assignments
        const usersWithPersonas = mockUsers.map(user => {
          const assignment = allAssignments.find(a => a.userId === user.id);
          const assignedPersona = assignment ? personas.find(p => p.id === assignment.personaId) : undefined;
          
          return {
            ...user,
            assignedPersona
          };
        });

        setUsers(usersWithPersonas);
        setAssignments(allAssignments);
      } catch (err) {
        console.error('Error loading user role data:', err);
        setError('Failed to load user role data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [tenantId, canManagePersonas, personas]);

  // Filter users based on search and persona filter
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesPersonaFilter = selectedPersonaFilter === 'all' ||
                                user.assignedPersona?.id === selectedPersonaFilter ||
                                (selectedPersonaFilter === 'unassigned' && !user.assignedPersona);
    
    return matchesSearch && matchesPersonaFilter;
  });

  // Handle persona assignment
  const handleAssignPersona = async (userId: string, personaId: string) => {
    try {
      await assignPersonaToUser(userId, personaId, user?.uid || 'system');
      
      // Update local state
      setUsers(prev => prev.map(u => 
        u.id === userId 
          ? { ...u, assignedPersona: personas.find(p => p.id === personaId) }
          : u
      ));
    } catch (err) {
      console.error('Error assigning persona:', err);
      setError('Failed to assign persona');
    }
  };

  if (!canManagePersonas) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          You do not have permission to manage user roles and personas.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">User Role Management</h1>
          <p className="text-gray-600 mt-2">
            Manage user personas and permissions across your organization
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button size="sm">
            <UserPlus className="h-4 w-4 mr-2" />
            Invite User
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="users" className="space-y-4">
        <TabsList>
          <TabsTrigger value="users">Users & Assignments</TabsTrigger>
          <TabsTrigger value="personas">Persona Management</TabsTrigger>
          <TabsTrigger value="analytics">Role Analytics</TabsTrigger>
        </TabsList>

        {/* Users & Assignments Tab */}
        <TabsContent value="users" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex gap-4 items-center">
                <div className="flex-1">
                  <Label htmlFor="search">Search Users</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="search"
                      placeholder="Search by name or email..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <div className="w-48">
                  <Label htmlFor="persona-filter">Filter by Persona</Label>
                  <Select value={selectedPersonaFilter} onValueChange={setSelectedPersonaFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All personas" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Personas</SelectItem>
                      <SelectItem value="unassigned">Unassigned</SelectItem>
                      {personas.map(persona => (
                        <SelectItem key={persona.id} value={persona.id}>
                          {persona.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Users Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Users ({filteredUsers.length})
              </CardTitle>
              <CardDescription>
                Manage user persona assignments and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center py-8">Loading users...</div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Current Persona</TableHead>
                      <TableHead>Last Login</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.map(user => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{user.displayName}</div>
                            <div className="text-sm text-gray-500">{user.email}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge 
                            variant={user.status === 'active' ? 'default' : 'secondary'}
                          >
                            {user.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {user.assignedPersona ? (
                            <Badge variant="outline">
                              {user.assignedPersona.name}
                            </Badge>
                          ) : (
                            <span className="text-gray-400">Unassigned</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {user.lastLogin ? (
                            <span className="text-sm">
                              {user.lastLogin.toLocaleDateString()}
                            </span>
                          ) : (
                            <span className="text-gray-400">Never</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <PersonaAssignmentDialog
                              user={user}
                              personas={personas}
                              onAssign={handleAssignPersona}
                            />
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Settings className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Persona Management Tab */}
        <TabsContent value="personas" className="space-y-4">
          <PersonaManagementSection 
            personas={personas}
            isLoading={personasLoading}
            onCreatePersona={createPersona}
            onUpdatePersona={updatePersona}
            onDeletePersona={deletePersona}
          />
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <RoleAnalyticsSection 
            users={users}
            personas={personas}
            assignments={assignments}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

// ===== PERSONA ASSIGNMENT DIALOG =====

interface PersonaAssignmentDialogProps {
  user: UserProfile;
  personas: TenantPersona[];
  onAssign: (userId: string, personaId: string) => Promise<void>;
}

const PersonaAssignmentDialog: React.FC<PersonaAssignmentDialogProps> = ({
  user,
  personas,
  onAssign
}) => {
  const [selectedPersonaId, setSelectedPersonaId] = useState<string>('');
  const [isAssigning, setIsAssigning] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const handleAssign = async () => {
    if (!selectedPersonaId) return;

    try {
      setIsAssigning(true);
      await onAssign(user.id, selectedPersonaId);
      setIsOpen(false);
      setSelectedPersonaId('');
    } catch (error) {
      console.error('Error assigning persona:', error);
    } finally {
      setIsAssigning(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm">
          <Shield className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Assign Persona</DialogTitle>
          <DialogDescription>
            Assign a persona to {user.displayName} ({user.email})
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="persona-select">Select Persona</Label>
            <Select value={selectedPersonaId} onValueChange={setSelectedPersonaId}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a persona..." />
              </SelectTrigger>
              <SelectContent>
                {personas.map(persona => (
                  <SelectItem key={persona.id} value={persona.id}>
                    <div>
                      <div className="font-medium">{persona.name}</div>
                      <div className="text-sm text-gray-500">{persona.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedPersonaId && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">Persona Details</h4>
              {(() => {
                const persona = personas.find(p => p.id === selectedPersonaId);
                if (!persona) return null;
                
                return (
                  <div className="space-y-2 text-sm">
                    <p><strong>Description:</strong> {persona.description}</p>
                    <p><strong>Modules:</strong> {persona.permissions.modules.length} modules</p>
                    <p><strong>Category:</strong> {persona.category}</p>
                  </div>
                );
              })()}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleAssign} 
            disabled={!selectedPersonaId || isAssigning}
          >
            {isAssigning ? 'Assigning...' : 'Assign Persona'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// ===== PLACEHOLDER COMPONENTS =====

const PersonaManagementSection: React.FC<{
  personas: TenantPersona[];
  isLoading: boolean;
  onCreatePersona: any;
  onUpdatePersona: any;
  onDeletePersona: any;
}> = ({ personas, isLoading, onCreatePersona, onUpdatePersona, onDeletePersona }) => {
  const [selectedPersona, setSelectedPersona] = useState<TenantPersona | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  const handleDuplicatePersona = (persona: TenantPersona) => {
    const duplicatedPersona = {
      ...persona,
      name: `${persona.name} (Copy)`,
      id: '',
      category: 'custom' as const,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    setSelectedPersona(duplicatedPersona);
  };

  const defaultPersonas = personas.filter(p => p.category === 'default');
  const customPersonas = personas.filter(p => p.category === 'custom');

  return (
    <div className="space-y-6">
      {/* Default Personas */}
      <Card>
        <CardHeader>
          <CardTitle>Default Personas</CardTitle>
          <CardDescription>
            Built-in personas with predefined permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {defaultPersonas.map(persona => (
              <PersonaCard
                key={persona.id}
                persona={persona}
                onEdit={() => setSelectedPersona(persona)}
                onDelete={onDeletePersona}
                onDuplicate={handleDuplicatePersona}
                isDefault={true}
              />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Custom Personas */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Custom Personas</CardTitle>
            <CardDescription>
              Organization-specific personas with custom permissions
            </CardDescription>
          </div>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <UserPlus className="h-4 w-4 mr-2" />
            Create Persona
          </Button>
        </CardHeader>
        <CardContent>
          {customPersonas.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {customPersonas.map(persona => (
                <PersonaCard
                  key={persona.id}
                  persona={persona}
                  onEdit={() => setSelectedPersona(persona)}
                  onDelete={onDeletePersona}
                  onDuplicate={handleDuplicatePersona}
                  isDefault={false}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No custom personas created yet. Create your first custom persona to get started.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create/Edit Persona Dialog */}
      <PersonaEditorDialog
        persona={selectedPersona}
        isOpen={!!selectedPersona || isCreateDialogOpen}
        onClose={() => {
          setSelectedPersona(null);
          setIsCreateDialogOpen(false);
        }}
        onSave={async (personaData) => {
          if (selectedPersona && selectedPersona.id) {
            await onUpdatePersona(selectedPersona.id, personaData);
          } else {
            await onCreatePersona(personaData);
          }
        }}
      />
    </div>
  );
};

const RoleAnalyticsSection: React.FC<{
  users: UserProfile[];
  personas: TenantPersona[];
  assignments: PersonaAssignment[];
}> = ({ users, personas, assignments }) => (
  <Card>
    <CardHeader>
      <CardTitle>Role Analytics</CardTitle>
      <CardDescription>Insights into role distribution and usage</CardDescription>
    </CardHeader>
    <CardContent>
      <div className="text-center py-8 text-gray-500">
        Role analytics dashboard coming soon...
      </div>
    </CardContent>
  </Card>
);

// ===== PERSONA CARD COMPONENT =====

interface PersonaCardProps {
  persona: TenantPersona;
  onEdit: () => void;
  onDelete: (personaId: string) => Promise<void>;
  onDuplicate?: (persona: TenantPersona) => void;
  isDefault: boolean;
}

const PersonaCard: React.FC<PersonaCardProps> = ({
  persona,
  onEdit,
  onDelete,
  onDuplicate,
  isDefault
}) => {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete the persona "${persona.name}"?`)) {
      return;
    }

    try {
      setIsDeleting(true);
      await onDelete(persona.id);
    } catch (error) {
      console.error('Error deleting persona:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const moduleCount = persona.permissions.modules.length;
  const systemPermCount = Object.values(persona.permissions.systemPermissions)
    .filter(Boolean).length;

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg">{persona.name}</CardTitle>
            <div className="flex gap-2 mt-1">
              <Badge variant={isDefault ? 'default' : 'secondary'}>
                {isDefault ? 'Default' : 'Custom'}
              </Badge>
              {!persona.isActive && (
                <Badge variant="destructive">Inactive</Badge>
              )}
            </div>
          </div>
          <div className="flex gap-1">
            <Button variant="ghost" size="sm" onClick={onEdit} title="View Details">
              <Eye className="h-4 w-4" />
            </Button>
            {onDuplicate && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDuplicate(persona)}
                title="Duplicate Persona"
              >
                <Copy className="h-4 w-4" />
              </Button>
            )}
            {!isDefault && (
              <>
                <Button variant="ghost" size="sm" onClick={onEdit} title="Edit Persona">
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDelete}
                  disabled={isDeleting}
                  title="Delete Persona"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
          {persona.description}
        </p>

        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-500">Modules:</span>
            <span className="font-medium">{moduleCount}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">System Permissions:</span>
            <span className="font-medium">{systemPermCount}</span>
          </div>
          {persona.targetUsers && (
            <div className="mt-3">
              <span className="text-gray-500 text-xs">Target Users:</span>
              <p className="text-xs text-gray-600 mt-1">
                {persona.targetUsers.slice(0, 2).join(', ')}
                {persona.targetUsers.length > 2 && ` +${persona.targetUsers.length - 2} more`}
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// ===== PERSONA EDITOR DIALOG =====

interface PersonaEditorDialogProps {
  persona: TenantPersona | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (personaData: any) => Promise<void>;
}

const PersonaEditorDialog: React.FC<PersonaEditorDialogProps> = ({
  persona,
  isOpen,
  onClose,
  onSave
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isActive: true,
    targetUsers: [] as string[],
    permissions: {
      modules: [] as any[],
      systemPermissions: {
        canManageUsers: false,
        canManageSettings: false,
        canViewAnalytics: false,
        canExportData: false,
        canManageIntegrations: false,
        canAccessSupport: false
      }
    }
  });
  const [isSaving, setIsSaving] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Initialize form data when persona changes
  useEffect(() => {
    if (persona) {
      setFormData({
        name: persona.name,
        description: persona.description,
        isActive: persona.isActive,
        targetUsers: persona.targetUsers || [],
        permissions: persona.permissions
      });
    } else {
      // Reset for new persona
      setFormData({
        name: '',
        description: '',
        isActive: true,
        targetUsers: [],
        permissions: {
          modules: [],
          systemPermissions: {
            canManageUsers: false,
            canManageSettings: false,
            canViewAnalytics: false,
            canExportData: false,
            canManageIntegrations: false,
            canAccessSupport: false
          }
        }
      });
    }
  }, [persona]);

  // Validation function
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Persona name is required';
    }

    if (!formData.description.trim()) {
      errors.description = 'Description is required';
    }

    if (formData.permissions.modules.length === 0) {
      errors.modules = 'At least one module permission is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsSaving(true);
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('Error saving persona:', error);
      setValidationErrors({ general: 'Failed to save persona. Please try again.' });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {persona ? `Edit ${persona.name}` : 'Create New Persona'}
          </DialogTitle>
          <DialogDescription>
            {persona ? 'Modify the persona settings and permissions.' : 'Create a new custom persona with specific permissions.'}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Persona Template Selector (for new personas) */}
          {!persona && (
            <PersonaTemplateSelector
              onTemplateSelect={(template) => {
                setFormData({
                  name: template.name,
                  description: template.description,
                  isActive: true,
                  targetUsers: template.targetUsers || [],
                  permissions: template.permissions
                });
              }}
            />
          )}

          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="persona-name">Name</Label>
                <Input
                  id="persona-name"
                  value={formData.name}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, name: e.target.value }));
                    if (validationErrors.name) {
                      setValidationErrors(prev => ({ ...prev, name: '' }));
                    }
                  }}
                  placeholder="Enter persona name..."
                  className={validationErrors.name ? 'border-red-500' : ''}
                />
                {validationErrors.name && (
                  <p className="text-sm text-red-600 mt-1">{validationErrors.name}</p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="persona-active"
                  checked={formData.isActive}
                  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                />
                <Label htmlFor="persona-active">Active</Label>
              </div>
            </div>

            <div>
              <Label htmlFor="persona-description">Description</Label>
              <textarea
                id="persona-description"
                className={`w-full p-2 border rounded-md ${validationErrors.description ? 'border-red-500' : ''}`}
                rows={3}
                value={formData.description}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, description: e.target.value }));
                  if (validationErrors.description) {
                    setValidationErrors(prev => ({ ...prev, description: '' }));
                  }
                }}
                placeholder="Describe the persona and its intended use..."
              />
              {validationErrors.description && (
                <p className="text-sm text-red-600 mt-1">{validationErrors.description}</p>
              )}
            </div>

            {/* Target Users */}
            <div>
              <Label htmlFor="target-users">Target Users</Label>
              <TargetUsersEditor
                targetUsers={formData.targetUsers}
                onChange={(targetUsers) => setFormData(prev => ({ ...prev, targetUsers }))}
              />
            </div>
          </div>

          {/* System Permissions */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">System Permissions</h3>
            <div className="grid grid-cols-2 gap-4">
              {Object.entries(formData.permissions.systemPermissions).map(([key, value]) => (
                <div key={key} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id={`system-${key}`}
                    checked={value}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      permissions: {
                        ...prev.permissions,
                        systemPermissions: {
                          ...prev.permissions.systemPermissions,
                          [key]: e.target.checked
                        }
                      }
                    }))}
                  />
                  <Label htmlFor={`system-${key}`}>
                    {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Module Permissions */}
          <div className="space-y-2">
            <ModulePermissionsEditor
              permissions={formData.permissions.modules}
              onChange={(modules) => {
                setFormData(prev => ({
                  ...prev,
                  permissions: {
                    ...prev.permissions,
                    modules
                  }
                }));
                if (validationErrors.modules) {
                  setValidationErrors(prev => ({ ...prev, modules: '' }));
                }
              }}
            />
            {validationErrors.modules && (
              <p className="text-sm text-red-600">{validationErrors.modules}</p>
            )}
          </div>
        </div>

        {/* General Error Display */}
        {validationErrors.general && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{validationErrors.general}</p>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || !formData.name.trim()}
            className={isSaving ? 'opacity-50' : ''}
          >
            {isSaving ? 'Saving...' : persona ? 'Update Persona' : 'Create Persona'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// ===== TARGET USERS EDITOR =====

interface TargetUsersEditorProps {
  targetUsers: string[];
  onChange: (targetUsers: string[]) => void;
}

const TargetUsersEditor: React.FC<TargetUsersEditorProps> = ({
  targetUsers,
  onChange
}) => {
  const [newUser, setNewUser] = useState('');

  const addUser = () => {
    if (newUser.trim() && !targetUsers.includes(newUser.trim())) {
      onChange([...targetUsers, newUser.trim()]);
      setNewUser('');
    }
  };

  const removeUser = (index: number) => {
    onChange(targetUsers.filter((_, i) => i !== index));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addUser();
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex space-x-2">
        <Input
          value={newUser}
          onChange={(e) => setNewUser(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Add target user type (e.g., Exhibition Manager)"
          className="flex-1"
        />
        <Button type="button" onClick={addUser} size="sm">
          Add
        </Button>
      </div>

      {targetUsers.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {targetUsers.map((user, index) => (
            <Badge key={index} variant="secondary" className="flex items-center space-x-1">
              <span>{user}</span>
              <button
                type="button"
                onClick={() => removeUser(index)}
                className="ml-1 text-gray-500 hover:text-red-500"
              >
                ×
              </button>
            </Badge>
          ))}
        </div>
      )}

      <p className="text-xs text-gray-600">
        Specify the types of users this persona is designed for
      </p>
    </div>
  );
};

// ===== PERSONA TEMPLATE SELECTOR =====

interface PersonaTemplateSelectorProps {
  onTemplateSelect: (template: any) => void;
}

const PersonaTemplateSelector: React.FC<PersonaTemplateSelectorProps> = ({
  onTemplateSelect
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');

  const templates = [
    {
      id: 'blank',
      name: 'Blank Persona',
      description: 'Start with no permissions and configure manually',
      permissions: {
        modules: [],
        systemPermissions: {
          canManageUsers: false,
          canManageSettings: false,
          canViewAnalytics: false,
          canExportData: false,
          canManageIntegrations: false,
          canAccessSupport: false
        }
      },
      targetUsers: []
    },
    {
      id: 'exhibition_focused',
      name: 'Exhibition Focused',
      description: 'Core exhibition management with basic permissions',
      permissions: {
        modules: [
          { module: 'exhibitions', actions: ['read', 'write'], description: 'Manage exhibitions' },
          { module: 'events', actions: ['read', 'write'], description: 'Manage events' },
          { module: 'tasks', actions: ['read', 'write'], description: 'Manage tasks' },
          { module: 'dashboards', actions: ['read'], description: 'View dashboards' }
        ],
        systemPermissions: {
          canManageUsers: false,
          canManageSettings: false,
          canViewAnalytics: true,
          canExportData: true,
          canManageIntegrations: false,
          canAccessSupport: true
        }
      },
      targetUsers: ['Exhibition coordinators', 'Event managers']
    },
    {
      id: 'marketing_focused',
      name: 'Marketing Focused',
      description: 'Marketing and lead management with social media access',
      permissions: {
        modules: [
          { module: 'leads', actions: ['read', 'write'], description: 'Manage leads' },
          { module: 'contacts', actions: ['read', 'write'], description: 'Manage contacts' },
          { module: 'social_media', actions: ['read', 'write'], description: 'Manage social media' },
          { module: 'marketing', actions: ['read', 'write'], description: 'Marketing campaigns' },
          { module: 'analytics', actions: ['read'], description: 'View analytics' }
        ],
        systemPermissions: {
          canManageUsers: false,
          canManageSettings: false,
          canViewAnalytics: true,
          canExportData: true,
          canManageIntegrations: false,
          canAccessSupport: true
        }
      },
      targetUsers: ['Marketing specialists', 'Social media managers']
    },
    {
      id: 'financial_focused',
      name: 'Financial Focused',
      description: 'Budget and expense management with financial reporting',
      permissions: {
        modules: [
          { module: 'budgets', actions: ['read', 'write'], description: 'Manage budgets' },
          { module: 'expenses', actions: ['read', 'write'], description: 'Manage expenses' },
          { module: 'purchase_orders', actions: ['read', 'write'], description: 'Purchase orders' },
          { module: 'financial_reports', actions: ['read'], description: 'Financial reports' },
          { module: 'vendors', actions: ['read'], description: 'View vendors' }
        ],
        systemPermissions: {
          canManageUsers: false,
          canManageSettings: false,
          canViewAnalytics: true,
          canExportData: true,
          canManageIntegrations: false,
          canAccessSupport: true
        }
      },
      targetUsers: ['Financial controllers', 'Budget managers']
    }
  ];

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(templateId);
      onTemplateSelect(template);
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Start with Template</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {templates.map(template => (
          <Card
            key={template.id}
            className={`p-4 cursor-pointer transition-colors ${
              selectedTemplate === template.id ? 'border-blue-500 bg-blue-50' : 'hover:bg-gray-50'
            }`}
            onClick={() => handleTemplateSelect(template.id)}
          >
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  checked={selectedTemplate === template.id}
                  onChange={() => handleTemplateSelect(template.id)}
                  className="w-4 h-4"
                />
                <h4 className="font-medium">{template.name}</h4>
              </div>
              <p className="text-sm text-gray-600">{template.description}</p>
              <div className="text-xs text-gray-500">
                {template.permissions.modules.length} modules configured
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

// ===== MODULE PERMISSIONS EDITOR =====

interface ModulePermissionsEditorProps {
  permissions: any[];
  onChange: (permissions: any[]) => void;
}

const ModulePermissionsEditor: React.FC<ModulePermissionsEditorProps> = ({
  permissions,
  onChange
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Group modules by category
  const modulesByCategory = useMemo(() => {
    const grouped: Record<string, any[]> = {};

    Object.entries(MODULE_METADATA).forEach(([moduleId, metadata]) => {
      if (!grouped[metadata.category]) {
        grouped[metadata.category] = [];
      }
      grouped[metadata.category].push({
        id: moduleId,
        ...metadata
      });
    });

    return grouped;
  }, []);

  // Get current permission for a module
  const getModulePermission = (moduleId: string) => {
    return permissions.find(p => p.module === moduleId);
  };

  // Update module permission
  const updateModulePermission = (moduleId: string, actions: string[], description: string) => {
    const updatedPermissions = permissions.filter(p => p.module !== moduleId);

    if (actions.length > 0) {
      updatedPermissions.push({
        module: moduleId,
        actions,
        description
      });
    }

    onChange(updatedPermissions);
  };

  // Toggle permission action for a module
  const togglePermissionAction = (moduleId: string, action: string) => {
    const currentPermission = getModulePermission(moduleId);
    const currentActions = currentPermission?.actions || [];

    let newActions: string[];
    if (currentActions.includes(action)) {
      newActions = currentActions.filter(a => a !== action);
    } else {
      newActions = [...currentActions, action];
    }

    const metadata = MODULE_METADATA[moduleId as keyof typeof MODULE_METADATA];
    updateModulePermission(
      moduleId,
      newActions,
      `${newActions.join(', ')} access to ${metadata?.name || moduleId}`
    );
  };

  // Set preset permission level for a module
  const setModulePreset = (moduleId: string, preset: 'none' | 'read' | 'write' | 'admin') => {
    const metadata = MODULE_METADATA[moduleId as keyof typeof MODULE_METADATA];

    const presetActions: Record<string, string[]> = {
      none: [],
      read: ['read'],
      write: ['read', 'write'],
      admin: ['read', 'write', 'delete', 'admin']
    };

    updateModulePermission(
      moduleId,
      presetActions[preset],
      `${preset} access to ${metadata?.name || moduleId}`
    );
  };

  const categories = ['all', ...Object.keys(modulesByCategory)];
  const filteredModules = selectedCategory === 'all'
    ? Object.values(modulesByCategory).flat()
    : modulesByCategory[selectedCategory] || [];

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Module Permissions</h3>
        <div className="text-sm text-gray-600">
          {permissions.length} modules configured
        </div>
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2">
        {categories.map(category => (
          <Button
            key={category}
            variant={selectedCategory === category ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(category)}
            className="capitalize"
          >
            {category}
          </Button>
        ))}
      </div>

      {/* Modules Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
        {filteredModules.map(module => {
          const currentPermission = getModulePermission(module.id);
          const hasAnyAccess = currentPermission && currentPermission.actions.length > 0;

          return (
            <Card key={module.id} className={`p-4 ${hasAnyAccess ? 'border-blue-200 bg-blue-50' : ''}`}>
              <div className="space-y-3">
                {/* Module Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full bg-${module.color}-500`} />
                    <div>
                      <h4 className="font-medium text-sm">{module.name}</h4>
                      <p className="text-xs text-gray-600">{module.description}</p>
                    </div>
                  </div>
                  {module.isPremium && (
                    <Badge variant="secondary" className="text-xs">Premium</Badge>
                  )}
                </div>

                {/* Permission Presets */}
                <div className="flex space-x-1">
                  {['none', 'read', 'write', 'admin'].map(preset => (
                    <Button
                      key={preset}
                      variant="outline"
                      size="sm"
                      className="text-xs px-2 py-1"
                      onClick={() => setModulePreset(module.id, preset as any)}
                    >
                      {preset}
                    </Button>
                  ))}
                </div>

                {/* Individual Actions */}
                <div className="flex flex-wrap gap-2">
                  {['read', 'write', 'delete', 'admin'].map(action => (
                    <label key={action} className="flex items-center space-x-1 text-xs">
                      <input
                        type="checkbox"
                        checked={currentPermission?.actions.includes(action) || false}
                        onChange={() => togglePermissionAction(module.id, action)}
                        className="w-3 h-3"
                      />
                      <span className="capitalize">{action}</span>
                    </label>
                  ))}
                </div>
              </div>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default UserRoleManagement;
