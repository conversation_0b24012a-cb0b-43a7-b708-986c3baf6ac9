/**
 * Performance Testing Dashboard
 * Comprehensive performance testing interface for query optimization and scalability validation
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Zap, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Download,
  Database,
  Timer,
  BarChart3,
  Activity,
  Gauge,
  Target,
  Layers,
  Network
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import { performanceTestingService, type PerformanceTestResult, type ScalabilityTestResult } from '@/services/performanceTestingService';
import { indexingStrategyTestService, performanceBenchmarkService, type IndexTestResult } from '@/services/indexingStrategyTestService';

interface PerformanceMetrics {
  overallScore: number;
  queryPerformance: number;
  scalabilityScore: number;
  indexEfficiency: number;
  lastTestDate: Date;
  totalTests: number;
  passedTests: number;
  failedTests: number;
}

export default function PerformanceTestingDashboard() {
  const { user, tenant } = useAuth();
  const { toast } = useToast();
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [performanceResults, setPerformanceResults] = useState<PerformanceTestResult[]>([]);
  const [scalabilityResults, setScalabilityResults] = useState<ScalabilityTestResult[]>([]);
  const [indexResults, setIndexResults] = useState<IndexTestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [testProgress, setTestProgress] = useState(0);
  const [currentTest, setCurrentTest] = useState('');

  useEffect(() => {
    loadPerformanceData();
  }, []);

  const loadPerformanceData = async () => {
    try {
      // Load existing performance metrics (would come from database in real implementation)
      const mockMetrics: PerformanceMetrics = {
        overallScore: 87,
        queryPerformance: 92,
        scalabilityScore: 85,
        indexEfficiency: 84,
        lastTestDate: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        totalTests: 24,
        passedTests: 21,
        failedTests: 3
      };

      setMetrics(mockMetrics);
    } catch (error) {
      toast({
        title: "Error Loading Performance Data",
        description: "Failed to load performance metrics.",
        variant: "destructive",
      });
    }
  };

  const runPerformanceTests = async () => {
    if (!tenant?.id) {
      toast({
        title: "Authentication Required",
        description: "Please ensure you're logged in with a valid tenant.",
        variant: "destructive",
      });
      return;
    }

    setIsRunningTests(true);
    setTestProgress(0);

    try {
      // Run performance tests
      setCurrentTest('Running query performance tests...');
      setTestProgress(20);
      const perfResults = await performanceTestingService.runAllPerformanceTests();
      setPerformanceResults(perfResults);

      // Run scalability tests
      setCurrentTest('Running scalability tests...');
      setTestProgress(50);
      const scalResults = await performanceTestingService.testScalability();
      setScalabilityResults(scalResults);

      // Run index tests
      setCurrentTest('Testing indexing strategies...');
      setTestProgress(80);
      const idxResults = await indexingStrategyTestService.testAllIndexes(tenant.id);
      setIndexResults(idxResults);

      setTestProgress(100);
      setCurrentTest('Tests completed');

      // Calculate updated metrics
      const totalTests = perfResults.length + scalResults.length + idxResults.length;
      const passedTests = perfResults.filter(r => r.passed).length + 
                         scalResults.filter(r => r.passed).length + 
                         idxResults.filter(r => r.performance !== 'critical').length;

      const updatedMetrics: PerformanceMetrics = {
        overallScore: Math.round((passedTests / totalTests) * 100),
        queryPerformance: Math.round((perfResults.filter(r => r.passed).length / perfResults.length) * 100),
        scalabilityScore: Math.round(scalResults.reduce((sum, r) => sum + r.scalabilityScore, 0) / scalResults.length),
        indexEfficiency: Math.round((idxResults.filter(r => r.performance !== 'critical').length / idxResults.length) * 100),
        lastTestDate: new Date(),
        totalTests,
        passedTests,
        failedTests: totalTests - passedTests
      };

      setMetrics(updatedMetrics);

      toast({
        title: "Performance Tests Complete",
        description: `Completed ${totalTests} tests with ${passedTests} passing. Overall score: ${updatedMetrics.overallScore}/100`,
        variant: passedTests === totalTests ? "default" : "destructive",
      });

    } catch (error) {
      toast({
        title: "Performance Tests Failed",
        description: "Failed to complete performance tests. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsRunningTests(false);
      setTestProgress(0);
      setCurrentTest('');
    }
  };

  const runBenchmarkTests = async () => {
    if (!tenant?.id) return;

    try {
      setCurrentTest('Running benchmark comparisons...');
      
      // Run flat vs nested benchmark
      const flatVsNested = await performanceBenchmarkService.benchmarkFlatVsNested(tenant.id);
      
      // Run data duplication vs joins benchmark
      const dupVsJoins = await performanceBenchmarkService.benchmarkDataDuplicationVsJoins(tenant.id);

      toast({
        title: "Benchmark Tests Complete",
        description: `Flat collections are ${flatVsNested.improvement.toFixed(1)}% faster. Data duplication improves performance by ${dupVsJoins.improvement.toFixed(1)}%.`,
      });

    } catch (error) {
      toast({
        title: "Benchmark Tests Failed",
        description: "Failed to complete benchmark tests.",
        variant: "destructive",
      });
    }
  };

  const exportPerformanceReport = async () => {
    try {
      const report = {
        timestamp: new Date().toISOString(),
        tenant: tenant?.name || 'Unknown',
        metrics,
        performanceResults,
        scalabilityResults,
        indexResults,
        summary: {
          totalTests: metrics?.totalTests || 0,
          passedTests: metrics?.passedTests || 0,
          failedTests: metrics?.failedTests || 0,
          overallScore: metrics?.overallScore || 0
        }
      };

      const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `performance-report-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Report Exported",
        description: "Performance report has been downloaded successfully.",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export performance report.",
        variant: "destructive",
      });
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBackground = (score: number) => {
    if (score >= 90) return 'bg-green-100';
    if (score >= 70) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  const getPerformanceBadgeVariant = (performance: string) => {
    switch (performance) {
      case 'excellent': return 'default';
      case 'good': return 'secondary';
      case 'poor': return 'outline';
      case 'critical': return 'destructive';
      default: return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Zap className="h-6 w-6" />
            Performance Testing Dashboard
          </h2>
          <p className="text-muted-foreground mt-1">
            Monitor query performance, validate indexing strategies, and ensure scalability
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportPerformanceReport}>
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
          <Button variant="outline" onClick={runBenchmarkTests} disabled={isRunningTests}>
            <BarChart3 className="mr-2 h-4 w-4" />
            Run Benchmarks
          </Button>
          <Button onClick={runPerformanceTests} disabled={isRunningTests}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isRunningTests ? 'animate-spin' : ''}`} />
            {isRunningTests ? 'Running Tests...' : 'Run Performance Tests'}
          </Button>
        </div>
      </div>

      {/* Test Progress */}
      {isRunningTests && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{currentTest}</span>
                <span>{testProgress}%</span>
              </div>
              <Progress value={testProgress} className="h-2" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Performance Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <div className={`p-2 rounded-full ${getScoreBackground(metrics.overallScore)}`}>
                  <Gauge className={`h-6 w-6 ${getScoreColor(metrics.overallScore)}`} />
                </div>
                <div>
                  <div className={`text-2xl font-bold ${getScoreColor(metrics.overallScore)}`}>
                    {metrics.overallScore}/100
                  </div>
                  <div className="text-sm text-gray-600">Overall Score</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <div className={`p-2 rounded-full ${getScoreBackground(metrics.queryPerformance)}`}>
                  <Timer className={`h-6 w-6 ${getScoreColor(metrics.queryPerformance)}`} />
                </div>
                <div>
                  <div className={`text-2xl font-bold ${getScoreColor(metrics.queryPerformance)}`}>
                    {metrics.queryPerformance}/100
                  </div>
                  <div className="text-sm text-gray-600">Query Performance</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <div className={`p-2 rounded-full ${getScoreBackground(metrics.scalabilityScore)}`}>
                  <TrendingUp className={`h-6 w-6 ${getScoreColor(metrics.scalabilityScore)}`} />
                </div>
                <div>
                  <div className={`text-2xl font-bold ${getScoreColor(metrics.scalabilityScore)}`}>
                    {metrics.scalabilityScore}/100
                  </div>
                  <div className="text-sm text-gray-600">Scalability</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <div className={`p-2 rounded-full ${getScoreBackground(metrics.indexEfficiency)}`}>
                  <Database className={`h-6 w-6 ${getScoreColor(metrics.indexEfficiency)}`} />
                </div>
                <div>
                  <div className={`text-2xl font-bold ${getScoreColor(metrics.indexEfficiency)}`}>
                    {metrics.indexEfficiency}/100
                  </div>
                  <div className="text-sm text-gray-600">Index Efficiency</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Test Results Tabs */}
      <Tabs defaultValue="performance" className="w-full">
        <TabsList>
          <TabsTrigger value="performance">Query Performance</TabsTrigger>
          <TabsTrigger value="scalability">Scalability</TabsTrigger>
          <TabsTrigger value="indexing">Indexing</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Timer className="h-5 w-5" />
                Query Performance Results
              </CardTitle>
              <CardDescription>
                Flat collection query performance and optimization results
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {performanceResults.map((result, index) => (
                  <div key={index} className="flex items-start space-x-4 p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">{result.testName}</h4>
                        {result.passed ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-500" />
                        )}
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Query Time:</span>
                          <div className="font-medium">{result.metrics.queryTime?.toFixed(2)}ms</div>
                        </div>
                        <div>
                          <span className="text-gray-500">Documents:</span>
                          <div className="font-medium">{result.metrics.documentsRead || 0}</div>
                        </div>
                        <div>
                          <span className="text-gray-500">Cache Hit:</span>
                          <div className="font-medium">{result.metrics.cacheHitRate ? 'Yes' : 'No'}</div>
                        </div>
                        <div>
                          <span className="text-gray-500">Duration:</span>
                          <div className="font-medium">{result.duration.toFixed(2)}ms</div>
                        </div>
                      </div>
                      {result.details && (
                        <p className="text-sm text-gray-600 mt-2">{result.details}</p>
                      )}
                      {result.error && (
                        <p className="text-sm text-red-600 mt-2">Error: {result.error}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scalability" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Scalability Test Results
              </CardTitle>
              <CardDescription>
                Performance scaling with increasing data size and tenant count
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {scalabilityResults.map((result, index) => (
                  <div key={index} className="flex items-start space-x-4 p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">{result.testName}</h4>
                        {result.passed ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-500" />
                        )}
                        <Badge variant="outline">Score: {result.scalabilityScore}/100</Badge>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Data Size:</span>
                          <div className="font-medium">{result.dataSize} docs</div>
                        </div>
                        <div>
                          <span className="text-gray-500">Query Time:</span>
                          <div className="font-medium">{result.queryTime.toFixed(2)}ms</div>
                        </div>
                        <div>
                          <span className="text-gray-500">Throughput:</span>
                          <div className="font-medium">{result.throughput.toFixed(1)} docs/sec</div>
                        </div>
                        <div>
                          <span className="text-gray-500">Memory:</span>
                          <div className="font-medium">{result.memoryUsage.toFixed(1)}MB</div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="indexing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Indexing Strategy Results
              </CardTitle>
              <CardDescription>
                Index performance validation and optimization recommendations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {indexResults.map((result, index) => (
                  <div key={index} className="flex items-start space-x-4 p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">{result.indexName}</h4>
                        <Badge variant={getPerformanceBadgeVariant(result.performance)}>
                          {result.performance}
                        </Badge>
                        <Badge variant="outline">{result.collection}</Badge>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Query Time:</span>
                          <div className="font-medium">{result.testResults.queryTime.toFixed(2)}ms</div>
                        </div>
                        <div>
                          <span className="text-gray-500">Documents:</span>
                          <div className="font-medium">{result.testResults.documentsReturned}</div>
                        </div>
                        <div>
                          <span className="text-gray-500">Index Used:</span>
                          <div className="font-medium">{result.testResults.indexUsed ? 'Yes' : 'No'}</div>
                        </div>
                        <div>
                          <span className="text-gray-500">Fields:</span>
                          <div className="font-medium">{result.fields.join(', ')}</div>
                        </div>
                      </div>
                      {result.recommendations.length > 0 && (
                        <div className="mt-2">
                          <span className="text-sm text-gray-500">Recommendations:</span>
                          <ul className="text-sm text-gray-600 mt-1 list-disc list-inside">
                            {result.recommendations.slice(0, 2).map((rec, i) => (
                              <li key={i}>{rec}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
