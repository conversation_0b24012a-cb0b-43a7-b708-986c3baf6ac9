"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Database,
  RefreshCw,
  Trash2,
  CheckCircle,
  AlertTriangle,
  Zap,
  Shield,
  Activity,
  Wrench,
  TestTube,
  Eye
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface DataSystemStatus {
  isHealthy: boolean;
  totalCollections: number;
  issues: string[];
  collectionStats: { [key: string]: { count: number; hasIssues: boolean } };
  availableOperations: string[];
  validationStats?: {
    totalViolations: number;
    violationsByType: Record<string, number>;
    violationsByCollection: Record<string, number>;
    recentViolations: any[];
  };
}

interface DataOperation {
  action: 'seed' | 'reset' | 'validate' | 'fix-tenant-ids' | 'validation-stats' | 'clear-violations' | 'toggle-validation' | 'test-professional';
  label: string;
  description: string;
  icon: React.ReactNode;
  variant: 'default' | 'destructive' | 'outline' | 'secondary';
  confirmationRequired: boolean;
}

export function ProfessionalDataManager() {
  const [status, setStatus] = useState<DataSystemStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const { toast } = useToast();

  const dataOperations: DataOperation[] = [
    {
      action: 'validate',
      label: 'Validate Data',
      description: 'Check data consistency and integrity',
      icon: <CheckCircle className="h-4 w-4" />,
      variant: 'outline',
      confirmationRequired: false
    },
    {
      action: 'fix-tenant-ids',
      label: 'Fix Tenant IDs',
      description: 'Fix incorrect/missing tenant IDs in existing documents',
      icon: <Wrench className="h-4 w-4" />,
      variant: 'secondary',
      confirmationRequired: true
    },
    {
      action: 'seed',
      label: 'Professional Seed',
      description: 'Seed database with clean, standardized data',
      icon: <Database className="h-4 w-4" />,
      variant: 'default',
      confirmationRequired: true
    },
    {
      action: 'test-professional',
      label: 'Test Professional System',
      description: 'Test authentication, tenant isolation, and data services',
      icon: <CheckCircle className="h-4 w-4" />,
      variant: 'outline',
      confirmationRequired: false
    },
    {
      action: 'reset',
      label: 'Nuclear Reset',
      description: '🚨 DELETE ALL DOCUMENTS from ALL collections (collections remain, data deleted)',
      icon: <Trash2 className="h-4 w-4" />,
      variant: 'destructive',
      confirmationRequired: true
    },
    {
      action: 'validation-stats',
      label: 'Validation Stats',
      description: 'View runtime validation statistics and violations',
      icon: <Activity className="h-4 w-4" />,
      variant: 'outline',
      confirmationRequired: false
    },
    {
      action: 'clear-violations',
      label: 'Clear Violations',
      description: 'Clear validation violation log',
      icon: <RefreshCw className="h-4 w-4" />,
      variant: 'secondary',
      confirmationRequired: true
    },
    {
      action: 'toggle-validation',
      label: 'Toggle Validation',
      description: 'Enable/disable runtime validation (emergency use only)',
      icon: <Shield className="h-4 w-4" />,
      variant: 'destructive',
      confirmationRequired: true
    }
  ];

  // Load data system status
  const loadStatus = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/professional-data-manager');
      const data = await response.json();

      if (data.success) {
        // Load validation statistics
        try {
          const validationResponse = await fetch('/api/validation-stats');
          const validationData = await validationResponse.json();
          data.status.validationStats = validationData.success ? validationData.stats : {
            totalViolations: 0,
            violationsByType: {},
            violationsByCollection: {},
            recentViolations: []
          };
        } catch (validationError) {
          console.warn('Failed to load validation stats:', validationError);
          data.status.validationStats = {
            totalViolations: 0,
            violationsByType: {},
            violationsByCollection: {},
            recentViolations: []
          };
        }

        setStatus(data.status);
        setLastUpdated(new Date());
      } else {
        toast({
          title: "Error",
          description: data.message || "Failed to load data system status",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to connect to data manager",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Execute data operation
  const executeOperation = async (operation: DataOperation) => {
    if (operation.confirmationRequired) {
      let confirmMessage = `Are you sure you want to ${operation.label}?\n\n${operation.description}\n\nThis action cannot be undone.`;

      if (operation.action === 'reset') {
        confirmMessage = `🚨 NUCLEAR RESET WARNING 🚨\n\nThis will DELETE ALL DOCUMENTS from ALL Firebase collections!\n\n• Collections will remain but be empty\n• All data will be permanently lost\n• This affects the entire database\n• This action is IRREVERSIBLE\n\nType "DELETE ALL DATA" to confirm:`;

        const userInput = window.prompt(confirmMessage);
        if (userInput !== "DELETE ALL DATA") {
          toast({
            title: "Nuclear Reset Cancelled",
            description: "Confirmation text did not match. Reset cancelled for safety.",
          });
          return;
        }
      } else {
        const confirmed = window.confirm(confirmMessage);
        if (!confirmed) return;
      }
    }

    setIsLoading(true);
    try {
      let response;
      let data;

      // Handle validation-specific operations
      if (['validation-stats', 'clear-violations', 'toggle-validation'].includes(operation.action)) {
        if (operation.action === 'validation-stats') {
          // Just show current stats - reload status
          await loadStatus();
          toast({
            title: "Validation Statistics",
            description: "Statistics refreshed successfully",
          });
          return;
        } else if (operation.action === 'clear-violations') {
          response = await fetch('/api/validation-stats', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ action: 'clear-violations' })
          });
        } else if (operation.action === 'toggle-validation') {
          // Get current config first
          const configResponse = await fetch('/api/validation-stats', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ action: 'get-config' })
          });
          const configData = await configResponse.json();

          if (configData.success) {
            const currentConfig = configData.config;
            const newConfig = {
              enforceSchemaValidation: !currentConfig.enforceSchemaValidation,
              enforceCollectionWhitelist: !currentConfig.enforceCollectionWhitelist,
              enforceTenantIsolation: !currentConfig.enforceTenantIsolation,
              blockUnauthorizedOperations: !currentConfig.blockUnauthorizedOperations,
            };

            response = await fetch('/api/validation-stats', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ action: 'update-config', config: newConfig })
            });
          } else {
            throw new Error('Failed to get current validation config');
          }
        }

        data = await response.json();
      } else if (operation.action === 'test-professional') {
        // Redirect to professional test page
        window.open('/professional-test', '_blank');
        return;
      } else {
        // Handle regular data manager operations
        response = await fetch(`/api/professional-data-manager?action=${operation.action}`, {
          method: 'POST'
        });
        data = await response.json();
      }

      if (data.success) {
        toast({
          title: "Success",
          description: data.message,
        });

        // Reload status after operation
        await loadStatus();
      } else {
        toast({
          title: "Error",
          description: data.message || `Failed to ${operation.label}`,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to execute ${operation.label}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load status on component mount
  useEffect(() => {
    loadStatus();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Professional Data Manager</h2>
          <p className="text-muted-foreground">
            Unified data management system for EVEXA
          </p>
        </div>
        <Button 
          onClick={loadStatus} 
          disabled={isLoading}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            System Status
          </CardTitle>
          <CardDescription>
            Current state of the EVEXA data system
          </CardDescription>
        </CardHeader>
        <CardContent>
          {status ? (
            <div className="space-y-4">
              {/* Health Status */}
              <div className="flex items-center gap-2">
                <Badge variant={status.isHealthy ? "default" : "destructive"}>
                  {status.isHealthy ? (
                    <>
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Healthy
                    </>
                  ) : (
                    <>
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Issues Found
                    </>
                  )}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  {status.totalCollections} collections
                </span>
                {lastUpdated && (
                  <span className="text-xs text-muted-foreground">
                    Last updated: {lastUpdated.toLocaleTimeString()}
                  </span>
                )}
              </div>

              {/* Issues */}
              {status.issues.length > 0 && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      <p className="font-medium">Data Issues Found:</p>
                      <ul className="list-disc list-inside text-sm">
                        {status.issues.map((issue, index) => (
                          <li key={index}>{issue}</li>
                        ))}
                      </ul>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {/* Collection Stats */}
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                {Object.entries(status.collectionStats).map(([collection, stats]) => (
                  <div
                    key={collection}
                    className={`p-2 rounded border text-sm ${
                      stats.hasIssues ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'
                    }`}
                  >
                    <div className="font-medium">{collection}</div>
                    <div className="text-xs text-muted-foreground">
                      {stats.count} documents
                    </div>
                  </div>
                ))}
              </div>

              {/* Validation Statistics */}
              {status.validationStats && (
                <div className="border-t pt-4">
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Runtime Validation Statistics
                  </h4>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Total Violations */}
                    <div className="p-3 rounded border">
                      <div className="text-2xl font-bold text-red-600">
                        {status.validationStats.totalViolations}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Total Violations
                      </div>
                    </div>

                    {/* Violations by Type */}
                    <div className="p-3 rounded border">
                      <div className="text-sm font-medium mb-2">By Type</div>
                      <div className="space-y-1">
                        {Object.entries(status.validationStats.violationsByType).map(([type, count]) => (
                          <div key={type} className="flex justify-between text-xs">
                            <span className="capitalize">{type.replace('_', ' ')}</span>
                            <span className="font-medium">{count}</span>
                          </div>
                        ))}
                        {Object.keys(status.validationStats.violationsByType).length === 0 && (
                          <div className="text-xs text-muted-foreground">No violations</div>
                        )}
                      </div>
                    </div>

                    {/* Violations by Collection */}
                    <div className="p-3 rounded border">
                      <div className="text-sm font-medium mb-2">By Collection</div>
                      <div className="space-y-1">
                        {Object.entries(status.validationStats.violationsByCollection).slice(0, 5).map(([collection, count]) => (
                          <div key={collection} className="flex justify-between text-xs">
                            <span className="truncate">{collection}</span>
                            <span className="font-medium">{count}</span>
                          </div>
                        ))}
                        {Object.keys(status.validationStats.violationsByCollection).length === 0 && (
                          <div className="text-xs text-muted-foreground">No violations</div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Recent Violations */}
                  {status.validationStats.recentViolations.length > 0 && (
                    <div className="mt-4">
                      <div className="text-sm font-medium mb-2">Recent Violations</div>
                      <div className="space-y-2 max-h-32 overflow-y-auto">
                        {status.validationStats.recentViolations.slice(0, 5).map((violation, index) => (
                          <div key={index} className="text-xs p-2 bg-red-50 border border-red-200 rounded">
                            <div className="font-medium text-red-800">
                              {violation.type.replace('_', ' ').toUpperCase()}
                            </div>
                            <div className="text-red-600">
                              {violation.collection}: {violation.error}
                            </div>
                            <div className="text-red-500 mt-1">
                              {new Date(violation.timestamp).toLocaleString()}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-4">
              <div className="animate-pulse">Loading system status...</div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Data Operations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Data Operations
          </CardTitle>
          <CardDescription>
            Professional data management operations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {dataOperations.map((operation) => (
              <Card key={operation.action} className="relative">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      {operation.icon}
                      <h3 className="font-medium">{operation.label}</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {operation.description}
                    </p>
                    <Button
                      onClick={() => executeOperation(operation)}
                      disabled={isLoading}
                      variant={operation.variant}
                      size="sm"
                      className="w-full"
                    >
                      {isLoading ? (
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        operation.icon
                      )}
                      {operation.label}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Warning */}
      <Alert>
        <Zap className="h-4 w-4" />
        <AlertDescription>
          <strong>Professional Data Manager:</strong> This system replaces all previous chaotic seeding mechanisms. 
          Use only this interface for data operations to maintain consistency and prevent duplicate collections.
        </AlertDescription>
      </Alert>
    </div>
  );
}
