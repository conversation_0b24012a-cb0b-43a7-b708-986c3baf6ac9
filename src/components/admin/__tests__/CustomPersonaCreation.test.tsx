/**
 * Custom Persona Creation Tests
 * Tests for the enhanced persona creation functionality
 */

import { createTenantPersonaService } from '@/services/tenantPersonaService';
import { PersonaTemplate } from '@/types/personas';

// Mock Firebase
jest.mock('@/lib/firebase', () => ({
  db: {}
}));

jest.mock('firebase/firestore', () => ({
  collection: jest.fn(),
  doc: jest.fn(),
  addDoc: jest.fn(),
  updateDoc: jest.fn(),
  deleteDoc: jest.fn(),
  getDocs: jest.fn(),
  getDoc: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  orderBy: jest.fn(),
  serverTimestamp: jest.fn(() => new Date()),
  writeBatch: jest.fn()
}));

describe('Custom Persona Creation Service', () => {
  const testTenantId = 'test-tenant-123';
  let personaService: any;

  beforeEach(() => {
    personaService = createTenantPersonaService(testTenantId);
    jest.clearAllMocks();
  });

  test('should create persona service instance', () => {
    expect(personaService).toBeDefined();
    expect(personaService.tenantId).toBe(testTenantId);
  });

  test('should validate persona data structure', () => {
    const validPersonaData: Omit<PersonaTemplate, 'id' | 'category' | 'createdAt' | 'updatedAt'> = {
      name: 'Custom Exhibition Manager',
      description: 'Manages exhibitions with custom permissions',
      isActive: true,
      permissions: {
        modules: [
          {
            module: 'exhibitions',
            actions: ['read', 'write'],
            description: 'Manage exhibitions'
          }
        ],
        systemPermissions: {
          canManageUsers: false,
          canManageSettings: false,
          canViewAnalytics: true,
          canExportData: true,
          canManageIntegrations: false,
          canAccessSupport: true
        }
      },
      targetUsers: ['Exhibition managers', 'Event coordinators']
    };

    // Validate structure
    expect(validPersonaData.name).toBeDefined();
    expect(validPersonaData.description).toBeDefined();
    expect(validPersonaData.permissions).toBeDefined();
    expect(validPersonaData.permissions.modules).toBeInstanceOf(Array);
    expect(validPersonaData.permissions.systemPermissions).toBeDefined();
    expect(validPersonaData.targetUsers).toBeInstanceOf(Array);
  });

  test('should handle persona templates correctly', () => {
    const exhibitionTemplate = {
      name: 'Exhibition Focused',
      description: 'Core exhibition management with basic permissions',
      permissions: {
        modules: [
          { module: 'exhibitions', actions: ['read', 'write'], description: 'Manage exhibitions' },
          { module: 'events', actions: ['read', 'write'], description: 'Manage events' },
          { module: 'tasks', actions: ['read', 'write'], description: 'Manage tasks' }
        ],
        systemPermissions: {
          canManageUsers: false,
          canManageSettings: false,
          canViewAnalytics: true,
          canExportData: true,
          canManageIntegrations: false,
          canAccessSupport: true
        }
      },
      targetUsers: ['Exhibition coordinators', 'Event managers']
    };

    expect(exhibitionTemplate.permissions.modules.length).toBe(3);
    expect(exhibitionTemplate.permissions.modules[0].module).toBe('exhibitions');
    expect(exhibitionTemplate.permissions.systemPermissions.canViewAnalytics).toBe(true);
  });

  test('should validate module permissions structure', () => {
    const modulePermission = {
      module: 'exhibitions',
      actions: ['read', 'write', 'delete'],
      description: 'Full access to exhibitions'
    };

    expect(modulePermission.module).toBeDefined();
    expect(modulePermission.actions).toBeInstanceOf(Array);
    expect(modulePermission.actions.length).toBeGreaterThan(0);
    expect(modulePermission.description).toBeDefined();
  });

  test('should validate system permissions structure', () => {
    const systemPermissions = {
      canManageUsers: true,
      canManageSettings: false,
      canViewAnalytics: true,
      canExportData: true,
      canManageIntegrations: false,
      canAccessSupport: true
    };

    const permissionKeys = Object.keys(systemPermissions);
    expect(permissionKeys).toContain('canManageUsers');
    expect(permissionKeys).toContain('canManageSettings');
    expect(permissionKeys).toContain('canViewAnalytics');
    expect(permissionKeys).toContain('canExportData');
    expect(permissionKeys).toContain('canManageIntegrations');
    expect(permissionKeys).toContain('canAccessSupport');
  });
});
