/**
 * Invitation Management Dashboard
 * Comprehensive dashboard for managing user invitations
 */

'use client';

import { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  UserPlus, 
  Mail, 
  Clock, 
  CheckCircle, 
  XCircle, 
  MoreHorizontal,
  RefreshCw,
  Search,
  Filter,
  TrendingUp,
  Users,
  Send,
  Ban
} from 'lucide-react';
import { InvitationManagementService, UserInvitation, InvitationStats } from '@/services/invitationManagementService';
import { EnhancedUserInvitation } from './EnhancedUserInvitation';
import { useAuth } from '@/contexts/auth-context';

interface InvitationManagementDashboardProps {
  className?: string;
}

export const InvitationManagementDashboard: React.FC<InvitationManagementDashboardProps> = ({
  className
}) => {
  const { user } = useAuth();
  const [invitations, setInvitations] = useState<UserInvitation[]>([]);
  const [stats, setStats] = useState<InvitationStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [refreshing, setRefreshing] = useState(false);

  const invitationService = user?.tenantId ? new InvitationManagementService(user.tenantId) : null;

  useEffect(() => {
    if (invitationService) {
      loadData();
    }
  }, [invitationService]);

  const loadData = async () => {
    if (!invitationService) return;

    try {
      setLoading(true);
      setError(null);

      const [invitationsData, statsData] = await Promise.all([
        invitationService.getInvitations(),
        invitationService.getInvitationStats()
      ]);

      setInvitations(invitationsData);
      setStats(statsData);
    } catch (error) {
      console.error('Error loading invitation data:', error);
      setError('Failed to load invitation data');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleResendInvitation = async (invitationId: string) => {
    if (!invitationService) return;

    try {
      const result = await invitationService.resendInvitation(invitationId);
      if (result.success) {
        await loadData(); // Refresh data
      } else {
        setError(result.error || 'Failed to resend invitation');
      }
    } catch (error) {
      console.error('Error resending invitation:', error);
      setError('Failed to resend invitation');
    }
  };

  const handleCancelInvitation = async (invitationId: string) => {
    if (!invitationService) return;

    try {
      const result = await invitationService.cancelInvitation(invitationId);
      if (result.success) {
        await loadData(); // Refresh data
      } else {
        setError(result.error || 'Failed to cancel invitation');
      }
    } catch (error) {
      console.error('Error cancelling invitation:', error);
      setError('Failed to cancel invitation');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'accepted':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'expired':
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'resent':
        return <Send className="h-4 w-4 text-blue-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'expired':
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'resent':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredInvitations = invitations.filter(invitation => {
    const matchesSearch =
      invitation.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invitation.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invitation.lastName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || invitation.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Define columns for AdvancedDataTable
  const columns: AdvancedTableColumn<UserInvitation>[] = useMemo(() => [
    {
      id: 'user',
      header: 'User',
      accessorKey: 'email',
      cell: ({ row }) => (
        <div>
          <div className="font-medium">
            {row.original.firstName} {row.original.lastName}
          </div>
          <div className="text-sm text-muted-foreground">
            {row.original.email}
          </div>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      id: 'role',
      header: 'Role',
      accessorKey: 'roleName',
      cell: ({ row }) => (
        <span>{row.original.roleName}</span>
      ),
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      id: 'status',
      header: 'Status',
      accessorKey: 'status',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(row.original.status)}
          <Badge className={getStatusColor(row.original.status)}>
            {row.original.status.charAt(0).toUpperCase() + row.original.status.slice(1)}
          </Badge>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      id: 'sent',
      header: 'Sent',
      accessorKey: 'sentAt',
      cell: ({ row }) => (
        <span>{row.original.sentAt.toLocaleDateString()}</span>
      ),
      enableSorting: true,
      enableColumnFilter: false,
    },
    {
      id: 'expires',
      header: 'Expires',
      accessorKey: 'expiresAt',
      cell: ({ row }) => (
        <span className={new Date(row.original.expiresAt) < new Date() ? 'text-red-600' : ''}>
          {row.original.expiresAt.toLocaleDateString()}
        </span>
      ),
      enableSorting: true,
      enableColumnFilter: false,
    },
  ], []);

  // Define row actions
  const getRowActions = (invitation: UserInvitation): AdvancedTableRowAction<UserInvitation>[] => [
    {
      label: 'Resend Invitation',
      icon: Mail,
      onClick: () => handleResendInvitation(invitation.id),
      disabled: invitation.status === 'accepted',
    },
    {
      label: 'Cancel Invitation',
      icon: XCircle,
      onClick: () => handleCancelInvitation(invitation.id),
      variant: 'destructive' as const,
      disabled: invitation.status === 'accepted',
    },
    {
      label: 'View Details',
      icon: Mail,
      onClick: () => {}, // Add view details functionality if needed
    },
  ];

  // Mobile card renderer
  const mobileCardRenderer = (invitation: UserInvitation) => (
    <div className="p-4 space-y-2">
      <div className="flex items-center justify-between">
        <div>
          <div className="font-medium">
            {invitation.firstName} {invitation.lastName}
          </div>
          <div className="text-sm text-muted-foreground">
            {invitation.email}
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getStatusIcon(invitation.status)}
          <Badge className={getStatusColor(invitation.status)}>
            {invitation.status.charAt(0).toUpperCase() + invitation.status.slice(1)}
          </Badge>
        </div>
      </div>
      <div className="text-sm">
        <span className="font-medium">Role:</span> {invitation.roleName}
      </div>
      <div className="text-sm">
        <span className="font-medium">Sent:</span> {invitation.sentAt.toLocaleDateString()}
      </div>
      <div className="text-sm">
        <span className="font-medium">Expires:</span>{' '}
        <span className={new Date(invitation.expiresAt) < new Date() ? 'text-red-600' : ''}>
          {invitation.expiresAt.toLocaleDateString()}
        </span>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Invitation Management</h2>
          <p className="text-muted-foreground">Manage user invitations and track acceptance rates</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <EnhancedUserInvitation onInvitationSent={loadData} />
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Invitations</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
                <Users className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Pending</p>
                  <p className="text-2xl font-bold">{stats.pending}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Accepted</p>
                  <p className="text-2xl font-bold">{stats.accepted}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Acceptance Rate</p>
                  <p className="text-2xl font-bold">{stats.acceptanceRate.toFixed(1)}%</p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Label htmlFor="search">Search Invitations</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="status">Filter by Status</Label>
              <select
                id="status"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
              >
                <option value="all">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="accepted">Accepted</option>
                <option value="expired">Expired</option>
                <option value="cancelled">Cancelled</option>
                <option value="resent">Resent</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Invitations Table */}
      <Card>
        <CardHeader>
          <CardTitle>Invitations ({filteredInvitations.length})</CardTitle>
          <CardDescription>
            Manage and track all user invitations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AdvancedDataTable
            data={filteredInvitations}
            columns={columns}
            loading={loading}
            enableVirtualization={filteredInvitations.length > 100}
            enableGlobalSearch={true}
            searchPlaceholder="Search invitations by name, email, or role..."
            enableColumnFilters={true}
            enableRowSelection={false}
            enableColumnResizing={true}
            enableColumnVisibility={true}
            enableRowActions={true}
            rowActions={getRowActions}
            maxVisibleRowActions={3}
            enableExport={true}
            exportFormats={['csv', 'excel']}
            exportFileName="invitations-export"
            mobileCardRenderer={mobileCardRenderer}
            onRefresh={handleRefresh}
            variant="default"
            className="w-full"
            emptyMessage={
              searchTerm || statusFilter !== 'all'
                ? 'Try adjusting your search or filter criteria'
                : 'Start by inviting users to join your team'
            }
          />
        </CardContent>
      </Card>
    </div>
  );
};
