/**
 * Security Report Dashboard
 * Comprehensive security reporting and monitoring interface
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Shield, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Download,
  Eye,
  Clock,
  Target,
  Lock,
  Unlock,
  Database,
  Users,
  FileText
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import { generateSecurityReport } from '@/lib/security-validation';
import { securityAuditService } from '@/services/securityAuditService';
import { tenantIsolationTestService } from '@/services/tenantIsolationTestService';

interface SecurityMetrics {
  overallScore: number;
  lastAuditDate: Date;
  criticalIssues: number;
  highIssues: number;
  mediumIssues: number;
  lowIssues: number;
  totalIssues: number;
  resolvedIssues: number;
  trend: 'improving' | 'declining' | 'stable';
}

interface SecurityRecommendation {
  id: string;
  title: string;
  description: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  category: string;
  estimatedEffort: string;
  impact: string;
  status: 'pending' | 'in_progress' | 'completed';
}

export default function SecurityReportDashboard() {
  const { user, tenant } = useAuth();
  const { toast } = useToast();
  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null);
  const [recommendations, setRecommendations] = useState<SecurityRecommendation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRunningAudit, setIsRunningAudit] = useState(false);
  const [auditProgress, setAuditProgress] = useState(0);

  useEffect(() => {
    loadSecurityData();
  }, []);

  const loadSecurityData = async () => {
    setIsLoading(true);
    try {
      // Load existing security metrics (would come from database in real implementation)
      const mockMetrics: SecurityMetrics = {
        overallScore: 85,
        lastAuditDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        criticalIssues: 1,
        highIssues: 3,
        mediumIssues: 5,
        lowIssues: 8,
        totalIssues: 17,
        resolvedIssues: 12,
        trend: 'improving'
      };

      const mockRecommendations: SecurityRecommendation[] = [
        {
          id: '1',
          title: 'Implement Multi-Factor Authentication',
          description: 'Add MFA for all admin users to enhance account security',
          priority: 'critical',
          category: 'Authentication',
          estimatedEffort: '2-3 days',
          impact: 'High',
          status: 'pending'
        },
        {
          id: '2',
          title: 'Encrypt Sensitive Data Fields',
          description: 'Implement field-level encryption for PII and sensitive data',
          priority: 'high',
          category: 'Data Protection',
          estimatedEffort: '1 week',
          impact: 'High',
          status: 'in_progress'
        },
        {
          id: '3',
          title: 'Regular Security Audits',
          description: 'Schedule automated security audits to run weekly',
          priority: 'medium',
          category: 'Monitoring',
          estimatedEffort: '1 day',
          impact: 'Medium',
          status: 'pending'
        }
      ];

      setMetrics(mockMetrics);
      setRecommendations(mockRecommendations);
    } catch (error) {
      toast({
        title: "Error Loading Security Data",
        description: "Failed to load security metrics and recommendations.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runComprehensiveAudit = async () => {
    setIsRunningAudit(true);
    setAuditProgress(0);

    try {
      // Simulate audit progress
      const progressInterval = setInterval(() => {
        setAuditProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 500);

      // Run comprehensive security audit
      const auditResult = await securityAuditService.runSecurityAudit();
      
      // Run tenant isolation tests
      const isolationResults = await tenantIsolationTestService.runAllTests();
      
      // Generate security report
      const securityReport = await generateSecurityReport();

      clearInterval(progressInterval);
      setAuditProgress(100);

      // Update metrics based on audit results
      const updatedMetrics: SecurityMetrics = {
        overallScore: auditResult.overallScore,
        lastAuditDate: new Date(),
        criticalIssues: auditResult.summary.critical,
        highIssues: auditResult.summary.high,
        mediumIssues: auditResult.summary.medium,
        lowIssues: auditResult.summary.low,
        totalIssues: auditResult.summary.total,
        resolvedIssues: metrics?.resolvedIssues || 0,
        trend: auditResult.overallScore > (metrics?.overallScore || 0) ? 'improving' : 'declining'
      };

      setMetrics(updatedMetrics);

      // Convert audit recommendations to UI format
      const newRecommendations: SecurityRecommendation[] = auditResult.recommendations.slice(0, 10).map((rec, index) => ({
        id: `audit-${index}`,
        title: rec,
        description: `Security recommendation from latest audit`,
        priority: index < 2 ? 'critical' : index < 5 ? 'high' : 'medium',
        category: 'Security Audit',
        estimatedEffort: 'TBD',
        impact: 'TBD',
        status: 'pending' as const
      }));

      setRecommendations(prev => [...newRecommendations, ...prev.filter(r => !r.id.startsWith('audit-'))]);

      toast({
        title: "Security Audit Complete",
        description: `Audit completed with score: ${auditResult.overallScore}/100. Found ${auditResult.summary.total} issues.`,
        variant: auditResult.summary.critical > 0 ? "destructive" : "default",
      });

    } catch (error) {
      toast({
        title: "Audit Failed",
        description: "Failed to complete security audit. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsRunningAudit(false);
      setAuditProgress(0);
    }
  };

  const exportSecurityReport = async () => {
    try {
      const report = await generateSecurityReport();
      
      // Create downloadable report
      const reportData = {
        timestamp: new Date().toISOString(),
        tenant: tenant?.name || 'Unknown',
        metrics,
        recommendations,
        summary: report.summary,
        results: report.results
      };

      const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `security-report-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Report Exported",
        description: "Security report has been downloaded successfully.",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export security report.",
        variant: "destructive",
      });
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBackground = (score: number) => {
    if (score >= 90) return 'bg-green-100';
    if (score >= 70) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'destructive';
      case 'high': return 'secondary';
      case 'medium': return 'outline';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Shield className="h-6 w-6" />
            Security Dashboard
          </h2>
          <p className="text-muted-foreground mt-1">
            Monitor security posture and track improvements
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportSecurityReport}>
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
          <Button 
            onClick={runComprehensiveAudit} 
            disabled={isRunningAudit}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isRunningAudit ? 'animate-spin' : ''}`} />
            {isRunningAudit ? 'Running Audit...' : 'Run Security Audit'}
          </Button>
        </div>
      </div>

      {/* Audit Progress */}
      {isRunningAudit && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Security Audit Progress</span>
                <span>{auditProgress}%</span>
              </div>
              <Progress value={auditProgress} className="h-2" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Security Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <div className={`p-2 rounded-full ${getScoreBackground(metrics.overallScore)}`}>
                  <Shield className={`h-6 w-6 ${getScoreColor(metrics.overallScore)}`} />
                </div>
                <div>
                  <div className={`text-2xl font-bold ${getScoreColor(metrics.overallScore)}`}>
                    {metrics.overallScore}/100
                  </div>
                  <div className="text-sm text-gray-600">Security Score</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <div className="p-2 rounded-full bg-red-100">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-600">
                    {metrics.criticalIssues + metrics.highIssues}
                  </div>
                  <div className="text-sm text-gray-600">Critical & High Issues</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <div className="p-2 rounded-full bg-green-100">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {metrics.resolvedIssues}
                  </div>
                  <div className="text-sm text-gray-600">Resolved Issues</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <div className="p-2 rounded-full bg-blue-100">
                  {metrics.trend === 'improving' ? (
                    <TrendingUp className="h-6 w-6 text-blue-600" />
                  ) : (
                    <TrendingDown className="h-6 w-6 text-blue-600" />
                  )}
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {metrics.trend === 'improving' ? '+' : '-'}
                    {Math.abs(metrics.overallScore - 80)}
                  </div>
                  <div className="text-sm text-gray-600">Trend</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Security Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Security Recommendations
          </CardTitle>
          <CardDescription>
            Prioritized actions to improve your security posture
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recommendations.slice(0, 5).map((rec) => (
              <div key={rec.id} className="flex items-start space-x-4 p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-medium">{rec.title}</h4>
                    <Badge variant={getPriorityColor(rec.priority)}>
                      {rec.priority}
                    </Badge>
                    <Badge variant="outline">{rec.category}</Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{rec.description}</p>
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <span>Effort: {rec.estimatedEffort}</span>
                    <span>Impact: {rec.impact}</span>
                  </div>
                </div>
                <div className="flex items-center">
                  {rec.status === 'completed' ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : rec.status === 'in_progress' ? (
                    <Clock className="h-5 w-5 text-yellow-500" />
                  ) : (
                    <AlertTriangle className="h-5 w-5 text-red-500" />
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
