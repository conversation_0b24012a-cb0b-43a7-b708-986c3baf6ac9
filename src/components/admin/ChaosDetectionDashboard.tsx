/**
 * EVEXA Chaos Detection Dashboard
 * 
 * Real-time monitoring dashboard for the automated chaos detection system.
 * Shows metrics, violation history, prevention rules, and system health.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  ShieldAlert, 
  ShieldCheck, 
  Play, 
  Pause, 
  Scan, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Activity,
  Database,
  Eye,
  Settings
} from 'lucide-react';

interface ChaosDetectionMetrics {
  totalScans: number;
  violationsDetected: number;
  criticalViolations: number;
  lastScanTime: Date;
  systemHealth: 'healthy' | 'warning' | 'critical';
  preventedChaosEvents: number;
  monitoringUptime: number;
}

interface ChaosViolation {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  timestamp: Date;
  collectionName: string;
  details: Record<string, any>;
}

interface ChaosDetectionStatus {
  isMonitoring: boolean;
  metrics: ChaosDetectionMetrics;
  rulesCount: number;
}

export function ChaosDetectionDashboard() {
  const [status, setStatus] = useState<ChaosDetectionStatus | null>(null);
  const [violations, setViolations] = useState<ChaosViolation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isScanning, setIsScanning] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  useEffect(() => {
    loadDashboardData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // Load status and metrics
      const statusResponse = await fetch('/api/chaos-detection?action=status');
      const statusData = await statusResponse.json();
      
      if (statusData.success) {
        setStatus(statusData.data);
      }
      
      // Load recent violations
      const violationsResponse = await fetch('/api/chaos-detection?action=violations&limit=20');
      const violationsData = await violationsResponse.json();
      
      if (violationsData.success) {
        setViolations(violationsData.data.map((v: any) => ({
          ...v,
          timestamp: new Date(v.timestamp)
        })));
      }
      
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Failed to load chaos detection data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleMonitoring = async () => {
    if (!status) return;
    
    try {
      const action = status.isMonitoring ? 'stop' : 'start';
      const response = await fetch(`/api/chaos-detection?action=${action}`, {
        method: 'POST'
      });
      
      const data = await response.json();
      if (data.success) {
        await loadDashboardData();
      }
    } catch (error) {
      console.error('Failed to toggle monitoring:', error);
    }
  };

  const performManualScan = async () => {
    try {
      setIsScanning(true);
      const response = await fetch('/api/chaos-detection?action=scan', {
        method: 'POST'
      });
      
      const data = await response.json();
      if (data.success) {
        await loadDashboardData();
      }
    } catch (error) {
      console.error('Failed to perform manual scan:', error);
    } finally {
      setIsScanning(false);
    }
  };

  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'healthy':
        return <ShieldCheck className="h-5 w-5 text-green-600" />;
      case 'warning':
        return <ShieldAlert className="h-5 w-5 text-yellow-600" />;
      case 'critical':
        return <Shield className="h-5 w-5 text-red-600" />;
      default:
        return <Shield className="h-5 w-5 text-gray-600" />;
    }
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getSeverityBadge = (severity: string) => {
    const colors = {
      low: 'bg-blue-100 text-blue-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-orange-100 text-orange-800',
      critical: 'bg-red-100 text-red-800'
    };
    
    return (
      <Badge className={colors[severity as keyof typeof colors] || colors.medium}>
        {severity.toUpperCase()}
      </Badge>
    );
  };

  if (isLoading && !status) {
    return (
      <div className="flex items-center justify-center h-64">
        <Activity className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading chaos detection data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Shield className="h-6 w-6" />
            Chaos Detection System
          </h2>
          <p className="text-muted-foreground">
            Automated monitoring and prevention of data chaos
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={performManualScan}
            disabled={isScanning}
            variant="outline"
            size="sm"
          >
            <Scan className={`h-4 w-4 mr-2 ${isScanning ? 'animate-spin' : ''}`} />
            {isScanning ? 'Scanning...' : 'Manual Scan'}
          </Button>
          <Button
            onClick={toggleMonitoring}
            variant={status?.isMonitoring ? "destructive" : "default"}
            size="sm"
          >
            {status?.isMonitoring ? (
              <>
                <Pause className="h-4 w-4 mr-2" />
                Stop Monitoring
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Start Monitoring
              </>
            )}
          </Button>
        </div>
      </div>

      {/* System Health Alert */}
      {status && status.metrics.systemHealth !== 'healthy' && (
        <Alert className={getHealthColor(status.metrics.systemHealth)}>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            System health: <strong>{status.metrics.systemHealth.toUpperCase()}</strong>
            {status.metrics.criticalViolations > 0 && (
              <span> - {status.metrics.criticalViolations} critical violations detected</span>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            {status && getHealthIcon(status.metrics.systemHealth)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold capitalize">
              {status?.metrics.systemHealth || 'Unknown'}
            </div>
            <p className="text-xs text-muted-foreground">
              {status?.isMonitoring ? 'Monitoring Active' : 'Monitoring Inactive'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Scans</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{status?.metrics.totalScans || 0}</div>
            <p className="text-xs text-muted-foreground">
              Last: {status?.metrics.lastScanTime ? 
                new Date(status.metrics.lastScanTime).toLocaleTimeString() : 'Never'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Violations Detected</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{status?.metrics.violationsDetected || 0}</div>
            <p className="text-xs text-muted-foreground">
              {status?.metrics.criticalViolations || 0} critical
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Chaos Prevented</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{status?.metrics.preventedChaosEvents || 0}</div>
            <p className="text-xs text-muted-foreground">
              {status?.rulesCount || 0} active rules
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Violations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Recent Violations
          </CardTitle>
          <CardDescription>
            Latest chaos detection violations and alerts
          </CardDescription>
        </CardHeader>
        <CardContent>
          {violations.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
              <p>No violations detected</p>
              <p className="text-sm">System is running cleanly</p>
            </div>
          ) : (
            <div className="space-y-4">
              {violations.slice(0, 10).map((violation, index) => (
                <div key={index} className="flex items-start justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      {getSeverityBadge(violation.severity)}
                      <span className="text-sm font-medium">{violation.type}</span>
                      <span className="text-xs text-muted-foreground">
                        {violation.collectionName}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {violation.description}
                    </p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      {violation.timestamp.toLocaleString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Footer */}
      <div className="text-center text-sm text-muted-foreground">
        Last updated: {lastRefresh.toLocaleTimeString()}
      </div>
    </div>
  );
}
