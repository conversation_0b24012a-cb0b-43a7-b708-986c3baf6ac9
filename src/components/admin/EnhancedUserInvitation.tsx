/**
 * Enhanced User Invitation Component
 * User invitation system with subscription enforcement
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { 
  UserPlus, 
  Mail, 
  AlertTriangle, 
  CheckCircle, 
  Crown,
  Users,
  Trash2
} from 'lucide-react';
import { useUserLimits, useInvitationLimits, useEnforcementActions } from '@/hooks/useSubscriptionEnforcement';
import { UserLimitGate, EnforcementDialog } from '@/components/subscription/SubscriptionEnforcementComponents';
import { usePersonaManagement } from '@/hooks/usePersonaPermissions';
import { EnhancedEmailService } from '@/services/enhancedEmailService';
import { useAuth } from '@/contexts/auth-context';
import { emailService } from '@/lib/email-service';
import { useSubscriptionLimits } from '@/hooks/useSubscriptionLimits';
import { useToast } from '@/hooks/use-toast';

// ===== TYPES =====

interface InvitationFormData {
  email: string;
  firstName: string;
  lastName: string;
  personaId: string;
  message?: string;
}

interface EnhancedUserInvitationProps {
  onInvitationSent?: (invitation: any) => void;
  className?: string;
}

interface BulkInvitationProps {
  onInvitationsSent?: (invitations: any[]) => void;
  className?: string;
}

// ===== ENHANCED USER INVITATION =====

export const EnhancedUserInvitation: React.FC<EnhancedUserInvitationProps> = ({
  onInvitationSent,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState<InvitationFormData>({
    email: '',
    firstName: '',
    lastName: '',
    personaId: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showEnforcementDialog, setShowEnforcementDialog] = useState(false);

  const { canAddUsers, availableSlots, isAtLimit } = useUserLimits();
  const { canSendInvitations } = useInvitationLimits();
  const { checkAction } = useEnforcementActions();
  const { personas } = usePersonaManagement();
  const { user } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!canAddUsers(1) || !canSendInvitations(1)) {
      const action = await checkAction(1);
      if (action.type === 'block') {
        setShowEnforcementDialog(true);
        return;
      }
    }

    setIsSubmitting(true);
    setError(null);

    try {
      if (!user?.tenantId) {
        throw new Error('User tenant ID not found');
      }

      // Create enhanced email service instance
      const emailService = new EnhancedEmailService(user.tenantId);

      // Generate invitation URL and expiration date
      const invitationToken = `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const invitationUrl = `${window.location.origin}/invitation/${invitationToken}`;
      const expirationDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

      // Get persona name
      const selectedPersona = personas.find(p => p.id === formData.personaId);
      const roleName = selectedPersona?.name || 'Team Member';

      // Send invitation email
      const result = await emailService.sendInvitationEmail(
        formData.email,
        `${formData.firstName} ${formData.lastName}`,
        user.displayName || user.email || 'Team Admin',
        roleName,
        invitationUrl,
        expirationDate.toLocaleDateString()
      );

      if (!result.success) {
        throw new Error(result.error || 'Failed to send invitation email');
      }

      // Create invitation record
      const invitation = {
        id: invitationToken,
        ...formData,
        status: 'pending',
        sentAt: new Date(),
        expiresAt: expirationDate,
        invitationUrl,
        messageId: result.messageId
      };

      onInvitationSent?.(invitation);
      
      // Reset form
      setFormData({
        email: '',
        firstName: '',
        lastName: '',
        personaId: '',
        message: ''
      });
      
      setIsOpen(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send invitation');
    } finally {
      setIsSubmitting(false);
    }
  };

  const isFormValid = formData.email && formData.firstName && formData.lastName && formData.personaId;

  return (
    <div className={className}>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <UserLimitGate requestedUsers={1} showUpgradePrompt={false}>
            <Button className="flex items-center gap-2">
              <UserPlus className="h-4 w-4" />
              Invite User
              {availableSlots > 0 && availableSlots < 10 && (
                <Badge variant="secondary" className="ml-2">
                  {availableSlots} left
                </Badge>
              )}
            </Button>
          </UserLimitGate>
        </DialogTrigger>

        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Invite New User
            </DialogTitle>
            <DialogDescription>
              Send an invitation to join your organization
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Email */}
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
                required
              />
            </div>

            {/* Name Fields */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                  placeholder="John"
                  required
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                  placeholder="Doe"
                  required
                />
              </div>
            </div>

            {/* Persona Selection */}
            <div>
              <Label htmlFor="persona">Role</Label>
              <Select value={formData.personaId} onValueChange={(value) => setFormData(prev => ({ ...prev, personaId: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a role..." />
                </SelectTrigger>
                <SelectContent>
                  {personas.map(persona => (
                    <SelectItem key={persona.id} value={persona.id}>
                      {persona.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Optional Message */}
            <div>
              <Label htmlFor="message">Personal Message (Optional)</Label>
              <textarea
                id="message"
                className="w-full p-2 border rounded-md"
                rows={3}
                value={formData.message}
                onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                placeholder="Welcome to our team! We're excited to have you join us."
              />
            </div>

            {/* Error Display */}
            {error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Limit Warning */}
            {isAtLimit && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  You've reached your user limit. Upgrade to invite more users.
                </AlertDescription>
              </Alert>
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={!isFormValid || isSubmitting || isAtLimit}
                className="flex items-center gap-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Sending...
                  </>
                ) : (
                  <>
                    <Mail className="h-4 w-4" />
                    Send Invitation
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Enforcement Dialog */}
      <EnforcementDialog
        isOpen={showEnforcementDialog}
        onClose={() => setShowEnforcementDialog(false)}
        action={{
          type: 'block',
          message: 'You cannot invite more users. Your subscription limit has been reached.',
          upgradeUrl: '/billing/upgrade'
        }}
        onUpgrade={() => {
          window.location.href = '/billing/upgrade';
        }}
      />
    </div>
  );
};

// ===== BULK INVITATION COMPONENT =====

export const BulkUserInvitation: React.FC<BulkInvitationProps> = ({
  onInvitationsSent,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [emailList, setEmailList] = useState('');
  const [selectedPersonaId, setSelectedPersonaId] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { canAddUsers, availableSlots } = useUserLimits();
  const { personas } = usePersonaManagement();
  const { user } = useAuth();

  const emailAddresses = emailList
    .split('\n')
    .map(email => email.trim())
    .filter(email => email && email.includes('@'));

  const canInviteAll = canAddUsers(emailAddresses.length);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!canInviteAll) {
      setError(`Cannot invite ${emailAddresses.length} users. Only ${availableSlots} slots available.`);
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      if (!user?.tenantId) {
        throw new Error('User tenant ID not found');
      }

      // Create enhanced email service instance
      const emailService = new EnhancedEmailService(user.tenantId);

      // Get persona name
      const selectedPersona = personas.find(p => p.id === selectedPersonaId);
      const roleName = selectedPersona?.name || 'Team Member';

      // Send invitations to all email addresses
      const invitations = [];
      const errors = [];

      for (const email of emailAddresses) {
        try {
          // Generate invitation token and URL
          const invitationToken = `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          const invitationUrl = `${window.location.origin}/invitation/${invitationToken}`;
          const expirationDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

          // Send invitation email
          const result = await emailService.sendInvitationEmail(
            email,
            email.split('@')[0], // Use email prefix as name
            user.displayName || user.email || 'Team Admin',
            roleName,
            invitationUrl,
            expirationDate.toLocaleDateString()
          );

          if (result.success) {
            invitations.push({
              id: invitationToken,
              email,
              personaId: selectedPersonaId,
              status: 'pending',
              sentAt: new Date(),
              expiresAt: expirationDate,
              invitationUrl,
              messageId: result.messageId
            });
          } else {
            errors.push(`Failed to send to ${email}: ${result.error}`);
          }
        } catch (error) {
          errors.push(`Error sending to ${email}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      if (errors.length > 0) {
        setError(`Some invitations failed: ${errors.join(', ')}`);
      }

      if (invitations.length > 0) {
        onInvitationsSent?.(invitations);
      }
      
      setEmailList('');
      setSelectedPersonaId('');
      setIsOpen(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send invitations');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={className}>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Bulk Invite
          </Button>
        </DialogTrigger>

        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Bulk User Invitation</DialogTitle>
            <DialogDescription>
              Invite multiple users at once by entering their email addresses
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="emails">Email Addresses (one per line)</Label>
              <textarea
                id="emails"
                className="w-full p-2 border rounded-md"
                rows={6}
                value={emailList}
                onChange={(e) => setEmailList(e.target.value)}
                placeholder="<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
              />
              <div className="text-sm text-gray-600 mt-1">
                {emailAddresses.length} valid email addresses found
              </div>
            </div>

            <div>
              <Label htmlFor="bulkPersona">Role for All Users</Label>
              <Select value={selectedPersonaId} onValueChange={setSelectedPersonaId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a role..." />
                </SelectTrigger>
                <SelectContent>
                  {personas.map(persona => (
                    <SelectItem key={persona.id} value={persona.id}>
                      {persona.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {!canInviteAll && emailAddresses.length > 0 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Cannot invite {emailAddresses.length} users. Only {availableSlots} slots available.
                </AlertDescription>
              </Alert>
            )}

            {error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={!selectedPersonaId || emailAddresses.length === 0 || !canInviteAll || isSubmitting}
              >
                {isSubmitting ? 'Sending...' : `Send ${emailAddresses.length} Invitations`}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};


