/**
 * Email Template Preview Component
 * Professional preview and testing interface for EVEXA email templates
 */

import React, { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { 
  Mail, 
  Eye, 
  Send, 
  Code, 
  Smartphone, 
  Monitor, 
  CheckCircle,
  AlertTriangle,
  Copy
} from 'lucide-react';
import { useEmailTemplates, useSystemEmail } from '@/hooks/useSystemEmail';
import { useTenantBranding } from '@/contexts/tenant-context';
import { replaceTemplateVariables } from '@/templates/system-email-templates';

// ===== TYPES =====

interface EmailTemplatePreviewProps {
  className?: string;
}

interface TemplateTestProps {
  templateId: string;
  onClose: () => void;
}

// ===== EMAIL TEMPLATE PREVIEW =====

export const EmailTemplatePreview: React.FC<EmailTemplatePreviewProps> = ({
  className
}) => {
  const { templates, isLoading, error } = useEmailTemplates();
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>('');
  const [previewVariables, setPreviewVariables] = useState<Record<string, string>>({});
  const [showTestDialog, setShowTestDialog] = useState(false);

  const selectedTemplate = useMemo(() => {
    return templates.find(t => t.id === selectedTemplateId) || null;
  }, [templates, selectedTemplateId]);

  const branding = useTenantBranding();

  // Generate sample variables for preview
  const sampleVariables = useMemo(() => {
    if (!selectedTemplate) return {};

    // Use real tenant data for preview
    const variables: Record<string, string> = {
      recipientName: user?.displayName || user?.email?.split('@')[0] || 'User',
      senderName: user?.displayName || 'Admin',
      tenantName: branding?.companyName || 'Your Company',
      tenantLogo: branding?.logoUrl || '',
      supportEmail: branding?.supportEmail || '<EMAIL>',
      invitationUrl: `${window.location.origin}/invitation/preview-token`,
      loginUrl: `${window.location.origin}/dashboard`,
      resetUrl: `${window.location.origin}/reset-password?token=preview-token`,
      roleName: 'Team Member',
      expirationDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString(),
      suspensionReason: 'Security policy violation'
    };

    return variables;
  }, [selectedTemplate, branding, user]);

  // Merge sample variables with user input
  const mergedVariables = useMemo(() => {
    return { ...sampleVariables, ...previewVariables };
  }, [sampleVariables, previewVariables]);

  // Generate preview content
  const previewContent = useMemo(() => {
    if (!selectedTemplate) return { subject: '', html: '', text: '' };

    return {
      subject: replaceTemplateVariables(selectedTemplate.subject || '', mergedVariables),
      html: replaceTemplateVariables(selectedTemplate.htmlContent || '', mergedVariables),
      text: replaceTemplateVariables(selectedTemplate.textContent || '', mergedVariables)
    };
  }, [selectedTemplate, mergedVariables]);

  if (isLoading) {
    return (
      <div className={`animate-pulse space-y-4 ${className}`}>
        <div className="h-8 bg-gray-200 rounded w-1/3"></div>
        <div className="h-64 bg-gray-200 rounded"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Failed to load email templates: {error}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Template Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Template Preview
          </CardTitle>
          <CardDescription>
            Preview and test EVEXA system email templates with your branding
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="template-select">Select Template</Label>
            <Select value={selectedTemplateId} onValueChange={setSelectedTemplateId}>
              <SelectTrigger>
                <SelectValue placeholder="Choose an email template..." />
              </SelectTrigger>
              <SelectContent>
                {templates.map(template => (
                  <SelectItem key={template.id} value={template.id}>
                    <div className="flex items-center gap-2">
                      <span>{template.name}</span>
                      <Badge variant="secondary" className="text-xs">
                        {template.type}
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedTemplate && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowTestDialog(true)}
                className="flex items-center gap-2"
              >
                <Send className="h-4 w-4" />
                Send Test Email
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(previewContent.html);
                }}
                className="flex items-center gap-2"
              >
                <Copy className="h-4 w-4" />
                Copy HTML
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {selectedTemplate && (
        <>
          {/* Variable Editor */}
          <Card>
            <CardHeader>
              <CardTitle>Template Variables</CardTitle>
              <CardDescription>
                Customize the variables to see how the email will look
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {selectedTemplate.variables
                  .filter(variable => variable.required || sampleVariables[variable.name])
                  .map(variable => (
                    <div key={variable.name}>
                      <Label htmlFor={variable.name}>
                        {variable.label}
                        {variable.required && <span className="text-red-500 ml-1">*</span>}
                      </Label>
                      <Input
                        id={variable.name}
                        value={previewVariables[variable.name] || sampleVariables[variable.name] || ''}
                        onChange={(e) => setPreviewVariables(prev => ({
                          ...prev,
                          [variable.name]: e.target.value
                        }))}
                        placeholder={variable.description}
                      />
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>

          {/* Email Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Email Preview</CardTitle>
              <CardDescription>
                Preview how the email will appear to recipients
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="desktop" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="desktop" className="flex items-center gap-2">
                    <Monitor className="h-4 w-4" />
                    Desktop
                  </TabsTrigger>
                  <TabsTrigger value="mobile" className="flex items-center gap-2">
                    <Smartphone className="h-4 w-4" />
                    Mobile
                  </TabsTrigger>
                  <TabsTrigger value="code" className="flex items-center gap-2">
                    <Code className="h-4 w-4" />
                    HTML
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="desktop" className="mt-4">
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="bg-white rounded shadow-sm">
                      {/* Email Header */}
                      <div className="border-b p-4 bg-gray-100">
                        <div className="text-sm text-gray-600">Subject:</div>
                        <div className="font-medium">{previewContent.subject}</div>
                      </div>
                      
                      {/* Email Content */}
                      <div 
                        className="email-preview"
                        dangerouslySetInnerHTML={{ __html: previewContent.html }}
                        style={{ maxWidth: '600px', margin: '0 auto' }}
                      />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="mobile" className="mt-4">
                  <div className="border rounded-lg p-4 bg-gray-50 flex justify-center">
                    <div className="w-80 bg-white rounded shadow-sm">
                      {/* Email Header */}
                      <div className="border-b p-3 bg-gray-100">
                        <div className="text-xs text-gray-600">Subject:</div>
                        <div className="font-medium text-sm">{previewContent.subject}</div>
                      </div>
                      
                      {/* Email Content */}
                      <div 
                        className="email-preview text-sm"
                        dangerouslySetInnerHTML={{ __html: previewContent.html }}
                      />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="code" className="mt-4">
                  <div className="border rounded-lg">
                    <Tabs defaultValue="html" className="w-full">
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="html">HTML</TabsTrigger>
                        <TabsTrigger value="text">Plain Text</TabsTrigger>
                      </TabsList>
                      
                      <TabsContent value="html" className="p-4">
                        <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96">
                          <code>{previewContent.html}</code>
                        </pre>
                      </TabsContent>
                      
                      <TabsContent value="text" className="p-4">
                        <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96">
                          <code>{previewContent.text}</code>
                        </pre>
                      </TabsContent>
                    </Tabs>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </>
      )}

      {/* Test Email Dialog */}
      {selectedTemplate && (
        <TemplateTestDialog
          templateId={selectedTemplate.id}
          isOpen={showTestDialog}
          onClose={() => setShowTestDialog(false)}
        />
      )}
    </div>
  );
};

// ===== TEMPLATE TEST DIALOG =====

const TemplateTestDialog: React.FC<TemplateTestProps & { isOpen: boolean; onClose: () => void }> = ({
  templateId,
  isOpen,
  onClose
}) => {
  const [testEmail, setTestEmail] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);
  const { sendEmail } = useSystemEmail();

  const handleSendTest = async () => {
    if (!testEmail) return;

    setIsSending(true);
    setResult(null);

    try {
      const emailResult = await sendEmail({
        to: testEmail,
        templateId,
        variables: {
          recipientName: 'Test User',
          senderName: 'EVEXA Team'
        },
        priority: 'normal'
      });

      setResult({
        success: emailResult.success,
        message: emailResult.success 
          ? 'Test email sent successfully!' 
          : emailResult.error || 'Failed to send test email'
      });

    } catch (error) {
      setResult({
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Send Test Email</DialogTitle>
          <DialogDescription>
            Send a test email to verify the template renders correctly
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label htmlFor="test-email">Test Email Address</Label>
            <Input
              id="test-email"
              type="email"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>

          {result && (
            <Alert variant={result.success ? "default" : "destructive"}>
              {result.success ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertTriangle className="h-4 w-4" />
              )}
              <AlertDescription>{result.message}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleSendTest} 
            disabled={!testEmail || isSending}
            className="flex items-center gap-2"
          >
            {isSending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Sending...
              </>
            ) : (
              <>
                <Send className="h-4 w-4" />
                Send Test
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EmailTemplatePreview;
