'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { designTokens } from '@/lib/design-tokens';

export function ModernDesignTokensShowcase() {
  return (
    <div className="space-y-8 p-6">
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold gradient-primary bg-clip-text text-transparent">
          EVEXA Design Tokens v2.0
        </h1>
        <p className="text-muted-foreground text-lg">
          Modern, comprehensive design system with enhanced colors, gradients, and effects
        </p>
      </div>

      {/* Enhanced Color Palettes */}
      <Card>
        <CardHeader>
          <CardTitle>Enhanced Color Palettes</CardTitle>
          <CardDescription>
            Expanded color scales with better accessibility and contrast ratios
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Primary Colors */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Primary Colors</h3>
            <div className="grid grid-cols-12 gap-2">
              {Object.entries(designTokens.colors.primary).map(([key, value]) => (
                key !== 'DEFAULT' && key !== 'foreground' && (
                  <div key={key} className="text-center">
                    <div 
                      className="w-full h-12 rounded-md border"
                      style={{ backgroundColor: value }}
                    />
                    <p className="text-xs mt-1">{key}</p>
                  </div>
                )
              ))}
            </div>
          </div>

          {/* Semantic Colors */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Semantic Colors</h3>
            <div className="grid grid-cols-4 gap-4">
              {['success', 'warning', 'error', 'info'].map((color) => (
                <div key={color} className="space-y-2">
                  <h4 className="font-medium capitalize">{color}</h4>
                  <div className="grid grid-cols-6 gap-1">
                    {Object.entries(designTokens.colors[color as keyof typeof designTokens.colors]).map(([key, value]) => (
                      key !== 'DEFAULT' && key !== 'foreground' && (
                        <div key={key} className="text-center">
                          <div 
                            className="w-full h-8 rounded border"
                            style={{ backgroundColor: value }}
                          />
                          <p className="text-xs">{key}</p>
                        </div>
                      )
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Modern Gradients */}
      <Card>
        <CardHeader>
          <CardTitle>Modern Gradients</CardTitle>
          <CardDescription>
            Beautiful gradient combinations for modern UI elements
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {Object.entries(designTokens.gradients.linear).map(([name, gradient]) => (
              <div key={name} className="space-y-2">
                <div 
                  className="h-20 rounded-lg border"
                  style={{ background: gradient }}
                />
                <p className="text-sm font-medium capitalize">{name.replace('-', ' ')}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Glassmorphism Effects */}
      <Card>
        <CardHeader>
          <CardTitle>Glassmorphism Effects</CardTitle>
          <CardDescription>
            Modern glass-like effects with backdrop blur
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative h-64 bg-gradient-ocean rounded-lg overflow-hidden">
            <div className="absolute inset-4 grid grid-cols-2 gap-4">
              {Object.entries(designTokens.glassmorphism.surface).map(([name, styles]) => (
                <div 
                  key={name}
                  className="p-4 rounded-lg flex items-center justify-center"
                  style={styles}
                >
                  <div className="text-center">
                    <p className="font-medium text-white">{name}</p>
                    <p className="text-xs text-white/80">Glass Effect</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Shadows */}
      <Card>
        <CardHeader>
          <CardTitle>Enhanced Shadow System</CardTitle>
          <CardDescription>
            Comprehensive shadow tokens including glow effects and elevation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {/* Standard Shadows */}
            <div className="space-y-4">
              <h4 className="font-medium">Standard</h4>
              {['sm', 'DEFAULT', 'md', 'lg', 'xl'].map((size) => (
                <div 
                  key={size}
                  className="h-12 bg-white rounded-lg flex items-center justify-center text-sm"
                  style={{ boxShadow: designTokens.boxShadow[size as keyof typeof designTokens.boxShadow] }}
                >
                  {size === 'DEFAULT' ? 'default' : size}
                </div>
              ))}
            </div>

            {/* Glow Shadows */}
            <div className="space-y-4">
              <h4 className="font-medium">Glow Effects</h4>
              {Object.entries(designTokens.boxShadow.glow).map(([name, shadow]) => (
                <div 
                  key={name}
                  className="h-12 bg-white rounded-lg flex items-center justify-center text-sm"
                  style={{ boxShadow: shadow }}
                >
                  {name}
                </div>
              ))}
            </div>

            {/* Glass Shadows */}
            <div className="space-y-4">
              <h4 className="font-medium">Glass</h4>
              {Object.entries(designTokens.boxShadow.glass).map(([name, shadow]) => (
                <div 
                  key={name}
                  className="h-12 bg-white/10 backdrop-blur-sm rounded-lg flex items-center justify-center text-sm border border-white/20"
                  style={{ boxShadow: shadow }}
                >
                  {name}
                </div>
              ))}
            </div>

            {/* Elevation Shadows */}
            <div className="space-y-4">
              <h4 className="font-medium">Elevation</h4>
              {Object.entries(designTokens.boxShadow.elevation).map(([level, shadow]) => (
                <div 
                  key={level}
                  className="h-12 bg-white rounded-lg flex items-center justify-center text-sm"
                  style={{ boxShadow: shadow }}
                >
                  Level {level}
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Interactive Components */}
      <Card>
        <CardHeader>
          <CardTitle>Interactive Components</CardTitle>
          <CardDescription>
            Components showcasing modern interactions and animations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button className="interactive-scale shadow-glow">
              Glow Button
            </Button>
            <Button variant="outline" className="glass-light interactive-scale">
              Glass Button
            </Button>
            <Button className="gradient-sunset text-white interactive-scale">
              Gradient Button
            </Button>
            <Button variant="ghost" className="interactive-glow">
              Hover Glow
            </Button>
          </div>
          
          <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
            <Badge className="shadow-glow-accent">Glowing Badge</Badge>
            <Badge variant="outline" className="glass-medium">Glass Badge</Badge>
            <Badge className="gradient-rainbow text-white">Rainbow Badge</Badge>
            <Badge variant="secondary" className="interactive-scale">Interactive Badge</Badge>
          </div>
        </CardContent>
      </Card>

      {/* Animation Showcase */}
      <Card>
        <CardHeader>
          <CardTitle>Animation System</CardTitle>
          <CardDescription>
            Modern animation tokens with smooth easing curves
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="p-4 border rounded-lg animate-fade-in">
              <p className="text-sm font-medium">Fade In</p>
            </div>
            <div className="p-4 border rounded-lg animate-slide-in-up">
              <p className="text-sm font-medium">Slide Up</p>
            </div>
            <div className="p-4 border rounded-lg animate-scale-in">
              <p className="text-sm font-medium">Scale In</p>
            </div>
            <div className="p-4 border rounded-lg animate-bounce-in">
              <p className="text-sm font-medium">Bounce In</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default ModernDesignTokensShowcase;
