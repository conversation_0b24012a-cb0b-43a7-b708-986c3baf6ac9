/**
 * EVEXA Optimized Image Component
 * High-performance image component with lazy loading, progressive enhancement, and error handling
 */

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { useImageLazyLoad, usePerformanceMonitor } from '@/lib/performance-utils';

export interface OptimizedImageProps extends Omit<React.ImgHTMLAttributes<HTMLImageElement>, 'src' | 'loading'> {
  src: string;
  alt: string;
  placeholder?: string;
  blurDataURL?: string;
  priority?: boolean;
  quality?: number;
  sizes?: string;
  fill?: boolean;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  objectPosition?: string;
  onLoad?: () => void;
  onError?: () => void;
  fallback?: React.ReactNode;
  aspectRatio?: number;
  lazy?: boolean;
  progressive?: boolean;
  webp?: boolean;
  avif?: boolean;
}

export const OptimizedImage = React.forwardRef<HTMLImageElement, OptimizedImageProps>(({
  src,
  alt,
  placeholder,
  blurDataURL,
  priority = false,
  quality = 75,
  sizes,
  fill = false,
  objectFit = 'cover',
  objectPosition = 'center',
  onLoad,
  onError,
  fallback,
  aspectRatio,
  lazy = true,
  progressive = true,
  webp = true,
  avif = true,
  className,
  style,
  ...props
}, ref) => {
  usePerformanceMonitor('OptimizedImage');

  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(placeholder || blurDataURL || '');
  const imgRef = useRef<HTMLImageElement>(null);

  // Use lazy loading if not priority
  const { ref: lazyRef, inView } = useImageLazyLoad(
    src,
    placeholder || blurDataURL
  );

  // Combine refs
  const combinedRef = (node: HTMLImageElement) => {
    if (ref) {
      if (typeof ref === 'function') {
        ref(node);
      } else {
        ref.current = node;
      }
    }
    imgRef.current = node;
    if (!priority && lazy) {
      lazyRef(node);
    }
  };

  // Generate optimized src URLs
  const generateOptimizedSrc = (originalSrc: string, format?: 'webp' | 'avif') => {
    // This would integrate with your image optimization service
    // For now, return the original src
    if (format && originalSrc.includes('.')) {
      const parts = originalSrc.split('.');
      const extension = parts.pop();
      return `${parts.join('.')}.${format}`;
    }
    return originalSrc;
  };

  // Create srcSet for responsive images
  const createSrcSet = (originalSrc: string) => {
    const breakpoints = [640, 768, 1024, 1280, 1536];
    return breakpoints
      .map(width => `${originalSrc}?w=${width}&q=${quality} ${width}w`)
      .join(', ');
  };

  // Handle image loading
  useEffect(() => {
    if (!priority && lazy && !inView) return;
    if (priority || inView || !lazy) {
      loadImage();
    }
  }, [src, inView, priority, lazy]);

  const loadImage = () => {
    if (!src) return;

    const img = new Image();
    
    // Set up progressive loading
    if (progressive && webp) {
      // Try WebP first
      const webpSrc = generateOptimizedSrc(src, 'webp');
      img.src = webpSrc;
      
      img.onload = () => {
        setCurrentSrc(webpSrc);
        setIsLoaded(true);
        onLoad?.();
      };
      
      img.onerror = () => {
        // Fallback to AVIF
        if (avif) {
          const avifSrc = generateOptimizedSrc(src, 'avif');
          img.src = avifSrc;
          
          img.onload = () => {
            setCurrentSrc(avifSrc);
            setIsLoaded(true);
            onLoad?.();
          };
          
          img.onerror = () => {
            // Final fallback to original
            img.src = src;
            img.onload = () => {
              setCurrentSrc(src);
              setIsLoaded(true);
              onLoad?.();
            };
            img.onerror = handleError;
          };
        } else {
          // Direct fallback to original
          img.src = src;
          img.onload = () => {
            setCurrentSrc(src);
            setIsLoaded(true);
            onLoad?.();
          };
          img.onerror = handleError;
        }
      };
    } else {
      // Simple loading
      img.src = src;
      img.onload = () => {
        setCurrentSrc(src);
        setIsLoaded(true);
        onLoad?.();
      };
      img.onerror = handleError;
    }
  };

  const handleError = () => {
    setHasError(true);
    setIsLoaded(false);
    onError?.();
  };

  // Calculate aspect ratio styles
  const aspectRatioStyles = aspectRatio ? {
    aspectRatio: aspectRatio.toString(),
  } : {};

  // Fill styles
  const fillStyles = fill ? {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
  } : {};

  if (hasError && fallback) {
    return <>{fallback}</>;
  }

  if (hasError) {
    return (
      <div 
        className={cn(
          'flex items-center justify-center bg-muted text-muted-foreground',
          className
        )}
        style={{
          ...aspectRatioStyles,
          ...fillStyles,
          ...style,
        }}
      >
        <span className="text-sm">Failed to load image</span>
      </div>
    );
  }

  return (
    <div
      className={cn(
        'relative overflow-hidden',
        fill && 'absolute inset-0',
        className
      )}
      style={{
        ...aspectRatioStyles,
        ...(!fill ? style : {}),
      }}
    >
      {/* Blur placeholder */}
      {blurDataURL && !isLoaded && (
        <img
          src={blurDataURL}
          alt=""
          className={cn(
            'absolute inset-0 w-full h-full object-cover filter blur-sm scale-110 transition-opacity duration-300',
            isLoaded && 'opacity-0'
          )}
          style={{
            objectFit,
            objectPosition,
          }}
        />
      )}

      {/* Main image */}
      <img
        ref={combinedRef}
        src={currentSrc}
        alt={alt}
        className={cn(
          'transition-opacity duration-300',
          !isLoaded && 'opacity-0',
          isLoaded && 'opacity-100',
          fill && 'absolute inset-0 w-full h-full'
        )}
        style={{
          objectFit,
          objectPosition,
          ...(fill ? fillStyles : {}),
          ...(fill ? style : {}),
        }}
        sizes={sizes}
        srcSet={createSrcSet(src)}
        loading={priority ? 'eager' : 'lazy'}
        decoding="async"
        {...props}
      />

      {/* Loading indicator */}
      {!isLoaded && !hasError && !blurDataURL && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      )}
    </div>
  );
});

OptimizedImage.displayName = 'OptimizedImage';

// Image gallery component with virtualization
export interface ImageGalleryProps {
  images: Array<{
    src: string;
    alt: string;
    placeholder?: string;
    aspectRatio?: number;
  }>;
  columns?: number;
  gap?: number;
  height?: number;
  className?: string;
  onImageClick?: (image: any, index: number) => void;
}

export const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  columns = 3,
  gap = 16,
  height = 600,
  className,
  onImageClick,
}) => {
  usePerformanceMonitor('ImageGallery');

  return (
    <div className={cn('grid gap-4', className)} style={{ 
      gridTemplateColumns: `repeat(${columns}, 1fr)`,
      gap: `${gap}px`,
      height: `${height}px`,
      overflowY: 'auto'
    }}>
      {images.map((image, index) => (
        <div
          key={index}
          className="cursor-pointer hover:opacity-80 transition-opacity"
          onClick={() => onImageClick?.(image, index)}
        >
          <OptimizedImage
            src={image.src}
            alt={image.alt}
            placeholder={image.placeholder}
            aspectRatio={image.aspectRatio || 1}
            className="w-full h-full rounded-lg"
          />
        </div>
      ))}
    </div>
  );
};

export default OptimizedImage;
