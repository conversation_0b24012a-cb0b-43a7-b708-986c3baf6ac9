/**
 * EVEXA Enhanced Skeleton Components
 * Advanced skeleton loading states with animations and layouts
 */

'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { skeletonVariants, type SkeletonVariants } from '@/lib/component-variants';
import { usePerformanceMonitor } from '@/lib/performance-utils';

// Enhanced Skeleton Component
export interface SkeletonProps extends SkeletonVariants {
  className?: string;
  width?: string | number;
  height?: string | number;
  children?: React.ReactNode;
}

export const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(({
  variant = "default",
  shape = "default",
  className,
  width,
  height,
  children,
  ...props
}, ref) => {
  usePerformanceMonitor('Skeleton');

  const style = {
    ...(width && { width: typeof width === 'number' ? `${width}px` : width }),
    ...(height && { height: typeof height === 'number' ? `${height}px` : height }),
  };

  return (
    <div
      ref={ref}
      className={cn(skeletonVariants({ variant, shape }), className)}
      style={style}
      {...props}
    >
      {children}
    </div>
  );
});

Skeleton.displayName = 'Skeleton';

// Skeleton Text Component
export interface SkeletonTextProps {
  lines?: number;
  className?: string;
  variant?: SkeletonVariants['variant'];
  lastLineWidth?: string;
}

export const SkeletonText: React.FC<SkeletonTextProps> = ({
  lines = 3,
  className,
  variant = "default",
  lastLineWidth = "60%",
}) => {
  usePerformanceMonitor('SkeletonText');

  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: lines }, (_, i) => (
        <Skeleton
          key={i}
          variant={variant}
          shape="text"
          className={cn(
            "h-4",
            i === lines - 1 && "w-[var(--last-line-width)]"
          )}
          style={
            i === lines - 1
              ? { '--last-line-width': lastLineWidth } as React.CSSProperties
              : undefined
          }
        />
      ))}
    </div>
  );
};

// Skeleton Avatar Component
export interface SkeletonAvatarProps {
  size?: 'xs' | 'sm' | 'default' | 'lg' | 'xl' | '2xl';
  variant?: SkeletonVariants['variant'];
  className?: string;
}

export const SkeletonAvatar: React.FC<SkeletonAvatarProps> = ({
  size = "default",
  variant = "default",
  className,
}) => {
  usePerformanceMonitor('SkeletonAvatar');

  const sizeClasses = {
    xs: "h-6 w-6",
    sm: "h-8 w-8",
    default: "h-10 w-10",
    lg: "h-12 w-12",
    xl: "h-16 w-16",
    "2xl": "h-20 w-20"
  };

  return (
    <Skeleton
      variant={variant}
      shape="circle"
      className={cn(sizeClasses[size], className)}
    />
  );
};

// Skeleton Card Component
export interface SkeletonCardProps {
  showAvatar?: boolean;
  showImage?: boolean;
  textLines?: number;
  className?: string;
  variant?: SkeletonVariants['variant'];
  imageHeight?: string;
}

export const SkeletonCard: React.FC<SkeletonCardProps> = ({
  showAvatar = false,
  showImage = false,
  textLines = 3,
  className,
  variant = "default",
  imageHeight = "200px",
}) => {
  usePerformanceMonitor('SkeletonCard');

  return (
    <div className={cn("p-6 border rounded-lg bg-card space-y-4", className)}>
      {showImage && (
        <Skeleton
          variant={variant}
          shape="card"
          height={imageHeight}
          className="w-full"
        />
      )}
      
      {showAvatar && (
        <div className="flex items-center space-x-4">
          <SkeletonAvatar variant={variant} />
          <div className="space-y-2 flex-1">
            <Skeleton variant={variant} shape="text" className="h-4 w-1/4" />
            <Skeleton variant={variant} shape="text" className="h-3 w-1/3" />
          </div>
        </div>
      )}

      <div className="space-y-3">
        <Skeleton variant={variant} shape="text" className="h-5 w-3/4" />
        <SkeletonText lines={textLines} variant={variant} />
      </div>

      <div className="flex space-x-2 pt-2">
        <Skeleton variant={variant} shape="button" className="w-20" />
        <Skeleton variant={variant} shape="button" className="w-16" />
      </div>
    </div>
  );
};

// Skeleton Table Component
export interface SkeletonTableProps {
  rows?: number;
  columns?: number;
  showHeader?: boolean;
  className?: string;
  variant?: SkeletonVariants['variant'];
}

export const SkeletonTable: React.FC<SkeletonTableProps> = ({
  rows = 5,
  columns = 4,
  showHeader = true,
  className,
  variant = "default",
}) => {
  usePerformanceMonitor('SkeletonTable');

  return (
    <div className={cn("border rounded-lg overflow-hidden", className)}>
      {showHeader && (
        <div className="border-b bg-muted/50 p-4">
          <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
            {Array.from({ length: columns }, (_, i) => (
              <Skeleton key={i} variant={variant} shape="text" className="h-4" />
            ))}
          </div>
        </div>
      )}
      
      <div className="divide-y">
        {Array.from({ length: rows }, (_, rowIndex) => (
          <div key={rowIndex} className="p-4">
            <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
              {Array.from({ length: columns }, (_, colIndex) => (
                <Skeleton
                  key={colIndex}
                  variant={variant}
                  shape="text"
                  className="h-4"
                  style={{ animationDelay: `${(rowIndex * columns + colIndex) * 0.05}s` }}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Skeleton List Component
export interface SkeletonListProps {
  items?: number;
  showAvatar?: boolean;
  showIcon?: boolean;
  className?: string;
  variant?: SkeletonVariants['variant'];
}

export const SkeletonList: React.FC<SkeletonListProps> = ({
  items = 5,
  showAvatar = false,
  showIcon = false,
  className,
  variant = "default",
}) => {
  usePerformanceMonitor('SkeletonList');

  return (
    <div className={cn("space-y-3", className)}>
      {Array.from({ length: items }, (_, i) => (
        <div key={i} className="flex items-center space-x-4 p-3 border rounded-lg">
          {showAvatar && <SkeletonAvatar variant={variant} size="sm" />}
          {showIcon && !showAvatar && (
            <Skeleton variant={variant} shape="square" className="h-5 w-5" />
          )}
          
          <div className="flex-1 space-y-2">
            <Skeleton
              variant={variant}
              shape="text"
              className="h-4 w-3/4"
              style={{ animationDelay: `${i * 0.1}s` }}
            />
            <Skeleton
              variant={variant}
              shape="text"
              className="h-3 w-1/2"
              style={{ animationDelay: `${i * 0.1 + 0.05}s` }}
            />
          </div>
          
          <Skeleton
            variant={variant}
            shape="text"
            className="h-4 w-16"
            style={{ animationDelay: `${i * 0.1 + 0.1}s` }}
          />
        </div>
      ))}
    </div>
  );
};

// Skeleton Dashboard Component
export interface SkeletonDashboardProps {
  showStats?: boolean;
  showChart?: boolean;
  showTable?: boolean;
  className?: string;
  variant?: SkeletonVariants['variant'];
}

export const SkeletonDashboard: React.FC<SkeletonDashboardProps> = ({
  showStats = true,
  showChart = true,
  showTable = true,
  className,
  variant = "shimmer",
}) => {
  usePerformanceMonitor('SkeletonDashboard');

  return (
    <div className={cn("space-y-6", className)}>
      {showStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Array.from({ length: 4 }, (_, i) => (
            <div key={i} className="p-6 border rounded-lg bg-card space-y-3">
              <div className="flex items-center justify-between">
                <Skeleton variant={variant} shape="text" className="h-4 w-20" />
                <Skeleton variant={variant} shape="square" className="h-5 w-5" />
              </div>
              <Skeleton variant={variant} shape="text" className="h-8 w-16" />
              <Skeleton variant={variant} shape="text" className="h-3 w-24" />
            </div>
          ))}
        </div>
      )}

      {showChart && (
        <div className="p-6 border rounded-lg bg-card space-y-4">
          <div className="flex items-center justify-between">
            <Skeleton variant={variant} shape="text" className="h-6 w-32" />
            <Skeleton variant={variant} shape="button" className="w-24" />
          </div>
          <Skeleton variant={variant} shape="card" height="300px" className="w-full" />
        </div>
      )}

      {showTable && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Skeleton variant={variant} shape="text" className="h-6 w-40" />
            <Skeleton variant={variant} shape="button" className="w-28" />
          </div>
          <SkeletonTable variant={variant} />
        </div>
      )}
    </div>
  );
};

export default Skeleton;
