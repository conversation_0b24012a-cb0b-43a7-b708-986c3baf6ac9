"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { TimezoneSelector } from '@/components/ui/timezone-selector';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar as CalendarIcon, Clock, Globe } from 'lucide-react';
import { format, parse, isValid } from 'date-fns';
import { cn } from '@/lib/utils';

export interface DateTimePickerProps {
  value?: Date;
  onChange: (date: Date | undefined) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  showTime?: boolean;
  showTimezone?: boolean;
  timezone?: string;
  onTimezoneChange?: (timezone: string) => void;
  minDate?: Date;
  maxDate?: Date;
  dateFormat?: string;
  timeFormat?: '12' | '24';
  variant?: 'default' | 'compact' | 'inline';
  showPresets?: boolean;
  showClear?: boolean;
  label?: string;
  description?: string;
}

export function DateTimePicker({
  value,
  onChange,
  placeholder = "Pick a date",
  className,
  disabled = false,
  showTime = false,
  showTimezone = false,
  timezone,
  onTimezoneChange,
  minDate,
  maxDate,
  dateFormat = "PPP",
  timeFormat = '12',
  variant = 'default',
  showPresets = false,
  showClear = true,
  label,
  description
}: DateTimePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [timeValue, setTimeValue] = useState(() => {
    if (value) {
      return format(value, timeFormat === '12' ? 'hh:mm a' : 'HH:mm');
    }
    return '';
  });

  // Date presets
  const datePresets = [
    { label: 'Today', value: new Date() },
    { label: 'Tomorrow', value: new Date(Date.now() + 24 * 60 * 60 * 1000) },
    { label: 'Next Week', value: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) },
    { label: 'Next Month', value: new Date(new Date().getFullYear(), new Date().getMonth() + 1, new Date().getDate()) },
  ];

  // Generate time options
  const timeOptions = React.useMemo(() => {
    const options = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 15) {
        const time = new Date();
        time.setHours(hour, minute, 0, 0);
        
        const value24 = format(time, 'HH:mm');
        const value12 = format(time, 'hh:mm a');
        
        options.push({
          value: value24,
          label: timeFormat === '12' ? value12 : value24
        });
      }
    }
    return options;
  }, [timeFormat]);

  const handleDateSelect = (selectedDate: Date | undefined) => {
    if (!selectedDate) {
      onChange(undefined);
      return;
    }

    let newDate = selectedDate;

    // If we have a time value, apply it to the selected date
    if (showTime && timeValue) {
      try {
        const timeParts = timeValue.match(/(\d{1,2}):(\d{2})(?:\s*(AM|PM))?/i);
        if (timeParts) {
          let hours = parseInt(timeParts[1]);
          const minutes = parseInt(timeParts[2]);
          const ampm = timeParts[3];

          // Convert 12-hour to 24-hour format
          if (ampm) {
            if (ampm.toUpperCase() === 'PM' && hours !== 12) {
              hours += 12;
            } else if (ampm.toUpperCase() === 'AM' && hours === 12) {
              hours = 0;
            }
          }

          newDate = new Date(selectedDate);
          newDate.setHours(hours, minutes, 0, 0);
        }
      } catch (error) {
        console.warn('Invalid time format:', timeValue);
      }
    }

    onChange(newDate);
    if (!showTime) {
      setIsOpen(false);
    }
  };

  const handleTimeChange = (newTimeValue: string) => {
    setTimeValue(newTimeValue);
    
    if (value) {
      try {
        const timeParts = newTimeValue.match(/(\d{1,2}):(\d{2})(?:\s*(AM|PM))?/i);
        if (timeParts) {
          let hours = parseInt(timeParts[1]);
          const minutes = parseInt(timeParts[2]);
          const ampm = timeParts[3];

          // Convert 12-hour to 24-hour format
          if (ampm) {
            if (ampm.toUpperCase() === 'PM' && hours !== 12) {
              hours += 12;
            } else if (ampm.toUpperCase() === 'AM' && hours === 12) {
              hours = 0;
            }
          }

          const newDate = new Date(value);
          newDate.setHours(hours, minutes, 0, 0);
          onChange(newDate);
        }
      } catch (error) {
        console.warn('Invalid time format:', newTimeValue);
      }
    }
  };

  const formatDisplayValue = () => {
    if (!value) return placeholder;
    
    let displayValue = format(value, dateFormat);
    
    if (showTime) {
      const timeStr = format(value, timeFormat === '12' ? 'hh:mm a' : 'HH:mm');
      displayValue += ` at ${timeStr}`;
    }
    
    if (showTimezone && timezone) {
      try {
        const tzDisplay = new Intl.DateTimeFormat('en-US', {
          timeZone: timezone,
          timeZoneName: 'short'
        }).formatToParts(value).find(part => part.type === 'timeZoneName')?.value;
        
        if (tzDisplay) {
          displayValue += ` (${tzDisplay})`;
        }
      } catch (error) {
        console.warn('Invalid timezone:', timezone);
      }
    }
    
    return displayValue;
  };

  return (
    <div className={cn("space-y-2", className)}>
      {label && <Label className="text-sm font-medium">{label}</Label>}

      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal h-10",
              !value && "text-muted-foreground",
              variant === 'compact' && "h-9 text-sm"
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {formatDisplayValue()}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="flex">
            {/* Presets sidebar */}
            {showPresets && (
              <div className="border-r p-3 space-y-2 min-w-[120px]">
                <div className="text-sm font-medium text-muted-foreground mb-2">Quick Select</div>
                {datePresets.map((preset) => (
                  <Button
                    key={preset.label}
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start h-8 text-xs"
                    onClick={() => {
                      handleDateSelect(preset.value);
                      if (!showTime) setIsOpen(false);
                    }}
                  >
                    {preset.label}
                  </Button>
                ))}
              </div>
            )}

            {/* Main content */}
            <div className="p-3 space-y-3">
              {/* Calendar */}
              <Calendar
                mode="single"
                selected={value}
                onSelect={handleDateSelect}
                disabled={(date) => {
                  if (minDate && date < minDate) return true;
                  if (maxDate && date > maxDate) return true;
                  return false;
                }}
                initialFocus
                className="rounded-md"
              />

            {/* Time Picker */}
            {showTime && (
              <div className="border-t pt-3">
                <Label className="text-sm font-medium mb-2 flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Time
                </Label>
                <Select value={timeValue} onValueChange={handleTimeChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select time" />
                  </SelectTrigger>
                  <SelectContent className="max-h-60">
                    {timeOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Timezone Selector */}
            {showTimezone && (
              <div className="border-t pt-3">
                <Label className="text-sm font-medium mb-2 flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  Timezone
                </Label>
                <TimezoneSelector
                  value={timezone}
                  onChange={onTimezoneChange || (() => {})}
                  showSearch={false}
                  showPopular={true}
                />
              </div>
            )}

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2 border-t">
                {showClear && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      onChange(undefined);
                      setTimeValue('');
                      setIsOpen(false);
                    }}
                    className="flex-1"
                  >
                    Clear
                  </Button>
                )}
                <Button
                  size="sm"
                  onClick={() => setIsOpen(false)}
                  className={cn("flex-1", !showClear && "w-full")}
                >
                  Done
                </Button>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Current time in selected timezone */}
      {showTimezone && timezone && value && (
        <div className="text-xs text-muted-foreground flex items-center gap-1">
          <Globe className="h-3 w-3" />
          Current time in {timezone}:{' '}
          {new Intl.DateTimeFormat('en-US', {
            timeZone: timezone,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: timeFormat === '12'
          }).format(new Date())}
        </div>
      )}

      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}
    </div>
  );
}
