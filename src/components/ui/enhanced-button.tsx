"use client";

import React from 'react';
import { Button, ButtonProps } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';

export interface EnhancedButtonProps extends ButtonProps {
  animation?: 'none' | 'hover-lift' | 'hover-glow' | 'hover-slide' | 'hover-bounce' | 'hover-pulse' | 'gradient-shift' | 'elastic';
  icon?: LucideIcon;
  iconPosition?: 'left' | 'right';
  glowColor?: 'blue' | 'green' | 'purple' | 'pink' | 'orange' | 'red' | 'primary';
  gradient?: 'none' | 'sunset' | 'ocean' | 'forest' | 'royal' | 'gold' | 'cyber';
}

export const EnhancedButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ 
    className, 
    animation = 'hover-lift', 
    icon: Icon, 
    iconPosition = 'left',
    glowColor,
    gradient = 'none',
    children, 
    ...props 
  }, ref) => {
    
    // Animation classes
    const animationClasses = {
      'none': '',
      'hover-lift': 'transition-all duration-200 hover:scale-105 hover:shadow-lg active:scale-95',
      'hover-glow': 'transition-all duration-300 hover:shadow-lg hover:shadow-primary/25 hover:scale-102',
      'hover-slide': 'transition-all duration-200 hover:translate-x-1 hover:shadow-md',
      'hover-bounce': 'transition-all duration-200 hover:-translate-y-1 hover:shadow-lg',
      'hover-pulse': 'transition-all duration-200 hover:animate-pulse hover:shadow-lg',
      'gradient-shift': 'bg-gradient-to-r from-primary to-primary/80 hover:from-primary/80 hover:to-primary transition-all duration-300 hover:shadow-lg',
      'elastic': 'transition-all duration-300 hover:scale-110 active:scale-90 hover:shadow-xl',
      'icon-slide': 'group relative overflow-hidden transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden'
    };

    // Glow color classes
    const glowClasses = {
      'blue': 'hover:shadow-blue-500/25',
      'green': 'hover:shadow-green-500/25',
      'purple': 'hover:shadow-purple-500/25',
      'pink': 'hover:shadow-pink-500/25',
      'orange': 'hover:shadow-orange-500/25',
      'red': 'hover:shadow-red-500/25',
      'primary': 'hover:shadow-primary/25'
    };

    // Gradient classes
    const gradientClasses = {
      'none': '',
      'sunset': 'bg-gradient-to-r from-orange-400 via-red-500 to-pink-500 hover:from-orange-500 hover:via-red-600 hover:to-pink-600',
      'ocean': 'bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600 hover:from-blue-500 hover:via-blue-600 hover:to-blue-700',
      'forest': 'bg-gradient-to-r from-green-400 via-green-500 to-emerald-600 hover:from-green-500 hover:via-green-600 hover:to-emerald-700',
      'royal': 'bg-gradient-to-r from-purple-400 via-purple-500 to-indigo-600 hover:from-purple-500 hover:via-purple-600 hover:to-indigo-700',
      'gold': 'bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 hover:from-yellow-500 hover:via-orange-600 hover:to-red-600',
      'cyber': 'bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 hover:from-cyan-500 hover:via-blue-600 hover:to-purple-700'
    };

    const combinedClassName = cn(
      animationClasses[animation],
      glowColor && glowClasses[glowColor],
      gradient !== 'none' && gradientClasses[gradient],
      gradient !== 'none' && 'text-white border-0', // Override text color for gradients
      className
    );

    // Special handling for sliding icon animation
    if (animation === 'icon-slide' && Icon) {
      return (
        <button
          className={cn(
            "group relative inline-flex items-center overflow-hidden rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden",
            className
          )}
          ref={ref}
          {...props}
        >
          <span className="absolute -start-full transition-all group-hover:start-2">
            <Icon className="size-4" />
          </span>
          <span className="text-sm font-medium transition-all group-hover:ms-2">
            {children}
          </span>
        </button>
      );
    }

    return (
      <Button
        className={combinedClassName}
        ref={ref}
        {...props}
      >
        {Icon && iconPosition === 'left' && <Icon className="mr-2 h-4 w-4" />}
        {children}
        {Icon && iconPosition === 'right' && <Icon className="ml-2 h-4 w-4" />}
      </Button>
    );
  }
);

EnhancedButton.displayName = "EnhancedButton";

// Preset button variants for common use cases
export const PrimaryActionButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ children, ...props }, ref) => (
    <EnhancedButton
      animation="hover-glow"
      glowColor="primary"
      ref={ref}
      {...props}
    >
      {children}
    </EnhancedButton>
  )
);

export const SecondaryActionButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ children, ...props }, ref) => (
    <EnhancedButton
      animation="hover-lift"
      variant="outline"
      ref={ref}
      {...props}
    >
      {children}
    </EnhancedButton>
  )
);

export const DestructiveActionButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ children, ...props }, ref) => (
    <EnhancedButton
      animation="hover-lift"
      variant="destructive"
      glowColor="red"
      ref={ref}
      {...props}
    >
      {children}
    </EnhancedButton>
  )
);

export const SuccessActionButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ children, ...props }, ref) => (
    <EnhancedButton
      animation="hover-glow"
      glowColor="green"
      className="bg-green-600 hover:bg-green-700 text-white"
      ref={ref}
      {...props}
    >
      {children}
    </EnhancedButton>
  )
);

export const SlidingIconButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ children, ...props }, ref) => (
    <EnhancedButton
      animation="icon-slide"
      ref={ref}
      {...props}
    >
      {children}
    </EnhancedButton>
  )
);

export const PremiumActionButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ children, ...props }, ref) => (
    <EnhancedButton
      animation="elastic"
      gradient="gold"
      ref={ref}
      {...props}
    >
      {children}
    </EnhancedButton>
  )
);

PrimaryActionButton.displayName = "PrimaryActionButton";
SecondaryActionButton.displayName = "SecondaryActionButton";
DestructiveActionButton.displayName = "DestructiveActionButton";
SuccessActionButton.displayName = "SuccessActionButton";
PremiumActionButton.displayName = "PremiumActionButton";
