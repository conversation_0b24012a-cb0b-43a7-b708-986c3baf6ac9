"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import {
  generateId,
  generateAriaDescribedBy,
  announceToScreenReader,
  useLiveRegion,
  focusVisibleClasses
} from "@/lib/accessibility";
import { LiveRegion } from "@/components/ui/live-region";
import { AlertCircle, CheckCircle, Info, AlertTriangle } from "lucide-react";

// Form Context for managing form state and accessibility
interface FormContextValue {
  formId: string;
  errors: Record<string, string>;
  setError: (field: string, error: string) => void;
  clearError: (field: string) => void;
  announceError: (message: string) => void;
  announceSuccess: (message: string) => void;
}

const FormContext = React.createContext<FormContextValue | null>(null);

export const useFormContext = () => {
  const context = React.useContext(FormContext);
  if (!context) {
    throw new Error('useFormContext must be used within an AccessibleForm');
  }
  return context;
};

// Main Form Component
export interface AccessibleFormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  children: React.ReactNode;
  onSubmit?: (event: React.FormEvent<HTMLFormElement>) => void | Promise<void>;
  title?: string;
  description?: string;
  errorSummary?: boolean;
}

export const AccessibleForm = React.forwardRef<HTMLFormElement, AccessibleFormProps>(
  ({ className, children, onSubmit, title, description, errorSummary = true, ...props }, ref) => {
    const formId = React.useId();
    const [errors, setErrors] = React.useState<Record<string, string>>({});
    const [isSubmitting, setIsSubmitting] = React.useState(false);
    const { announce, liveRegionRef } = useLiveRegion();

    const setError = (field: string, error: string) => {
      setErrors(prev => ({ ...prev, [field]: error }));
    };

    const clearError = (field: string) => {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    };

    const announceError = (message: string) => {
      announce(message, 'assertive');
    };

    const announceSuccess = (message: string) => {
      announce(message, 'polite');
    };

    const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
      event.preventDefault();
      
      if (Object.keys(errors).length > 0) {
        announceError(`Form has ${Object.keys(errors).length} error${Object.keys(errors).length > 1 ? 's' : ''}. Please review and correct.`);
        return;
      }

      if (onSubmit) {
        try {
          setIsSubmitting(true);
          await onSubmit(event);
          announceSuccess('Form submitted successfully');
        } catch (error) {
          announceError('Form submission failed. Please try again.');
        } finally {
          setIsSubmitting(false);
        }
      }
    };

    const contextValue: FormContextValue = {
      formId,
      errors,
      setError,
      clearError,
      announceError,
      announceSuccess,
    };

    const errorList = Object.entries(errors);

    return (
      <FormContext.Provider value={contextValue}>
        <div className="space-y-6">
          {/* Form Header */}
          {(title || description) && (
            <div className="space-y-2">
              {title && (
                <h2 id={`${formId}-title`} className="text-2xl font-semibold tracking-tight">
                  {title}
                </h2>
              )}
              {description && (
                <p id={`${formId}-description`} className="text-muted-foreground">
                  {description}
                </p>
              )}
            </div>
          )}

          {/* Error Summary */}
          {errorSummary && errorList.length > 0 && (
            <div
              role="alert"
              aria-labelledby={`${formId}-error-summary-title`}
              className="rounded-md border border-destructive bg-destructive/10 p-4"
            >
              <div className="flex items-center gap-2 mb-2">
                <AlertCircle className="h-5 w-5 text-destructive" aria-hidden="true" />
                <h3 id={`${formId}-error-summary-title`} className="font-medium text-destructive">
                  There {errorList.length === 1 ? 'is' : 'are'} {errorList.length} error{errorList.length > 1 ? 's' : ''} with this form
                </h3>
              </div>
              <ul className="list-disc list-inside space-y-1 text-sm text-destructive">
                {errorList.map(([field, error]) => (
                  <li key={field}>
                    <a
                      href={`#${formId}-${field}`}
                      className="underline hover:no-underline focus:outline-none focus:ring-2 focus:ring-destructive focus:ring-offset-2 rounded"
                    >
                      {error}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Form */}
          <form
            ref={ref}
            id={formId}
            className={cn("space-y-6", className)}
            onSubmit={handleSubmit}
            noValidate // We handle validation ourselves for better accessibility
            aria-labelledby={title ? `${formId}-title` : undefined}
            aria-describedby={description ? `${formId}-description` : undefined}
            {...props}
          >
            {children}
          </form>

          <LiveRegion liveRegionRef={liveRegionRef} />
        </div>
      </FormContext.Provider>
    );
  }
);

AccessibleForm.displayName = "AccessibleForm";

// Form Field Component
export interface AccessibleFormFieldProps {
  children: React.ReactNode;
  label: string;
  name: string;
  description?: string;
  required?: boolean;
  error?: string;
  className?: string;
}

export const AccessibleFormField = React.forwardRef<HTMLDivElement, AccessibleFormFieldProps>(
  ({ children, label, name, description, required = false, error, className }, ref) => {
    const { formId, errors } = useFormContext();
    const fieldError = error || errors[name];
    const fieldId = `${formId}-${name}`;
    const labelId = `${fieldId}-label`;
    const descriptionId = description ? `${fieldId}-description` : undefined;
    const errorId = fieldError ? `${fieldId}-error` : undefined;

    const describedBy = generateAriaDescribedBy(
      fieldId,
      ...[
        description && 'description',
        fieldError && 'error'
      ].filter(Boolean) as string[]
    );

    return (
      <div ref={ref} className={cn("space-y-2", className)}>
        <label
          id={labelId}
          htmlFor={fieldId}
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          {label}
          {required && (
            <span className="text-destructive ml-1" aria-label="required">
              *
            </span>
          )}
        </label>

        {description && (
          <p id={descriptionId} className="text-sm text-muted-foreground">
            {description}
          </p>
        )}

        <div className="relative">
          {React.cloneElement(children as React.ReactElement, {
            id: fieldId,
            name,
            'aria-labelledby': labelId,
            'aria-describedby': describedBy || undefined,
            'aria-required': required,
            'aria-invalid': !!fieldError,
            className: cn(
              (children as React.ReactElement).props.className,
              fieldError && "border-destructive focus:border-destructive focus:ring-destructive"
            )
          })}
        </div>

        {fieldError && (
          <div
            id={errorId}
            role="alert"
            className="flex items-center gap-2 text-sm text-destructive"
          >
            <AlertCircle className="h-4 w-4" aria-hidden="true" />
            {fieldError}
          </div>
        )}
      </div>
    );
  }
);

AccessibleFormField.displayName = "AccessibleFormField";

// Form Section Component
export interface AccessibleFormSectionProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  className?: string;
}

export const AccessibleFormSection = React.forwardRef<HTMLFieldSetElement, AccessibleFormSectionProps>(
  ({ children, title, description, className }, ref) => {
    const sectionId = React.useId();

    return (
      <fieldset
        ref={ref}
        className={cn("space-y-4 border-0 p-0", className)}
        aria-labelledby={title ? `${sectionId}-title` : undefined}
        aria-describedby={description ? `${sectionId}-description` : undefined}
      >
        {title && (
          <legend id={`${sectionId}-title`} className="text-lg font-medium">
            {title}
          </legend>
        )}
        
        {description && (
          <p id={`${sectionId}-description`} className="text-sm text-muted-foreground">
            {description}
          </p>
        )}
        
        <div className="space-y-4">
          {children}
        </div>
      </fieldset>
    );
  }
);

AccessibleFormSection.displayName = "AccessibleFormSection";

// Form Message Component for success/info messages
export interface FormMessageProps {
  type: 'success' | 'info' | 'warning' | 'error';
  title?: string;
  children: React.ReactNode;
  className?: string;
}

export const FormMessage = React.forwardRef<HTMLDivElement, FormMessageProps>(
  ({ type, title, children, className }, ref) => {
    const icons = {
      success: CheckCircle,
      info: Info,
      warning: AlertTriangle,
      error: AlertCircle,
    };

    const styles = {
      success: "border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-950 dark:text-green-200",
      info: "border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-200",
      warning: "border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-200",
      error: "border-destructive bg-destructive/10 text-destructive",
    };

    const Icon = icons[type];

    return (
      <div
        ref={ref}
        role={type === 'error' ? 'alert' : 'status'}
        className={cn(
          "rounded-md border p-4",
          styles[type],
          className
        )}
      >
        <div className="flex items-start gap-3">
          <Icon className="h-5 w-5 mt-0.5 flex-shrink-0" aria-hidden="true" />
          <div className="space-y-1">
            {title && (
              <h4 className="font-medium">{title}</h4>
            )}
            <div className="text-sm">{children}</div>
          </div>
        </div>
      </div>
    );
  }
);

FormMessage.displayName = "FormMessage";
