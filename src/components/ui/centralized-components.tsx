/**
 * EVEXA Centralized UI Components
 * 
 * This file provides centralized, consistent UI components that solve
 * the alignment, styling, and behavior issues across the application.
 */

"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { 
  buttonVariants, 
  inputVariants, 
  cardVariants, 
  formVariants, 
  layoutVariants,
  type ButtonVariants,
  type InputVariants,
  type CardVariants,
} from "@/lib/ui-system";
import { Button } from "./button";
import { Input } from "./input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./card";

// ============================================================================
// CENTRALIZED BUTTON SYSTEM
// ============================================================================

interface CentralizedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement>, ButtonVariants {
  loading?: boolean;
  icon?: React.ReactNode;
  fullWidth?: boolean;
  children: React.ReactNode;
}

export const CentralizedButton = React.forwardRef<HTMLButtonElement, CentralizedButtonProps>(
  ({ className, variant = "default", size = "default", alignment = "center", ...props }, ref) => {
    return (
      <Button
        ref={ref}
        className={cn("transition-all duration-200", className)}
        variant={variant}
        size={size}
        alignment={alignment}
        {...props}
      />
    );
  }
);
CentralizedButton.displayName = "CentralizedButton";

// ============================================================================
// CENTRALIZED INPUT SYSTEM
// ============================================================================

interface CentralizedInputProps extends React.InputHTMLAttributes<HTMLInputElement>, InputVariants {
  label?: string;
  description?: string;
  error?: string;
  success?: string;
  warning?: string;
  prefixIcon?: React.ReactNode;
  suffixIcon?: React.ReactNode;
}

export const CentralizedInput = React.forwardRef<HTMLInputElement, CentralizedInputProps>(
  ({ 
    className, 
    label, 
    description, 
    error, 
    success, 
    warning, 
    size = "default", 
    variant = "default",
    ...props 
  }, ref) => {
    const id = React.useId();
    const resolvedVariant = error ? "error" : success ? "success" : warning ? "warning" : variant;
    
    return (
      <div className={cn(formVariants.field())}>
        {label && (
          <label 
            htmlFor={id}
            className={cn(formVariants.label())}
          >
            {label}
          </label>
        )}
        
        <Input
          ref={ref}
          id={id}
          className={className}
          size={size}
          variant={resolvedVariant}
          {...props}
        />
        
        {description && !error && !success && !warning && (
          <p className={cn(formVariants.description())}>
            {description}
          </p>
        )}
        
        {error && (
          <p className={cn(formVariants.message({ variant: "default" }))}>
            {error}
          </p>
        )}
        
        {success && (
          <p className={cn(formVariants.message({ variant: "success" }))}>
            {success}
          </p>
        )}
        
        {warning && (
          <p className={cn(formVariants.message({ variant: "warning" }))}>
            {warning}
          </p>
        )}
      </div>
    );
  }
);
CentralizedInput.displayName = "CentralizedInput";

// ============================================================================
// CENTRALIZED CARD SYSTEM
// ============================================================================

interface CentralizedCardProps extends React.HTMLAttributes<HTMLDivElement>, CardVariants {
  title?: string;
  description?: string;
  children: React.ReactNode;
  actions?: React.ReactNode;
}

export const CentralizedCard = React.forwardRef<HTMLDivElement, CentralizedCardProps>(
  ({ 
    className, 
    title, 
    description, 
    children, 
    actions,
    variant = "default", 
    padding = "md", 
    interactive = false,
    ...props 
  }, ref) => {
    return (
      <Card
        ref={ref}
        className={cn(cardVariants({ variant, padding, interactive }), className)}
        {...props}
      >
        {(title || description || actions) && (
          <CardHeader className={cn(padding === "none" && "p-6")}>
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                {title && <CardTitle>{title}</CardTitle>}
                {description && <CardDescription>{description}</CardDescription>}
              </div>
              {actions && (
                <div className="flex items-center gap-2">
                  {actions}
                </div>
              )}
            </div>
          </CardHeader>
        )}
        
        <CardContent className={cn(padding === "none" && "p-6", (title || description || actions) && "pt-0")}>
          {children}
        </CardContent>
      </Card>
    );
  }
);
CentralizedCard.displayName = "CentralizedCard";

// ============================================================================
// CENTRALIZED LAYOUT SYSTEM
// ============================================================================

interface CentralizedContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "sm" | "md" | "lg" | "xl" | "full";
  padding?: "none" | "sm" | "md" | "lg";
  children: React.ReactNode;
}

export const CentralizedContainer = React.forwardRef<HTMLDivElement, CentralizedContainerProps>(
  ({ className, size = "lg", padding = "md", children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(layoutVariants.container({ size, padding }), className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);
CentralizedContainer.displayName = "CentralizedContainer";

interface CentralizedStackProps extends React.HTMLAttributes<HTMLDivElement> {
  gap?: "none" | "xs" | "sm" | "md" | "lg" | "xl";
  align?: "start" | "center" | "end" | "stretch";
  children: React.ReactNode;
}

export const CentralizedStack = React.forwardRef<HTMLDivElement, CentralizedStackProps>(
  ({ className, gap = "md", align = "stretch", children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(layoutVariants.stack({ gap, align }), className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);
CentralizedStack.displayName = "CentralizedStack";

interface CentralizedGridProps extends React.HTMLAttributes<HTMLDivElement> {
  cols?: 1 | 2 | 3 | 4 | 6 | 12;
  gap?: "none" | "xs" | "sm" | "md" | "lg" | "xl";
  children: React.ReactNode;
}

export const CentralizedGrid = React.forwardRef<HTMLDivElement, CentralizedGridProps>(
  ({ className, cols = 1, gap = "md", children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(layoutVariants.grid({ cols, gap }), className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);
CentralizedGrid.displayName = "CentralizedGrid";

// ============================================================================
// CENTRALIZED FORM SYSTEM
// ============================================================================

interface CentralizedFormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  children: React.ReactNode;
  loading?: boolean;
}

export const CentralizedForm = React.forwardRef<HTMLFormElement, CentralizedFormProps>(
  ({ className, children, loading = false, ...props }, ref) => {
    return (
      <form
        ref={ref}
        className={cn(
          "space-y-6",
          loading && "pointer-events-none opacity-70",
          className
        )}
        {...props}
      >
        {children}
      </form>
    );
  }
);
CentralizedForm.displayName = "CentralizedForm";

// ============================================================================
// CENTRALIZED PAGE HEADER SYSTEM
// ============================================================================

interface CentralizedPageHeaderProps {
  title: string;
  description?: string;
  actions?: React.ReactNode;
  breadcrumbs?: React.ReactNode;
  className?: string;
}

export const CentralizedPageHeader: React.FC<CentralizedPageHeaderProps> = ({
  title,
  description,
  actions,
  breadcrumbs,
  className,
}) => {
  return (
    <div className={cn("space-y-4", className)}>
      {breadcrumbs}
      
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <h1 className="text-2xl font-semibold tracking-tight">{title}</h1>
          {description && (
            <p className="text-muted-foreground">{description}</p>
          )}
        </div>
        
        {actions && (
          <div className="flex items-center gap-2">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// CENTRALIZED TABLE ACTIONS
// ============================================================================

interface CentralizedTableActionsProps {
  title?: string;
  description?: string;
  searchPlaceholder?: string;
  onSearch?: (query: string) => void;
  actions?: React.ReactNode;
  filters?: React.ReactNode;
  className?: string;
}

export const CentralizedTableActions: React.FC<CentralizedTableActionsProps> = ({
  title,
  description,
  searchPlaceholder = "Search...",
  onSearch,
  actions,
  filters,
  className,
}) => {
  const [searchQuery, setSearchQuery] = React.useState("");

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    onSearch?.(query);
  };

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          {title && <h2 className="text-lg font-medium">{title}</h2>}
          {description && <p className="text-sm text-muted-foreground">{description}</p>}
        </div>
        
        {actions && (
          <div className="flex items-center gap-2">
            {actions}
          </div>
        )}
      </div>
      
      <div className="flex items-center justify-between gap-4">
        {onSearch && (
          <div className="flex-1 max-w-sm">
            <CentralizedInput
              placeholder={searchPlaceholder}
              value={searchQuery}
              onChange={handleSearchChange}
              prefixIcon={
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              }
            />
          </div>
        )}
        
        {filters && (
          <div className="flex items-center gap-2">
            {filters}
          </div>
        )}
      </div>
    </div>
  );
};
