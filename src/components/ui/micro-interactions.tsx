"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { useReducedMotion } from "@/lib/accessibility";

// Hover Scale Component
export interface HoverScaleProps {
  children: React.ReactNode;
  scale?: number;
  className?: string;
  disabled?: boolean;
}

export const HoverScale = React.forwardRef<HTMLDivElement, HoverScaleProps>(
  ({ children, scale = 1.05, className, disabled = false }, ref) => {
    const prefersReducedMotion = useReducedMotion();

    return (
      <div
        ref={ref}
        className={cn(
          "transition-transform duration-200 ease-out",
          !disabled && !prefersReducedMotion && "hover:scale-105 active:scale-95",
          disabled && "cursor-not-allowed opacity-50",
          className
        )}
        style={{
          '--hover-scale': scale,
        } as React.CSSProperties}
      >
        {children}
      </div>
    );
  }
);

HoverScale.displayName = "HoverScale";

// Fade In Animation
export interface FadeInProps {
  children: React.ReactNode;
  delay?: number;
  duration?: number;
  className?: string;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
}

export const FadeIn = React.forwardRef<HTMLDivElement, FadeInProps>(
  ({ children, delay = 0, duration = 300, className, direction = 'up' }, ref) => {
    const [isVisible, setIsVisible] = React.useState(false);
    const prefersReducedMotion = useReducedMotion();

    React.useEffect(() => {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, delay);

      return () => clearTimeout(timer);
    }, [delay]);

    const getTransformClasses = () => {
      if (prefersReducedMotion) return '';
      
      const baseTransform = isVisible ? 'translate-0 opacity-100' : 'opacity-0';
      
      switch (direction) {
        case 'up':
          return isVisible ? baseTransform : 'translate-y-4 opacity-0';
        case 'down':
          return isVisible ? baseTransform : '-translate-y-4 opacity-0';
        case 'left':
          return isVisible ? baseTransform : 'translate-x-4 opacity-0';
        case 'right':
          return isVisible ? baseTransform : '-translate-x-4 opacity-0';
        case 'none':
        default:
          return baseTransform;
      }
    };

    return (
      <div
        ref={ref}
        className={cn(
          "transition-all ease-out",
          getTransformClasses(),
          className
        )}
        style={{
          transitionDuration: prefersReducedMotion ? '0ms' : `${duration}ms`,
        }}
      >
        {children}
      </div>
    );
  }
);

FadeIn.displayName = "FadeIn";

// Stagger Children Animation
export interface StaggerChildrenProps {
  children: React.ReactNode;
  staggerDelay?: number;
  className?: string;
}

export const StaggerChildren = React.forwardRef<HTMLDivElement, StaggerChildrenProps>(
  ({ children, staggerDelay = 100, className }, ref) => {
    const prefersReducedMotion = useReducedMotion();

    return (
      <div ref={ref} className={className}>
        {React.Children.map(children, (child, index) => {
          if (!React.isValidElement(child)) return child;
          
          return (
            <FadeIn
              delay={prefersReducedMotion ? 0 : index * staggerDelay}
              direction="up"
            >
              {child}
            </FadeIn>
          );
        })}
      </div>
    );
  }
);

StaggerChildren.displayName = "StaggerChildren";

// Bounce Animation
export interface BounceProps {
  children: React.ReactNode;
  trigger?: boolean;
  className?: string;
}

export const Bounce = React.forwardRef<HTMLDivElement, BounceProps>(
  ({ children, trigger = false, className }, ref) => {
    const [shouldBounce, setShouldBounce] = React.useState(false);
    const prefersReducedMotion = useReducedMotion();

    React.useEffect(() => {
      if (trigger && !prefersReducedMotion) {
        setShouldBounce(true);
        const timer = setTimeout(() => setShouldBounce(false), 600);
        return () => clearTimeout(timer);
      }
    }, [trigger, prefersReducedMotion]);

    return (
      <div
        ref={ref}
        className={cn(
          shouldBounce && "animate-bounce",
          className
        )}
      >
        {children}
      </div>
    );
  }
);

Bounce.displayName = "Bounce";

// Pulse Animation
export interface PulseProps {
  children: React.ReactNode;
  active?: boolean;
  className?: string;
}

export const Pulse = React.forwardRef<HTMLDivElement, PulseProps>(
  ({ children, active = false, className }, ref) => {
    const prefersReducedMotion = useReducedMotion();

    return (
      <div
        ref={ref}
        className={cn(
          active && !prefersReducedMotion && "animate-pulse",
          className
        )}
      >
        {children}
      </div>
    );
  }
);

Pulse.displayName = "Pulse";

// Shake Animation
export interface ShakeProps {
  children: React.ReactNode;
  trigger?: boolean;
  className?: string;
}

export const Shake = React.forwardRef<HTMLDivElement, ShakeProps>(
  ({ children, trigger = false, className }, ref) => {
    const [shouldShake, setShouldShake] = React.useState(false);
    const prefersReducedMotion = useReducedMotion();

    React.useEffect(() => {
      if (trigger && !prefersReducedMotion) {
        setShouldShake(true);
        const timer = setTimeout(() => setShouldShake(false), 500);
        return () => clearTimeout(timer);
      }
    }, [trigger, prefersReducedMotion]);

    return (
      <div
        ref={ref}
        className={cn(
          shouldShake && "animate-shake",
          className
        )}
        style={{
          animation: shouldShake && !prefersReducedMotion 
            ? 'shake 0.5s ease-in-out' 
            : undefined,
        }}
      >
        {children}
        <style jsx>{`
          @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
            20%, 40%, 60%, 80% { transform: translateX(2px); }
          }
        `}</style>
      </div>
    );
  }
);

Shake.displayName = "Shake";

// Slide In Animation
export interface SlideInProps {
  children: React.ReactNode;
  direction: 'left' | 'right' | 'up' | 'down';
  isVisible?: boolean;
  duration?: number;
  className?: string;
}

export const SlideIn = React.forwardRef<HTMLDivElement, SlideInProps>(
  ({ children, direction, isVisible = true, duration = 300, className }, ref) => {
    const prefersReducedMotion = useReducedMotion();

    const getTransformClasses = () => {
      if (prefersReducedMotion) return 'opacity-100';
      
      const baseClasses = 'transition-all ease-out';
      
      if (isVisible) {
        return `${baseClasses} translate-0 opacity-100`;
      }
      
      switch (direction) {
        case 'left':
          return `${baseClasses} -translate-x-full opacity-0`;
        case 'right':
          return `${baseClasses} translate-x-full opacity-0`;
        case 'up':
          return `${baseClasses} -translate-y-full opacity-0`;
        case 'down':
          return `${baseClasses} translate-y-full opacity-0`;
        default:
          return `${baseClasses} opacity-0`;
      }
    };

    return (
      <div
        ref={ref}
        className={cn(getTransformClasses(), className)}
        style={{
          transitionDuration: prefersReducedMotion ? '0ms' : `${duration}ms`,
        }}
      >
        {children}
      </div>
    );
  }
);

SlideIn.displayName = "SlideIn";

// Rotate Animation
export interface RotateProps {
  children: React.ReactNode;
  degrees?: number;
  trigger?: boolean;
  className?: string;
}

export const Rotate = React.forwardRef<HTMLDivElement, RotateProps>(
  ({ children, degrees = 180, trigger = false, className }, ref) => {
    const prefersReducedMotion = useReducedMotion();

    return (
      <div
        ref={ref}
        className={cn(
          "transition-transform duration-300 ease-out",
          className
        )}
        style={{
          transform: trigger && !prefersReducedMotion 
            ? `rotate(${degrees}deg)` 
            : 'rotate(0deg)',
        }}
      >
        {children}
      </div>
    );
  }
);

Rotate.displayName = "Rotate";

// Morphing Button
export interface MorphingButtonProps {
  children: React.ReactNode;
  morphTo?: React.ReactNode;
  trigger?: boolean;
  className?: string;
  onClick?: () => void;
}

export const MorphingButton = React.forwardRef<HTMLButtonElement, MorphingButtonProps>(
  ({ children, morphTo, trigger = false, className, onClick }, ref) => {
    const prefersReducedMotion = useReducedMotion();

    return (
      <button
        ref={ref}
        onClick={onClick}
        className={cn(
          "relative overflow-hidden transition-all duration-300 ease-out",
          "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
          className
        )}
      >
        <div
          className={cn(
            "transition-all duration-300 ease-out",
            trigger && !prefersReducedMotion && "opacity-0 scale-95"
          )}
        >
          {children}
        </div>
        {morphTo && (
          <div
            className={cn(
              "absolute inset-0 flex items-center justify-center transition-all duration-300 ease-out",
              trigger && !prefersReducedMotion 
                ? "opacity-100 scale-100" 
                : "opacity-0 scale-95"
            )}
          >
            {morphTo}
          </div>
        )}
      </button>
    );
  }
);

MorphingButton.displayName = "MorphingButton";

// Floating Animation
export interface FloatingProps {
  children: React.ReactNode;
  intensity?: 'subtle' | 'normal' | 'strong';
  className?: string;
}

export const Floating = React.forwardRef<HTMLDivElement, FloatingProps>(
  ({ children, intensity = 'normal', className }, ref) => {
    const prefersReducedMotion = useReducedMotion();

    const intensityClasses = {
      subtle: 'animate-float-subtle',
      normal: 'animate-float',
      strong: 'animate-float-strong',
    };

    return (
      <div
        ref={ref}
        className={cn(
          !prefersReducedMotion && intensityClasses[intensity],
          className
        )}
      >
        {children}
        <style jsx>{`
          @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
          }
          @keyframes float-subtle {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
          }
          @keyframes float-strong {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
          }
          .animate-float {
            animation: float 3s ease-in-out infinite;
          }
          .animate-float-subtle {
            animation: float-subtle 4s ease-in-out infinite;
          }
          .animate-float-strong {
            animation: float-strong 2s ease-in-out infinite;
          }
        `}</style>
      </div>
    );
  }
);

Floating.displayName = "Floating";
