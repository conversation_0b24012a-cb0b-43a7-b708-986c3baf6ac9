"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { useReducedMotion } from "@/lib/accessibility";
import { Loader2, RefreshCw, Download, Upload, Search, CheckCircle, AlertCircle } from "lucide-react";

// Loading Spinner Component
export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  color?: 'primary' | 'secondary' | 'muted';
}

export const LoadingSpinner = React.forwardRef<HTMLDivElement, LoadingSpinnerProps>(
  ({ size = 'md', className, color = 'primary' }, ref) => {
    const prefersReducedMotion = useReducedMotion();
    
    const sizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-6 w-6',
      lg: 'h-8 w-8',
      xl: 'h-12 w-12',
    };

    const colorClasses = {
      primary: 'text-primary',
      secondary: 'text-secondary-foreground',
      muted: 'text-muted-foreground',
    };

    return (
      <div ref={ref} className={cn("flex items-center justify-center", className)}>
        <Loader2 
          className={cn(
            sizeClasses[size],
            colorClasses[color],
            !prefersReducedMotion && "animate-spin"
          )}
          aria-label="Loading"
        />
      </div>
    );
  }
);

LoadingSpinner.displayName = "LoadingSpinner";

// Skeleton Loader Component
export interface SkeletonProps {
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular';
  width?: string | number;
  height?: string | number;
  lines?: number;
}

export const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, variant = 'rectangular', width, height, lines = 1 }, ref) => {
    const prefersReducedMotion = useReducedMotion();

    const baseClasses = cn(
      "bg-muted animate-pulse",
      !prefersReducedMotion && "animate-pulse",
      variant === 'circular' && "rounded-full",
      variant === 'text' && "rounded h-4",
      variant === 'rectangular' && "rounded-md"
    );

    if (variant === 'text' && lines > 1) {
      return (
        <div ref={ref} className={cn("space-y-2", className)}>
          {Array.from({ length: lines }).map((_, i) => (
            <div
              key={i}
              className={cn(
                baseClasses,
                i === lines - 1 && "w-3/4" // Last line is shorter
              )}
              style={{ width: i === lines - 1 ? '75%' : width, height }}
            />
          ))}
        </div>
      );
    }

    return (
      <div
        ref={ref}
        className={cn(baseClasses, className)}
        style={{ width, height }}
      />
    );
  }
);

Skeleton.displayName = "Skeleton";

// Progress Bar Component
export interface ProgressBarProps {
  value: number;
  max?: number;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'success' | 'warning' | 'error';
  showLabel?: boolean;
  label?: string;
  animated?: boolean;
}

export const ProgressBar = React.forwardRef<HTMLDivElement, ProgressBarProps>(
  ({ 
    value, 
    max = 100, 
    className, 
    size = 'md', 
    variant = 'default',
    showLabel = false,
    label,
    animated = true
  }, ref) => {
    const prefersReducedMotion = useReducedMotion();
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

    const sizeClasses = {
      sm: 'h-1',
      md: 'h-2',
      lg: 'h-3',
    };

    const variantClasses = {
      default: 'bg-primary',
      success: 'bg-green-500',
      warning: 'bg-yellow-500',
      error: 'bg-red-500',
    };

    return (
      <div ref={ref} className={cn("w-full", className)}>
        {showLabel && (
          <div className="flex justify-between text-sm mb-1">
            <span>{label}</span>
            <span>{Math.round(percentage)}%</span>
          </div>
        )}
        <div className={cn("w-full bg-muted rounded-full overflow-hidden", sizeClasses[size])}>
          <div
            className={cn(
              "h-full transition-all duration-300 ease-out rounded-full",
              variantClasses[variant],
              animated && !prefersReducedMotion && "transition-all duration-300"
            )}
            style={{ width: `${percentage}%` }}
            role="progressbar"
            aria-valuenow={value}
            aria-valuemin={0}
            aria-valuemax={max}
            aria-label={label || `Progress: ${Math.round(percentage)}%`}
          />
        </div>
      </div>
    );
  }
);

ProgressBar.displayName = "ProgressBar";

// Loading Overlay Component
export interface LoadingOverlayProps {
  isLoading: boolean;
  children: React.ReactNode;
  message?: string;
  className?: string;
  blur?: boolean;
}

export const LoadingOverlay = React.forwardRef<HTMLDivElement, LoadingOverlayProps>(
  ({ isLoading, children, message = "Loading...", className, blur = true }, ref) => {
    return (
      <div ref={ref} className={cn("relative", className)}>
        {children}
        {isLoading && (
          <div className={cn(
            "absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50",
            blur && "backdrop-blur-sm"
          )}>
            <div className="flex flex-col items-center gap-3">
              <LoadingSpinner size="lg" />
              <p className="text-sm text-muted-foreground">{message}</p>
            </div>
          </div>
        )}
      </div>
    );
  }
);

LoadingOverlay.displayName = "LoadingOverlay";

// Action Loading States
export interface ActionLoadingProps {
  action: 'saving' | 'loading' | 'uploading' | 'downloading' | 'searching' | 'processing';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const ActionLoading = React.forwardRef<HTMLDivElement, ActionLoadingProps>(
  ({ action, size = 'md', className }, ref) => {
    const prefersReducedMotion = useReducedMotion();

    const icons = {
      saving: Loader2,
      loading: Loader2,
      uploading: Upload,
      downloading: Download,
      searching: Search,
      processing: RefreshCw,
    };

    const messages = {
      saving: 'Saving...',
      loading: 'Loading...',
      uploading: 'Uploading...',
      downloading: 'Downloading...',
      searching: 'Searching...',
      processing: 'Processing...',
    };

    const Icon = icons[action];

    const sizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-5 w-5',
      lg: 'h-6 w-6',
    };

    return (
      <div ref={ref} className={cn("flex items-center gap-2", className)}>
        <Icon 
          className={cn(
            sizeClasses[size],
            !prefersReducedMotion && "animate-spin"
          )}
          aria-hidden="true"
        />
        <span className="text-sm text-muted-foreground">{messages[action]}</span>
      </div>
    );
  }
);

ActionLoading.displayName = "ActionLoading";

// Status Indicator Component
export interface StatusIndicatorProps {
  status: 'loading' | 'success' | 'error' | 'idle';
  message?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const StatusIndicator = React.forwardRef<HTMLDivElement, StatusIndicatorProps>(
  ({ status, message, className, size = 'md' }, ref) => {
    const prefersReducedMotion = useReducedMotion();

    const sizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-5 w-5',
      lg: 'h-6 w-6',
    };

    const getStatusContent = () => {
      switch (status) {
        case 'loading':
          return {
            icon: <Loader2 className={cn(sizeClasses[size], !prefersReducedMotion && "animate-spin")} />,
            color: 'text-blue-500',
            defaultMessage: 'Loading...'
          };
        case 'success':
          return {
            icon: <CheckCircle className={sizeClasses[size]} />,
            color: 'text-green-500',
            defaultMessage: 'Success'
          };
        case 'error':
          return {
            icon: <AlertCircle className={sizeClasses[size]} />,
            color: 'text-red-500',
            defaultMessage: 'Error'
          };
        case 'idle':
        default:
          return {
            icon: null,
            color: 'text-muted-foreground',
            defaultMessage: ''
          };
      }
    };

    const { icon, color, defaultMessage } = getStatusContent();

    if (status === 'idle' && !message) {
      return null;
    }

    return (
      <div ref={ref} className={cn("flex items-center gap-2", className)}>
        {icon && <div className={color}>{icon}</div>}
        {(message || defaultMessage) && (
          <span className={cn("text-sm", color)}>
            {message || defaultMessage}
          </span>
        )}
      </div>
    );
  }
);

StatusIndicator.displayName = "StatusIndicator";

// Pulse Loading Component
export interface PulseLoadingProps {
  className?: string;
  count?: number;
  size?: 'sm' | 'md' | 'lg';
}

export const PulseLoading = React.forwardRef<HTMLDivElement, PulseLoadingProps>(
  ({ className, count = 3, size = 'md' }, ref) => {
    const prefersReducedMotion = useReducedMotion();

    const sizeClasses = {
      sm: 'h-2 w-2',
      md: 'h-3 w-3',
      lg: 'h-4 w-4',
    };

    return (
      <div ref={ref} className={cn("flex items-center gap-1", className)}>
        {Array.from({ length: count }).map((_, i) => (
          <div
            key={i}
            className={cn(
              "bg-current rounded-full",
              sizeClasses[size],
              !prefersReducedMotion && "animate-pulse"
            )}
            style={{
              animationDelay: !prefersReducedMotion ? `${i * 0.2}s` : undefined,
            }}
          />
        ))}
      </div>
    );
  }
);

PulseLoading.displayName = "PulseLoading";

// Loading Card Skeleton
export interface LoadingCardProps {
  className?: string;
  showAvatar?: boolean;
  lines?: number;
}

export const LoadingCard = React.forwardRef<HTMLDivElement, LoadingCardProps>(
  ({ className, showAvatar = false, lines = 3 }, ref) => {
    return (
      <div ref={ref} className={cn("p-4 border rounded-lg space-y-3", className)}>
        <div className="flex items-center gap-3">
          {showAvatar && <Skeleton variant="circular" width={40} height={40} />}
          <div className="flex-1 space-y-2">
            <Skeleton variant="text" width="60%" />
            <Skeleton variant="text" width="40%" />
          </div>
        </div>
        <div className="space-y-2">
          <Skeleton variant="text" lines={lines} />
        </div>
      </div>
    );
  }
);

LoadingCard.displayName = "LoadingCard";
