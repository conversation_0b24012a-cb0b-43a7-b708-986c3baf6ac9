"use client";

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Search, 
  Filter, 
  X, 
  History, 
  Star, 
  Save, 
  Trash2,
  ChevronDown,
  Calendar,
  Tag,
  SortAsc,
  SortDesc,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

export interface SearchFilter {
  id: string;
  label: string;
  type: 'text' | 'select' | 'date' | 'number' | 'boolean';
  options?: Array<{ value: string; label: string }>;
  placeholder?: string;
}

export interface SavedSearch {
  id: string;
  name: string;
  query: string;
  filters: Record<string, any>;
  createdAt: Date;
  favorite?: boolean;
}

export interface AdvancedSearchProps {
  placeholder?: string;
  filters?: SearchFilter[];
  onSearch: (query: string, filters: Record<string, any>) => void;
  onClear?: () => void;
  className?: string;
  showHistory?: boolean;
  showSavedSearches?: boolean;
  showKeyboardShortcuts?: boolean;
  debounceMs?: number;
  loading?: boolean;
  savedSearches?: SavedSearch[];
  onSaveSavedSearch?: (search: Omit<SavedSearch, 'id' | 'createdAt'>) => void;
  onDeleteSavedSearch?: (id: string) => void;
  onToggleFavorite?: (id: string) => void;
}

export function AdvancedSearch({
  placeholder = "Search...",
  filters = [],
  onSearch,
  onClear,
  className,
  showHistory = true,
  showSavedSearches = true,
  showKeyboardShortcuts = true,
  debounceMs = 300,
  loading = false,
  savedSearches = [],
  onSaveSavedSearch,
  onDeleteSavedSearch,
  onToggleFavorite
}: AdvancedSearchProps) {
  const { toast } = useToast();
  const [query, setQuery] = useState('');
  const [activeFilters, setActiveFilters] = useState<Record<string, any>>({});
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [saveSearchName, setSaveSearchName] = useState('');
  const debounceRef = useRef<NodeJS.Timeout>();
  const inputRef = useRef<HTMLInputElement>(null);

  // Load search history from localStorage
  useEffect(() => {
    const history = localStorage.getItem('search-history');
    if (history) {
      try {
        setSearchHistory(JSON.parse(history));
      } catch (error) {
        console.warn('Failed to load search history:', error);
      }
    }
  }, []);

  // Debounced search
  const debouncedSearch = useCallback(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }
    
    debounceRef.current = setTimeout(() => {
      onSearch(query, activeFilters);
      
      // Add to history if query is not empty
      if (query.trim()) {
        const newHistory = [query, ...searchHistory.filter(h => h !== query)].slice(0, 10);
        setSearchHistory(newHistory);
        localStorage.setItem('search-history', JSON.stringify(newHistory));
      }
    }, debounceMs);
  }, [query, activeFilters, onSearch, searchHistory, debounceMs]);

  // Trigger search when query or filters change
  useEffect(() => {
    debouncedSearch();
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [debouncedSearch]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (showKeyboardShortcuts) {
        // Ctrl/Cmd + K to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
          e.preventDefault();
          inputRef.current?.focus();
        }
        
        // Escape to clear search
        if (e.key === 'Escape' && document.activeElement === inputRef.current) {
          handleClear();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [showKeyboardShortcuts]);

  const handleClear = () => {
    setQuery('');
    setActiveFilters({});
    onClear?.();
  };

  const handleFilterChange = (filterId: string, value: any) => {
    setActiveFilters(prev => {
      if (value === '' || value === null || value === undefined) {
        const { [filterId]: removed, ...rest } = prev;
        return rest;
      }
      return { ...prev, [filterId]: value };
    });
  };

  const handleSaveSearch = () => {
    if (!saveSearchName.trim()) {
      toast({
        title: "Name required",
        description: "Please enter a name for your saved search.",
        variant: "destructive"
      });
      return;
    }

    onSaveSavedSearch?.({
      name: saveSearchName,
      query,
      filters: activeFilters,
      favorite: false
    });

    setSaveSearchName('');
    setShowSaveDialog(false);
    toast({
      title: "Search saved",
      description: `"${saveSearchName}" has been saved to your searches.`
    });
  };

  const handleLoadSavedSearch = (savedSearch: SavedSearch) => {
    setQuery(savedSearch.query);
    setActiveFilters(savedSearch.filters);
  };

  const activeFilterCount = Object.keys(activeFilters).length;
  const hasActiveSearch = query.trim() || activeFilterCount > 0;

  return (
    <div className={cn("space-y-4", className)}>
      {/* Main Search Bar */}
      <div className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            ref={inputRef}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder={showKeyboardShortcuts ? `${placeholder} (Ctrl+K)` : placeholder}
            className="pl-10 pr-20"
          />
          
          {/* Loading indicator */}
          {loading && (
            <Loader2 className="absolute right-16 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-muted-foreground" />
          )}
          
          {/* Clear button */}
          {hasActiveSearch && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClear}
              className="absolute right-8 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Filter Toggle */}
        {filters.length > 0 && (
          <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="absolute right-2 top-1/2 transform -translate-y-1/2"
              >
                <Filter className="h-4 w-4 mr-1" />
                {activeFilterCount > 0 && (
                  <Badge variant="secondary" className="ml-1 h-5 min-w-5 text-xs">
                    {activeFilterCount}
                  </Badge>
                )}
                <ChevronDown className="h-3 w-3 ml-1" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80" align="end">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Filters</h4>
                  {activeFilterCount > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setActiveFilters({})}
                    >
                      Clear all
                    </Button>
                  )}
                </div>
                
                <div className="space-y-3">
                  {filters.map((filter) => (
                    <div key={filter.id} className="space-y-1">
                      <label className="text-sm font-medium">{filter.label}</label>
                      {filter.type === 'select' ? (
                        <Select
                          value={activeFilters[filter.id] || ''}
                          onValueChange={(value) => handleFilterChange(filter.id, value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder={filter.placeholder} />
                          </SelectTrigger>
                          <SelectContent>
                            {filter.options?.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      ) : (
                        <Input
                          type={filter.type}
                          value={activeFilters[filter.id] || ''}
                          onChange={(e) => handleFilterChange(filter.id, e.target.value)}
                          placeholder={filter.placeholder}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        )}
      </div>

      {/* Active Filters Display */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {Object.entries(activeFilters).map(([filterId, value]) => {
            const filter = filters.find(f => f.id === filterId);
            if (!filter || !value) return null;
            
            const displayValue = filter.type === 'select' 
              ? filter.options?.find(opt => opt.value === value)?.label || value
              : value;

            return (
              <Badge key={filterId} variant="secondary" className="gap-1">
                {filter.label}: {displayValue}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleFilterChange(filterId, '')}
                  className="h-3 w-3 p-0 hover:bg-transparent"
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            );
          })}
        </div>
      )}

      {/* Search History and Saved Searches */}
      {(showHistory || showSavedSearches) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Search History */}
          {showHistory && searchHistory.length > 0 && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <History className="h-4 w-4" />
                  Recent Searches
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <ScrollArea className="h-32">
                  <div className="space-y-1">
                    {searchHistory.map((historyItem, index) => (
                      <Button
                        key={index}
                        variant="ghost"
                        size="sm"
                        onClick={() => setQuery(historyItem)}
                        className="w-full justify-start text-left h-auto p-2"
                      >
                        <span className="truncate">{historyItem}</span>
                      </Button>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          )}

          {/* Saved Searches */}
          {showSavedSearches && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Star className="h-4 w-4" />
                  Saved Searches
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowSaveDialog(true)}
                    className="ml-auto h-6 w-6 p-0"
                  >
                    <Save className="h-3 w-3" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <ScrollArea className="h-32">
                  <div className="space-y-1">
                    {savedSearches.map((savedSearch) => (
                      <div key={savedSearch.id} className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleLoadSavedSearch(savedSearch)}
                          className="flex-1 justify-start text-left h-auto p-2"
                        >
                          <span className="truncate">{savedSearch.name}</span>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onToggleFavorite?.(savedSearch.id)}
                          className="h-6 w-6 p-0"
                        >
                          <Star className={cn("h-3 w-3", savedSearch.favorite && "fill-current")} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDeleteSavedSearch?.(savedSearch.id)}
                          className="h-6 w-6 p-0 text-destructive"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Save Search Dialog */}
      {showSaveDialog && (
        <Card className="border-2 border-primary">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Save Current Search</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Input
              value={saveSearchName}
              onChange={(e) => setSaveSearchName(e.target.value)}
              placeholder="Enter search name..."
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSaveSearch();
                } else if (e.key === 'Escape') {
                  setShowSaveDialog(false);
                }
              }}
            />
            <div className="flex gap-2">
              <Button size="sm" onClick={handleSaveSearch}>
                Save
              </Button>
              <Button size="sm" variant="outline" onClick={() => setShowSaveDialog(false)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
