"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Save, 
  Loader2, 
  CheckCircle, 
  AlertCircle, 
  Info,
  ArrowLeft,
  ArrowRight
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface ModernFormProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  className?: string;
  variant?: 'default' | 'card' | 'inline';
  showProgress?: boolean;
  progress?: number;
  maxSteps?: number;
  currentStep?: number;
  onPrevious?: () => void;
  onNext?: () => void;
  onSubmit?: () => void;
  isSubmitting?: boolean;
  submitLabel?: string;
  showCancel?: boolean;
  onCancel?: () => void;
  cancelLabel?: string;
  errors?: string[];
  warnings?: string[];
  info?: string[];
  success?: string[];
  disabled?: boolean;
  autoSave?: boolean;
  lastSaved?: Date;
  badges?: Array<{ label: string; variant?: 'default' | 'secondary' | 'destructive' | 'outline' }>;
}

export function ModernForm({
  children,
  title,
  description,
  className,
  variant = 'default',
  showProgress = false,
  progress = 0,
  maxSteps,
  currentStep,
  onPrevious,
  onNext,
  onSubmit,
  isSubmitting = false,
  submitLabel = 'Save',
  showCancel = false,
  onCancel,
  cancelLabel = 'Cancel',
  errors = [],
  warnings = [],
  info = [],
  success = [],
  disabled = false,
  autoSave = false,
  lastSaved,
  badges = []
}: ModernFormProps) {
  
  const renderHeader = () => {
    if (!title && !description && !showProgress && badges.length === 0) return null;

    return (
      <div className="space-y-4">
        {(title || description || badges.length > 0) && (
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              {title && (
                <div className="flex items-center gap-2">
                  <h2 className="text-lg font-semibold">{title}</h2>
                  {badges.map((badge, index) => (
                    <Badge key={index} variant={badge.variant}>
                      {badge.label}
                    </Badge>
                  ))}
                </div>
              )}
              {description && (
                <p className="text-sm text-muted-foreground">{description}</p>
              )}
            </div>
            
            {autoSave && lastSaved && (
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <CheckCircle className="h-3 w-3 text-green-600" />
                Saved {lastSaved.toLocaleTimeString()}
              </div>
            )}
          </div>
        )}

        {showProgress && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Progress</span>
              {maxSteps && currentStep && (
                <span className="text-muted-foreground">
                  Step {currentStep} of {maxSteps}
                </span>
              )}
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}

        <Separator />
      </div>
    );
  };

  const renderAlerts = () => {
    const alerts = [
      ...errors.map(error => ({ type: 'error' as const, message: error })),
      ...warnings.map(warning => ({ type: 'warning' as const, message: warning })),
      ...info.map(infoMsg => ({ type: 'info' as const, message: infoMsg })),
      ...success.map(successMsg => ({ type: 'success' as const, message: successMsg }))
    ];

    if (alerts.length === 0) return null;

    return (
      <div className="space-y-2">
        {alerts.map((alert, index) => (
          <Alert 
            key={index} 
            variant={alert.type === 'error' ? 'destructive' : 'default'}
            className={cn(
              alert.type === 'warning' && 'border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-200',
              alert.type === 'info' && 'border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-200',
              alert.type === 'success' && 'border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-950 dark:text-green-200'
            )}
          >
            {alert.type === 'error' && <AlertCircle className="h-4 w-4" />}
            {alert.type === 'warning' && <AlertCircle className="h-4 w-4" />}
            {alert.type === 'info' && <Info className="h-4 w-4" />}
            {alert.type === 'success' && <CheckCircle className="h-4 w-4" />}
            <AlertDescription>{alert.message}</AlertDescription>
          </Alert>
        ))}
      </div>
    );
  };

  const renderActions = () => {
    const hasActions = onPrevious || onNext || onSubmit || showCancel;
    if (!hasActions) return null;

    return (
      <div className="space-y-4">
        <Separator />
        <div className="flex items-center justify-between">
          <div className="flex gap-2">
            {onPrevious && (
              <Button
                type="button"
                variant="outline"
                onClick={onPrevious}
                disabled={disabled || isSubmitting}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
            )}
          </div>
          
          <div className="flex gap-2">
            {showCancel && onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                {cancelLabel}
              </Button>
            )}
            
            {onNext && (
              <Button
                type="button"
                onClick={onNext}
                disabled={disabled || isSubmitting}
              >
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            )}
            
            {onSubmit && (
              <Button
                type="submit"
                onClick={onSubmit}
                disabled={disabled || isSubmitting}
                className="min-w-[100px]"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    {submitLabel}
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  };

  const content = (
    <div className="space-y-6">
      {renderHeader()}
      {renderAlerts()}
      <div className="space-y-4">
        {children}
      </div>
      {renderActions()}
    </div>
  );

  if (variant === 'card') {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          {content}
        </CardContent>
      </Card>
    );
  }

  if (variant === 'inline') {
    return (
      <div className={cn("space-y-4", className)}>
        {children}
      </div>
    );
  }

  // Default variant
  return (
    <div className={cn("space-y-6 p-6 bg-background rounded-lg border", className)}>
      {content}
    </div>
  );
}

// Form field wrapper for consistent styling
export interface ModernFormFieldProps {
  children: React.ReactNode;
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
  className?: string;
  layout?: 'vertical' | 'horizontal';
}

export function ModernFormField({
  children,
  label,
  description,
  error,
  required = false,
  className,
  layout = 'vertical'
}: ModernFormFieldProps) {
  if (layout === 'horizontal') {
    return (
      <div className={cn("grid grid-cols-1 md:grid-cols-3 gap-4 items-start", className)}>
        {label && (
          <div className="space-y-1">
            <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {label}
              {required && <span className="text-destructive ml-1">*</span>}
            </label>
            {description && (
              <p className="text-xs text-muted-foreground">{description}</p>
            )}
          </div>
        )}
        <div className="md:col-span-2 space-y-1">
          {children}
          {error && (
            <p className="text-xs text-destructive flex items-center gap-1">
              <AlertCircle className="h-3 w-3" />
              {error}
            </p>
          )}
        </div>
      </div>
    );
  }

  // Vertical layout (default)
  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </label>
      )}
      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}
      {children}
      {error && (
        <p className="text-xs text-destructive flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          {error}
        </p>
      )}
    </div>
  );
}

// Form section for grouping related fields
export interface ModernFormSectionProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  className?: string;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}

export function ModernFormSection({
  children,
  title,
  description,
  className,
  collapsible = false,
  defaultCollapsed = false
}: ModernFormSectionProps) {
  const [isCollapsed, setIsCollapsed] = React.useState(defaultCollapsed);

  return (
    <div className={cn("space-y-4", className)}>
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <div className="flex items-center justify-between">
              <h3 className="text-base font-medium">{title}</h3>
              {collapsible && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsCollapsed(!isCollapsed)}
                >
                  {isCollapsed ? 'Show' : 'Hide'}
                </Button>
              )}
            </div>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
          <Separator />
        </div>
      )}
      
      {(!collapsible || !isCollapsed) && (
        <div className="space-y-4">
          {children}
        </div>
      )}
    </div>
  );
}
