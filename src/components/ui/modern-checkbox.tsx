"use client";

import React, { useId } from "react";
import { LucideIcon } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

export interface ModernCheckboxItem {
  value: string;
  label: string;
  description?: string;
  icon?: LucideIcon;
  defaultChecked?: boolean;
  disabled?: boolean;
}

export interface ModernCheckboxProps {
  items: ModernCheckboxItem[];
  value?: string[];
  onChange?: (value: string[]) => void;
  className?: string;
  variant?: 'default' | 'card' | 'compact';
  columns?: 1 | 2 | 3 | 4;
  size?: 'sm' | 'md' | 'lg';
}

export function ModernCheckbox({
  items,
  value = [],
  onChange,
  className,
  variant = 'card',
  columns = 2,
  size = 'md'
}: ModernCheckboxProps) {
  const id = useId();

  const handleChange = (itemValue: string, checked: boolean) => {
    if (!onChange) return;
    
    if (checked) {
      onChange([...value, itemValue]);
    } else {
      onChange(value.filter(v => v !== itemValue));
    }
  };

  const getGridCols = () => {
    switch (columns) {
      case 1: return 'grid-cols-1';
      case 2: return 'grid-cols-1 sm:grid-cols-2';
      case 3: return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3';
      case 4: return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4';
      default: return 'grid-cols-1 sm:grid-cols-2';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm': return 'p-3 gap-2';
      case 'md': return 'p-4 gap-3';
      case 'lg': return 'p-6 gap-4';
      default: return 'p-4 gap-3';
    }
  };

  if (variant === 'default') {
    return (
      <div className={cn("space-y-3", className)}>
        {items.map((item) => {
          const isChecked = value.includes(item.value);
          return (
            <div key={`${id}-${item.value}`} className="flex items-center space-x-2">
              <Checkbox
                id={`${id}-${item.value}`}
                checked={isChecked}
                onCheckedChange={(checked) => handleChange(item.value, checked as boolean)}
                disabled={item.disabled}
              />
              <div className="grid gap-1.5 leading-none">
                <Label
                  htmlFor={`${id}-${item.value}`}
                  className={cn(
                    "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
                    item.disabled && "opacity-70"
                  )}
                >
                  {item.label}
                </Label>
                {item.description && (
                  <p className="text-xs text-muted-foreground">
                    {item.description}
                  </p>
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn("flex flex-wrap gap-2", className)}>
        {items.map((item) => {
          const isChecked = value.includes(item.value);
          const Icon = item.icon;
          
          return (
            <div
              key={`${id}-${item.value}`}
              className={cn(
                "relative flex items-center space-x-2 rounded-md border px-3 py-2 text-sm transition-colors",
                "hover:bg-accent hover:text-accent-foreground",
                "has-[:checked]:bg-primary has-[:checked]:text-primary-foreground",
                "has-[:disabled]:opacity-50 has-[:disabled]:cursor-not-allowed",
                item.disabled && "opacity-50 cursor-not-allowed"
              )}
            >
              <Checkbox
                id={`${id}-${item.value}`}
                checked={isChecked}
                onCheckedChange={(checked) => handleChange(item.value, checked as boolean)}
                disabled={item.disabled}
                className="sr-only"
              />
              {Icon && <Icon size={16} className="opacity-60" aria-hidden="true" />}
              <Label
                htmlFor={`${id}-${item.value}`}
                className="cursor-pointer text-sm font-medium"
              >
                {item.label}
              </Label>
            </div>
          );
        })}
      </div>
    );
  }

  // Card variant (default)
  return (
    <div className={cn("grid gap-3", getGridCols(), className)}>
      {items.map((item) => {
        const isChecked = value.includes(item.value);
        const Icon = item.icon;
        
        return (
          <div
            key={`${id}-${item.value}`}
            className={cn(
              "relative flex cursor-pointer flex-col rounded-md border border-input shadow-sm outline-none transition-colors",
              "hover:bg-accent/50",
              "has-[:checked]:border-primary/50 has-[:checked]:bg-primary/5",
              "has-[:disabled]:opacity-50 has-[:disabled]:cursor-not-allowed",
              getSizeClasses(),
              item.disabled && "opacity-50 cursor-not-allowed"
            )}
          >
            <div className="flex justify-between items-start gap-2">
              <Checkbox
                id={`${id}-${item.value}`}
                checked={isChecked}
                onCheckedChange={(checked) => handleChange(item.value, checked as boolean)}
                disabled={item.disabled}
                className="order-1 after:absolute after:inset-0"
              />
              {Icon && <Icon className="opacity-60" size={16} aria-hidden="true" />}
            </div>
            <div className="space-y-1">
              <Label
                htmlFor={`${id}-${item.value}`}
                className="text-sm font-medium leading-none cursor-pointer"
              >
                {item.label}
              </Label>
              {item.description && (
                <p className="text-xs text-muted-foreground leading-relaxed">
                  {item.description}
                </p>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}

// Export individual components for flexibility
export { ModernCheckboxItem };

// Helper function to create checkbox items
export const createCheckboxItem = (
  value: string,
  label: string,
  options?: Partial<Omit<ModernCheckboxItem, 'value' | 'label'>>
): ModernCheckboxItem => ({
  value,
  label,
  ...options
});
