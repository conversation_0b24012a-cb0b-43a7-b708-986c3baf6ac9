"use client";

import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { FormLabel, FormDescription, FormMessage } from '@/components/ui/form';
import { LucideIcon, Plus } from 'lucide-react';

// ============================================================================
// CENTRALIZED FORM DESIGN SYSTEM
// ============================================================================
// This is the single source of truth for all form styling across EVEXA
// All forms (events, exhibitions, etc.) should use these components
// Theme-aware and consistent across the entire application
// ============================================================================

// Form Container - Main wrapper for all forms
interface FormContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '4xl' | 'full';
}

export const FormContainer: React.FC<FormContainerProps> = ({ 
  children, 
  className,
  maxWidth = '4xl'
}) => {
  const maxWidthClasses = {
    'sm': 'max-w-sm',
    'md': 'max-w-md', 
    'lg': 'max-w-lg',
    'xl': 'max-w-xl',
    '2xl': 'max-w-2xl',
    '4xl': 'max-w-4xl',
    'full': 'max-w-full'
  };

  return (
    <div className={cn(
      'space-y-6 mx-auto',
      maxWidthClasses[maxWidth],
      className
    )}>
      {children}
    </div>
  );
};

// Form Section Card - Theme-aware card for form sections
interface FormSectionCardProps {
  children: React.ReactNode;
  variant?: 'default' | 'primary' | 'secondary' | 'success';
  className?: string;
}

export const FormSectionCard: React.FC<FormSectionCardProps> = ({ 
  children, 
  variant = 'default',
  className 
}) => {
  const variantClasses = {
    'default': 'bg-card border-border hover:border-border/80',
    'primary': 'bg-card border-primary/30 hover:border-primary/40',
    'secondary': 'bg-card border-primary/20 hover:border-primary/30',
    'success': 'bg-card border-green-500/30 hover:border-green-500/40'
  };

  return (
    <div className={cn(
      // Base styles
      'relative p-6 rounded-xl border shadow-sm transition-all duration-200',
      'hover:shadow-md',
      // Theme-aware background gradient
      'before:absolute before:inset-0 before:rounded-xl before:pointer-events-none',
      'before:bg-gradient-to-br before:from-primary/[0.02] before:to-primary/[0.05]',
      // Variant-specific styles
      variantClasses[variant],
      // Ensure content is above pseudo-elements
      '[&>*]:relative [&>*]:z-10',
      className
    )}>
      {children}
    </div>
  );
};

// Form Section Header - Consistent header with icon
interface FormSectionHeaderProps {
  title: string;
  icon?: LucideIcon;
  description?: string;
  variant?: 'default' | 'primary' | 'secondary' | 'success';
  className?: string;
}

export const FormSectionHeader: React.FC<FormSectionHeaderProps> = ({
  title,
  icon: Icon,
  description,
  variant = 'default',
  className
}) => {
  const variantClasses = {
    'default': 'text-foreground',
    'primary': 'text-primary',
    'secondary': 'text-primary/80', 
    'success': 'text-green-600 dark:text-green-400'
  };

  const iconVariantClasses = {
    'default': 'bg-muted text-muted-foreground',
    'primary': 'bg-primary/10 text-primary',
    'secondary': 'bg-primary/5 text-primary/80',
    'success': 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400'
  };

  return (
    <div className={cn('space-y-3', className)}>
      <div className="flex items-center">
        {Icon && (
          <div className={cn(
            'p-2 rounded-lg mr-3 transition-colors',
            iconVariantClasses[variant]
          )}>
            <Icon className="h-5 w-5" />
          </div>
        )}
        <h3 className={cn(
          'text-lg font-semibold',
          variantClasses[variant]
        )}>
          {title}
        </h3>
      </div>
      {description && (
        <FormDescription className="text-sm text-muted-foreground/80">
          {description}
        </FormDescription>
      )}
    </div>
  );
};

// Form Field Group - Wrapper for form fields with consistent spacing
interface FormFieldGroupProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3;
  className?: string;
}

export const FormFieldGroup: React.FC<FormFieldGroupProps> = ({ 
  children, 
  columns = 1,
  className 
}) => {
  const columnClasses = {
    1: 'space-y-4',
    2: 'grid grid-cols-1 lg:grid-cols-2 gap-6',
    3: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
  };

  return (
    <div className={cn(columnClasses[columns], className)}>
      {children}
    </div>
  );
};

// Enhanced Form Label - Consistent styling for all form labels
interface EnhancedFormLabelProps {
  children: React.ReactNode;
  required?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const EnhancedFormLabel: React.FC<EnhancedFormLabelProps> = ({
  children,
  required = false,
  size = 'md',
  className
}) => {
  const sizeClasses = {
    'sm': 'text-sm font-medium',
    'md': 'text-base font-medium', 
    'lg': 'text-lg font-semibold'
  };

  return (
    <FormLabel className={cn(
      sizeClasses[size],
      'text-foreground/90',
      className
    )}>
      {children}
      {required && <span className="text-destructive ml-1">*</span>}
    </FormLabel>
  );
};

// Add Item Button - Consistent button for adding form items with sliding animation
interface AddItemButtonProps {
  onClick: () => void;
  label: string;
  icon?: LucideIcon;
  variant?: 'default' | 'outline' | 'secondary';
  className?: string;
}

export const AddItemButton: React.FC<AddItemButtonProps> = ({
  onClick,
  label,
  icon: Icon = Plus,
  variant = 'outline',
  className
}) => {
  return (
    <Button
      type="button"
      variant={variant}
      onClick={onClick}
      className={cn(
        // Sliding icon animation
        'group relative overflow-hidden transition-all duration-200',
        'hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-none',
        // Theme-aware styling
        'bg-background/80 hover:bg-background border-border/60 hover:border-border',
        className
      )}
    >
      <span className="absolute -start-full transition-all group-hover:start-2">
        <Icon className="h-4 w-4" />
      </span>
      <span className="text-sm font-medium transition-all group-hover:ms-2">
        {label}
      </span>
    </Button>
  );
};

// Remove Item Button - Consistent remove button
interface RemoveItemButtonProps {
  onClick: () => void;
  ariaLabel: string;
  className?: string;
}

export const RemoveItemButton: React.FC<RemoveItemButtonProps> = ({
  onClick,
  ariaLabel,
  className
}) => {
  return (
    <Button
      type="button"
      variant="destructive"
      size="icon"
      onClick={onClick}
      className={cn(
        'absolute top-2 right-2 h-7 w-7 transition-all duration-200',
        'hover:scale-110 focus:ring-2 focus:outline-none',
        className
      )}
      aria-label={ariaLabel}
    >
      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
      </svg>
    </Button>
  );
};

// Form Grid - Responsive grid for form layouts
interface FormGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const FormGrid: React.FC<FormGridProps> = ({ 
  children, 
  columns = 2,
  gap = 'md',
  className 
}) => {
  const columnClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 lg:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  };

  const gapClasses = {
    'sm': 'gap-4',
    'md': 'gap-6',
    'lg': 'gap-8'
  };

  return (
    <div className={cn(
      'grid',
      columnClasses[columns],
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  );
};

// Enhanced Input Classes - Theme-aware input styling
export const getEnhancedInputClasses = (variant: 'default' | 'large' = 'default') => {
  const baseClasses = 'transition-all duration-200 bg-background/80 border-border/60 hover:bg-background hover:border-border focus:ring-2 focus:ring-primary/20 focus:border-primary/50';

  const variantClasses = {
    'default': 'h-11',
    'large': 'h-12 text-lg'
  };

  return cn(baseClasses, variantClasses[variant]);
};

// Enhanced Textarea Classes
export const getEnhancedTextareaClasses = () => {
  return 'transition-all duration-200 bg-background/80 border-border/60 hover:bg-background hover:border-border focus:ring-2 focus:ring-primary/20 focus:border-primary/50 resize-none';
};

// Enhanced Select Classes
export const getEnhancedSelectClasses = () => {
  return 'transition-all duration-200 bg-background border-border hover:bg-accent/50 hover:border-border focus:ring-2 focus:ring-primary/20 focus:border-primary/50';
};

// Enhanced Button Classes
export const getEnhancedButtonClasses = (variant: 'outline' | 'default' = 'outline') => {
  const baseClasses = 'transition-all duration-200';

  const variantClasses = {
    'outline': 'bg-background/80 hover:bg-background border-border/60 hover:border-border',
    'default': 'bg-primary hover:bg-primary/90 text-primary-foreground'
  };

  return cn(baseClasses, variantClasses[variant]);
};

// Form Section - Complete section with header and content
interface FormSectionProps {
  title: string;
  icon?: LucideIcon;
  description?: string;
  variant?: 'default' | 'primary' | 'secondary' | 'success';
  children: React.ReactNode;
  className?: string;
}

export const FormSection: React.FC<FormSectionProps> = ({
  title,
  icon,
  description,
  variant = 'secondary',
  children,
  className
}) => {
  return (
    <FormSectionCard variant={variant} className={className}>
      <FormSectionHeader
        title={title}
        icon={icon}
        description={description}
        variant={variant}
      />
      <FormFieldGroup>
        {children}
      </FormFieldGroup>
    </FormSectionCard>
  );
};

// Repeatable Form Item - For dynamic form arrays (vendors, staff, etc.)
interface RepeatableFormItemProps {
  title: string;
  icon?: LucideIcon;
  index: number;
  onRemove: () => void;
  removeLabel: string;
  children: React.ReactNode;
  className?: string;
}

export const RepeatableFormItem: React.FC<RepeatableFormItemProps> = ({
  title,
  icon,
  index,
  onRemove,
  removeLabel,
  children,
  className
}) => {
  return (
    <FormSectionCard variant="secondary" className={cn('relative', className)}>
      <FormSectionHeader
        title={`${title} #${index + 1}`}
        icon={icon}
        variant="secondary"
      />
      <FormFieldGroup>
        {children}
      </FormFieldGroup>
      <RemoveItemButton
        onClick={onRemove}
        ariaLabel={`Remove ${removeLabel}`}
      />
    </FormSectionCard>
  );
};
