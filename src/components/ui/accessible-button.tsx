"use client";

import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { Loader2 } from "lucide-react";

import { cn } from "@/lib/utils";
import { useReducedMotion, focusVisibleClasses, announceToScreenReader } from "@/lib/accessibility";

const accessibleButtonVariants = cva(
  [
    "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium",
    "transition-colors duration-200 ease-in-out",
    "disabled:pointer-events-none disabled:opacity-50",
    "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
    // High contrast mode support
    "forced-colors:border forced-colors:border-[ButtonBorder]",
    // Touch target size (minimum 44x44px for WCAG AA)
    "min-h-[44px] min-w-[44px]",
    // Ensure sufficient padding for touch targets
    "px-4 py-2"
  ].join(" "),
  {
    variants: {
      variant: {
        default: [
          "bg-primary text-primary-foreground shadow",
          "hover:bg-primary/90",
          "active:bg-primary/95",
          // High contrast mode
          "forced-colors:bg-[ButtonFace] forced-colors:text-[ButtonText]",
          "forced-colors:hover:bg-[Highlight] forced-colors:hover:text-[HighlightText]"
        ].join(" "),
        destructive: [
          "bg-destructive text-destructive-foreground shadow-sm",
          "hover:bg-destructive/90",
          "active:bg-destructive/95",
          "forced-colors:bg-[ButtonFace] forced-colors:text-[ButtonText]"
        ].join(" "),
        outline: [
          "border border-input bg-background shadow-sm",
          "hover:bg-accent hover:text-accent-foreground",
          "active:bg-accent/90",
          "forced-colors:border-[ButtonBorder] forced-colors:bg-[ButtonFace] forced-colors:text-[ButtonText]"
        ].join(" "),
        secondary: [
          "bg-secondary text-secondary-foreground shadow-sm",
          "hover:bg-secondary/80",
          "active:bg-secondary/90",
          "forced-colors:bg-[ButtonFace] forced-colors:text-[ButtonText]"
        ].join(" "),
        ghost: [
          "hover:bg-accent hover:text-accent-foreground",
          "active:bg-accent/90",
          "forced-colors:hover:bg-[Highlight] forced-colors:hover:text-[HighlightText]"
        ].join(" "),
        link: [
          "text-primary underline-offset-4",
          "hover:underline",
          "active:text-primary/90",
          "forced-colors:text-[LinkText] forced-colors:hover:text-[VisitedText]"
        ].join(" "),
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3 text-xs min-h-[36px]", // Still meets minimum touch target
        lg: "h-12 rounded-md px-8 min-h-[48px]",
        icon: "h-10 w-10 min-h-[44px] min-w-[44px]", // Ensure icon buttons meet touch target size
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface AccessibleButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof accessibleButtonVariants> {
  asChild?: boolean;
  loading?: boolean;
  loadingText?: string;
  successMessage?: string;
  errorMessage?: string;
  announceOnClick?: boolean;
  announceMessage?: string;
}

const AccessibleButton = React.forwardRef<HTMLButtonElement, AccessibleButtonProps>(
  ({ 
    className, 
    variant, 
    size, 
    asChild = false, 
    loading = false,
    loadingText,
    successMessage,
    errorMessage,
    announceOnClick = false,
    announceMessage,
    disabled,
    children,
    onClick,
    ...props 
  }, ref) => {
    const Comp = asChild ? Slot : "button";
    const prefersReducedMotion = useReducedMotion();
    const [actionState, setActionState] = React.useState<'idle' | 'loading' | 'success' | 'error'>('idle');

    // Generate unique IDs for ARIA relationships
    const buttonId = React.useId();
    const statusId = `${buttonId}-status`;

    const handleClick = async (event: React.MouseEvent<HTMLButtonElement>) => {
      if (loading || disabled) return;

      // Announce click action if requested
      if (announceOnClick && announceMessage) {
        announceToScreenReader(announceMessage, 'polite');
      }

      if (onClick) {
        try {
          setActionState('loading');
          await onClick(event);
          
          if (successMessage) {
            setActionState('success');
            announceToScreenReader(successMessage, 'polite');
            setTimeout(() => setActionState('idle'), 3000);
          } else {
            setActionState('idle');
          }
        } catch (error) {
          setActionState('error');
          if (errorMessage) {
            announceToScreenReader(errorMessage, 'assertive');
          }
          setTimeout(() => setActionState('idle'), 3000);
        }
      }
    };

    const isLoading = loading || actionState === 'loading';
    const isDisabled = disabled || isLoading;

    // Determine button content based on state
    const getButtonContent = () => {
      if (isLoading) {
        return (
          <>
            <Loader2 
              className={cn(
                "h-4 w-4",
                prefersReducedMotion ? "" : "animate-spin"
              )} 
              aria-hidden="true"
            />
            {loadingText || "Loading..."}
          </>
        );
      }
      return children;
    };

    // ARIA attributes for better accessibility
    const ariaAttributes = {
      'aria-disabled': isDisabled,
      'aria-describedby': statusId,
      ...(isLoading && { 'aria-busy': true }),
      ...(actionState === 'success' && { 'aria-live': 'polite' }),
      ...(actionState === 'error' && { 'aria-live': 'assertive' }),
    };

    return (
      <>
        <Comp
          className={cn(accessibleButtonVariants({ variant, size, className }))}
          ref={ref}
          disabled={isDisabled}
          onClick={handleClick}
          {...ariaAttributes}
          {...props}
        >
          {getButtonContent()}
        </Comp>
        
        {/* Hidden status region for screen readers */}
        <div
          id={statusId}
          className="sr-only"
          aria-live="polite"
          aria-atomic="true"
        >
          {actionState === 'loading' && (loadingText || "Loading")}
          {actionState === 'success' && successMessage}
          {actionState === 'error' && errorMessage}
        </div>
      </>
    );
  }
);

AccessibleButton.displayName = "AccessibleButton";

export { AccessibleButton, accessibleButtonVariants };

// Enhanced Button Group for related actions
export interface ButtonGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  orientation?: 'horizontal' | 'vertical';
  children: React.ReactNode;
}

export const AccessibleButtonGroup = React.forwardRef<HTMLDivElement, ButtonGroupProps>(
  ({ className, orientation = 'horizontal', children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        role="group"
        className={cn(
          "inline-flex",
          orientation === 'horizontal' ? "flex-row" : "flex-col",
          "[&>button]:rounded-none",
          "[&>button:first-child]:rounded-l-md",
          "[&>button:last-child]:rounded-r-md",
          orientation === 'vertical' && [
            "[&>button:first-child]:rounded-t-md [&>button:first-child]:rounded-l-none",
            "[&>button:last-child]:rounded-b-md [&>button:last-child]:rounded-r-none"
          ],
          "[&>button:not(:first-child)]:border-l-0",
          orientation === 'vertical' && "[&>button:not(:first-child)]:border-t-0 [&>button:not(:first-child)]:border-l",
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

AccessibleButtonGroup.displayName = "AccessibleButtonGroup";

// Floating Action Button with accessibility enhancements
export interface FloatingActionButtonProps extends AccessibleButtonProps {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  label: string; // Required for accessibility
}

export const FloatingActionButton = React.forwardRef<HTMLButtonElement, FloatingActionButtonProps>(
  ({ className, position = 'bottom-right', label, ...props }, ref) => {
    const positionClasses = {
      'bottom-right': 'bottom-6 right-6',
      'bottom-left': 'bottom-6 left-6',
      'top-right': 'top-6 right-6',
      'top-left': 'top-6 left-6',
    };

    return (
      <AccessibleButton
        ref={ref}
        className={cn(
          "fixed z-50 rounded-full shadow-lg",
          "h-14 w-14 min-h-[56px] min-w-[56px]", // Larger touch target for FAB
          positionClasses[position],
          className
        )}
        size="icon"
        aria-label={label}
        {...props}
      />
    );
  }
);

FloatingActionButton.displayName = "FloatingActionButton";
