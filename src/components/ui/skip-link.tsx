"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

export interface SkipLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
}

export const SkipLink = React.forwardRef<HTMLAnchorElement, SkipLinkProps>(
  ({ href, children, className }, ref) => {
    return (
      <a
        ref={ref}
        href={href}
        className={cn(
          "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50",
          "focus:px-4 focus:py-2 focus:bg-primary focus:text-primary-foreground",
          "focus:rounded-md focus:shadow-lg focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
          "transition-all duration-200",
          className
        )}
      >
        {children}
      </a>
    );
  }
);

SkipLink.displayName = "SkipLink";
