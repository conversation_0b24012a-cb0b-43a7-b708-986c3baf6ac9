"use client";

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FixedSizeList as List } from 'react-window';
import { 
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  type ColumnDef,
  type SortingState,
  type ColumnFiltersState,
  type VisibilityState,
  type RowSelectionState,
} from '@tanstack/react-table';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TableActions, type TableAction } from '@/components/ui/table-actions';
import {
  ChevronDown,
  ChevronUp,
  ChevronsUpDown,
  Download,
  Filter,
  MoreHorizontal,
  Search,
  Settings2,
  X,
  ArrowUpDown,
  Eye,
  EyeOff,
  RefreshCw,
  Columns3,
  SortAsc,
  SortDesc,
  FileText,
  FileSpreadsheet,
  Printer,
  Plus
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface AdvancedTableColumn<T> extends ColumnDef<T> {
  accessorKey?: string;
  title?: string;
  sortable?: boolean;
  filterable?: boolean;
  resizable?: boolean;
  exportable?: boolean;
  width?: number;
  minWidth?: number;
  maxWidth?: number;
}

export interface AdvancedTableRowAction<T> {
  type: 'view' | 'edit' | 'delete' | 'copy' | 'download' | 'email' | 'call' | 'message' | 'schedule' | 'favorite' | 'archive' | 'share' | 'external' | 'custom';
  label: string;
  href?: string;
  onClick?: (row: T) => void;
  icon?: React.ReactNode;
  variant?: 'default' | 'destructive' | 'secondary';
  disabled?: (row: T) => boolean;
  hidden?: (row: T) => boolean;
}

export interface AdvancedTableFilter {
  id: string;
  label: string;
  type: 'text' | 'select' | 'date' | 'number' | 'boolean';
  options?: { label: string; value: string }[];
  placeholder?: string;
}

export interface AdvancedTableAction<T> {
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  onClick: (rows: T[]) => void;
  variant?: 'default' | 'destructive' | 'secondary';
  disabled?: (rows: T[]) => boolean;
  requiresSelection?: boolean;
}

export interface AdvancedDataTableProps<T> {
  data: T[];
  columns: AdvancedTableColumn<T>[];

  // Virtual scrolling
  enableVirtualization?: boolean;
  rowHeight?: number;
  containerHeight?: number;

  // Filtering & Search
  enableGlobalSearch?: boolean;
  enableColumnFilters?: boolean;
  filters?: AdvancedTableFilter[];
  searchPlaceholder?: string;

  // Sorting
  enableSorting?: boolean;
  defaultSorting?: SortingState;

  // Selection
  enableRowSelection?: boolean;
  enableMultiRowSelection?: boolean;
  onRowSelectionChange?: (selectedRows: T[]) => void;

  // Pagination
  enablePagination?: boolean;
  pageSize?: number;
  pageSizeOptions?: number[];

  // Column management
  enableColumnResizing?: boolean;
  enableColumnReordering?: boolean;
  enableColumnVisibility?: boolean;

  // Bulk actions
  bulkActions?: AdvancedTableAction<T>[];

  // Row actions
  enableRowActions?: boolean;
  rowActions?: (row: T) => AdvancedTableRowAction<T>[];
  maxVisibleRowActions?: number;

  // Export
  enableExport?: boolean;
  exportFormats?: ('csv' | 'excel' | 'pdf')[];
  exportFileName?: string;

  // Primary action (Create/Add button)
  primaryAction?: {
    label: string;
    onClick: () => void;
    icon?: React.ComponentType<{ className?: string }>;
  };

  // Mobile responsiveness
  mobileBreakpoint?: number;
  mobileCardRenderer?: (item: T) => React.ReactNode;

  // Loading & Empty states
  loading?: boolean;
  error?: string | null;
  emptyMessage?: string;

  // Styling
  className?: string;
  variant?: 'default' | 'compact' | 'comfortable';

  // Callbacks
  onRefresh?: () => void;
  onRowClick?: (row: T) => void;
}

export function AdvancedDataTable<T>({
  data,
  columns,
  enableVirtualization = false,
  rowHeight = 52,
  containerHeight = 600,
  enableGlobalSearch = true,
  enableColumnFilters = true,
  filters = [],
  searchPlaceholder = "Search...",
  enableSorting = true,
  defaultSorting = [],
  enableRowSelection = false,
  enableMultiRowSelection = true,
  onRowSelectionChange,
  enablePagination = true,
  pageSize = 50,
  pageSizeOptions = [10, 25, 50, 100],
  enableColumnResizing = true,
  enableColumnReordering = false,
  enableColumnVisibility = true,
  bulkActions = [],
  enableRowActions = true,
  rowActions,
  maxVisibleRowActions = 3,
  enableExport = true,
  exportFormats = ['csv', 'excel'],
  exportFileName = 'data-export',
  primaryAction,
  mobileBreakpoint = 768,
  mobileCardRenderer,
  loading = false,
  error = null,
  emptyMessage = "No data available",
  className,
  variant = 'default',
  onRefresh,
  onRowClick,
}: AdvancedDataTableProps<T>) {
  const [sorting, setSorting] = React.useState<SortingState>(defaultSorting);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({});
  const [globalFilter, setGlobalFilter] = React.useState('');
  const [isMobile, setIsMobile] = React.useState(false);

  // Responsive detection
  React.useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < mobileBreakpoint);
    };
    
    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, [mobileBreakpoint]);

  // Enhanced columns with selection and actions
  const enhancedColumns = React.useMemo(() => {
    const cols = [...columns];

    if (enableRowSelection) {
      cols.unshift({
        id: 'select',
        header: ({ table }) => (
          <Checkbox
            checked={table.getIsAllPageRowsSelected()}
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Select all"
            className="translate-y-[2px]"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
            className="translate-y-[2px]"
          />
        ),
        enableSorting: false,
        enableHiding: false,
        size: 40,
      });
    }

    // Add actions column if row actions are enabled
    if (enableRowActions && rowActions) {
      cols.push({
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => {
          const actions = rowActions(row.original);
          const tableActions: TableAction[] = actions.map(action => ({
            type: action.type,
            label: action.label,
            href: action.href,
            onClick: action.onClick ? () => action.onClick!(row.original) : undefined,
            icon: action.icon,
            variant: action.variant,
            disabled: action.disabled ? action.disabled(row.original) : false,
            hidden: action.hidden ? action.hidden(row.original) : false,
          }));

          return (
            <TableActions
              actions={tableActions}
              size="sm"
              maxVisible={maxVisibleRowActions}
              showLabels={false}
            />
          );
        },
        enableSorting: false,
        enableHiding: false,
        size: 120,
      });
    }

    return cols;
  }, [columns, enableRowSelection, enableRowActions, rowActions, maxVisibleRowActions]);

  const table = useReactTable({
    data,
    columns: enhancedColumns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: enablePagination ? getPaginationRowModel() : undefined,
    getSortedRowModel: enableSorting ? getSortedRowModel() : undefined,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
    enableRowSelection,
    enableMultiRowSelection,
    enableColumnResizing,
    columnResizeMode: 'onChange',
  });

  // Handle row selection changes
  React.useEffect(() => {
    if (onRowSelectionChange) {
      const selectedRows = table.getFilteredSelectedRowModel().rows.map(row => row.original);
      onRowSelectionChange(selectedRows);
    }
  }, [rowSelection, onRowSelectionChange, table]);

  const selectedRowCount = table.getFilteredSelectedRowModel().rows.length;
  const totalRowCount = table.getFilteredRowModel().rows.length;

  if (loading) {
    return <TableSkeleton variant={variant} />;
  }

  if (error) {
    return <TableError error={error} onRetry={onRefresh} />;
  }

  if (isMobile && mobileCardRenderer) {
    return (
      <MobileCardView
        data={data}
        cardRenderer={mobileCardRenderer}
        onRowClick={onRowClick}
        className={className}
      />
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Table Toolbar */}
      <TableToolbar
        table={table}
        enableGlobalSearch={enableGlobalSearch}
        searchPlaceholder={searchPlaceholder}
        globalFilter={globalFilter}
        setGlobalFilter={setGlobalFilter}
        enableColumnFilters={enableColumnFilters}
        filters={filters}
        enableColumnVisibility={enableColumnVisibility}
        enableExport={enableExport}
        exportFormats={exportFormats}
        exportFileName={exportFileName}
        selectedRowCount={selectedRowCount}
        totalRowCount={totalRowCount}
        bulkActions={bulkActions}
        primaryAction={primaryAction}
        onRefresh={onRefresh}
        variant={variant}
      />

      {/* Table Container */}
      <div className="rounded-md border">
        {enableVirtualization && data.length > 100 ? (
          <VirtualizedTable
            table={table}
            rowHeight={rowHeight}
            containerHeight={containerHeight}
            onRowClick={onRowClick}
            variant={variant}
          />
        ) : (
          <StandardTable
            table={table}
            onRowClick={onRowClick}
            variant={variant}
            emptyMessage={emptyMessage}
          />
        )}
      </div>

      {/* Pagination */}
      {enablePagination && (
        <TablePagination
          table={table}
          pageSizeOptions={pageSizeOptions}
          selectedRowCount={selectedRowCount}
          totalRowCount={totalRowCount}
        />
      )}
    </div>
  );
}

// Skeleton loading component
function TableSkeleton({ variant }: { variant: string }) {
  const rowCount = variant === 'compact' ? 8 : 6;
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="h-10 w-64 bg-muted animate-pulse rounded" />
        <div className="flex gap-2">
          <div className="h-10 w-24 bg-muted animate-pulse rounded" />
          <div className="h-10 w-24 bg-muted animate-pulse rounded" />
        </div>
      </div>
      
      <div className="rounded-md border">
        <div className="p-4 space-y-3">
          {Array.from({ length: rowCount }).map((_, i) => (
            <div key={i} className="flex items-center space-x-4">
              <div className="h-4 w-4 bg-muted animate-pulse rounded" />
              <div className="h-4 flex-1 bg-muted animate-pulse rounded" />
              <div className="h-4 w-24 bg-muted animate-pulse rounded" />
              <div className="h-4 w-16 bg-muted animate-pulse rounded" />
              <div className="h-4 w-20 bg-muted animate-pulse rounded" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Error component
function TableError({ error, onRetry }: { error: string; onRetry?: () => void }) {
  return (
    <Card>
      <CardContent className="flex flex-col items-center justify-center py-12">
        <div className="text-center space-y-4">
          <div className="text-destructive font-medium">Error loading data</div>
          <p className="text-sm text-muted-foreground">{error}</p>
          {onRetry && (
            <Button onClick={onRetry} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Table Toolbar Component
function TableToolbar<T>({
  table,
  enableGlobalSearch,
  searchPlaceholder,
  globalFilter,
  setGlobalFilter,
  enableColumnFilters,
  filters,
  enableColumnVisibility,
  enableExport,
  exportFormats,
  exportFileName,
  selectedRowCount,
  totalRowCount,
  bulkActions,
  primaryAction,
  onRefresh,
  variant
}: any) {
  const [showFilters, setShowFilters] = React.useState(false);

  return (
    <div className="space-y-4">
      {/* Main toolbar - New Design */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 py-3 px-4 bg-muted/30 rounded-lg border">
        <div className="flex flex-1 items-center gap-4">
          {/* Global Search */}
          {enableGlobalSearch && (
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={searchPlaceholder}
                value={globalFilter ?? ''}
                onChange={(e) => setGlobalFilter(e.target.value)}
                className="pl-10"
              />
              {globalFilter && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                  onClick={() => setGlobalFilter('')}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          )}

          {/* Results count - when no selection */}
          {selectedRowCount === 0 && (
            <div className="text-sm text-muted-foreground whitespace-nowrap">
              {totalRowCount} results
            </div>
          )}

          {/* Bulk Actions - Show when rows selected */}
          {selectedRowCount > 0 && bulkActions.length > 0 && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground whitespace-nowrap">
                {selectedRowCount} selected
              </span>
              {bulkActions.map((action, index) => (
                <button
                  key={index}
                  onClick={() => action.onClick(table.getFilteredSelectedRowModel().rows.map(row => row.original))}
                  disabled={action.disabled?.(table.getFilteredSelectedRowModel().rows.map(row => row.original))}
                  className="group relative inline-flex items-center overflow-hidden rounded-md border border-current px-3 py-1.5 text-sm text-foreground hover:bg-accent transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="absolute -start-full transition-all group-hover:start-2">
                    {action.icon && <action.icon className="size-4" />}
                  </span>
                  <span className="text-sm font-medium transition-all group-hover:ms-2">{action.label}</span>
                </button>
              ))}
            </div>
          )}
        </div>

        <div className="flex items-center gap-2 flex-wrap">
          {/* Primary Action Button - Dynamic */}
          {primaryAction && (
            <button
              onClick={primaryAction.onClick}
              className="group relative inline-flex items-center overflow-hidden rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90 transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-hidden"
            >
              <span className="absolute -start-full transition-all group-hover:start-2">
                {primaryAction.icon ? <primaryAction.icon className="size-4" /> : <Plus className="size-4" />}
              </span>
              <span className="text-sm font-medium transition-all group-hover:ms-2">{primaryAction.label}</span>
            </button>
          )}

          {/* Actions Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="group relative inline-flex items-center overflow-hidden rounded-md border border-input px-3 py-2 text-sm text-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden">
                <span className="absolute -start-full transition-all group-hover:start-2">
                  <ChevronDown className="size-4" />
                </span>
                <span className="text-sm font-medium transition-all group-hover:ms-2">Actions</span>
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px]">
              {/* Column Visibility */}
              {enableColumnVisibility && (
                <>
                  <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {table
                    .getAllColumns()
                    .filter((column) => column.getCanHide())
                    .map((column) => {
                      return (
                        <DropdownMenuCheckboxItem
                          key={column.id}
                          className="capitalize"
                          checked={column.getIsVisible()}
                          onCheckedChange={(value) => column.toggleVisibility(!!value)}
                        >
                          {column.columnDef.title || column.id}
                        </DropdownMenuCheckboxItem>
                      );
                    })}
                  <DropdownMenuSeparator />
                </>
              )}

              {/* Export Options */}
              {enableExport && (
                <>
                  <DropdownMenuLabel>Export data</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {exportFormats?.includes('csv') && (
                    <DropdownMenuItem onClick={() => exportToCSV(table, exportFileName)}>
                      <FileText className="mr-2 h-4 w-4" />
                      Export as CSV
                    </DropdownMenuItem>
                  )}
                  {exportFormats?.includes('excel') && (
                    <DropdownMenuItem onClick={() => exportToExcel(table, exportFileName)}>
                      <FileSpreadsheet className="mr-2 h-4 w-4" />
                      Export as Excel
                    </DropdownMenuItem>
                  )}
                  {exportFormats?.includes('pdf') && (
                    <DropdownMenuItem onClick={() => exportToPDF(table, exportFileName)}>
                      <Printer className="mr-2 h-4 w-4" />
                      Export as PDF
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuSeparator />
                </>
              )}

              {/* Refresh */}
              {onRefresh && (
                <DropdownMenuItem onClick={onRefresh}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Refresh
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Filter Button */}
          {enableColumnFilters && filters.length > 0 && (
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={cn(
                "group relative inline-flex items-center overflow-hidden rounded-md border border-input px-3 py-2 text-sm transition-all duration-200 hover:scale-105 hover:shadow-md focus:ring-2 focus:outline-hidden",
                showFilters
                  ? "bg-accent text-accent-foreground"
                  : "text-foreground hover:bg-accent hover:text-accent-foreground"
              )}
            >
              <span className="absolute -start-full transition-all group-hover:start-2">
                <Filter className="size-4" />
              </span>
              <span className="text-sm font-medium transition-all group-hover:ms-2">
                Filter
                {table.getState().columnFilters.length > 0 && (
                  <Badge variant="secondary" className="ml-2 h-5 w-5 rounded-full p-0 text-xs">
                    {table.getState().columnFilters.length}
                  </Badge>
                )}
              </span>
            </button>
          )}
        </div>
      </div>

      {/* Advanced filters */}
      {showFilters && enableColumnFilters && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 p-4 bg-muted/30 rounded-lg"
        >
          {filters.map((filter) => (
            <div key={filter.id} className="space-y-2">
              <label className="text-sm font-medium">{filter.label}</label>
              {filter.type === 'select' ? (
                <Select
                  value={(() => {
                    const column = table.getColumn(filter.id);
                    if (!column) {
                      console.warn(`Column with id '${filter.id}' does not exist in table`);
                      return '';
                    }
                    return (column.getFilterValue() as string) ?? '';
                  })()}
                  onValueChange={(value) => {
                    const column = table.getColumn(filter.id);
                    if (column) {
                      column.setFilterValue(value === 'all' ? '' : value);
                    } else {
                      console.warn(`Column with id '${filter.id}' does not exist in table`);
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={filter.placeholder} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    {filter.options?.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <Input
                  placeholder={filter.placeholder}
                  value={(() => {
                    const column = table.getColumn(filter.id);
                    if (!column) {
                      console.warn(`Column with id '${filter.id}' does not exist in table`);
                      return '';
                    }
                    return (column.getFilterValue() as string) ?? '';
                  })()}
                  onChange={(e) => {
                    const column = table.getColumn(filter.id);
                    if (column) {
                      column.setFilterValue(e.target.value);
                    } else {
                      console.warn(`Column with id '${filter.id}' does not exist in table`);
                    }
                  }}
                  type={filter.type === 'number' ? 'number' : filter.type === 'date' ? 'date' : 'text'}
                />
              )}
            </div>
          ))}
        </motion.div>
      )}
    </div>
  );
}

// Standard Table Component
function StandardTable<T>({ table, onRowClick, variant, emptyMessage }: any) {
  const rows = table.getRowModel().rows;

  if (rows.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="text-muted-foreground mb-2">No results found</div>
        <p className="text-sm text-muted-foreground">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        {table.getHeaderGroups().map((headerGroup: any) => (
          <TableRow key={headerGroup.id}>
            {headerGroup.headers.map((header: any) => (
              <TableHead
                key={header.id}
                className={cn(
                  "relative",
                  header.column.getCanSort() && "cursor-pointer select-none hover:bg-muted/50",
                  variant === 'compact' && "py-2 text-xs"
                )}
                style={{ width: header.getSize() }}
                onClick={header.column.getToggleSortingHandler()}
              >
                <div className="flex items-center space-x-2">
                  {header.isPlaceholder ? null : (
                    <>
                      {flexRender(header.column.columnDef.header, header.getContext())}
                      {header.column.getCanSort() && (
                        <div className="flex flex-col">
                          {header.column.getIsSorted() === 'asc' ? (
                            <ChevronUp className="h-3 w-3" />
                          ) : header.column.getIsSorted() === 'desc' ? (
                            <ChevronDown className="h-3 w-3" />
                          ) : (
                            <ChevronsUpDown className="h-3 w-3 opacity-50" />
                          )}
                        </div>
                      )}
                    </>
                  )}
                </div>

                {/* Column resizer */}
                {header.column.getCanResize() && (
                  <div
                    onMouseDown={header.getResizeHandler()}
                    onTouchStart={header.getResizeHandler()}
                    className="absolute right-0 top-0 h-full w-1 bg-border hover:bg-primary cursor-col-resize opacity-0 hover:opacity-100 transition-opacity"
                  />
                )}
              </TableHead>
            ))}
          </TableRow>
        ))}
      </TableHeader>
      <TableBody>
        <AnimatePresence>
          {rows.map((row: any, index: number) => (
            <motion.tr
              key={row.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2, delay: index * 0.02 }}
              className={cn(
                "border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",
                onRowClick && "cursor-pointer",
                variant === 'compact' && "text-sm"
              )}
              onClick={() => onRowClick?.(row.original)}
            >
              {row.getVisibleCells().map((cell: any) => (
                <TableCell
                  key={cell.id}
                  className={cn(
                    variant === 'compact' && "py-2",
                    variant === 'comfortable' && "py-6"
                  )}
                  style={{ width: cell.column.getSize() }}
                >
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </TableCell>
              ))}
            </motion.tr>
          ))}
        </AnimatePresence>
      </TableBody>
    </Table>
  );
}

// Virtualized Table Component
function VirtualizedTable<T>({ table, rowHeight, containerHeight, onRowClick, variant }: any) {
  const rows = table.getRowModel().rows;

  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => {
    const row = rows[index];

    return (
      <div
        style={style}
        className={cn(
          "flex items-center border-b transition-colors hover:bg-muted/50",
          onRowClick && "cursor-pointer",
          row.getIsSelected() && "bg-muted"
        )}
        onClick={() => onRowClick?.(row.original)}
      >
        {row.getVisibleCells().map((cell: any) => (
          <div
            key={cell.id}
            className={cn(
              "flex items-center px-4 truncate",
              variant === 'compact' && "text-sm"
            )}
            style={{ width: cell.column.getSize(), minWidth: cell.column.getSize() }}
          >
            {flexRender(cell.column.columnDef.cell, cell.getContext())}
          </div>
        ))}
      </div>
    );
  };

  if (rows.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="text-muted-foreground mb-2">No results found</div>
        <p className="text-sm text-muted-foreground">Try adjusting your search or filters</p>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="border-b bg-muted/30">
        {table.getHeaderGroups().map((headerGroup: any) => (
          <div key={headerGroup.id} className="flex">
            {headerGroup.headers.map((header: any) => (
              <div
                key={header.id}
                className={cn(
                  "flex items-center px-4 py-3 font-medium text-muted-foreground",
                  header.column.getCanSort() && "cursor-pointer select-none hover:bg-muted/50",
                  variant === 'compact' && "py-2 text-xs"
                )}
                style={{ width: header.getSize(), minWidth: header.getSize() }}
                onClick={header.column.getToggleSortingHandler()}
              >
                <div className="flex items-center space-x-2 truncate">
                  {header.isPlaceholder ? null : (
                    <>
                      {flexRender(header.column.columnDef.header, header.getContext())}
                      {header.column.getCanSort() && (
                        <div className="flex flex-col">
                          {header.column.getIsSorted() === 'asc' ? (
                            <ChevronUp className="h-3 w-3" />
                          ) : header.column.getIsSorted() === 'desc' ? (
                            <ChevronDown className="h-3 w-3" />
                          ) : (
                            <ChevronsUpDown className="h-3 w-3 opacity-50" />
                          )}
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>

      {/* Virtual List */}
      <List
        height={containerHeight}
        itemCount={rows.length}
        itemSize={rowHeight}
        itemData={rows}
      >
        {Row}
      </List>
    </div>
  );
}

// Mobile Card View Component
function MobileCardView<T>({
  data,
  cardRenderer,
  onRowClick,
  className
}: {
  data: T[];
  cardRenderer: (item: T) => React.ReactNode;
  onRowClick?: (row: T) => void;
  className?: string;
}) {
  if (data.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="text-muted-foreground mb-2">No results found</div>
        <p className="text-sm text-muted-foreground">Try adjusting your search or filters</p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      <AnimatePresence>
        {data.map((item, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2, delay: index * 0.05 }}
            className={cn(
              "rounded-lg border bg-card p-4 shadow-sm transition-all hover:shadow-md",
              onRowClick && "cursor-pointer hover:bg-accent/50"
            )}
            onClick={() => onRowClick?.(item)}
          >
            {cardRenderer(item)}
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}

// Table Pagination Component
function TablePagination<T>({
  table,
  pageSizeOptions,
  selectedRowCount,
  totalRowCount
}: {
  table: any;
  pageSizeOptions: number[];
  selectedRowCount: number;
  totalRowCount: number;
}) {
  return (
    <div className="flex items-center justify-between px-2">
      <div className="flex items-center space-x-6 lg:space-x-8">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">Rows per page</p>
          <Select
            value={`${table.getState().pagination.pageSize}`}
            onValueChange={(value) => {
              table.setPageSize(Number(value));
            }}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={table.getState().pagination.pageSize} />
            </SelectTrigger>
            <SelectContent side="top">
              {pageSizeOptions.map((pageSize) => (
                <SelectItem key={pageSize} value={`${pageSize}`}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
          Page {table.getState().pagination.pageIndex + 1} of{" "}
          {table.getPageCount()}
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
          >
            <span className="sr-only">Go to first page</span>
            <ChevronDown className="h-4 w-4 rotate-90" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <span className="sr-only">Go to previous page</span>
            <ChevronDown className="h-4 w-4 rotate-90" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            <span className="sr-only">Go to next page</span>
            <ChevronDown className="h-4 w-4 -rotate-90" />
          </Button>
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => table.setPageIndex(table.getPageCount() - 1)}
            disabled={!table.getCanNextPage()}
          >
            <span className="sr-only">Go to last page</span>
            <ChevronDown className="h-4 w-4 -rotate-90" />
          </Button>
        </div>
      </div>

      <div className="text-sm text-muted-foreground">
        {selectedRowCount > 0 ? (
          <span>{selectedRowCount} of {totalRowCount} row(s) selected</span>
        ) : (
          <span>{totalRowCount} row(s) total</span>
        )}
      </div>
    </div>
  );
}

// Export Functions
function exportToCSV(table: any, filename: string) {
  const headers = table.getVisibleFlatColumns()
    .filter((col: any) => col.id !== 'select' && col.id !== 'actions')
    .map((col: any) => col.columnDef.title || col.id);

  const rows = table.getFilteredRowModel().rows.map((row: any) =>
    table.getVisibleFlatColumns()
      .filter((col: any) => col.id !== 'select' && col.id !== 'actions')
      .map((col: any) => {
        const value = row.getValue(col.id);
        return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value;
      })
  );

  const csvContent = [headers.join(','), ...rows.map(row => row.join(','))].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

function exportToExcel(table: any, filename: string) {
  // This would require a library like xlsx
  console.log('Excel export would be implemented with xlsx library');
  exportToCSV(table, filename); // Fallback to CSV for now
}

function exportToPDF(table: any, filename: string) {
  // This would require a library like jsPDF
  console.log('PDF export would be implemented with jsPDF library');
  exportToCSV(table, filename); // Fallback to CSV for now
}
