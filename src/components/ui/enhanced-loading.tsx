/**
 * EVEXA Enhanced Loading Components
 * Comprehensive loading states with animations and accessibility
 */

'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { loadingVariants, type LoadingVariants } from '@/lib/component-variants';
import { usePerformanceMonitor } from '@/lib/performance-utils';

// Enhanced Loading Spinner
export interface LoadingSpinnerProps extends LoadingVariants {
  className?: string;
  label?: string;
  'aria-label'?: string;
}

export const LoadingSpinner = React.forwardRef<HTMLDivElement, LoadingSpinnerProps>(({
  variant = "spinner",
  size = "default",
  color = "default",
  className,
  label,
  'aria-label': ariaLabel,
  ...props
}, ref) => {
  usePerformanceMonitor('LoadingSpinner');

  if (variant === "dots") {
    return (
      <div
        ref={ref}
        className={cn("inline-flex items-center space-x-1", className)}
        role="status"
        aria-label={ariaLabel || label || "Loading"}
        {...props}
      >
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className={cn(
              "rounded-full bg-current animate-pulse",
              size === "xs" && "h-1 w-1",
              size === "sm" && "h-1.5 w-1.5",
              size === "default" && "h-2 w-2",
              size === "lg" && "h-2.5 w-2.5",
              size === "xl" && "h-3 w-3",
              color === "primary" && "text-primary",
              color === "secondary" && "text-secondary",
              color === "accent" && "text-accent",
              color === "muted" && "text-muted-foreground",
              color === "default" && "text-muted-foreground"
            )}
            style={{
              animationDelay: `${i * 0.2}s`,
              animationDuration: '1.4s'
            }}
          />
        ))}
        {label && <span className="ml-2 text-sm text-muted-foreground">{label}</span>}
      </div>
    );
  }

  return (
    <div
      ref={ref}
      className={cn("inline-flex items-center", className)}
      role="status"
      aria-label={ariaLabel || label || "Loading"}
      {...props}
    >
      <div className={cn(loadingVariants({ variant, size, color }))} />
      {label && <span className="ml-2 text-sm text-muted-foreground">{label}</span>}
    </div>
  );
});

LoadingSpinner.displayName = 'LoadingSpinner';

// Loading Overlay Component
export interface LoadingOverlayProps {
  isLoading: boolean;
  children: React.ReactNode;
  variant?: LoadingVariants['variant'];
  size?: LoadingVariants['size'];
  color?: LoadingVariants['color'];
  label?: string;
  className?: string;
  overlayClassName?: string;
  blur?: boolean;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  children,
  variant = "spinner",
  size = "lg",
  color = "primary",
  label = "Loading...",
  className,
  overlayClassName,
  blur = true,
}) => {
  usePerformanceMonitor('LoadingOverlay');

  return (
    <div className={cn("relative", className)}>
      {children}
      {isLoading && (
        <div
          className={cn(
            "absolute inset-0 flex items-center justify-center bg-background/80 z-50",
            blur && "backdrop-blur-sm",
            overlayClassName
          )}
        >
          <div className="flex flex-col items-center space-y-4">
            <LoadingSpinner
              variant={variant}
              size={size}
              color={color}
            />
            {label && (
              <p className="text-sm text-muted-foreground font-medium">
                {label}
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Loading Button Component
export interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean;
  loadingText?: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'xs' | 'sm' | 'default' | 'lg' | 'xl';
  children: React.ReactNode;
}

export const LoadingButton = React.forwardRef<HTMLButtonElement, LoadingButtonProps>(({
  isLoading = false,
  loadingText,
  variant = "default",
  size = "default",
  children,
  disabled,
  className,
  ...props
}, ref) => {
  usePerformanceMonitor('LoadingButton');

  return (
    <button
      ref={ref}
      disabled={disabled || isLoading}
      className={cn(
        "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
        // Variant styles
        variant === "default" && "bg-primary text-primary-foreground hover:bg-primary/90",
        variant === "destructive" && "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        variant === "outline" && "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        variant === "secondary" && "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        variant === "ghost" && "hover:bg-accent hover:text-accent-foreground",
        variant === "link" && "text-primary underline-offset-4 hover:underline",
        // Size styles
        size === "xs" && "h-8 px-2 text-xs",
        size === "sm" && "h-9 px-3 text-xs",
        size === "default" && "h-10 px-4 py-2",
        size === "lg" && "h-11 px-8",
        size === "xl" && "h-12 px-10 text-base",
        className
      )}
      {...props}
    >
      {isLoading && (
        <LoadingSpinner
          variant="spinner"
          size={size === "xs" ? "xs" : size === "sm" ? "sm" : "sm"}
          color="default"
        />
      )}
      {isLoading ? (loadingText || "Loading...") : children}
    </button>
  );
});

LoadingButton.displayName = 'LoadingButton';

// Loading Card Component
export interface LoadingCardProps {
  isLoading: boolean;
  children: React.ReactNode;
  className?: string;
  loadingLines?: number;
  showAvatar?: boolean;
}

export const LoadingCard: React.FC<LoadingCardProps> = ({
  isLoading,
  children,
  className,
  loadingLines = 3,
  showAvatar = false,
}) => {
  usePerformanceMonitor('LoadingCard');

  if (isLoading) {
    return (
      <div className={cn("p-6 border rounded-lg bg-card", className)}>
        <div className="space-y-4">
          {showAvatar && (
            <div className="flex items-center space-x-4">
              <div className="h-12 w-12 bg-muted rounded-full animate-pulse" />
              <div className="space-y-2 flex-1">
                <div className="h-4 bg-muted rounded animate-pulse w-1/4" />
                <div className="h-3 bg-muted rounded animate-pulse w-1/3" />
              </div>
            </div>
          )}
          <div className="space-y-3">
            {Array.from({ length: loadingLines }, (_, i) => (
              <div
                key={i}
                className={cn(
                  "h-4 bg-muted rounded animate-pulse",
                  i === loadingLines - 1 ? "w-2/3" : "w-full"
                )}
                style={{ animationDelay: `${i * 0.1}s` }}
              />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("p-6 border rounded-lg bg-card", className)}>
      {children}
    </div>
  );
};

// Progress Loading Component
export interface ProgressLoadingProps {
  progress: number;
  label?: string;
  showPercentage?: boolean;
  className?: string;
  variant?: 'default' | 'success' | 'warning' | 'error';
}

export const ProgressLoading: React.FC<ProgressLoadingProps> = ({
  progress,
  label,
  showPercentage = true,
  className,
  variant = "default",
}) => {
  usePerformanceMonitor('ProgressLoading');

  const clampedProgress = Math.min(Math.max(progress, 0), 100);

  return (
    <div className={cn("space-y-2", className)}>
      {(label || showPercentage) && (
        <div className="flex justify-between items-center">
          {label && <span className="text-sm font-medium">{label}</span>}
          {showPercentage && (
            <span className="text-sm text-muted-foreground">
              {Math.round(clampedProgress)}%
            </span>
          )}
        </div>
      )}
      <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
        <div
          className={cn(
            "h-full transition-all duration-300 ease-out rounded-full",
            variant === "default" && "bg-primary",
            variant === "success" && "bg-success",
            variant === "warning" && "bg-warning",
            variant === "error" && "bg-error"
          )}
          style={{ width: `${clampedProgress}%` }}
        />
      </div>
    </div>
  );
};

export default LoadingSpinner;
