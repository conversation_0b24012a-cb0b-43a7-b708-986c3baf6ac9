"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

export interface LiveRegionProps {
  liveRegionRef: React.RefObject<HTMLDivElement>;
  className?: string;
}

export const LiveRegion = React.forwardRef<HTMLDivElement, LiveRegionProps>(
  ({ liveRegionRef, className }, ref) => {
    return (
      <div
        ref={liveRegionRef}
        aria-live="polite"
        aria-atomic="true"
        className={cn("sr-only", className)}
      />
    );
  }
);

LiveRegion.displayName = "LiveRegion";
