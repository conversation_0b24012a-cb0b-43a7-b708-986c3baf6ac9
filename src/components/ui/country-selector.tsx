"use client";

import React, { useState, useMemo } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Check, ChevronsUpDown, Globe, Search } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface Country {
  code: string; // ISO 3166-1 alpha-2
  name: string;
  flag: string;
  dialCode: string;
  currency: string;
  continent: string;
  popular?: boolean;
}

// Comprehensive country list with all ISO countries
const COUNTRIES: Country[] = [
  // Popular countries first
  { code: 'US', name: 'United States', flag: '🇺🇸', dialCode: '+1', currency: 'USD', continent: 'North America', popular: true },
  { code: 'GB', name: 'United Kingdom', flag: '🇬🇧', dialCode: '+44', currency: 'GBP', continent: 'Europe', popular: true },
  { code: 'CA', name: 'Canada', flag: '🇨🇦', dialCode: '+1', currency: 'CAD', continent: 'North America', popular: true },
  { code: 'AU', name: 'Australia', flag: '🇦🇺', dialCode: '+61', currency: 'AUD', continent: 'Oceania', popular: true },
  { code: 'DE', name: 'Germany', flag: '🇩🇪', dialCode: '+49', currency: 'EUR', continent: 'Europe', popular: true },
  { code: 'FR', name: 'France', flag: '🇫🇷', dialCode: '+33', currency: 'EUR', continent: 'Europe', popular: true },
  { code: 'JP', name: 'Japan', flag: '🇯🇵', dialCode: '+81', currency: 'JPY', continent: 'Asia', popular: true },
  { code: 'CN', name: 'China', flag: '🇨🇳', dialCode: '+86', currency: 'CNY', continent: 'Asia', popular: true },
  { code: 'IN', name: 'India', flag: '🇮🇳', dialCode: '+91', currency: 'INR', continent: 'Asia', popular: true },
  { code: 'SG', name: 'Singapore', flag: '🇸🇬', dialCode: '+65', currency: 'SGD', continent: 'Asia', popular: true },
  
  // All other countries alphabetically
  { code: 'AD', name: 'Andorra', flag: '🇦🇩', dialCode: '+376', currency: 'EUR', continent: 'Europe' },
  { code: 'AE', name: 'United Arab Emirates', flag: '🇦🇪', dialCode: '+971', currency: 'AED', continent: 'Asia' },
  { code: 'AF', name: 'Afghanistan', flag: '🇦🇫', dialCode: '+93', currency: 'AFN', continent: 'Asia' },
  { code: 'AG', name: 'Antigua and Barbuda', flag: '🇦🇬', dialCode: '+1', currency: 'XCD', continent: 'North America' },
  { code: 'AI', name: 'Anguilla', flag: '🇦🇮', dialCode: '+1', currency: 'XCD', continent: 'North America' },
  { code: 'AL', name: 'Albania', flag: '🇦🇱', dialCode: '+355', currency: 'ALL', continent: 'Europe' },
  { code: 'AM', name: 'Armenia', flag: '🇦🇲', dialCode: '+374', currency: 'AMD', continent: 'Asia' },
  { code: 'AO', name: 'Angola', flag: '🇦🇴', dialCode: '+244', currency: 'AOA', continent: 'Africa' },
  { code: 'AQ', name: 'Antarctica', flag: '🇦🇶', dialCode: '+672', currency: 'USD', continent: 'Antarctica' },
  { code: 'AR', name: 'Argentina', flag: '🇦🇷', dialCode: '+54', currency: 'ARS', continent: 'South America' },
  { code: 'AS', name: 'American Samoa', flag: '🇦🇸', dialCode: '+1', currency: 'USD', continent: 'Oceania' },
  { code: 'AT', name: 'Austria', flag: '🇦🇹', dialCode: '+43', currency: 'EUR', continent: 'Europe' },
  { code: 'AW', name: 'Aruba', flag: '🇦🇼', dialCode: '+297', currency: 'AWG', continent: 'North America' },
  { code: 'AX', name: 'Åland Islands', flag: '🇦🇽', dialCode: '+358', currency: 'EUR', continent: 'Europe' },
  { code: 'AZ', name: 'Azerbaijan', flag: '🇦🇿', dialCode: '+994', currency: 'AZN', continent: 'Asia' },
  { code: 'BA', name: 'Bosnia and Herzegovina', flag: '🇧🇦', dialCode: '+387', currency: 'BAM', continent: 'Europe' },
  { code: 'BB', name: 'Barbados', flag: '🇧🇧', dialCode: '+1', currency: 'BBD', continent: 'North America' },
  { code: 'BD', name: 'Bangladesh', flag: '🇧🇩', dialCode: '+880', currency: 'BDT', continent: 'Asia' },
  { code: 'BE', name: 'Belgium', flag: '🇧🇪', dialCode: '+32', currency: 'EUR', continent: 'Europe' },
  { code: 'BF', name: 'Burkina Faso', flag: '🇧🇫', dialCode: '+226', currency: 'XOF', continent: 'Africa' },
  { code: 'BG', name: 'Bulgaria', flag: '🇧🇬', dialCode: '+359', currency: 'BGN', continent: 'Europe' },
  { code: 'BH', name: 'Bahrain', flag: '🇧🇭', dialCode: '+973', currency: 'BHD', continent: 'Asia' },
  { code: 'BI', name: 'Burundi', flag: '🇧🇮', dialCode: '+257', currency: 'BIF', continent: 'Africa' },
  { code: 'BJ', name: 'Benin', flag: '🇧🇯', dialCode: '+229', currency: 'XOF', continent: 'Africa' },
  { code: 'BL', name: 'Saint Barthélemy', flag: '🇧🇱', dialCode: '+590', currency: 'EUR', continent: 'North America' },
  { code: 'BM', name: 'Bermuda', flag: '🇧🇲', dialCode: '+1', currency: 'BMD', continent: 'North America' },
  { code: 'BN', name: 'Brunei', flag: '🇧🇳', dialCode: '+673', currency: 'BND', continent: 'Asia' },
  { code: 'BO', name: 'Bolivia', flag: '🇧🇴', dialCode: '+591', currency: 'BOB', continent: 'South America' },
  { code: 'BQ', name: 'Caribbean Netherlands', flag: '🇧🇶', dialCode: '+599', currency: 'USD', continent: 'North America' },
  { code: 'BR', name: 'Brazil', flag: '🇧🇷', dialCode: '+55', currency: 'BRL', continent: 'South America' },
  { code: 'BS', name: 'Bahamas', flag: '🇧🇸', dialCode: '+1', currency: 'BSD', continent: 'North America' },
  { code: 'BT', name: 'Bhutan', flag: '🇧🇹', dialCode: '+975', currency: 'BTN', continent: 'Asia' },
  { code: 'BV', name: 'Bouvet Island', flag: '🇧🇻', dialCode: '+47', currency: 'NOK', continent: 'Antarctica' },
  { code: 'BW', name: 'Botswana', flag: '🇧🇼', dialCode: '+267', currency: 'BWP', continent: 'Africa' },
  { code: 'BY', name: 'Belarus', flag: '🇧🇾', dialCode: '+375', currency: 'BYN', continent: 'Europe' },
  { code: 'BZ', name: 'Belize', flag: '🇧🇿', dialCode: '+501', currency: 'BZD', continent: 'North America' },
  { code: 'CC', name: 'Cocos Islands', flag: '🇨🇨', dialCode: '+61', currency: 'AUD', continent: 'Asia' },
  { code: 'CD', name: 'Democratic Republic of the Congo', flag: '🇨🇩', dialCode: '+243', currency: 'CDF', continent: 'Africa' },
  { code: 'CF', name: 'Central African Republic', flag: '🇨🇫', dialCode: '+236', currency: 'XAF', continent: 'Africa' },
  { code: 'CG', name: 'Republic of the Congo', flag: '🇨🇬', dialCode: '+242', currency: 'XAF', continent: 'Africa' },
  { code: 'CH', name: 'Switzerland', flag: '🇨🇭', dialCode: '+41', currency: 'CHF', continent: 'Europe' },
  { code: 'CI', name: 'Côte d\'Ivoire', flag: '🇨🇮', dialCode: '+225', currency: 'XOF', continent: 'Africa' },
  { code: 'CK', name: 'Cook Islands', flag: '🇨🇰', dialCode: '+682', currency: 'NZD', continent: 'Oceania' },
  { code: 'CL', name: 'Chile', flag: '🇨🇱', dialCode: '+56', currency: 'CLP', continent: 'South America' },
  { code: 'CM', name: 'Cameroon', flag: '🇨🇲', dialCode: '+237', currency: 'XAF', continent: 'Africa' },
  { code: 'CO', name: 'Colombia', flag: '🇨🇴', dialCode: '+57', currency: 'COP', continent: 'South America' },
  { code: 'CR', name: 'Costa Rica', flag: '🇨🇷', dialCode: '+506', currency: 'CRC', continent: 'North America' },
  { code: 'CU', name: 'Cuba', flag: '🇨🇺', dialCode: '+53', currency: 'CUP', continent: 'North America' },
  { code: 'CV', name: 'Cape Verde', flag: '🇨🇻', dialCode: '+238', currency: 'CVE', continent: 'Africa' },
  { code: 'CW', name: 'Curaçao', flag: '🇨🇼', dialCode: '+599', currency: 'ANG', continent: 'North America' },
  { code: 'CX', name: 'Christmas Island', flag: '🇨🇽', dialCode: '+61', currency: 'AUD', continent: 'Asia' },
  { code: 'CY', name: 'Cyprus', flag: '🇨🇾', dialCode: '+357', currency: 'EUR', continent: 'Europe' },
  { code: 'CZ', name: 'Czech Republic', flag: '🇨🇿', dialCode: '+420', currency: 'CZK', continent: 'Europe' },
  { code: 'DJ', name: 'Djibouti', flag: '🇩🇯', dialCode: '+253', currency: 'DJF', continent: 'Africa' },
  { code: 'DK', name: 'Denmark', flag: '🇩🇰', dialCode: '+45', currency: 'DKK', continent: 'Europe' },
  { code: 'DM', name: 'Dominica', flag: '🇩🇲', dialCode: '+1', currency: 'XCD', continent: 'North America' },
  { code: 'DO', name: 'Dominican Republic', flag: '🇩🇴', dialCode: '+1', currency: 'DOP', continent: 'North America' },
  { code: 'DZ', name: 'Algeria', flag: '🇩🇿', dialCode: '+213', currency: 'DZD', continent: 'Africa' },
  { code: 'EC', name: 'Ecuador', flag: '🇪🇨', dialCode: '+593', currency: 'USD', continent: 'South America' },
  { code: 'EE', name: 'Estonia', flag: '🇪🇪', dialCode: '+372', currency: 'EUR', continent: 'Europe' },
  { code: 'EG', name: 'Egypt', flag: '🇪🇬', dialCode: '+20', currency: 'EGP', continent: 'Africa' },
  { code: 'EH', name: 'Western Sahara', flag: '🇪🇭', dialCode: '+212', currency: 'MAD', continent: 'Africa' },
  { code: 'ER', name: 'Eritrea', flag: '🇪🇷', dialCode: '+291', currency: 'ERN', continent: 'Africa' },
  { code: 'ES', name: 'Spain', flag: '🇪🇸', dialCode: '+34', currency: 'EUR', continent: 'Europe' },
  { code: 'ET', name: 'Ethiopia', flag: '🇪🇹', dialCode: '+251', currency: 'ETB', continent: 'Africa' },
  { code: 'FI', name: 'Finland', flag: '🇫🇮', dialCode: '+358', currency: 'EUR', continent: 'Europe' },
  { code: 'FJ', name: 'Fiji', flag: '🇫🇯', dialCode: '+679', currency: 'FJD', continent: 'Oceania' },
  { code: 'FK', name: 'Falkland Islands', flag: '🇫🇰', dialCode: '+500', currency: 'FKP', continent: 'South America' },
  { code: 'FM', name: 'Micronesia', flag: '🇫🇲', dialCode: '+691', currency: 'USD', continent: 'Oceania' },
  { code: 'FO', name: 'Faroe Islands', flag: '🇫🇴', dialCode: '+298', currency: 'DKK', continent: 'Europe' },
  { code: 'GA', name: 'Gabon', flag: '🇬🇦', dialCode: '+241', currency: 'XAF', continent: 'Africa' },
  { code: 'GD', name: 'Grenada', flag: '🇬🇩', dialCode: '+1', currency: 'XCD', continent: 'North America' },
  { code: 'GE', name: 'Georgia', flag: '🇬🇪', dialCode: '+995', currency: 'GEL', continent: 'Asia' },
  { code: 'GF', name: 'French Guiana', flag: '🇬🇫', dialCode: '+594', currency: 'EUR', continent: 'South America' },
  { code: 'GG', name: 'Guernsey', flag: '🇬🇬', dialCode: '+44', currency: 'GBP', continent: 'Europe' },
  { code: 'GH', name: 'Ghana', flag: '🇬🇭', dialCode: '+233', currency: 'GHS', continent: 'Africa' },
  { code: 'GI', name: 'Gibraltar', flag: '🇬🇮', dialCode: '+350', currency: 'GIP', continent: 'Europe' },
  { code: 'GL', name: 'Greenland', flag: '🇬🇱', dialCode: '+299', currency: 'DKK', continent: 'North America' },
  { code: 'GM', name: 'Gambia', flag: '🇬🇲', dialCode: '+220', currency: 'GMD', continent: 'Africa' },
  { code: 'GN', name: 'Guinea', flag: '🇬🇳', dialCode: '+224', currency: 'GNF', continent: 'Africa' },
  { code: 'GP', name: 'Guadeloupe', flag: '🇬🇵', dialCode: '+590', currency: 'EUR', continent: 'North America' },
  { code: 'GQ', name: 'Equatorial Guinea', flag: '🇬🇶', dialCode: '+240', currency: 'XAF', continent: 'Africa' },
  { code: 'GR', name: 'Greece', flag: '🇬🇷', dialCode: '+30', currency: 'EUR', continent: 'Europe' },
  { code: 'GS', name: 'South Georgia', flag: '🇬🇸', dialCode: '+500', currency: 'GBP', continent: 'Antarctica' },
  { code: 'GT', name: 'Guatemala', flag: '🇬🇹', dialCode: '+502', currency: 'GTQ', continent: 'North America' },
  { code: 'GU', name: 'Guam', flag: '🇬🇺', dialCode: '+1', currency: 'USD', continent: 'Oceania' },
  { code: 'GW', name: 'Guinea-Bissau', flag: '🇬🇼', dialCode: '+245', currency: 'XOF', continent: 'Africa' },
  { code: 'GY', name: 'Guyana', flag: '🇬🇾', dialCode: '+592', currency: 'GYD', continent: 'South America' },
  { code: 'HK', name: 'Hong Kong', flag: '🇭🇰', dialCode: '+852', currency: 'HKD', continent: 'Asia' },
  { code: 'HM', name: 'Heard Island', flag: '🇭🇲', dialCode: '+672', currency: 'AUD', continent: 'Antarctica' },
  { code: 'HN', name: 'Honduras', flag: '🇭🇳', dialCode: '+504', currency: 'HNL', continent: 'North America' },
  { code: 'HR', name: 'Croatia', flag: '🇭🇷', dialCode: '+385', currency: 'EUR', continent: 'Europe' },
  { code: 'HT', name: 'Haiti', flag: '🇭🇹', dialCode: '+509', currency: 'HTG', continent: 'North America' },
  { code: 'HU', name: 'Hungary', flag: '🇭🇺', dialCode: '+36', currency: 'HUF', continent: 'Europe' },
  { code: 'ID', name: 'Indonesia', flag: '🇮🇩', dialCode: '+62', currency: 'IDR', continent: 'Asia' },
  { code: 'IE', name: 'Ireland', flag: '🇮🇪', dialCode: '+353', currency: 'EUR', continent: 'Europe' },
  { code: 'IL', name: 'Israel', flag: '🇮🇱', dialCode: '+972', currency: 'ILS', continent: 'Asia' },
  { code: 'IM', name: 'Isle of Man', flag: '🇮🇲', dialCode: '+44', currency: 'GBP', continent: 'Europe' },
  { code: 'IO', name: 'British Indian Ocean Territory', flag: '🇮🇴', dialCode: '+246', currency: 'USD', continent: 'Asia' },
  { code: 'IQ', name: 'Iraq', flag: '🇮🇶', dialCode: '+964', currency: 'IQD', continent: 'Asia' },
  { code: 'IR', name: 'Iran', flag: '🇮🇷', dialCode: '+98', currency: 'IRR', continent: 'Asia' },
  { code: 'IS', name: 'Iceland', flag: '🇮🇸', dialCode: '+354', currency: 'ISK', continent: 'Europe' },
  { code: 'IT', name: 'Italy', flag: '🇮🇹', dialCode: '+39', currency: 'EUR', continent: 'Europe' },
  { code: 'JE', name: 'Jersey', flag: '🇯🇪', dialCode: '+44', currency: 'GBP', continent: 'Europe' },
  { code: 'JM', name: 'Jamaica', flag: '🇯🇲', dialCode: '+1', currency: 'JMD', continent: 'North America' },
  { code: 'JO', name: 'Jordan', flag: '🇯🇴', dialCode: '+962', currency: 'JOD', continent: 'Asia' },
  { code: 'KE', name: 'Kenya', flag: '🇰🇪', dialCode: '+254', currency: 'KES', continent: 'Africa' },
  { code: 'KG', name: 'Kyrgyzstan', flag: '🇰🇬', dialCode: '+996', currency: 'KGS', continent: 'Asia' },
  { code: 'KH', name: 'Cambodia', flag: '🇰🇭', dialCode: '+855', currency: 'KHR', continent: 'Asia' },
  { code: 'KI', name: 'Kiribati', flag: '🇰🇮', dialCode: '+686', currency: 'AUD', continent: 'Oceania' },
  { code: 'KM', name: 'Comoros', flag: '🇰🇲', dialCode: '+269', currency: 'KMF', continent: 'Africa' },
  { code: 'KN', name: 'Saint Kitts and Nevis', flag: '🇰🇳', dialCode: '+1', currency: 'XCD', continent: 'North America' },
  { code: 'KP', name: 'North Korea', flag: '🇰🇵', dialCode: '+850', currency: 'KPW', continent: 'Asia' },
  { code: 'KR', name: 'South Korea', flag: '🇰🇷', dialCode: '+82', currency: 'KRW', continent: 'Asia' },
  { code: 'KW', name: 'Kuwait', flag: '🇰🇼', dialCode: '+965', currency: 'KWD', continent: 'Asia' },
  { code: 'KY', name: 'Cayman Islands', flag: '🇰🇾', dialCode: '+1', currency: 'KYD', continent: 'North America' },
  { code: 'KZ', name: 'Kazakhstan', flag: '🇰🇿', dialCode: '+7', currency: 'KZT', continent: 'Asia' },
  { code: 'LA', name: 'Laos', flag: '🇱🇦', dialCode: '+856', currency: 'LAK', continent: 'Asia' },
  { code: 'LB', name: 'Lebanon', flag: '🇱🇧', dialCode: '+961', currency: 'LBP', continent: 'Asia' },
  { code: 'LC', name: 'Saint Lucia', flag: '🇱🇨', dialCode: '+1', currency: 'XCD', continent: 'North America' },
  { code: 'LI', name: 'Liechtenstein', flag: '🇱🇮', dialCode: '+423', currency: 'CHF', continent: 'Europe' },
  { code: 'LK', name: 'Sri Lanka', flag: '🇱🇰', dialCode: '+94', currency: 'LKR', continent: 'Asia' },
  { code: 'LR', name: 'Liberia', flag: '🇱🇷', dialCode: '+231', currency: 'LRD', continent: 'Africa' },
  { code: 'LS', name: 'Lesotho', flag: '🇱🇸', dialCode: '+266', currency: 'LSL', continent: 'Africa' },
  { code: 'LT', name: 'Lithuania', flag: '🇱🇹', dialCode: '+370', currency: 'EUR', continent: 'Europe' },
  { code: 'LU', name: 'Luxembourg', flag: '🇱🇺', dialCode: '+352', currency: 'EUR', continent: 'Europe' },
  { code: 'LV', name: 'Latvia', flag: '🇱🇻', dialCode: '+371', currency: 'EUR', continent: 'Europe' },
  { code: 'LY', name: 'Libya', flag: '🇱🇾', dialCode: '+218', currency: 'LYD', continent: 'Africa' },
  { code: 'MA', name: 'Morocco', flag: '🇲🇦', dialCode: '+212', currency: 'MAD', continent: 'Africa' },
  { code: 'MC', name: 'Monaco', flag: '🇲🇨', dialCode: '+377', currency: 'EUR', continent: 'Europe' },
  { code: 'MD', name: 'Moldova', flag: '🇲🇩', dialCode: '+373', currency: 'MDL', continent: 'Europe' },
  { code: 'ME', name: 'Montenegro', flag: '🇲🇪', dialCode: '+382', currency: 'EUR', continent: 'Europe' },
  { code: 'MF', name: 'Saint Martin', flag: '🇲🇫', dialCode: '+590', currency: 'EUR', continent: 'North America' },
  { code: 'MG', name: 'Madagascar', flag: '🇲🇬', dialCode: '+261', currency: 'MGA', continent: 'Africa' },
  { code: 'MH', name: 'Marshall Islands', flag: '🇲🇭', dialCode: '+692', currency: 'USD', continent: 'Oceania' },
  { code: 'MK', name: 'North Macedonia', flag: '🇲🇰', dialCode: '+389', currency: 'MKD', continent: 'Europe' },
  { code: 'ML', name: 'Mali', flag: '🇲🇱', dialCode: '+223', currency: 'XOF', continent: 'Africa' },
  { code: 'MM', name: 'Myanmar', flag: '🇲🇲', dialCode: '+95', currency: 'MMK', continent: 'Asia' },
  { code: 'MN', name: 'Mongolia', flag: '🇲🇳', dialCode: '+976', currency: 'MNT', continent: 'Asia' },
  { code: 'MO', name: 'Macao', flag: '🇲🇴', dialCode: '+853', currency: 'MOP', continent: 'Asia' },
  { code: 'MP', name: 'Northern Mariana Islands', flag: '🇲🇵', dialCode: '+1', currency: 'USD', continent: 'Oceania' },
  { code: 'MQ', name: 'Martinique', flag: '🇲🇶', dialCode: '+596', currency: 'EUR', continent: 'North America' },
  { code: 'MR', name: 'Mauritania', flag: '🇲🇷', dialCode: '+222', currency: 'MRU', continent: 'Africa' },
  { code: 'MS', name: 'Montserrat', flag: '🇲🇸', dialCode: '+1', currency: 'XCD', continent: 'North America' },
  { code: 'MT', name: 'Malta', flag: '🇲🇹', dialCode: '+356', currency: 'EUR', continent: 'Europe' },
  { code: 'MU', name: 'Mauritius', flag: '🇲🇺', dialCode: '+230', currency: 'MUR', continent: 'Africa' },
  { code: 'MV', name: 'Maldives', flag: '🇲🇻', dialCode: '+960', currency: 'MVR', continent: 'Asia' },
  { code: 'MW', name: 'Malawi', flag: '🇲🇼', dialCode: '+265', currency: 'MWK', continent: 'Africa' },
  { code: 'MX', name: 'Mexico', flag: '🇲🇽', dialCode: '+52', currency: 'MXN', continent: 'North America' },
  { code: 'MY', name: 'Malaysia', flag: '🇲🇾', dialCode: '+60', currency: 'MYR', continent: 'Asia' },
  { code: 'MZ', name: 'Mozambique', flag: '🇲🇿', dialCode: '+258', currency: 'MZN', continent: 'Africa' },
  { code: 'NA', name: 'Namibia', flag: '🇳🇦', dialCode: '+264', currency: 'NAD', continent: 'Africa' },
  { code: 'NC', name: 'New Caledonia', flag: '🇳🇨', dialCode: '+687', currency: 'XPF', continent: 'Oceania' },
  { code: 'NE', name: 'Niger', flag: '🇳🇪', dialCode: '+227', currency: 'XOF', continent: 'Africa' },
  { code: 'NF', name: 'Norfolk Island', flag: '🇳🇫', dialCode: '+672', currency: 'AUD', continent: 'Oceania' },
  { code: 'NG', name: 'Nigeria', flag: '🇳🇬', dialCode: '+234', currency: 'NGN', continent: 'Africa' },
  { code: 'NI', name: 'Nicaragua', flag: '🇳🇮', dialCode: '+505', currency: 'NIO', continent: 'North America' },
  { code: 'NL', name: 'Netherlands', flag: '🇳🇱', dialCode: '+31', currency: 'EUR', continent: 'Europe' },
  { code: 'NO', name: 'Norway', flag: '🇳🇴', dialCode: '+47', currency: 'NOK', continent: 'Europe' },
  { code: 'NP', name: 'Nepal', flag: '🇳🇵', dialCode: '+977', currency: 'NPR', continent: 'Asia' },
  { code: 'NR', name: 'Nauru', flag: '🇳🇷', dialCode: '+674', currency: 'AUD', continent: 'Oceania' },
  { code: 'NU', name: 'Niue', flag: '🇳🇺', dialCode: '+683', currency: 'NZD', continent: 'Oceania' },
  { code: 'NZ', name: 'New Zealand', flag: '🇳🇿', dialCode: '+64', currency: 'NZD', continent: 'Oceania' },
  { code: 'OM', name: 'Oman', flag: '🇴🇲', dialCode: '+968', currency: 'OMR', continent: 'Asia' },
  { code: 'PA', name: 'Panama', flag: '🇵🇦', dialCode: '+507', currency: 'PAB', continent: 'North America' },
  { code: 'PE', name: 'Peru', flag: '🇵🇪', dialCode: '+51', currency: 'PEN', continent: 'South America' },
  { code: 'PF', name: 'French Polynesia', flag: '🇵🇫', dialCode: '+689', currency: 'XPF', continent: 'Oceania' },
  { code: 'PG', name: 'Papua New Guinea', flag: '🇵🇬', dialCode: '+675', currency: 'PGK', continent: 'Oceania' },
  { code: 'PH', name: 'Philippines', flag: '🇵🇭', dialCode: '+63', currency: 'PHP', continent: 'Asia' },
  { code: 'PK', name: 'Pakistan', flag: '🇵🇰', dialCode: '+92', currency: 'PKR', continent: 'Asia' },
  { code: 'PL', name: 'Poland', flag: '🇵🇱', dialCode: '+48', currency: 'PLN', continent: 'Europe' },
  { code: 'PM', name: 'Saint Pierre and Miquelon', flag: '🇵🇲', dialCode: '+508', currency: 'EUR', continent: 'North America' },
  { code: 'PN', name: 'Pitcairn', flag: '🇵🇳', dialCode: '+64', currency: 'NZD', continent: 'Oceania' },
  { code: 'PR', name: 'Puerto Rico', flag: '🇵🇷', dialCode: '+1', currency: 'USD', continent: 'North America' },
  { code: 'PS', name: 'Palestine', flag: '🇵🇸', dialCode: '+970', currency: 'ILS', continent: 'Asia' },
  { code: 'PT', name: 'Portugal', flag: '🇵🇹', dialCode: '+351', currency: 'EUR', continent: 'Europe' },
  { code: 'PW', name: 'Palau', flag: '🇵🇼', dialCode: '+680', currency: 'USD', continent: 'Oceania' },
  { code: 'PY', name: 'Paraguay', flag: '🇵🇾', dialCode: '+595', currency: 'PYG', continent: 'South America' },
  { code: 'QA', name: 'Qatar', flag: '🇶🇦', dialCode: '+974', currency: 'QAR', continent: 'Asia' },
  { code: 'RE', name: 'Réunion', flag: '🇷🇪', dialCode: '+262', currency: 'EUR', continent: 'Africa' },
  { code: 'RO', name: 'Romania', flag: '🇷🇴', dialCode: '+40', currency: 'RON', continent: 'Europe' },
  { code: 'RS', name: 'Serbia', flag: '🇷🇸', dialCode: '+381', currency: 'RSD', continent: 'Europe' },
  { code: 'RU', name: 'Russia', flag: '🇷🇺', dialCode: '+7', currency: 'RUB', continent: 'Europe' },
  { code: 'RW', name: 'Rwanda', flag: '🇷🇼', dialCode: '+250', currency: 'RWF', continent: 'Africa' },
  { code: 'SA', name: 'Saudi Arabia', flag: '🇸🇦', dialCode: '+966', currency: 'SAR', continent: 'Asia' },
  { code: 'SB', name: 'Solomon Islands', flag: '🇸🇧', dialCode: '+677', currency: 'SBD', continent: 'Oceania' },
  { code: 'SC', name: 'Seychelles', flag: '🇸🇨', dialCode: '+248', currency: 'SCR', continent: 'Africa' },
  { code: 'SD', name: 'Sudan', flag: '🇸🇩', dialCode: '+249', currency: 'SDG', continent: 'Africa' },
  { code: 'SE', name: 'Sweden', flag: '🇸🇪', dialCode: '+46', currency: 'SEK', continent: 'Europe' },
  { code: 'SH', name: 'Saint Helena', flag: '🇸🇭', dialCode: '+290', currency: 'SHP', continent: 'Africa' },
  { code: 'SI', name: 'Slovenia', flag: '🇸🇮', dialCode: '+386', currency: 'EUR', continent: 'Europe' },
  { code: 'SJ', name: 'Svalbard and Jan Mayen', flag: '🇸🇯', dialCode: '+47', currency: 'NOK', continent: 'Europe' },
  { code: 'SK', name: 'Slovakia', flag: '🇸🇰', dialCode: '+421', currency: 'EUR', continent: 'Europe' },
  { code: 'SL', name: 'Sierra Leone', flag: '🇸🇱', dialCode: '+232', currency: 'SLL', continent: 'Africa' },
  { code: 'SM', name: 'San Marino', flag: '🇸🇲', dialCode: '+378', currency: 'EUR', continent: 'Europe' },
  { code: 'SN', name: 'Senegal', flag: '🇸🇳', dialCode: '+221', currency: 'XOF', continent: 'Africa' },
  { code: 'SO', name: 'Somalia', flag: '🇸🇴', dialCode: '+252', currency: 'SOS', continent: 'Africa' },
  { code: 'SR', name: 'Suriname', flag: '🇸🇷', dialCode: '+597', currency: 'SRD', continent: 'South America' },
  { code: 'SS', name: 'South Sudan', flag: '🇸🇸', dialCode: '+211', currency: 'SSP', continent: 'Africa' },
  { code: 'ST', name: 'São Tomé and Príncipe', flag: '🇸🇹', dialCode: '+239', currency: 'STN', continent: 'Africa' },
  { code: 'SV', name: 'El Salvador', flag: '🇸🇻', dialCode: '+503', currency: 'USD', continent: 'North America' },
  { code: 'SX', name: 'Sint Maarten', flag: '🇸🇽', dialCode: '+1', currency: 'ANG', continent: 'North America' },
  { code: 'SY', name: 'Syria', flag: '🇸🇾', dialCode: '+963', currency: 'SYP', continent: 'Asia' },
  { code: 'SZ', name: 'Eswatini', flag: '🇸🇿', dialCode: '+268', currency: 'SZL', continent: 'Africa' },
  { code: 'TC', name: 'Turks and Caicos Islands', flag: '🇹🇨', dialCode: '+1', currency: 'USD', continent: 'North America' },
  { code: 'TD', name: 'Chad', flag: '🇹🇩', dialCode: '+235', currency: 'XAF', continent: 'Africa' },
  { code: 'TF', name: 'French Southern Territories', flag: '🇹🇫', dialCode: '+262', currency: 'EUR', continent: 'Antarctica' },
  { code: 'TG', name: 'Togo', flag: '🇹🇬', dialCode: '+228', currency: 'XOF', continent: 'Africa' },
  { code: 'TH', name: 'Thailand', flag: '🇹🇭', dialCode: '+66', currency: 'THB', continent: 'Asia' },
  { code: 'TJ', name: 'Tajikistan', flag: '🇹🇯', dialCode: '+992', currency: 'TJS', continent: 'Asia' },
  { code: 'TK', name: 'Tokelau', flag: '🇹🇰', dialCode: '+690', currency: 'NZD', continent: 'Oceania' },
  { code: 'TL', name: 'Timor-Leste', flag: '🇹🇱', dialCode: '+670', currency: 'USD', continent: 'Asia' },
  { code: 'TM', name: 'Turkmenistan', flag: '🇹🇲', dialCode: '+993', currency: 'TMT', continent: 'Asia' },
  { code: 'TN', name: 'Tunisia', flag: '🇹🇳', dialCode: '+216', currency: 'TND', continent: 'Africa' },
  { code: 'TO', name: 'Tonga', flag: '🇹🇴', dialCode: '+676', currency: 'TOP', continent: 'Oceania' },
  { code: 'TR', name: 'Turkey', flag: '🇹🇷', dialCode: '+90', currency: 'TRY', continent: 'Asia' },
  { code: 'TT', name: 'Trinidad and Tobago', flag: '🇹🇹', dialCode: '+1', currency: 'TTD', continent: 'North America' },
  { code: 'TV', name: 'Tuvalu', flag: '🇹🇻', dialCode: '+688', currency: 'AUD', continent: 'Oceania' },
  { code: 'TW', name: 'Taiwan', flag: '🇹🇼', dialCode: '+886', currency: 'TWD', continent: 'Asia' },
  { code: 'TZ', name: 'Tanzania', flag: '🇹🇿', dialCode: '+255', currency: 'TZS', continent: 'Africa' },
  { code: 'UA', name: 'Ukraine', flag: '🇺🇦', dialCode: '+380', currency: 'UAH', continent: 'Europe' },
  { code: 'UG', name: 'Uganda', flag: '🇺🇬', dialCode: '+256', currency: 'UGX', continent: 'Africa' },
  { code: 'UM', name: 'United States Minor Outlying Islands', flag: '🇺🇲', dialCode: '+1', currency: 'USD', continent: 'Oceania' },
  { code: 'UY', name: 'Uruguay', flag: '🇺🇾', dialCode: '+598', currency: 'UYU', continent: 'South America' },
  { code: 'UZ', name: 'Uzbekistan', flag: '🇺🇿', dialCode: '+998', currency: 'UZS', continent: 'Asia' },
  { code: 'VA', name: 'Vatican City', flag: '🇻🇦', dialCode: '+39', currency: 'EUR', continent: 'Europe' },
  { code: 'VC', name: 'Saint Vincent and the Grenadines', flag: '🇻🇨', dialCode: '+1', currency: 'XCD', continent: 'North America' },
  { code: 'VE', name: 'Venezuela', flag: '🇻🇪', dialCode: '+58', currency: 'VES', continent: 'South America' },
  { code: 'VG', name: 'British Virgin Islands', flag: '🇻🇬', dialCode: '+1', currency: 'USD', continent: 'North America' },
  { code: 'VI', name: 'U.S. Virgin Islands', flag: '🇻🇮', dialCode: '+1', currency: 'USD', continent: 'North America' },
  { code: 'VN', name: 'Vietnam', flag: '🇻🇳', dialCode: '+84', currency: 'VND', continent: 'Asia' },
  { code: 'VU', name: 'Vanuatu', flag: '🇻🇺', dialCode: '+678', currency: 'VUV', continent: 'Oceania' },
  { code: 'WF', name: 'Wallis and Futuna', flag: '🇼🇫', dialCode: '+681', currency: 'XPF', continent: 'Oceania' },
  { code: 'WS', name: 'Samoa', flag: '🇼🇸', dialCode: '+685', currency: 'WST', continent: 'Oceania' },
  { code: 'YE', name: 'Yemen', flag: '🇾🇪', dialCode: '+967', currency: 'YER', continent: 'Asia' },
  { code: 'YT', name: 'Mayotte', flag: '🇾🇹', dialCode: '+262', currency: 'EUR', continent: 'Africa' },
  { code: 'ZA', name: 'South Africa', flag: '🇿🇦', dialCode: '+27', currency: 'ZAR', continent: 'Africa' },
  { code: 'ZM', name: 'Zambia', flag: '🇿🇲', dialCode: '+260', currency: 'ZMW', continent: 'Africa' },
  { code: 'ZW', name: 'Zimbabwe', flag: '🇿🇼', dialCode: '+263', currency: 'ZWL', continent: 'Africa' },
];

export interface CountrySelectorProps {
  value?: string; // Country code
  onChange: (countryCode: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  showFlag?: boolean;
  showDialCode?: boolean;
  showCurrency?: boolean;
  showSearch?: boolean;
  showPopular?: boolean;
  variant?: 'default' | 'compact';
}

export function CountrySelector({
  value,
  onChange,
  placeholder = "Select country...",
  className,
  disabled = false,
  showFlag = true,
  showDialCode = false,
  showCurrency = false,
  showSearch = true,
  showPopular = true,
  variant = 'default'
}: CountrySelectorProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Filter countries based on search
  const filteredCountries = useMemo(() => {
    if (!searchQuery) return COUNTRIES;
    
    const query = searchQuery.toLowerCase();
    return COUNTRIES.filter(country => 
      country.name.toLowerCase().includes(query) ||
      country.code.toLowerCase().includes(query) ||
      country.currency.toLowerCase().includes(query) ||
      country.continent.toLowerCase().includes(query)
    );
  }, [searchQuery]);

  // Group countries
  const popularCountries = useMemo(() => 
    filteredCountries.filter(country => country.popular), 
    [filteredCountries]
  );

  const otherCountries = useMemo(() => 
    filteredCountries.filter(country => !country.popular), 
    [filteredCountries]
  );

  const selectedCountry = COUNTRIES.find(country => country.code === value);

  const formatDisplayValue = (country: Country) => {
    const parts = [];
    
    if (showFlag) parts.push(country.flag);
    parts.push(country.name);
    if (showDialCode) parts.push(country.dialCode);
    if (showCurrency) parts.push(country.currency);
    
    return parts.join(' ');
  };

  if (variant === 'compact') {
    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn("justify-between", className)}
            disabled={disabled}
          >
            {selectedCountry ? (
              <div className="flex items-center gap-2">
                {showFlag && <span>{selectedCountry.flag}</span>}
                <span className="truncate">{selectedCountry.name}</span>
                {showDialCode && <Badge variant="outline" className="text-xs">{selectedCountry.dialCode}</Badge>}
              </div>
            ) : (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Globe className="h-4 w-4" />
                {placeholder}
              </div>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-72 p-0" align="start">
          <Command>
            {showSearch && (
              <div className="flex items-center border-b px-3">
                <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                <Input
                  placeholder="Search countries..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                />
              </div>
            )}
            <div className="max-h-60 overflow-y-auto">
              <CommandEmpty>No country found.</CommandEmpty>

              {showPopular && popularCountries.length > 0 && (
                <CommandGroup heading="Popular Countries">
                  {popularCountries.map((country) => (
                    <CommandItem
                      key={country.code}
                      value={country.code}
                      onSelect={() => {
                        onChange(country.code);
                        setOpen(false);
                      }}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          value === country.code ? "opacity-100" : "opacity-0"
                        )}
                      />
                      <div className="flex items-center gap-2 flex-1">
                        {showFlag && <span>{country.flag}</span>}
                        <span className="truncate">{country.name}</span>
                        {showDialCode && <Badge variant="outline" className="text-xs ml-auto">{country.dialCode}</Badge>}
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}

              <CommandGroup heading={showPopular ? "All Countries" : "Countries"}>
                {otherCountries.map((country) => (
                  <CommandItem
                    key={country.code}
                    value={country.code}
                    onSelect={() => {
                      onChange(country.code);
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === country.code ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <div className="flex items-center gap-2 flex-1">
                      {showFlag && <span>{country.flag}</span>}
                      <span className="truncate">{country.name}</span>
                      {showDialCode && <Badge variant="outline" className="text-xs ml-auto">{country.dialCode}</Badge>}
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </div>
          </Command>
        </PopoverContent>
      </Popover>
    );
  }

  // Default variant with search
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("justify-between", className)}
          disabled={disabled}
        >
          {selectedCountry ? (
            <div className="flex items-center gap-2">
              {showFlag && <span>{selectedCountry.flag}</span>}
              <span>{selectedCountry.name}</span>
              {showDialCode && <Badge variant="outline" className="text-xs">{selectedCountry.dialCode}</Badge>}
              {showCurrency && <Badge variant="secondary" className="text-xs">{selectedCountry.currency}</Badge>}
            </div>
          ) : (
            <div className="flex items-center gap-2 text-muted-foreground">
              <Globe className="h-4 w-4" />
              {placeholder}
            </div>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        <Command>
          {showSearch && (
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <Input
                placeholder="Search countries..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
              />
            </div>
          )}
          <div className="max-h-80 overflow-y-auto">
            <CommandEmpty>No country found.</CommandEmpty>
            
            {showPopular && popularCountries.length > 0 && (
              <CommandGroup heading="Popular Countries">
                {popularCountries.map((country) => (
                  <CommandItem
                    key={country.code}
                    value={country.code}
                    onSelect={() => {
                      onChange(country.code);
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === country.code ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <div className="flex items-center gap-2 flex-1">
                      {showFlag && <span>{country.flag}</span>}
                      <span>{country.name}</span>
                      <div className="ml-auto flex gap-1">
                        {showDialCode && <Badge variant="outline" className="text-xs">{country.dialCode}</Badge>}
                        {showCurrency && <Badge variant="secondary" className="text-xs">{country.currency}</Badge>}
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
            
            <CommandGroup heading="All Countries">
              {otherCountries.map((country) => (
                <CommandItem
                  key={country.code}
                  value={country.code}
                  onSelect={() => {
                    onChange(country.code);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === country.code ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <div className="flex items-center gap-2 flex-1">
                    {showFlag && <span>{country.flag}</span>}
                    <span>{country.name}</span>
                    <div className="ml-auto flex gap-1">
                      {showDialCode && <Badge variant="outline" className="text-xs">{country.dialCode}</Badge>}
                      {showCurrency && <Badge variant="secondary" className="text-xs">{country.currency}</Badge>}
                    </div>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </div>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

// Helper function to get country by code
export function getCountryByCode(code: string): Country | undefined {
  return COUNTRIES.find(country => country.code === code);
}

// Helper function to get countries by continent
export function getCountriesByContinent(continent: string): Country[] {
  return COUNTRIES.filter(country => country.continent === continent);
}

// Helper function to get popular countries
export function getPopularCountries(): Country[] {
  return COUNTRIES.filter(country => country.popular);
}
