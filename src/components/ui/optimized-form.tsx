/**
 * EVEXA Optimized Form Components
 * Performance-optimized form components with debouncing, validation, and error handling
 */

'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { cn } from '@/lib/utils';
import { useEnhancedDebounce, useOptimizedCallback, usePerformanceMonitor } from '@/lib/performance-utils';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertCircle, CheckCircle } from 'lucide-react';

// Debounced Input Component
export interface DebouncedInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  value: string;
  onChange: (value: string) => void;
  debounceMs?: number;
  onDebouncedChange?: (value: string) => void;
  validation?: (value: string) => string | null;
  showValidation?: boolean;
  label?: string;
  description?: string;
}

export const DebouncedInput = React.forwardRef<HTMLInputElement, DebouncedInputProps>(({
  value,
  onChange,
  debounceMs = 300,
  onDebouncedChange,
  validation,
  showValidation = true,
  label,
  description,
  className,
  ...props
}, ref) => {
  usePerformanceMonitor('DebouncedInput');

  const [localValue, setLocalValue] = useState(value);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  // Debounced value for external updates
  const debouncedValue = useEnhancedDebounce(localValue, debounceMs);

  // Handle validation
  const validateValue = useCallback(async (val: string) => {
    if (!validation) return;
    
    setIsValidating(true);
    try {
      const error = await validation(val);
      setValidationError(error);
    } catch (err) {
      setValidationError('Validation failed');
    } finally {
      setIsValidating(false);
    }
  }, [validation]);

  // Effect for debounced changes
  React.useEffect(() => {
    if (debouncedValue !== value) {
      onChange(debouncedValue);
      onDebouncedChange?.(debouncedValue);
      validateValue(debouncedValue);
    }
  }, [debouncedValue, onChange, onDebouncedChange, validateValue, value]);

  // Handle input change
  const handleChange = useOptimizedCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalValue(e.target.value);
  }, []);

  const hasError = showValidation && validationError;
  const isValid = showValidation && !validationError && localValue.length > 0 && !isValidating;

  return (
    <div className="space-y-2">
      {label && <Label htmlFor={props.id}>{label}</Label>}
      <div className="relative">
        <Input
          ref={ref}
          value={localValue}
          onChange={handleChange}
          className={cn(
            hasError && 'border-red-500 focus:border-red-500',
            isValid && 'border-green-500 focus:border-green-500',
            className
          )}
          {...props}
        />
        {showValidation && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            {isValidating && <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />}
            {isValid && <CheckCircle className="h-4 w-4 text-green-500" />}
            {hasError && <AlertCircle className="h-4 w-4 text-red-500" />}
          </div>
        )}
      </div>
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
      {hasError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{validationError}</AlertDescription>
        </Alert>
      )}
    </div>
  );
});

DebouncedInput.displayName = 'DebouncedInput';

// Debounced Textarea Component
export interface DebouncedTextareaProps extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'onChange'> {
  value: string;
  onChange: (value: string) => void;
  debounceMs?: number;
  onDebouncedChange?: (value: string) => void;
  validation?: (value: string) => string | null;
  showValidation?: boolean;
  label?: string;
  description?: string;
  maxLength?: number;
  showCharCount?: boolean;
}

export const DebouncedTextarea = React.forwardRef<HTMLTextAreaElement, DebouncedTextareaProps>(({
  value,
  onChange,
  debounceMs = 300,
  onDebouncedChange,
  validation,
  showValidation = true,
  label,
  description,
  maxLength,
  showCharCount = false,
  className,
  ...props
}, ref) => {
  usePerformanceMonitor('DebouncedTextarea');

  const [localValue, setLocalValue] = useState(value);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  const debouncedValue = useEnhancedDebounce(localValue, debounceMs);

  const validateValue = useCallback(async (val: string) => {
    if (!validation) return;
    
    setIsValidating(true);
    try {
      const error = await validation(val);
      setValidationError(error);
    } catch (err) {
      setValidationError('Validation failed');
    } finally {
      setIsValidating(false);
    }
  }, [validation]);

  React.useEffect(() => {
    if (debouncedValue !== value) {
      onChange(debouncedValue);
      onDebouncedChange?.(debouncedValue);
      validateValue(debouncedValue);
    }
  }, [debouncedValue, onChange, onDebouncedChange, validateValue, value]);

  const handleChange = useOptimizedCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (maxLength && newValue.length > maxLength) return;
    setLocalValue(newValue);
  }, [maxLength]);

  const hasError = showValidation && validationError;
  const isValid = showValidation && !validationError && localValue.length > 0 && !isValidating;
  const charCount = localValue.length;
  const isNearLimit = maxLength && charCount > maxLength * 0.8;

  return (
    <div className="space-y-2">
      {label && <Label htmlFor={props.id}>{label}</Label>}
      <div className="relative">
        <Textarea
          ref={ref}
          value={localValue}
          onChange={handleChange}
          className={cn(
            hasError && 'border-red-500 focus:border-red-500',
            isValid && 'border-green-500 focus:border-green-500',
            className
          )}
          {...props}
        />
        {showValidation && (
          <div className="absolute right-3 top-3">
            {isValidating && <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />}
            {isValid && <CheckCircle className="h-4 w-4 text-green-500" />}
            {hasError && <AlertCircle className="h-4 w-4 text-red-500" />}
          </div>
        )}
      </div>
      <div className="flex justify-between items-center">
        <div>
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
        {(showCharCount || maxLength) && (
          <p className={cn(
            'text-sm',
            isNearLimit ? 'text-orange-500' : 'text-muted-foreground'
          )}>
            {charCount}{maxLength && `/${maxLength}`}
          </p>
        )}
      </div>
      {hasError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{validationError}</AlertDescription>
        </Alert>
      )}
    </div>
  );
});

DebouncedTextarea.displayName = 'DebouncedTextarea';

// Optimized Form Component
export interface OptimizedFormProps {
  children: React.ReactNode;
  onSubmit?: (data: Record<string, any>) => void | Promise<void>;
  className?: string;
  autoSave?: boolean;
  autoSaveDelay?: number;
  onAutoSave?: (data: Record<string, any>) => void;
  validation?: Record<string, (value: any) => string | null>;
  initialData?: Record<string, any>;
}

export const OptimizedForm: React.FC<OptimizedFormProps> = ({
  children,
  onSubmit,
  className,
  autoSave = false,
  autoSaveDelay = 2000,
  onAutoSave,
  validation,
  initialData = {},
}) => {
  usePerformanceMonitor('OptimizedForm');

  const [formData, setFormData] = useState(initialData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Auto-save functionality
  const debouncedFormData = useEnhancedDebounce(formData, autoSaveDelay);

  React.useEffect(() => {
    if (autoSave && onAutoSave && Object.keys(debouncedFormData).length > 0) {
      onAutoSave(debouncedFormData);
    }
  }, [debouncedFormData, autoSave, onAutoSave]);

  // Form submission
  const handleSubmit = useOptimizedCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!onSubmit) return;

    setIsSubmitting(true);
    try {
      // Validate all fields
      if (validation) {
        const newErrors: Record<string, string> = {};
        for (const [field, validator] of Object.entries(validation)) {
          const error = validator(formData[field]);
          if (error) {
            newErrors[field] = error;
          }
        }
        
        if (Object.keys(newErrors).length > 0) {
          setErrors(newErrors);
          return;
        }
      }

      await onSubmit(formData);
      setErrors({});
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, onSubmit, validation]);

  // Update form data
  const updateField = useCallback((field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when value changes
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [errors]);

  // Provide form context to children
  const formContext = useMemo(() => ({
    formData,
    updateField,
    errors,
    isSubmitting,
  }), [formData, updateField, errors, isSubmitting]);

  return (
    <form onSubmit={handleSubmit} className={cn('space-y-4', className)}>
      <FormContext.Provider value={formContext}>
        {children}
      </FormContext.Provider>
      {onSubmit && (
        <Button type="submit" disabled={isSubmitting} className="w-full">
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isSubmitting ? 'Submitting...' : 'Submit'}
        </Button>
      )}
    </form>
  );
};

// Form Context
const FormContext = React.createContext<{
  formData: Record<string, any>;
  updateField: (field: string, value: any) => void;
  errors: Record<string, string>;
  isSubmitting: boolean;
} | null>(null);

export const useFormContext = () => {
  const context = React.useContext(FormContext);
  if (!context) {
    throw new Error('useFormContext must be used within OptimizedForm');
  }
  return context;
};

export default OptimizedForm;
