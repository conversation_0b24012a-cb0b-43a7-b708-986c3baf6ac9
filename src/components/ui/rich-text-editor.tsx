"use client";

import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Bold, 
  Italic, 
  Underline, 
  List, 
  ListOrdered, 
  Link, 
  Quote,
  Undo,
  Redo,
  Type,
  AlignLeft,
  AlignCenter,
  AlignRight
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  height?: number;
  toolbar?: string[];
  disabled?: boolean;
}

const DEFAULT_TOOLBAR = [
  'bold', 'italic', 'underline', '|',
  'bulletList', 'orderedList', '|',
  'quote', 'link', '|',
  'alignLeft', 'alignCenter', 'alignRight', '|',
  'undo', 'redo'
];

export function RichTextEditor({
  value,
  onChange,
  placeholder = "Start typing...",
  className,
  height = 200,
  toolbar = DEFAULT_TOOLBAR,
  disabled = false
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  const executeCommand = useCallback((command: string, value?: string) => {
    if (disabled) return;
    
    document.execCommand(command, false, value);
    editorRef.current?.focus();
    
    // Update the value
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  }, [disabled, onChange]);

  const handleInput = useCallback(() => {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  }, [onChange]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    // Handle keyboard shortcuts
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          executeCommand('bold');
          break;
        case 'i':
          e.preventDefault();
          executeCommand('italic');
          break;
        case 'u':
          e.preventDefault();
          executeCommand('underline');
          break;
        case 'z':
          e.preventDefault();
          if (e.shiftKey) {
            executeCommand('redo');
          } else {
            executeCommand('undo');
          }
          break;
      }
    }
  }, [executeCommand]);

  const renderToolbarButton = (item: string) => {
    if (item === '|') {
      return <Separator key={item} orientation="vertical" className="h-6" />;
    }

    const buttonConfig: Record<string, { icon: React.ReactNode; command: string; title: string }> = {
      bold: { icon: <Bold className="h-4 w-4" />, command: 'bold', title: 'Bold (Ctrl+B)' },
      italic: { icon: <Italic className="h-4 w-4" />, command: 'italic', title: 'Italic (Ctrl+I)' },
      underline: { icon: <Underline className="h-4 w-4" />, command: 'underline', title: 'Underline (Ctrl+U)' },
      bulletList: { icon: <List className="h-4 w-4" />, command: 'insertUnorderedList', title: 'Bullet List' },
      orderedList: { icon: <ListOrdered className="h-4 w-4" />, command: 'insertOrderedList', title: 'Numbered List' },
      quote: { icon: <Quote className="h-4 w-4" />, command: 'formatBlock', title: 'Quote' },
      link: { icon: <Link className="h-4 w-4" />, command: 'createLink', title: 'Insert Link' },
      alignLeft: { icon: <AlignLeft className="h-4 w-4" />, command: 'justifyLeft', title: 'Align Left' },
      alignCenter: { icon: <AlignCenter className="h-4 w-4" />, command: 'justifyCenter', title: 'Align Center' },
      alignRight: { icon: <AlignRight className="h-4 w-4" />, command: 'justifyRight', title: 'Align Right' },
      undo: { icon: <Undo className="h-4 w-4" />, command: 'undo', title: 'Undo (Ctrl+Z)' },
      redo: { icon: <Redo className="h-4 w-4" />, command: 'redo', title: 'Redo (Ctrl+Shift+Z)' },
    };

    const config = buttonConfig[item];
    if (!config) return null;

    const handleClick = () => {
      if (item === 'link') {
        const url = prompt('Enter URL:');
        if (url) {
          executeCommand(config.command, url);
        }
      } else if (item === 'quote') {
        executeCommand(config.command, 'blockquote');
      } else {
        executeCommand(config.command);
      }
    };

    return (
      <Button
        key={item}
        variant="ghost"
        size="sm"
        onClick={handleClick}
        disabled={disabled}
        title={config.title}
        className="h-8 w-8 p-0"
      >
        {config.icon}
      </Button>
    );
  };

  return (
    <div className={cn("border rounded-md", isFocused && "ring-2 ring-ring ring-offset-2", className)}>
      {/* Toolbar */}
      <div className="flex items-center gap-1 p-2 border-b bg-muted/50">
        {toolbar.map((item, index) => renderToolbarButton(item))}
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable={!disabled}
        onInput={handleInput}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        onKeyDown={handleKeyDown}
        dangerouslySetInnerHTML={{ __html: value }}
        className={cn(
          "p-3 min-h-[120px] outline-none prose prose-sm max-w-none",
          disabled && "opacity-50 cursor-not-allowed",
          !value && "text-muted-foreground"
        )}
        style={{ height: height }}
        data-placeholder={placeholder}
      />

      {/* Character count or other status */}
      <div className="px-3 py-1 text-xs text-muted-foreground border-t bg-muted/30">
        {value.replace(/<[^>]*>/g, '').length} characters
      </div>
    </div>
  );
}
