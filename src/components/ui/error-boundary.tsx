/**
 * EVEXA Error Boundary Components
 * Enhanced error handling with performance monitoring and recovery options
 */

'use client';

import React from 'react';
import { ErrorBoundary as ReactErrorBoundary } from 'react-error-boundary';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  RefreshCw, 
  Bug, 
  Home, 
  ChevronDown, 
  ChevronUp,
  Copy,
  ExternalLink
} from 'lucide-react';
import { performanceUtils } from '@/lib/performance-utils';

interface ErrorInfo {
  componentStack: string;
  errorBoundary?: string;
  errorBoundaryStack?: string;
}

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
  errorInfo?: ErrorInfo;
}

// Enhanced Error Fallback Component
export const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetErrorBoundary,
  errorInfo,
}) => {
  const [showDetails, setShowDetails] = React.useState(false);
  const [copied, setCopied] = React.useState(false);

  // Collect error details
  const errorDetails = React.useMemo(() => {
    const memory = performanceUtils.getMemoryUsage();
    const bundle = performanceUtils.analyzeBundleSize();
    
    return {
      message: error.message,
      stack: error.stack,
      name: error.name,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      memory,
      bundle,
      componentStack: errorInfo?.componentStack,
    };
  }, [error, errorInfo]);

  // Copy error details to clipboard
  const copyErrorDetails = async () => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy error details:', err);
    }
  };

  // Report error to monitoring service
  const reportError = () => {
    // This would integrate with your error reporting service
    console.error('Error reported:', errorDetails);
    alert('Error reported to development team');
  };

  return (
    <div className="min-h-[400px] flex items-center justify-center p-6">
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <div className="flex items-center gap-3">
            <AlertTriangle className="h-8 w-8 text-red-500" />
            <div>
              <CardTitle className="text-red-600">Something went wrong</CardTitle>
              <CardDescription>
                An unexpected error occurred. You can try refreshing the page or go back to the homepage.
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Error Summary */}
          <Alert variant="destructive">
            <Bug className="h-4 w-4" />
            <AlertDescription>
              <strong>{error.name}:</strong> {error.message}
            </AlertDescription>
          </Alert>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button onClick={resetErrorBoundary} className="gap-2">
              <RefreshCw className="h-4 w-4" />
              Try Again
            </Button>
            
            <Button 
              variant="outline" 
              onClick={() => window.location.href = '/'}
              className="gap-2"
            >
              <Home className="h-4 w-4" />
              Go Home
            </Button>

            <Button 
              variant="outline" 
              onClick={() => window.location.reload()}
              className="gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh Page
            </Button>

            <Button 
              variant="outline" 
              onClick={reportError}
              className="gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              Report Issue
            </Button>
          </div>

          {/* Error Details Toggle */}
          <div className="border-t pt-4">
            <Button
              variant="ghost"
              onClick={() => setShowDetails(!showDetails)}
              className="gap-2 w-full justify-between"
            >
              <span>Technical Details</span>
              {showDetails ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>

            {showDetails && (
              <div className="mt-4 space-y-4">
                {/* Error Information */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Error Type:</strong>
                    <Badge variant="outline" className="ml-2">{error.name}</Badge>
                  </div>
                  <div>
                    <strong>Timestamp:</strong>
                    <span className="ml-2 font-mono">{errorDetails.timestamp}</span>
                  </div>
                </div>

                {/* Memory Information */}
                {errorDetails.memory && (
                  <div className="p-3 bg-muted rounded-lg">
                    <h4 className="font-medium mb-2">Memory Usage</h4>
                    <div className="grid grid-cols-3 gap-2 text-sm">
                      <div>Used: {errorDetails.memory.used}MB</div>
                      <div>Total: {errorDetails.memory.total}MB</div>
                      <div>Limit: {errorDetails.memory.limit}MB</div>
                    </div>
                  </div>
                )}

                {/* Stack Trace */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Stack Trace</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={copyErrorDetails}
                      className="gap-2"
                    >
                      <Copy className="h-3 w-3" />
                      {copied ? 'Copied!' : 'Copy All'}
                    </Button>
                  </div>
                  <pre className="text-xs bg-muted p-3 rounded-lg overflow-auto max-h-40">
                    {error.stack}
                  </pre>
                </div>

                {/* Component Stack */}
                {errorInfo?.componentStack && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Component Stack</h4>
                    <pre className="text-xs bg-muted p-3 rounded-lg overflow-auto max-h-40">
                      {errorInfo.componentStack}
                    </pre>
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Error Boundary Wrapper Component
export interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  isolate?: boolean;
}

export const ErrorBoundary: React.FC<ErrorBoundaryProps> = ({
  children,
  fallback: FallbackComponent = ErrorFallback,
  onError,
  isolate = false,
}) => {
  const handleError = React.useCallback((error: Error, errorInfo: ErrorInfo) => {
    // Log error with performance context
    const memory = performanceUtils.getMemoryUsage();
    console.error('Error Boundary caught an error:', {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      errorInfo,
      memory,
      timestamp: new Date().toISOString(),
      url: window.location.href,
    });

    // Call custom error handler
    onError?.(error, errorInfo);

    // Report to error monitoring service
    // This would integrate with services like Sentry, LogRocket, etc.
  }, [onError]);

  return (
    <ReactErrorBoundary
      FallbackComponent={FallbackComponent}
      onError={handleError}
      isolate={isolate}
    >
      {children}
    </ReactErrorBoundary>
  );
};

// Async Error Boundary for handling async errors
export const AsyncErrorBoundary: React.FC<ErrorBoundaryProps> = ({
  children,
  ...props
}) => {
  React.useEffect(() => {
    // Handle unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason);
      // You could throw this to be caught by the error boundary
      // throw new Error(`Unhandled promise rejection: ${event.reason}`);
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return <ErrorBoundary {...props}>{children}</ErrorBoundary>;
};

// HOC for wrapping components with error boundaries
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

// Hook for manually triggering error boundaries
export const useErrorHandler = () => {
  return React.useCallback((error: Error) => {
    throw error;
  }, []);
};

export default ErrorBoundary;
