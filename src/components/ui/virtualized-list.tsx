/**
 * EVEXA Virtualized List Component
 * High-performance list component using @tanstack/react-virtual
 */

'use client';

import React, { forwardRef, useMemo } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { cn } from '@/lib/utils';
import { usePerformanceMonitor } from '@/lib/performance-utils';

export interface VirtualizedListProps<T> {
  items: T[];
  height: number;
  itemHeight?: number;
  estimateSize?: (index: number) => number;
  renderItem: (item: T, index: number) => React.ReactNode;
  className?: string;
  overscan?: number;
  onScroll?: (scrollTop: number) => void;
  getItemKey?: (item: T, index: number) => string | number;
  gap?: number;
  paddingStart?: number;
  paddingEnd?: number;
}

export const VirtualizedList = forwardRef<
  HTMLDivElement,
  VirtualizedListProps<any>
>(({
  items,
  height,
  itemHeight = 50,
  estimateSize,
  renderItem,
  className,
  overscan = 5,
  onScroll,
  getItemKey,
  gap = 0,
  paddingStart = 0,
  paddingEnd = 0,
  ...props
}, ref) => {
  usePerformanceMonitor('VirtualizedList');

  const parentRef = React.useRef<HTMLDivElement>(null);

  const virtualizer = useVirtualizer({
    count: items.length,
    getScrollElement: () => parentRef.current,
    estimateSize: estimateSize || (() => itemHeight),
    overscan,
    gap,
    paddingStart,
    paddingEnd,
  });

  const virtualItems = virtualizer.getVirtualItems();

  // Handle scroll events
  React.useEffect(() => {
    if (onScroll && parentRef.current) {
      const handleScroll = () => {
        if (parentRef.current) {
          onScroll(parentRef.current.scrollTop);
        }
      };

      parentRef.current.addEventListener('scroll', handleScroll);
      return () => {
        parentRef.current?.removeEventListener('scroll', handleScroll);
      };
    }
  }, [onScroll]);

  return (
    <div
      ref={parentRef}
      className={cn(
        'overflow-auto',
        className
      )}
      style={{ height }}
      {...props}
    >
      <div
        style={{
          height: virtualizer.getTotalSize(),
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualItems.map((virtualItem) => {
          const item = items[virtualItem.index];
          const key = getItemKey ? getItemKey(item, virtualItem.index) : virtualItem.index;

          return (
            <div
              key={key}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: virtualItem.size,
                transform: `translateY(${virtualItem.start}px)`,
              }}
            >
              {renderItem(item, virtualItem.index)}
            </div>
          );
        })}
      </div>
    </div>
  );
});

VirtualizedList.displayName = 'VirtualizedList';

// Grid virtualization component
export interface VirtualizedGridProps<T> {
  items: T[];
  height: number;
  width: number;
  columnCount: number;
  rowHeight?: number;
  columnWidth?: number;
  estimateSize?: (index: number) => number;
  renderItem: (item: T, rowIndex: number, columnIndex: number) => React.ReactNode;
  className?: string;
  overscan?: number;
  gap?: number;
  getItemKey?: (item: T, index: number) => string | number;
}

export const VirtualizedGrid = forwardRef<
  HTMLDivElement,
  VirtualizedGridProps<any>
>(({
  items,
  height,
  width,
  columnCount,
  rowHeight = 100,
  columnWidth,
  estimateSize,
  renderItem,
  className,
  overscan = 5,
  gap = 0,
  getItemKey,
  ...props
}, ref) => {
  usePerformanceMonitor('VirtualizedGrid');

  const parentRef = React.useRef<HTMLDivElement>(null);
  
  const rowCount = Math.ceil(items.length / columnCount);
  const actualColumnWidth = columnWidth || width / columnCount;

  const rowVirtualizer = useVirtualizer({
    count: rowCount,
    getScrollElement: () => parentRef.current,
    estimateSize: estimateSize || (() => rowHeight),
    overscan,
    gap,
  });

  const virtualRows = rowVirtualizer.getVirtualItems();

  return (
    <div
      ref={parentRef}
      className={cn('overflow-auto', className)}
      style={{ height, width }}
      {...props}
    >
      <div
        style={{
          height: rowVirtualizer.getTotalSize(),
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualRows.map((virtualRow) => (
          <div
            key={virtualRow.index}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: virtualRow.size,
              transform: `translateY(${virtualRow.start}px)`,
              display: 'flex',
              gap: `${gap}px`,
            }}
          >
            {Array.from({ length: columnCount }, (_, columnIndex) => {
              const itemIndex = virtualRow.index * columnCount + columnIndex;
              const item = items[itemIndex];
              
              if (!item) return null;

              const key = getItemKey ? getItemKey(item, itemIndex) : itemIndex;

              return (
                <div
                  key={key}
                  style={{
                    width: actualColumnWidth,
                    height: '100%',
                  }}
                >
                  {renderItem(item, virtualRow.index, columnIndex)}
                </div>
              );
            })}
          </div>
        ))}
      </div>
    </div>
  );
});

VirtualizedGrid.displayName = 'VirtualizedGrid';

// Infinite scroll virtualized list
export interface InfiniteVirtualizedListProps<T> extends VirtualizedListProps<T> {
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  fetchNextPage?: () => void;
  loadingComponent?: React.ReactNode;
}

export const InfiniteVirtualizedList = forwardRef<
  HTMLDivElement,
  InfiniteVirtualizedListProps<any>
>(({
  hasNextPage,
  isFetchingNextPage,
  fetchNextPage,
  loadingComponent,
  ...props
}, ref) => {
  usePerformanceMonitor('InfiniteVirtualizedList');

  const parentRef = React.useRef<HTMLDivElement>(null);

  const virtualizer = useVirtualizer({
    count: hasNextPage ? props.items.length + 1 : props.items.length,
    getScrollElement: () => parentRef.current,
    estimateSize: props.estimateSize || (() => props.itemHeight || 50),
    overscan: props.overscan || 5,
  });

  const virtualItems = virtualizer.getVirtualItems();

  // Fetch next page when scrolling near the end
  React.useEffect(() => {
    const [lastItem] = [...virtualItems].reverse();

    if (!lastItem) return;

    if (
      lastItem.index >= props.items.length - 1 &&
      hasNextPage &&
      !isFetchingNextPage
    ) {
      fetchNextPage?.();
    }
  }, [
    hasNextPage,
    fetchNextPage,
    props.items.length,
    isFetchingNextPage,
    virtualItems,
  ]);

  return (
    <div
      ref={parentRef}
      className={cn('overflow-auto', props.className)}
      style={{ height: props.height }}
    >
      <div
        style={{
          height: virtualizer.getTotalSize(),
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualItems.map((virtualItem) => {
          const isLoaderRow = virtualItem.index > props.items.length - 1;
          const item = props.items[virtualItem.index];

          return (
            <div
              key={virtualItem.index}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: virtualItem.size,
                transform: `translateY(${virtualItem.start}px)`,
              }}
            >
              {isLoaderRow ? (
                hasNextPage ? (
                  loadingComponent || (
                    <div className="flex items-center justify-center p-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  )
                ) : (
                  <div className="flex items-center justify-center p-4 text-muted-foreground">
                    No more items
                  </div>
                )
              ) : (
                props.renderItem(item, virtualItem.index)
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
});

InfiniteVirtualizedList.displayName = 'InfiniteVirtualizedList';

// Virtualized Table Component
export interface VirtualizedTableProps<T> {
  data: T[];
  columns: Array<{
    key: keyof T;
    header: string;
    width?: number;
    render?: (value: any, item: T, index: number) => React.ReactNode;
  }>;
  height: number;
  rowHeight?: number;
  className?: string;
  headerClassName?: string;
  rowClassName?: string | ((item: T, index: number) => string);
  onRowClick?: (item: T, index: number) => void;
  getRowKey?: (item: T, index: number) => string | number;
}

export const VirtualizedTable = forwardRef<
  HTMLDivElement,
  VirtualizedTableProps<any>
>(({
  data,
  columns,
  height,
  rowHeight = 50,
  className,
  headerClassName,
  rowClassName,
  onRowClick,
  getRowKey,
  ...props
}, ref) => {
  usePerformanceMonitor('VirtualizedTable');

  const parentRef = React.useRef<HTMLDivElement>(null);

  const virtualizer = useVirtualizer({
    count: data.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => rowHeight,
    overscan: 5,
  });

  const virtualItems = virtualizer.getVirtualItems();

  return (
    <div className={cn('border rounded-lg overflow-hidden', className)} {...props}>
      {/* Header */}
      <div className={cn('border-b bg-muted/50', headerClassName)}>
        <div className="flex">
          {columns.map((column, index) => (
            <div
              key={String(column.key)}
              className="px-4 py-3 font-medium text-sm"
              style={{ width: column.width || `${100 / columns.length}%` }}
            >
              {column.header}
            </div>
          ))}
        </div>
      </div>

      {/* Virtualized Body */}
      <div
        ref={parentRef}
        className="overflow-auto"
        style={{ height: height - 50 }} // Subtract header height
      >
        <div
          style={{
            height: virtualizer.getTotalSize(),
            width: '100%',
            position: 'relative',
          }}
        >
          {virtualItems.map((virtualItem) => {
            const item = data[virtualItem.index];
            const key = getRowKey ? getRowKey(item, virtualItem.index) : virtualItem.index;
            const rowClass = typeof rowClassName === 'function'
              ? rowClassName(item, virtualItem.index)
              : rowClassName;

            return (
              <div
                key={key}
                className={cn(
                  'absolute top-0 left-0 w-full border-b hover:bg-muted/50 transition-colors',
                  onRowClick && 'cursor-pointer',
                  rowClass
                )}
                style={{
                  height: virtualItem.size,
                  transform: `translateY(${virtualItem.start}px)`,
                }}
                onClick={() => onRowClick?.(item, virtualItem.index)}
              >
                <div className="flex h-full items-center">
                  {columns.map((column) => (
                    <div
                      key={String(column.key)}
                      className="px-4 py-2 text-sm"
                      style={{ width: column.width || `${100 / columns.length}%` }}
                    >
                      {column.render
                        ? column.render(item[column.key], item, virtualItem.index)
                        : String(item[column.key] || '')
                      }
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
});

VirtualizedTable.displayName = 'VirtualizedTable';

export default VirtualizedList;
