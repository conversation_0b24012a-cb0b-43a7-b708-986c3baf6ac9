/**
 * EVEXA Enhanced Status Components
 * Comprehensive status indicators with animations and accessibility
 */

'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { statusVariants, type StatusVariants } from '@/lib/component-variants';
import { usePerformanceMonitor } from '@/lib/performance-utils';
import {
  CheckCircle,
  AlertCircle,
  XCircle,
  Info,
  Clock,
  Zap,
  Pause,
  Play,
  AlertTriangle,
  Loader2
} from 'lucide-react';

// Enhanced Status Indicator Component
export interface StatusIndicatorProps extends StatusVariants {
  label?: string;
  icon?: React.ReactNode;
  showIcon?: boolean;
  animate?: boolean;
  className?: string;
  'aria-label'?: string;
}

const statusIcons = {
  success: CheckCircle,
  error: XCircle,
  warning: AlertTriangle,
  info: Info,
  pending: Clock,
  active: Play,
  inactive: Pause,
  default: Info,
};

export const StatusIndicator = React.forwardRef<HTMLDivElement, StatusIndicatorProps>(({
  variant = "default",
  indicator = "none",
  size = "default",
  label,
  icon,
  showIcon = true,
  animate = false,
  className,
  'aria-label': ariaLabel,
  ...props
}, ref) => {
  usePerformanceMonitor('StatusIndicator');

  const IconComponent = icon ? null : statusIcons[variant as keyof typeof statusIcons] || statusIcons.default;

  return (
    <div
      ref={ref}
      className={cn(
        statusVariants({ variant, indicator, size }),
        animate && "transition-all duration-200",
        className
      )}
      role="status"
      aria-label={ariaLabel || label}
      {...props}
    >
      {showIcon && IconComponent && (
        <IconComponent 
          className={cn(
            "flex-shrink-0",
            size === "sm" && "h-3 w-3",
            size === "default" && "h-4 w-4",
            size === "lg" && "h-5 w-5",
            animate && variant === "pending" && "animate-spin"
          )}
        />
      )}
      {label && <span>{label}</span>}
    </div>
  );
});

StatusIndicator.displayName = 'StatusIndicator';

// Status Badge Component
export interface StatusBadgeProps {
  status: 'online' | 'offline' | 'away' | 'busy' | 'idle';
  showLabel?: boolean;
  size?: 'sm' | 'default' | 'lg';
  animate?: boolean;
  className?: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  showLabel = false,
  size = "default",
  animate = true,
  className,
}) => {
  usePerformanceMonitor('StatusBadge');

  const statusConfig = {
    online: { color: 'bg-green-500', label: 'Online' },
    offline: { color: 'bg-gray-400', label: 'Offline' },
    away: { color: 'bg-yellow-500', label: 'Away' },
    busy: { color: 'bg-red-500', label: 'Busy' },
    idle: { color: 'bg-orange-500', label: 'Idle' },
  };

  const config = statusConfig[status];
  
  const sizeClasses = {
    sm: "h-2 w-2",
    default: "h-3 w-3",
    lg: "h-4 w-4"
  };

  return (
    <div className={cn("inline-flex items-center gap-2", className)}>
      <div className="relative">
        <div
          className={cn(
            "rounded-full border-2 border-background",
            config.color,
            sizeClasses[size]
          )}
        />
        {animate && status === 'online' && (
          <div
            className={cn(
              "absolute inset-0 rounded-full animate-ping",
              config.color,
              "opacity-75"
            )}
          />
        )}
      </div>
      {showLabel && (
        <span className="text-sm font-medium text-muted-foreground">
          {config.label}
        </span>
      )}
    </div>
  );
};

// Connection Status Component
export interface ConnectionStatusProps {
  isConnected: boolean;
  showLabel?: boolean;
  onReconnect?: () => void;
  className?: string;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  isConnected,
  showLabel = true,
  onReconnect,
  className,
}) => {
  usePerformanceMonitor('ConnectionStatus');

  return (
    <div className={cn("inline-flex items-center gap-2", className)}>
      <div className="relative">
        <div
          className={cn(
            "h-2 w-2 rounded-full",
            isConnected ? "bg-green-500" : "bg-red-500"
          )}
        />
        {isConnected && (
          <div className="absolute inset-0 h-2 w-2 rounded-full bg-green-500 animate-ping opacity-75" />
        )}
      </div>
      
      {showLabel && (
        <span className="text-sm text-muted-foreground">
          {isConnected ? "Connected" : "Disconnected"}
        </span>
      )}
      
      {!isConnected && onReconnect && (
        <button
          onClick={onReconnect}
          className="text-xs text-primary hover:text-primary/80 underline"
        >
          Reconnect
        </button>
      )}
    </div>
  );
};

// Loading Status Component
export interface LoadingStatusProps {
  isLoading: boolean;
  label?: string;
  successLabel?: string;
  errorLabel?: string;
  hasError?: boolean;
  className?: string;
}

export const LoadingStatus: React.FC<LoadingStatusProps> = ({
  isLoading,
  label = "Loading...",
  successLabel = "Complete",
  errorLabel = "Error",
  hasError = false,
  className,
}) => {
  usePerformanceMonitor('LoadingStatus');

  if (hasError) {
    return (
      <StatusIndicator
        variant="error"
        label={errorLabel}
        className={className}
      />
    );
  }

  if (isLoading) {
    return (
      <div className={cn("inline-flex items-center gap-2", className)}>
        <Loader2 className="h-4 w-4 animate-spin text-primary" />
        <span className="text-sm text-muted-foreground">{label}</span>
      </div>
    );
  }

  return (
    <StatusIndicator
      variant="success"
      label={successLabel}
      className={className}
    />
  );
};

// Progress Status Component
export interface ProgressStatusProps {
  current: number;
  total: number;
  label?: string;
  showPercentage?: boolean;
  variant?: 'default' | 'success' | 'warning' | 'error';
  className?: string;
}

export const ProgressStatus: React.FC<ProgressStatusProps> = ({
  current,
  total,
  label,
  showPercentage = true,
  variant = "default",
  className,
}) => {
  usePerformanceMonitor('ProgressStatus');

  const percentage = Math.round((current / total) * 100);
  const isComplete = current >= total;

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {isComplete ? (
            <CheckCircle className="h-4 w-4 text-success" />
          ) : (
            <div className="h-4 w-4 rounded-full border-2 border-primary border-t-transparent animate-spin" />
          )}
          {label && (
            <span className="text-sm font-medium">
              {label}
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>{current} of {total}</span>
          {showPercentage && (
            <span>({percentage}%)</span>
          )}
        </div>
      </div>
      
      <div className="w-full bg-muted rounded-full h-2">
        <div
          className={cn(
            "h-2 rounded-full transition-all duration-300",
            variant === "default" && "bg-primary",
            variant === "success" && "bg-success",
            variant === "warning" && "bg-warning",
            variant === "error" && "bg-error"
          )}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
    </div>
  );
};

// Health Status Component
export interface HealthStatusProps {
  status: 'healthy' | 'warning' | 'critical' | 'unknown';
  label?: string;
  details?: string;
  showIcon?: boolean;
  className?: string;
}

export const HealthStatus: React.FC<HealthStatusProps> = ({
  status,
  label,
  details,
  showIcon = true,
  className,
}) => {
  usePerformanceMonitor('HealthStatus');

  const statusConfig = {
    healthy: {
      variant: 'success' as const,
      icon: CheckCircle,
      defaultLabel: 'Healthy'
    },
    warning: {
      variant: 'warning' as const,
      icon: AlertTriangle,
      defaultLabel: 'Warning'
    },
    critical: {
      variant: 'error' as const,
      icon: XCircle,
      defaultLabel: 'Critical'
    },
    unknown: {
      variant: 'default' as const,
      icon: Info,
      defaultLabel: 'Unknown'
    }
  };

  const config = statusConfig[status];
  const IconComponent = config.icon;

  return (
    <div className={cn("space-y-1", className)}>
      <div className="flex items-center gap-2">
        {showIcon && (
          <IconComponent className="h-4 w-4" />
        )}
        <StatusIndicator
          variant={config.variant}
          label={label || config.defaultLabel}
          showIcon={false}
        />
      </div>
      {details && (
        <p className="text-xs text-muted-foreground ml-6">
          {details}
        </p>
      )}
    </div>
  );
};

// Sync Status Component
export interface SyncStatusProps {
  lastSync?: Date;
  isSyncing?: boolean;
  hasError?: boolean;
  onSync?: () => void;
  className?: string;
}

export const SyncStatus: React.FC<SyncStatusProps> = ({
  lastSync,
  isSyncing = false,
  hasError = false,
  onSync,
  className,
}) => {
  usePerformanceMonitor('SyncStatus');

  const formatLastSync = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {isSyncing ? (
        <>
          <Loader2 className="h-4 w-4 animate-spin text-primary" />
          <span className="text-sm text-muted-foreground">Syncing...</span>
        </>
      ) : hasError ? (
        <>
          <XCircle className="h-4 w-4 text-error" />
          <span className="text-sm text-error">Sync failed</span>
        </>
      ) : (
        <>
          <CheckCircle className="h-4 w-4 text-success" />
          <span className="text-sm text-muted-foreground">
            {lastSync ? `Synced ${formatLastSync(lastSync)}` : 'Never synced'}
          </span>
        </>
      )}
      
      {onSync && !isSyncing && (
        <button
          onClick={onSync}
          className="text-xs text-primary hover:text-primary/80 underline ml-2"
        >
          Sync now
        </button>
      )}
    </div>
  );
};

export default StatusIndicator;
