"use client";

import React, { useState, useMemo } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Clock, Globe, Search } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface TimezoneOption {
  value: string;
  label: string;
  offset: string;
  region: string;
  popular?: boolean;
}

const TIMEZONE_DATA: TimezoneOption[] = [
  // Popular timezones
  { value: 'America/New_York', label: 'Eastern Time', offset: 'UTC-5/-4', region: 'North America', popular: true },
  { value: 'America/Chicago', label: 'Central Time', offset: 'UTC-6/-5', region: 'North America', popular: true },
  { value: 'America/Denver', label: 'Mountain Time', offset: 'UTC-7/-6', region: 'North America', popular: true },
  { value: 'America/Los_Angeles', label: 'Pacific Time', offset: 'UTC-8/-7', region: 'North America', popular: true },
  { value: 'Europe/London', label: 'Greenwich Mean Time', offset: 'UTC+0/+1', region: 'Europe', popular: true },
  { value: 'Europe/Paris', label: 'Central European Time', offset: 'UTC+1/+2', region: 'Europe', popular: true },
  { value: 'Asia/Tokyo', label: 'Japan Standard Time', offset: 'UTC+9', region: 'Asia', popular: true },
  { value: 'Asia/Shanghai', label: 'China Standard Time', offset: 'UTC+8', region: 'Asia', popular: true },
  { value: 'Australia/Sydney', label: 'Australian Eastern Time', offset: 'UTC+10/+11', region: 'Australia', popular: true },
  
  // Additional timezones
  { value: 'UTC', label: 'Coordinated Universal Time', offset: 'UTC+0', region: 'UTC' },
  { value: 'America/Toronto', label: 'Eastern Time (Toronto)', offset: 'UTC-5/-4', region: 'North America' },
  { value: 'America/Vancouver', label: 'Pacific Time (Vancouver)', offset: 'UTC-8/-7', region: 'North America' },
  { value: 'America/Mexico_City', label: 'Central Time (Mexico)', offset: 'UTC-6/-5', region: 'North America' },
  { value: 'America/Sao_Paulo', label: 'Brasília Time', offset: 'UTC-3/-2', region: 'South America' },
  { value: 'America/Argentina/Buenos_Aires', label: 'Argentina Time', offset: 'UTC-3', region: 'South America' },
  { value: 'Europe/Berlin', label: 'Central European Time (Berlin)', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Rome', label: 'Central European Time (Rome)', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Madrid', label: 'Central European Time (Madrid)', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Amsterdam', label: 'Central European Time (Amsterdam)', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Moscow', label: 'Moscow Time', offset: 'UTC+3', region: 'Europe' },
  { value: 'Asia/Dubai', label: 'Gulf Standard Time', offset: 'UTC+4', region: 'Asia' },
  { value: 'Asia/Kolkata', label: 'India Standard Time', offset: 'UTC+5:30', region: 'Asia' },
  { value: 'Asia/Singapore', label: 'Singapore Time', offset: 'UTC+8', region: 'Asia' },
  { value: 'Asia/Hong_Kong', label: 'Hong Kong Time', offset: 'UTC+8', region: 'Asia' },
  { value: 'Asia/Seoul', label: 'Korea Standard Time', offset: 'UTC+9', region: 'Asia' },
  { value: 'Australia/Melbourne', label: 'Australian Eastern Time (Melbourne)', offset: 'UTC+10/+11', region: 'Australia' },
  { value: 'Pacific/Auckland', label: 'New Zealand Time', offset: 'UTC+12/+13', region: 'Pacific' },
  { value: 'Africa/Cairo', label: 'Eastern European Time', offset: 'UTC+2', region: 'Africa' },
  { value: 'Africa/Johannesburg', label: 'South Africa Time', offset: 'UTC+2', region: 'Africa' },
];

export interface TimezoneSelectorProps {
  value?: string;
  onChange: (timezone: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  showSearch?: boolean;
  showPopular?: boolean;
  detectUserTimezone?: boolean;
}

export function TimezoneSelector({
  value,
  onChange,
  placeholder = "Select timezone...",
  className,
  disabled = false,
  showSearch = true,
  showPopular = true,
  detectUserTimezone = true
}: TimezoneSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [userTimezone, setUserTimezone] = useState<string | null>(null);

  // Detect user's timezone
  React.useEffect(() => {
    if (detectUserTimezone) {
      try {
        const detected = Intl.DateTimeFormat().resolvedOptions().timeZone;
        setUserTimezone(detected);
      } catch (error) {
        console.warn('Could not detect user timezone:', error);
      }
    }
  }, [detectUserTimezone]);

  // Filter timezones based on search
  const filteredTimezones = useMemo(() => {
    if (!searchQuery) return TIMEZONE_DATA;
    
    const query = searchQuery.toLowerCase();
    return TIMEZONE_DATA.filter(tz => 
      tz.label.toLowerCase().includes(query) ||
      tz.value.toLowerCase().includes(query) ||
      tz.region.toLowerCase().includes(query) ||
      tz.offset.toLowerCase().includes(query)
    );
  }, [searchQuery]);

  // Group timezones
  const groupedTimezones = useMemo(() => {
    const groups: Record<string, TimezoneOption[]> = {};
    
    filteredTimezones.forEach(tz => {
      if (!groups[tz.region]) {
        groups[tz.region] = [];
      }
      groups[tz.region].push(tz);
    });
    
    return groups;
  }, [filteredTimezones]);

  const popularTimezones = useMemo(() => 
    filteredTimezones.filter(tz => tz.popular), 
    [filteredTimezones]
  );

  const getCurrentTime = (timezone: string) => {
    try {
      return new Intl.DateTimeFormat('en-US', {
        timeZone: timezone,
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }).format(new Date());
    } catch {
      return '';
    }
  };

  const selectedTimezone = TIMEZONE_DATA.find(tz => tz.value === value);

  return (
    <div className={cn("space-y-2", className)}>
      {/* Current selection display */}
      {selectedTimezone && (
        <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-md text-sm">
          <Globe className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{selectedTimezone.label}</span>
          <Badge variant="secondary" className="text-xs">
            {selectedTimezone.offset}
          </Badge>
          <div className="flex items-center gap-1 ml-auto text-muted-foreground">
            <Clock className="h-3 w-3" />
            {getCurrentTime(selectedTimezone.value)}
          </div>
        </div>
      )}

      <Select value={value} onValueChange={onChange} disabled={disabled}>
        <SelectTrigger className={className}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent className="max-h-80">
          {/* Search */}
          {showSearch && (
            <div className="p-2 border-b">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search timezones..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8 h-8"
                />
              </div>
            </div>
          )}

          {/* User's detected timezone */}
          {userTimezone && !value && (
            <>
              <div className="px-2 py-1 text-xs font-medium text-muted-foreground">
                Detected
              </div>
              <SelectItem value={userTimezone}>
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-2">
                    <span>{TIMEZONE_DATA.find(tz => tz.value === userTimezone)?.label || userTimezone}</span>
                    <Badge variant="outline" className="text-xs">Auto</Badge>
                  </div>
                  <span className="text-xs text-muted-foreground ml-2">
                    {getCurrentTime(userTimezone)}
                  </span>
                </div>
              </SelectItem>
            </>
          )}

          {/* Popular timezones */}
          {showPopular && popularTimezones.length > 0 && (
            <>
              <div className="px-2 py-1 text-xs font-medium text-muted-foreground">
                Popular
              </div>
              {popularTimezones.map((tz) => (
                <SelectItem key={`popular-${tz.value}`} value={tz.value}>
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                      <span>{tz.label}</span>
                      <Badge variant="secondary" className="text-xs">
                        {tz.offset}
                      </Badge>
                    </div>
                    <span className="text-xs text-muted-foreground ml-2">
                      {getCurrentTime(tz.value)}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </>
          )}

          {/* Grouped timezones */}
          {Object.entries(groupedTimezones).map(([region, timezones]) => (
            <div key={region}>
              <div className="px-2 py-1 text-xs font-medium text-muted-foreground">
                {region}
              </div>
              {timezones.map((tz) => (
                <SelectItem key={tz.value} value={tz.value}>
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                      <span>{tz.label}</span>
                      <Badge variant="outline" className="text-xs">
                        {tz.offset}
                      </Badge>
                    </div>
                    <span className="text-xs text-muted-foreground ml-2">
                      {getCurrentTime(tz.value)}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </div>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
