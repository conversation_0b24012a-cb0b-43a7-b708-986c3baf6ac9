"use client";

import React, { useState, useMemo } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Check, ChevronsUpDown, DollarSign, Search } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface Currency {
  code: string; // ISO 4217
  name: string;
  symbol: string;
  flag?: string; // Country flag for major currencies
  popular?: boolean;
  region?: string;
}

// Comprehensive currency list with major world currencies
const CURRENCIES: Currency[] = [
  // Major/Popular currencies
  { code: 'USD', name: 'US Dollar', symbol: '$', flag: '🇺🇸', popular: true, region: 'Americas' },
  { code: 'EUR', name: 'Euro', symbol: '€', flag: '🇪🇺', popular: true, region: 'Europe' },
  { code: 'GBP', name: 'British Pound', symbol: '£', flag: '🇬🇧', popular: true, region: 'Europe' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥', flag: '🇯🇵', popular: true, region: 'Asia' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$', flag: '🇨🇦', popular: true, region: 'Americas' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$', flag: '🇦🇺', popular: true, region: 'Oceania' },
  { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF', flag: '🇨🇭', popular: true, region: 'Europe' },
  { code: 'CNY', name: 'Chinese Yuan', symbol: '¥', flag: '🇨🇳', popular: true, region: 'Asia' },
  { code: 'INR', name: 'Indian Rupee', symbol: '₹', flag: '🇮🇳', popular: true, region: 'Asia' },
  { code: 'SGD', name: 'Singapore Dollar', symbol: 'S$', flag: '🇸🇬', popular: true, region: 'Asia' },

  // Other major currencies
  { code: 'AED', name: 'UAE Dirham', symbol: 'د.إ', flag: '🇦🇪', region: 'Middle East' },
  { code: 'AFN', name: 'Afghan Afghani', symbol: '؋', region: 'Asia' },
  { code: 'ALL', name: 'Albanian Lek', symbol: 'L', region: 'Europe' },
  { code: 'AMD', name: 'Armenian Dram', symbol: '֏', region: 'Asia' },
  { code: 'ANG', name: 'Netherlands Antillean Guilder', symbol: 'ƒ', region: 'Americas' },
  { code: 'AOA', name: 'Angolan Kwanza', symbol: 'Kz', region: 'Africa' },
  { code: 'ARS', name: 'Argentine Peso', symbol: '$', flag: '🇦🇷', region: 'Americas' },
  { code: 'AWG', name: 'Aruban Florin', symbol: 'ƒ', region: 'Americas' },
  { code: 'AZN', name: 'Azerbaijani Manat', symbol: '₼', region: 'Asia' },
  { code: 'BAM', name: 'Bosnia-Herzegovina Convertible Mark', symbol: 'KM', region: 'Europe' },
  { code: 'BBD', name: 'Barbadian Dollar', symbol: '$', region: 'Americas' },
  { code: 'BDT', name: 'Bangladeshi Taka', symbol: '৳', flag: '🇧🇩', region: 'Asia' },
  { code: 'BGN', name: 'Bulgarian Lev', symbol: 'лв', region: 'Europe' },
  { code: 'BHD', name: 'Bahraini Dinar', symbol: '.د.ب', region: 'Middle East' },
  { code: 'BIF', name: 'Burundian Franc', symbol: 'Fr', region: 'Africa' },
  { code: 'BMD', name: 'Bermudan Dollar', symbol: '$', region: 'Americas' },
  { code: 'BND', name: 'Brunei Dollar', symbol: '$', region: 'Asia' },
  { code: 'BOB', name: 'Bolivian Boliviano', symbol: 'Bs.', region: 'Americas' },
  { code: 'BRL', name: 'Brazilian Real', symbol: 'R$', flag: '🇧🇷', region: 'Americas' },
  { code: 'BSD', name: 'Bahamian Dollar', symbol: '$', region: 'Americas' },
  { code: 'BTN', name: 'Bhutanese Ngultrum', symbol: 'Nu.', region: 'Asia' },
  { code: 'BWP', name: 'Botswanan Pula', symbol: 'P', region: 'Africa' },
  { code: 'BYN', name: 'Belarusian Ruble', symbol: 'Br', region: 'Europe' },
  { code: 'BZD', name: 'Belize Dollar', symbol: '$', region: 'Americas' },
  { code: 'CDF', name: 'Congolese Franc', symbol: 'Fr', region: 'Africa' },
  { code: 'CLP', name: 'Chilean Peso', symbol: '$', flag: '🇨🇱', region: 'Americas' },
  { code: 'COP', name: 'Colombian Peso', symbol: '$', flag: '🇨🇴', region: 'Americas' },
  { code: 'CRC', name: 'Costa Rican Colón', symbol: '₡', region: 'Americas' },
  { code: 'CUP', name: 'Cuban Peso', symbol: '$', region: 'Americas' },
  { code: 'CVE', name: 'Cape Verdean Escudo', symbol: '$', region: 'Africa' },
  { code: 'CZK', name: 'Czech Koruna', symbol: 'Kč', flag: '🇨🇿', region: 'Europe' },
  { code: 'DJF', name: 'Djiboutian Franc', symbol: 'Fr', region: 'Africa' },
  { code: 'DKK', name: 'Danish Krone', symbol: 'kr', flag: '🇩🇰', region: 'Europe' },
  { code: 'DOP', name: 'Dominican Peso', symbol: '$', region: 'Americas' },
  { code: 'DZD', name: 'Algerian Dinar', symbol: 'د.ج', region: 'Africa' },
  { code: 'EGP', name: 'Egyptian Pound', symbol: '£', flag: '🇪🇬', region: 'Africa' },
  { code: 'ERN', name: 'Eritrean Nakfa', symbol: 'Nfk', region: 'Africa' },
  { code: 'ETB', name: 'Ethiopian Birr', symbol: 'Br', region: 'Africa' },
  { code: 'FJD', name: 'Fijian Dollar', symbol: '$', region: 'Oceania' },
  { code: 'FKP', name: 'Falkland Islands Pound', symbol: '£', region: 'Americas' },
  { code: 'GEL', name: 'Georgian Lari', symbol: '₾', region: 'Asia' },
  { code: 'GGP', name: 'Guernsey Pound', symbol: '£', region: 'Europe' },
  { code: 'GHS', name: 'Ghanaian Cedi', symbol: '₵', region: 'Africa' },
  { code: 'GIP', name: 'Gibraltar Pound', symbol: '£', region: 'Europe' },
  { code: 'GMD', name: 'Gambian Dalasi', symbol: 'D', region: 'Africa' },
  { code: 'GNF', name: 'Guinean Franc', symbol: 'Fr', region: 'Africa' },
  { code: 'GTQ', name: 'Guatemalan Quetzal', symbol: 'Q', region: 'Americas' },
  { code: 'GYD', name: 'Guyanaese Dollar', symbol: '$', region: 'Americas' },
  { code: 'HKD', name: 'Hong Kong Dollar', symbol: 'HK$', flag: '🇭🇰', region: 'Asia' },
  { code: 'HNL', name: 'Honduran Lempira', symbol: 'L', region: 'Americas' },
  { code: 'HRK', name: 'Croatian Kuna', symbol: 'kn', region: 'Europe' },
  { code: 'HTG', name: 'Haitian Gourde', symbol: 'G', region: 'Americas' },
  { code: 'HUF', name: 'Hungarian Forint', symbol: 'Ft', flag: '🇭🇺', region: 'Europe' },
  { code: 'IDR', name: 'Indonesian Rupiah', symbol: 'Rp', flag: '🇮🇩', region: 'Asia' },
  { code: 'ILS', name: 'Israeli New Sheqel', symbol: '₪', flag: '🇮🇱', region: 'Middle East' },
  { code: 'IMP', name: 'Manx Pound', symbol: '£', region: 'Europe' },
  { code: 'IQD', name: 'Iraqi Dinar', symbol: 'ع.د', region: 'Middle East' },
  { code: 'IRR', name: 'Iranian Rial', symbol: '﷼', region: 'Middle East' },
  { code: 'ISK', name: 'Icelandic Króna', symbol: 'kr', region: 'Europe' },
  { code: 'JEP', name: 'Jersey Pound', symbol: '£', region: 'Europe' },
  { code: 'JMD', name: 'Jamaican Dollar', symbol: '$', region: 'Americas' },
  { code: 'JOD', name: 'Jordanian Dinar', symbol: 'د.ا', region: 'Middle East' },
  { code: 'KES', name: 'Kenyan Shilling', symbol: 'Sh', flag: '🇰🇪', region: 'Africa' },
  { code: 'KGS', name: 'Kyrgystani Som', symbol: 'с', region: 'Asia' },
  { code: 'KHR', name: 'Cambodian Riel', symbol: '៛', region: 'Asia' },
  { code: 'KMF', name: 'Comorian Franc', symbol: 'Fr', region: 'Africa' },
  { code: 'KPW', name: 'North Korean Won', symbol: '₩', region: 'Asia' },
  { code: 'KRW', name: 'South Korean Won', symbol: '₩', flag: '🇰🇷', region: 'Asia' },
  { code: 'KWD', name: 'Kuwaiti Dinar', symbol: 'د.ك', region: 'Middle East' },
  { code: 'KYD', name: 'Cayman Islands Dollar', symbol: '$', region: 'Americas' },
  { code: 'KZT', name: 'Kazakhstani Tenge', symbol: '₸', region: 'Asia' },
  { code: 'LAK', name: 'Laotian Kip', symbol: '₭', region: 'Asia' },
  { code: 'LBP', name: 'Lebanese Pound', symbol: 'ل.ل', region: 'Middle East' },
  { code: 'LKR', name: 'Sri Lankan Rupee', symbol: 'Rs', flag: '🇱🇰', region: 'Asia' },
  { code: 'LRD', name: 'Liberian Dollar', symbol: '$', region: 'Africa' },
  { code: 'LSL', name: 'Lesotho Loti', symbol: 'L', region: 'Africa' },
  { code: 'LYD', name: 'Libyan Dinar', symbol: 'ل.د', region: 'Africa' },
  { code: 'MAD', name: 'Moroccan Dirham', symbol: 'د.م.', flag: '🇲🇦', region: 'Africa' },
  { code: 'MDL', name: 'Moldovan Leu', symbol: 'L', region: 'Europe' },
  { code: 'MGA', name: 'Malagasy Ariary', symbol: 'Ar', region: 'Africa' },
  { code: 'MKD', name: 'Macedonian Denar', symbol: 'ден', region: 'Europe' },
  { code: 'MMK', name: 'Myanmar Kyat', symbol: 'Ks', region: 'Asia' },
  { code: 'MNT', name: 'Mongolian Tugrik', symbol: '₮', region: 'Asia' },
  { code: 'MOP', name: 'Macanese Pataca', symbol: 'P', region: 'Asia' },
  { code: 'MRU', name: 'Mauritanian Ouguiya', symbol: 'UM', region: 'Africa' },
  { code: 'MUR', name: 'Mauritian Rupee', symbol: '₨', region: 'Africa' },
  { code: 'MVR', name: 'Maldivian Rufiyaa', symbol: '.ރ', region: 'Asia' },
  { code: 'MWK', name: 'Malawian Kwacha', symbol: 'MK', region: 'Africa' },
  { code: 'MXN', name: 'Mexican Peso', symbol: '$', flag: '🇲🇽', region: 'Americas' },
  { code: 'MYR', name: 'Malaysian Ringgit', symbol: 'RM', flag: '🇲🇾', region: 'Asia' },
  { code: 'MZN', name: 'Mozambican Metical', symbol: 'MT', region: 'Africa' },
  { code: 'NAD', name: 'Namibian Dollar', symbol: '$', region: 'Africa' },
  { code: 'NGN', name: 'Nigerian Naira', symbol: '₦', flag: '🇳🇬', region: 'Africa' },
  { code: 'NIO', name: 'Nicaraguan Córdoba', symbol: 'C$', region: 'Americas' },
  { code: 'NOK', name: 'Norwegian Krone', symbol: 'kr', flag: '🇳🇴', region: 'Europe' },
  { code: 'NPR', name: 'Nepalese Rupee', symbol: '₨', region: 'Asia' },
  { code: 'NZD', name: 'New Zealand Dollar', symbol: 'NZ$', flag: '🇳🇿', region: 'Oceania' },
  { code: 'OMR', name: 'Omani Rial', symbol: 'ر.ع.', region: 'Middle East' },
  { code: 'PAB', name: 'Panamanian Balboa', symbol: 'B/.', region: 'Americas' },
  { code: 'PEN', name: 'Peruvian Nuevo Sol', symbol: 'S/', flag: '🇵🇪', region: 'Americas' },
  { code: 'PGK', name: 'Papua New Guinean Kina', symbol: 'K', region: 'Oceania' },
  { code: 'PHP', name: 'Philippine Peso', symbol: '₱', flag: '🇵🇭', region: 'Asia' },
  { code: 'PKR', name: 'Pakistani Rupee', symbol: '₨', flag: '🇵🇰', region: 'Asia' },
  { code: 'PLN', name: 'Polish Zloty', symbol: 'zł', flag: '🇵🇱', region: 'Europe' },
  { code: 'PYG', name: 'Paraguayan Guarani', symbol: '₲', region: 'Americas' },
  { code: 'QAR', name: 'Qatari Rial', symbol: 'ر.ق', flag: '🇶🇦', region: 'Middle East' },
  { code: 'RON', name: 'Romanian Leu', symbol: 'lei', flag: '🇷🇴', region: 'Europe' },
  { code: 'RSD', name: 'Serbian Dinar', symbol: 'дин.', region: 'Europe' },
  { code: 'RUB', name: 'Russian Ruble', symbol: '₽', flag: '🇷🇺', region: 'Europe' },
  { code: 'RWF', name: 'Rwandan Franc', symbol: 'Fr', region: 'Africa' },
  { code: 'SAR', name: 'Saudi Riyal', symbol: 'ر.س', flag: '🇸🇦', region: 'Middle East' },
  { code: 'SBD', name: 'Solomon Islands Dollar', symbol: '$', region: 'Oceania' },
  { code: 'SCR', name: 'Seychellois Rupee', symbol: '₨', region: 'Africa' },
  { code: 'SDG', name: 'Sudanese Pound', symbol: 'ج.س.', region: 'Africa' },
  { code: 'SEK', name: 'Swedish Krona', symbol: 'kr', flag: '🇸🇪', region: 'Europe' },
  { code: 'SHP', name: 'Saint Helena Pound', symbol: '£', region: 'Africa' },
  { code: 'SLE', name: 'Sierra Leonean Leone', symbol: 'Le', region: 'Africa' },
  { code: 'SOS', name: 'Somali Shilling', symbol: 'Sh', region: 'Africa' },
  { code: 'SRD', name: 'Surinamese Dollar', symbol: '$', region: 'Americas' },
  { code: 'SSP', name: 'South Sudanese Pound', symbol: '£', region: 'Africa' },
  { code: 'STN', name: 'São Tomé and Príncipe Dobra', symbol: 'Db', region: 'Africa' },
  { code: 'SYP', name: 'Syrian Pound', symbol: '£', region: 'Middle East' },
  { code: 'SZL', name: 'Swazi Lilangeni', symbol: 'L', region: 'Africa' },
  { code: 'THB', name: 'Thai Baht', symbol: '฿', flag: '🇹🇭', region: 'Asia' },
  { code: 'TJS', name: 'Tajikistani Somoni', symbol: 'ЅМ', region: 'Asia' },
  { code: 'TMT', name: 'Turkmenistani Manat', symbol: 'm', region: 'Asia' },
  { code: 'TND', name: 'Tunisian Dinar', symbol: 'د.ت', region: 'Africa' },
  { code: 'TOP', name: 'Tongan Paʻanga', symbol: 'T$', region: 'Oceania' },
  { code: 'TRY', name: 'Turkish Lira', symbol: '₺', flag: '🇹🇷', region: 'Europe' },
  { code: 'TTD', name: 'Trinidad and Tobago Dollar', symbol: '$', region: 'Americas' },
  { code: 'TWD', name: 'New Taiwan Dollar', symbol: 'NT$', flag: '🇹🇼', region: 'Asia' },
  { code: 'TZS', name: 'Tanzanian Shilling', symbol: 'Sh', region: 'Africa' },
  { code: 'UAH', name: 'Ukrainian Hryvnia', symbol: '₴', flag: '🇺🇦', region: 'Europe' },
  { code: 'UGX', name: 'Ugandan Shilling', symbol: 'Sh', region: 'Africa' },
  { code: 'UYU', name: 'Uruguayan Peso', symbol: '$', region: 'Americas' },
  { code: 'UZS', name: 'Uzbekistan Som', symbol: 'so\'m', region: 'Asia' },
  { code: 'VES', name: 'Venezuelan Bolívar', symbol: 'Bs.S.', region: 'Americas' },
  { code: 'VND', name: 'Vietnamese Dong', symbol: '₫', flag: '🇻🇳', region: 'Asia' },
  { code: 'VUV', name: 'Vanuatu Vatu', symbol: 'Vt', region: 'Oceania' },
  { code: 'WST', name: 'Samoan Tala', symbol: 'T', region: 'Oceania' },
  { code: 'XAF', name: 'CFA Franc BEAC', symbol: 'Fr', region: 'Africa' },
  { code: 'XCD', name: 'East Caribbean Dollar', symbol: '$', region: 'Americas' },
  { code: 'XDR', name: 'Special Drawing Rights', symbol: 'SDR', region: 'International' },
  { code: 'XOF', name: 'CFA Franc BCEAO', symbol: 'Fr', region: 'Africa' },
  { code: 'XPF', name: 'CFP Franc', symbol: 'Fr', region: 'Oceania' },
  { code: 'YER', name: 'Yemeni Rial', symbol: '﷼', region: 'Middle East' },
  { code: 'ZAR', name: 'South African Rand', symbol: 'R', flag: '🇿🇦', region: 'Africa' },
  { code: 'ZMW', name: 'Zambian Kwacha', symbol: 'ZK', region: 'Africa' },
  { code: 'ZWL', name: 'Zimbabwean Dollar', symbol: '$', region: 'Africa' },
];

export interface CurrencySelectorProps {
  value?: string; // Currency code
  onChange: (currencyCode: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  showSymbol?: boolean;
  showFlag?: boolean;
  showSearch?: boolean;
  showPopular?: boolean;
  variant?: 'default' | 'compact';
}

export function CurrencySelector({
  value,
  onChange,
  placeholder = "Select currency...",
  className,
  disabled = false,
  showSymbol = true,
  showFlag = false,
  showSearch = true,
  showPopular = true,
  variant = 'default'
}: CurrencySelectorProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Filter currencies based on search
  const filteredCurrencies = useMemo(() => {
    if (!searchQuery) return CURRENCIES;
    
    const query = searchQuery.toLowerCase();
    return CURRENCIES.filter(currency => 
      currency.name.toLowerCase().includes(query) ||
      currency.code.toLowerCase().includes(query) ||
      currency.symbol.toLowerCase().includes(query) ||
      currency.region?.toLowerCase().includes(query)
    );
  }, [searchQuery]);

  // Group currencies
  const popularCurrencies = useMemo(() => 
    filteredCurrencies.filter(currency => currency.popular), 
    [filteredCurrencies]
  );

  const otherCurrencies = useMemo(() => 
    filteredCurrencies.filter(currency => !currency.popular), 
    [filteredCurrencies]
  );

  const selectedCurrency = CURRENCIES.find(currency => currency.code === value);

  if (variant === 'compact') {
    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn("justify-between", className)}
            disabled={disabled}
          >
            {selectedCurrency ? (
              <div className="flex items-center gap-2">
                {showFlag && selectedCurrency.flag && <span>{selectedCurrency.flag}</span>}
                {showSymbol && <span className="font-mono">{selectedCurrency.symbol}</span>}
                <span className="font-medium">{selectedCurrency.code}</span>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-muted-foreground">
                <DollarSign className="h-4 w-4" />
                {placeholder}
              </div>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="start">
          <Command>
            {showSearch && (
              <div className="flex items-center border-b px-3">
                <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                <Input
                  placeholder="Search currencies..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                />
              </div>
            )}
            <div className="max-h-60 overflow-y-auto">
              <CommandEmpty>No currency found.</CommandEmpty>

              {showPopular && popularCurrencies.length > 0 && (
                <CommandGroup heading="Popular Currencies">
                  {popularCurrencies.map((currency) => (
                    <CommandItem
                      key={currency.code}
                      value={currency.code}
                      onSelect={() => {
                        onChange(currency.code);
                        setOpen(false);
                      }}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          value === currency.code ? "opacity-100" : "opacity-0"
                        )}
                      />
                      <div className="flex items-center gap-2 flex-1">
                        {showFlag && currency.flag && <span>{currency.flag}</span>}
                        {showSymbol && <span className="font-mono w-8">{currency.symbol}</span>}
                        <span className="font-medium">{currency.code}</span>
                        <span className="text-muted-foreground truncate">- {currency.name}</span>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}

              <CommandGroup heading={showPopular ? "All Currencies" : "Currencies"}>
                {otherCurrencies.map((currency) => (
                  <CommandItem
                    key={currency.code}
                    value={currency.code}
                    onSelect={() => {
                      onChange(currency.code);
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === currency.code ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <div className="flex items-center gap-2 flex-1">
                      {showFlag && currency.flag && <span>{currency.flag}</span>}
                      {showSymbol && <span className="font-mono w-8">{currency.symbol}</span>}
                      <span className="font-medium">{currency.code}</span>
                      <span className="text-muted-foreground truncate">- {currency.name}</span>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </div>
          </Command>
        </PopoverContent>
      </Popover>
    );
  }

  // Default variant with search
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("justify-between", className)}
          disabled={disabled}
        >
          {selectedCurrency ? (
            <div className="flex items-center gap-2">
              {showFlag && selectedCurrency.flag && <span>{selectedCurrency.flag}</span>}
              {showSymbol && <span className="font-mono">{selectedCurrency.symbol}</span>}
              <span className="font-medium">{selectedCurrency.code}</span>
              <span className="text-muted-foreground">- {selectedCurrency.name}</span>
            </div>
          ) : (
            <div className="flex items-center gap-2 text-muted-foreground">
              <DollarSign className="h-4 w-4" />
              {placeholder}
            </div>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-96 p-0" align="start">
        <Command>
          {showSearch && (
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <Input
                placeholder="Search currencies..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
              />
            </div>
          )}
          <div className="max-h-80 overflow-y-auto">
            <CommandEmpty>No currency found.</CommandEmpty>
            
            {showPopular && popularCurrencies.length > 0 && (
              <CommandGroup heading="Popular Currencies">
                {popularCurrencies.map((currency) => (
                  <CommandItem
                    key={currency.code}
                    value={currency.code}
                    onSelect={() => {
                      onChange(currency.code);
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === currency.code ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <div className="flex items-center gap-2 flex-1">
                      {showFlag && currency.flag && <span>{currency.flag}</span>}
                      {showSymbol && <span className="font-mono w-8">{currency.symbol}</span>}
                      <span className="font-medium">{currency.code}</span>
                      <span className="text-muted-foreground">- {currency.name}</span>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
            
            <CommandGroup heading="All Currencies">
              {otherCurrencies.map((currency) => (
                <CommandItem
                  key={currency.code}
                  value={currency.code}
                  onSelect={() => {
                    onChange(currency.code);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === currency.code ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <div className="flex items-center gap-2 flex-1">
                    {showFlag && currency.flag && <span>{currency.flag}</span>}
                    {showSymbol && <span className="font-mono w-8">{currency.symbol}</span>}
                    <span className="font-medium">{currency.code}</span>
                    <span className="text-muted-foreground">- {currency.name}</span>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </div>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

// Helper function to get currency by code
export function getCurrencyByCode(code: string): Currency | undefined {
  return CURRENCIES.find(currency => currency.code === code);
}

// Helper function to get currencies by region
export function getCurrenciesByRegion(region: string): Currency[] {
  return CURRENCIES.filter(currency => currency.region === region);
}

// Helper function to get popular currencies
export function getPopularCurrencies(): Currency[] {
  return CURRENCIES.filter(currency => currency.popular);
}

// Helper function to format currency amount
export function formatCurrency(amount: number, currencyCode: string, locale: string = 'en-US'): string {
  const currency = getCurrencyByCode(currencyCode);
  if (!currency) return `${amount} ${currencyCode}`;
  
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currencyCode,
    }).format(amount);
  } catch {
    return `${currency.symbol}${amount.toLocaleString()}`;
  }
}
