"use client";

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Filter, 
  Search, 
  X, 
  ChevronDown,
  RotateCcw,
  Settings,
  Star
} from "lucide-react";
import { cn } from "@/lib/utils";

export interface FilterOption {
  value: string;
  label: string;
  color?: string;
  icon?: React.ReactNode;
}

export interface FilterSection {
  key: string;
  label: string;
  icon?: React.ReactNode;
  type: 'checkbox' | 'badge' | 'range' | 'select' | 'date';
  options?: FilterOption[];
  placeholder?: string;
  multiple?: boolean;
}

export interface FilterCriteria {
  [key: string]: any;
}

export interface FilterPreset {
  id: string;
  name: string;
  criteria: FilterCriteria;
  isDefault?: boolean;
  usageCount: number;
}

interface EnhancedFiltersProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  searchPlaceholder?: string;
  sections: FilterSection[];
  criteria: FilterCriteria;
  onCriteriaChange: (criteria: FilterCriteria) => void;
  presets?: FilterPreset[];
  onPresetSave?: (name: string, criteria: FilterCriteria) => void;
  onPresetDelete?: (id: string) => void;
  onPresetApply?: (preset: FilterPreset) => void;
  className?: string;
  compact?: boolean;
}

export function EnhancedFilters({
  searchValue,
  onSearchChange,
  searchPlaceholder = "Search...",
  sections,
  criteria,
  onCriteriaChange,
  presets = [],
  onPresetSave,
  onPresetDelete,
  onPresetApply,
  className,
  compact = false
}: EnhancedFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activePreset, setActivePreset] = useState<string | null>(null);

  const updateCriteria = useCallback((updates: Partial<FilterCriteria>) => {
    onCriteriaChange({ ...criteria, ...updates });
    setActivePreset(null);
  }, [criteria, onCriteriaChange]);

  const toggleArrayValue = useCallback((key: string, value: string) => {
    const currentArray = Array.isArray(criteria[key]) ? criteria[key] : [];
    const isSelected = currentArray.includes(value);
    const newArray = isSelected
      ? currentArray.filter((item: string) => item !== value)
      : [...currentArray, value];

    updateCriteria({ [key]: newArray });
  }, [criteria, updateCriteria]);

  const clearAllFilters = useCallback(() => {
    const defaultCriteria: FilterCriteria = {};
    sections.forEach(section => {
      if (section.type === 'checkbox' || section.type === 'badge') {
        defaultCriteria[section.key] = [];
      } else if (section.type === 'range') {
        defaultCriteria[section.key] = { min: null, max: null };
      } else {
        defaultCriteria[section.key] = '';
      }
    });
    onCriteriaChange(defaultCriteria);
    setActivePreset(null);
  }, [sections, onCriteriaChange]);

  const getActiveFiltersCount = useCallback(() => {
    let count = 0;
    if (searchValue) count++;
    sections.forEach(section => {
      const value = criteria[section.key];
      if (Array.isArray(value) && value.length > 0) count++;
      else if (typeof value === 'object' && value !== null) {
        if (value.min !== null || value.max !== null) count++;
      } else if (value && value !== '') count++;
    });
    return count;
  }, [searchValue, criteria, sections]);

  const applyPreset = useCallback((preset: FilterPreset) => {
    onCriteriaChange(preset.criteria);
    setActivePreset(preset.id);
    onPresetApply?.(preset);
  }, [onCriteriaChange, onPresetApply]);

  const hasActiveFilters = getActiveFiltersCount() > 0;

  const renderFilterSection = (section: FilterSection) => {
    const value = criteria[section.key];

    switch (section.type) {
      case 'badge':
        return (
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              {section.icon}
              {section.label}
            </Label>
            <div className="grid grid-cols-2 gap-2">
              {section.options?.map((option) => (
                <Badge
                  key={option.value}
                  variant={Array.isArray(value) && value.includes(option.value) ? "default" : "outline"}
                  className={cn(
                    "cursor-pointer justify-center py-2 px-3 hover:scale-105 transition-all",
                    Array.isArray(value) && value.includes(option.value) ? option.color : 'hover:bg-muted'
                  )}
                  onClick={() => toggleArrayValue(section.key, option.value)}
                >
                  {option.icon}
                  {option.label}
                </Badge>
              ))}
            </div>
          </div>
        );

      case 'checkbox':
        return (
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              {section.icon}
              {section.label}
            </Label>
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
              {section.options?.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`${section.key}-${option.value}`}
                    checked={Array.isArray(value) ? value.includes(option.value) : false}
                    onCheckedChange={(checked) => {
                      const currentArray = Array.isArray(value) ? value : [];
                      if (checked) {
                        updateCriteria({ [section.key]: [...currentArray, option.value] });
                      } else {
                        updateCriteria({ [section.key]: currentArray.filter((v: string) => v !== option.value) });
                      }
                    }}
                  />
                  <Label htmlFor={`${section.key}-${option.value}`} className="text-sm cursor-pointer">
                    {option.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        );

      case 'range':
        return (
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              {section.icon}
              {section.label}
            </Label>
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">Minimum</Label>
                <Input
                  type="number"
                  placeholder="0"
                  value={value?.min || ''}
                  onChange={(e) => updateCriteria({ 
                    [section.key]: { 
                      ...value, 
                      min: e.target.value ? parseInt(e.target.value) : null 
                    } 
                  })}
                  className="h-9"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">Maximum</Label>
                <Input
                  type="number"
                  placeholder="∞"
                  value={value?.max || ''}
                  onChange={(e) => updateCriteria({ 
                    [section.key]: { 
                      ...value, 
                      max: e.target.value ? parseInt(e.target.value) : null 
                    } 
                  })}
                  className="h-9"
                />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (compact) {
    return (
      <div className={cn("flex items-center gap-4", className)}>
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={searchPlaceholder}
            value={searchValue}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className="gap-2">
              <Filter className="h-4 w-4" />
              Filters
              {hasActiveFilters && (
                <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 flex items-center justify-center text-xs">
                  {getActiveFiltersCount()}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[500px] p-0" align="end">
            <Card className="border-0 shadow-none">
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Advanced Filters</CardTitle>
                <CardDescription>Refine your search results</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6 max-h-[70vh] overflow-y-auto">
                {sections.map((section, index) => (
                  <React.Fragment key={section.key}>
                    {renderFilterSection(section)}
                    {index < sections.length - 1 && <Separator />}
                  </React.Fragment>
                ))}
                
                <Separator />
                
                <div className="flex justify-between pt-2">
                  <Button variant="outline" size="sm" onClick={clearAllFilters} className="gap-2">
                    <RotateCcw className="h-4 w-4" />
                    Clear All
                  </Button>
                </div>
              </CardContent>
            </Card>
          </PopoverContent>
        </Popover>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
            {hasActiveFilters && (
              <Badge variant="secondary" className="ml-2">
                {getActiveFiltersCount()} active
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="gap-1 text-muted-foreground hover:text-foreground"
              >
                <X className="h-3 w-3" />
                Clear
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <ChevronDown className={`h-4 w-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={searchPlaceholder}
            value={searchValue}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 h-10"
          />
        </div>

        {isExpanded && (
          <div className="space-y-6">
            <Separator />
            {sections.map((section, index) => (
              <React.Fragment key={section.key}>
                {renderFilterSection(section)}
                {index < sections.length - 1 && <Separator />}
              </React.Fragment>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
