/**
 * EVEXA Enhanced Empty State Components
 * Comprehensive empty states with illustrations and actions
 */

'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { emptyStateVariants, type EmptyStateVariants } from '@/lib/component-variants';
import { Button } from '@/components/ui/button';
import { usePerformanceMonitor } from '@/lib/performance-utils';
import {
  Search,
  FileX,
  Users,
  Calendar,
  ShoppingCart,
  Mail,
  Image,
  Database,
  Inbox,
  Plus,
  RefreshCw,
  ArrowLeft,
  ExternalLink
} from 'lucide-react';

// Enhanced Empty State Component
export interface EmptyStateProps extends EmptyStateVariants {
  icon?: React.ReactNode;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: 'default' | 'outline' | 'ghost';
    icon?: React.ReactNode;
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
    variant?: 'default' | 'outline' | 'ghost';
    icon?: React.ReactNode;
  };
  illustration?: 'search' | 'files' | 'users' | 'calendar' | 'cart' | 'mail' | 'image' | 'database' | 'inbox' | 'custom';
  className?: string;
  children?: React.ReactNode;
}

const illustrations = {
  search: Search,
  files: FileX,
  users: Users,
  calendar: Calendar,
  cart: ShoppingCart,
  mail: Mail,
  image: Image,
  database: Database,
  inbox: Inbox,
};

export const EmptyState = React.forwardRef<HTMLDivElement, EmptyStateProps>(({
  variant = "default",
  size = "default",
  icon,
  title,
  description,
  action,
  secondaryAction,
  illustration = "search",
  className,
  children,
  ...props
}, ref) => {
  usePerformanceMonitor('EmptyState');

  const IconComponent = illustration !== 'custom' ? illustrations[illustration] : null;

  return (
    <div
      ref={ref}
      className={cn(emptyStateVariants({ variant, size }), className)}
      {...props}
    >
      {/* Icon or Illustration */}
      <div className="mb-4">
        {icon ? (
          <div className="text-muted-foreground/60">
            {icon}
          </div>
        ) : IconComponent ? (
          <div className="p-4 rounded-full bg-muted/50 text-muted-foreground/60">
            <IconComponent className="h-8 w-8" />
          </div>
        ) : null}
      </div>

      {/* Content */}
      <div className="space-y-2 max-w-md">
        <h3 className="text-lg font-semibold text-foreground">
          {title}
        </h3>
        {description && (
          <p className="text-muted-foreground">
            {description}
          </p>
        )}
      </div>

      {/* Actions */}
      {(action || secondaryAction) && (
        <div className="flex flex-col sm:flex-row gap-3 mt-6">
          {action && (
            <Button
              onClick={action.onClick}
              variant={action.variant || "default"}
              className="gap-2"
            >
              {action.icon}
              {action.label}
            </Button>
          )}
          {secondaryAction && (
            <Button
              onClick={secondaryAction.onClick}
              variant={secondaryAction.variant || "outline"}
              className="gap-2"
            >
              {secondaryAction.icon}
              {secondaryAction.label}
            </Button>
          )}
        </div>
      )}

      {/* Custom children */}
      {children && (
        <div className="mt-6">
          {children}
        </div>
      )}
    </div>
  );
});

EmptyState.displayName = 'EmptyState';

// Predefined Empty State Components
export const NoSearchResults: React.FC<{
  searchTerm?: string;
  onClearSearch?: () => void;
  onTryAgain?: () => void;
  className?: string;
}> = ({ searchTerm, onClearSearch, onTryAgain, className }) => {
  return (
    <EmptyState
      illustration="search"
      title="No results found"
      description={
        searchTerm
          ? `We couldn't find anything matching "${searchTerm}". Try adjusting your search terms.`
          : "Try adjusting your search terms or filters."
      }
      action={
        onClearSearch
          ? {
              label: "Clear search",
              onClick: onClearSearch,
              variant: "outline",
              icon: <RefreshCw className="h-4 w-4" />
            }
          : undefined
      }
      secondaryAction={
        onTryAgain
          ? {
              label: "Try again",
              onClick: onTryAgain,
              variant: "ghost"
            }
          : undefined
      }
      className={className}
    />
  );
};

export const NoDataAvailable: React.FC<{
  title?: string;
  description?: string;
  onRefresh?: () => void;
  onCreate?: () => void;
  className?: string;
}> = ({
  title = "No data available",
  description = "There's no data to display at the moment.",
  onRefresh,
  onCreate,
  className
}) => {
  return (
    <EmptyState
      illustration="database"
      title={title}
      description={description}
      action={
        onCreate
          ? {
              label: "Create new",
              onClick: onCreate,
              icon: <Plus className="h-4 w-4" />
            }
          : undefined
      }
      secondaryAction={
        onRefresh
          ? {
              label: "Refresh",
              onClick: onRefresh,
              variant: "outline",
              icon: <RefreshCw className="h-4 w-4" />
            }
          : undefined
      }
      className={className}
    />
  );
};

export const EmptyInbox: React.FC<{
  onCompose?: () => void;
  onRefresh?: () => void;
  className?: string;
}> = ({ onCompose, onRefresh, className }) => {
  return (
    <EmptyState
      illustration="inbox"
      title="Your inbox is empty"
      description="You're all caught up! No new messages to display."
      action={
        onCompose
          ? {
              label: "Compose message",
              onClick: onCompose,
              icon: <Plus className="h-4 w-4" />
            }
          : undefined
      }
      secondaryAction={
        onRefresh
          ? {
              label: "Check again",
              onClick: onRefresh,
              variant: "outline",
              icon: <RefreshCw className="h-4 w-4" />
            }
          : undefined
      }
      className={className}
    />
  );
};

export const EmptyCart: React.FC<{
  onBrowse?: () => void;
  className?: string;
}> = ({ onBrowse, className }) => {
  return (
    <EmptyState
      illustration="cart"
      title="Your cart is empty"
      description="Looks like you haven't added any items to your cart yet."
      action={
        onBrowse
          ? {
              label: "Browse products",
              onClick: onBrowse,
              icon: <ExternalLink className="h-4 w-4" />
            }
          : undefined
      }
      className={className}
    />
  );
};

export const NoUsersFound: React.FC<{
  onInvite?: () => void;
  onRefresh?: () => void;
  className?: string;
}> = ({ onInvite, onRefresh, className }) => {
  return (
    <EmptyState
      illustration="users"
      title="No users found"
      description="There are no users to display. Invite team members to get started."
      action={
        onInvite
          ? {
              label: "Invite users",
              onClick: onInvite,
              icon: <Plus className="h-4 w-4" />
            }
          : undefined
      }
      secondaryAction={
        onRefresh
          ? {
              label: "Refresh",
              onClick: onRefresh,
              variant: "outline",
              icon: <RefreshCw className="h-4 w-4" />
            }
          : undefined
      }
      className={className}
    />
  );
};

export const ErrorState: React.FC<{
  title?: string;
  description?: string;
  onRetry?: () => void;
  onGoBack?: () => void;
  className?: string;
}> = ({
  title = "Something went wrong",
  description = "We encountered an error while loading this content.",
  onRetry,
  onGoBack,
  className
}) => {
  return (
    <EmptyState
      variant="card"
      icon={
        <div className="p-4 rounded-full bg-error/10 text-error">
          <FileX className="h-8 w-8" />
        </div>
      }
      title={title}
      description={description}
      action={
        onRetry
          ? {
              label: "Try again",
              onClick: onRetry,
              icon: <RefreshCw className="h-4 w-4" />
            }
          : undefined
      }
      secondaryAction={
        onGoBack
          ? {
              label: "Go back",
              onClick: onGoBack,
              variant: "outline",
              icon: <ArrowLeft className="h-4 w-4" />
            }
          : undefined
      }
      className={className}
    />
  );
};

// Empty State with Custom Illustration
export const CustomEmptyState: React.FC<{
  illustration: React.ReactNode;
  title: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
}> = ({ illustration, title, description, actions, className }) => {
  return (
    <EmptyState
      icon={illustration}
      title={title}
      description={description}
      className={className}
    >
      {actions}
    </EmptyState>
  );
};

export default EmptyState;
