"use client";

import { useEffect } from 'react';

export default function SecurityInitializer() {
  useEffect(() => {
    const initializeSecurity = async () => {
      try {
        // Set build-time constants for development
        if (typeof window !== 'undefined') {
          (globalThis as any).__EVEXA_BUILD_TIME__ = new Date().toISOString();
          (globalThis as any).__EVEXA_BUILD_HASH__ = 'dev-' + Math.random().toString(36).substr(2, 8);
          (globalThis as any).__EVEXA_PROTECTED__ = true;
        }

        // Initialize security service
        try {
          const { securityService } = await import('@/services/securityService');
          if (securityService && typeof securityService.initializeIPProtection === 'function') {
            await securityService.initializeIPProtection();
            console.log('🔒 Security service initialized');
          }
        } catch (error) {
          console.warn('Security service initialization failed:', error);
        }

        // Skip secure environment initialization to avoid validation errors
        console.log('🔐 Secure environment initialization skipped to prevent validation errors');

        console.log('✅ Security initialization complete');
      } catch (error) {
        console.error('❌ Security initialization failed:', error);
      }
    };

    initializeSecurity();
  }, []);

  return null; // This component doesn't render anything
}
