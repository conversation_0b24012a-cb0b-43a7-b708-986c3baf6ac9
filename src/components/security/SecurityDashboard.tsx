"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Activity, 
  Eye, 
  Lock,
  Users,
  Globe,
  Clock,
  TrendingUp,
  AlertCircle
} from 'lucide-react';
import { securityMonitoringService, type SecurityEvent } from '@/services/securityMonitoringService';
import { realLicenseService } from '@/services/realLicenseService';

interface SecurityMetrics {
  totalEvents: number;
  criticalEvents: number;
  blockedAttempts: number;
  suspiciousActivities: number;
  threatLevel: 'low' | 'medium' | 'high' | 'critical';
  riskScore: number;
}

export default function SecurityDashboard() {
  const [metrics, setMetrics] = useState<SecurityMetrics>({
    totalEvents: 0,
    criticalEvents: 0,
    blockedAttempts: 0,
    suspiciousActivities: 0,
    threatLevel: 'low',
    riskScore: 0
  });
  const [recentEvents, setRecentEvents] = useState<SecurityEvent[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    // Load initial data
    loadSecurityData();
    
    // Set up real-time monitoring
    startRealTimeMonitoring();
    
    // Cleanup on unmount
    return () => {
      stopRealTimeMonitoring();
    };
  }, []);

  const loadSecurityData = () => {
    // Load recent events from session storage
    if (typeof window !== 'undefined' && window.sessionStorage) {
      try {
        const events = JSON.parse(sessionStorage.getItem('recent-security-events') || '[]');
        setRecentEvents(events.slice(0, 10)); // Show last 10 events
        
        // Calculate metrics from events
        const criticalCount = events.filter((e: SecurityEvent) => e.severity === 'critical').length;
        const blockedCount = events.filter((e: SecurityEvent) => e.outcome === 'blocked').length;
        const suspiciousCount = events.filter((e: SecurityEvent) => 
          e.type === 'suspicious_activity' || e.type === 'unauthorized_access'
        ).length;
        
        // Calculate threat level based on recent events
        let threatLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';
        if (criticalCount > 0) threatLevel = 'critical';
        else if (suspiciousCount > 5) threatLevel = 'high';
        else if (blockedCount > 10) threatLevel = 'medium';
        
        // Calculate risk score (0-100)
        const riskScore = Math.min(100, (criticalCount * 20) + (suspiciousCount * 5) + (blockedCount * 2));
        
        setMetrics({
          totalEvents: events.length,
          criticalEvents: criticalCount,
          blockedAttempts: blockedCount,
          suspiciousActivities: suspiciousCount,
          threatLevel,
          riskScore
        });
      } catch (error) {
        console.warn('Failed to load security data:', error);
      }
    }
  };

  const startRealTimeMonitoring = () => {
    if (typeof window === 'undefined') return;
    
    setIsMonitoring(true);
    
    // Listen for security events
    const handleSecurityEvent = (event: CustomEvent) => {
      const securityEvent = event.detail as SecurityEvent;
      setRecentEvents(prev => [securityEvent, ...prev.slice(0, 9)]);
      setLastUpdate(new Date());
      
      // Update metrics
      loadSecurityData();
    };
    
    // Listen for critical alerts
    const handleCriticalAlert = (event: CustomEvent) => {
      const securityEvent = event.detail as SecurityEvent;
      // Show immediate alert notification
      console.warn('🚨 CRITICAL SECURITY ALERT:', securityEvent);
    };
    
    window.addEventListener('security-event', handleSecurityEvent as EventListener);
    window.addEventListener('critical-security-alert', handleCriticalAlert as EventListener);
    
    // Store cleanup functions
    (window as any).__securityEventCleanup = () => {
      window.removeEventListener('security-event', handleSecurityEvent as EventListener);
      window.removeEventListener('critical-security-alert', handleCriticalAlert as EventListener);
    };
  };

  const stopRealTimeMonitoring = () => {
    setIsMonitoring(false);
    if (typeof window !== 'undefined' && (window as any).__securityEventCleanup) {
      (window as any).__securityEventCleanup();
    }
  };

  const getThreatLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getEventSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'high': return <AlertTriangle className="w-4 h-4 text-orange-500" />;
      case 'medium': return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'low': return <CheckCircle className="w-4 h-4 text-green-500" />;
      default: return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatEventTime = (timestamp: Date) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Security Dashboard</h2>
          <p className="text-muted-foreground">Real-time security monitoring and threat detection</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={isMonitoring ? "default" : "secondary"}>
            <Activity className="w-3 h-3 mr-1" />
            {isMonitoring ? 'Monitoring Active' : 'Monitoring Inactive'}
          </Badge>
          <span className="text-sm text-muted-foreground">
            Last update: {formatEventTime(lastUpdate)}
          </span>
        </div>
      </div>

      {/* Security Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Threat Level</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${getThreatLevelColor(metrics.threatLevel)}`} />
              <div className="text-2xl font-bold capitalize">{metrics.threatLevel}</div>
            </div>
            <p className="text-xs text-muted-foreground">
              Risk Score: {metrics.riskScore}/100
            </p>
            <Progress value={metrics.riskScore} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Security Events</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalEvents}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.criticalEvents} critical events
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Blocked Attempts</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.blockedAttempts}</div>
            <p className="text-xs text-muted-foreground">
              Threats neutralized
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Suspicious Activity</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.suspiciousActivities}</div>
            <p className="text-xs text-muted-foreground">
              Under investigation
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Critical Alerts */}
      {metrics.criticalEvents > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <strong>Critical Security Alert:</strong> {metrics.criticalEvents} critical security event(s) detected. 
            Immediate attention required.
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="events" className="space-y-4">
        <TabsList>
          <TabsTrigger value="events">Recent Events</TabsTrigger>
          <TabsTrigger value="threats">Threat Analysis</TabsTrigger>
          <TabsTrigger value="monitoring">System Status</TabsTrigger>
        </TabsList>

        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Security Events</CardTitle>
              <CardDescription>
                Latest security events and system activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentEvents.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Shield className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No recent security events</p>
                  <p className="text-sm">System is secure and monitoring</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {recentEvents.map((event, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        {getEventSeverityIcon(event.severity)}
                        <div>
                          <div className="font-medium">{event.type.replace(/_/g, ' ').toUpperCase()}</div>
                          <div className="text-sm text-muted-foreground">
                            {event.source} • {event.ipAddress || 'Unknown IP'}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge variant={event.outcome === 'blocked' ? 'destructive' : 'secondary'}>
                          {event.outcome || 'detected'}
                        </Badge>
                        <div className="text-xs text-muted-foreground mt-1">
                          {formatEventTime(event.timestamp)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="threats">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8 text-muted-foreground">
                <TrendingUp className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>Threat analysis dashboard</p>
                <p className="text-sm">Advanced threat detection and analysis coming soon</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitoring">
          <Card>
            <CardHeader>
              <CardTitle>System Security Status</CardTitle>
              <CardDescription>
                Current status of security systems and monitoring
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span>IP Protection</span>
                  </div>
                  <Badge variant="default">Active</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span>Anti-Debugging</span>
                  </div>
                  <Badge variant="default">Active</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span>Real-time Monitoring</span>
                  </div>
                  <Badge variant="default">Active</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span>License Validation</span>
                  </div>
                  <Badge variant="default">Active</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
