"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { securityService } from '@/services/securityService';
import { licenseService, type LicenseValidationResult } from '@/services/licenseService';

interface SecurityContextType {
  isSecurityInitialized: boolean;
  licenseStatus: LicenseValidationResult | null;
  securityLevel: 'low' | 'medium' | 'high';
  isLicenseValid: boolean;
}

const SecurityContext = createContext<SecurityContextType>({
  isSecurityInitialized: false,
  licenseStatus: null,
  securityLevel: 'medium',
  isLicenseValid: false,
});

export const useSecurityContext = () => useContext(SecurityContext);

interface SecurityProviderProps {
  children: React.ReactNode;
  licenseKey?: string;
}

export function SecurityProvider({ children, licenseKey }: SecurityProviderProps) {
  const [isSecurityInitialized, setIsSecurityInitialized] = useState(false);
  const [licenseStatus, setLicenseStatus] = useState<LicenseValidationResult | null>(null);
  const [securityLevel] = useState<'low' | 'medium' | 'high'>('high');

  useEffect(() => {
    initializeSecurity();
  }, []);

  const initializeSecurity = async () => {
    try {
      // Initialize IP protection
      await securityService.initializeIPProtection();

      // Validate license if provided
      if (licenseKey) {
        const validation = await licenseService.validateLicense(licenseKey);
        setLicenseStatus(validation);

        if (!validation.isValid) {
          console.warn('⚠️ License validation failed:', validation.reason);
          // In production, you might want to limit functionality
        }
      }

      setIsSecurityInitialized(true);
      console.log('🔒 Security system initialized');
    } catch (error) {
      console.error('❌ Security initialization failed:', error);
      setIsSecurityInitialized(false);
    }
  };

  // Show security error if license is invalid in production
  if (process.env.NODE_ENV === 'production' && licenseStatus && !licenseStatus.isValid) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-xl p-6 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h1 className="text-xl font-bold text-gray-900 mb-2">License Validation Failed</h1>
          <p className="text-gray-600 mb-4">{licenseStatus.reason}</p>
          <p className="text-sm text-gray-500">
            Please contact support to resolve this issue.
          </p>
        </div>
      </div>
    );
  }

  // Show loading screen while security initializes
  if (!isSecurityInitialized) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Initializing Security</h2>
          <p className="text-gray-600">Please wait while we secure your session...</p>
        </div>
      </div>
    );
  }

  const contextValue: SecurityContextType = {
    isSecurityInitialized,
    licenseStatus,
    securityLevel,
    isLicenseValid: licenseStatus?.isValid ?? false,
  };

  return (
    <SecurityContext.Provider value={contextValue}>
      {children}
      {/* License warnings */}
      {licenseStatus?.warnings && licenseStatus.warnings.length > 0 && (
        <LicenseWarnings warnings={licenseStatus.warnings} />
      )}
    </SecurityContext.Provider>
  );
}

interface LicenseWarningsProps {
  warnings: string[];
}

function LicenseWarnings({ warnings }: LicenseWarningsProps) {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium text-yellow-800">License Warnings</h3>
            <div className="mt-2 text-sm text-yellow-700">
              <ul className="list-disc list-inside space-y-1">
                {warnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            </div>
          </div>
          <div className="ml-auto pl-3">
            <button
              onClick={() => setIsVisible(false)}
              className="inline-flex text-yellow-400 hover:text-yellow-600"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
