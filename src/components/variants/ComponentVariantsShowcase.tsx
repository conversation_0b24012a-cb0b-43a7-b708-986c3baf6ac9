/**
 * EVEXA Component Variants Showcase
 * Comprehensive demonstration of all component variants and states
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LoadingSpinner, LoadingOverlay, LoadingButton, ProgressLoading } from '@/components/ui/enhanced-loading';
import { Skeleton, SkeletonText, SkeletonAvatar, SkeletonCard, SkeletonTable, SkeletonDashboard } from '@/components/ui/enhanced-skeleton';
import { EmptyState, NoSearchResults, NoDataAvailable, ErrorState } from '@/components/ui/enhanced-empty-state';
import { StatusIndicator, StatusBadge, ConnectionStatus, HealthStatus } from '@/components/ui/enhanced-status';
import { usePerformanceMonitor } from '@/lib/performance-utils';
import { 
  Palette, 
  Loader2, 
  Sparkles, 
  AlertCircle, 
  CheckCircle, 
  Zap,
  Heart,
  Star,
  Bookmark,
  Share,
  Download,
  Upload,
  Settings,
  User,
  Mail,
  Phone,
  Calendar,
  Clock,
  MapPin,
  Camera,
  Mic,
  Video
} from 'lucide-react';

export const ComponentVariantsShowcase: React.FC = () => {
  usePerformanceMonitor('ComponentVariantsShowcase');

  const [isLoading, setIsLoading] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [showSkeleton, setShowSkeleton] = useState(false);

  // Simulate loading progress
  const simulateLoading = () => {
    setIsLoading(true);
    setLoadingProgress(0);
    
    const interval = setInterval(() => {
      setLoadingProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsLoading(false);
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  return (
    <div className="space-y-8 p-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold flex items-center justify-center gap-3">
          <Palette className="h-10 w-10 text-primary" />
          Component Variants & States
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Comprehensive showcase of EVEXA's enhanced component variants, states, and micro-interactions
        </p>
      </div>

      <Tabs defaultValue="buttons" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="buttons">Buttons</TabsTrigger>
          <TabsTrigger value="cards">Cards</TabsTrigger>
          <TabsTrigger value="badges">Badges</TabsTrigger>
          <TabsTrigger value="loading">Loading</TabsTrigger>
          <TabsTrigger value="empty">Empty States</TabsTrigger>
          <TabsTrigger value="status">Status</TabsTrigger>
        </TabsList>

        {/* Enhanced Buttons */}
        <TabsContent value="buttons" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Enhanced Button Variants</CardTitle>
              <CardDescription>
                Buttons with micro-interactions, states, and accessibility features
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Variants */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Variants</h3>
                <div className="flex flex-wrap gap-3">
                  <Button variant="default">Default</Button>
                  <Button variant="destructive">Destructive</Button>
                  <Button variant="outline">Outline</Button>
                  <Button variant="secondary">Secondary</Button>
                  <Button variant="ghost">Ghost</Button>
                  <Button variant="link">Link</Button>
                  <Button variant="gradient">Gradient</Button>
                  <Button variant="glass">Glass</Button>
                  <Button variant="success">Success</Button>
                  <Button variant="warning">Warning</Button>
                  <Button variant="info">Info</Button>
                  <Button variant="accent">Accent</Button>
                </div>
              </div>

              {/* Sizes */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Sizes</h3>
                <div className="flex flex-wrap items-center gap-3">
                  <Button size="xs">Extra Small</Button>
                  <Button size="sm">Small</Button>
                  <Button size="default">Default</Button>
                  <Button size="lg">Large</Button>
                  <Button size="xl">Extra Large</Button>
                  <Button size="2xl">2X Large</Button>
                </div>
              </div>

              {/* Icon Buttons */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Icon Buttons</h3>
                <div className="flex flex-wrap items-center gap-3">
                  <Button size="icon-xs" variant="outline"><Heart className="h-3 w-3" /></Button>
                  <Button size="icon-sm" variant="outline"><Star className="h-3.5 w-3.5" /></Button>
                  <Button size="icon" variant="outline"><Bookmark className="h-4 w-4" /></Button>
                  <Button size="icon-lg" variant="outline"><Share className="h-5 w-5" /></Button>
                  <Button size="icon-xl" variant="outline"><Download className="h-6 w-6" /></Button>
                </div>
              </div>

              {/* Loading Buttons */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Loading States</h3>
                <div className="flex flex-wrap gap-3">
                  <LoadingButton isLoading={isLoading} onClick={simulateLoading}>
                    {isLoading ? 'Processing...' : 'Start Process'}
                  </LoadingButton>
                  <LoadingButton isLoading={true} variant="outline">
                    Loading...
                  </LoadingButton>
                  <LoadingButton isLoading={true} variant="ghost" size="sm">
                    Saving...
                  </LoadingButton>
                </div>
              </div>

              {/* Interactive Buttons */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Interactive Examples</h3>
                <div className="flex flex-wrap gap-3">
                  <Button className="gap-2">
                    <Upload className="h-4 w-4" />
                    Upload File
                  </Button>
                  <Button variant="outline" className="gap-2">
                    <Settings className="h-4 w-4" />
                    Settings
                  </Button>
                  <Button variant="gradient" className="gap-2">
                    <Sparkles className="h-4 w-4" />
                    Premium
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Enhanced Cards */}
        <TabsContent value="cards" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card variant="default">
              <CardHeader>
                <CardTitle>Default Card</CardTitle>
                <CardDescription>Standard card with default styling</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  This is a default card with standard border and background.
                </p>
              </CardContent>
            </Card>

            <Card variant="elevated" hover="lift">
              <CardHeader>
                <CardTitle>Elevated Card</CardTitle>
                <CardDescription>Card with elevation and lift hover effect</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Hover over this card to see the lift effect.
                </p>
              </CardContent>
            </Card>

            <Card variant="glass" hover="glow">
              <CardHeader>
                <CardTitle>Glass Card</CardTitle>
                <CardDescription>Glassmorphism effect with glow hover</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Modern glass effect with backdrop blur.
                </p>
              </CardContent>
            </Card>

            <Card variant="gradient">
              <CardHeader>
                <CardTitle className="text-white">Gradient Card</CardTitle>
                <CardDescription className="text-white/80">
                  Card with gradient background
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-white/90">
                  Beautiful gradient background effect.
                </p>
              </CardContent>
            </Card>

            <Card variant="success" hover="scale">
              <CardHeader>
                <CardTitle>Success Card</CardTitle>
                <CardDescription>Success state with scale hover</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Hover to see the scale effect.
                </p>
              </CardContent>
            </Card>

            <Card variant="interactive" hover="float">
              <CardHeader>
                <CardTitle>Interactive Card</CardTitle>
                <CardDescription>Clickable card with float hover</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  This card has interactive styling.
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Enhanced Badges */}
        <TabsContent value="badges" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Enhanced Badge Variants</CardTitle>
              <CardDescription>
                Badges with animations, interactions, and various styles
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Variants */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Variants</h3>
                <div className="flex flex-wrap gap-3">
                  <Badge variant="default">Default</Badge>
                  <Badge variant="secondary">Secondary</Badge>
                  <Badge variant="destructive">Destructive</Badge>
                  <Badge variant="outline">Outline</Badge>
                  <Badge variant="success">Success</Badge>
                  <Badge variant="warning">Warning</Badge>
                  <Badge variant="info">Info</Badge>
                  <Badge variant="accent">Accent</Badge>
                  <Badge variant="ghost">Ghost</Badge>
                  <Badge variant="gradient">Gradient</Badge>
                  <Badge variant="glass">Glass</Badge>
                  <Badge variant="pulse">Pulse</Badge>
                </div>
              </div>

              {/* Sizes */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Sizes</h3>
                <div className="flex flex-wrap items-center gap-3">
                  <Badge size="xs">Extra Small</Badge>
                  <Badge size="sm">Small</Badge>
                  <Badge size="default">Default</Badge>
                  <Badge size="lg">Large</Badge>
                  <Badge size="xl">Extra Large</Badge>
                </div>
              </div>

              {/* Interactive Badges */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Interactive</h3>
                <div className="flex flex-wrap gap-3">
                  <Badge interactive variant="default">Clickable</Badge>
                  <Badge interactive variant="outline">Interactive</Badge>
                  <Badge interactive variant="success">Active</Badge>
                </div>
              </div>

              {/* Status Badges */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Status Examples</h3>
                <div className="flex flex-wrap gap-3">
                  <StatusBadge status="online" showLabel />
                  <StatusBadge status="away" showLabel />
                  <StatusBadge status="busy" showLabel />
                  <StatusBadge status="offline" showLabel />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Loading States */}
        <TabsContent value="loading" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Loading States</CardTitle>
              <CardDescription>
                Comprehensive loading indicators and skeleton states
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
              {/* Loading Spinners */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Loading Spinners</h3>
                <div className="flex flex-wrap items-center gap-6">
                  <LoadingSpinner variant="spinner" size="sm" label="Small" />
                  <LoadingSpinner variant="spinner" size="default" label="Default" />
                  <LoadingSpinner variant="spinner" size="lg" label="Large" />
                  <LoadingSpinner variant="dots" size="default" label="Dots" />
                </div>
              </div>

              {/* Progress Loading */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Progress Loading</h3>
                <div className="space-y-4 max-w-md">
                  <ProgressLoading progress={loadingProgress} label="Processing..." />
                  <ProgressLoading progress={75} variant="success" label="Upload Progress" />
                  <ProgressLoading progress={45} variant="warning" label="Sync Status" />
                </div>
              </div>

              {/* Skeleton States */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Skeleton Loading</h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <Button
                      onClick={() => setShowSkeleton(!showSkeleton)}
                      variant="outline"
                    >
                      {showSkeleton ? 'Hide' : 'Show'} Skeleton
                    </Button>
                  </div>
                  
                  {showSkeleton ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <SkeletonCard showAvatar showImage />
                      <SkeletonCard textLines={4} />
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Card>
                        <CardContent className="p-6">
                          <div className="flex items-center space-x-4 mb-4">
                            <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                              <User className="h-5 w-5 text-primary" />
                            </div>
                            <div>
                              <h4 className="font-medium">John Doe</h4>
                              <p className="text-sm text-muted-foreground">Software Engineer</p>
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            This is actual content that would replace the skeleton loading state.
                          </p>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardContent className="p-6">
                          <h4 className="font-medium mb-2">Project Update</h4>
                          <p className="text-sm text-muted-foreground mb-4">
                            The new feature implementation is progressing well. We've completed
                            the initial design phase and are now moving into development.
                          </p>
                          <div className="flex gap-2">
                            <Badge variant="success">In Progress</Badge>
                            <Badge variant="outline">High Priority</Badge>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Empty States */}
        <TabsContent value="empty" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardContent className="p-0">
                <NoSearchResults
                  searchTerm="nonexistent"
                  onClearSearch={() => console.log('Clear search')}
                  onTryAgain={() => console.log('Try again')}
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-0">
                <NoDataAvailable
                  onCreate={() => console.log('Create new')}
                  onRefresh={() => console.log('Refresh')}
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-0">
                <ErrorState
                  onRetry={() => console.log('Retry')}
                  onGoBack={() => console.log('Go back')}
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-0">
                <EmptyState
                  illustration="calendar"
                  title="No events scheduled"
                  description="You don't have any events scheduled for today."
                  action={{
                    label: "Schedule event",
                    onClick: () => console.log('Schedule'),
                    icon: <Calendar className="h-4 w-4" />
                  }}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Status Indicators */}
        <TabsContent value="status" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Status Indicators</CardTitle>
              <CardDescription>
                Various status indicators and health monitors
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Status */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Basic Status</h3>
                <div className="space-y-3">
                  <StatusIndicator variant="success" label="Operation successful" />
                  <StatusIndicator variant="warning" label="Warning: Check configuration" />
                  <StatusIndicator variant="error" label="Error: Connection failed" />
                  <StatusIndicator variant="info" label="Information: Update available" />
                  <StatusIndicator variant="pending" label="Processing..." animate />
                </div>
              </div>

              {/* Health Status */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Health Status</h3>
                <div className="space-y-3">
                  <HealthStatus status="healthy" details="All systems operational" />
                  <HealthStatus status="warning" details="High memory usage detected" />
                  <HealthStatus status="critical" details="Database connection lost" />
                  <HealthStatus status="unknown" details="Status check in progress" />
                </div>
              </div>

              {/* Connection Status */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Connection Status</h3>
                <div className="space-y-3">
                  <ConnectionStatus isConnected={true} />
                  <ConnectionStatus 
                    isConnected={false} 
                    onReconnect={() => console.log('Reconnecting...')} 
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ComponentVariantsShowcase;
