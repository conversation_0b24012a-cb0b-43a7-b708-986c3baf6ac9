/**
 * Professional Lead Widget
 * Secure lead display with proper tenant isolation
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, Users, Target, AlertCircle } from "lucide-react";
import Link from 'next/link';
import { useAuth } from '@/contexts/auth-context';
import { useTenant } from '@/contexts/tenant-context';
import { professionalDataService } from '@/services/professionalDataService';
import type { Lead } from '@/types/firestore';

interface ProfessionalLeadWidgetProps {
  compact?: boolean;
}

interface LeadStats {
  totalLeads: number;
  newLeads: number;
  qualifiedLeads: number;
  conversionRate: number;
  recentLeads: Lead[];
}

export function ProfessionalLeadWidget({ compact = false }: ProfessionalLeadWidgetProps) {
  const [leadStats, setLeadStats] = useState<LeadStats>({
    totalLeads: 0,
    newLeads: 0,
    qualifiedLeads: 0,
    conversionRate: 0,
    recentLeads: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const { user } = useAuth();
  const { tenant } = useTenant();

  useEffect(() => {
    const fetchLeadData = async () => {
      if (!user || !tenant?.id) {
        setError('Authentication or tenant context missing');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // 🔒 SECURITY: Only fetch data for current tenant
        const allLeads = await professionalDataService.leads.getByTenant(tenant.id);
        const newLeads = await professionalDataService.leads.getByStatus('New', tenant.id);
        const qualifiedLeads = await professionalDataService.leads.getByStatus('Qualified', tenant.id);

        // Calculate conversion rate
        const convertedLeads = allLeads.filter(lead => 
          lead.status === 'Closed Won' || lead.status === 'converted'
        );
        const conversionRate = allLeads.length > 0 
          ? Math.round((convertedLeads.length / allLeads.length) * 100)
          : 0;

        // Get recent leads (last 5)
        const recentLeads = allLeads
          .sort((a, b) => {
            const aDate = a.createdAt?.toDate?.() || new Date(a.createdAt);
            const bDate = b.createdAt?.toDate?.() || new Date(b.createdAt);
            return bDate.getTime() - aDate.getTime();
          })
          .slice(0, 5);

        setLeadStats({
          totalLeads: allLeads.length,
          newLeads: newLeads.length,
          qualifiedLeads: qualifiedLeads.length,
          conversionRate,
          recentLeads
        });

      } catch (error) {
        console.error('Error fetching lead data:', error);
        setError('Failed to load lead data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchLeadData();
  }, [user, tenant?.id]);

  if (isLoading) {
    return (
      <Card className="h-full">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Leads</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 rounded mb-4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="h-full">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Leads</CardTitle>
          <AlertCircle className="h-4 w-4 text-red-500" />
        </CardHeader>
        <CardContent>
          <div className="text-sm text-red-600">{error}</div>
          <Button 
            variant="outline" 
            size="sm" 
            className="mt-2"
            onClick={() => window.location.reload()}
          >
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (compact) {
    return (
      <Card className="h-full">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Leads</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{leadStats.totalLeads}</div>
          <p className="text-xs text-muted-foreground">
            {leadStats.newLeads} new this week
          </p>
          <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-2">
            <Badge variant="secondary">{leadStats.conversionRate}% conversion</Badge>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Lead Management</CardTitle>
        <Users className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Stats Grid */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{leadStats.totalLeads}</div>
              <div className="text-xs text-muted-foreground">Total Leads</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{leadStats.newLeads}</div>
              <div className="text-xs text-muted-foreground">New Leads</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{leadStats.qualifiedLeads}</div>
              <div className="text-xs text-muted-foreground">Qualified</div>
            </div>
          </div>

          {/* Conversion Rate */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Conversion Rate</span>
            <div className="flex items-center space-x-1">
              <TrendingUp className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium">{leadStats.conversionRate}%</span>
            </div>
          </div>

          {/* Recent Leads */}
          {leadStats.recentLeads.length > 0 && (
            <div className="space-y-2">
              <div className="text-sm font-medium">Recent Leads</div>
              <div className="space-y-1">
                {leadStats.recentLeads.slice(0, 3).map((lead) => (
                  <div key={lead.id} className="flex items-center justify-between text-xs">
                    <span className="truncate">{lead.fullName || lead.firstName + ' ' + lead.lastName}</span>
                    <Badge variant="outline" className="text-xs">
                      {lead.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Action Button */}
          <Link href="/leads">
            <Button variant="outline" size="sm" className="w-full">
              <Target className="h-4 w-4 mr-2" />
              View All Leads
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
