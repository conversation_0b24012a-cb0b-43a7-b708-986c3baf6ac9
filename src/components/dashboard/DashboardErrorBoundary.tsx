/**
 * EVEXA Dashboard Error Boundary
 * Specialized error boundary for dashboard components with Firebase error handling
 */

'use client';

import React from 'react';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  RefreshCw, 
  Database, 
  Wifi, 
  Settings,
  ExternalLink,
  Bug
} from 'lucide-react';
import { reportCollectionError } from '@/lib/firebase-validation';

interface DashboardErrorInfo {
  componentStack: string;
  errorBoundary?: string;
  errorBoundaryStack?: string;
}

interface DashboardErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
  errorInfo?: DashboardErrorInfo;
}

// Firebase-specific error detection
function isFirebaseError(error: Error): boolean {
  return error.message.includes('FirebaseError') || 
         error.message.includes('collection() cannot be called with an empty path') ||
         error.message.includes('Invalid collection reference') ||
         error.name === 'FirebaseError';
}

function isCollectionPathError(error: Error): boolean {
  return error.message.includes('collection() cannot be called with an empty path') ||
         error.message.includes('Invalid collection reference');
}

function isNetworkError(error: Error): boolean {
  return error.message.includes('network') ||
         error.message.includes('offline') ||
         error.message.includes('connection') ||
         error.message.includes('timeout');
}

// Dashboard-specific error fallback component
export const DashboardErrorFallback: React.FC<DashboardErrorFallbackProps> = ({
  error,
  resetErrorBoundary,
  errorInfo,
}) => {
  const [showDetails, setShowDetails] = React.useState(false);

  // Determine error type and provide specific guidance
  const errorType = React.useMemo(() => {
    if (isCollectionPathError(error)) {
      return {
        type: 'collection-path',
        title: 'Database Configuration Error',
        description: 'There\'s an issue with the database collection configuration.',
        icon: Database,
        color: 'text-orange-600 bg-orange-100',
        suggestions: [
          'Check if all required collections are properly configured',
          'Verify that collection names are not empty or undefined',
          'Ensure proper tenant isolation is set up'
        ]
      };
    }
    
    if (isFirebaseError(error)) {
      return {
        type: 'firebase',
        title: 'Database Connection Error',
        description: 'Unable to connect to the database.',
        icon: Database,
        color: 'text-red-600 bg-red-100',
        suggestions: [
          'Check your internet connection',
          'Verify Firebase configuration',
          'Try refreshing the page'
        ]
      };
    }
    
    if (isNetworkError(error)) {
      return {
        type: 'network',
        title: 'Network Connection Error',
        description: 'Unable to connect to the server.',
        icon: Wifi,
        color: 'text-blue-600 bg-blue-100',
        suggestions: [
          'Check your internet connection',
          'Try refreshing the page',
          'Contact support if the issue persists'
        ]
      };
    }
    
    return {
      type: 'general',
      title: 'Dashboard Error',
      description: 'An unexpected error occurred while loading the dashboard.',
      icon: AlertTriangle,
      color: 'text-red-600 bg-red-100',
      suggestions: [
        'Try refreshing the page',
        'Clear your browser cache',
        'Contact support if the issue persists'
      ]
    };
  }, [error]);

  // Report error for monitoring
  React.useEffect(() => {
    reportCollectionError(error, {
      component: 'DashboardErrorBoundary',
      operation: 'error-boundary-catch'
    });
  }, [error]);

  const IconComponent = errorType.icon;

  return (
    <div className="min-h-[400px] flex items-center justify-center p-6">
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-full ${errorType.color}`}>
              <IconComponent className="h-6 w-6" />
            </div>
            <div>
              <CardTitle className="text-red-600">{errorType.title}</CardTitle>
              <CardDescription>{errorType.description}</CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Error Summary */}
          <Alert variant="destructive">
            <Bug className="h-4 w-4" />
            <AlertDescription>
              <strong>Error:</strong> {error.message}
            </AlertDescription>
          </Alert>

          {/* Suggestions */}
          <div className="space-y-2">
            <h4 className="font-medium">What you can try:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              {errorType.suggestions.map((suggestion, index) => (
                <li key={index}>{suggestion}</li>
              ))}
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button onClick={resetErrorBoundary} className="gap-2">
              <RefreshCw className="h-4 w-4" />
              Try Again
            </Button>
            
            <Button 
              variant="outline" 
              onClick={() => window.location.reload()}
              className="gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh Page
            </Button>

            <Button 
              variant="outline" 
              onClick={() => window.location.href = '/dashboard'}
              className="gap-2"
            >
              <Settings className="h-4 w-4" />
              Go to Dashboard
            </Button>

            {process.env.NODE_ENV === 'development' && (
              <Button 
                variant="ghost" 
                onClick={() => setShowDetails(!showDetails)}
                className="gap-2"
              >
                <Bug className="h-4 w-4" />
                {showDetails ? 'Hide' : 'Show'} Details
              </Button>
            )}
          </div>

          {/* Development Details */}
          {process.env.NODE_ENV === 'development' && showDetails && (
            <div className="border-t pt-4 space-y-4">
              <div>
                <h4 className="font-medium mb-2">Error Details</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Error Type:</strong>
                    <Badge variant="outline" className="ml-2">{errorType.type}</Badge>
                  </div>
                  <div>
                    <strong>Timestamp:</strong>
                    <span className="ml-2 font-mono">{new Date().toISOString()}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Stack Trace</h4>
                <pre className="text-xs bg-muted p-3 rounded-lg overflow-auto max-h-40">
                  {error.stack}
                </pre>
              </div>

              {errorInfo?.componentStack && (
                <div className="space-y-2">
                  <h4 className="font-medium">Component Stack</h4>
                  <pre className="text-xs bg-muted p-3 rounded-lg overflow-auto max-h-40">
                    {errorInfo.componentStack}
                  </pre>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// Dashboard Error Boundary wrapper
export interface DashboardErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<DashboardErrorFallbackProps>;
  onError?: (error: Error, errorInfo: DashboardErrorInfo) => void;
}

export const DashboardErrorBoundary: React.FC<DashboardErrorBoundaryProps> = ({
  children,
  fallback: FallbackComponent = DashboardErrorFallback,
  onError,
}) => {
  const handleError = React.useCallback((error: Error, errorInfo: DashboardErrorInfo) => {
    // Enhanced logging for dashboard errors
    console.error('Dashboard Error Boundary caught an error:', {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      errorInfo,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      isFirebaseError: isFirebaseError(error),
      isCollectionPathError: isCollectionPathError(error),
      isNetworkError: isNetworkError(error),
    });

    // Report to error monitoring
    reportCollectionError(error, {
      component: 'DashboardErrorBoundary',
      operation: 'error-boundary-catch'
    });

    // Call custom error handler
    onError?.(error, errorInfo);
  }, [onError]);

  return (
    <ErrorBoundary
      FallbackComponent={FallbackComponent}
      onError={handleError}
    >
      {children}
    </ErrorBoundary>
  );
};

export default DashboardErrorBoundary;
