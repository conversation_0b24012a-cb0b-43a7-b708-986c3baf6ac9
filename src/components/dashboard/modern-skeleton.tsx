"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface ModernSkeletonProps {
  className?: string;
  variant?: 'default' | 'shimmer' | 'pulse' | 'wave';
  lines?: number;
  showAvatar?: boolean;
  showChart?: boolean;
  showStats?: boolean;
}

export function ModernSkeleton({
  className,
  variant = 'shimmer',
  lines = 3,
  showAvatar = false,
  showChart = false,
  showStats = false
}: ModernSkeletonProps) {
  const getVariantAnimation = () => {
    switch (variant) {
      case 'shimmer':
        return {
          background: [
            "linear-gradient(90deg, hsl(var(--muted)) 0%, hsl(var(--muted))/50% 50%, hsl(var(--muted)) 100%)",
            "linear-gradient(90deg, hsl(var(--muted))/50% 0%, hsl(var(--muted)) 50%, hsl(var(--muted))/50% 100%)",
            "linear-gradient(90deg, hsl(var(--muted)) 0%, hsl(var(--muted))/50% 50%, hsl(var(--muted)) 100%)"
          ],
          transition: {
            duration: 2,
            repeat: Infinity,
            ease: "linear"
          }
        };
      case 'pulse':
        return {
          opacity: [0.5, 1, 0.5],
          transition: {
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut"
          }
        };
      case 'wave':
        return {
          y: [0, -2, 0],
          transition: {
            duration: 1,
            repeat: Infinity,
            ease: "easeInOut"
          }
        };
      default:
        return {};
    }
  };

  const SkeletonLine = ({ width = "100%", height = "1rem", delay = 0 }) => (
    <motion.div
      className="rounded bg-muted"
      style={{ width, height }}
      animate={getVariantAnimation()}
      transition={{ ...getVariantAnimation().transition, delay }}
    />
  );

  if (showStats) {
    return (
      <div className={cn("space-y-4", className)}>
        {/* Stats grid skeleton */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={i}
              className="space-y-2"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: i * 0.1 }}
            >
              <SkeletonLine width="60%" height="0.75rem" />
              <SkeletonLine width="80%" height="2rem" />
              <SkeletonLine width="40%" height="0.75rem" />
            </motion.div>
          ))}
        </div>
        
        {/* Additional content lines */}
        <div className="space-y-2">
          {[...Array(lines)].map((_, i) => (
            <SkeletonLine
              key={i}
              width={i === lines - 1 ? "75%" : "100%"}
              delay={i * 0.1}
            />
          ))}
        </div>
      </div>
    );
  }

  if (showChart) {
    return (
      <div className={cn("space-y-4", className)}>
        {/* Chart header */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <SkeletonLine width="120px" height="1.25rem" />
            <SkeletonLine width="200px" height="0.875rem" />
          </div>
          <SkeletonLine width="80px" height="2rem" />
        </div>
        
        {/* Chart area */}
        <div className="relative h-64 bg-muted/30 rounded-lg overflow-hidden">
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-muted/50 to-transparent"
            animate={{
              x: ["-100%", "100%"],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "linear"
            }}
          />
          
          {/* Chart bars simulation */}
          <div className="absolute bottom-4 left-4 right-4 flex items-end justify-between gap-2">
            {[...Array(8)].map((_, i) => (
              <motion.div
                key={i}
                className="bg-muted rounded-t"
                style={{
                  width: "100%",
                  height: `${Math.random() * 60 + 20}%`
                }}
                initial={{ height: 0 }}
                animate={{ height: `${Math.random() * 60 + 20}%` }}
                transition={{ delay: i * 0.1, duration: 0.5 }}
              />
            ))}
          </div>
        </div>
        
        {/* Chart legend */}
        <div className="flex items-center gap-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center gap-2">
              <SkeletonLine width="12px" height="12px" />
              <SkeletonLine width="60px" height="0.875rem" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-3", className)}>
      {showAvatar && (
        <div className="flex items-center gap-3">
          <motion.div
            className="w-10 h-10 rounded-full bg-muted"
            animate={getVariantAnimation()}
          />
          <div className="space-y-2 flex-1">
            <SkeletonLine width="120px" height="1rem" />
            <SkeletonLine width="80px" height="0.75rem" />
          </div>
        </div>
      )}
      
      <div className="space-y-2">
        {[...Array(lines)].map((_, i) => (
          <SkeletonLine
            key={i}
            width={
              i === 0 ? "100%" :
              i === lines - 1 ? "75%" :
              `${85 + Math.random() * 15}%`
            }
            delay={i * 0.1}
          />
        ))}
      </div>
    </div>
  );
}

// Specialized skeleton components
export function WidgetSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("p-6 space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <ModernSkeleton className="w-5 h-5" />
          <ModernSkeleton className="w-32 h-5" />
        </div>
        <ModernSkeleton className="w-8 h-8 rounded" />
      </div>
      
      {/* Content */}
      <ModernSkeleton showStats />
    </div>
  );
}

export function ChartSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("p-6", className)}>
      <ModernSkeleton showChart />
    </div>
  );
}

export function ListSkeleton({ 
  items = 5, 
  showAvatar = true, 
  className 
}: { 
  items?: number; 
  showAvatar?: boolean; 
  className?: string; 
}) {
  return (
    <div className={cn("space-y-4", className)}>
      {[...Array(items)].map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: i * 0.1 }}
        >
          <ModernSkeleton showAvatar={showAvatar} lines={2} />
        </motion.div>
      ))}
    </div>
  );
}

export function TableSkeleton({ 
  rows = 5, 
  columns = 4, 
  className 
}: { 
  rows?: number; 
  columns?: number; 
  className?: string; 
}) {
  return (
    <div className={cn("space-y-4", className)}>
      {/* Table header */}
      <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {[...Array(columns)].map((_, i) => (
          <ModernSkeleton key={i} className="h-4" />
        ))}
      </div>
      
      {/* Table rows */}
      <div className="space-y-3">
        {[...Array(rows)].map((_, rowIndex) => (
          <motion.div
            key={rowIndex}
            className="grid gap-4"
            style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: rowIndex * 0.1 }}
          >
            {[...Array(columns)].map((_, colIndex) => (
              <ModernSkeleton
                key={colIndex}
                className="h-4"
                variant={colIndex === 0 ? "shimmer" : "pulse"}
              />
            ))}
          </motion.div>
        ))}
      </div>
    </div>
  );
}
