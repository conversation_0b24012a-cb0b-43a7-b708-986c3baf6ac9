"use client";

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MoreHorizontal, 
  Maximize2, 
  Minimize2, 
  RefreshCw, 
  Settings,
  TrendingUp,
  TrendingDown,
  Minus,
  Eye,
  EyeOff
} from 'lucide-react';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator 
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

interface ModernWidgetWrapperProps {
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  isLoading?: boolean;
  error?: string | null;
  className?: string;
  variant?: 'default' | 'glass' | 'gradient' | 'minimal';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showActions?: boolean;
  showTrend?: boolean;
  trendValue?: number;
  trendLabel?: string;
  badge?: {
    text: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  };
  onRefresh?: () => void;
  onExpand?: () => void;
  onSettings?: () => void;
  onToggleVisibility?: () => void;
  isExpanded?: boolean;
  isVisible?: boolean;
}

export function ModernWidgetWrapper({
  title,
  subtitle,
  icon,
  children,
  isLoading = false,
  error = null,
  className,
  variant = 'glass',
  size = 'md',
  showActions = true,
  showTrend = false,
  trendValue,
  trendLabel,
  badge,
  onRefresh,
  onExpand,
  onSettings,
  onToggleVisibility,
  isExpanded = false,
  isVisible = true
}: ModernWidgetWrapperProps) {
  const [isHovered, setIsHovered] = React.useState(false);
  const [isRefreshing, setIsRefreshing] = React.useState(false);

  const handleRefresh = async () => {
    if (onRefresh && !isRefreshing) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setTimeout(() => setIsRefreshing(false), 1000); // Minimum animation time
      }
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'glass':
        return "bg-background/80 backdrop-blur-sm border-border/50 shadow-lg hover:shadow-xl";
      case 'gradient':
        return "bg-gradient-to-br from-primary/5 to-accent/5 border-primary/20 shadow-md hover:shadow-lg";
      case 'minimal':
        return "bg-background border-border shadow-sm hover:shadow-md";
      default:
        return "bg-card border-border shadow-sm hover:shadow-md";
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return "p-3";
      case 'lg':
        return "p-8";
      case 'xl':
        return "p-10";
      default:
        return "p-6";
    }
  };

  const getTrendIcon = () => {
    if (!trendValue) return <Minus className="h-3 w-3" />;
    return trendValue > 0 ? 
      <TrendingUp className="h-3 w-3 text-green-600" /> : 
      <TrendingDown className="h-3 w-3 text-red-600" />;
  };

  const getTrendColor = () => {
    if (!trendValue) return "text-muted-foreground";
    return trendValue > 0 ? "text-green-600" : "text-red-600";
  };

  if (!isVisible) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ 
        opacity: 1, 
        y: 0, 
        scale: 1,
        rotateX: isHovered ? 2 : 0,
        rotateY: isHovered ? 2 : 0,
      }}
      exit={{ opacity: 0, y: -20, scale: 0.95 }}
      transition={{ 
        duration: 0.3, 
        type: "spring", 
        stiffness: 300, 
        damping: 30 
      }}
      whileHover={{ 
        y: -4,
        transition: { duration: 0.2 }
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={cn("group relative", className)}
      style={{ perspective: "1000px" }}
    >
      <Card className={cn(
        "relative overflow-hidden transition-all duration-300",
        getVariantStyles(),
        isExpanded && "col-span-full row-span-2",
        error && "border-destructive/50 bg-destructive/5"
      )}>
        {/* Glass morphism overlay */}
        {variant === 'glass' && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            initial={false}
            animate={{ opacity: isHovered ? 0.1 : 0 }}
          />
        )}

        {/* Gradient animation for gradient variant */}
        {variant === 'gradient' && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-primary/10 via-accent/5 to-secondary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
            animate={{
              background: isHovered ? [
                "linear-gradient(135deg, hsl(var(--primary))/10%, hsl(var(--accent))/5%, hsl(var(--secondary))/10%)",
                "linear-gradient(135deg, hsl(var(--accent))/10%, hsl(var(--secondary))/5%, hsl(var(--primary))/10%)",
                "linear-gradient(135deg, hsl(var(--primary))/10%, hsl(var(--accent))/5%, hsl(var(--secondary))/10%)"
              ] : undefined
            }}
            transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
          />
        )}

        <CardHeader className={cn("relative z-10", getSizeStyles(), "pb-2")}>
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3 min-w-0 flex-1">
              {icon && (
                <motion.div
                  animate={{ 
                    scale: isHovered ? 1.1 : 1,
                    rotate: isHovered ? 5 : 0 
                  }}
                  transition={{ duration: 0.2 }}
                  className="flex-shrink-0 mt-1"
                >
                  {icon}
                </motion.div>
              )}
              <div className="min-w-0 flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <CardTitle className="text-base font-semibold truncate">
                    {title}
                  </CardTitle>
                  {badge && (
                    <Badge variant={badge.variant} className="text-xs">
                      {badge.text}
                    </Badge>
                  )}
                </div>
                {subtitle && (
                  <p className="text-sm text-muted-foreground truncate">
                    {subtitle}
                  </p>
                )}
                {showTrend && trendValue !== undefined && (
                  <div className={cn("flex items-center gap-1 text-xs mt-1", getTrendColor())}>
                    {getTrendIcon()}
                    <span>{Math.abs(trendValue)}%</span>
                    {trendLabel && <span className="text-muted-foreground">• {trendLabel}</span>}
                  </div>
                )}
              </div>
            </div>

            {/* Action buttons */}
            {showActions && (
              <motion.div
                initial={{ opacity: 0, x: 10 }}
                animate={{ opacity: isHovered ? 1 : 0, x: isHovered ? 0 : 10 }}
                transition={{ duration: 0.2 }}
                className="flex items-center gap-1"
              >
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 opacity-60 hover:opacity-100"
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    {onRefresh && (
                      <DropdownMenuItem onClick={handleRefresh} disabled={isRefreshing}>
                        <RefreshCw className={cn("mr-2 h-4 w-4", isRefreshing && "animate-spin")} />
                        Refresh Data
                      </DropdownMenuItem>
                    )}
                    {onExpand && (
                      <DropdownMenuItem onClick={onExpand}>
                        {isExpanded ? (
                          <>
                            <Minimize2 className="mr-2 h-4 w-4" />
                            Minimize
                          </>
                        ) : (
                          <>
                            <Maximize2 className="mr-2 h-4 w-4" />
                            Expand
                          </>
                        )}
                      </DropdownMenuItem>
                    )}
                    {onSettings && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={onSettings}>
                          <Settings className="mr-2 h-4 w-4" />
                          Settings
                        </DropdownMenuItem>
                      </>
                    )}
                    {onToggleVisibility && (
                      <DropdownMenuItem onClick={onToggleVisibility}>
                        {isVisible ? (
                          <>
                            <EyeOff className="mr-2 h-4 w-4" />
                            Hide Widget
                          </>
                        ) : (
                          <>
                            <Eye className="mr-2 h-4 w-4" />
                            Show Widget
                          </>
                        )}
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </motion.div>
            )}
          </div>
        </CardHeader>

        <CardContent className={cn("relative z-10", getSizeStyles(), "pt-0")}>
          <AnimatePresence mode="wait">
            {error ? (
              <motion.div
                key="error"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="text-center py-8 text-destructive"
              >
                <p className="font-medium">Error loading widget</p>
                <p className="text-sm text-muted-foreground mt-1">{error}</p>
                {onRefresh && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRefresh}
                    className="mt-3"
                    disabled={isRefreshing}
                  >
                    <RefreshCw className={cn("mr-2 h-4 w-4", isRefreshing && "animate-spin")} />
                    Try Again
                  </Button>
                )}
              </motion.div>
            ) : isLoading ? (
              <motion.div
                key="loading"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="space-y-3"
              >
                {/* Modern skeleton loading */}
                <div className="space-y-2">
                  <div className="h-4 bg-gradient-to-r from-muted via-muted/50 to-muted rounded animate-pulse" />
                  <div className="h-4 bg-gradient-to-r from-muted via-muted/50 to-muted rounded animate-pulse w-3/4" />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="h-8 bg-gradient-to-r from-muted via-muted/50 to-muted rounded animate-pulse" />
                  <div className="h-8 bg-gradient-to-r from-muted via-muted/50 to-muted rounded animate-pulse" />
                </div>
              </motion.div>
            ) : (
              <motion.div
                key="content"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
              >
                {children}
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>

        {/* Hover glow effect */}
        <motion.div
          className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
          style={{
            background: "radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%), hsl(var(--primary))/0.1, transparent 50%)",
          }}
          animate={{ opacity: isHovered ? 0.1 : 0 }}
        />
      </Card>
    </motion.div>
  );
}
