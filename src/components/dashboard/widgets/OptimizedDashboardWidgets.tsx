"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { 
  Calendar,
  Users,
  Target,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Activity,
  CheckCircle,
  Clock,
  AlertTriangle,
  RefreshCw,
  Eye,
  BarChart3
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import {
  getOptimizedDashboardData,
  getExhibitionsWithCounts,
  getTasksWithEmbeddedData,
  getLeadsWithEmbeddedData,
  getFinancialSummary
} from '@/services/optimizedFirestoreService';
import { cn } from '@/lib/utils';
import type { Exhibition, Task, Lead } from '@/types/firestore';

// ===== INTERFACES =====

interface WidgetProps {
  className?: string;
  refreshInterval?: number;
  showHeader?: boolean;
  compact?: boolean;
}

interface DashboardCounts {
  totalExhibitions: number;
  totalTasks: number;
  totalLeads: number;
  totalEvents: number;
}

// ===== OPTIMIZED OVERVIEW WIDGET =====

export function OptimizedOverviewWidget({ 
  className, 
  refreshInterval = 5 * 60 * 1000, // 5 minutes
  showHeader = true,
  compact = false 
}: WidgetProps) {
  const { user, tenant } = useAuth();
  const { toast } = useToast();
  const [counts, setCounts] = useState<DashboardCounts | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  const loadData = useCallback(async (forceRefresh = false) => {
    if (!tenant?.id) return;

    try {
      const data = await getOptimizedDashboardData(tenant.id, {
        includeCounts: true
      }, forceRefresh);

      if (data.counts) {
        setCounts(data.counts);
        setLastRefresh(new Date());
      }
    } catch (error) {
      console.error('Error loading overview data:', error);
      toast({
        title: "Error Loading Overview",
        description: "Could not load dashboard overview. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [tenant?.id, toast]);

  // Auto-refresh
  useEffect(() => {
    loadData();
    
    if (refreshInterval > 0) {
      const interval = setInterval(() => loadData(false), refreshInterval);
      return () => clearInterval(interval);
    }
  }, [loadData, refreshInterval]);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <Skeleton key={i} className="h-16" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Overview</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => loadData(true)}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
          <CardDescription>
            Last updated: {lastRefresh.toLocaleTimeString()}
          </CardDescription>
        </CardHeader>
      )}

      <CardContent>
        <div className={cn("grid gap-4", compact ? "grid-cols-2" : "grid-cols-2 lg:grid-cols-4")}>
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <Calendar className="h-6 w-6 mx-auto mb-2 text-blue-500" />
            <div className="text-2xl font-bold text-blue-700">
              {counts?.totalExhibitions || 0}
            </div>
            <div className="text-xs text-blue-600">Exhibitions</div>
          </div>
          
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <Target className="h-6 w-6 mx-auto mb-2 text-green-500" />
            <div className="text-2xl font-bold text-green-700">
              {counts?.totalTasks || 0}
            </div>
            <div className="text-xs text-green-600">Tasks</div>
          </div>
          
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <Users className="h-6 w-6 mx-auto mb-2 text-purple-500" />
            <div className="text-2xl font-bold text-purple-700">
              {counts?.totalLeads || 0}
            </div>
            <div className="text-xs text-purple-600">Leads</div>
          </div>
          
          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <Activity className="h-6 w-6 mx-auto mb-2 text-orange-500" />
            <div className="text-2xl font-bold text-orange-700">
              {counts?.totalEvents || 0}
            </div>
            <div className="text-xs text-orange-600">Events</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// ===== OPTIMIZED UPCOMING EXHIBITIONS WIDGET =====

export function OptimizedUpcomingExhibitionsWidget({ 
  className, 
  refreshInterval = 10 * 60 * 1000, // 10 minutes
  showHeader = true,
  compact = false 
}: WidgetProps) {
  const { user, tenant } = useAuth();
  const { toast } = useToast();
  const [exhibitions, setExhibitions] = useState<Exhibition[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const loadData = useCallback(async (forceRefresh = false) => {
    if (!tenant?.id) return;

    try {
      const data = await getOptimizedDashboardData(tenant.id, {
        includeUpcoming: true,
        limit: compact ? 3 : 5
      }, forceRefresh);

      if (data.upcomingExhibitions) {
        setExhibitions(data.upcomingExhibitions);
      }
    } catch (error) {
      console.error('Error loading upcoming exhibitions:', error);
      toast({
        title: "Error Loading Exhibitions",
        description: "Could not load upcoming exhibitions.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [tenant?.id, compact, toast]);

  useEffect(() => {
    loadData();
    
    if (refreshInterval > 0) {
      const interval = setInterval(() => loadData(false), refreshInterval);
      return () => clearInterval(interval);
    }
  }, [loadData, refreshInterval]);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <Skeleton className="h-6 w-40" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <Skeleton key={i} className="h-16" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Upcoming Exhibitions</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => loadData(true)}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
      )}

      <CardContent>
        {exhibitions.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No upcoming exhibitions</p>
          </div>
        ) : (
          <div className="space-y-3">
            {exhibitions.map((exhibition) => (
              <div key={exhibition.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-sm">{exhibition.name}</h4>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">
                      {new Date(exhibition.startDate as any).toLocaleDateString()}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {exhibition.status}
                    </Badge>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">
                    ${(exhibition.estimatedBudget || 0).toLocaleString()}
                  </div>
                  <div className="text-xs text-muted-foreground">Budget</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// ===== OPTIMIZED RECENT TASKS WIDGET =====

export function OptimizedRecentTasksWidget({ 
  className, 
  refreshInterval = 3 * 60 * 1000, // 3 minutes
  showHeader = true,
  compact = false 
}: WidgetProps) {
  const { user, tenant } = useAuth();
  const { toast } = useToast();
  const [tasks, setTasks] = useState<(Task & { exhibitionName?: string; assigneeName?: string })[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const loadData = useCallback(async (forceRefresh = false) => {
    if (!tenant?.id) return;

    try {
      const result = await getTasksWithEmbeddedData(tenant.id, {
        limit: compact ? 5 : 10
      }, forceRefresh);

      setTasks(result.items);
    } catch (error) {
      console.error('Error loading recent tasks:', error);
      toast({
        title: "Error Loading Tasks",
        description: "Could not load recent tasks.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [tenant?.id, compact, toast]);

  useEffect(() => {
    loadData();
    
    if (refreshInterval > 0) {
      const interval = setInterval(() => loadData(false), refreshInterval);
      return () => clearInterval(interval);
    }
  }, [loadData, refreshInterval]);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i} className="h-12" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Recent Tasks</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => loadData(true)}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
      )}

      <CardContent>
        {tasks.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Target className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No recent tasks</p>
          </div>
        ) : (
          <div className="space-y-3">
            {tasks.map((task) => (
              <div key={task.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-sm">{task.title}</h4>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-xs text-muted-foreground">
                      {task.exhibitionName || 'Unknown Exhibition'}
                    </span>
                    <Badge 
                      variant={task.status === 'Completed' ? 'default' : 'secondary'} 
                      className="text-xs"
                    >
                      {task.status}
                    </Badge>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-xs text-muted-foreground">
                    Due: {task.dueDate ? new Date(task.dueDate as any).toLocaleDateString() : 'No date'}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
