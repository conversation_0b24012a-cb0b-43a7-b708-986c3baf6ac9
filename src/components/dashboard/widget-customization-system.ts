/**
 * Widget Customization System
 * Provides types and utilities for dashboard widget customization
 */

export interface WidgetConfig {
  id: string;
  name: string;
  type: string;
  enabled: boolean;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  settings: Record<string, any>;
}

export interface DashboardLayout {
  id: string;
  name: string;
  widgets: WidgetConfig[];
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface WidgetType {
  id: string;
  name: string;
  description: string;
  component: React.ComponentType<any>;
  defaultSettings: Record<string, any>;
  configSchema: Record<string, any>;
}

export interface CustomizationOptions {
  allowResize: boolean;
  allowMove: boolean;
  allowRemove: boolean;
  allowAdd: boolean;
  gridSize: number;
  maxWidgets: number;
}

// Widget customization utilities
export class WidgetCustomizationSystem {
  private layouts: Map<string, DashboardLayout> = new Map();
  private widgetTypes: Map<string, WidgetType> = new Map();

  registerWidgetType(widgetType: WidgetType): void {
    this.widgetTypes.set(widgetType.id, widgetType);
  }

  getWidgetTypes(): WidgetType[] {
    return Array.from(this.widgetTypes.values());
  }

  createLayout(name: string, widgets: WidgetConfig[] = []): DashboardLayout {
    const layout: DashboardLayout = {
      id: `layout_${Date.now()}`,
      name,
      widgets,
      isDefault: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    this.layouts.set(layout.id, layout);
    return layout;
  }

  updateLayout(layoutId: string, updates: Partial<DashboardLayout>): DashboardLayout | null {
    const layout = this.layouts.get(layoutId);
    if (!layout) return null;

    const updatedLayout = {
      ...layout,
      ...updates,
      updatedAt: new Date()
    };

    this.layouts.set(layoutId, updatedLayout);
    return updatedLayout;
  }

  getLayout(layoutId: string): DashboardLayout | null {
    return this.layouts.get(layoutId) || null;
  }

  getAllLayouts(): DashboardLayout[] {
    return Array.from(this.layouts.values());
  }

  deleteLayout(layoutId: string): boolean {
    return this.layouts.delete(layoutId);
  }

  addWidgetToLayout(layoutId: string, widget: WidgetConfig): boolean {
    const layout = this.layouts.get(layoutId);
    if (!layout) return false;

    layout.widgets.push(widget);
    layout.updatedAt = new Date();
    return true;
  }

  removeWidgetFromLayout(layoutId: string, widgetId: string): boolean {
    const layout = this.layouts.get(layoutId);
    if (!layout) return false;

    const index = layout.widgets.findIndex(w => w.id === widgetId);
    if (index === -1) return false;

    layout.widgets.splice(index, 1);
    layout.updatedAt = new Date();
    return true;
  }

  updateWidgetInLayout(layoutId: string, widgetId: string, updates: Partial<WidgetConfig>): boolean {
    const layout = this.layouts.get(layoutId);
    if (!layout) return false;

    const widget = layout.widgets.find(w => w.id === widgetId);
    if (!widget) return false;

    Object.assign(widget, updates);
    layout.updatedAt = new Date();
    return true;
  }
}

// Default widget customization system instance
export const widgetCustomizationSystem = new WidgetCustomizationSystem();

// Export types for external use
export type {
  WidgetConfig,
  DashboardLayout,
  WidgetType,
  CustomizationOptions
};
