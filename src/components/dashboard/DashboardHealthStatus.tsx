/**
 * EVEXA Dashboard Health Status Component
 * Real-time monitoring of Firebase collection health
 */

'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  RefreshCw, 
  Database,
  Activity,
  Clock,
  TrendingUp
} from 'lucide-react';
import { useFirebaseHealth, type FirebaseHealthReport } from '@/lib/firebase-health-check';
import { cn } from '@/lib/utils';

interface DashboardHealthStatusProps {
  className?: string;
  compact?: boolean;
  showDetails?: boolean;
}

export const DashboardHealthStatus: React.FC<DashboardHealthStatusProps> = ({
  className,
  compact = false,
  showDetails = false
}) => {
  const { healthReport, isLoading, performHealthCheck } = useFirebaseHealth();

  const getHealthColor = (status: 'healthy' | 'warning' | 'critical') => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-100';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      case 'critical':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getHealthIcon = (status: 'healthy' | 'warning' | 'critical') => {
    switch (status) {
      case 'healthy':
        return CheckCircle;
      case 'warning':
        return AlertTriangle;
      case 'critical':
        return XCircle;
      default:
        return Database;
    }
  };

  const getHealthPercentage = (report: FirebaseHealthReport) => {
    return Math.round((report.healthyCollections / report.totalCollections) * 100);
  };

  if (isLoading && !healthReport) {
    return (
      <Card className={cn("animate-pulse", className)}>
        <CardHeader className="pb-3">
          <div className="flex items-center gap-2">
            <div className="h-5 w-5 bg-muted rounded-full" />
            <div className="h-4 w-24 bg-muted rounded" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="h-2 bg-muted rounded w-full" />
            <div className="h-3 bg-muted rounded w-1/2" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!healthReport) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center space-y-2">
            <Database className="h-8 w-8 mx-auto text-muted-foreground" />
            <p className="text-sm text-muted-foreground">Health check unavailable</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const HealthIcon = getHealthIcon(healthReport.overall);
  const healthPercentage = getHealthPercentage(healthReport);

  if (compact) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className={cn("p-1 rounded-full", getHealthColor(healthReport.overall))}>
          <HealthIcon className="h-3 w-3" />
        </div>
        <span className="text-xs text-muted-foreground">
          {healthPercentage}% healthy
        </span>
        <Button
          variant="ghost"
          size="sm"
          onClick={performHealthCheck}
          disabled={isLoading}
          className="h-6 w-6 p-0"
        >
          <RefreshCw className={cn("h-3 w-3", isLoading && "animate-spin")} />
        </Button>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={cn("p-2 rounded-full", getHealthColor(healthReport.overall))}>
              <HealthIcon className="h-4 w-4" />
            </div>
            <div>
              <CardTitle className="text-sm">System Health</CardTitle>
              <CardDescription className="text-xs">
                Database collections status
              </CardDescription>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={performHealthCheck}
            disabled={isLoading}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Overall Health */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Overall Health</span>
            <Badge variant={healthReport.overall === 'healthy' ? 'default' : 'destructive'}>
              {healthReport.overall}
            </Badge>
          </div>
          <Progress value={healthPercentage} className="h-2" />
          <p className="text-xs text-muted-foreground">
            {healthReport.healthyCollections} of {healthReport.totalCollections} collections healthy
          </p>
        </div>

        {/* Health Summary */}
        <div className="grid grid-cols-3 gap-2 text-center">
          <div className="space-y-1">
            <div className="flex items-center justify-center gap-1">
              <CheckCircle className="h-3 w-3 text-green-600" />
              <span className="text-xs font-medium">{healthReport.healthyCollections}</span>
            </div>
            <p className="text-xs text-muted-foreground">Healthy</p>
          </div>
          <div className="space-y-1">
            <div className="flex items-center justify-center gap-1">
              <AlertTriangle className="h-3 w-3 text-yellow-600" />
              <span className="text-xs font-medium">{healthReport.warningCollections}</span>
            </div>
            <p className="text-xs text-muted-foreground">Warning</p>
          </div>
          <div className="space-y-1">
            <div className="flex items-center justify-center gap-1">
              <XCircle className="h-3 w-3 text-red-600" />
              <span className="text-xs font-medium">{healthReport.criticalCollections}</span>
            </div>
            <p className="text-xs text-muted-foreground">Critical</p>
          </div>
        </div>

        {/* Critical Issues Alert */}
        {healthReport.criticalCollections > 0 && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              {healthReport.criticalCollections} collection{healthReport.criticalCollections > 1 ? 's' : ''} 
              {' '}require immediate attention
            </AlertDescription>
          </Alert>
        )}

        {/* Warning Issues Alert */}
        {healthReport.warningCollections > 0 && healthReport.criticalCollections === 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              {healthReport.warningCollections} collection{healthReport.warningCollections > 1 ? 's' : ''} 
              {' '}may need attention
            </AlertDescription>
          </Alert>
        )}

        {/* Detailed Collection Status */}
        {showDetails && (
          <div className="space-y-2">
            <h4 className="text-xs font-medium">Collection Details</h4>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {healthReport.collections.map((collection) => (
                <div key={collection.name} className="flex items-center justify-between text-xs">
                  <span className="truncate flex-1">{collection.name}</span>
                  <div className="flex items-center gap-1">
                    {collection.documentCount !== undefined && (
                      <span className="text-muted-foreground">
                        {collection.documentCount}
                      </span>
                    )}
                    {collection.isValid && collection.isAccessible ? (
                      <CheckCircle className="h-3 w-3 text-green-600" />
                    ) : collection.isValid ? (
                      <AlertTriangle className="h-3 w-3 text-yellow-600" />
                    ) : (
                      <XCircle className="h-3 w-3 text-red-600" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Last Updated */}
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Clock className="h-3 w-3" />
          <span>
            Updated {healthReport.generatedAt.toLocaleTimeString()}
          </span>
        </div>
      </CardContent>
    </Card>
  );
};

// Compact health indicator for header/navbar
export const CompactHealthIndicator: React.FC<{ className?: string }> = ({ className }) => {
  return <DashboardHealthStatus compact className={className} />;
};

// Detailed health panel for admin/debug views
export const DetailedHealthPanel: React.FC<{ className?: string }> = ({ className }) => {
  return <DashboardHealthStatus showDetails className={className} />;
};

export default DashboardHealthStatus;
