"use client";

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useCollaboration } from '@/contexts/CollaborationContext';
import { MousePointer2 } from 'lucide-react';

interface LiveCursorProps {
  userId: string;
  x: number;
  y: number;
  userName: string;
  color: string;
  taskId?: string;
}

function LiveCursor({ userId, x, y, userName, color, taskId }: LiveCursorProps) {
  return (
    <motion.div
      className="fixed pointer-events-none z-50"
      initial={{ opacity: 0, scale: 0 }}
      animate={{ 
        opacity: 1, 
        scale: 1,
        x: x - 12, // Offset for cursor tip
        y: y - 12
      }}
      exit={{ opacity: 0, scale: 0 }}
      transition={{ 
        type: "spring", 
        stiffness: 500, 
        damping: 30,
        opacity: { duration: 0.2 }
      }}
    >
      {/* Cursor Icon */}
      <div className="relative">
        <MousePointer2 
          className="w-6 h-6 drop-shadow-lg" 
          style={{ color }} 
          fill={color}
        />
        
        {/* User Name Label */}
        <motion.div
          className="absolute top-6 left-2 bg-black/80 text-white text-xs px-2 py-1 rounded-md whitespace-nowrap"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          style={{ borderColor: color }}
        >
          <div className="flex items-center gap-1">
            <div 
              className="w-2 h-2 rounded-full" 
              style={{ backgroundColor: color }}
            />
            <span>{userName}</span>
            {taskId && (
              <span className="text-gray-300">• editing</span>
            )}
          </div>
          
          {/* Arrow pointing to cursor */}
          <div 
            className="absolute -top-1 left-2 w-2 h-2 bg-black/80 rotate-45"
            style={{ borderTopColor: color, borderLeftColor: color }}
          />
        </motion.div>
      </div>
    </motion.div>
  );
}

export default function LiveCursors() {
  const { state } = useCollaboration();
  
  // Filter out current user's cursor and get user info
  const otherUserCursors = state.cursors.filter(
    cursor => cursor.userId !== state.currentUser?.id
  );
  
  return (
    <AnimatePresence>
      {otherUserCursors.map(cursor => {
        const user = state.activeUsers.find(u => u.id === cursor.userId);
        if (!user) return null;
        
        return (
          <LiveCursor
            key={cursor.userId}
            userId={cursor.userId}
            x={cursor.x}
            y={cursor.y}
            userName={user.name}
            color={user.color}
            taskId={cursor.taskId}
          />
        );
      })}
    </AnimatePresence>
  );
}
