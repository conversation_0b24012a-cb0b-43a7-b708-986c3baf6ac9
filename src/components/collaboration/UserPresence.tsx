"use client";

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useCollaboration } from '@/contexts/CollaborationContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Users, Eye, Edit3 } from 'lucide-react';

interface UserAvatarProps {
  user: any;
  isEditing?: boolean;
  editingTaskTitle?: string;
}

function UserAvatar({ user, isEditing, editingTaskTitle }: UserAvatarProps) {
  const initials = user.name
    .split(' ')
    .map((n: string) => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <motion.div
            className="relative"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
            whileHover={{ scale: 1.1 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            <Avatar className="w-8 h-8 border-2 border-background shadow-md">
              <AvatarImage src={user.avatar} alt={user.name} />
              <AvatarFallback 
                className="text-xs font-semibold text-white"
                style={{ backgroundColor: user.color }}
              >
                {initials}
              </AvatarFallback>
            </Avatar>
            
            {/* Online Status Indicator */}
            <motion.div
              className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-background rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
            
            {/* Editing Indicator */}
            {isEditing && (
              <motion.div
                className="absolute -top-1 -left-1 w-4 h-4 bg-blue-500 border-2 border-background rounded-full flex items-center justify-center"
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <Edit3 className="w-2 h-2 text-white" />
              </motion.div>
            )}
          </motion.div>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="max-w-xs">
          <div className="space-y-1">
            <p className="font-semibold">{user.name}</p>
            <p className="text-xs text-muted-foreground">{user.email}</p>
            {isEditing && editingTaskTitle && (
              <p className="text-xs text-blue-400 flex items-center gap-1">
                <Edit3 className="w-3 h-3" />
                Editing: {editingTaskTitle}
              </p>
            )}
            <p className="text-xs text-muted-foreground">
              Active {Math.floor((Date.now() - user.lastSeen.getTime()) / 1000)}s ago
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export default function UserPresence() {
  const { state } = useCollaboration();
  
  // Get users who are currently editing tasks
  const editingSessions = state.editingSessions;
  
  // Filter out current user from active users display
  const otherActiveUsers = state.activeUsers.filter(
    user => user.id !== state.currentUser?.id
  );

  if (otherActiveUsers.length === 0) {
    return null;
  }

  return (
    <motion.div
      className="flex items-center gap-2 bg-background/80 backdrop-blur-sm border rounded-lg px-3 py-2 shadow-sm"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Active Users Count */}
      <div className="flex items-center gap-1 text-sm text-muted-foreground">
        <Users className="w-4 h-4" />
        <span>{otherActiveUsers.length + 1}</span>
      </div>
      
      {/* User Avatars */}
      <div className="flex items-center -space-x-2">
        <AnimatePresence>
          {otherActiveUsers.slice(0, 5).map(user => {
            const editingSession = editingSessions.find(s => s.userId === user.id);
            return (
              <UserAvatar
                key={user.id}
                user={user}
                isEditing={!!editingSession}
                editingTaskTitle={editingSession ? `Task ${editingSession.taskId.slice(0, 8)}...` : undefined}
              />
            );
          })}
        </AnimatePresence>
        
        {/* Show overflow count */}
        {otherActiveUsers.length > 5 && (
          <motion.div
            className="w-8 h-8 bg-muted border-2 border-background rounded-full flex items-center justify-center text-xs font-semibold shadow-md"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            +{otherActiveUsers.length - 5}
          </motion.div>
        )}
      </div>
      
      {/* Conflicts Indicator */}
      {state.conflicts.length > 0 && (
        <motion.div
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          className="ml-2"
        >
          <Badge variant="destructive" className="text-xs">
            {state.conflicts.length} conflict{state.conflicts.length > 1 ? 's' : ''}
          </Badge>
        </motion.div>
      )}
    </motion.div>
  );
}
