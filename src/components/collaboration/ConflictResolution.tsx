"use client";

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useCollaboration } from '@/contexts/CollaborationContext';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  AlertTriangle, 
  Users, 
  Clock, 
  CheckCircle, 
  X,
  RefreshCw
} from 'lucide-react';

interface ConflictAlertProps {
  taskId: string;
  conflictingUsers: any[];
  onResolve: () => void;
  onDismiss: () => void;
}

function ConflictAlert({ taskId, conflictingUsers, onResolve, onDismiss }: ConflictAlertProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -20, scale: 0.95 }}
      transition={{ type: "spring", stiffness: 300, damping: 25 }}
      className="mb-4"
    >
      <Alert className="border-amber-200 bg-amber-50/50 dark:border-amber-800 dark:bg-amber-950/50">
        <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
        <AlertTitle className="text-amber-800 dark:text-amber-200 flex items-center gap-2">
          Editing Conflict Detected
          <Badge variant="outline" className="text-xs">
            Task {taskId.slice(0, 8)}...
          </Badge>
        </AlertTitle>
        <AlertDescription className="text-amber-700 dark:text-amber-300 mt-2">
          <div className="space-y-3">
            <p>Multiple users are editing this task simultaneously:</p>
            
            {/* Conflicting Users */}
            <div className="flex items-center gap-2 flex-wrap">
              {conflictingUsers.map(user => {
                const initials = user.name
                  .split(' ')
                  .map((n: string) => n[0])
                  .join('')
                  .toUpperCase()
                  .slice(0, 2);
                
                return (
                  <motion.div
                    key={user.id}
                    className="flex items-center gap-2 bg-white/50 dark:bg-gray-800/50 rounded-full px-2 py-1 border"
                    whileHover={{ scale: 1.05 }}
                  >
                    <Avatar className="w-5 h-5">
                      <AvatarImage src={user.avatar} alt={user.name} />
                      <AvatarFallback 
                        className="text-xs font-semibold text-white text-[10px]"
                        style={{ backgroundColor: user.color }}
                      >
                        {initials}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-xs font-medium">{user.name}</span>
                    <motion.div
                      className="w-2 h-2 bg-red-500 rounded-full"
                      animate={{ opacity: [1, 0.3, 1] }}
                      transition={{ duration: 1, repeat: Infinity }}
                    />
                  </motion.div>
                );
              })}
            </div>
            
            {/* Action Buttons */}
            <div className="flex items-center gap-2 pt-2">
              <Button
                size="sm"
                onClick={onResolve}
                className="bg-amber-600 hover:bg-amber-700 text-white"
              >
                <CheckCircle className="w-3 h-3 mr-1" />
                Resolve Conflict
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                onClick={onDismiss}
                className="border-amber-300 text-amber-700 hover:bg-amber-100"
              >
                <X className="w-3 h-3 mr-1" />
                Dismiss
              </Button>
              
              <div className="flex items-center gap-1 text-xs text-amber-600 ml-auto">
                <Clock className="w-3 h-3" />
                <span>Auto-resolve in 30s</span>
              </div>
            </div>
          </div>
        </AlertDescription>
      </Alert>
    </motion.div>
  );
}

export default function ConflictResolution() {
  const { state, resolveConflict } = useCollaboration();
  
  // Get conflicts with user information
  const conflictsWithUsers = state.conflicts.map(taskId => {
    const editingSessions = state.editingSessions.filter(s => s.taskId === taskId);
    const conflictingUsers = editingSessions
      .map(session => state.activeUsers.find(u => u.id === session.userId))
      .filter(Boolean);
    
    return {
      taskId,
      conflictingUsers
    };
  }).filter(conflict => conflict.conflictingUsers.length > 1);

  if (conflictsWithUsers.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-40 max-w-md">
      <AnimatePresence>
        {conflictsWithUsers.map(({ taskId, conflictingUsers }) => (
          <ConflictAlert
            key={taskId}
            taskId={taskId}
            conflictingUsers={conflictingUsers}
            onResolve={() => resolveConflict(taskId)}
            onDismiss={() => resolveConflict(taskId)}
          />
        ))}
      </AnimatePresence>
      
      {/* Global Conflict Summary */}
      {conflictsWithUsers.length > 1 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mt-2 p-3 bg-amber-100 dark:bg-amber-950 border border-amber-200 dark:border-amber-800 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-amber-800 dark:text-amber-200">
              <AlertTriangle className="w-4 h-4" />
              <span className="text-sm font-medium">
                {conflictsWithUsers.length} active conflicts
              </span>
            </div>
            
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                conflictsWithUsers.forEach(({ taskId }) => resolveConflict(taskId));
              }}
              className="text-xs"
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Resolve All
            </Button>
          </div>
        </motion.div>
      )}
    </div>
  );
}
