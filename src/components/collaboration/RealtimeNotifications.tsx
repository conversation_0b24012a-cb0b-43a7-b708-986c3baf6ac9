"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useCollaboration } from '@/contexts/CollaborationContext';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Bell, 
  X, 
  ArrowRight, 
  Users, 
  Activity,
  Clock,
  CheckCircle
} from 'lucide-react';
import { format } from 'date-fns';

interface RealtimeNotification {
  id: string;
  type: 'task-change' | 'user-joined' | 'user-left' | 'conflict-resolved';
  message: string;
  user?: {
    id: string;
    name: string;
    color: string;
  };
  timestamp: Date;
  taskId?: string;
  data?: any;
}

export default function RealtimeNotifications() {
  const { state } = useCollaboration();
  const [notifications, setNotifications] = useState<RealtimeNotification[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  // Generate notifications based on collaboration state changes
  useEffect(() => {
    const generateNotifications = () => {
      const newNotifications: RealtimeNotification[] = [];

      // Check for new active users
      state.activeUsers.forEach(user => {
        if (user.id !== state.currentUser?.id) {
          const existingNotification = notifications.find(
            n => n.type === 'user-joined' && n.user?.id === user.id
          );
          
          if (!existingNotification) {
            newNotifications.push({
              id: `user-joined-${user.id}-${Date.now()}`,
              type: 'user-joined',
              message: `${user.name} joined the collaboration`,
              user: {
                id: user.id,
                name: user.name,
                color: user.color
              },
              timestamp: new Date()
            });
          }
        }
      });

      // Check for resolved conflicts
      const previousConflicts = notifications.filter(n => n.type === 'conflict-resolved').length;
      if (state.conflicts.length === 0 && previousConflicts > 0) {
        newNotifications.push({
          id: `conflicts-resolved-${Date.now()}`,
          type: 'conflict-resolved',
          message: 'All editing conflicts have been resolved',
          timestamp: new Date()
        });
      }

      if (newNotifications.length > 0) {
        setNotifications(prev => [...newNotifications, ...prev].slice(0, 10)); // Keep last 10
        setIsVisible(true);
      }
    };

    generateNotifications();
  }, [state.activeUsers, state.conflicts, state.currentUser?.id, notifications]);

  // Auto-hide notifications after 5 seconds
  useEffect(() => {
    if (isVisible && notifications.length > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [isVisible, notifications.length]);

  // Clear old notifications
  useEffect(() => {
    const cleanup = setInterval(() => {
      const now = new Date();
      setNotifications(prev => 
        prev.filter(n => now.getTime() - n.timestamp.getTime() < 30000) // Keep for 30 seconds
      );
    }, 5000);

    return () => clearInterval(cleanup);
  }, []);

  const handleDismiss = (notificationId: string) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  };

  const handleDismissAll = () => {
    setNotifications([]);
    setIsVisible(false);
  };

  if (!isVisible || notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 left-4 z-50 max-w-sm">
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, x: -100, scale: 0.95 }}
          animate={{ opacity: 1, x: 0, scale: 1 }}
          exit={{ opacity: 0, x: -100, scale: 0.95 }}
          transition={{ type: "spring", stiffness: 300, damping: 25 }}
        >
          <Card className="shadow-lg border-l-4 border-l-blue-500 bg-background/95 backdrop-blur-sm">
            <CardContent className="p-4">
              {/* Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Bell className="h-4 w-4 text-blue-500" />
                  <span className="text-sm font-medium">Live Updates</span>
                  <Badge variant="secondary" className="text-xs">
                    {notifications.length}
                  </Badge>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleDismissAll}
                    className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>

              {/* Notifications List */}
              <div className="space-y-2 max-h-64 overflow-y-auto">
                <AnimatePresence>
                  {notifications.slice(0, 5).map((notification, index) => (
                    <motion.div
                      key={notification.id}
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-start gap-3 p-2 rounded-lg bg-muted/50 hover:bg-muted/70 transition-colors"
                    >
                      {/* Icon */}
                      <div className="flex-shrink-0 mt-0.5">
                        {notification.type === 'user-joined' && (
                          <div className="flex items-center">
                            <Avatar className="w-6 h-6">
                              <AvatarFallback 
                                className="text-xs text-white"
                                style={{ backgroundColor: notification.user?.color }}
                              >
                                {notification.user?.name.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                          </div>
                        )}
                        {notification.type === 'task-change' && (
                          <Activity className="h-4 w-4 text-green-500" />
                        )}
                        {notification.type === 'conflict-resolved' && (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        )}
                        {notification.type === 'user-left' && (
                          <Users className="h-4 w-4 text-orange-500" />
                        )}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <p className="text-xs text-foreground leading-relaxed">
                          {notification.message}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <Clock className="h-3 w-3 text-muted-foreground" />
                          <span className="text-xs text-muted-foreground">
                            {format(notification.timestamp, 'HH:mm:ss')}
                          </span>
                          {notification.taskId && (
                            <>
                              <ArrowRight className="h-3 w-3 text-muted-foreground" />
                              <span className="text-xs text-muted-foreground">
                                Task {notification.taskId.slice(0, 8)}...
                              </span>
                            </>
                          )}
                        </div>
                      </div>

                      {/* Dismiss Button */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDismiss(notification.id)}
                        className="h-5 w-5 p-0 text-muted-foreground hover:text-foreground flex-shrink-0"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>

              {/* Show more indicator */}
              {notifications.length > 5 && (
                <div className="text-center mt-2 pt-2 border-t">
                  <span className="text-xs text-muted-foreground">
                    +{notifications.length - 5} more updates
                  </span>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </AnimatePresence>
    </div>
  );
}
