"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Activity,
  Database,
  Settings,
  Link,
  CheckCircle,
  AlertTriangle,
  Clock,
  Zap
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { globalIntegrationService, type APIPartnership } from '@/services/globalIntegrationService';

interface APIPartnershipManagerProps {
  partnerships: APIPartnership[];
  onPartnershipsUpdate: (partnerships: APIPartnership[]) => void;
}

export default function APIPartnershipManager({ partnerships, onPartnershipsUpdate }: APIPartnershipManagerProps) {
  const { toast } = useToast();
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [selectedPartnership, setSelectedPartnership] = useState<APIPartnership | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const [newPartnership, setNewPartnership] = useState({
    partnerName: '',
    partnerType: 'marketing' as APIPartnership['partnerType'],
    region: '',
    apiDetails: {
      baseUrl: '',
      version: '',
      authMethod: 'api_key' as const,
      rateLimits: {
        requestsPerMinute: 1000,
        requestsPerHour: 50000,
        requestsPerDay: 1000000
      },
      endpoints: []
    },
    dataExchange: {
      inboundData: [] as string[],
      outboundData: [] as string[],
      syncFrequency: 'hourly' as const,
      dataFormat: 'json' as const
    }
  });

  const handleCreatePartnership = async () => {
    try {
      setIsLoading(true);
      const partnership = await globalIntegrationService.createAPIPartnership({
        ...newPartnership,
        performance: {
          uptime: 99.9,
          averageResponseTime: 200,
          errorRate: 0.001,
          lastHealthCheck: new Date()
        },
        status: 'pending'
      });
      
      onPartnershipsUpdate([partnership, ...partnerships]);
      setShowCreateDialog(false);
      resetForm();
      
      toast({
        title: "Success",
        description: "API partnership created successfully"
      });
    } catch (error) {
      console.error('Failed to create API partnership:', error);
      toast({
        title: "Error",
        description: "Failed to create API partnership",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleHealthCheck = async (partnershipId: string) => {
    try {
      setIsLoading(true);
      const healthResult = await globalIntegrationService.performHealthCheck(partnershipId);
      
      // Update the partnership in the list
      const updatedPartnerships = partnerships.map(p => 
        p.id === partnershipId 
          ? { ...p, performance: { ...p.performance, ...healthResult } }
          : p
      );
      onPartnershipsUpdate(updatedPartnerships);
      
      toast({
        title: "Health Check Complete",
        description: `Partnership is ${healthResult.status}. Response time: ${healthResult.responseTime.toFixed(0)}ms`
      });
    } catch (error) {
      console.error('Health check failed:', error);
      toast({
        title: "Error",
        description: "Health check failed",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setNewPartnership({
      partnerName: '',
      partnerType: 'marketing',
      region: '',
      apiDetails: {
        baseUrl: '',
        version: '',
        authMethod: 'api_key',
        rateLimits: {
          requestsPerMinute: 1000,
          requestsPerHour: 50000,
          requestsPerDay: 1000000
        },
        endpoints: []
      },
      dataExchange: {
        inboundData: [],
        outboundData: [],
        syncFrequency: 'hourly',
        dataFormat: 'json'
      }
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'testing': return 'bg-blue-100 text-blue-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getHealthStatusIcon = (uptime: number) => {
    if (uptime >= 99.5) return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (uptime >= 95) return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    return <AlertTriangle className="h-4 w-4 text-red-600" />;
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">API Partnerships</h3>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Partnership
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create API Partnership</DialogTitle>
              <DialogDescription>Add a new API partnership for global integration</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="partnerName">Partner Name</Label>
                  <Input
                    id="partnerName"
                    value={newPartnership.partnerName}
                    onChange={(e) => setNewPartnership(prev => ({ ...prev, partnerName: e.target.value }))}
                    placeholder="EventBrite, Salesforce, etc."
                  />
                </div>
                <div>
                  <Label htmlFor="partnerType">Partner Type</Label>
                  <Select 
                    value={newPartnership.partnerType} 
                    onValueChange={(value) => setNewPartnership(prev => ({ 
                      ...prev, 
                      partnerType: value as APIPartnership['partnerType'] 
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="exhibition_organizer">Exhibition Organizer</SelectItem>
                      <SelectItem value="venue_management">Venue Management</SelectItem>
                      <SelectItem value="payment_processor">Payment Processor</SelectItem>
                      <SelectItem value="logistics">Logistics</SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                      <SelectItem value="analytics">Analytics</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="region">Region</Label>
                  <Input
                    id="region"
                    value={newPartnership.region}
                    onChange={(e) => setNewPartnership(prev => ({ ...prev, region: e.target.value }))}
                    placeholder="Global, North America, Europe, etc."
                  />
                </div>
                <div>
                  <Label htmlFor="baseUrl">API Base URL</Label>
                  <Input
                    id="baseUrl"
                    value={newPartnership.apiDetails.baseUrl}
                    onChange={(e) => setNewPartnership(prev => ({ 
                      ...prev, 
                      apiDetails: { ...prev.apiDetails, baseUrl: e.target.value }
                    }))}
                    placeholder="https://api.partner.com/v1"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="version">API Version</Label>
                  <Input
                    id="version"
                    value={newPartnership.apiDetails.version}
                    onChange={(e) => setNewPartnership(prev => ({ 
                      ...prev, 
                      apiDetails: { ...prev.apiDetails, version: e.target.value }
                    }))}
                    placeholder="v1.0, v2.3, etc."
                  />
                </div>
                <div>
                  <Label htmlFor="authMethod">Authentication Method</Label>
                  <Select 
                    value={newPartnership.apiDetails.authMethod} 
                    onValueChange={(value) => setNewPartnership(prev => ({ 
                      ...prev, 
                      apiDetails: { ...prev.apiDetails, authMethod: value as any }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="api_key">API Key</SelectItem>
                      <SelectItem value="oauth2">OAuth 2.0</SelectItem>
                      <SelectItem value="jwt">JWT</SelectItem>
                      <SelectItem value="basic_auth">Basic Auth</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreatePartnership} disabled={isLoading}>
                  {isLoading ? 'Creating...' : 'Create Partnership'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4">
        {partnerships.map((partnership) => (
          <Card key={partnership.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Link className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <CardTitle className="text-lg">{partnership.partnerName}</CardTitle>
                    <CardDescription>{partnership.region} • {partnership.partnerType.replace('_', ' ')}</CardDescription>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getHealthStatusIcon(partnership.performance.uptime)}
                  <Badge className={getStatusColor(partnership.status)}>
                    {partnership.status}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <h4 className="font-medium mb-2">API Details</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Version:</span>
                      <span className="font-medium">{partnership.apiDetails.version}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Auth:</span>
                      <span className="font-medium">{partnership.apiDetails.authMethod}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Endpoints:</span>
                      <span className="font-medium">{partnership.apiDetails.endpoints.length}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Performance</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Uptime:</span>
                      <span className="font-medium">{partnership.performance.uptime.toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Response Time:</span>
                      <span className="font-medium">{partnership.performance.averageResponseTime.toFixed(0)}ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Error Rate:</span>
                      <span className="font-medium">{(partnership.performance.errorRate * 100).toFixed(2)}%</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Data Exchange</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Sync:</span>
                      <span className="font-medium">{partnership.dataExchange.syncFrequency}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Format:</span>
                      <span className="font-medium">{partnership.dataExchange.dataFormat.toUpperCase()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Last Sync:</span>
                      <span className="font-medium">{partnership.performance.lastHealthCheck.toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-2 mt-4">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => handleHealthCheck(partnership.id!)}
                  disabled={isLoading}
                >
                  <Activity className="h-3 w-3 mr-1" />
                  Health Check
                </Button>
                <Button variant="outline" size="sm">
                  <Database className="h-3 w-3 mr-1" />
                  Sync Data
                </Button>
                <Button variant="outline" size="sm">
                  <Settings className="h-3 w-3 mr-1" />
                  Configure
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
