"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  RefreshCw, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Calendar,
  Users,
  Target,
  DollarSign,
  AlertCircle
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import { getOptimizedDashboardData } from '@/services/optimizedFirestoreService';
import type { Exhibition, Task, Lead } from '@/types/firestore';

// ===== INTERFACES =====

interface DashboardCounts {
  totalExhibitions: number;
  totalTasks: number;
  totalLeads: number;
  totalEvents: number;
}

interface DashboardData {
  upcomingExhibitions?: Exhibition[];
  recentTasks?: Task[];
  recentLeads?: Lead[];
  counts?: DashboardCounts;
  fetchTime: number;
}

interface OptimizedDashboardWidgetProps {
  title: string;
  type: 'upcoming-exhibitions' | 'recent-tasks' | 'recent-leads' | 'counts-overview';
  limit?: number;
  refreshInterval?: number; // in milliseconds
  className?: string;
}

// ===== OPTIMIZED DASHBOARD WIDGET COMPONENT =====

export function OptimizedDashboardWidget({
  title,
  type,
  limit = 5,
  refreshInterval,
  className = ""
}: OptimizedDashboardWidgetProps) {
  const { user, tenant } = useAuth();
  const { toast } = useToast();
  const [data, setData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Load dashboard data
  const loadData = useCallback(async (forceRefresh = false) => {
    if (!tenant?.id) return;

    setIsLoading(true);
    setError(null);

    try {
      const options = {
        includeUpcoming: type === 'upcoming-exhibitions',
        includeRecent: type === 'recent-tasks' || type === 'recent-leads',
        includeCounts: type === 'counts-overview',
        limit
      };

      const result = await getOptimizedDashboardData(tenant.id, options, forceRefresh);
      setData(result);
      setLastRefresh(new Date());
      
    } catch (err) {
      console.error(`Error loading dashboard widget ${type}:`, err);
      setError(err instanceof Error ? err.message : 'Failed to load data');
      toast({
        title: "Error Loading Widget",
        description: `Could not load ${title}. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [tenant?.id, type, limit, title, toast]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    loadData(true);
  }, [loadData]);

  // Auto-refresh setup
  useEffect(() => {
    if (!refreshInterval) return;

    const interval = setInterval(() => {
      loadData(false); // Use cache if available
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval, loadData]);

  // Initial load
  useEffect(() => {
    if (tenant?.id) {
      loadData();
    }
  }, [tenant?.id, loadData]);

  // Render upcoming exhibitions
  const renderUpcomingExhibitions = () => {
    if (!data?.upcomingExhibitions) return null;

    return (
      <div className="space-y-3">
        {data.upcomingExhibitions.map((exhibition) => (
          <div key={exhibition.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
            <div className="flex-1">
              <h4 className="font-medium text-sm">{exhibition.name}</h4>
              <div className="flex items-center gap-2 mt-1">
                <Calendar className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">
                  {new Date(exhibition.startDate as any).toLocaleDateString()}
                </span>
                <Badge variant="outline" className="text-xs">
                  {exhibition.status}
                </Badge>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm font-medium">
                ${(exhibition.estimatedBudget || 0).toLocaleString()}
              </div>
              <div className="text-xs text-muted-foreground">Budget</div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Render recent tasks
  const renderRecentTasks = () => {
    if (!data?.recentTasks) return null;

    return (
      <div className="space-y-3">
        {data.recentTasks.map((task) => (
          <div key={task.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
            <div className="flex-1">
              <h4 className="font-medium text-sm">{task.title}</h4>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-xs text-muted-foreground">
                  {(task as any).exhibitionName || 'Unknown Exhibition'}
                </span>
                <Badge 
                  variant={task.status === 'Completed' ? 'default' : 'secondary'} 
                  className="text-xs"
                >
                  {task.status}
                </Badge>
              </div>
            </div>
            <div className="text-right">
              <div className="text-xs text-muted-foreground">
                Due: {task.dueDate ? new Date(task.dueDate as any).toLocaleDateString() : 'No date'}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Render recent leads
  const renderRecentLeads = () => {
    if (!data?.recentLeads) return null;

    return (
      <div className="space-y-3">
        {data.recentLeads.map((lead) => (
          <div key={lead.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
            <div className="flex-1">
              <h4 className="font-medium text-sm">{lead.firstName} {lead.lastName}</h4>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-xs text-muted-foreground">{lead.company}</span>
                <Badge 
                  variant={lead.status === 'Qualified' ? 'default' : 'secondary'} 
                  className="text-xs"
                >
                  {lead.status}
                </Badge>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm font-medium">
                ${(lead.estimatedValue || 5000).toLocaleString()}
              </div>
              <div className="text-xs text-muted-foreground">Est. Value</div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Render counts overview
  const renderCountsOverview = () => {
    if (!data?.counts) return null;

    const counts = data.counts;
    
    return (
      <div className="grid grid-cols-2 gap-4">
        <div className="text-center p-4 bg-muted/50 rounded-lg">
          <Calendar className="h-6 w-6 mx-auto mb-2 text-blue-500" />
          <div className="text-2xl font-bold">{counts.totalExhibitions}</div>
          <div className="text-xs text-muted-foreground">Exhibitions</div>
        </div>
        
        <div className="text-center p-4 bg-muted/50 rounded-lg">
          <Target className="h-6 w-6 mx-auto mb-2 text-green-500" />
          <div className="text-2xl font-bold">{counts.totalTasks}</div>
          <div className="text-xs text-muted-foreground">Tasks</div>
        </div>
        
        <div className="text-center p-4 bg-muted/50 rounded-lg">
          <Users className="h-6 w-6 mx-auto mb-2 text-purple-500" />
          <div className="text-2xl font-bold">{counts.totalLeads}</div>
          <div className="text-xs text-muted-foreground">Leads</div>
        </div>
        
        <div className="text-center p-4 bg-muted/50 rounded-lg">
          <Calendar className="h-6 w-6 mx-auto mb-2 text-orange-500" />
          <div className="text-2xl font-bold">{counts.totalEvents}</div>
          <div className="text-xs text-muted-foreground">Events</div>
        </div>
      </div>
    );
  };

  // Render content based on type
  const renderContent = () => {
    switch (type) {
      case 'upcoming-exhibitions':
        return renderUpcomingExhibitions();
      case 'recent-tasks':
        return renderRecentTasks();
      case 'recent-leads':
        return renderRecentLeads();
      case 'counts-overview':
        return renderCountsOverview();
      default:
        return <div>Unknown widget type</div>;
    }
  };

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <AlertCircle className="h-8 w-8 text-destructive mb-2" />
          <p className="text-sm text-muted-foreground text-center mb-4">{error}</p>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{title}</CardTitle>
          <div className="flex items-center gap-2">
            {data && (
              <Badge variant="outline" className="text-xs">
                {data.fetchTime.toFixed(0)}ms
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
        
        {lastRefresh && (
          <p className="text-xs text-muted-foreground">
            Last updated: {lastRefresh.toLocaleTimeString()}
          </p>
        )}
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="space-y-3">
            {Array.from({ length: type === 'counts-overview' ? 4 : 3 }).map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        ) : (
          renderContent()
        )}
      </CardContent>
    </Card>
  );
}
