"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Search, 
  Filter, 
  RefreshCw, 
  ChevronDown,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import type { OptimizedListResult } from '@/services/performanceOptimizedQueryService';
import type { TenantAwareEntity } from '@/types/firestore';

// ===== INTERFACES =====

export interface OptimizedListViewProps<T extends TenantAwareEntity> {
  title: string;
  description?: string;
  queryFunction: (
    tenantId: string,
    options: any,
    forceRefresh?: boolean
  ) => Promise<OptimizedListResult<T>>;
  renderItem: (item: T, index: number) => React.ReactNode;
  renderEmpty?: () => React.ReactNode;
  filters?: Array<{
    key: string;
    label: string;
    options: Array<{ value: string; label: string }>;
  }>;
  searchFields?: string[];
  defaultLimit?: number;
  enableInfiniteScroll?: boolean;
  enableSearch?: boolean;
  enableFilters?: boolean;
  enableRefresh?: boolean;
  className?: string;
}

interface FilterState {
  [key: string]: string;
}

// ===== OPTIMIZED LIST VIEW COMPONENT =====

export function OptimizedListView<T extends TenantAwareEntity>({
  title,
  description,
  queryFunction,
  renderItem,
  renderEmpty,
  filters = [],
  searchFields = [],
  defaultLimit = 50,
  enableInfiniteScroll = true,
  enableSearch = true,
  enableFilters = true,
  enableRefresh = true,
  className = ""
}: OptimizedListViewProps<T>) {
  const { user, tenant } = useAuth();
  const { toast } = useToast();
  const [items, setItems] = useState<T[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [lastDoc, setLastDoc] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterState, setFilterState] = useState<FilterState>({});
  const [error, setError] = useState<string | null>(null);
  const [fetchTime, setFetchTime] = useState<number>(0);
  const [cacheHit, setCacheHit] = useState<boolean>(false);
  
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  // Build query options from current state
  const buildQueryOptions = useCallback(() => {
    const options: any = {
      limit: defaultLimit,
      startAfter: lastDoc
    };

    // Add filters
    Object.entries(filterState).forEach(([key, value]) => {
      if (value) {
        options[key] = value;
      }
    });

    return options;
  }, [filterState, lastDoc, defaultLimit]);

  // Load initial data
  const loadData = useCallback(async (forceRefresh = false) => {
    if (!tenant?.id) return;

    setIsLoading(true);
    setError(null);

    try {
      const options = buildQueryOptions();
      options.startAfter = null; // Reset pagination for fresh load
      
      const result = await queryFunction(tenant.id, options, forceRefresh);
      
      setItems(result.items);
      setHasMore(result.hasMore);
      setLastDoc(result.lastDoc);
      setFetchTime(result.fetchTime);
      setCacheHit(result.cacheHit);
      
      if (result.cacheHit) {
        console.log(`✅ Cache hit for ${title} - loaded in ${result.fetchTime.toFixed(2)}ms`);
      } else {
        console.log(`🔥 Fresh data for ${title} - loaded in ${result.fetchTime.toFixed(2)}ms`);
      }
      
    } catch (err) {
      console.error(`Error loading ${title}:`, err);
      setError(err instanceof Error ? err.message : 'Failed to load data');
      toast({
        title: "Error Loading Data",
        description: `Could not load ${title.toLowerCase()}. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [tenant?.id, queryFunction, buildQueryOptions, title, toast]);

  // Load more data for infinite scroll
  const loadMore = useCallback(async () => {
    if (!tenant?.id || !hasMore || isLoadingMore) return;

    setIsLoadingMore(true);

    try {
      const options = buildQueryOptions();
      const result = await queryFunction(tenant.id, options);
      
      setItems(prev => [...prev, ...result.items]);
      setHasMore(result.hasMore);
      setLastDoc(result.lastDoc);
      
    } catch (err) {
      console.error(`Error loading more ${title}:`, err);
      toast({
        title: "Error Loading More Data",
        description: "Could not load additional items. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingMore(false);
    }
  }, [tenant?.id, hasMore, isLoadingMore, queryFunction, buildQueryOptions, title, toast]);

  // Filter items based on search query
  const filteredItems = React.useMemo(() => {
    if (!searchQuery || searchFields.length === 0) return items;

    return items.filter(item => {
      return searchFields.some(field => {
        const value = (item as any)[field];
        return value && value.toString().toLowerCase().includes(searchQuery.toLowerCase());
      });
    });
  }, [items, searchQuery, searchFields]);

  // Handle filter changes
  const handleFilterChange = useCallback((key: string, value: string) => {
    setFilterState(prev => ({ ...prev, [key]: value }));
    setLastDoc(null); // Reset pagination
  }, []);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setLastDoc(null);
    loadData(true);
  }, [loadData]);

  // Setup intersection observer for infinite scroll
  useEffect(() => {
    if (!enableInfiniteScroll) return;

    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoadingMore) {
          loadMore();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [enableInfiniteScroll, hasMore, isLoadingMore, loadMore]);

  // Load data when filters change
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Initial load
  useEffect(() => {
    if (tenant?.id) {
      loadData();
    }
  }, [tenant?.id]);

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <AlertCircle className="h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-semibold mb-2">Error Loading Data</h3>
          <p className="text-muted-foreground text-center mb-4">{error}</p>
          <Button onClick={() => loadData(true)} variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {title}
              {cacheHit && (
                <Badge variant="secondary" className="text-xs">
                  Cached ({fetchTime.toFixed(0)}ms)
                </Badge>
              )}
              {!cacheHit && fetchTime > 0 && (
                <Badge variant="outline" className="text-xs">
                  Fresh ({fetchTime.toFixed(0)}ms)
                </Badge>
              )}
            </CardTitle>
            {description && (
              <p className="text-sm text-muted-foreground mt-1">{description}</p>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {enableRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isLoading}
              >
                <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        {(enableSearch || enableFilters) && (
          <div className="flex flex-col sm:flex-row gap-4 mt-4">
            {enableSearch && searchFields.length > 0 && (
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={`Search ${title.toLowerCase()}...`}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            )}

            {enableFilters && filters.length > 0 && (
              <div className="flex gap-2">
                {filters.map((filter) => (
                  <select
                    key={filter.key}
                    value={filterState[filter.key] || ''}
                    onChange={(e) => handleFilterChange(filter.key, e.target.value)}
                    className="px-3 py-2 border border-input bg-background rounded-md text-sm"
                  >
                    <option value="">{filter.label}</option>
                    {filter.options.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                ))}
              </div>
            )}
          </div>
        )}
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        ) : filteredItems.length === 0 ? (
          renderEmpty ? renderEmpty() : (
            <div className="flex flex-col items-center justify-center py-10 text-center">
              <p className="text-muted-foreground">No {title.toLowerCase()} found.</p>
            </div>
          )
        ) : (
          <>
            <div className="space-y-2">
              {filteredItems.map((item, index) => renderItem(item, index))}
            </div>

            {/* Infinite scroll trigger */}
            {enableInfiniteScroll && hasMore && (
              <div ref={loadMoreRef} className="flex justify-center py-4">
                {isLoadingMore ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-muted-foreground">Loading more...</span>
                  </div>
                ) : (
                  <Button
                    variant="outline"
                    onClick={loadMore}
                    className="w-full"
                  >
                    <ChevronDown className="mr-2 h-4 w-4" />
                    Load More
                  </Button>
                )}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
