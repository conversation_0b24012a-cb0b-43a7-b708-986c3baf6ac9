/**
 * EVEXA Enhanced Performance Monitor
 * Comprehensive performance monitoring with real-time metrics and optimization suggestions
 */

'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  Zap, 
  Clock, 
  Database, 
  Wifi, 
  AlertTriangle, 
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Monitor,
  Cpu,
  HardDrive
} from 'lucide-react';
import { performanceUtils, useWebVitals, usePerformanceMonitor } from '@/lib/performance-utils';
import { cn } from '@/lib/utils';

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  status: 'good' | 'needs-improvement' | 'poor';
  threshold: { good: number; poor: number };
}

interface ResourceTiming {
  name: string;
  duration: number;
  size?: number;
  type: string;
}

export const EnhancedPerformanceMonitor: React.FC<{
  className?: string;
  showDetails?: boolean;
}> = ({ className, showDetails = false }) => {
  usePerformanceMonitor('EnhancedPerformanceMonitor');

  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [resources, setResources] = useState<ResourceTiming[]>([]);
  const [memoryUsage, setMemoryUsage] = useState<any>(null);
  const [networkInfo, setNetworkInfo] = useState<any>(null);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout>();

  const webVitals = useWebVitals();

  // Initialize performance monitoring
  useEffect(() => {
    if (isMonitoring) {
      startMonitoring();
    } else {
      stopMonitoring();
    }

    return () => stopMonitoring();
  }, [isMonitoring]);

  const startMonitoring = () => {
    // Initial measurement
    measurePerformance();

    // Set up interval for continuous monitoring
    intervalRef.current = setInterval(measurePerformance, 5000);
  };

  const stopMonitoring = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };

  const measurePerformance = () => {
    // Core Web Vitals
    const newMetrics: PerformanceMetric[] = [];

    // First Contentful Paint
    const fcpEntries = performance.getEntriesByName('first-contentful-paint');
    if (fcpEntries.length > 0) {
      const fcp = fcpEntries[0].startTime;
      newMetrics.push({
        name: 'First Contentful Paint',
        value: fcp,
        unit: 'ms',
        status: fcp < 1800 ? 'good' : fcp < 3000 ? 'needs-improvement' : 'poor',
        threshold: { good: 1800, poor: 3000 }
      });
    }

    // Largest Contentful Paint
    if (webVitals.LCP) {
      newMetrics.push({
        name: 'Largest Contentful Paint',
        value: webVitals.LCP,
        unit: 'ms',
        status: webVitals.LCP < 2500 ? 'good' : webVitals.LCP < 4000 ? 'needs-improvement' : 'poor',
        threshold: { good: 2500, poor: 4000 }
      });
    }

    // First Input Delay
    if (webVitals.FID) {
      newMetrics.push({
        name: 'First Input Delay',
        value: webVitals.FID,
        unit: 'ms',
        status: webVitals.FID < 100 ? 'good' : webVitals.FID < 300 ? 'needs-improvement' : 'poor',
        threshold: { good: 100, poor: 300 }
      });
    }

    // Cumulative Layout Shift
    if (webVitals.CLS) {
      newMetrics.push({
        name: 'Cumulative Layout Shift',
        value: webVitals.CLS,
        unit: '',
        status: webVitals.CLS < 0.1 ? 'good' : webVitals.CLS < 0.25 ? 'needs-improvement' : 'poor',
        threshold: { good: 0.1, poor: 0.25 }
      });
    }

    // Time to First Byte
    if (webVitals.TTFB) {
      newMetrics.push({
        name: 'Time to First Byte',
        value: webVitals.TTFB,
        unit: 'ms',
        status: webVitals.TTFB < 800 ? 'good' : webVitals.TTFB < 1800 ? 'needs-improvement' : 'poor',
        threshold: { good: 800, poor: 1800 }
      });
    }

    setMetrics(newMetrics);

    // Memory usage
    const memory = performanceUtils.getMemoryUsage();
    setMemoryUsage(memory);

    // Resource timing
    const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    const resourceData = resourceEntries.slice(-20).map(entry => ({
      name: entry.name.split('/').pop() || entry.name,
      duration: entry.duration,
      size: entry.transferSize,
      type: entry.initiatorType
    }));
    setResources(resourceData);

    // Network information
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      setNetworkInfo({
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-600 bg-green-100';
      case 'needs-improvement': return 'text-yellow-600 bg-yellow-100';
      case 'poor': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good': return <CheckCircle className="h-4 w-4" />;
      case 'needs-improvement': return <AlertTriangle className="h-4 w-4" />;
      case 'poor': return <TrendingDown className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const calculateOverallScore = () => {
    if (metrics.length === 0) return 0;
    const scores = metrics.map(metric => {
      switch (metric.status) {
        case 'good': return 100;
        case 'needs-improvement': return 60;
        case 'poor': return 20;
        default: return 0;
      }
    });
    return Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);
  };

  const overallScore = calculateOverallScore();

  if (!showDetails) {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsMonitoring(!isMonitoring)}
          className="gap-2"
        >
          <Activity className={cn('h-4 w-4', isMonitoring && 'animate-pulse text-green-500')} />
          Performance
        </Button>
        {overallScore > 0 && (
          <Badge variant={overallScore > 80 ? 'default' : overallScore > 60 ? 'secondary' : 'destructive'}>
            {overallScore}
          </Badge>
        )}
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="h-5 w-5" />
              Performance Monitor
            </CardTitle>
            <CardDescription>
              Real-time performance metrics and optimization insights
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant={isMonitoring ? 'destructive' : 'default'}
              size="sm"
              onClick={() => setIsMonitoring(!isMonitoring)}
            >
              {isMonitoring ? 'Stop' : 'Start'} Monitoring
            </Button>
            {overallScore > 0 && (
              <div className="text-center">
                <div className="text-2xl font-bold">{overallScore}</div>
                <div className="text-xs text-muted-foreground">Score</div>
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="vitals" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="vitals">Core Vitals</TabsTrigger>
            <TabsTrigger value="resources">Resources</TabsTrigger>
            <TabsTrigger value="memory">Memory</TabsTrigger>
            <TabsTrigger value="network">Network</TabsTrigger>
          </TabsList>

          <TabsContent value="vitals" className="space-y-4">
            {metrics.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                Start monitoring to see performance metrics
              </div>
            ) : (
              <div className="space-y-3">
                {metrics.map((metric, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(metric.status)}
                      <div>
                        <div className="font-medium">{metric.name}</div>
                        <div className="text-sm text-muted-foreground">
                          Good: &lt; {metric.threshold.good}{metric.unit} | 
                          Poor: &gt; {metric.threshold.poor}{metric.unit}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-mono text-lg">
                        {metric.value.toFixed(metric.unit === 'ms' ? 0 : 2)}{metric.unit}
                      </div>
                      <Badge className={getStatusColor(metric.status)}>
                        {metric.status.replace('-', ' ')}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="resources" className="space-y-4">
            <div className="space-y-2">
              {resources.map((resource, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{resource.type}</Badge>
                    <span className="text-sm font-mono truncate max-w-xs">{resource.name}</span>
                  </div>
                  <div className="text-right text-sm">
                    <div>{resource.duration.toFixed(0)}ms</div>
                    {resource.size && (
                      <div className="text-muted-foreground">
                        {(resource.size / 1024).toFixed(1)}KB
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="memory" className="space-y-4">
            {memoryUsage ? (
              <div className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-4 border rounded-lg">
                    <Cpu className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                    <div className="text-2xl font-bold">{memoryUsage.used}MB</div>
                    <div className="text-sm text-muted-foreground">Used</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <HardDrive className="h-8 w-8 mx-auto mb-2 text-green-500" />
                    <div className="text-2xl font-bold">{memoryUsage.total}MB</div>
                    <div className="text-sm text-muted-foreground">Total</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <Database className="h-8 w-8 mx-auto mb-2 text-purple-500" />
                    <div className="text-2xl font-bold">{memoryUsage.limit}MB</div>
                    <div className="text-sm text-muted-foreground">Limit</div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Memory Usage</span>
                    <span>{((memoryUsage.used / memoryUsage.total) * 100).toFixed(1)}%</span>
                  </div>
                  <Progress value={(memoryUsage.used / memoryUsage.total) * 100} />
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                Memory information not available
              </div>
            )}
          </TabsContent>

          <TabsContent value="network" className="space-y-4">
            {networkInfo ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg">
                    <Wifi className="h-6 w-6 mb-2 text-blue-500" />
                    <div className="text-lg font-semibold">{networkInfo.effectiveType}</div>
                    <div className="text-sm text-muted-foreground">Connection Type</div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <TrendingUp className="h-6 w-6 mb-2 text-green-500" />
                    <div className="text-lg font-semibold">{networkInfo.downlink} Mbps</div>
                    <div className="text-sm text-muted-foreground">Downlink</div>
                  </div>
                </div>
                <div className="p-4 border rounded-lg">
                  <Clock className="h-6 w-6 mb-2 text-orange-500" />
                  <div className="text-lg font-semibold">{networkInfo.rtt}ms</div>
                  <div className="text-sm text-muted-foreground">Round Trip Time</div>
                </div>
                {networkInfo.saveData && (
                  <Badge variant="secondary">Data Saver Mode Active</Badge>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                Network information not available
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default EnhancedPerformanceMonitor;
