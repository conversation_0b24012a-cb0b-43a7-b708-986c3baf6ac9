'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Search, 
  BookOpen, 
  Plus, 
  Edit, 
  Eye, 
  ThumbsUp, 
  ThumbsDown,
  Clock,
  User,
  Tag,
  FileText,
  Video,
  Link,
  Image,
  Filter
} from 'lucide-react';
import { CustomerSuccessService, KnowledgeBaseArticle, KnowledgeBaseCategory } from '@/services/customerSuccessService';

interface KnowledgeBaseManagerProps {
  tenantId: string;
  userRole: string;
}

export default function KnowledgeBaseManager({ tenantId, userRole }: KnowledgeBaseManagerProps) {
  const [articles, setArticles] = useState<KnowledgeBaseArticle[]>([]);
  const [categories, setCategories] = useState<KnowledgeBaseCategory[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedArticle, setSelectedArticle] = useState<KnowledgeBaseArticle | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('browse');

  const customerSuccessService = CustomerSuccessService.getInstance();

  useEffect(() => {
    loadKnowledgeBaseData();
  }, []);

  const loadKnowledgeBaseData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load sample data for demonstration
      const sampleArticles: KnowledgeBaseArticle[] = [
        {
          id: 'kb_001',
          title: 'Getting Started with EVEXA',
          content: 'Welcome to EVEXA! This comprehensive guide will help you get started with our exhibition management platform...',
          summary: 'A complete introduction to EVEXA platform features and basic setup.',
          category: 'Getting Started',
          tags: ['onboarding', 'basics', 'setup'],
          difficulty: 'beginner',
          estimatedReadTime: 5,
          author: 'EVEXA Team',
          lastUpdated: new Date(),
          version: '1.0',
          status: 'published',
          views: 1250,
          likes: 89,
          helpfulVotes: 156,
          notHelpfulVotes: 12,
          relatedArticles: ['kb_002', 'kb_003'],
          attachments: [
            {
              id: 'att_001',
              name: 'EVEXA Quick Start Guide',
              type: 'document',
              url: '/docs/quick-start.pdf',
              description: 'PDF guide for quick setup'
            }
          ],
          searchKeywords: ['getting started', 'onboarding', 'setup', 'introduction']
        },
        {
          id: 'kb_002',
          title: 'Creating Your First Exhibition',
          content: 'Learn how to create and configure your first exhibition in EVEXA...',
          summary: 'Step-by-step guide to creating exhibitions and managing events.',
          category: 'Exhibitions',
          tags: ['exhibitions', 'events', 'creation'],
          difficulty: 'beginner',
          estimatedReadTime: 8,
          author: 'EVEXA Team',
          lastUpdated: new Date(),
          version: '1.2',
          status: 'published',
          views: 980,
          likes: 67,
          helpfulVotes: 134,
          notHelpfulVotes: 8,
          relatedArticles: ['kb_001', 'kb_004'],
          attachments: [
            {
              id: 'att_002',
              name: 'Exhibition Setup Video',
              type: 'video',
              url: '/videos/exhibition-setup.mp4',
              description: 'Video tutorial for exhibition creation'
            }
          ],
          searchKeywords: ['exhibition', 'create', 'setup', 'events']
        },
        {
          id: 'kb_003',
          title: 'Advanced Analytics and Reporting',
          content: 'Unlock the power of EVEXA analytics to gain insights into your exhibition performance...',
          summary: 'Comprehensive guide to analytics features and custom reporting.',
          category: 'Analytics',
          tags: ['analytics', 'reporting', 'insights'],
          difficulty: 'advanced',
          estimatedReadTime: 12,
          author: 'Analytics Team',
          lastUpdated: new Date(),
          version: '2.1',
          status: 'published',
          views: 567,
          likes: 45,
          helpfulVotes: 89,
          notHelpfulVotes: 5,
          relatedArticles: ['kb_005'],
          attachments: [],
          searchKeywords: ['analytics', 'reporting', 'insights', 'data', 'metrics']
        }
      ];

      const sampleCategories: KnowledgeBaseCategory[] = [
        {
          id: 'cat_001',
          name: 'Getting Started',
          description: 'Essential guides for new users',
          icon: 'play-circle',
          order: 1,
          articleCount: 5,
          subcategories: []
        },
        {
          id: 'cat_002',
          name: 'Exhibitions',
          description: 'Exhibition management and setup',
          icon: 'briefcase',
          order: 2,
          articleCount: 12,
          subcategories: []
        },
        {
          id: 'cat_003',
          name: 'Analytics',
          description: 'Reporting and data analysis',
          icon: 'bar-chart',
          order: 3,
          articleCount: 8,
          subcategories: []
        },
        {
          id: 'cat_004',
          name: 'Troubleshooting',
          description: 'Common issues and solutions',
          icon: 'help-circle',
          order: 4,
          articleCount: 15,
          subcategories: []
        }
      ];

      setArticles(sampleArticles);
      setCategories(sampleCategories);
    } catch (err) {
      console.error('Error loading knowledge base data:', err);
      setError('Failed to load knowledge base data');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      await loadKnowledgeBaseData();
      return;
    }

    try {
      setLoading(true);
      const results = await customerSuccessService.searchKnowledgeBase(
        searchQuery,
        selectedCategory !== 'all' ? selectedCategory : undefined
      );
      setArticles(results);
    } catch (err) {
      console.error('Error searching knowledge base:', err);
      setError('Failed to search knowledge base');
    } finally {
      setLoading(false);
    }
  };

  const filteredArticles = articles.filter(article => {
    if (selectedCategory !== 'all' && article.category !== selectedCategory) {
      return false;
    }
    return true;
  });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getAttachmentIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="h-4 w-4" />;
      case 'document': return <FileText className="h-4 w-4" />;
      case 'link': return <Link className="h-4 w-4" />;
      case 'image': return <Image className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Knowledge Base</h1>
          <p className="text-muted-foreground">Comprehensive help articles and documentation</p>
        </div>
        {(userRole === 'super_admin' || userRole === 'admin') && (
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Article
          </Button>
        )}
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Search and Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search knowledge base..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category.id} value={category.name}>
                    {category.name}
                  </option>
                ))}
              </select>
              <Button onClick={handleSearch}>
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="browse">Browse Articles</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="popular">Popular</TabsTrigger>
        </TabsList>

        <TabsContent value="browse" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {filteredArticles.map((article) => (
              <Card key={article.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg line-clamp-2">{article.title}</CardTitle>
                    <Badge className={getDifficultyColor(article.difficulty)}>
                      {article.difficulty}
                    </Badge>
                  </div>
                  <CardDescription className="line-clamp-3">
                    {article.summary}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Tags */}
                    <div className="flex flex-wrap gap-1">
                      {article.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          <Tag className="h-3 w-3 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                      {article.tags.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{article.tags.length - 3} more
                        </Badge>
                      )}
                    </div>

                    {/* Attachments */}
                    {article.attachments.length > 0 && (
                      <div className="flex gap-2">
                        {article.attachments.slice(0, 3).map((attachment) => (
                          <div key={attachment.id} className="flex items-center gap-1 text-xs text-muted-foreground">
                            {getAttachmentIcon(attachment.type)}
                            <span>{attachment.type}</span>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Metadata */}
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {article.estimatedReadTime} min
                        </div>
                        <div className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          {article.views}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1">
                          <ThumbsUp className="h-3 w-3" />
                          {article.helpfulVotes}
                        </div>
                        <div className="flex items-center gap-1">
                          <ThumbsDown className="h-3 w-3" />
                          {article.notHelpfulVotes}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <BookOpen className="h-4 w-4 mr-2" />
                        Read
                      </Button>
                      {(userRole === 'super_admin' || userRole === 'admin') && (
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredArticles.length === 0 && (
            <div className="text-center py-12">
              <BookOpen className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">No articles found</h3>
              <p className="text-muted-foreground">
                {searchQuery ? 'Try adjusting your search terms or filters.' : 'No articles available in this category.'}
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category) => (
              <Card key={category.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                      <BookOpen className="h-4 w-4 text-primary" />
                    </div>
                    {category.name}
                  </CardTitle>
                  <CardDescription>{category.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <Badge variant="outline">{category.articleCount} articles</Badge>
                    <Button variant="ghost" size="sm">
                      Browse →
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="popular" className="space-y-6">
          <div className="space-y-4">
            {articles
              .sort((a, b) => b.views - a.views)
              .slice(0, 10)
              .map((article, index) => (
                <Card key={article.id}>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-4">
                      <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center font-bold text-primary">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium">{article.title}</h3>
                        <p className="text-sm text-muted-foreground">{article.summary}</p>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          {article.views}
                        </div>
                        <div className="flex items-center gap-1">
                          <ThumbsUp className="h-3 w-3" />
                          {article.helpfulVotes}
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        Read
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
