'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Users, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  BookOpen,
  GraduationCap,
  MessageSquare,
  BarChart3,
  Heart,
  Target,
  Award,
  HelpCircle
} from 'lucide-react';
import { CustomerSuccessService, CustomerSuccessProfile } from '@/services/customerSuccessService';

interface CustomerSuccessDashboardProps {
  tenantId: string;
  userRole: string;
}

export default function CustomerSuccessDashboard({ tenantId, userRole }: CustomerSuccessDashboardProps) {
  const [healthReport, setHealthReport] = useState<any>(null);
  const [selectedProfile, setSelectedProfile] = useState<CustomerSuccessProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  const customerSuccessService = CustomerSuccessService.getInstance();

  useEffect(() => {
    loadCustomerSuccessData();
  }, [tenantId]);

  const loadCustomerSuccessData = async () => {
    try {
      setLoading(true);
      setError(null);

      const report = await customerSuccessService.generateCustomerHealthReport(tenantId);
      setHealthReport(report);
    } catch (err) {
      console.error('Error loading customer success data:', err);
      setError('Failed to load customer success data');
    } finally {
      setLoading(false);
    }
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'high': return 'bg-orange-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Customer Success Dashboard</h1>
          <p className="text-muted-foreground">Monitor customer health, engagement, and success metrics</p>
        </div>
        <Button onClick={loadCustomerSuccessData} variant="outline">
          Refresh Data
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{healthReport?.totalUsers || 0}</div>
            <p className="text-xs text-muted-foreground">
              Active customer accounts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Health Score</CardTitle>
            <Heart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getHealthScoreColor(healthReport?.averageHealthScore || 0)}`}>
              {healthReport?.averageHealthScore?.toFixed(1) || 0}
            </div>
            <Progress value={healthReport?.averageHealthScore || 0} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">At-Risk Customers</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {(healthReport?.riskDistribution?.high || 0) + (healthReport?.riskDistribution?.critical || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Requiring immediate attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Onboarding Complete</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {healthReport?.onboardingStatus?.completed || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Successfully onboarded users
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="health">Health Scores</TabsTrigger>
          <TabsTrigger value="onboarding">Onboarding</TabsTrigger>
          <TabsTrigger value="knowledge">Knowledge Base</TabsTrigger>
          <TabsTrigger value="training">Training</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Risk Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Risk Distribution</CardTitle>
                <CardDescription>Customer risk levels across the organization</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {healthReport?.riskDistribution && Object.entries(healthReport.riskDistribution).map(([level, count]) => (
                  <div key={level} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${getRiskLevelColor(level)}`} />
                      <span className="capitalize">{level} Risk</span>
                    </div>
                    <Badge variant="outline">{count as number}</Badge>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Onboarding Status */}
            <Card>
              <CardHeader>
                <CardTitle>Onboarding Progress</CardTitle>
                <CardDescription>User onboarding status breakdown</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {healthReport?.onboardingStatus && Object.entries(healthReport.onboardingStatus).map(([status, count]) => (
                  <div key={status} className="flex items-center justify-between">
                    <span className="capitalize">{status.replace('_', ' ')}</span>
                    <Badge variant="outline">{count as number}</Badge>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Recent Customer Profiles */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Profiles</CardTitle>
              <CardDescription>Recent customer success profiles and health scores</CardDescription>
            </CardHeader>
            <CardContent>
              {healthReport?.profiles?.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">
                  No customer profiles found. Customer profiles are created automatically when users engage with the platform.
                </p>
              ) : (
                <div className="space-y-4">
                  {healthReport?.profiles?.map((profile: CustomerSuccessProfile, index: number) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${getRiskLevelColor(profile.riskLevel)}`} />
                        <div>
                          <h4 className="font-medium">User {profile.userId.slice(-8)}</h4>
                          <p className="text-sm text-muted-foreground">
                            Last active: {new Date(profile.lastActivity).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className={`font-bold ${getHealthScoreColor(profile.healthScore)}`}>
                            {profile.healthScore}
                          </div>
                          <div className="text-xs text-muted-foreground">Health Score</div>
                        </div>
                        <Badge variant={profile.onboardingStatus === 'completed' ? 'default' : 'secondary'}>
                          {profile.onboardingStatus.replace('_', ' ')}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="health" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Heart className="h-5 w-5" />
                Customer Health Monitoring
              </CardTitle>
              <CardDescription>
                Detailed health score analysis and risk factors
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Health score monitoring dashboard coming soon.</p>
                <p className="text-sm">Track customer engagement, feature adoption, and success metrics.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="onboarding" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Customer Onboarding
              </CardTitle>
              <CardDescription>
                Onboarding progress tracking and milestone management
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Onboarding management dashboard coming soon.</p>
                <p className="text-sm">Track onboarding milestones, completion rates, and user progress.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="knowledge" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Knowledge Base Management
              </CardTitle>
              <CardDescription>
                Comprehensive knowledge base with searchable articles and resources
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <HelpCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Knowledge base management coming soon.</p>
                <p className="text-sm">Create, organize, and manage help articles and documentation.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="training" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GraduationCap className="h-5 w-5" />
                Training Programs
              </CardTitle>
              <CardDescription>
                Customer training and certification programs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Award className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Training program management coming soon.</p>
                <p className="text-sm">Create training modules, track progress, and issue certifications.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common customer success management tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <BookOpen className="h-6 w-6" />
              <span>Create KB Article</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <GraduationCap className="h-6 w-6" />
              <span>New Training Program</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <MessageSquare className="h-6 w-6" />
              <span>Customer Outreach</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <BarChart3 className="h-6 w-6" />
              <span>Generate Report</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
