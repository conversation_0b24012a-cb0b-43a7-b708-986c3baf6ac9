/**
 * EVEXA Performance Showcase
 * Comprehensive demonstration of all performance optimizations and components
 */

'use client';

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { VirtualizedList, VirtualizedGrid, VirtualizedTable, InfiniteVirtualizedList } from '@/components/ui/virtualized-list';
import { OptimizedImage, ImageGallery } from '@/components/ui/optimized-image';
import { DebouncedInput, DebouncedTextarea, OptimizedForm } from '@/components/ui/optimized-form';
import { EnhancedPerformanceMonitor } from '@/components/debug/EnhancedPerformanceMonitor';
import { usePerformanceMonitor, useLazyLoad, performanceUtils } from '@/lib/performance-utils';
import { Zap, Image, List, Table, FormInput, Activity, Loader2 } from 'lucide-react';

// Generate mock data for demonstrations
const generateMockData = (count: number) => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    name: `Item ${index + 1}`,
    description: `Description for item ${index + 1}`,
    value: Math.floor(Math.random() * 1000),
    status: ['active', 'inactive', 'pending'][Math.floor(Math.random() * 3)],
    createdAt: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
  }));
};

const generateImageData = (count: number) => {
  return Array.from({ length: count }, (_, index) => ({
    src: `https://picsum.photos/300/200?random=${index}`,
    alt: `Random image ${index + 1}`,
    placeholder: `https://picsum.photos/30/20?random=${index}`,
    aspectRatio: 1.5,
  }));
};

export const PerformanceShowcase: React.FC = () => {
  usePerformanceMonitor('PerformanceShowcase');

  const [listData] = useState(() => generateMockData(10000));
  const [imageData] = useState(() => generateImageData(50));
  const [formData, setFormData] = useState({ name: '', email: '', message: '' });
  const [showMonitor, setShowMonitor] = useState(false);

  // Lazy load section
  const { ref: lazyRef, inView } = useLazyLoad();

  // Memoized table columns
  const tableColumns = useMemo(() => [
    {
      key: 'id' as const,
      header: 'ID',
      width: 80,
    },
    {
      key: 'name' as const,
      header: 'Name',
      width: 200,
    },
    {
      key: 'description' as const,
      header: 'Description',
      width: 300,
    },
    {
      key: 'value' as const,
      header: 'Value',
      width: 100,
      render: (value: number) => `$${value.toLocaleString()}`,
    },
    {
      key: 'status' as const,
      header: 'Status',
      width: 120,
      render: (status: string) => (
        <Badge variant={status === 'active' ? 'default' : status === 'pending' ? 'secondary' : 'outline'}>
          {status}
        </Badge>
      ),
    },
  ], []);

  // Performance measurement
  const measureRenderTime = () => {
    const measure = performanceUtils.measureRender('PerformanceShowcase');
    setTimeout(measure, 0);
  };

  React.useEffect(() => {
    measureRenderTime();
  });

  return (
    <div className="space-y-8 p-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold flex items-center justify-center gap-3">
          <Zap className="h-10 w-10 text-yellow-500" />
          Performance Showcase
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Comprehensive demonstration of EVEXA's performance-optimized components and utilities
        </p>
        <div className="flex justify-center gap-4">
          <Button onClick={() => setShowMonitor(!showMonitor)} variant="outline">
            <Activity className="mr-2 h-4 w-4" />
            {showMonitor ? 'Hide' : 'Show'} Performance Monitor
          </Button>
          <Button onClick={() => window.location.reload()} variant="outline">
            Refresh Page
          </Button>
        </div>
      </div>

      {/* Performance Monitor */}
      {showMonitor && (
        <EnhancedPerformanceMonitor showDetails={true} />
      )}

      <Tabs defaultValue="virtualization" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="virtualization">Virtualization</TabsTrigger>
          <TabsTrigger value="images">Images</TabsTrigger>
          <TabsTrigger value="forms">Forms</TabsTrigger>
          <TabsTrigger value="lazy-loading">Lazy Loading</TabsTrigger>
          <TabsTrigger value="utilities">Utilities</TabsTrigger>
        </TabsList>

        {/* Virtualization Tab */}
        <TabsContent value="virtualization" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <List className="h-5 w-5" />
                Virtualized List
              </CardTitle>
              <CardDescription>
                Efficiently render 10,000 items with virtual scrolling
              </CardDescription>
            </CardHeader>
            <CardContent>
              <VirtualizedList
                items={listData}
                height={400}
                itemHeight={60}
                renderItem={(item, index) => (
                  <div className="flex items-center justify-between p-4 border-b hover:bg-muted/50">
                    <div>
                      <div className="font-medium">{item.name}</div>
                      <div className="text-sm text-muted-foreground">{item.description}</div>
                    </div>
                    <Badge variant="outline">{item.status}</Badge>
                  </div>
                )}
                className="border rounded-lg"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Table className="h-5 w-5" />
                Virtualized Table
              </CardTitle>
              <CardDescription>
                High-performance table with 10,000 rows
              </CardDescription>
            </CardHeader>
            <CardContent>
              <VirtualizedTable
                data={listData}
                columns={tableColumns}
                height={400}
                rowHeight={50}
                onRowClick={(item) => console.log('Clicked:', item)}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Virtualized Grid</CardTitle>
              <CardDescription>
                Grid layout with virtual scrolling
              </CardDescription>
            </CardHeader>
            <CardContent>
              <VirtualizedGrid
                items={listData.slice(0, 1000)}
                height={400}
                width={800}
                columnCount={4}
                rowHeight={120}
                renderItem={(item, rowIndex, colIndex) => (
                  <Card className="h-full">
                    <CardContent className="p-4">
                      <div className="font-medium text-sm">{item.name}</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        ${item.value}
                      </div>
                      <Badge variant="outline" className="mt-2 text-xs">
                        {item.status}
                      </Badge>
                    </CardContent>
                  </Card>
                )}
                className="border rounded-lg mx-auto"
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Images Tab */}
        <TabsContent value="images" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Image className="h-5 w-5" />
                Optimized Images
              </CardTitle>
              <CardDescription>
                Lazy loading, progressive enhancement, and format optimization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {imageData.slice(0, 9).map((image, index) => (
                  <OptimizedImage
                    key={index}
                    src={image.src}
                    alt={image.alt}
                    placeholder={image.placeholder}
                    aspectRatio={image.aspectRatio}
                    className="rounded-lg"
                    lazy={index > 2} // First 3 images load immediately
                    progressive={true}
                    webp={true}
                  />
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Image Gallery</CardTitle>
              <CardDescription>
                Virtualized image gallery with lazy loading
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ImageGallery
                images={imageData}
                columns={4}
                height={400}
                onImageClick={(image, index) => console.log('Image clicked:', index)}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Forms Tab */}
        <TabsContent value="forms" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FormInput className="h-5 w-5" />
                Optimized Forms
              </CardTitle>
              <CardDescription>
                Debounced inputs, real-time validation, and auto-save
              </CardDescription>
            </CardHeader>
            <CardContent>
              <OptimizedForm
                initialData={formData}
                autoSave={true}
                onAutoSave={(data) => console.log('Auto-saved:', data)}
                onSubmit={async (data) => {
                  console.log('Form submitted:', data);
                  await new Promise(resolve => setTimeout(resolve, 2000));
                }}
                validation={{
                  name: (value) => !value ? 'Name is required' : null,
                  email: (value) => {
                    if (!value) return 'Email is required';
                    if (!/\S+@\S+\.\S+/.test(value)) return 'Invalid email format';
                    return null;
                  },
                }}
              >
                <DebouncedInput
                  label="Name"
                  placeholder="Enter your name"
                  value={formData.name}
                  onChange={(value) => setFormData(prev => ({ ...prev, name: value }))}
                  validation={(value) => !value ? 'Name is required' : null}
                />

                <DebouncedInput
                  label="Email"
                  type="email"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChange={(value) => setFormData(prev => ({ ...prev, email: value }))}
                  validation={(value) => {
                    if (!value) return 'Email is required';
                    if (!/\S+@\S+\.\S+/.test(value)) return 'Invalid email format';
                    return null;
                  }}
                />

                <DebouncedTextarea
                  label="Message"
                  placeholder="Enter your message"
                  value={formData.message}
                  onChange={(value) => setFormData(prev => ({ ...prev, message: value }))}
                  maxLength={500}
                  showCharCount={true}
                  rows={4}
                />
              </OptimizedForm>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Lazy Loading Tab */}
        <TabsContent value="lazy-loading" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Intersection Observer</CardTitle>
              <CardDescription>
                Content loads when it comes into view
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                <div className="h-96 bg-muted rounded-lg flex items-center justify-center">
                  <p className="text-muted-foreground">Scroll down to see lazy-loaded content</p>
                </div>
                
                <div ref={lazyRef} className="min-h-[200px] border rounded-lg p-6">
                  {inView ? (
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Lazy Loaded Content</h3>
                      <p>This content was loaded when it came into view!</p>
                      <div className="grid grid-cols-2 gap-4">
                        {Array.from({ length: 4 }, (_, i) => (
                          <OptimizedImage
                            key={i}
                            src={`https://picsum.photos/200/150?random=${i + 100}`}
                            alt={`Lazy loaded image ${i + 1}`}
                            aspectRatio={4/3}
                            className="rounded"
                          />
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Utilities Tab */}
        <TabsContent value="utilities" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Performance Utilities</CardTitle>
              <CardDescription>
                Built-in performance monitoring and optimization tools
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg">
                  <h4 className="font-medium mb-2">Memory Usage</h4>
                  <Button
                    onClick={() => {
                      const memory = performanceUtils.getMemoryUsage();
                      alert(JSON.stringify(memory, null, 2));
                    }}
                    variant="outline"
                    size="sm"
                  >
                    Check Memory
                  </Button>
                </div>
                
                <div className="p-4 border rounded-lg">
                  <h4 className="font-medium mb-2">Bundle Analysis</h4>
                  <Button
                    onClick={() => {
                      const bundle = performanceUtils.analyzeBundleSize();
                      alert(JSON.stringify(bundle, null, 2));
                    }}
                    variant="outline"
                    size="sm"
                  >
                    Analyze Bundle
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PerformanceShowcase;
