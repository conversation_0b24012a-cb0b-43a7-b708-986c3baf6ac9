/**
 * Post-Registration Onboarding Component
 * Guides new users through initial setup after accepting invitation
 */

'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  User, 
  Building, 
  Settings, 
  ArrowRight,
  ArrowLeft,
  Upload,
  Mail,
  Phone,
  Globe,
  Clock,
  Shield
} from 'lucide-react';
import { InvitationRegistrationService } from '@/services/invitationRegistrationService';
import { useAuth } from '@/contexts/auth-context';
import type { EvexUser } from '@/types/firestore';

interface PostRegistrationOnboardingProps {
  user: EvexUser;
  onComplete: () => void;
}

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  completed: boolean;
}

export const PostRegistrationOnboarding: React.FC<PostRegistrationOnboardingProps> = ({
  user,
  onComplete
}) => {
  const router = useRouter();
  const { user: authUser } = useAuth();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [profileData, setProfileData] = useState({
    profileImageUrl: user.profileImageUrl || '',
    phone: user.phone || '',
    bio: '',
    workPreferences: {
      preferredWorkingHours: '9:00 AM - 5:00 PM',
      communicationPreference: 'email',
      availabilityStatus: 'available'
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: true,
      taskReminders: true,
      weeklyDigest: true
    }
  });

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to EVEXA',
      description: 'Let\'s get you set up with your new account',
      icon: <User className="h-6 w-6" />,
      completed: false
    },
    {
      id: 'profile',
      title: 'Complete Your Profile',
      description: 'Add your photo and contact information',
      icon: <User className="h-6 w-6" />,
      completed: false
    },
    {
      id: 'preferences',
      title: 'Set Your Preferences',
      description: 'Configure your work preferences and notifications',
      icon: <Settings className="h-6 w-6" />,
      completed: false
    },
    {
      id: 'team',
      title: 'Meet Your Team',
      description: 'Learn about your role and team structure',
      icon: <Building className="h-6 w-6" />,
      completed: false
    }
  ];

  const progress = ((currentStep + 1) / steps.length) * 100;

  const handleNext = async () => {
    if (currentStep === steps.length - 1) {
      await handleComplete();
    } else {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleComplete = async () => {
    try {
      setLoading(true);
      setError(null);

      // Update user profile with onboarding data
      const result = await InvitationRegistrationService.updateUserProfile(user.id, {
        profileImageUrl: profileData.profileImageUrl,
        phone: profileData.phone,
        preferences: {
          ...user.preferences,
          notifications: profileData.notifications.emailNotifications,
          workingHours: profileData.workPreferences.preferredWorkingHours
        },
        onboardingCompleted: true,
        onboardingCompletedAt: new Date()
      });

      if (result.success) {
        onComplete();
      } else {
        setError(result.error || 'Failed to complete onboarding');
      }

    } catch (error) {
      console.error('Error completing onboarding:', error);
      setError('Failed to complete onboarding. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderWelcomeStep = () => (
    <div className="text-center space-y-6">
      <div className="flex items-center justify-center mb-6">
        <div className="bg-blue-100 p-4 rounded-full">
          <CheckCircle className="h-12 w-12 text-blue-600" />
        </div>
      </div>
      
      <div>
        <h2 className="text-2xl font-bold mb-2">Welcome to EVEXA, {user.firstName}!</h2>
        <p className="text-muted-foreground mb-4">
          Your account has been successfully created. Let's get you set up to start collaborating with your team.
        </p>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Email:</span>
          <span className="text-sm">{user.email}</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Role:</span>
          <Badge variant="secondary">{user.department}</Badge>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Team:</span>
          <span className="text-sm">{user.tenantId}</span>
        </div>
      </div>

      {authUser?.emailVerified === false && (
        <Alert>
          <Mail className="h-4 w-4" />
          <AlertDescription>
            Please check your email and verify your account for full access to all features.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );

  const renderProfileStep = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-bold mb-2">Complete Your Profile</h2>
        <p className="text-muted-foreground">
          Add your photo and contact information to help your team recognize and connect with you.
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="profileImage">Profile Photo</Label>
          <div className="flex items-center gap-4 mt-2">
            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
              {profileData.profileImageUrl ? (
                <img 
                  src={profileData.profileImageUrl} 
                  alt="Profile" 
                  className="w-16 h-16 rounded-full object-cover"
                />
              ) : (
                <User className="h-8 w-8 text-gray-400" />
              )}
            </div>
            <Button variant="outline" size="sm">
              <Upload className="h-4 w-4 mr-2" />
              Upload Photo
            </Button>
          </div>
        </div>

        <div>
          <Label htmlFor="phone">Phone Number</Label>
          <div className="relative">
            <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="phone"
              type="tel"
              value={profileData.phone}
              onChange={(e) => setProfileData(prev => ({ ...prev, phone: e.target.value }))}
              placeholder="Your phone number"
              className="pl-10"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="bio">Bio (Optional)</Label>
          <Textarea
            id="bio"
            value={profileData.bio}
            onChange={(e) => setProfileData(prev => ({ ...prev, bio: e.target.value }))}
            placeholder="Tell your team a bit about yourself..."
            rows={3}
          />
        </div>
      </div>
    </div>
  );

  const renderPreferencesStep = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-bold mb-2">Set Your Preferences</h2>
        <p className="text-muted-foreground">
          Configure your work preferences and notification settings.
        </p>
      </div>

      <div className="space-y-6">
        <div>
          <h3 className="font-medium mb-3">Work Preferences</h3>
          <div className="space-y-4">
            <div>
              <Label htmlFor="workingHours">Preferred Working Hours</Label>
              <div className="relative">
                <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="workingHours"
                  value={profileData.workPreferences.preferredWorkingHours}
                  onChange={(e) => setProfileData(prev => ({
                    ...prev,
                    workPreferences: { ...prev.workPreferences, preferredWorkingHours: e.target.value }
                  }))}
                  placeholder="e.g., 9:00 AM - 5:00 PM"
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="communication">Communication Preference</Label>
              <select
                id="communication"
                value={profileData.workPreferences.communicationPreference}
                onChange={(e) => setProfileData(prev => ({
                  ...prev,
                  workPreferences: { ...prev.workPreferences, communicationPreference: e.target.value }
                }))}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
              >
                <option value="email">Email</option>
                <option value="slack">Slack</option>
                <option value="teams">Microsoft Teams</option>
                <option value="phone">Phone</option>
              </select>
            </div>
          </div>
        </div>

        <div>
          <h3 className="font-medium mb-3">Notification Settings</h3>
          <div className="space-y-3">
            {Object.entries(profileData.notifications).map(([key, value]) => (
              <div key={key} className="flex items-center justify-between">
                <Label htmlFor={key} className="text-sm">
                  {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </Label>
                <input
                  type="checkbox"
                  id={key}
                  checked={value}
                  onChange={(e) => setProfileData(prev => ({
                    ...prev,
                    notifications: { ...prev.notifications, [key]: e.target.checked }
                  }))}
                  className="h-4 w-4 rounded border-gray-300"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderTeamStep = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-bold mb-2">Meet Your Team</h2>
        <p className="text-muted-foreground">
          Learn about your role and how you'll collaborate with your team.
        </p>
      </div>

      <div className="grid gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Shield className="h-8 w-8 text-blue-500" />
              <div>
                <h3 className="font-medium">Your Role</h3>
                <p className="text-sm text-muted-foreground">{user.department}</p>
                <Badge variant="outline" className="mt-1">{user.role}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Building className="h-8 w-8 text-green-500" />
              <div>
                <h3 className="font-medium">Team Access</h3>
                <p className="text-sm text-muted-foreground">
                  You have access to team collaboration tools, project management, and communication channels.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Globe className="h-8 w-8 text-purple-500" />
              <div>
                <h3 className="font-medium">Getting Started</h3>
                <p className="text-sm text-muted-foreground">
                  Explore the dashboard, check your assigned tasks, and connect with your team members.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderWelcomeStep();
      case 1:
        return renderProfileStep();
      case 2:
        return renderPreferencesStep();
      case 3:
        return renderTeamStep();
      default:
        return renderWelcomeStep();
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <div className="flex items-center justify-between mb-4">
            <div>
              <CardTitle>Account Setup</CardTitle>
              <CardDescription>
                Step {currentStep + 1} of {steps.length}
              </CardDescription>
            </div>
            <Badge variant="outline">{Math.round(progress)}% Complete</Badge>
          </div>
          <Progress value={progress} className="w-full" />
        </CardHeader>

        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {renderCurrentStep()}

          <div className="flex items-center justify-between pt-6">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 0}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>

            <Button
              onClick={handleNext}
              disabled={loading}
            >
              {currentStep === steps.length - 1 ? (
                loading ? 'Completing...' : 'Complete Setup'
              ) : (
                <>
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
