"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  CreditCard,
  DollarSign,
  Calendar,
  Users,
  Database,
  Mail,
  Activity,
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  Download,
  Settings,
  FileText,
  Building
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { auth } from '@/lib/firebase';
import { toast } from 'sonner';
import type { TenantSubscription, SubscriptionPlan, BillingInvoice } from '@/services/subscriptionService';
import EnhancedInvoiceManagement from './EnhancedInvoiceManagement';
import AdvancedUsageAnalytics from './AdvancedUsageAnalytics';
import AutomatedDunningDashboard from './AutomatedDunningDashboard';
import EnterpriseBillingDashboard from './EnterpriseBillingDashboard';

interface BillingDashboardProps {
  tenantId: string;
}

export function BillingDashboard({ tenantId }: BillingDashboardProps) {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState<TenantSubscription | null>(null);
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [invoices, setInvoices] = useState<BillingInvoice[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadBillingData();
  }, [tenantId]);

  const loadBillingData = async () => {
    try {
      setLoading(true);
      const firebaseUser = auth.currentUser;
      if (!firebaseUser) {
        throw new Error('User not authenticated');
      }
      const token = await firebaseUser.getIdToken();
      
      const [subscriptionRes, plansRes, invoicesRes] = await Promise.all([
        fetch(`/api/billing?action=subscription&tenantId=${tenantId}`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        fetch(`/api/billing?action=plans&tenantId=${tenantId}`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        fetch(`/api/billing?action=invoices&tenantId=${tenantId}`, {
          headers: { Authorization: `Bearer ${token}` }
        })
      ]);

      if (subscriptionRes.ok) {
        const subData = await subscriptionRes.json();
        setSubscription(subData);
      }

      if (plansRes.ok) {
        const plansData = await plansRes.json();
        setPlans(plansData);
      }

      if (invoicesRes.ok) {
        const invoicesData = await invoicesRes.json();
        setInvoices(invoicesData);
      }
    } catch (error) {
      console.error('Error loading billing data:', error);
      toast.error('Failed to load billing information');
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async (planId: string) => {
    try {
      const firebaseUser = auth.currentUser;
      if (!firebaseUser) {
        throw new Error('User not authenticated');
      }
      const token = await firebaseUser.getIdToken();
      const plan = plans.find(p => p.id === planId);
      
      if (!plan) return;

      const response = await fetch('/api/billing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          action: 'create-checkout-session',
          tenantId,
          priceId: plan.stripePriceId,
          successUrl: `${window.location.origin}/billing/success`,
          cancelUrl: `${window.location.origin}/billing/cancel`
        })
      });

      if (response.ok) {
        const { url } = await response.json();
        window.location.href = url;
      } else {
        throw new Error('Failed to create checkout session');
      }
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      toast.error('Failed to start upgrade process');
    }
  };

  const handleManageBilling = async () => {
    try {
      const token = await user?.getIdToken();
      
      const response = await fetch('/api/billing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          action: 'create-billing-portal',
          tenantId,
          customerId: subscription?.billing.customerId,
          returnUrl: window.location.href
        })
      });

      if (response.ok) {
        const { url } = await response.json();
        window.location.href = url;
      } else {
        throw new Error('Failed to create billing portal session');
      }
    } catch (error) {
      console.error('Error opening billing portal:', error);
      toast.error('Failed to open billing management');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'default';
      case 'trialing': return 'secondary';
      case 'past_due': return 'destructive';
      case 'canceled': return 'outline';
      default: return 'secondary';
    }
  };

  const getUsagePercentage = (used: number, limit: number) => {
    if (limit === -1) return 0; // Unlimited
    return Math.min((used / limit) * 100, 100);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const currentPlan = plans.find(p => p.id === subscription?.planId);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Billing & Subscription</h2>
          <p className="text-muted-foreground">
            Manage your subscription, usage, and billing information
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleManageBilling}>
            <Settings className="h-4 w-4 mr-2" />
            Manage Billing
          </Button>
        </div>
      </div>

      {/* Current Subscription */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Current Subscription
            </span>
            <Badge variant={getStatusColor(subscription?.status || 'inactive')}>
              {subscription?.status || 'No Subscription'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {subscription && currentPlan ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <h3 className="font-semibold text-lg">{currentPlan.displayName}</h3>
                <p className="text-muted-foreground">{currentPlan.description}</p>
                <div className="mt-2">
                  <span className="text-2xl font-bold">
                    ${subscription.billingCycle === 'yearly' 
                      ? currentPlan.pricing.yearly 
                      : currentPlan.pricing.monthly}
                  </span>
                  <span className="text-muted-foreground">
                    /{subscription.billingCycle === 'yearly' ? 'year' : 'month'}
                  </span>
                </div>
              </div>
              
              <div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Current Period</span>
                    <span>{new Date(subscription.currentPeriodStart).toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Renewal Date</span>
                    <span>{new Date(subscription.currentPeriodEnd).toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Next Payment</span>
                    <span>${subscription.billing.amountDue}</span>
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-2">
                <Button onClick={() => handleUpgrade('professional')} className="w-full">
                  Upgrade Plan
                </Button>
                <Button variant="outline" onClick={handleManageBilling} className="w-full">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Manage Billing
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Active Subscription</h3>
              <p className="text-muted-foreground mb-4">
                Choose a plan to get started with EVEXA
              </p>
              <Button onClick={() => handleUpgrade('starter')}>
                View Plans
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <Tabs defaultValue="usage" className="space-y-4">
        <TabsList>
          <TabsTrigger value="usage">Usage & Limits</TabsTrigger>
          <TabsTrigger value="invoices">Invoices</TabsTrigger>
          <TabsTrigger value="enhanced-invoices">
            <FileText className="h-4 w-4 mr-2" />
            Enhanced Invoices
          </TabsTrigger>
          <TabsTrigger value="usage-analytics">
            <Activity className="h-4 w-4 mr-2" />
            Usage Analytics
          </TabsTrigger>
          <TabsTrigger value="dunning">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Dunning Management
          </TabsTrigger>
          <TabsTrigger value="enterprise">
            <Building className="h-4 w-4 mr-2" />
            Enterprise Billing
          </TabsTrigger>
          <TabsTrigger value="plans">Available Plans</TabsTrigger>
        </TabsList>

        <TabsContent value="usage" className="space-y-4">
          {subscription && currentPlan && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Users
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{subscription.usage.users}</span>
                      <span>{currentPlan.features.maxUsers === -1 ? '∞' : currentPlan.features.maxUsers}</span>
                    </div>
                    <Progress 
                      value={getUsagePercentage(subscription.usage.users, currentPlan.features.maxUsers)} 
                      className="h-2" 
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Exhibitions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{subscription.usage.exhibitions}</span>
                      <span>{currentPlan.features.maxExhibitions === -1 ? '∞' : currentPlan.features.maxExhibitions}</span>
                    </div>
                    <Progress 
                      value={getUsagePercentage(subscription.usage.exhibitions, currentPlan.features.maxExhibitions)} 
                      className="h-2" 
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <Database className="h-4 w-4" />
                    Storage
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{subscription.usage.storageUsedGB.toFixed(1)} GB</span>
                      <span>{currentPlan.features.storageGB} GB</span>
                    </div>
                    <Progress 
                      value={getUsagePercentage(subscription.usage.storageUsedGB, currentPlan.features.storageGB)} 
                      className="h-2" 
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <Activity className="h-4 w-4" />
                    API Calls
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{subscription.usage.apiCallsThisMonth.toLocaleString()}</span>
                      <span>{currentPlan.features.apiCallsPerMonth.toLocaleString()}</span>
                    </div>
                    <Progress 
                      value={getUsagePercentage(subscription.usage.apiCallsThisMonth, currentPlan.features.apiCallsPerMonth)} 
                      className="h-2" 
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Emails
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{subscription.usage.emailsSentThisMonth.toLocaleString()}</span>
                      <span>{currentPlan.features.emailsPerMonth.toLocaleString()}</span>
                    </div>
                    <Progress 
                      value={getUsagePercentage(subscription.usage.emailsSentThisMonth, currentPlan.features.emailsPerMonth)} 
                      className="h-2" 
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="invoices" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Invoices</CardTitle>
            </CardHeader>
            <CardContent>
              {invoices.length > 0 ? (
                <div className="space-y-4">
                  {invoices.map((invoice) => (
                    <div key={invoice.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <div className="font-medium">#{invoice.invoiceNumber}</div>
                        <div className="text-sm text-muted-foreground">
                          Due: {new Date(invoice.dueDate).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">${invoice.amount}</div>
                        <Badge variant={invoice.status === 'paid' ? 'default' : 'destructive'}>
                          {invoice.status}
                        </Badge>
                      </div>
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Invoices</h3>
                  <p className="text-muted-foreground">
                    Your invoices will appear here once billing begins
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="enhanced-invoices" className="space-y-4">
          <EnhancedInvoiceManagement />
        </TabsContent>

        <TabsContent value="usage-analytics" className="space-y-4">
          <AdvancedUsageAnalytics />
        </TabsContent>

        <TabsContent value="dunning" className="space-y-4">
          <AutomatedDunningDashboard />
        </TabsContent>

        <TabsContent value="enterprise" className="space-y-4">
          <EnterpriseBillingDashboard />
        </TabsContent>

        <TabsContent value="plans" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {plans.map((plan) => (
              <Card key={plan.id} className={`relative ${subscription?.planId === plan.id ? 'ring-2 ring-primary' : ''}`}>
                {subscription?.planId === plan.id && (
                  <Badge className="absolute -top-2 left-4">Current Plan</Badge>
                )}
                <CardHeader>
                  <CardTitle>{plan.displayName}</CardTitle>
                  <div className="text-2xl font-bold">
                    ${plan.pricing.monthly}
                    <span className="text-sm font-normal text-muted-foreground">/month</span>
                  </div>
                  <p className="text-sm text-muted-foreground">{plan.description}</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Users</span>
                      <span>{plan.features.maxUsers === -1 ? 'Unlimited' : plan.features.maxUsers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Exhibitions</span>
                      <span>{plan.features.maxExhibitions === -1 ? 'Unlimited' : plan.features.maxExhibitions}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Storage</span>
                      <span>{plan.features.storageGB} GB</span>
                    </div>
                    <div className="flex justify-between">
                      <span>API Calls</span>
                      <span>{plan.features.apiCallsPerMonth.toLocaleString()}/month</span>
                    </div>
                  </div>
                  
                  <Button 
                    className="w-full mt-4" 
                    onClick={() => handleUpgrade(plan.id)}
                    disabled={subscription?.planId === plan.id}
                  >
                    {subscription?.planId === plan.id ? 'Current Plan' : 'Select Plan'}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
