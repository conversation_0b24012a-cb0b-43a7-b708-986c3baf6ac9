'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  Check, 
  Crown, 
  Zap, 
  Star, 
  Building2,
  ArrowRight,
  Sparkles
} from 'lucide-react';
import { subscriptionService, type SubscriptionPlan } from '@/services/subscriptionService';
import { useTenantAware } from '@/hooks/useTenantAware';

interface PricingPlansProps {
  onSelectPlan?: (planId: string, isYearly: boolean) => void;
  showCurrentPlan?: boolean;
  compact?: boolean;
}

export default function PricingPlans({ 
  onSelectPlan, 
  showCurrentPlan = true,
  compact = false 
}: PricingPlansProps) {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [isYearly, setIsYearly] = useState(false);
  const { isOnPlan, tenant } = useTenantAware();

  useEffect(() => {
    loadPlans();
  }, []);

  const loadPlans = async () => {
    try {
      setLoading(true);
      const availablePlans = await subscriptionService.getAvailablePlans();
      setPlans(availablePlans);
    } catch (error) {
      console.error('Error loading plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'free':
        return <Star className="h-6 w-6 text-gray-500" />;
      case 'starter':
        return <Zap className="h-6 w-6 text-blue-500" />;
      case 'professional':
        return <Crown className="h-6 w-6 text-purple-500" />;
      case 'enterprise':
        return <Building2 className="h-6 w-6 text-gold-500" />;
      default:
        return <Star className="h-6 w-6 text-gray-500" />;
    }
  };

  const getPlanColor = (planId: string) => {
    switch (planId) {
      case 'free':
        return 'border-gray-200 hover:border-gray-300';
      case 'starter':
        return 'border-blue-200 hover:border-blue-300';
      case 'professional':
        return 'border-purple-200 hover:border-purple-300 ring-2 ring-purple-100';
      case 'enterprise':
        return 'border-yellow-200 hover:border-yellow-300';
      default:
        return 'border-gray-200 hover:border-gray-300';
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(price);
  };

  const getFeatureList = (plan: SubscriptionPlan) => {
    const features = [];
    
    // Core limits
    if (plan.features.maxExhibitions === -1) {
      features.push('Unlimited exhibitions');
    } else {
      features.push(`${plan.features.maxExhibitions} exhibitions`);
    }
    
    if (plan.features.maxEvents === -1) {
      features.push('Unlimited events');
    } else {
      features.push(`${plan.features.maxEvents} events`);
    }
    
    if (plan.features.maxUsers === -1) {
      features.push('Unlimited team members');
    } else {
      features.push(`${plan.features.maxUsers} team members`);
    }
    
    features.push(`${plan.features.storageGB}GB storage`);
    
    // Premium features
    if (plan.features.advancedAnalytics) features.push('Advanced analytics');
    if (plan.features.customBranding) features.push('Custom branding');
    if (plan.features.whiteLabeling) features.push('White labeling');
    if (plan.features.apiAccess) features.push('API access');
    if (plan.features.workflowAutomation) features.push('Workflow automation');
    if (plan.features.aiFeatures) features.push('AI features');
    if (plan.features.prioritySupport) features.push('Priority support');
    if (plan.features.ssoIntegration) features.push('SSO integration');
    if (plan.features.auditLogs) features.push('Audit logs');
    
    return features.slice(0, compact ? 6 : 10);
  };

  const handleSelectPlan = (plan: SubscriptionPlan) => {
    if (onSelectPlan) {
      onSelectPlan(plan.id, isYearly);
    } else {
      // Default action - redirect to checkout
      const priceId = isYearly ? plan.stripeYearlyPriceId : plan.stripePriceId;
      if (priceId) {
        window.location.href = `/billing/checkout?priceId=${priceId}`;
      }
    }
  };

  if (loading) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {[1, 2, 3, 4, 5].map((j) => (
                  <div key={j} className="h-3 bg-gray-200 rounded"></div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Billing Toggle */}
      <div className="flex items-center justify-center space-x-4">
        <span className={`text-sm ${!isYearly ? 'font-medium' : 'text-muted-foreground'}`}>
          Monthly
        </span>
        <Switch
          checked={isYearly}
          onCheckedChange={setIsYearly}
        />
        <span className={`text-sm ${isYearly ? 'font-medium' : 'text-muted-foreground'}`}>
          Yearly
        </span>
        {isYearly && (
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            Save up to 17%
          </Badge>
        )}
      </div>

      {/* Plans Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {plans.map((plan) => {
          const isCurrentPlan = showCurrentPlan && isOnPlan(plan.tier as any);
          const price = isYearly ? plan.pricing.yearly : plan.pricing.monthly;
          const features = getFeatureList(plan);

          return (
            <Card 
              key={plan.id} 
              className={`relative transition-all duration-200 ${getPlanColor(plan.id)} ${
                plan.isPopular ? 'scale-105' : ''
              }`}
            >
              {plan.isPopular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-purple-500 text-white px-3 py-1">
                    <Sparkles className="h-3 w-3 mr-1" />
                    Most Popular
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-3">
                  {getPlanIcon(plan.id)}
                </div>
                <CardTitle className="text-xl">{plan.displayName}</CardTitle>
                <CardDescription className="text-sm">
                  {plan.description}
                </CardDescription>
                
                <div className="pt-4">
                  <div className="text-3xl font-bold">
                    {formatPrice(price)}
                    {price > 0 && (
                      <span className="text-sm font-normal text-muted-foreground">
                        /{isYearly ? 'year' : 'month'}
                      </span>
                    )}
                  </div>
                  {isYearly && price > 0 && plan.pricing.yearlyDiscount > 0 && (
                    <div className="text-sm text-green-600">
                      Save {plan.pricing.yearlyDiscount}% annually
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Features List */}
                <ul className="space-y-2">
                  {features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm">
                      <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* Action Button */}
                <div className="pt-4">
                  {isCurrentPlan ? (
                    <Button variant="outline" className="w-full" disabled>
                      Current Plan
                    </Button>
                  ) : (
                    <Button 
                      onClick={() => handleSelectPlan(plan)}
                      className="w-full"
                      variant={plan.isPopular ? 'default' : 'outline'}
                    >
                      {plan.id === 'free' ? 'Get Started' : 'Upgrade Now'}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  )}
                </div>

                {/* Add-ons (if any) */}
                {plan.addOns && plan.addOns.length > 0 && (
                  <div className="pt-2 border-t">
                    <p className="text-xs text-muted-foreground mb-2">Add-ons available:</p>
                    <div className="space-y-1">
                      {plan.addOns.slice(0, 2).map((addon) => (
                        <div key={addon.id} className="text-xs text-muted-foreground">
                          {addon.name}: {formatPrice(addon.price)} {addon.unit}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Enterprise Contact */}
      <div className="text-center pt-8 border-t">
        <h3 className="text-lg font-semibold mb-2">Need a custom solution?</h3>
        <p className="text-muted-foreground mb-4">
          Contact us for enterprise pricing and custom features tailored to your needs.
        </p>
        <Button variant="outline">
          Contact Sales
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
