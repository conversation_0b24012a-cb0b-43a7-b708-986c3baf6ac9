'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Crown, 
  Zap, 
  TrendingUp, 
  Users, 
  Calendar, 
  Database,
  ArrowRight,
  Sparkles
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { useTenantAware } from '@/hooks/useTenantAware';
import { useSubscriptionLimits } from '@/hooks/useSubscriptionLimits';
import { getTierComparison, formatPrice } from '@/lib/subscription-limits';

interface UpgradePromptProps {
  trigger: 'limit_reached' | 'feature_locked' | 'usage_warning' | 'trial_ending';
  feature?: string;
  currentUsage?: number;
  limit?: number;
  onUpgrade?: () => void;
  onDismiss?: () => void;
  compact?: boolean;
  description?: string;
  showComparison?: boolean;
}

export default function UpgradePrompt({
  trigger,
  feature,
  currentUsage,
  limit,
  onUpgrade,
  onDismiss,
  compact = false,
  description,
  showComparison = false
}: UpgradePromptProps) {
  const { user } = useAuth();

  // SUPER ADMIN BYPASS: Super admin should never see upgrade prompts
  const { isSuperAdmin } = require('@/services/superAdminService');
  if (user && isSuperAdmin(user.id)) {
    return null; // Don't render anything for super admin
  }

  const {
    tenant,
    isOnPlan,
    getTrialDaysRemaining,
    isInTrial,
    getCurrentUsage,
    getUsageLimits
  } = useTenantAware();

  const {
    subscriptionTier,
    usage: subscriptionUsage,
    limits: subscriptionLimits,
    isLoading
  } = useSubscriptionLimits();

  const usage = getCurrentUsage();
  const limits = getUsageLimits();
  const trialDays = getTrialDaysRemaining();
  const tiers = getTierComparison();

  const getPromptContent = () => {
    switch (trigger) {
      case 'limit_reached':
        return {
          icon: <Database className="h-6 w-6 text-red-500" />,
          title: `${feature} Limit Reached`,
          description: `You've reached your ${feature?.toLowerCase()} limit (${currentUsage}/${limit}). Upgrade to continue adding more.`,
          urgency: 'high',
          ctaText: 'Upgrade Now',
          benefits: ['Unlimited resources', 'Advanced features', 'Priority support']
        };

      case 'feature_locked':
        return {
          icon: <Crown className="h-6 w-6 text-amber-500" />,
          title: `${feature} is a Premium Feature`,
          description: `Unlock ${feature?.toLowerCase()} and other advanced features with a paid plan.`,
          urgency: 'medium',
          ctaText: 'Unlock Feature',
          benefits: ['Advanced analytics', 'Custom branding', 'API access']
        };

      case 'usage_warning':
        return {
          icon: <TrendingUp className="h-6 w-6 text-orange-500" />,
          title: 'Approaching Usage Limit',
          description: `You're using ${Math.round((currentUsage! / limit!) * 100)}% of your ${feature?.toLowerCase()} limit.`,
          urgency: 'medium',
          ctaText: 'Upgrade Plan',
          benefits: ['Higher limits', 'Better performance', 'More features']
        };

      case 'trial_ending':
        return {
          icon: <Sparkles className="h-6 w-6 text-blue-500" />,
          title: `Trial Ending in ${trialDays} Days`,
          description: 'Continue enjoying EVEXA with full access to all features.',
          urgency: trialDays <= 3 ? 'high' : 'medium',
          ctaText: 'Choose Your Plan',
          benefits: ['Uninterrupted access', 'Full feature set', 'Premium support']
        };

      default:
        return {
          icon: <Zap className="h-6 w-6 text-blue-500" />,
          title: 'Upgrade Your Plan',
          description: 'Get more out of EVEXA with advanced features and higher limits.',
          urgency: 'low',
          ctaText: 'View Plans',
          benefits: ['More resources', 'Advanced features', 'Better support']
        };
    }
  };

  const content = getPromptContent();
  const urgencyColors = {
    high: 'border-red-200 bg-red-50',
    medium: 'border-orange-200 bg-orange-50',
    low: 'border-blue-200 bg-blue-50'
  };

  const handleUpgrade = () => {
    if (onUpgrade) {
      onUpgrade();
    } else {
      // Default upgrade action - redirect to billing
      window.location.href = '/billing';
    }
  };

  if (compact) {
    return (
      <div className={`flex items-center justify-between p-3 rounded-lg border ${urgencyColors[content.urgency]}`}>
        <div className="flex items-center space-x-3">
          {content.icon}
          <div>
            <p className="font-medium text-sm">{content.title}</p>
            <p className="text-xs text-muted-foreground">{content.description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {onDismiss && (
            <Button variant="ghost" size="sm" onClick={onDismiss}>
              Later
            </Button>
          )}
          <Button size="sm" onClick={handleUpgrade}>
            {content.ctaText}
            <ArrowRight className="ml-1 h-3 w-3" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <Card className={`${urgencyColors[content.urgency]} border-2`}>
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            {content.icon}
            <div>
              <CardTitle className="text-lg">{content.title}</CardTitle>
              <CardDescription className="mt-1">
                {content.description}
              </CardDescription>
            </div>
          </div>
          {content.urgency === 'high' && (
            <Badge variant="destructive" className="animate-pulse">
              Action Required
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Usage Progress (if applicable) */}
        {currentUsage !== undefined && limit !== undefined && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Current Usage</span>
              <span>{currentUsage} / {limit === -1 ? '∞' : limit}</span>
            </div>
            <Progress 
              value={limit === -1 ? 0 : (currentUsage / limit) * 100} 
              className="h-2"
            />
          </div>
        )}

        {/* Benefits */}
        <div className="space-y-2">
          <p className="font-medium text-sm">What you'll get:</p>
          <ul className="space-y-1">
            {content.benefits.map((benefit, index) => (
              <li key={index} className="flex items-center text-sm text-muted-foreground">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2" />
                {benefit}
              </li>
            ))}
          </ul>
        </div>

        {/* Current Plan Info */}
        {tenant && (
          <div className="flex items-center justify-between text-sm bg-white/50 p-3 rounded-lg">
            <span>Current Plan:</span>
            <Badge variant="outline">
              {isOnPlan('free') ? 'Free' : 
               isOnPlan('starter') ? 'Starter' : 
               isOnPlan('professional') ? 'Professional' : 'Enterprise'}
            </Badge>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-3 pt-2">
          <Button onClick={handleUpgrade} className="flex-1">
            {content.ctaText}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
          {onDismiss && (
            <Button variant="outline" onClick={onDismiss}>
              Maybe Later
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Named export for compatibility
export { UpgradePrompt };

// Usage examples:
// <UpgradePrompt trigger="limit_reached" feature="Exhibitions" currentUsage={2} limit={2} />
// <UpgradePrompt trigger="feature_locked" feature="Advanced Analytics" />
// <UpgradePrompt trigger="trial_ending" compact />
