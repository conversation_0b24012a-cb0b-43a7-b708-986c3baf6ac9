"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/contexts/auth-context';
// import { useTenant } from '@/hooks/useTenant';
import { auth } from '@/lib/firebase';
import { toast } from 'sonner';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import {
  Globe,
  Calculator,
  FileText,
  Building,
  DollarSign,
  TrendingUp,
  CheckCircle,
  Clock,
  AlertTriangle,
  Plus,
  Eye,
  Edit,
  Trash2,
  Download,
  RefreshCw,
  CreditCard,
  Receipt,
  Settings
} from 'lucide-react';
import { 
  CurrencyConfig, 
  TaxConfiguration, 
  PurchaseOrder, 
  EnterpriseContract,
  TaxCalculationResult 
} from '@/services/enterpriseBillingService';

interface EnterpriseBillingDashboardProps {
  className?: string;
}

const COLORS = ['#2563eb', '#dc2626', '#059669', '#d97706', '#7c3aed'];

export default function EnterpriseBillingDashboard({ className }: EnterpriseBillingDashboardProps) {
  const { user } = useAuth();
  // const { tenantId } = useTenant();
  const tenantId = 'default-tenant'; // Temporary fallback
  const [loading, setLoading] = useState(true);
  const [currencies, setCurrencies] = useState<CurrencyConfig[]>([]);
  const [taxConfig, setTaxConfig] = useState<TaxConfiguration | null>(null);
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [contracts, setContracts] = useState<EnterpriseContract[]>([]);
  const [selectedTab, setSelectedTab] = useState('overview');
  
  // Tax Calculator State
  const [taxCalculator, setTaxCalculator] = useState({
    amount: '',
    currency: 'USD',
    country: 'US',
    state: '',
    categories: '',
    result: null as TaxCalculationResult | null
  });

  // Currency Converter State
  const [currencyConverter, setCurrencyConverter] = useState({
    amount: '',
    fromCurrency: 'USD',
    toCurrency: 'EUR',
    result: null as any
  });

  useEffect(() => {
    if (user && tenantId) {
      loadEnterpriseBillingData();
    }
  }, [user, tenantId]);

  const loadEnterpriseBillingData = async () => {
    try {
      setLoading(true);
      const firebaseUser = auth.currentUser;
      if (!firebaseUser) {
        throw new Error('User not authenticated');
      }
      const token = await firebaseUser.getIdToken();
      
      const [currenciesRes, taxConfigRes, purchaseOrdersRes, contractsRes] = await Promise.all([
        fetch(`/api/enterprise-billing?action=currencies&tenantId=${tenantId}`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        fetch(`/api/enterprise-billing?action=tax-config&tenantId=${tenantId}`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        fetch(`/api/enterprise-billing?action=purchase-orders&tenantId=${tenantId}`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        fetch(`/api/enterprise-billing?action=contracts&tenantId=${tenantId}`, {
          headers: { Authorization: `Bearer ${token}` }
        })
      ]);

      if (currenciesRes.ok) {
        const currenciesData = await currenciesRes.json();
        setCurrencies(currenciesData);
      }

      if (taxConfigRes.ok) {
        const taxConfigData = await taxConfigRes.json();
        setTaxConfig(taxConfigData);
      }

      if (purchaseOrdersRes.ok) {
        const purchaseOrdersData = await purchaseOrdersRes.json();
        setPurchaseOrders(purchaseOrdersData);
      }

      if (contractsRes.ok) {
        const contractsData = await contractsRes.json();
        setContracts(contractsData);
      }
    } catch (error) {
      console.error('Error loading enterprise billing data:', error);
      toast.error('Failed to load enterprise billing data');
    } finally {
      setLoading(false);
    }
  };

  const handleCalculateTax = async () => {
    if (!taxCalculator.amount || !taxCalculator.currency) {
      toast.error('Please enter amount and currency');
      return;
    }

    try {
      const token = await user?.getIdToken();
      const response = await fetch(
        `/api/enterprise-billing?action=calculate-tax&tenantId=${tenantId}&amount=${taxCalculator.amount}&currency=${taxCalculator.currency}&country=${taxCalculator.country}&state=${taxCalculator.state}&categories=${taxCalculator.categories}`,
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      if (response.ok) {
        const result = await response.json();
        setTaxCalculator(prev => ({ ...prev, result }));
        toast.success('Tax calculated successfully');
      } else {
        throw new Error('Failed to calculate tax');
      }
    } catch (error) {
      console.error('Error calculating tax:', error);
      toast.error('Failed to calculate tax');
    }
  };

  const handleConvertCurrency = async () => {
    if (!currencyConverter.amount || !currencyConverter.fromCurrency || !currencyConverter.toCurrency) {
      toast.error('Please fill in all currency conversion fields');
      return;
    }

    try {
      const token = await user?.getIdToken();
      const response = await fetch(
        `/api/enterprise-billing?action=convert-currency&tenantId=${tenantId}&amount=${currencyConverter.amount}&from=${currencyConverter.fromCurrency}&to=${currencyConverter.toCurrency}`,
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      if (response.ok) {
        const result = await response.json();
        setCurrencyConverter(prev => ({ ...prev, result }));
        toast.success('Currency converted successfully');
      } else {
        throw new Error('Failed to convert currency');
      }
    } catch (error) {
      console.error('Error converting currency:', error);
      toast.error('Failed to convert currency');
    }
  };

  const handleUpdateExchangeRates = async () => {
    try {
      const token = await user?.getIdToken();
      const response = await fetch('/api/enterprise-billing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          action: 'update-exchange-rates',
          tenantId
        })
      });

      if (response.ok) {
        toast.success('Exchange rates updated successfully');
        loadEnterpriseBillingData();
      } else {
        throw new Error('Failed to update exchange rates');
      }
    } catch (error) {
      console.error('Error updating exchange rates:', error);
      toast.error('Failed to update exchange rates');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { color: string; icon: any }> = {
      draft: { color: 'bg-gray-100 text-gray-800', icon: Edit },
      submitted: { color: 'bg-blue-100 text-blue-800', icon: Clock },
      approved: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      rejected: { color: 'bg-red-100 text-red-800', icon: AlertTriangle },
      active: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      expired: { color: 'bg-gray-100 text-gray-800', icon: Clock },
      cancelled: { color: 'bg-red-100 text-red-800', icon: AlertTriangle }
    };

    const config = statusConfig[status] || statusConfig.draft;
    const Icon = config.icon;

    return (
      <Badge className={config.color}>
        <Icon className="h-3 w-3 mr-1" />
        {status.toUpperCase()}
      </Badge>
    );
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(amount);
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Prepare chart data
  const currencyDistribution = currencies.slice(0, 5).map((currency, index) => ({
    name: currency.code,
    value: Math.random() * 100, // This would be actual usage data
    color: COLORS[index % COLORS.length]
  }));

  const poStatusData = [
    { status: 'Draft', count: purchaseOrders.filter(po => po.status === 'draft').length },
    { status: 'Submitted', count: purchaseOrders.filter(po => po.status === 'submitted').length },
    { status: 'Approved', count: purchaseOrders.filter(po => po.status === 'approved').length },
    { status: 'Rejected', count: purchaseOrders.filter(po => po.status === 'rejected').length }
  ];

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Enterprise Billing Features</h2>
            <p className="text-gray-600">Multi-currency support, tax automation, purchase orders, and enterprise contracts</p>
          </div>
          <div className="flex items-center gap-2">
            <Button onClick={handleUpdateExchangeRates} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Update Rates
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Supported Currencies</p>
                  <p className="text-2xl font-bold">{currencies.length}</p>
                </div>
                <Globe className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Active Purchase Orders</p>
                  <p className="text-2xl font-bold">
                    {purchaseOrders.filter(po => ['submitted', 'approved'].includes(po.status)).length}
                  </p>
                </div>
                <FileText className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Active Contracts</p>
                  <p className="text-2xl font-bold">
                    {contracts.filter(contract => contract.status === 'active').length}
                  </p>
                </div>
                <Building className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Tax Automation</p>
                  <p className="text-2xl font-bold">{taxConfig?.automation.enabled ? 'ON' : 'OFF'}</p>
                </div>
                <Calculator className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="currencies">Multi-Currency</TabsTrigger>
            <TabsTrigger value="tax-automation">Tax Automation</TabsTrigger>
            <TabsTrigger value="purchase-orders">Purchase Orders</TabsTrigger>
            <TabsTrigger value="contracts">Enterprise Contracts</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Currency Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={currencyDistribution}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        >
                          {currencyDistribution.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Purchase Order Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={poStatusData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="status" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="count" fill="#2563eb" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="currencies" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Currency Converter */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Currency Converter
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="amount">Amount</Label>
                      <Input
                        id="amount"
                        type="number"
                        value={currencyConverter.amount}
                        onChange={(e) => setCurrencyConverter(prev => ({ ...prev, amount: e.target.value }))}
                        placeholder="Enter amount"
                      />
                    </div>
                    <div>
                      <Label htmlFor="from-currency">From</Label>
                      <Select 
                        value={currencyConverter.fromCurrency} 
                        onValueChange={(value) => setCurrencyConverter(prev => ({ ...prev, fromCurrency: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {currencies.map(currency => (
                            <SelectItem key={currency.id} value={currency.code}>
                              {currency.code} - {currency.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="to-currency">To</Label>
                    <Select 
                      value={currencyConverter.toCurrency} 
                      onValueChange={(value) => setCurrencyConverter(prev => ({ ...prev, toCurrency: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {currencies.map(currency => (
                          <SelectItem key={currency.id} value={currency.code}>
                            {currency.code} - {currency.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Button onClick={handleConvertCurrency} className="w-full">
                    Convert Currency
                  </Button>

                  {currencyConverter.result && (
                    <div className="p-4 bg-green-50 rounded-lg">
                      <div className="text-lg font-semibold">
                        {formatCurrency(parseFloat(currencyConverter.amount), currencyConverter.fromCurrency)} = {' '}
                        {formatCurrency(currencyConverter.result.convertedAmount, currencyConverter.toCurrency)}
                      </div>
                      <div className="text-sm text-gray-600">
                        Rate: {currencyConverter.result.rate.toFixed(4)} | 
                        Last updated: {formatDate(currencyConverter.result.lastUpdated)}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Supported Currencies */}
              <Card>
                <CardHeader>
                  <CardTitle>Supported Currencies</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {currencies.map(currency => (
                      <div key={currency.id} className="flex items-center justify-between p-2 border rounded">
                        <div>
                          <div className="font-medium">{currency.code} - {currency.name}</div>
                          <div className="text-sm text-gray-600">{currency.symbol}</div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm">
                            1 USD = {currency.exchangeRates.USD?.rate.toFixed(4)} {currency.code}
                          </div>
                          <div className="text-xs text-gray-500">
                            {formatDate(currency.exchangeRates.USD?.lastUpdated || new Date())}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="tax-automation" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Tax Calculator */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calculator className="h-5 w-5" />
                    Tax Calculator
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="tax-amount">Amount</Label>
                      <Input
                        id="tax-amount"
                        type="number"
                        value={taxCalculator.amount}
                        onChange={(e) => setTaxCalculator(prev => ({ ...prev, amount: e.target.value }))}
                        placeholder="Enter amount"
                      />
                    </div>
                    <div>
                      <Label htmlFor="tax-currency">Currency</Label>
                      <Select
                        value={taxCalculator.currency}
                        onValueChange={(value) => setTaxCalculator(prev => ({ ...prev, currency: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {currencies.map(currency => (
                            <SelectItem key={currency.id} value={currency.code}>
                              {currency.code}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="tax-country">Country</Label>
                      <Select
                        value={taxCalculator.country}
                        onValueChange={(value) => setTaxCalculator(prev => ({ ...prev, country: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="US">United States</SelectItem>
                          <SelectItem value="CA">Canada</SelectItem>
                          <SelectItem value="GB">United Kingdom</SelectItem>
                          <SelectItem value="DE">Germany</SelectItem>
                          <SelectItem value="FR">France</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="tax-state">State/Province</Label>
                      <Input
                        id="tax-state"
                        value={taxCalculator.state}
                        onChange={(e) => setTaxCalculator(prev => ({ ...prev, state: e.target.value }))}
                        placeholder="e.g., CA, NY, ON"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="tax-categories">Product Categories</Label>
                    <Input
                      id="tax-categories"
                      value={taxCalculator.categories}
                      onChange={(e) => setTaxCalculator(prev => ({ ...prev, categories: e.target.value }))}
                      placeholder="software,services,subscriptions"
                    />
                  </div>

                  <Button onClick={handleCalculateTax} className="w-full">
                    Calculate Tax
                  </Button>

                  {taxCalculator.result && (
                    <div className="p-4 bg-blue-50 rounded-lg space-y-2">
                      <div className="font-semibold">Tax Calculation Result</div>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>Subtotal: {formatCurrency(taxCalculator.result.subtotal, taxCalculator.result.currency)}</div>
                        <div>Tax: {formatCurrency(taxCalculator.result.taxAmount, taxCalculator.result.currency)}</div>
                        <div className="font-medium col-span-2">
                          Total: {formatCurrency(taxCalculator.result.totalAmount, taxCalculator.result.currency)}
                        </div>
                      </div>
                      {taxCalculator.result.breakdown.length > 0 && (
                        <div className="mt-2">
                          <div className="text-sm font-medium">Tax Breakdown:</div>
                          {taxCalculator.result.breakdown.map((tax, index) => (
                            <div key={index} className="text-xs text-gray-600">
                              {tax.taxType}: {tax.rate}% = {formatCurrency(tax.amount, taxCalculator.result!.currency)}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Tax Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle>Tax Configuration</CardTitle>
                </CardHeader>
                <CardContent>
                  {taxConfig ? (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{taxConfig.name}</span>
                        <Badge className={taxConfig.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                          {taxConfig.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>

                      <div>
                        <div className="text-sm font-medium mb-2">Automation Settings</div>
                        <div className="space-y-1 text-sm">
                          <div>Provider: {taxConfig.automation.provider}</div>
                          <div>Auto Calculate: {taxConfig.automation.autoCalculate ? 'Yes' : 'No'}</div>
                          <div>Auto File: {taxConfig.automation.autoFile ? 'Yes' : 'No'}</div>
                        </div>
                      </div>

                      <div>
                        <div className="text-sm font-medium mb-2">Tax Rules ({taxConfig.rules.length})</div>
                        <div className="space-y-2 max-h-32 overflow-y-auto">
                          {taxConfig.rules.map(rule => (
                            <div key={rule.id} className="p-2 bg-gray-50 rounded text-sm">
                              <div className="font-medium">{rule.name}</div>
                              <div className="text-gray-600">Rate: {rule.rate}%</div>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <div className="text-sm font-medium mb-2">Registered Regions ({taxConfig.regions.length})</div>
                        <div className="space-y-1 text-sm">
                          {taxConfig.regions.map((region, index) => (
                            <div key={index} className="text-gray-600">
                              {region.country}{region.state ? `-${region.state}` : ''}: {region.taxId}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      No tax configuration found
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="purchase-orders" className="space-y-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Purchase Orders</h3>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Purchase Order
              </Button>
            </div>

            <Card>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b">
                      <tr>
                        <th className="p-4 text-left font-medium text-gray-900">PO Number</th>
                        <th className="p-4 text-left font-medium text-gray-900">Vendor</th>
                        <th className="p-4 text-left font-medium text-gray-900">Amount</th>
                        <th className="p-4 text-left font-medium text-gray-900">Status</th>
                        <th className="p-4 text-left font-medium text-gray-900">Department</th>
                        <th className="p-4 text-left font-medium text-gray-900">Created</th>
                        <th className="p-4 text-left font-medium text-gray-900">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {purchaseOrders.map((po) => (
                        <tr key={po.id} className="hover:bg-gray-50">
                          <td className="p-4">
                            <div className="font-medium text-gray-900">{po.poNumber}</div>
                          </td>
                          <td className="p-4">
                            <div className="font-medium text-gray-900">{po.vendor.name}</div>
                            <div className="text-sm text-gray-500">{po.vendor.email}</div>
                          </td>
                          <td className="p-4">
                            <div className="font-medium text-gray-900">
                              {formatCurrency(po.financial.totalAmount, po.financial.currency)}
                            </div>
                          </td>
                          <td className="p-4">
                            {getStatusBadge(po.status)}
                          </td>
                          <td className="p-4">
                            <div className="text-sm text-gray-900">{po.buyer.department}</div>
                            <div className="text-xs text-gray-500">{po.buyer.requestedBy}</div>
                          </td>
                          <td className="p-4">
                            <div className="text-sm text-gray-900">
                              {formatDate(po.metadata.createdAt)}
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="flex items-center gap-1">
                              <Button size="sm" variant="ghost">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button size="sm" variant="ghost">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button size="sm" variant="ghost">
                                <Download className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="contracts" className="space-y-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Enterprise Contracts</h3>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Contract
              </Button>
            </div>

            <Card>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b">
                      <tr>
                        <th className="p-4 text-left font-medium text-gray-900">Contract #</th>
                        <th className="p-4 text-left font-medium text-gray-900">Client</th>
                        <th className="p-4 text-left font-medium text-gray-900">Value</th>
                        <th className="p-4 text-left font-medium text-gray-900">Status</th>
                        <th className="p-4 text-left font-medium text-gray-900">Term</th>
                        <th className="p-4 text-left font-medium text-gray-900">Billing</th>
                        <th className="p-4 text-left font-medium text-gray-900">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {contracts.map((contract) => (
                        <tr key={contract.id} className="hover:bg-gray-50">
                          <td className="p-4">
                            <div className="font-medium text-gray-900">{contract.contractNumber}</div>
                          </td>
                          <td className="p-4">
                            <div className="font-medium text-gray-900">{contract.parties.client.companyName}</div>
                            <div className="text-sm text-gray-500">{contract.parties.client.contactPerson}</div>
                          </td>
                          <td className="p-4">
                            <div className="font-medium text-gray-900">
                              {contract.pricing.baseAmount ?
                                formatCurrency(contract.pricing.baseAmount, contract.terms.currency) :
                                'Usage-based'
                              }
                            </div>
                            <div className="text-sm text-gray-500">{contract.pricing.model}</div>
                          </td>
                          <td className="p-4">
                            {getStatusBadge(contract.status)}
                          </td>
                          <td className="p-4">
                            <div className="text-sm text-gray-900">
                              {formatDate(contract.terms.startDate)} - {formatDate(contract.terms.endDate)}
                            </div>
                            <div className="text-xs text-gray-500">
                              {contract.terms.autoRenewal ? 'Auto-renewal' : 'Fixed term'}
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="text-sm text-gray-900">{contract.billing.frequency}</div>
                            <div className="text-xs text-gray-500">Net {contract.billing.paymentDue}</div>
                          </td>
                          <td className="p-4">
                            <div className="flex items-center gap-1">
                              <Button size="sm" variant="ghost">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button size="sm" variant="ghost">
                                <Receipt className="h-4 w-4" />
                              </Button>
                              <Button size="sm" variant="ghost">
                                <Download className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
