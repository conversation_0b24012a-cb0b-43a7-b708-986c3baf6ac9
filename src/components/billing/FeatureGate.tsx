'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Lock,
  Crown,
  Zap,
  Star,
  ArrowRight,
  X
} from 'lucide-react';
import { useTenantAware } from '@/hooks/useTenantAware';
import { subscriptionService, type SubscriptionPlan } from '@/services/subscriptionService';
import UpgradePrompt from './UpgradePrompt';

interface FeatureGateProps {
  feature: keyof SubscriptionPlan['features'];
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requiredPlan?: 'starter' | 'professional' | 'enterprise';
  featureName?: string;
  featureDescription?: string;
  showUpgradePrompt?: boolean;
  onUpgrade?: () => void;
  className?: string;
}

export default function FeatureGate({
  feature,
  children,
  fallback,
  requiredPlan,
  featureName,
  featureDescription,
  showUpgradePrompt = true,
  onUpgrade,
  className = ''
}: FeatureGateProps) {
  const { canUseFeature, isOnPlan, tenant } = useTenantAware();
  const { user } = useAuth();
  const [showPrompt, setShowPrompt] = useState(false);

  // SUPER ADMIN BYPASS: Super admin has access to all features
  const { isSuperAdmin } = require('@/services/superAdminService');
  if (user && isSuperAdmin(user.id)) {
    return <>{children}</>;
  }

  // Check if user has access to the feature
  const hasAccess = canUseFeature(feature);

  // If user has access, render the children
  if (hasAccess) {
    return <>{children}</>;
  }

  // If custom fallback is provided, use it
  if (fallback) {
    return <>{fallback}</>;
  }

  // Default feature gate UI
  const getFeatureInfo = () => {
    const featureMap: Record<string, {
      name: string;
      description: string;
      icon: React.ReactNode;
      requiredPlan: string;
      benefits: string[];
    }> = {
      advancedAnalytics: {
        name: 'Advanced Analytics',
        description: 'Detailed insights and custom reports for your exhibitions',
        icon: <Zap className="h-5 w-5" />,
        requiredPlan: 'Starter',
        benefits: ['Custom dashboards', 'Export reports', 'Historical data']
      },
      customBranding: {
        name: 'Custom Branding',
        description: 'Customize EVEXA with your company colors and logo',
        icon: <Star className="h-5 w-5" />,
        requiredPlan: 'Starter',
        benefits: ['Custom colors', 'Logo upload', 'White-label experience']
      },
      whiteLabeling: {
        name: 'White Labeling',
        description: 'Complete white-label solution for your clients',
        icon: <Crown className="h-5 w-5" />,
        requiredPlan: 'Professional',
        benefits: ['Remove EVEXA branding', 'Custom domain', 'Client portals']
      },
      apiAccess: {
        name: 'API Access',
        description: 'Integrate EVEXA with your existing systems',
        icon: <Zap className="h-5 w-5" />,
        requiredPlan: 'Starter',
        benefits: ['REST API', 'Webhooks', 'Custom integrations']
      },
      customIntegrations: {
        name: 'Custom Integrations',
        description: 'Connect with third-party tools and services',
        icon: <Zap className="h-5 w-5" />,
        requiredPlan: 'Professional',
        benefits: ['CRM integration', 'Marketing tools', 'Custom connectors']
      },
      workflowAutomation: {
        name: 'Workflow Automation',
        description: 'Automate repetitive tasks and processes',
        icon: <Zap className="h-5 w-5" />,
        requiredPlan: 'Starter',
        benefits: ['Automated emails', 'Task scheduling', 'Smart notifications']
      },
      aiFeatures: {
        name: 'AI Features',
        description: 'AI-powered insights and automation',
        icon: <Zap className="h-5 w-5" />,
        requiredPlan: 'Starter',
        benefits: ['Smart recommendations', 'Predictive analytics', 'Auto-categorization']
      },
      prioritySupport: {
        name: 'Priority Support',
        description: '24/7 priority customer support',
        icon: <Crown className="h-5 w-5" />,
        requiredPlan: 'Professional',
        benefits: ['24/7 support', 'Phone support', 'Dedicated account manager']
      },
      ssoIntegration: {
        name: 'SSO Integration',
        description: 'Single sign-on with your identity provider',
        icon: <Lock className="h-5 w-5" />,
        requiredPlan: 'Professional',
        benefits: ['SAML/OAuth', 'Active Directory', 'Enhanced security']
      },
      auditLogs: {
        name: 'Audit Logs',
        description: 'Comprehensive activity logging and compliance',
        icon: <Lock className="h-5 w-5" />,
        requiredPlan: 'Professional',
        benefits: ['Activity tracking', 'Compliance reports', 'Security monitoring']
      }
    };

    return featureMap[feature as string] || {
      name: featureName || 'Premium Feature',
      description: featureDescription || 'This feature requires a paid plan',
      icon: <Crown className="h-5 w-5" />,
      requiredPlan: requiredPlan || 'Starter',
      benefits: ['Enhanced functionality', 'Better performance', 'Premium support']
    };
  };

  const featureInfo = getFeatureInfo();

  const handleUpgrade = () => {
    if (onUpgrade) {
      onUpgrade();
    } else if (showUpgradePrompt) {
      setShowPrompt(true);
    } else {
      // Default upgrade action
      window.location.href = '/billing';
    }
  };

  const getPlanBadgeColor = (plan: string) => {
    switch (plan.toLowerCase()) {
      case 'starter':
        return 'bg-blue-100 text-blue-800';
      case 'professional':
        return 'bg-purple-100 text-purple-800';
      case 'enterprise':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={className}>
      <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
        <CardHeader className="text-center pb-4">
          <div className="flex justify-center mb-3">
            <div className="p-3 bg-white rounded-full shadow-sm">
              {featureInfo.icon}
            </div>
          </div>
          <CardTitle className="flex items-center justify-center space-x-2">
            <Lock className="h-4 w-4 text-gray-400" />
            <span>{featureInfo.name}</span>
          </CardTitle>
          <CardDescription className="max-w-sm mx-auto">
            {featureInfo.description}
          </CardDescription>
        </CardHeader>

        <CardContent className="text-center space-y-4">
          <Badge 
            variant="secondary" 
            className={`${getPlanBadgeColor(featureInfo.requiredPlan)} font-medium`}
          >
            Requires {featureInfo.requiredPlan} Plan
          </Badge>

          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-700">What you'll get:</p>
            <ul className="text-sm text-gray-600 space-y-1">
              {featureInfo.benefits.map((benefit, index) => (
                <li key={index} className="flex items-center justify-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="pt-2">
            <Button onClick={handleUpgrade} className="w-full">
              Upgrade to {featureInfo.requiredPlan}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>

          {/* Current Plan Info */}
          {tenant && (
            <div className="text-xs text-gray-500 pt-2 border-t">
              Current plan: {' '}
              <span className="font-medium">
                {isOnPlan('free') ? 'Free' : 
                 isOnPlan('starter') ? 'Starter' : 
                 isOnPlan('professional') ? 'Professional' : 'Enterprise'}
              </span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upgrade Prompt Modal */}
      {showPrompt && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="max-w-md w-full relative">
            <Button
              variant="ghost"
              size="sm"
              className="absolute -top-2 -right-2 z-10 bg-white shadow-md hover:bg-gray-100"
              onClick={() => setShowPrompt(false)}
            >
              <X className="h-4 w-4" />
            </Button>
            <UpgradePrompt
              trigger="feature_locked"
              feature={featureInfo.name}
              onUpgrade={() => {
                setShowPrompt(false);
                onUpgrade?.();
              }}
              onDismiss={() => setShowPrompt(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
}

// Usage examples:
// <FeatureGate feature="advancedAnalytics">
//   <AdvancedAnalyticsComponent />
// </FeatureGate>
//
// <FeatureGate 
//   feature="customBranding" 
//   featureName="Brand Customization"
//   featureDescription="Make EVEXA match your brand"
// >
//   <BrandingSettings />
// </FeatureGate>
