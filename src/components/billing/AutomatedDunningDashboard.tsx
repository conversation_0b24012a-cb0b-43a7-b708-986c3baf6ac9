"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/auth-context';
// import { useTenant } from '@/hooks/useTenant';
import { toast } from 'sonner';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>spons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from 'recharts';
import {
  AlertTriangle,
  Bell,
  CheckCircle,
  Clock,
  DollarSign,
  Mail,
  MessageSquare,
  Phone,
  Play,
  Pause,
  X,
  RefreshCw,
  Eye,
  Send,
  Ban,
  TrendingUp,
  Users,
  Calendar,
  Target
} from 'lucide-react';
import { DunningCampaign, DunningAnalytics } from '@/services/automatedDunningService';

interface AutomatedDunningDashboardProps {
  className?: string;
}

const COLORS = ['#2563eb', '#dc2626', '#059669', '#d97706', '#7c3aed'];

export default function AutomatedDunningDashboard({ className }: AutomatedDunningDashboardProps) {
  const { user } = useAuth();
  // const { tenantId } = useTenant();
  const tenantId = 'default-tenant'; // Temporary fallback
  const [loading, setLoading] = useState(true);
  const [campaigns, setCampaigns] = useState<DunningCampaign[]>([]);
  const [analytics, setAnalytics] = useState<DunningAnalytics | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedCampaign, setSelectedCampaign] = useState<DunningCampaign | null>(null);

  useEffect(() => {
    if (user && tenantId) {
      loadDunningData();
    }
  }, [user, tenantId, selectedStatus]);

  const loadDunningData = async () => {
    try {
      setLoading(true);
      const token = await user?.getIdToken();
      
      const statusParam = selectedStatus === 'all' ? '' : `&status=${selectedStatus}`;
      const [campaignsRes, analyticsRes] = await Promise.all([
        fetch(`/api/dunning?action=campaigns&tenantId=${tenantId}${statusParam}`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        fetch(`/api/dunning?action=analytics&tenantId=${tenantId}&startDate=${new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()}&endDate=${new Date().toISOString()}`, {
          headers: { Authorization: `Bearer ${token}` }
        })
      ]);

      if (campaignsRes.ok) {
        const campaignsData = await campaignsRes.json();
        setCampaigns(campaignsData);
      }

      if (analyticsRes.ok) {
        const analyticsData = await analyticsRes.json();
        setAnalytics(analyticsData);
      }
    } catch (error) {
      console.error('Error loading dunning data:', error);
      toast.error('Failed to load dunning data');
    } finally {
      setLoading(false);
    }
  };

  const handleCampaignAction = async (campaignId: string, action: string, data?: any) => {
    try {
      const token = await user?.getIdToken();
      const response = await fetch('/api/dunning', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          action,
          campaignId,
          tenantId,
          ...data
        })
      });

      if (response.ok) {
        toast.success(`Campaign ${action} successful`);
        loadDunningData();
      } else {
        throw new Error(`Failed to ${action} campaign`);
      }
    } catch (error) {
      console.error(`Error ${action} campaign:`, error);
      toast.error(`Failed to ${action} campaign`);
    }
  };

  const getStatusBadge = (status: DunningCampaign['status']) => {
    const statusConfig = {
      active: { color: 'bg-blue-100 text-blue-800', icon: Clock },
      paused: { color: 'bg-yellow-100 text-yellow-800', icon: Pause },
      completed: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      failed: { color: 'bg-red-100 text-red-800', icon: X },
      cancelled: { color: 'bg-gray-100 text-gray-800', icon: Ban }
    };

    const config = statusConfig[status];
    const Icon = config.icon;

    return (
      <Badge className={config.color}>
        <Icon className="h-3 w-3 mr-1" />
        {status.toUpperCase()}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: DunningCampaign['metadata']['priority']) => {
    const priorityConfig = {
      low: { color: 'bg-gray-100 text-gray-800' },
      medium: { color: 'bg-yellow-100 text-yellow-800' },
      high: { color: 'bg-orange-100 text-orange-800' },
      critical: { color: 'bg-red-100 text-red-800' }
    };

    const config = priorityConfig[priority];
    return <Badge className={config.color}>{priority.toUpperCase()}</Badge>;
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Prepare chart data
  const recoveryChartData = analytics ? [
    { stage: 'Initial', recovered: analytics.recovery.recoveryByStage.initial },
    { stage: 'Reminder 1', recovered: analytics.recovery.recoveryByStage.reminder_1 },
    { stage: 'Reminder 2', recovered: analytics.recovery.recoveryByStage.reminder_2 },
    { stage: 'Final Notice', recovered: analytics.recovery.recoveryByStage.final_notice }
  ] : [];

  const communicationChartData = analytics ? [
    { name: 'Email', sent: analytics.communication.byChannel.email.sent, delivered: analytics.communication.byChannel.email.delivered },
    { name: 'SMS', sent: analytics.communication.byChannel.sms.sent, delivered: analytics.communication.byChannel.sms.delivered },
    { name: 'In-App', sent: analytics.communication.byChannel.in_app.sent, delivered: analytics.communication.byChannel.in_app.delivered }
  ] : [];

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Automated Dunning Management</h2>
            <p className="text-gray-600">Failed payment recovery, smart retry logic, and customer communication</p>
          </div>
          <div className="flex items-center gap-2">
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="paused">Paused</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={loadDunningData} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        {analytics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Active Campaigns</p>
                    <p className="text-2xl font-bold">{analytics.activeCampaigns}</p>
                  </div>
                  <Clock className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Recovery Rate</p>
                    <p className="text-2xl font-bold">{(analytics.recovery.recoveryRate * 100).toFixed(1)}%</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Recovered</p>
                    <p className="text-2xl font-bold">{formatCurrency(analytics.recovery.totalRecovered)}</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Avg Recovery Time</p>
                    <p className="text-2xl font-bold">{analytics.recovery.averageRecoveryTime.toFixed(1)} days</p>
                  </div>
                  <Calendar className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <Tabs defaultValue="campaigns" className="space-y-4">
          <TabsList>
            <TabsTrigger value="campaigns">Active Campaigns</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="rules">Rules</TabsTrigger>
          </TabsList>

          <TabsContent value="campaigns" className="space-y-4">
            <Card>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b">
                      <tr>
                        <th className="p-4 text-left font-medium text-gray-900">Campaign</th>
                        <th className="p-4 text-left font-medium text-gray-900">Customer</th>
                        <th className="p-4 text-left font-medium text-gray-900">Amount</th>
                        <th className="p-4 text-left font-medium text-gray-900">Status</th>
                        <th className="p-4 text-left font-medium text-gray-900">Priority</th>
                        <th className="p-4 text-left font-medium text-gray-900">Attempts</th>
                        <th className="p-4 text-left font-medium text-gray-900">Next Retry</th>
                        <th className="p-4 text-left font-medium text-gray-900">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {campaigns.map((campaign) => (
                        <tr key={campaign.id} className="hover:bg-gray-50">
                          <td className="p-4">
                            <div className="font-medium text-gray-900">{campaign.invoiceId}</div>
                            <div className="text-sm text-gray-500">
                              Created {formatDate(campaign.metadata.createdAt)}
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="font-medium text-gray-900">{campaign.customerId}</div>
                          </td>
                          <td className="p-4">
                            <div className="font-medium text-gray-900">
                              {formatCurrency(campaign.payment.amount, campaign.payment.currency)}
                            </div>
                            <div className="text-sm text-gray-500">
                              Due: {formatDate(campaign.payment.dueDate)}
                            </div>
                          </td>
                          <td className="p-4">
                            {getStatusBadge(campaign.status)}
                          </td>
                          <td className="p-4">
                            {getPriorityBadge(campaign.metadata.priority)}
                          </td>
                          <td className="p-4">
                            <div className="text-center">
                              <div className="font-medium">{campaign.payment.attemptCount}</div>
                              <div className="text-xs text-gray-500">of {campaign.config.maxRetries}</div>
                              <Progress 
                                value={(campaign.payment.attemptCount / campaign.config.maxRetries) * 100}
                                className="h-1 mt-1"
                              />
                            </div>
                          </td>
                          <td className="p-4">
                            {campaign.status === 'active' && (
                              <div className="text-sm">
                                {formatDate(campaign.payment.nextRetryDate)}
                              </div>
                            )}
                          </td>
                          <td className="p-4">
                            <div className="flex items-center gap-1">
                              <Button 
                                size="sm" 
                                variant="ghost"
                                onClick={() => setSelectedCampaign(campaign)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              {campaign.status === 'active' && (
                                <>
                                  <Button 
                                    size="sm" 
                                    variant="ghost"
                                    onClick={() => handleCampaignAction(campaign.id, 'pause-campaign')}
                                  >
                                    <Pause className="h-4 w-4" />
                                  </Button>
                                  <Button 
                                    size="sm" 
                                    variant="ghost"
                                    onClick={() => handleCampaignAction(campaign.id, 'manual-retry')}
                                  >
                                    <RefreshCw className="h-4 w-4" />
                                  </Button>
                                </>
                              )}
                              {campaign.status === 'paused' && (
                                <Button 
                                  size="sm" 
                                  variant="ghost"
                                  onClick={() => handleCampaignAction(campaign.id, 'resume-campaign')}
                                >
                                  <Play className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            {analytics && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Recovery by Stage</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={recoveryChartData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="stage" />
                          <YAxis />
                          <Tooltip />
                          <Bar dataKey="recovered" fill="#2563eb" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Communication Effectiveness</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={communicationChartData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Bar dataKey="sent" fill="#dc2626" name="Sent" />
                          <Bar dataKey="delivered" fill="#059669" name="Delivered" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Campaign Details Modal */}
        {selectedCampaign && (
          <Dialog open={!!selectedCampaign} onOpenChange={() => setSelectedCampaign(null)}>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Campaign Details - {selectedCampaign.invoiceId}</DialogTitle>
              </DialogHeader>
              <div className="space-y-6">
                {/* Campaign Overview */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium mb-2">Payment Information</h3>
                    <div className="space-y-1 text-sm">
                      <div>Amount: {formatCurrency(selectedCampaign.payment.amount, selectedCampaign.payment.currency)}</div>
                      <div>Due Date: {formatDate(selectedCampaign.payment.dueDate)}</div>
                      <div>Attempts: {selectedCampaign.payment.attemptCount} of {selectedCampaign.config.maxRetries}</div>
                      <div>Failure Reason: {selectedCampaign.payment.failureReason}</div>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium mb-2">Campaign Status</h3>
                    <div className="space-y-2">
                      {getStatusBadge(selectedCampaign.status)}
                      {getPriorityBadge(selectedCampaign.metadata.priority)}
                    </div>
                  </div>
                </div>

                {/* Communications History */}
                <div>
                  <h3 className="font-medium mb-2">Communications</h3>
                  <div className="space-y-2">
                    {selectedCampaign.communications.map((comm) => (
                      <div key={comm.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div className="flex items-center gap-2">
                          {comm.type === 'email' && <Mail className="h-4 w-4" />}
                          {comm.type === 'sms' && <MessageSquare className="h-4 w-4" />}
                          {comm.type === 'in_app' && <Bell className="h-4 w-4" />}
                          <span className="text-sm">{comm.type.toUpperCase()}</span>
                        </div>
                        <div className="text-sm text-gray-600">
                          {formatDate(comm.sentAt)} - {comm.status}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Actions History */}
                <div>
                  <h3 className="font-medium mb-2">Actions</h3>
                  <div className="space-y-2">
                    {selectedCampaign.actions.map((action) => (
                      <div key={action.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div>
                          <div className="font-medium text-sm">{action.type.replace('_', ' ').toUpperCase()}</div>
                          <div className="text-xs text-gray-600">{action.details}</div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm">{action.result}</div>
                          <div className="text-xs text-gray-600">{formatDate(action.executedAt)}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </div>
  );
}
