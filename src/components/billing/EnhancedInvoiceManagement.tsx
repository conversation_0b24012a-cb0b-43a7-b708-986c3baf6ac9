"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useAuth } from '@/contexts/auth-context';
// import { useTenant } from '@/hooks/useTenant';
import { auth } from '@/lib/firebase';
import { toast } from 'sonner';
import {
  FileText,
  Plus,
  Send,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  Filter,
  Download,
  Edit,
  Trash2,
  Eye,
  Settings,
  Users,
  Calendar,
  AlertCircle
} from 'lucide-react';
import { CustomInvoice, InvoiceTemplate, BulkInvoiceOperation } from '@/services/invoiceManagementService';

interface EnhancedInvoiceManagementProps {
  className?: string;
}

export default function EnhancedInvoiceManagement({ className }: EnhancedInvoiceManagementProps) {
  const { user } = useAuth();
  // const { tenantId } = useTenant();
  const tenantId = 'default-tenant'; // Temporary fallback
  const [loading, setLoading] = useState(true);
  const [invoices, setInvoices] = useState<CustomInvoice[]>([]);
  const [templates, setTemplates] = useState<InvoiceTemplate[]>([]);
  const [bulkOperations, setBulkOperations] = useState<BulkInvoiceOperation[]>([]);
  const [selectedInvoices, setSelectedInvoices] = useState<string[]>([]);
  const [filters, setFilters] = useState({
    status: '',
    type: '',
    customerId: ''
  });

  useEffect(() => {
    if (user && tenantId) {
      loadData();
    }
  }, [user, tenantId]);

  const loadData = async () => {
    try {
      setLoading(true);
      const firebaseUser = auth.currentUser;
      if (!firebaseUser) {
        throw new Error('User not authenticated');
      }
      const token = await firebaseUser.getIdToken();
      
      const [invoicesRes, templatesRes, bulkOpsRes] = await Promise.all([
        fetch(`/api/invoices?action=list&tenantId=${tenantId}`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        fetch(`/api/invoices?action=templates&tenantId=${tenantId}`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        fetch(`/api/invoices?action=bulk-operations&tenantId=${tenantId}`, {
          headers: { Authorization: `Bearer ${token}` }
        })
      ]);

      if (invoicesRes.ok) {
        const invoicesData = await invoicesRes.json();
        setInvoices(invoicesData);
      }

      if (templatesRes.ok) {
        const templatesData = await templatesRes.json();
        setTemplates(templatesData);
      }

      if (bulkOpsRes.ok) {
        const bulkOpsData = await bulkOpsRes.json();
        setBulkOperations(bulkOpsData);
      }
    } catch (error) {
      console.error('Error loading invoice data:', error);
      toast.error('Failed to load invoice data');
    } finally {
      setLoading(false);
    }
  };

  const handleSendInvoice = async (invoiceId: string) => {
    try {
      const firebaseUser = auth.currentUser;
      if (!firebaseUser) {
        throw new Error('User not authenticated');
      }
      const token = await firebaseUser.getIdToken();
      const response = await fetch('/api/invoices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          action: 'send-invoice',
          invoiceId,
          tenantId
        })
      });

      if (response.ok) {
        toast.success('Invoice sent successfully');
        loadData();
      } else {
        throw new Error('Failed to send invoice');
      }
    } catch (error) {
      console.error('Error sending invoice:', error);
      toast.error('Failed to send invoice');
    }
  };

  const handleApproveInvoice = async (invoiceId: string) => {
    try {
      const firebaseUser = auth.currentUser;
      if (!firebaseUser) {
        throw new Error('User not authenticated');
      }
      const token = await firebaseUser.getIdToken();
      const response = await fetch('/api/invoices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          action: 'approve-invoice',
          invoiceId,
          approvedBy: user?.uid,
          tenantId
        })
      });

      if (response.ok) {
        toast.success('Invoice approved successfully');
        loadData();
      } else {
        throw new Error('Failed to approve invoice');
      }
    } catch (error) {
      console.error('Error approving invoice:', error);
      toast.error('Failed to approve invoice');
    }
  };

  const handleBulkOperation = async (operation: 'send' | 'approve' | 'cancel') => {
    if (selectedInvoices.length === 0) {
      toast.error('Please select invoices first');
      return;
    }

    try {
      const token = await user?.getIdToken();
      const response = await fetch('/api/invoices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          action: 'create-bulk-operation',
          operation,
          invoiceIds: selectedInvoices,
          tenantId
        })
      });

      if (response.ok) {
        toast.success(`Bulk ${operation} operation started`);
        setSelectedInvoices([]);
        loadData();
      } else {
        throw new Error(`Failed to start bulk ${operation} operation`);
      }
    } catch (error) {
      console.error(`Error starting bulk ${operation} operation:`, error);
      toast.error(`Failed to start bulk ${operation} operation`);
    }
  };

  const getStatusBadge = (status: CustomInvoice['status']) => {
    const statusConfig = {
      draft: { color: 'bg-gray-100 text-gray-800', icon: Edit },
      pending_approval: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      approved: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      sent: { color: 'bg-blue-100 text-blue-800', icon: Send },
      paid: { color: 'bg-green-100 text-green-800', icon: DollarSign },
      overdue: { color: 'bg-red-100 text-red-800', icon: AlertCircle },
      cancelled: { color: 'bg-gray-100 text-gray-800', icon: XCircle }
    };

    const config = statusConfig[status];
    const Icon = config.icon;

    return (
      <Badge className={config.color}>
        <Icon className="h-3 w-3 mr-1" />
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Enhanced Invoice Management</h2>
            <p className="text-gray-600">Manage custom invoices, templates, and automated billing</p>
          </div>
          <div className="flex gap-2">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Invoice
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Templates
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Invoices</p>
                  <p className="text-2xl font-bold">{invoices.length}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Pending Approval</p>
                  <p className="text-2xl font-bold">
                    {invoices.filter(inv => inv.status === 'pending_approval').length}
                  </p>
                </div>
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Revenue</p>
                  <p className="text-2xl font-bold">
                    {formatCurrency(invoices.reduce((sum, inv) => sum + inv.details.paidAmount, 0))}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Outstanding</p>
                  <p className="text-2xl font-bold">
                    {formatCurrency(invoices.reduce((sum, inv) => sum + inv.details.balanceAmount, 0))}
                  </p>
                </div>
                <AlertCircle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="invoices" className="space-y-4">
          <TabsList>
            <TabsTrigger value="invoices">Invoices</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="bulk-operations">Bulk Operations</TabsTrigger>
          </TabsList>

          <TabsContent value="invoices" className="space-y-4">
            {/* Filters and Bulk Actions */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <Select value={filters.status} onValueChange={(value) => setFilters({...filters, status: value})}>
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="All Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Status</SelectItem>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="pending_approval">Pending Approval</SelectItem>
                        <SelectItem value="approved">Approved</SelectItem>
                        <SelectItem value="sent">Sent</SelectItem>
                        <SelectItem value="paid">Paid</SelectItem>
                        <SelectItem value="overdue">Overdue</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select value={filters.type} onValueChange={(value) => setFilters({...filters, type: value})}>
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="All Types" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Types</SelectItem>
                        <SelectItem value="subscription">Subscription</SelectItem>
                        <SelectItem value="one_time">One Time</SelectItem>
                        <SelectItem value="usage_based">Usage Based</SelectItem>
                        <SelectItem value="custom">Custom</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedInvoices.length > 0 && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600">
                        {selectedInvoices.length} selected
                      </span>
                      <Button size="sm" onClick={() => handleBulkOperation('send')}>
                        <Send className="h-4 w-4 mr-1" />
                        Send
                      </Button>
                      <Button size="sm" onClick={() => handleBulkOperation('approve')}>
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Approve
                      </Button>
                      <Button size="sm" variant="destructive" onClick={() => handleBulkOperation('cancel')}>
                        <XCircle className="h-4 w-4 mr-1" />
                        Cancel
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Invoices Table */}
            <Card>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b">
                      <tr>
                        <th className="p-4 text-left">
                          <input
                            type="checkbox"
                            checked={selectedInvoices.length === invoices.length && invoices.length > 0}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedInvoices(invoices.map(inv => inv.id));
                              } else {
                                setSelectedInvoices([]);
                              }
                            }}
                            className="rounded border-gray-300"
                          />
                        </th>
                        <th className="p-4 text-left font-medium text-gray-900">Invoice #</th>
                        <th className="p-4 text-left font-medium text-gray-900">Customer</th>
                        <th className="p-4 text-left font-medium text-gray-900">Amount</th>
                        <th className="p-4 text-left font-medium text-gray-900">Status</th>
                        <th className="p-4 text-left font-medium text-gray-900">Due Date</th>
                        <th className="p-4 text-left font-medium text-gray-900">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {invoices.map((invoice) => (
                        <tr key={invoice.id} className="hover:bg-gray-50">
                          <td className="p-4">
                            <input
                              type="checkbox"
                              checked={selectedInvoices.includes(invoice.id)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSelectedInvoices([...selectedInvoices, invoice.id]);
                                } else {
                                  setSelectedInvoices(selectedInvoices.filter(id => id !== invoice.id));
                                }
                              }}
                              className="rounded border-gray-300"
                            />
                          </td>
                          <td className="p-4">
                            <div className="font-medium text-gray-900">{invoice.invoiceNumber}</div>
                            <div className="text-sm text-gray-500">{invoice.type}</div>
                          </td>
                          <td className="p-4">
                            <div className="font-medium text-gray-900">{invoice.customer.name}</div>
                            <div className="text-sm text-gray-500">{invoice.customer.email}</div>
                          </td>
                          <td className="p-4">
                            <div className="font-medium text-gray-900">
                              {formatCurrency(invoice.details.totalAmount, invoice.details.currency)}
                            </div>
                            {invoice.details.balanceAmount > 0 && (
                              <div className="text-sm text-red-600">
                                Balance: {formatCurrency(invoice.details.balanceAmount, invoice.details.currency)}
                              </div>
                            )}
                          </td>
                          <td className="p-4">
                            {getStatusBadge(invoice.status)}
                          </td>
                          <td className="p-4">
                            <div className="text-sm text-gray-900">
                              {new Date(invoice.details.dueDate).toLocaleDateString()}
                            </div>
                            {new Date(invoice.details.dueDate) < new Date() && invoice.status !== 'paid' && (
                              <div className="text-xs text-red-600">Overdue</div>
                            )}
                          </td>
                          <td className="p-4">
                            <div className="flex items-center gap-2">
                              <Button size="sm" variant="ghost">
                                <Eye className="h-4 w-4" />
                              </Button>
                              {invoice.status === 'draft' && (
                                <Button size="sm" variant="ghost">
                                  <Edit className="h-4 w-4" />
                                </Button>
                              )}
                              {invoice.status === 'approved' && (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleSendInvoice(invoice.id)}
                                >
                                  <Send className="h-4 w-4" />
                                </Button>
                              )}
                              {invoice.status === 'pending_approval' && (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleApproveInvoice(invoice.id)}
                                >
                                  <CheckCircle className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="templates" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Invoice Templates
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    New Template
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {templates.map((template) => (
                    <Card key={template.id} className="border-2 hover:border-blue-300 transition-colors">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="font-medium text-gray-900">{template.name}</h3>
                          {template.isDefault && (
                            <Badge className="bg-blue-100 text-blue-800">Default</Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-4">{template.description}</p>
                        <div className="flex items-center justify-between">
                          <div className="text-xs text-gray-500">
                            {template.branding.companyName}
                          </div>
                          <div className="flex items-center gap-1">
                            <Button size="sm" variant="ghost">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="ghost">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="ghost">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="bulk-operations" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Bulk Operations History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {bulkOperations.map((operation) => (
                    <div key={operation.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Badge className={
                            operation.status === 'completed' ? 'bg-green-100 text-green-800' :
                            operation.status === 'failed' ? 'bg-red-100 text-red-800' :
                            operation.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }>
                            {operation.status}
                          </Badge>
                          <span className="font-medium capitalize">{operation.operation}</span>
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(operation.metadata.createdAt).toLocaleString()}
                        </div>
                      </div>
                      <div className="grid grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Total:</span> {operation.totalCount}
                        </div>
                        <div>
                          <span className="text-gray-600">Success:</span> {operation.successCount}
                        </div>
                        <div>
                          <span className="text-gray-600">Failed:</span> {operation.failureCount}
                        </div>
                        <div>
                          <span className="text-gray-600">Progress:</span> {operation.processedCount}/{operation.totalCount}
                        </div>
                      </div>
                      {operation.errors.length > 0 && (
                        <div className="mt-2 p-2 bg-red-50 rounded text-sm">
                          <div className="font-medium text-red-800">Errors:</div>
                          {operation.errors.slice(0, 3).map((error, index) => (
                            <div key={index} className="text-red-600">
                              {error.invoiceId}: {error.error}
                            </div>
                          ))}
                          {operation.errors.length > 3 && (
                            <div className="text-red-600">
                              ... and {operation.errors.length - 3} more errors
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
