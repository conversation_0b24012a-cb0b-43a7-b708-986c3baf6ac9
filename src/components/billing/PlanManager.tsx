'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Edit, 
  Trash2, 
  Plus, 
  Copy, 
  Save,
  X,
  DollarSign,
  Users,
  Building2,
  Database,
  Mail,
  Zap
} from 'lucide-react';
import { subscriptionService, type SubscriptionPlan } from '@/services/subscriptionService';
import { useToast } from '@/hooks/use-toast';

interface PlanManagerProps {
  onPlanUpdated?: () => void;
}

export default function PlanManager({ onPlanUpdated }: PlanManagerProps) {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingPlan, setEditingPlan] = useState<SubscriptionPlan | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [formData, setFormData] = useState<Partial<SubscriptionPlan>>({});
  const { toast } = useToast();

  useEffect(() => {
    loadPlans();
  }, []);

  const loadPlans = async () => {
    try {
      setLoading(true);
      const allPlans = await subscriptionService.getAllPlans();
      // Remove duplicates by ID
      const uniquePlans = allPlans.filter((plan, index, self) => 
        index === self.findIndex(p => p.id === plan.id)
      );
      setPlans(uniquePlans);
    } catch (error) {
      console.error('Error loading plans:', error);
      toast({
        title: "Error",
        description: "Failed to load subscription plans",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditPlan = (plan: SubscriptionPlan) => {
    setEditingPlan(plan);
    setFormData(plan);
    setIsDialogOpen(true);
  };

  const handleCreatePlan = () => {
    setEditingPlan(null);
    setFormData({
      name: '',
      displayName: '',
      description: '',
      tier: 'starter',
      pricing: {
        monthly: 0,
        yearly: 0,
        currency: 'USD',
        yearlyDiscount: 0
      },
      features: {
        maxExhibitions: 1,
        maxEvents: 1,
        maxUsers: 1,
        maxTasks: 10,
        maxLeads: 10,
        maxVendors: 1,
        storageGB: 1,
        apiCallsPerMonth: 100,
        emailsPerMonth: 10,
        customBranding: false,
        advancedAnalytics: false,
        prioritySupport: false,
        whiteLabeling: false,
        apiAccess: false,
        customIntegrations: false,
        dedicatedAccount: false,
        ssoIntegration: false,
        auditLogs: false,
        dataExport: false,
        customReports: false,
        workflowAutomation: false,
        aiFeatures: false,
        mobileApp: false,
        offlineSync: false
      },
      isActive: true,
      isPopular: false
    });
    setIsDialogOpen(true);
  };

  const handleSavePlan = async () => {
    try {
      if (!formData.name || !formData.displayName) {
        toast({
          title: "Validation Error",
          description: "Plan name and display name are required",
          variant: "destructive"
        });
        return;
      }

      if (editingPlan) {
        // Update existing plan
        await subscriptionService.updatePlan(editingPlan.id, formData);
        toast({
          title: "Success",
          description: "Plan updated successfully"
        });
      } else {
        // Create new plan
        await subscriptionService.createPlan(formData as Omit<SubscriptionPlan, 'id' | 'metadata'>);
        toast({
          title: "Success",
          description: "Plan created successfully"
        });
      }

      setIsDialogOpen(false);
      loadPlans();
      onPlanUpdated?.();
    } catch (error) {
      console.error('Error saving plan:', error);
      toast({
        title: "Error",
        description: "Failed to save plan",
        variant: "destructive"
      });
    }
  };

  const handleDuplicatePlan = async (plan: SubscriptionPlan) => {
    try {
      const newName = `${plan.name}_copy_${Date.now()}`;
      await subscriptionService.duplicatePlan(plan.id, {
        name: newName,
        displayName: `${plan.displayName} (Copy)`
      });
      
      toast({
        title: "Success",
        description: "Plan duplicated successfully"
      });
      
      loadPlans();
      onPlanUpdated?.();
    } catch (error) {
      console.error('Error duplicating plan:', error);
      toast({
        title: "Error",
        description: "Failed to duplicate plan",
        variant: "destructive"
      });
    }
  };

  const handleDeletePlan = async (plan: SubscriptionPlan) => {
    if (!confirm(`Are you sure you want to delete the "${plan.displayName}" plan?`)) {
      return;
    }

    try {
      await subscriptionService.deletePlan(plan.id);
      toast({
        title: "Success",
        description: "Plan deleted successfully"
      });
      loadPlans();
      onPlanUpdated?.();
    } catch (error) {
      console.error('Error deleting plan:', error);
      toast({
        title: "Error",
        description: "Failed to delete plan",
        variant: "destructive"
      });
    }
  };

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => {
      if (field.includes('.')) {
        const [parent, child] = field.split('.');
        return {
          ...prev,
          [parent]: {
            ...((prev as any)[parent] || {}),
            [child]: value
          }
        };
      }
      return { ...prev, [field]: value };
    });
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(price);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">Subscription Plans</h2>
          <Button disabled>
            <Plus className="h-4 w-4 mr-2" />
            Add Plan
          </Button>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {[1, 2, 3].map((j) => (
                    <div key={j} className="h-3 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Subscription Plans</h2>
          <p className="text-muted-foreground">Manage your subscription plans and pricing</p>
        </div>
        <Button onClick={handleCreatePlan}>
          <Plus className="h-4 w-4 mr-2" />
          Add Plan
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {plans.map((plan) => (
          <Card key={plan.id} className="relative">
            {plan.isPopular && (
              <Badge className="absolute -top-2 -right-2 bg-purple-500">
                Popular
              </Badge>
            )}
            
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <span>{plan.displayName}</span>
                    {!plan.isActive && (
                      <Badge variant="secondary">Inactive</Badge>
                    )}
                  </CardTitle>
                  <CardDescription>{plan.description}</CardDescription>
                </div>
              </div>
              
              <div className="text-2xl font-bold">
                {formatPrice(plan.pricing.monthly)}
                {plan.pricing.monthly > 0 && (
                  <span className="text-sm font-normal text-muted-foreground">/month</span>
                )}
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Key Features */}
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Building2 className="h-4 w-4 text-blue-500" />
                  <span>{plan.features.maxExhibitions === -1 ? 'Unlimited' : plan.features.maxExhibitions} exhibitions</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-green-500" />
                  <span>{plan.features.maxUsers === -1 ? 'Unlimited' : plan.features.maxUsers} users</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Database className="h-4 w-4 text-orange-500" />
                  <span>{plan.features.storageGB}GB storage</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2 pt-4 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditPlan(plan)}
                  className="flex-1"
                >
                  <Edit className="h-3 w-3 mr-1" />
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDuplicatePlan(plan)}
                >
                  <Copy className="h-3 w-3" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDeletePlan(plan)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Edit/Create Plan Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingPlan ? 'Edit Plan' : 'Create New Plan'}
            </DialogTitle>
            <DialogDescription>
              {editingPlan ? 'Update the plan details below' : 'Create a new subscription plan'}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Basic Info */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Plan Name (ID)</Label>
                <Input
                  id="name"
                  value={formData.name || ''}
                  onChange={(e) => updateFormData('name', e.target.value)}
                  placeholder="e.g., professional"
                  disabled={!!editingPlan} // Don't allow changing ID of existing plans
                />
              </div>
              <div>
                <Label htmlFor="displayName">Display Name</Label>
                <Input
                  id="displayName"
                  value={formData.displayName || ''}
                  onChange={(e) => updateFormData('displayName', e.target.value)}
                  placeholder="e.g., Professional"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => updateFormData('description', e.target.value)}
                placeholder="Plan description..."
              />
            </div>

            {/* Pricing */}
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="monthly">Monthly Price ($)</Label>
                <Input
                  id="monthly"
                  type="number"
                  value={formData.pricing?.monthly || 0}
                  onChange={(e) => updateFormData('pricing.monthly', parseFloat(e.target.value) || 0)}
                />
              </div>
              <div>
                <Label htmlFor="yearly">Yearly Price ($)</Label>
                <Input
                  id="yearly"
                  type="number"
                  value={formData.pricing?.yearly || 0}
                  onChange={(e) => updateFormData('pricing.yearly', parseFloat(e.target.value) || 0)}
                />
              </div>
              <div>
                <Label htmlFor="discount">Yearly Discount (%)</Label>
                <Input
                  id="discount"
                  type="number"
                  value={formData.pricing?.yearlyDiscount || 0}
                  onChange={(e) => updateFormData('pricing.yearlyDiscount', parseFloat(e.target.value) || 0)}
                />
              </div>
            </div>

            {/* Limits */}
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="maxExhibitions">Max Exhibitions</Label>
                <Input
                  id="maxExhibitions"
                  type="number"
                  value={formData.features?.maxExhibitions || 0}
                  onChange={(e) => updateFormData('features.maxExhibitions', parseInt(e.target.value) || 0)}
                />
              </div>
              <div>
                <Label htmlFor="maxUsers">Max Users</Label>
                <Input
                  id="maxUsers"
                  type="number"
                  value={formData.features?.maxUsers || 0}
                  onChange={(e) => updateFormData('features.maxUsers', parseInt(e.target.value) || 0)}
                />
              </div>
              <div>
                <Label htmlFor="storageGB">Storage (GB)</Label>
                <Input
                  id="storageGB"
                  type="number"
                  value={formData.features?.storageGB || 0}
                  onChange={(e) => updateFormData('features.storageGB', parseInt(e.target.value) || 0)}
                />
              </div>
            </div>

            {/* Feature Toggles */}
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="advancedAnalytics"
                  checked={formData.features?.advancedAnalytics || false}
                  onCheckedChange={(checked) => updateFormData('features.advancedAnalytics', checked)}
                />
                <Label htmlFor="advancedAnalytics">Advanced Analytics</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="customBranding"
                  checked={formData.features?.customBranding || false}
                  onCheckedChange={(checked) => updateFormData('features.customBranding', checked)}
                />
                <Label htmlFor="customBranding">Custom Branding</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="apiAccess"
                  checked={formData.features?.apiAccess || false}
                  onCheckedChange={(checked) => updateFormData('features.apiAccess', checked)}
                />
                <Label htmlFor="apiAccess">API Access</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="prioritySupport"
                  checked={formData.features?.prioritySupport || false}
                  onCheckedChange={(checked) => updateFormData('features.prioritySupport', checked)}
                />
                <Label htmlFor="prioritySupport">Priority Support</Label>
              </div>
            </div>

            {/* Plan Settings */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive || false}
                  onCheckedChange={(checked) => updateFormData('isActive', checked)}
                />
                <Label htmlFor="isActive">Active</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isPopular"
                  checked={formData.isPopular || false}
                  onCheckedChange={(checked) => updateFormData('isPopular', checked)}
                />
                <Label htmlFor="isPopular">Popular</Label>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleSavePlan}>
              <Save className="h-4 w-4 mr-2" />
              {editingPlan ? 'Update Plan' : 'Create Plan'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
