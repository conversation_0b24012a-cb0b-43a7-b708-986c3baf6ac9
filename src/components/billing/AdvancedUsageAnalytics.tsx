"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/auth-context';
// import { useTenant } from '@/hooks/useTenant';
import { auth } from '@/lib/firebase';
import { toast } from 'sonner';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  Area,
  AreaChart
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  DollarSign,
  Activity,
  Users,
  Database,
  Zap,
  Target,
  Calendar,
  ArrowUp,
  ArrowDown,
  Minus,
  Eye,
  Download,
  RefreshCw
} from 'lucide-react';
import { UsageAnalytics, OverageBilling } from '@/services/advancedUsageAnalyticsService';

interface AdvancedUsageAnalyticsProps {
  className?: string;
}

const COLORS = ['#2563eb', '#dc2626', '#059669', '#d97706', '#7c3aed', '#db2777'];

export default function AdvancedUsageAnalytics({ className }: AdvancedUsageAnalyticsProps) {
  const { user } = useAuth();
  // const { tenantId } = useTenant();
  const tenantId = 'default-tenant'; // Temporary fallback
  const [loading, setLoading] = useState(true);
  const [analytics, setAnalytics] = useState<UsageAnalytics | null>(null);
  const [currentUsage, setCurrentUsage] = useState<Record<string, any>>({});
  const [overageBilling, setOverageBilling] = useState<OverageBilling[]>([]);
  const [usageTrends, setUsageTrends] = useState<any[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState<'daily' | 'weekly' | 'monthly' | 'quarterly'>('monthly');
  const [dateRange, setDateRange] = useState({
    start: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    end: new Date()
  });

  useEffect(() => {
    if (user && tenantId) {
      loadAnalyticsData();
    }
  }, [user, tenantId, selectedPeriod]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      const firebaseUser = auth.currentUser;
      if (!firebaseUser) {
        throw new Error('User not authenticated');
      }
      const token = await firebaseUser.getIdToken();
      
      const [analyticsRes, currentUsageRes, overageRes, trendsRes] = await Promise.all([
        fetch(`/api/usage-analytics?action=analytics&tenantId=${tenantId}&period=${selectedPeriod}&startDate=${dateRange.start.toISOString()}&endDate=${dateRange.end.toISOString()}`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        fetch(`/api/usage-analytics?action=current-usage&tenantId=${tenantId}`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        fetch(`/api/usage-analytics?action=overage-billing&tenantId=${tenantId}`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        fetch(`/api/usage-analytics?action=usage-trends&tenantId=${tenantId}`, {
          headers: { Authorization: `Bearer ${token}` }
        })
      ]);

      if (analyticsRes.ok) {
        const analyticsData = await analyticsRes.json();
        setAnalytics(analyticsData);
      }

      if (currentUsageRes.ok) {
        const currentUsageData = await currentUsageRes.json();
        setCurrentUsage(currentUsageData);
      }

      if (overageRes.ok) {
        const overageData = await overageRes.json();
        setOverageBilling(overageData);
      }

      if (trendsRes.ok) {
        const trendsData = await trendsRes.json();
        setUsageTrends(trendsData);
      }
    } catch (error) {
      console.error('Error loading analytics data:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const formatCurrency = (amount: number, currency: string = 'USD'): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(amount);
  };

  const getTrendIcon = (trend: 'increasing' | 'decreasing' | 'stable') => {
    switch (trend) {
      case 'increasing': return <ArrowUp className="h-4 w-4 text-green-600" />;
      case 'decreasing': return <ArrowDown className="h-4 w-4 text-red-600" />;
      default: return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const getRiskBadge = (risk: 'low' | 'medium' | 'high') => {
    const config = {
      low: { color: 'bg-green-100 text-green-800', label: 'Low Risk' },
      medium: { color: 'bg-yellow-100 text-yellow-800', label: 'Medium Risk' },
      high: { color: 'bg-red-100 text-red-800', label: 'High Risk' }
    };
    
    const { color, label } = config[risk];
    return <Badge className={color}>{label}</Badge>;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">No analytics data available</p>
        <Button onClick={loadAnalyticsData} className="mt-4">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  // Prepare chart data
  const usageChartData = Object.entries(analytics.totalUsage).map(([category, data]) => ({
    category: category.replace('_', ' ').toUpperCase(),
    usage: data.quantity,
    cost: data.cost,
    growth: data.growth
  }));

  const trendChartData = usageTrends.map(trend => ({
    period: trend.period,
    ...Object.entries(trend.usage).reduce((acc, [category, data]: [string, any]) => ({
      ...acc,
      [category]: data.quantity
    }), {})
  }));

  const pieChartData = analytics.topFeatures.slice(0, 6).map((feature, index) => ({
    name: feature.feature,
    value: feature.usage,
    color: COLORS[index % COLORS.length]
  }));

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Advanced Usage Analytics</h2>
            <p className="text-gray-600">Detailed usage tracking, overage billing, and predictive analytics</p>
          </div>
          <div className="flex items-center gap-2">
            <Select value={selectedPeriod} onValueChange={(value: any) => setSelectedPeriod(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={loadAnalyticsData} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* High-Risk Alerts */}
        {Object.entries(analytics.predictions.overageRisk).some(([_, risk]) => risk.risk === 'high') && (
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <strong>High Overage Risk Detected!</strong> You're projected to exceed limits in:{' '}
              {Object.entries(analytics.predictions.overageRisk)
                .filter(([_, risk]) => risk.risk === 'high')
                .map(([category]) => category)
                .join(', ')}
            </AlertDescription>
          </Alert>
        )}

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Usage Cost</p>
                  <p className="text-2xl font-bold">
                    {formatCurrency(Object.values(analytics.totalUsage).reduce((sum, u) => sum + u.cost, 0))}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Active Users</p>
                  <p className="text-2xl font-bold">{analytics.userActivity.activeUsers}</p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Sessions</p>
                  <p className="text-2xl font-bold">{formatNumber(analytics.userActivity.totalSessions)}</p>
                </div>
                <Activity className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Overage Charges</p>
                  <p className="text-2xl font-bold">
                    {formatCurrency(overageBilling.reduce((sum, o) => sum + o.overageAmount, 0))}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="usage-breakdown">Usage Breakdown</TabsTrigger>
            <TabsTrigger value="predictions">Predictions</TabsTrigger>
            <TabsTrigger value="overage-billing">Overage Billing</TabsTrigger>
            <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Usage Trends Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Usage Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={trendChartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="period" />
                      <YAxis />
                      <Tooltip />
                      {Object.keys(analytics.totalUsage).map((category, index) => (
                        <Area
                          key={category}
                          type="monotone"
                          dataKey={category}
                          stackId="1"
                          stroke={COLORS[index % COLORS.length]}
                          fill={COLORS[index % COLORS.length]}
                          fillOpacity={0.6}
                        />
                      ))}
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Current Usage by Category */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Usage by Category</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={usageChartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="category" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="usage" fill="#2563eb" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Top Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={pieChartData}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        >
                          {pieChartData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="usage-breakdown" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {Object.entries(analytics.totalUsage).map(([category, data]) => (
                <Card key={category}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span className="capitalize">{category.replace('_', ' ')}</span>
                      {getTrendIcon(data.trend)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Current Usage</span>
                        <span className="font-medium">{formatNumber(data.quantity)} {data.unit}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Cost</span>
                        <span className="font-medium">{formatCurrency(data.cost)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Growth</span>
                        <span className={`font-medium ${data.growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {data.growth >= 0 ? '+' : ''}{data.growth.toFixed(1)}%
                        </span>
                      </div>
                      {analytics.predictions.overageRisk[category] && (
                        <div className="pt-2 border-t">
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm text-gray-600">Overage Risk</span>
                            {getRiskBadge(analytics.predictions.overageRisk[category].risk)}
                          </div>
                          <Progress
                            value={(analytics.predictions.overageRisk[category].currentUsage / analytics.predictions.overageRisk[category].limit) * 100}
                            className="h-2"
                          />
                          <div className="flex justify-between text-xs text-gray-500 mt-1">
                            <span>{analytics.predictions.overageRisk[category].currentUsage}</span>
                            <span>{analytics.predictions.overageRisk[category].limit}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="predictions" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Next Period Predictions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(analytics.predictions.nextPeriodUsage).map(([category, prediction]) => (
                      <div key={category} className="border rounded-lg p-4">
                        <div className="flex justify-between items-center mb-2">
                          <h4 className="font-medium capitalize">{category.replace('_', ' ')}</h4>
                          <Badge className="bg-blue-100 text-blue-800">
                            {(prediction.confidence * 100).toFixed(0)}% confidence
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Predicted Usage:</span>
                            <div className="font-medium">{formatNumber(prediction.predicted)}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Estimated Cost:</span>
                            <div className="font-medium">{formatCurrency(prediction.estimatedCost)}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Overage Risk Assessment</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(analytics.predictions.overageRisk).map(([category, risk]) => (
                      <div key={category} className="border rounded-lg p-4">
                        <div className="flex justify-between items-center mb-2">
                          <h4 className="font-medium capitalize">{category.replace('_', ' ')}</h4>
                          {getRiskBadge(risk.risk)}
                        </div>
                        <div className="space-y-2">
                          <Progress
                            value={(risk.currentUsage / risk.limit) * 100}
                            className="h-2"
                          />
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-gray-600">Current:</span>
                              <div className="font-medium">{formatNumber(risk.currentUsage)}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Limit:</span>
                              <div className="font-medium">{formatNumber(risk.limit)}</div>
                            </div>
                          </div>
                          {risk.projectedOverage > 0 && (
                            <div className="text-sm">
                              <span className="text-gray-600">Projected Overage:</span>
                              <span className="font-medium text-red-600 ml-1">
                                {formatNumber(risk.projectedOverage)} units
                              </span>
                            </div>
                          )}
                          <div className="text-xs text-gray-500">
                            {risk.daysRemaining} days remaining in period
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="overage-billing" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Overage Billing History</CardTitle>
              </CardHeader>
              <CardContent>
                {overageBilling.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No overage charges found
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50 border-b">
                        <tr>
                          <th className="p-3 text-left font-medium text-gray-900">Period</th>
                          <th className="p-3 text-left font-medium text-gray-900">Category</th>
                          <th className="p-3 text-left font-medium text-gray-900">Usage</th>
                          <th className="p-3 text-left font-medium text-gray-900">Overage</th>
                          <th className="p-3 text-left font-medium text-gray-900">Amount</th>
                          <th className="p-3 text-left font-medium text-gray-900">Status</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {overageBilling.map((overage) => (
                          <tr key={overage.id} className="hover:bg-gray-50">
                            <td className="p-3">{overage.billingPeriod}</td>
                            <td className="p-3 capitalize">{overage.category.replace('_', ' ')}</td>
                            <td className="p-3">
                              {formatNumber(overage.actualUsage)} / {formatNumber(overage.baseLimit)}
                            </td>
                            <td className="p-3 text-red-600">
                              +{formatNumber(overage.overageQuantity)}
                            </td>
                            <td className="p-3 font-medium">
                              {formatCurrency(overage.overageAmount, overage.currency)}
                            </td>
                            <td className="p-3">
                              <Badge className={
                                overage.status === 'paid' ? 'bg-green-100 text-green-800' :
                                overage.status === 'billed' ? 'bg-blue-100 text-blue-800' :
                                overage.status === 'disputed' ? 'bg-red-100 text-red-800' :
                                'bg-gray-100 text-gray-800'
                              }>
                                {overage.status}
                              </Badge>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="recommendations" className="space-y-4">
            <div className="space-y-4">
              {analytics.recommendations.map((recommendation, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge className={
                            recommendation.impact === 'high' ? 'bg-red-100 text-red-800' :
                            recommendation.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-green-100 text-green-800'
                          }>
                            {recommendation.impact} impact
                          </Badge>
                          <Badge variant="outline" className="capitalize">
                            {recommendation.type.replace('_', ' ')}
                          </Badge>
                          {recommendation.actionRequired && (
                            <Badge className="bg-orange-100 text-orange-800">
                              Action Required
                            </Badge>
                          )}
                        </div>
                        <h3 className="font-medium text-gray-900 mb-1">
                          {recommendation.title}
                        </h3>
                        <p className="text-sm text-gray-600 mb-2">
                          {recommendation.description}
                        </p>
                        {recommendation.estimatedSavings && (
                          <p className="text-sm font-medium text-green-600">
                            Potential savings: {formatCurrency(recommendation.estimatedSavings)}
                          </p>
                        )}
                      </div>
                      {recommendation.actionRequired && (
                        <Button size="sm">
                          Take Action
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}

              {analytics.recommendations.length === 0 && (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="font-medium text-gray-900 mb-2">All Good!</h3>
                    <p className="text-gray-600">
                      Your usage patterns look optimal. No recommendations at this time.
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
