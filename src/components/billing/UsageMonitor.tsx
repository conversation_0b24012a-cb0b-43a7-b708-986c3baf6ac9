'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Calendar, 
  Users, 
  Database, 
  Mail, 
  Zap, 
  Building2,
  CheckCircle2,
  AlertTriangle,
  XCircle,
  TrendingUp,
  ArrowRight
} from 'lucide-react';
import { useTenantAware } from '@/hooks/useTenantAware';
import { subscriptionService } from '@/services/subscriptionService';
import UpgradePrompt from './UpgradePrompt';

interface UsageItem {
  key: string;
  name: string;
  icon: React.ReactNode;
  current: number;
  limit: number;
  unit: string;
  color: string;
  description: string;
}

interface UsageMonitorProps {
  showUpgradePrompts?: boolean;
  compact?: boolean;
  onUpgrade?: () => void;
}

export default function UsageMonitor({ 
  showUpgradePrompts = true, 
  compact = false,
  onUpgrade 
}: UsageMonitorProps) {
  const { tenant, getCurrentUsage, getUsageLimits, isOnPlan } = useTenantAware();
  const [usage, setUsage] = useState<any>(null);
  const [limits, setLimits] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [upgradePrompt, setUpgradePrompt] = useState<{
    show: boolean;
    trigger: 'limit_reached' | 'usage_warning';
    feature: string;
    current: number;
    limit: number;
  } | null>(null);

  useEffect(() => {
    loadUsageData();
  }, [tenant]);

  const loadUsageData = async () => {
    if (!tenant) return;

    try {
      setLoading(true);
      const currentUsage = getCurrentUsage();
      const currentLimits = getUsageLimits();
      
      setUsage(currentUsage);
      setLimits(currentLimits);
    } catch (error) {
      console.error('Error loading usage data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getUsageItems = (): UsageItem[] => {
    if (!usage || !limits) return [];

    return [
      {
        key: 'exhibitions',
        name: 'Exhibitions',
        icon: <Building2 className="h-4 w-4" />,
        current: usage.exhibitions || 0,
        limit: limits.exhibitions || 0,
        unit: 'exhibitions',
        color: 'blue',
        description: 'Active exhibitions you can manage'
      },
      {
        key: 'events',
        name: 'Events',
        icon: <Calendar className="h-4 w-4" />,
        current: usage.events || 0,
        limit: limits.events || 0,
        unit: 'events',
        color: 'green',
        description: 'Events within your exhibitions'
      },
      {
        key: 'users',
        name: 'Team Members',
        icon: <Users className="h-4 w-4" />,
        current: usage.users || 0,
        limit: limits.users || 0,
        unit: 'users',
        color: 'purple',
        description: 'Team members in your organization'
      },
      {
        key: 'storage',
        name: 'Storage',
        icon: <Database className="h-4 w-4" />,
        current: usage.storage || 0,
        limit: limits.storage || 0,
        unit: 'GB',
        color: 'orange',
        description: 'File storage for documents and media'
      },
      {
        key: 'emails',
        name: 'Emails',
        icon: <Mail className="h-4 w-4" />,
        current: usage.emailsSentThisMonth || 0,
        limit: limits.emailsPerMonth || 0,
        unit: 'emails/month',
        color: 'cyan',
        description: 'Email communications sent this month'
      },
      {
        key: 'apiCalls',
        name: 'API Calls',
        icon: <Zap className="h-4 w-4" />,
        current: usage.apiCallsThisMonth || 0,
        limit: limits.apiCallsPerMonth || 0,
        unit: 'calls/month',
        color: 'yellow',
        description: 'API requests made this month'
      }
    ];
  };

  const getUsageStatus = (current: number, limit: number) => {
    if (limit === -1) return 'unlimited';
    const percentage = (current / limit) * 100;
    if (percentage >= 100) return 'exceeded';
    if (percentage >= 80) return 'warning';
    return 'normal';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'exceeded':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'unlimited':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      default:
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'exceeded':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'warning':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'unlimited':
        return 'text-green-600 bg-green-50 border-green-200';
      default:
        return 'text-green-600 bg-green-50 border-green-200';
    }
  };

  const handleUsageClick = (item: UsageItem) => {
    const status = getUsageStatus(item.current, item.limit);
    
    if (status === 'exceeded' && showUpgradePrompts) {
      setUpgradePrompt({
        show: true,
        trigger: 'limit_reached',
        feature: item.name,
        current: item.current,
        limit: item.limit
      });
    } else if (status === 'warning' && showUpgradePrompts) {
      setUpgradePrompt({
        show: true,
        trigger: 'usage_warning',
        feature: item.name,
        current: item.current,
        limit: item.limit
      });
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Usage Overview</CardTitle>
          <CardDescription>Loading usage data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-2 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const usageItems = getUsageItems();

  if (compact) {
    return (
      <div className="space-y-2">
        {usageItems.map((item) => {
          const status = getUsageStatus(item.current, item.limit);
          const percentage = item.limit === -1 ? 0 : (item.current / item.limit) * 100;
          
          return (
            <div 
              key={item.key}
              className={`flex items-center justify-between p-2 rounded-lg border cursor-pointer hover:bg-gray-50 ${getStatusColor(status)}`}
              onClick={() => handleUsageClick(item)}
            >
              <div className="flex items-center space-x-2">
                {item.icon}
                <span className="text-sm font-medium">{item.name}</span>
                {getStatusIcon(status)}
              </div>
              <div className="text-right">
                <div className="text-sm font-medium">
                  {item.current} / {item.limit === -1 ? '∞' : item.limit}
                </div>
                {item.limit !== -1 && (
                  <div className="text-xs text-muted-foreground">
                    {percentage.toFixed(0)}% used
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Usage Overview</span>
              </CardTitle>
              <CardDescription>
                Monitor your current usage across all resources
              </CardDescription>
            </div>
            <Badge variant="outline">
              {isOnPlan('free') ? 'Free Plan' : 
               isOnPlan('starter') ? 'Starter Plan' : 
               isOnPlan('professional') ? 'Professional Plan' : 'Enterprise Plan'}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {usageItems.map((item) => {
              const status = getUsageStatus(item.current, item.limit);
              const percentage = item.limit === -1 ? 0 : (item.current / item.limit) * 100;
              
              return (
                <div 
                  key={item.key}
                  className={`p-4 rounded-lg border cursor-pointer hover:shadow-md transition-shadow ${getStatusColor(status)}`}
                  onClick={() => handleUsageClick(item)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {item.icon}
                      <span className="font-medium">{item.name}</span>
                    </div>
                    {getStatusIcon(status)}
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{item.current} {item.unit}</span>
                      <span>
                        {item.limit === -1 ? 'Unlimited' : `${item.limit} ${item.unit}`}
                      </span>
                    </div>
                    
                    {item.limit !== -1 && (
                      <Progress value={percentage} className="h-2" />
                    )}
                    
                    <p className="text-xs text-muted-foreground">
                      {item.description}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Upgrade Prompt Modal */}
      {upgradePrompt?.show && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="max-w-md w-full">
            <UpgradePrompt
              trigger={upgradePrompt.trigger}
              feature={upgradePrompt.feature}
              currentUsage={upgradePrompt.current}
              limit={upgradePrompt.limit}
              onUpgrade={() => {
                setUpgradePrompt(null);
                onUpgrade?.();
              }}
              onDismiss={() => setUpgradePrompt(null)}
            />
          </div>
        </div>
      )}
    </div>
  );
}
