"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Play, 
  Pause, 
  Stop, 
  Settings, 
  Plus,
  Mail,
  MessageSquare,
  Phone,
  Calendar
} from 'lucide-react';

export interface AutomatedSequence {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'paused' | 'stopped' | 'draft';
  trigger: {
    type: 'event' | 'date' | 'behavior' | 'manual';
    condition: string;
  };
  steps: SequenceStep[];
  metrics: {
    totalEnrolled: number;
    completed: number;
    active: number;
    conversionRate: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface SequenceStep {
  id: string;
  type: 'email' | 'sms' | 'call' | 'wait' | 'condition' | 'action';
  name: string;
  delay: number; // in hours
  content?: {
    subject?: string;
    body?: string;
    template?: string;
  };
  condition?: {
    field: string;
    operator: 'equals' | 'contains' | 'greater_than' | 'less_than';
    value: string;
  };
  metrics: {
    sent: number;
    opened: number;
    clicked: number;
    replied: number;
  };
}

interface AutomatedSequencesProps {
  sequences?: AutomatedSequence[];
  onCreateSequence?: () => void;
  onEditSequence?: (sequenceId: string) => void;
  onToggleSequence?: (sequenceId: string, status: 'active' | 'paused') => void;
}

export const AutomatedSequences: React.FC<AutomatedSequencesProps> = ({
  sequences = [],
  onCreateSequence,
  onEditSequence,
  onToggleSequence
}) => {
  const [selectedSequence, setSelectedSequence] = useState<string | null>(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'paused': return 'bg-yellow-500';
      case 'stopped': return 'bg-red-500';
      case 'draft': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const getStepIcon = (type: string) => {
    switch (type) {
      case 'email': return <Mail className="h-4 w-4" />;
      case 'sms': return <MessageSquare className="h-4 w-4" />;
      case 'call': return <Phone className="h-4 w-4" />;
      case 'wait': return <Calendar className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Automated Sequences</h2>
        <Button onClick={onCreateSequence}>
          <Plus className="h-4 w-4 mr-2" />
          Create Sequence
        </Button>
      </div>

      <div className="grid gap-6">
        {sequences.map((sequence) => (
          <Card key={sequence.id} className="w-full">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    {sequence.name}
                    <Badge 
                      variant="secondary" 
                      className={`${getStatusColor(sequence.status)} text-white`}
                    >
                      {sequence.status}
                    </Badge>
                  </CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    {sequence.description}
                  </p>
                </div>
                <div className="flex gap-2">
                  {sequence.status === 'active' ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onToggleSequence?.(sequence.id, 'paused')}
                    >
                      <Pause className="h-4 w-4" />
                    </Button>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onToggleSequence?.(sequence.id, 'active')}
                    >
                      <Play className="h-4 w-4" />
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEditSequence?.(sequence.id)}
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {sequence.metrics.totalEnrolled}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Enrolled</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {sequence.metrics.active}
                  </div>
                  <div className="text-sm text-muted-foreground">Active</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {sequence.metrics.completed}
                  </div>
                  <div className="text-sm text-muted-foreground">Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {sequence.metrics.conversionRate}%
                  </div>
                  <div className="text-sm text-muted-foreground">Conversion Rate</div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{Math.round((sequence.metrics.completed / sequence.metrics.totalEnrolled) * 100)}%</span>
                </div>
                <Progress 
                  value={(sequence.metrics.completed / sequence.metrics.totalEnrolled) * 100} 
                  className="w-full"
                />
              </div>

              <div className="mt-4">
                <h4 className="font-medium mb-2">Sequence Steps ({sequence.steps.length})</h4>
                <div className="flex gap-2 overflow-x-auto">
                  {sequence.steps.map((step, index) => (
                    <div
                      key={step.id}
                      className="flex-shrink-0 flex items-center gap-2 p-2 border rounded-lg min-w-[120px]"
                    >
                      {getStepIcon(step.type)}
                      <div className="text-xs">
                        <div className="font-medium">{step.name}</div>
                        <div className="text-muted-foreground">
                          {step.delay}h delay
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {sequences.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-muted-foreground mb-4">
              No automated sequences created yet.
            </p>
            <Button onClick={onCreateSequence}>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Sequence
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AutomatedSequences;
