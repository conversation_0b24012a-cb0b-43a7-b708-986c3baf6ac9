/**
 * Enhanced Permission Gate Components
 * Advanced permission gates using the Module Permission Engine
 */

import React, { ReactNode, useMemo } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Lock, AlertTriangle, Zap, Crown } from 'lucide-react';
import {
  useModuleAccessEnhanced,
  useBulkPermissions
} from '@/hooks/useModulePermissionEngine';
import { EvexaModule, PermissionAction } from '@/types/personas';

// ===== TYPES =====

interface EnhancedPermissionGateProps {
  module: EvexaModule;
  action?: PermissionAction;
  actions?: PermissionAction[];
  children: ReactNode;
  fallback?: ReactNode;
  showUpgradePrompt?: boolean;
  showPermissionInfo?: boolean;
  requireAll?: boolean;
  className?: string;
}

interface BulkPermissionGateProps {
  permissions: Array<{ module: EvexaModule; action: PermissionAction }>;
  children: ReactNode;
  fallback?: ReactNode;
  requireAll?: boolean;
  showMissingPermissions?: boolean;
  className?: string;
}

interface PermissionInfoProps {
  module: EvexaModule;
  permissions: PermissionAction[];
  hasAccess: boolean;
  className?: string;
}

interface UpgradePromptProps {
  module: EvexaModule;
  requiredAction: PermissionAction;
  className?: string;
}

// ===== ENHANCED PERMISSION GATE =====

const EnhancedPermissionGate: React.FC<EnhancedPermissionGateProps> = ({
  module,
  action = 'read',
  actions,
  children,
  fallback,
  showUpgradePrompt = true,
  showPermissionInfo = false,
  requireAll = true,
  className
}) => {
  const {
    hasAccess,
    permissions,
    isLoading,
    error,
    moduleMetadata,
    checkAction
  } = useModuleAccessEnhanced(module, action);

  // SUPER ADMIN BYPASS: Check if user is super admin
  const { user } = useAuth();
  const { isSuperAdmin: checkSuperAdmin } = require('@/services/superAdminService');
  const isSuperAdmin = user && checkSuperAdmin(user.id);

  // SUPER ADMIN BYPASS: Always allow access for super admin
  if (isSuperAdmin) {
    return <>{children}</>;
  }

  // Determine if user has required permissions
  const hasRequiredPermissions = useMemo(() => {
    if (actions) {
      return requireAll
        ? actions.every(a => checkAction(a))
        : actions.some(a => checkAction(a));
    }
    return hasAccess;
  }, [actions, requireAll, checkAction, hasAccess]);

  // Loading state
  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Permission check failed: {error}
        </AlertDescription>
      </Alert>
    );
  }

  // Access granted
  if (hasRequiredPermissions) {
    return (
      <div className={className}>
        {showPermissionInfo && (
          <PermissionInfo 
            module={module} 
            permissions={permissions} 
            hasAccess={hasAccess}
            className="mb-4"
          />
        )}
        {children}
      </div>
    );
  }

  // Access denied - show fallback or upgrade prompt
  if (fallback) {
    return <div className={className}>{fallback}</div>;
  }

  if (showUpgradePrompt) {
    return (
      <UpgradePrompt 
        module={module} 
        requiredAction={actions?.[0] || action}
        className={className}
      />
    );
  }

  return null;
};

// ===== BULK PERMISSION GATE =====

const BulkPermissionGate: React.FC<BulkPermissionGateProps> = ({
  permissions,
  children,
  fallback,
  requireAll = true,
  showMissingPermissions = true,
  className
}) => {
  const { 
    results, 
    isLoading, 
    error, 
    hasAllPermissions, 
    deniedPermissions 
  } = useBulkPermissions(permissions);

  const hasAccess = useMemo(() => {
    if (!results) return false;
    
    if (requireAll) {
      return hasAllPermissions;
    } else {
      return results.results.some(r => r.hasPermission);
    }
  }, [results, requireAll, hasAllPermissions]);

  // Loading state
  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Permission check failed: {error}
        </AlertDescription>
      </Alert>
    );
  }

  // Access granted
  if (hasAccess) {
    return <div className={className}>{children}</div>;
  }

  // Access denied
  if (fallback) {
    return <div className={className}>{fallback}</div>;
  }

  if (showMissingPermissions && deniedPermissions.length > 0) {
    return (
      <MissingPermissionsAlert 
        deniedPermissions={deniedPermissions}
        className={className}
      />
    );
  }

  return null;
};

// ===== PERMISSION INFO COMPONENT =====

const PermissionInfo: React.FC<PermissionInfoProps> = ({
  module,
  permissions,
  hasAccess,
  className
}) => {
  return (
    <Card className={`border-l-4 ${hasAccess ? 'border-l-green-500' : 'border-l-red-500'} ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex items-center gap-2">
          {hasAccess ? (
            <Zap className="h-4 w-4 text-green-500" />
          ) : (
            <Lock className="h-4 w-4 text-red-500" />
          )}
          {module.charAt(0).toUpperCase() + module.slice(1)} Module
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex flex-wrap gap-1">
          {['read', 'write', 'delete', 'admin'].map(action => (
            <Badge 
              key={action}
              variant={permissions.includes(action as PermissionAction) ? 'default' : 'secondary'}
              className="text-xs"
            >
              {action}
            </Badge>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// ===== UPGRADE PROMPT COMPONENT =====

const UpgradePrompt: React.FC<UpgradePromptProps> = ({
  module,
  requiredAction,
  className
}) => {
  return (
    <Card className={`border-amber-200 bg-amber-50 ${className}`}>
      <CardHeader>
        <CardTitle className="text-amber-800 flex items-center gap-2">
          <Crown className="h-5 w-5" />
          Premium Feature
        </CardTitle>
        <CardDescription className="text-amber-700">
          You need {requiredAction} access to the {module} module to use this feature.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            Learn More
          </Button>
          <Button size="sm" className="bg-amber-600 hover:bg-amber-700">
            Upgrade Plan
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// ===== MISSING PERMISSIONS ALERT =====

interface MissingPermissionsAlertProps {
  deniedPermissions: Array<{ module: EvexaModule; action: PermissionAction }>;
  className?: string;
}

const MissingPermissionsAlert: React.FC<MissingPermissionsAlertProps> = ({
  deniedPermissions,
  className
}) => {
  return (
    <Alert variant="destructive" className={className}>
      <Lock className="h-4 w-4" />
      <AlertDescription>
        <div className="space-y-2">
          <p>You don't have the required permissions:</p>
          <ul className="list-disc list-inside text-sm">
            {deniedPermissions.map(({ module, action }, index) => (
              <li key={index}>
                {action} access to {module}
              </li>
            ))}
          </ul>
          <Button variant="outline" size="sm" className="mt-2">
            Request Access
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  );
};

// ===== CONVENIENCE COMPONENTS =====

/**
 * Quick permission gate for read access
 */
const ReadGate: React.FC<{
  module: EvexaModule;
  children: ReactNode;
  fallback?: ReactNode;
}> = ({ module, children, fallback }) => (
  <EnhancedPermissionGate 
    module={module} 
    action="read" 
    fallback={fallback}
    showUpgradePrompt={false}
  >
    {children}
  </EnhancedPermissionGate>
);

/**
 * Quick permission gate for write access
 */
const WriteGate: React.FC<{
  module: EvexaModule;
  children: ReactNode;
  fallback?: ReactNode;
}> = ({ module, children, fallback }) => (
  <EnhancedPermissionGate 
    module={module} 
    action="write" 
    fallback={fallback}
  >
    {children}
  </EnhancedPermissionGate>
);

/**
 * Quick permission gate for admin access
 */
const AdminGate: React.FC<{
  module: EvexaModule;
  children: ReactNode;
  fallback?: ReactNode;
}> = ({ module, children, fallback }) => (
  <EnhancedPermissionGate
    module={module}
    action="admin"
    fallback={fallback}
  >
    {children}
  </EnhancedPermissionGate>
);

// ===== EXPORT ALL COMPONENTS =====

export {
  EnhancedPermissionGate,
  BulkPermissionGate,
  ReadGate,
  WriteGate,
  AdminGate
};
