/**
 * Higher-Order Component for Permission-Based Access Control
 * Wraps components with permission checking logic
 */

import React, { ComponentType, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '@/contexts/auth-context';
import { usePersonaPermissions } from '@/hooks/usePersonaPermissions';
import { EvexaModule, PermissionAction } from '@/types/personas';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Lock, ArrowLeft, Home } from 'lucide-react';

// ===== PERMISSION REQUIREMENT TYPES =====

export interface PermissionRequirement {
  module: EvexaModule;
  action: PermissionAction;
}

export interface SystemPermissionRequirement {
  systemPermission: keyof ReturnType<typeof usePersonaPermissions>['userPersona']['permissions']['systemPermissions'];
}

export interface WithPermissionsOptions {
  // Module permissions required
  modulePermissions?: PermissionRequirement[];
  
  // System permissions required
  systemPermissions?: string[];
  
  // Whether all permissions are required (AND) or any permission (OR)
  requireAll?: boolean;
  
  // Custom fallback component
  fallbackComponent?: ComponentType<{ reason: string; onRetry: () => void }>;
  
  // Redirect path when access is denied
  redirectTo?: string;
  
  // Whether to show loading state while checking permissions
  showLoading?: boolean;
  
  // Custom loading component
  loadingComponent?: ComponentType;
  
  // Whether to redirect or show fallback on access denied
  redirectOnDenied?: boolean;
}

// ===== DEFAULT COMPONENTS =====

const DefaultLoadingComponent: React.FC = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p className="text-gray-600">Checking permissions...</p>
    </div>
  </div>
);

const DefaultAccessDeniedComponent: React.FC<{ 
  reason: string; 
  onRetry: () => void;
  onGoBack: () => void;
  onGoHome: () => void;
}> = ({ reason, onRetry, onGoBack, onGoHome }) => (
  <div className="flex items-center justify-center min-h-screen bg-gray-50">
    <div className="max-w-md w-full mx-4">
      <Alert variant="destructive" className="mb-6">
        <Lock className="h-4 w-4" />
        <AlertDescription className="mt-2">
          <strong>Access Denied</strong>
          <br />
          {reason}
        </AlertDescription>
      </Alert>
      
      <div className="space-y-3">
        <Button 
          onClick={onRetry} 
          variant="outline" 
          className="w-full"
        >
          Retry
        </Button>
        
        <Button 
          onClick={onGoBack} 
          variant="outline" 
          className="w-full"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go Back
        </Button>
        
        <Button 
          onClick={onGoHome} 
          variant="default" 
          className="w-full"
        >
          <Home className="h-4 w-4 mr-2" />
          Go to Dashboard
        </Button>
      </div>
    </div>
  </div>
);

// ===== HIGHER-ORDER COMPONENT =====

export function withPermissions<P extends object>(
  WrappedComponent: ComponentType<P>,
  options: WithPermissionsOptions = {}
) {
  const {
    modulePermissions = [],
    systemPermissions = [],
    requireAll = true,
    fallbackComponent: CustomFallback,
    redirectTo = '/dashboard',
    showLoading = true,
    loadingComponent: CustomLoading,
    redirectOnDenied = false
  } = options;

  const WithPermissionsComponent: React.FC<P> = (props) => {
    const router = useRouter();
    const { user, isLoading: authLoading } = useAuth();
    const { 
      checkModuleAccess, 
      hasSystemPermission, 
      isLoading: permissionsLoading,
      refreshPermissions 
    } = usePersonaPermissions();
    
    const [accessChecked, setAccessChecked] = useState(false);
    const [hasAccess, setHasAccess] = useState(false);
    const [deniedReason, setDeniedReason] = useState('');

    // Check permissions when component mounts or dependencies change
    useEffect(() => {
      const checkAccess = async () => {
        if (authLoading || permissionsLoading || !user) {
          return;
        }

        try {
          // Check module permissions
          const moduleChecks = await Promise.all(
            modulePermissions.map(async ({ module, action }) => {
              const hasAccess = checkModuleAccess(module, action);
              return { module, action, hasAccess };
            })
          );

          // Check system permissions
          const systemChecks = systemPermissions.map(permission => {
            const hasAccess = hasSystemPermission(permission as any);
            return { permission, hasAccess };
          });

          // Determine overall access
          let overallAccess = true;
          const deniedPermissions: string[] = [];

          if (requireAll) {
            // All permissions must be granted
            moduleChecks.forEach(({ module, action, hasAccess }) => {
              if (!hasAccess) {
                overallAccess = false;
                deniedPermissions.push(`${module}:${action}`);
              }
            });

            systemChecks.forEach(({ permission, hasAccess }) => {
              if (!hasAccess) {
                overallAccess = false;
                deniedPermissions.push(permission);
              }
            });
          } else {
            // At least one permission must be granted
            const hasAnyModuleAccess = moduleChecks.some(({ hasAccess }) => hasAccess);
            const hasAnySystemAccess = systemChecks.some(({ hasAccess }) => hasAccess);
            
            overallAccess = hasAnyModuleAccess || hasAnySystemAccess;
            
            if (!overallAccess) {
              deniedPermissions.push(
                ...moduleChecks.map(({ module, action }) => `${module}:${action}`),
                ...systemChecks.map(({ permission }) => permission)
              );
            }
          }

          setHasAccess(overallAccess);
          setAccessChecked(true);

          if (!overallAccess) {
            const reason = `You need the following permissions: ${deniedPermissions.join(', ')}`;
            setDeniedReason(reason);

            if (redirectOnDenied) {
              router.replace(redirectTo);
              return;
            }
          }

        } catch (error) {
          console.error('Error checking permissions:', error);
          setHasAccess(false);
          setAccessChecked(true);
          setDeniedReason('Error checking permissions. Please try again.');
        }
      };

      checkAccess();
    }, [
      authLoading, 
      permissionsLoading, 
      user, 
      checkModuleAccess, 
      hasSystemPermission,
      router
    ]);

    // Handle retry
    const handleRetry = async () => {
      setAccessChecked(false);
      await refreshPermissions();
    };

    // Handle navigation
    const handleGoBack = () => {
      if (window.history.length > 1) {
        router.back();
      } else {
        router.push('/dashboard');
      }
    };

    const handleGoHome = () => {
      router.push('/dashboard');
    };

    // Show loading state
    if (authLoading || permissionsLoading || !accessChecked) {
      if (!showLoading) {
        return null;
      }
      
      const LoadingComponent = CustomLoading || DefaultLoadingComponent;
      return <LoadingComponent />;
    }

    // Show access denied
    if (!hasAccess) {
      if (CustomFallback) {
        return <CustomFallback reason={deniedReason} onRetry={handleRetry} />;
      }
      
      return (
        <DefaultAccessDeniedComponent
          reason={deniedReason}
          onRetry={handleRetry}
          onGoBack={handleGoBack}
          onGoHome={handleGoHome}
        />
      );
    }

    // Render the wrapped component
    return <WrappedComponent {...props} />;
  };

  // Set display name for debugging
  WithPermissionsComponent.displayName = `withPermissions(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithPermissionsComponent;
}

// ===== CONVENIENCE FUNCTIONS =====

/**
 * HOC for protecting pages that require specific module access
 */
export function withModuleAccess(
  module: EvexaModule,
  action: PermissionAction = 'read',
  options: Omit<WithPermissionsOptions, 'modulePermissions'> = {}
) {
  return function<P extends object>(WrappedComponent: ComponentType<P>) {
    return withPermissions(WrappedComponent, {
      ...options,
      modulePermissions: [{ module, action }]
    });
  };
}

/**
 * HOC for protecting admin-only pages
 */
export function withAdminAccess<P extends object>(
  WrappedComponent: ComponentType<P>,
  options: Omit<WithPermissionsOptions, 'systemPermissions'> = {}
) {
  return withPermissions(WrappedComponent, {
    ...options,
    systemPermissions: ['canManageUsers', 'canManageSettings']
  });
}

/**
 * HOC for protecting pages that require analytics access
 */
export function withAnalyticsAccess<P extends object>(
  WrappedComponent: ComponentType<P>,
  options: Omit<WithPermissionsOptions, 'systemPermissions'> = {}
) {
  return withPermissions(WrappedComponent, {
    ...options,
    systemPermissions: ['canViewAnalytics']
  });
}

/**
 * HOC for protecting pages that require multiple module access
 */
export function withMultipleModuleAccess(
  permissions: PermissionRequirement[],
  options: Omit<WithPermissionsOptions, 'modulePermissions'> = {}
) {
  return function<P extends object>(WrappedComponent: ComponentType<P>) {
    return withPermissions(WrappedComponent, {
      ...options,
      modulePermissions: permissions
    });
  };
}

// ===== EXPORTS =====

export default withPermissions;
