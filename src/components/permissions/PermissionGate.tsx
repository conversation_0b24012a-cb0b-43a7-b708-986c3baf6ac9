/**
 * Permission Gate Component
 * Conditionally renders content based on user permissions
 */

import React, { ReactNode } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { useModuleAccess, useSystemPermissions } from '@/hooks/usePersonaPermissions';
import { EvexaModule, PermissionAction } from '@/types/personas';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Lock, AlertTriangle } from 'lucide-react';

// ===== PERMISSION GATE TYPES =====

interface BasePermissionGateProps {
  children: ReactNode;
  fallback?: ReactNode;
  showFallback?: boolean;
  requireAll?: boolean;
}

interface ModulePermissionGateProps extends BasePermissionGateProps {
  module: EvexaModule;
  action?: PermissionAction;
  actions?: PermissionAction[];
}

interface SystemPermissionGateProps extends BasePermissionGateProps {
  systemPermission: keyof ReturnType<typeof useSystemPermissions>['permissions'];
}

interface MultiplePermissionGateProps extends BasePermissionGateProps {
  permissions: Array<{
    module: EvexaModule;
    action: PermissionAction;
  }>;
}

// ===== DEFAULT FALLBACK COMPONENTS =====

const DefaultAccessDeniedFallback: React.FC<{ reason?: string }> = ({ reason }) => (
  <Alert variant="destructive" className="m-4">
    <Lock className="h-4 w-4" />
    <AlertDescription>
      {reason || 'You do not have permission to access this content.'}
    </AlertDescription>
  </Alert>
);

const DefaultLoadingFallback: React.FC = () => (
  <Alert className="m-4">
    <AlertTriangle className="h-4 w-4" />
    <AlertDescription>
      Checking permissions...
    </AlertDescription>
  </Alert>
);

// ===== MODULE PERMISSION GATE =====

export const ModulePermissionGate: React.FC<ModulePermissionGateProps> = ({
  module,
  action = 'read',
  actions,
  children,
  fallback,
  showFallback = true,
  requireAll = true
}) => {
  const { hasAccess, permissions, isLoading, moduleMetadata } = useModuleAccess(module, action);
  const { user } = useAuth();

  // SUPER ADMIN BYPASS: Check if user is super admin
  const { isSuperAdmin } = require('@/services/superAdminService');
  const userIsSuperAdmin = user && isSuperAdmin(user.id);

  // Handle loading state
  if (isLoading) {
    return showFallback ? (fallback || <DefaultLoadingFallback />) : null;
  }

  // SUPER ADMIN BYPASS: Always allow access for super admin
  if (userIsSuperAdmin) {
    return <>{children}</>;
  }

  // Check permissions
  let hasRequiredPermissions = false;

  if (actions) {
    // Check multiple actions
    if (requireAll) {
      hasRequiredPermissions = actions.every(a => permissions.includes(a));
    } else {
      hasRequiredPermissions = actions.some(a => permissions.includes(a));
    }
  } else {
    // Check single action
    hasRequiredPermissions = hasAccess;
  }

  // Render based on permissions
  if (hasRequiredPermissions) {
    return <>{children}</>;
  }

  if (!showFallback) {
    return null;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  const actionsList = actions ? actions.join(', ') : action;
  const reason = `You need ${actionsList} permission for ${moduleMetadata?.name || module} to access this content.`;

  return <DefaultAccessDeniedFallback reason={reason} />;
};

// ===== SYSTEM PERMISSION GATE =====

const SystemPermissionGate: React.FC<SystemPermissionGateProps> = ({
  systemPermission,
  children,
  fallback,
  showFallback = true
}) => {
  const { permissions, isLoading } = useSystemPermissions();
  const { user } = useAuth();

  // SUPER ADMIN BYPASS: Check if user is super admin
  const { isSuperAdmin } = require('@/services/superAdminService');
  const userIsSuperAdmin = user && isSuperAdmin(user.id);

  // Handle loading state
  if (isLoading) {
    return showFallback ? (fallback || <DefaultLoadingFallback />) : null;
  }

  // SUPER ADMIN BYPASS: Always allow access for super admin
  if (userIsSuperAdmin) {
    return <>{children}</>;
  }

  // Check permission
  const hasPermission = permissions[systemPermission];

  if (hasPermission) {
    return <>{children}</>;
  }

  if (!showFallback) {
    return null;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  const reason = `You need ${systemPermission.replace(/([A-Z])/g, ' $1').toLowerCase()} permission to access this content.`;
  return <DefaultAccessDeniedFallback reason={reason} />;
};

// ===== MULTIPLE PERMISSION GATE =====

const MultiplePermissionGate: React.FC<MultiplePermissionGateProps> = ({
  permissions: requiredPermissions,
  children,
  fallback,
  showFallback = true,
  requireAll = true
}) => {
  const { user } = useAuth();

  // SUPER ADMIN BYPASS: Check if user is super admin
  const { isSuperAdmin } = require('@/services/superAdminService');
  const userIsSuperAdmin = user && isSuperAdmin(user.id);

  // SUPER ADMIN BYPASS: Always allow access for super admin
  if (userIsSuperAdmin) {
    return <>{children}</>;
  }

  const permissionChecks = requiredPermissions.map(({ module, action }) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { hasAccess, isLoading } = useModuleAccess(module, action);
    return { module, action, hasAccess, isLoading };
  });

  // Handle loading state
  const isLoading = permissionChecks.some(check => check.isLoading);
  if (isLoading) {
    return showFallback ? (fallback || <DefaultLoadingFallback />) : null;
  }

  // Check permissions
  let hasRequiredPermissions = false;
  if (requireAll) {
    hasRequiredPermissions = permissionChecks.every(check => check.hasAccess);
  } else {
    hasRequiredPermissions = permissionChecks.some(check => check.hasAccess);
  }

  if (hasRequiredPermissions) {
    return <>{children}</>;
  }

  if (!showFallback) {
    return null;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  const deniedPermissions = permissionChecks
    .filter(check => !check.hasAccess)
    .map(check => `${check.module}:${check.action}`)
    .join(', ');

  const reason = `You need the following permissions to access this content: ${deniedPermissions}`;
  return <DefaultAccessDeniedFallback reason={reason} />;
};

// ===== COMBINED PERMISSION GATE =====

interface CombinedPermissionGateProps extends BasePermissionGateProps {
  modulePermissions?: Array<{
    module: EvexaModule;
    action: PermissionAction;
  }>;
  systemPermissions?: Array<keyof ReturnType<typeof useSystemPermissions>['permissions']>;
}

const CombinedPermissionGate: React.FC<CombinedPermissionGateProps> = ({
  modulePermissions = [],
  systemPermissions = [],
  children,
  fallback,
  showFallback = true,
  requireAll = true
}) => {
  const { permissions: sysPerms, isLoading: sysLoading } = useSystemPermissions();

  // Check module permissions
  const moduleChecks = modulePermissions.map(({ module, action }) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { hasAccess, isLoading } = useModuleAccess(module, action);
    return { type: 'module', module, action, hasAccess, isLoading };
  });

  // Check system permissions
  const systemChecks = systemPermissions.map(permission => ({
    type: 'system',
    permission,
    hasAccess: sysPerms[permission],
    isLoading: sysLoading
  }));

  const allChecks = [...moduleChecks, ...systemChecks];

  // Handle loading state
  const isLoading = allChecks.some(check => check.isLoading);
  if (isLoading) {
    return showFallback ? (fallback || <DefaultLoadingFallback />) : null;
  }

  // Check permissions
  let hasRequiredPermissions = false;
  if (requireAll) {
    hasRequiredPermissions = allChecks.every(check => check.hasAccess);
  } else {
    hasRequiredPermissions = allChecks.some(check => check.hasAccess);
  }

  if (hasRequiredPermissions) {
    return <>{children}</>;
  }

  if (!showFallback) {
    return null;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  const deniedPermissions = allChecks
    .filter(check => !check.hasAccess)
    .map(check => {
      if (check.type === 'module') {
        return `${check.module}:${check.action}`;
      } else {
        return check.permission;
      }
    })
    .join(', ');

  const reason = `You need the following permissions to access this content: ${deniedPermissions}`;
  return <DefaultAccessDeniedFallback reason={reason} />;
};

// ===== UTILITY COMPONENTS =====

interface PermissionDebugProps {
  userId?: string;
  module?: EvexaModule;
  action?: PermissionAction;
}

const PermissionDebug: React.FC<PermissionDebugProps> = ({
  module,
  action = 'read'
}) => {
  const { hasAccess, permissions, userPersona, moduleAccess } = useModuleAccess(module || 'exhibitions', action);
  const { permissions: systemPermissions } = useSystemPermissions();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-900 text-white p-4 rounded-lg text-xs max-w-sm">
      <h4 className="font-bold mb-2">Permission Debug</h4>
      {module && (
        <div className="mb-2">
          <strong>Module:</strong> {module}<br />
          <strong>Action:</strong> {action}<br />
          <strong>Has Access:</strong> {hasAccess ? '✅' : '❌'}<br />
          <strong>Permissions:</strong> {permissions.join(', ') || 'None'}
        </div>
      )}
      {userPersona && (
        <div className="mb-2">
          <strong>Persona:</strong> {userPersona.name}<br />
          <strong>Category:</strong> {userPersona.category}
        </div>
      )}
      <div>
        <strong>System Permissions:</strong>
        <ul className="text-xs">
          {Object.entries(systemPermissions).map(([key, value]) => (
            <li key={key}>
              {key}: {value ? '✅' : '❌'}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

// ===== EXPORTS =====

export default ModulePermissionGate;

export {
  ModulePermissionGate as PermissionGate,
  SystemPermissionGate,
  MultiplePermissionGate,
  CombinedPermissionGate,
  PermissionDebug
};
