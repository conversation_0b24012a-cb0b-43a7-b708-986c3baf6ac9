/**
 * Subscription Gate Component
 * Conditionally renders content based on subscription and persona permissions
 */

import React, { ReactNode } from 'react';
import { useModuleSubscriptionAccess, useFeatureAccess } from '@/hooks/useSubscriptionPermissions';
import { useAuth } from '@/contexts/auth-context';
import { EvexaModule, PermissionAction } from '@/types/personas';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Lock, Crown, ArrowUp, AlertTriangle, Zap } from 'lucide-react';

// ===== SUBSCRIPTION GATE TYPES =====

interface BaseSubscriptionGateProps {
  children: ReactNode;
  fallback?: ReactNode;
  showFallback?: boolean;
  showUpgradePrompt?: boolean;
}

interface ModuleSubscriptionGateProps extends BaseSubscriptionGateProps {
  module: EvexaModule;
  action?: PermissionAction;
}

interface FeatureSubscriptionGateProps extends BaseSubscriptionGateProps {
  feature: string;
  featureName?: string;
}

// ===== DEFAULT FALLBACK COMPONENTS =====

const DefaultUpgradePrompt: React.FC<{
  reason: string;
  upgradeRequired?: string;
  featureName?: string;
  onUpgrade?: () => void;
}> = ({ reason, upgradeRequired, featureName, onUpgrade }) => (
  <Card className="border-amber-200 bg-amber-50">
    <CardHeader className="pb-3">
      <div className="flex items-center gap-2">
        <Crown className="h-5 w-5 text-amber-600" />
        <CardTitle className="text-amber-800">
          {featureName ? `${featureName} - Premium Feature` : 'Premium Feature Required'}
        </CardTitle>
      </div>
      <CardDescription className="text-amber-700">
        {reason}
      </CardDescription>
    </CardHeader>
    <CardContent className="pt-0">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {upgradeRequired && (
            <Badge variant="secondary" className="bg-amber-100 text-amber-800">
              {upgradeRequired.charAt(0).toUpperCase() + upgradeRequired.slice(1)} Plan Required
            </Badge>
          )}
        </div>
        <Button 
          onClick={onUpgrade}
          className="bg-amber-600 hover:bg-amber-700 text-white"
          size="sm"
        >
          <ArrowUp className="h-4 w-4 mr-2" />
          Upgrade Now
        </Button>
      </div>
    </CardContent>
  </Card>
);

const DefaultAccessDeniedFallback: React.FC<{
  reason: string;
  type: 'persona' | 'subscription' | 'both';
}> = ({ reason, type }) => {
  const getIcon = () => {
    switch (type) {
      case 'subscription':
        return <Crown className="h-4 w-4" />;
      case 'persona':
        return <Lock className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getVariant = () => {
    return type === 'subscription' ? 'default' : 'destructive';
  };

  return (
    <Alert variant={getVariant() as any} className="m-4">
      {getIcon()}
      <AlertDescription>
        {reason}
      </AlertDescription>
    </Alert>
  );
};

const DefaultLoadingFallback: React.FC = () => (
  <Alert className="m-4">
    <Zap className="h-4 w-4" />
    <AlertDescription>
      Checking access permissions...
    </AlertDescription>
  </Alert>
);

// ===== MODULE SUBSCRIPTION GATE =====

export const ModuleSubscriptionGate: React.FC<ModuleSubscriptionGateProps> = ({
  module,
  action = 'read',
  children,
  fallback,
  showFallback = true,
  showUpgradePrompt = true
}) => {
  const {
    hasAccess,
    isLoading,
    subscriptionAllowed,
    personaAllowed,
    upgradeRequired,
    accessReason
  } = useModuleSubscriptionAccess(module, action);

  // Check if user is super admin (bypass all restrictions)
  const { user } = useAuth();
  const { isSuperAdmin: checkSuperAdmin } = require('@/services/superAdminService');
  const isSuperAdmin = user && checkSuperAdmin(user.id);

  // Handle loading state
  if (isLoading) {
    return showFallback ? (fallback || <DefaultLoadingFallback />) : null;
  }

  // SUPER ADMIN BYPASS: Always allow access for super admin
  if (isSuperAdmin) {
    return <>{children}</>;
  }

  // Render content if access is granted
  if (hasAccess) {
    return <>{children}</>;
  }

  // Don't show fallback if requested
  if (!showFallback) {
    return null;
  }

  // Use custom fallback if provided
  if (fallback) {
    return <>{fallback}</>;
  }

  // Determine access denial type and reason
  let denialType: 'persona' | 'subscription' | 'both';
  let denialReason: string;

  if (!personaAllowed && !subscriptionAllowed) {
    denialType = 'both';
    denialReason = `You need both persona permissions and subscription access for ${module}`;
  } else if (!personaAllowed) {
    denialType = 'persona';
    denialReason = `Your persona does not have ${action} permission for ${module}`;
  } else {
    denialType = 'subscription';
    denialReason = accessReason || `Your subscription plan does not include access to ${module}`;
  }

  // Show upgrade prompt for subscription issues
  if (denialType === 'subscription' && showUpgradePrompt) {
    return (
      <DefaultUpgradePrompt
        reason={denialReason}
        upgradeRequired={upgradeRequired}
        featureName={module}
        onUpgrade={() => {
          // Handle upgrade action - could open upgrade modal or redirect
          console.log('Upgrade requested for module:', module);
        }}
      />
    );
  }

  // Show access denied for other cases
  return <DefaultAccessDeniedFallback reason={denialReason} type={denialType} />;
};

// ===== FEATURE SUBSCRIPTION GATE =====

export const FeatureSubscriptionGate: React.FC<FeatureSubscriptionGateProps> = ({
  feature,
  featureName,
  children,
  fallback,
  showFallback = true,
  showUpgradePrompt = true
}) => {
  const { hasAccess, isLoading } = useFeatureAccess(feature);

  // Handle loading state
  if (isLoading) {
    return showFallback ? (fallback || <DefaultLoadingFallback />) : null;
  }

  // Render content if access is granted
  if (hasAccess) {
    return <>{children}</>;
  }

  // Don't show fallback if requested
  if (!showFallback) {
    return null;
  }

  // Use custom fallback if provided
  if (fallback) {
    return <>{fallback}</>;
  }

  // Show upgrade prompt for feature access
  if (showUpgradePrompt) {
    return (
      <DefaultUpgradePrompt
        reason={`${featureName || feature} is not available in your current plan`}
        featureName={featureName || feature}
        onUpgrade={() => {
          console.log('Upgrade requested for feature:', feature);
        }}
      />
    );
  }

  // Show basic access denied
  return (
    <DefaultAccessDeniedFallback
      reason={`${featureName || feature} is not available in your current plan`}
      type="subscription"
    />
  );
};

// ===== COMBINED SUBSCRIPTION GATE =====

interface CombinedSubscriptionGateProps extends BaseSubscriptionGateProps {
  modulePermissions?: Array<{
    module: EvexaModule;
    action: PermissionAction;
  }>;
  features?: string[];
  requireAll?: boolean;
}

export const CombinedSubscriptionGate: React.FC<CombinedSubscriptionGateProps> = ({
  modulePermissions = [],
  features = [],
  requireAll = true,
  children,
  fallback,
  showFallback = true,
  showUpgradePrompt = true
}) => {
  // Check module permissions
  const moduleChecks = modulePermissions.map(({ module, action }) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const result = useModuleSubscriptionAccess(module, action);
    return { type: 'module', module, action, ...result };
  });

  // Check feature permissions
  const featureChecks = features.map(feature => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const result = useFeatureAccess(feature);
    return { type: 'feature', feature, ...result };
  });

  const allChecks = [...moduleChecks, ...featureChecks];

  // Handle loading state
  const isLoading = allChecks.some(check => check.isLoading);
  if (isLoading) {
    return showFallback ? (fallback || <DefaultLoadingFallback />) : null;
  }

  // Check access
  let hasAccess = false;
  if (requireAll) {
    hasAccess = allChecks.every(check => check.hasAccess);
  } else {
    hasAccess = allChecks.some(check => check.hasAccess);
  }

  // Render content if access is granted
  if (hasAccess) {
    return <>{children}</>;
  }

  // Don't show fallback if requested
  if (!showFallback) {
    return null;
  }

  // Use custom fallback if provided
  if (fallback) {
    return <>{fallback}</>;
  }

  // Determine what's missing
  const deniedChecks = allChecks.filter(check => !check.hasAccess);
  const deniedItems = deniedChecks.map(check => {
    if (check.type === 'module') {
      return `${check.module}:${check.action}`;
    } else {
      return check.feature;
    }
  }).join(', ');

  const reason = `Access denied. Missing: ${deniedItems}`;

  // Show upgrade prompt if any subscription issues
  const hasSubscriptionIssues = deniedChecks.some(check => 
    check.type === 'module' && !check.subscriptionAllowed
  );

  if (hasSubscriptionIssues && showUpgradePrompt) {
    return (
      <DefaultUpgradePrompt
        reason={reason}
        onUpgrade={() => {
          console.log('Upgrade requested for combined access');
        }}
      />
    );
  }

  // Show basic access denied
  return <DefaultAccessDeniedFallback reason={reason} type="both" />;
};

// ===== EXPORTS =====

export default ModuleSubscriptionGate;

export {
  ModuleSubscriptionGate as SubscriptionGate,
  FeatureSubscriptionGate,
  CombinedSubscriptionGate
};
