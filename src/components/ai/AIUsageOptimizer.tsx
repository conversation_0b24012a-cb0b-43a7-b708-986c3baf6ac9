"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Brain, 
  Zap, 
  TrendingUp, 
  Database, 
  Settings, 
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Clock,
  DollarSign
} from 'lucide-react';
import { toast } from 'sonner';

interface OptimizationSettings {
  enableSmartCaching: boolean;
  enablePredictiveCaching: boolean;
  enableSemanticCaching: boolean;
  enableCostPrediction: boolean;
  autoOptimization: boolean;
  aggressiveCaching: boolean;
}

interface OptimizationMetrics {
  totalSavings: number;
  cacheHitRate: number;
  averageResponseTime: number;
  optimizationScore: number;
  recommendationsApplied: number;
}

export function AIUsageOptimizer() {
  const [settings, setSettings] = useState<OptimizationSettings>({
    enableSmartCaching: true,
    enablePredictiveCaching: true,
    enableSemanticCaching: false,
    enableCostPrediction: true,
    autoOptimization: false,
    aggressiveCaching: false
  });

  const [metrics, setMetrics] = useState<OptimizationMetrics>({
    totalSavings: 0,
    cacheHitRate: 0,
    averageResponseTime: 0,
    optimizationScore: 0,
    recommendationsApplied: 0
  });

  const [isOptimizing, setIsOptimizing] = useState(false);
  const [lastOptimization, setLastOptimization] = useState<Date | null>(null);

  useEffect(() => {
    loadMetrics();
  }, []);

  const loadMetrics = async () => {
    try {
      const response = await fetch('/api/ai/cost-prediction?action=cache-performance');
      if (response.ok) {
        const data = await response.json();
        setMetrics({
          totalSavings: data.costSavings || 0,
          cacheHitRate: data.hitRate || 0,
          averageResponseTime: data.averageResponseTime || 0,
          optimizationScore: calculateOptimizationScore(data),
          recommendationsApplied: 3 // Mock data
        });
      }
    } catch (error) {
      console.error('Failed to load optimization metrics:', error);
    }
  };

  const calculateOptimizationScore = (data: any): number => {
    let score = 0;
    
    // Cache hit rate contributes 40% to score
    score += (data.hitRate || 0) * 0.4;
    
    // Response time contributes 30% (lower is better)
    const responseTimeScore = Math.max(0, 100 - (data.averageResponseTime || 0) / 10);
    score += responseTimeScore * 0.3;
    
    // Cost savings contribute 30%
    const savingsScore = Math.min(100, (data.costSavings || 0) * 10);
    score += savingsScore * 0.3;
    
    return Math.round(score);
  };

  const runOptimization = async () => {
    setIsOptimizing(true);
    try {
      // Run cache optimization
      await fetch('/api/ai/cost-prediction', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'optimize-cache' })
      });

      // Run predictive caching if enabled
      if (settings.enablePredictiveCaching) {
        await fetch('/api/ai/cost-prediction', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ action: 'predictive-cache' })
        });
      }

      // Analyze patterns
      await fetch('/api/ai/cost-prediction', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'analyze-patterns' })
      });

      setLastOptimization(new Date());
      await loadMetrics();
      toast.success('AI usage optimization completed successfully');
    } catch (error) {
      console.error('Optimization failed:', error);
      toast.error('Failed to run optimization');
    } finally {
      setIsOptimizing(false);
    }
  };

  const updateSetting = (key: keyof OptimizationSettings, value: boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    
    // Show relevant messages for important settings
    if (key === 'enableSemanticCaching' && value) {
      toast.info('Semantic caching will improve cache hits for similar queries');
    }
    if (key === 'autoOptimization' && value) {
      toast.info('Auto-optimization will run every 6 hours');
    }
  };

  const getOptimizationScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getOptimizationScoreBadge = (score: number) => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Brain className="h-6 w-6 text-blue-600" />
            AI Usage Optimizer
          </h2>
          <p className="text-muted-foreground">
            Intelligent optimization to minimize AI costs while maintaining performance
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={runOptimization}
            disabled={isOptimizing}
            className="flex items-center gap-2"
          >
            <Zap className={`h-4 w-4 ${isOptimizing ? 'animate-pulse' : ''}`} />
            {isOptimizing ? 'Optimizing...' : 'Run Optimization'}
          </Button>
        </div>
      </div>

      {/* Optimization Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Optimization Score
            </span>
            <Badge variant={getOptimizationScoreBadge(metrics.optimizationScore)}>
              {metrics.optimizationScore}/100
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Progress value={metrics.optimizationScore} className="h-3" />
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="font-medium">${metrics.totalSavings.toFixed(2)}</div>
                <div className="text-muted-foreground">Total Savings</div>
              </div>
              <div className="text-center">
                <div className="font-medium">{metrics.cacheHitRate.toFixed(1)}%</div>
                <div className="text-muted-foreground">Cache Hit Rate</div>
              </div>
              <div className="text-center">
                <div className="font-medium">{metrics.averageResponseTime.toFixed(0)}ms</div>
                <div className="text-muted-foreground">Avg Response Time</div>
              </div>
              <div className="text-center">
                <div className="font-medium">{metrics.recommendationsApplied}</div>
                <div className="text-muted-foreground">Optimizations Applied</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Optimization Settings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Optimization Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="smart-caching">Smart Caching</Label>
                <p className="text-xs text-muted-foreground">
                  Intelligent cache strategies based on query patterns
                </p>
              </div>
              <Switch
                id="smart-caching"
                checked={settings.enableSmartCaching}
                onCheckedChange={(checked) => updateSetting('enableSmartCaching', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="predictive-caching">Predictive Caching</Label>
                <p className="text-xs text-muted-foreground">
                  Pre-cache common queries during low-usage periods
                </p>
              </div>
              <Switch
                id="predictive-caching"
                checked={settings.enablePredictiveCaching}
                onCheckedChange={(checked) => updateSetting('enablePredictiveCaching', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="semantic-caching">Semantic Caching</Label>
                <p className="text-xs text-muted-foreground">
                  Cache similar queries with semantic matching
                </p>
              </div>
              <Switch
                id="semantic-caching"
                checked={settings.enableSemanticCaching}
                onCheckedChange={(checked) => updateSetting('enableSemanticCaching', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="cost-prediction">Cost Prediction</Label>
                <p className="text-xs text-muted-foreground">
                  Predict and prevent cost overruns
                </p>
              </div>
              <Switch
                id="cost-prediction"
                checked={settings.enableCostPrediction}
                onCheckedChange={(checked) => updateSetting('enableCostPrediction', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="auto-optimization">Auto Optimization</Label>
                <p className="text-xs text-muted-foreground">
                  Automatically run optimizations every 6 hours
                </p>
              </div>
              <Switch
                id="auto-optimization"
                checked={settings.autoOptimization}
                onCheckedChange={(checked) => updateSetting('autoOptimization', checked)}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Cache Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Workflow Automation</span>
                <div className="flex items-center gap-2">
                  <Progress value={85} className="w-20 h-2" />
                  <span className="text-xs text-muted-foreground">85%</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Budget Prediction</span>
                <div className="flex items-center gap-2">
                  <Progress value={72} className="w-20 h-2" />
                  <span className="text-xs text-muted-foreground">72%</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Content Generation</span>
                <div className="flex items-center gap-2">
                  <Progress value={58} className="w-20 h-2" />
                  <span className="text-xs text-muted-foreground">58%</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Performance Insights</span>
                <div className="flex items-center gap-2">
                  <Progress value={64} className="w-20 h-2" />
                  <span className="text-xs text-muted-foreground">64%</span>
                </div>
              </div>
            </div>

            {lastOptimization && (
              <div className="pt-4 border-t">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  Last optimization: {lastOptimization.toLocaleString()}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Status Alert */}
      {metrics.optimizationScore < 60 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Your AI usage optimization score is below 60%. Consider enabling more optimization features 
            or running the optimizer to improve performance and reduce costs.
          </AlertDescription>
        </Alert>
      )}

      {metrics.optimizationScore >= 80 && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Excellent! Your AI usage is well optimized. You're saving approximately ${metrics.totalSavings.toFixed(2)} 
            through intelligent caching and optimization strategies.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
