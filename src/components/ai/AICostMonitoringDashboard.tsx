"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  DollarSign, 
  TrendingUp, 
  Zap, 
  Database, 
  Clock, 
  AlertTriangle,
  RefreshCw,
  Settings,
  Download,
  Trash2
} from 'lucide-react';
import { toast } from 'sonner';
// Remove direct AI service imports - use API routes instead
// import { enhancedAIService } from '@/services/enhancedAIService';
// import { aiCostOptimizationService } from '@/services/aiCostOptimizationService';

// Define types locally to avoid import issues
interface AICostAnalytics {
  totalCost: number;
  totalRequests: number;
  averageCost: number;
  costByService: Record<string, number>;
  requestsByService: Record<string, number>;
  costByProvider: Record<string, number>;
  dailyCosts: Array<{ date: string; cost: number; requests: number }>;
  cacheHitRate: number;
  totalTokens: number;
  averageResponseTime: number;
}

export function AICostMonitoringDashboard() {
  const [analytics, setAnalytics] = useState<AICostAnalytics | null>(null);
  const [cacheStats, setCacheStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [analyticsResponse, cacheResponse] = await Promise.all([
        fetch('/api/ai/cost-monitoring?action=analytics'),
        fetch('/api/ai/cost-monitoring?action=cache-stats')
      ]);

      if (analyticsResponse.ok && cacheResponse.ok) {
        const analyticsData = await analyticsResponse.json();
        const cacheData = await cacheResponse.json();

        setAnalytics(analyticsData);
        setCacheStats(cacheData);
      } else {
        throw new Error('Failed to fetch AI cost data');
      }
    } catch (error) {
      console.error('Failed to load AI cost data:', error);
      toast.error('Failed to load AI cost data');
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
    toast.success('Data refreshed');
  };

  const clearCache = async () => {
    try {
      const response = await fetch('/api/ai/cost-monitoring', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'clear-cache' }),
      });

      if (response.ok) {
        toast.success('AI cache cleared');
        loadData();
      } else {
        throw new Error('Failed to clear cache');
      }
    } catch (error) {
      console.error('Failed to clear cache:', error);
      toast.error('Failed to clear cache');
    }
  };

  const exportData = () => {
    if (!analytics) return;

    const csvData = [
      'Date,Cost,Requests',
      ...analytics.costTrends.map(trend => 
        `${trend.date},${trend.cost},${trend.requests}`
      )
    ].join('\n');

    const blob = new Blob([csvData], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-cost-analytics-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);

    toast.success('Data exported');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Failed to load AI cost analytics. Please try refreshing the page.
        </AlertDescription>
      </Alert>
    );
  }

  // Use default limits for percentage calculations
  const defaultDailyLimit = 10.00;
  const defaultMonthlyLimit = 200.00;
  const dailyUsagePercent = (analytics.dailyCost / defaultDailyLimit) * 100;
  const monthlyUsagePercent = (analytics.monthlyCost / defaultMonthlyLimit) * 100;

  const serviceColors = ['#2563eb', '#dc2626', '#16a34a', '#ca8a04', '#9333ea', '#c2410c'];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">AI Cost Monitoring</h2>
          <p className="text-muted-foreground">
            Monitor AI usage, costs, and optimize performance
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={refreshData}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" size="sm" onClick={exportData}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm" onClick={clearCache}>
            <Trash2 className="h-4 w-4 mr-2" />
            Clear Cache
          </Button>
        </div>
      </div>

      {/* Cost Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Daily Cost</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${analytics.dailyCost.toFixed(2)}</div>
            <Progress value={dailyUsagePercent} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {dailyUsagePercent.toFixed(1)}% of daily limit
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Cost</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${analytics.monthlyCost.toFixed(2)}</div>
            <Progress value={monthlyUsagePercent} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {monthlyUsagePercent.toFixed(1)}% of monthly limit
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache Hit Rate</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.cacheHitRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {cacheStats?.totalHits || 0} cache hits
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.averageResponseTime.toFixed(0)}ms</div>
            <p className="text-xs text-muted-foreground">
              {analytics.totalRequests} total requests
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Alerts */}
      {(dailyUsagePercent > 80 || monthlyUsagePercent > 80) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {dailyUsagePercent > 80 && `Daily cost limit approaching: ${dailyUsagePercent.toFixed(1)}%`}
            {dailyUsagePercent > 80 && monthlyUsagePercent > 80 && ' | '}
            {monthlyUsagePercent > 80 && `Monthly cost limit approaching: ${monthlyUsagePercent.toFixed(1)}%`}
          </AlertDescription>
        </Alert>
      )}

      {/* Detailed Analytics */}
      <Tabs defaultValue="trends" className="space-y-4">
        <TabsList>
          <TabsTrigger value="trends">Cost Trends</TabsTrigger>
          <TabsTrigger value="services">By Service</TabsTrigger>
          <TabsTrigger value="cache">Cache Performance</TabsTrigger>
          <TabsTrigger value="expensive">Top Queries</TabsTrigger>
          <TabsTrigger value="predictions">Predictions</TabsTrigger>
        </TabsList>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cost Trends (Last 30 Days)</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analytics.costTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Line 
                    type="monotone" 
                    dataKey="cost" 
                    stroke="#2563eb" 
                    strokeWidth={2}
                    name="Cost ($)"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="services" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Cost by Service</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={Object.entries(analytics.costByService).map(([service, cost]) => ({
                        name: service,
                        value: cost
                      }))}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                      label={({ name, value }) => `${name}: $${value.toFixed(2)}`}
                    >
                      {Object.entries(analytics.costByService).map((_, index) => (
                        <Cell key={`cell-${index}`} fill={serviceColors[index % serviceColors.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Service Usage</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(analytics.costByService).map(([service, cost], index) => (
                    <div key={service} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: serviceColors[index % serviceColors.length] }}
                        />
                        <span className="text-sm font-medium">{service}</span>
                      </div>
                      <Badge variant="outline">${cost.toFixed(2)}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="cache" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Cache Size</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {((cacheStats?.size || 0) / 1024 / 1024).toFixed(2)} MB
                </div>
                <p className="text-xs text-muted-foreground">
                  {cacheStats?.entries || 0} entries
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Hit Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{(cacheStats?.hitRate || 0).toFixed(1)}%</div>
                <Progress value={cacheStats?.hitRate || 0} className="mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Total Hits</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{cacheStats?.totalHits || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Cache responses served
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="expensive" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Most Expensive Queries</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analytics.topExpensiveQueries.slice(0, 10).map((query, index) => (
                  <div key={query.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="text-xs">
                          {query.service}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {query.timestamp.toLocaleString()}
                        </span>
                      </div>
                      <p className="text-sm truncate">{query.prompt}</p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                        <span>{query.totalTokens} tokens</span>
                        <span>{query.responseTime}ms</span>
                        <span>{query.provider}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">${query.cost.toFixed(4)}</div>
                      <div className="text-xs text-muted-foreground">#{index + 1}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="predictions" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Cost Predictions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Predicted Daily Cost</span>
                    <Badge variant="outline">$12.50</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Predicted Monthly Cost</span>
                    <Badge variant="outline">$375.00</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Confidence Level</span>
                    <Badge variant="secondary">85%</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Risk Level</span>
                    <Badge variant="destructive">Medium</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Optimization Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 border rounded-lg">
                    <p className="text-sm font-medium">Improve Cache Hit Rate</p>
                    <p className="text-xs text-muted-foreground">
                      Potential savings: $3.50/day
                    </p>
                    <Badge variant="outline" className="mt-1">Low Effort</Badge>
                  </div>
                  <div className="p-3 border rounded-lg">
                    <p className="text-sm font-medium">Optimize Model Selection</p>
                    <p className="text-xs text-muted-foreground">
                      Potential savings: $2.25/day
                    </p>
                    <Badge variant="secondary" className="mt-1">Medium Effort</Badge>
                  </div>
                  <div className="p-3 border rounded-lg">
                    <p className="text-sm font-medium">Implement Rate Limiting</p>
                    <p className="text-xs text-muted-foreground">
                      Potential savings: $1.80/day
                    </p>
                    <Badge variant="outline" className="mt-1">Low Effort</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Smart Caching Strategies
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Workflow Automation</span>
                    <Badge variant="outline">24h TTL</Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    High-priority caching for workflow analysis
                  </p>
                  <div className="mt-2">
                    <Progress value={85} className="h-2" />
                    <p className="text-xs text-muted-foreground mt-1">85% hit rate</p>
                  </div>
                </div>

                <div className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Budget Prediction</span>
                    <Badge variant="outline">12h TTL</Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Medium-priority caching for budget analysis
                  </p>
                  <div className="mt-2">
                    <Progress value={72} className="h-2" />
                    <p className="text-xs text-muted-foreground mt-1">72% hit rate</p>
                  </div>
                </div>

                <div className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Content Generation</span>
                    <Badge variant="outline">6h TTL</Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Standard caching for content creation
                  </p>
                  <div className="mt-2">
                    <Progress value={58} className="h-2" />
                    <p className="text-xs text-muted-foreground mt-1">58% hit rate</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
