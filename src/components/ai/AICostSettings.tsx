"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  DollarSign, 
  Settings, 
  Database, 
  Clock, 
  Zap,
  AlertTriangle,
  Save,
  RotateCcw
} from 'lucide-react';
import { toast } from 'sonner';
// Remove direct AI service imports - use API routes instead

// Define types locally to avoid import issues
interface AICostConfig {
  maxDailyCost: number;
  maxMonthlyCost: number;
  enableCaching: boolean;
  cacheTTL: number;
  costAlerts: boolean;
  alertThreshold: number;
  preferredProviders: string[];
  fallbackToMock: boolean;
}

export function AICostSettings() {
  const [config, setConfig] = useState<AICostConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    loadConfiguration();
  }, []);

  const loadConfiguration = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/ai/cost-settings');

      if (response.ok) {
        const currentConfig = await response.json();
        setConfig(currentConfig);
      } else {
        throw new Error('Failed to fetch configuration');
      }
    } catch (error) {
      console.error('Failed to load AI cost configuration:', error);
      toast.error('Failed to load configuration');
    } finally {
      setLoading(false);
    }
  };

  const updateConfig = (updates: Partial<AICostConfig>) => {
    if (!config) return;
    
    setConfig({ ...config, ...updates });
    setHasChanges(true);
  };

  const updateNestedConfig = (path: string, value: any) => {
    if (!config) return;

    const keys = path.split('.');
    const newConfig = { ...config };
    let current: any = newConfig;

    for (let i = 0; i < keys.length - 1; i++) {
      current[keys[i]] = { ...current[keys[i]] };
      current = current[keys[i]];
    }

    current[keys[keys.length - 1]] = value;
    setConfig(newConfig);
    setHasChanges(true);
  };

  const saveConfiguration = async () => {
    if (!config) return;

    try {
      setSaving(true);
      const response = await fetch('/api/ai/cost-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'update-config', config }),
      });

      if (response.ok) {
        setHasChanges(false);
        toast.success('Configuration saved successfully');
      } else {
        throw new Error('Failed to save configuration');
      }
    } catch (error) {
      console.error('Failed to save configuration:', error);
      toast.error('Failed to save configuration');
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = async () => {
    try {
      setSaving(true);
      const response = await fetch('/api/ai/cost-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'reset-config' }),
      });

      if (response.ok) {
        const result = await response.json();
        setConfig(result.config);
        setHasChanges(false);
        toast.success('Configuration reset to defaults');
      } else {
        throw new Error('Failed to reset configuration');
      }
    } catch (error) {
      console.error('Failed to reset configuration:', error);
      toast.error('Failed to reset configuration');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!config) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Failed to load AI cost configuration. Please try refreshing the page.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">AI Cost Settings</h2>
          <p className="text-muted-foreground">
            Configure AI cost limits, caching, and optimization settings
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={resetToDefaults}
            disabled={saving}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button
            onClick={saveConfiguration}
            disabled={!hasChanges || saving}
          >
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {/* Configuration Tabs */}
      <Tabs defaultValue="limits" className="space-y-4">
        <TabsList>
          <TabsTrigger value="limits">Cost Limits</TabsTrigger>
          <TabsTrigger value="caching">Caching</TabsTrigger>
          <TabsTrigger value="rate-limiting">Rate Limiting</TabsTrigger>
          <TabsTrigger value="features">Feature Tiers</TabsTrigger>
        </TabsList>

        <TabsContent value="limits" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Cost Limits
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="maxDailyCost">Maximum Daily Cost ($)</Label>
                  <Input
                    id="maxDailyCost"
                    type="number"
                    step="0.01"
                    value={config.maxDailyCost}
                    onChange={(e) => updateConfig({ maxDailyCost: parseFloat(e.target.value) || 0 })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxMonthlyCost">Maximum Monthly Cost ($)</Label>
                  <Input
                    id="maxMonthlyCost"
                    type="number"
                    step="0.01"
                    value={config.maxMonthlyCost}
                    onChange={(e) => updateConfig({ maxMonthlyCost: parseFloat(e.target.value) || 0 })}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="enabled"
                    checked={config.enabled}
                    onCheckedChange={(checked) => updateConfig({ enabled: checked })}
                  />
                  <Label htmlFor="enabled">Enable cost optimization</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Alert Thresholds
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="dailyAlert">Daily Alert Threshold ($)</Label>
                  <Input
                    id="dailyAlert"
                    type="number"
                    step="0.01"
                    value={config.alertThresholds.daily}
                    onChange={(e) => updateNestedConfig('alertThresholds.daily', parseFloat(e.target.value) || 0)}
                  />
                  <p className="text-xs text-muted-foreground">
                    Alert when daily cost reaches this amount
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="monthlyAlert">Monthly Alert Threshold ($)</Label>
                  <Input
                    id="monthlyAlert"
                    type="number"
                    step="0.01"
                    value={config.alertThresholds.monthly}
                    onChange={(e) => updateNestedConfig('alertThresholds.monthly', parseFloat(e.target.value) || 0)}
                  />
                  <p className="text-xs text-muted-foreground">
                    Alert when monthly cost reaches this amount
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="caching" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Caching Strategy
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center space-x-2">
                <Switch
                  id="cachingEnabled"
                  checked={config.cachingStrategy.enabled}
                  onCheckedChange={(checked) => updateNestedConfig('cachingStrategy.enabled', checked)}
                />
                <Label htmlFor="cachingEnabled">Enable response caching</Label>
              </div>

              <div className="space-y-2">
                <Label>Default Cache TTL: {config.cachingStrategy.defaultTTL} minutes</Label>
                <Slider
                  value={[config.cachingStrategy.defaultTTL]}
                  onValueChange={([value]) => updateNestedConfig('cachingStrategy.defaultTTL', value)}
                  max={480}
                  min={5}
                  step={5}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>5 min</span>
                  <span>8 hours</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Max Cache Size: {config.cachingStrategy.maxCacheSize} MB</Label>
                <Slider
                  value={[config.cachingStrategy.maxCacheSize]}
                  onValueChange={([value]) => updateNestedConfig('cachingStrategy.maxCacheSize', value)}
                  max={1000}
                  min={10}
                  step={10}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>10 MB</span>
                  <span>1 GB</span>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="compressionEnabled"
                  checked={config.cachingStrategy.compressionEnabled}
                  onCheckedChange={(checked) => updateNestedConfig('cachingStrategy.compressionEnabled', checked)}
                />
                <Label htmlFor="compressionEnabled">Enable response compression</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rate-limiting" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Rate Limiting
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="requestsPerMinute">Requests per Minute</Label>
                <Input
                  id="requestsPerMinute"
                  type="number"
                  value={config.rateLimiting.requestsPerMinute}
                  onChange={(e) => updateNestedConfig('rateLimiting.requestsPerMinute', parseInt(e.target.value) || 0)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="requestsPerHour">Requests per Hour</Label>
                <Input
                  id="requestsPerHour"
                  type="number"
                  value={config.rateLimiting.requestsPerHour}
                  onChange={(e) => updateNestedConfig('rateLimiting.requestsPerHour', parseInt(e.target.value) || 0)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="requestsPerDay">Requests per Day</Label>
                <Input
                  id="requestsPerDay"
                  type="number"
                  value={config.rateLimiting.requestsPerDay}
                  onChange={(e) => updateNestedConfig('rateLimiting.requestsPerDay', parseInt(e.target.value) || 0)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="features" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(config.tieredFeatures).map(([tier, features]) => (
              <Card key={tier}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="capitalize">{tier} Tier</span>
                    <Badge variant="outline">{features.length} features</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {features.map((feature) => (
                      <div key={feature} className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Save Changes Alert */}
      {hasChanges && (
        <Alert>
          <Settings className="h-4 w-4" />
          <AlertDescription>
            You have unsaved changes. Click "Save Changes" to apply your configuration.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
