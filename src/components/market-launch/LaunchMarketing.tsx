"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Mail, 
  Plus, 
  Play, 
  Pause, 
  Edit, 
  Trash2, 
  TrendingUp, 
  Users,
  Calendar,
  Target,
  BarChart3,
  Zap,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Globe,
  Share2,
  MessageSquare
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { marketingCampaignService, type MarketingCampaign } from '@/services/marketingCampaignService';

interface LaunchMarketingProps {
  onCampaignUpdate?: () => void;
}

export default function LaunchMarketing({ onCampaignUpdate }: LaunchMarketingProps) {
  const [campaigns, setCampaigns] = useState<MarketingCampaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('campaigns');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const { toast } = useToast();

  const [newCampaign, setNewCampaign] = useState({
    name: '',
    type: 'email' as MarketingCampaign['type'],
    startDate: new Date(),
    budget: 0,
    targetAudience: '',
    channels: [] as string[],
    description: ''
  });

  useEffect(() => {
    loadCampaigns();
  }, []);

  const loadCampaigns = async () => {
    try {
      setLoading(true);
      // Load real marketing campaigns from Firebase
      const campaignsData = await marketingCampaignService.getAllCampaigns();
      setCampaigns(campaignsData);
    } catch (error) {
      console.error('Failed to load campaigns:', error);
      toast({
        title: "Error",
        description: "Failed to load marketing campaigns",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCampaign = async () => {
    try {
      const campaign = await marketingCampaignService.createCampaign({
        ...newCampaign,
        status: 'draft',
        spent: 0,
        metrics: {
          impressions: 0,
          clicks: 0,
          conversions: 0,
          leads: 0,
          cost_per_lead: 0
        }
      });

      setCampaigns(prev => [campaign, ...prev]);
      setShowCreateDialog(false);
      setNewCampaign({
        name: '',
        type: 'email',
        startDate: new Date(),
        budget: 0,
        targetAudience: '',
        channels: [],
        description: ''
      });

      toast({
        title: "Success",
        description: "Marketing campaign created successfully"
      });

      onCampaignUpdate?.();
    } catch (error) {
      console.error('Failed to create campaign:', error);
      toast({
        title: "Error",
        description: "Failed to create campaign",
        variant: "destructive"
      });
    }
  };

  const handleStatusUpdate = async (campaignId: string, newStatus: MarketingCampaign['status']) => {
    try {
      await marketingCampaignService.updateCampaign(campaignId, { status: newStatus });

      setCampaigns(prev => prev.map(campaign =>
        campaign.id === campaignId ? { ...campaign, status: newStatus } : campaign
      ));

      toast({
        title: "Success",
        description: "Campaign status updated successfully"
      });
    } catch (error) {
      console.error('Failed to update campaign status:', error);
      toast({
        title: "Error",
        description: "Failed to update campaign status",
        variant: "destructive"
      });
    }
  };

  const getStatusColor = (status: MarketingCampaign['status']) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'active': return 'bg-green-100 text-green-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: MarketingCampaign['type']) => {
    switch (type) {
      case 'email': return <Mail className="h-4 w-4" />;
      case 'social': return <Share2 className="h-4 w-4" />;
      case 'content': return <MessageSquare className="h-4 w-4" />;
      case 'webinar': return <Users className="h-4 w-4" />;
      case 'partnership': return <Target className="h-4 w-4" />;
      case 'pr': return <Globe className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const calculateROI = (campaign: MarketingCampaign) => {
    if (campaign.spent === 0) return 0;
    const revenue = campaign.metrics.conversions * 5000; // Assume $5k average deal
    return ((revenue - campaign.spent) / campaign.spent) * 100;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Launch Marketing Automation</h2>
          <p className="text-muted-foreground">Manage marketing campaigns and automation for market launch</p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Campaign
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create Marketing Campaign</DialogTitle>
              <DialogDescription>Launch a new marketing campaign for EVEXA</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Campaign Name</Label>
                  <Input
                    id="name"
                    value={newCampaign.name}
                    onChange={(e) => setNewCampaign(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter campaign name"
                  />
                </div>
                <div>
                  <Label htmlFor="type">Campaign Type</Label>
                  <Select value={newCampaign.type} onValueChange={(value) => setNewCampaign(prev => ({ ...prev, type: value as MarketingCampaign['type'] }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="email">Email Marketing</SelectItem>
                      <SelectItem value="social">Social Media</SelectItem>
                      <SelectItem value="content">Content Marketing</SelectItem>
                      <SelectItem value="webinar">Webinar</SelectItem>
                      <SelectItem value="partnership">Partnership</SelectItem>
                      <SelectItem value="pr">Public Relations</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="budget">Budget</Label>
                  <Input
                    id="budget"
                    type="number"
                    value={newCampaign.budget}
                    onChange={(e) => setNewCampaign(prev => ({ ...prev, budget: Number(e.target.value) }))}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={newCampaign.startDate.toISOString().split('T')[0]}
                    onChange={(e) => setNewCampaign(prev => ({ ...prev, startDate: new Date(e.target.value) }))}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="targetAudience">Target Audience</Label>
                <Input
                  id="targetAudience"
                  value={newCampaign.targetAudience}
                  onChange={(e) => setNewCampaign(prev => ({ ...prev, targetAudience: e.target.value }))}
                  placeholder="Describe your target audience"
                />
              </div>

              <div>
                <Label htmlFor="description">Campaign Description</Label>
                <Textarea
                  id="description"
                  value={newCampaign.description}
                  onChange={(e) => setNewCampaign(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe the campaign objectives and strategy"
                  rows={3}
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateCampaign}>
                  Create Campaign
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Campaign Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{campaigns.filter(c => c.status === 'active').length}</div>
            <p className="text-xs text-muted-foreground">
              {campaigns.length} total campaigns
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(campaigns.reduce((sum, c) => sum + c.budget, 0))}
            </div>
            <p className="text-xs text-muted-foreground">
              {formatCurrency(campaigns.reduce((sum, c) => sum + c.spent, 0))} spent
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Leads</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {campaigns.reduce((sum, c) => sum + c.metrics.leads, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {campaigns.reduce((sum, c) => sum + c.metrics.conversions, 0)} conversions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Cost per Lead</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(
                campaigns.reduce((sum, c) => sum + c.metrics.cost_per_lead, 0) / 
                campaigns.filter(c => c.metrics.cost_per_lead > 0).length || 0
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Average across campaigns
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="automation">Automation</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="campaigns" className="space-y-4">
          <div className="grid gap-4">
            {campaigns.map((campaign) => (
              <Card key={campaign.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getTypeIcon(campaign.type)}
                      <div>
                        <CardTitle className="text-lg">{campaign.name}</CardTitle>
                        <CardDescription>{campaign.targetAudience}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(campaign.status)}>
                        {campaign.status}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Campaign Details</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Type:</span>
                          <span className="font-medium capitalize">{campaign.type}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Start Date:</span>
                          <span className="font-medium">{campaign.startDate.toLocaleDateString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Budget:</span>
                          <span className="font-medium">{formatCurrency(campaign.budget)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Spent:</span>
                          <span className="font-medium">{formatCurrency(campaign.spent)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-2">Performance</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Impressions:</span>
                          <span className="font-medium">{campaign.metrics.impressions.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Clicks:</span>
                          <span className="font-medium">{campaign.metrics.clicks.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Leads:</span>
                          <span className="font-medium">{campaign.metrics.leads}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Cost/Lead:</span>
                          <span className="font-medium">{formatCurrency(campaign.metrics.cost_per_lead)}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Actions</h4>
                      <div className="space-y-2">
                        <div className="flex gap-2">
                          {campaign.status === 'draft' && (
                            <Button 
                              size="sm" 
                              onClick={() => handleStatusUpdate(campaign.id, 'active')}
                              className="flex-1"
                            >
                              <Play className="h-3 w-3 mr-1" />
                              Launch
                            </Button>
                          )}
                          {campaign.status === 'active' && (
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => handleStatusUpdate(campaign.id, 'paused')}
                              className="flex-1"
                            >
                              <Pause className="h-3 w-3 mr-1" />
                              Pause
                            </Button>
                          )}
                          {campaign.status === 'paused' && (
                            <Button 
                              size="sm" 
                              onClick={() => handleStatusUpdate(campaign.id, 'active')}
                              className="flex-1"
                            >
                              <Play className="h-3 w-3 mr-1" />
                              Resume
                            </Button>
                          )}
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm" className="flex-1">
                            <Edit className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                          <Button variant="outline" size="sm" className="flex-1">
                            <BarChart3 className="h-3 w-3 mr-1" />
                            Analytics
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Budget Progress */}
                  <div className="mt-4">
                    <div className="flex justify-between text-sm mb-1">
                      <span>Budget Utilization</span>
                      <span>{((campaign.spent / campaign.budget) * 100).toFixed(1)}%</span>
                    </div>
                    <Progress value={(campaign.spent / campaign.budget) * 100} className="h-2" />
                  </div>

                  {/* ROI Display */}
                  <div className="mt-2 p-2 bg-muted rounded-md">
                    <div className="flex justify-between text-sm">
                      <span>Estimated ROI:</span>
                      <span className={`font-medium ${calculateROI(campaign) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {calculateROI(campaign).toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="automation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Marketing Automation</CardTitle>
              <CardDescription>Automated marketing workflows and sequences</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Zap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Marketing Automation Hub</h3>
                <p className="text-muted-foreground mb-4">
                  Set up automated marketing workflows and sequences
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Automation
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Campaign Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {campaigns.map((campaign) => (
                    <div key={campaign.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {getTypeIcon(campaign.type)}
                        <div>
                          <h4 className="font-medium">{campaign.name}</h4>
                          <p className="text-sm text-muted-foreground">{campaign.metrics.leads} leads</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{formatCurrency(campaign.metrics.cost_per_lead)}</div>
                        <div className="text-sm text-muted-foreground">cost/lead</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Channel Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {['email', 'social', 'content', 'webinar', 'pr'].map((channel) => {
                    const channelCampaigns = campaigns.filter(c => c.type === channel);
                    const totalLeads = channelCampaigns.reduce((sum, c) => sum + c.metrics.leads, 0);
                    const totalSpent = channelCampaigns.reduce((sum, c) => sum + c.spent, 0);
                    const avgCostPerLead = totalLeads > 0 ? totalSpent / totalLeads : 0;
                    
                    return (
                      <div key={channel} className="flex items-center justify-between">
                        <span className="text-sm capitalize">{channel}</span>
                        <div className="flex items-center gap-4">
                          <span className="text-sm text-muted-foreground">{totalLeads} leads</span>
                          <span className="text-sm font-medium">{formatCurrency(avgCostPerLead)}</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
