"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Handshake, 
  Plus, 
  Edit, 
  Trash2, 
  ExternalLink, 
  TrendingUp, 
  Users, 
  DollarSign,
  Calendar,
  Mail,
  Phone,
  Globe,
  Star,
  Activity,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { partnershipService, type Partnership, type PartnershipOpportunity } from '@/services/partnershipService';

interface PartnershipManagementProps {
  onPartnershipUpdate?: () => void;
}

export default function PartnershipManagement({ onPartnershipUpdate }: PartnershipManagementProps) {
  const [partnerships, setPartnerships] = useState<Partnership[]>([]);
  const [opportunities, setOpportunities] = useState<PartnershipOpportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('partnerships');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingPartnership, setEditingPartnership] = useState<Partnership | null>(null);
  const { toast } = useToast();

  const [newPartnership, setNewPartnership] = useState({
    name: '',
    type: 'exhibition_organizer' as Partnership['type'],
    status: 'pending' as Partnership['status'],
    tier: 'bronze' as Partnership['tier'],
    contactInfo: {
      primaryContact: '',
      email: '',
      phone: '',
      company: '',
      website: ''
    },
    agreement: {
      startDate: new Date(),
      contractValue: 0,
      currency: 'USD',
      terms: ''
    },
    services: {
      provided: [] as string[],
      received: [] as string[],
      integrations: [] as string[]
    }
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [partnershipsData, opportunitiesData] = await Promise.all([
        partnershipService.getAllPartnerships(),
        partnershipService.getOpportunities()
      ]);
      setPartnerships(partnershipsData);
      setOpportunities(opportunitiesData);
    } catch (error) {
      console.error('Failed to load partnership data:', error);
      toast({
        title: "Error",
        description: "Failed to load partnership data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePartnership = async () => {
    try {
      const partnership = await partnershipService.createPartnership({
        ...newPartnership,
        performance: {
          leadsGenerated: 0,
          revenueGenerated: 0,
          clientsReferred: 0,
          satisfactionScore: 0
        }
      });
      
      setPartnerships(prev => [partnership, ...prev]);
      setShowCreateDialog(false);
      setNewPartnership({
        name: '',
        type: 'exhibition_organizer',
        status: 'pending',
        tier: 'bronze',
        contactInfo: {
          primaryContact: '',
          email: '',
          phone: '',
          company: '',
          website: ''
        },
        agreement: {
          startDate: new Date(),
          contractValue: 0,
          currency: 'USD',
          terms: ''
        },
        services: {
          provided: [],
          received: [],
          integrations: []
        }
      });
      
      toast({
        title: "Success",
        description: "Partnership created successfully"
      });
      
      onPartnershipUpdate?.();
    } catch (error) {
      console.error('Failed to create partnership:', error);
      toast({
        title: "Error",
        description: "Failed to create partnership",
        variant: "destructive"
      });
    }
  };

  const getStatusColor = (status: Partnership['status']) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'terminated': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTierColor = (tier: Partnership['tier']) => {
    switch (tier) {
      case 'platinum': return 'bg-purple-100 text-purple-800';
      case 'gold': return 'bg-yellow-100 text-yellow-800';
      case 'silver': return 'bg-gray-100 text-gray-800';
      case 'bronze': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: Partnership['type']) => {
    switch (type) {
      case 'exhibition_organizer': return <Calendar className="h-4 w-4" />;
      case 'technology_partner': return <Activity className="h-4 w-4" />;
      case 'vendor': return <Users className="h-4 w-4" />;
      case 'strategic_alliance': return <Handshake className="h-4 w-4" />;
      case 'reseller': return <TrendingUp className="h-4 w-4" />;
      default: return <Handshake className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Partnership Management</h2>
          <p className="text-muted-foreground">Manage strategic partnerships and integrations</p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Partnership
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Partnership</DialogTitle>
              <DialogDescription>Add a new strategic partnership to your portfolio</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Partnership Name</Label>
                  <Input
                    id="name"
                    value={newPartnership.name}
                    onChange={(e) => setNewPartnership(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter partnership name"
                  />
                </div>
                <div>
                  <Label htmlFor="type">Partnership Type</Label>
                  <Select value={newPartnership.type} onValueChange={(value) => setNewPartnership(prev => ({ ...prev, type: value as Partnership['type'] }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="exhibition_organizer">Exhibition Organizer</SelectItem>
                      <SelectItem value="technology_partner">Technology Partner</SelectItem>
                      <SelectItem value="vendor">Vendor</SelectItem>
                      <SelectItem value="strategic_alliance">Strategic Alliance</SelectItem>
                      <SelectItem value="reseller">Reseller</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="status">Status</Label>
                  <Select value={newPartnership.status} onValueChange={(value) => setNewPartnership(prev => ({ ...prev, status: value as Partnership['status'] }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="terminated">Terminated</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="tier">Partnership Tier</Label>
                  <Select value={newPartnership.tier} onValueChange={(value) => setNewPartnership(prev => ({ ...prev, tier: value as Partnership['tier'] }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="platinum">Platinum</SelectItem>
                      <SelectItem value="gold">Gold</SelectItem>
                      <SelectItem value="silver">Silver</SelectItem>
                      <SelectItem value="bronze">Bronze</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="primaryContact">Primary Contact</Label>
                  <Input
                    id="primaryContact"
                    value={newPartnership.contactInfo.primaryContact}
                    onChange={(e) => setNewPartnership(prev => ({ 
                      ...prev, 
                      contactInfo: { ...prev.contactInfo, primaryContact: e.target.value }
                    }))}
                    placeholder="Contact person name"
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={newPartnership.contactInfo.email}
                    onChange={(e) => setNewPartnership(prev => ({ 
                      ...prev, 
                      contactInfo: { ...prev.contactInfo, email: e.target.value }
                    }))}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="company">Company</Label>
                  <Input
                    id="company"
                    value={newPartnership.contactInfo.company}
                    onChange={(e) => setNewPartnership(prev => ({ 
                      ...prev, 
                      contactInfo: { ...prev.contactInfo, company: e.target.value }
                    }))}
                    placeholder="Partner company name"
                  />
                </div>
                <div>
                  <Label htmlFor="contractValue">Contract Value</Label>
                  <Input
                    id="contractValue"
                    type="number"
                    value={newPartnership.agreement.contractValue}
                    onChange={(e) => setNewPartnership(prev => ({ 
                      ...prev, 
                      agreement: { ...prev.agreement, contractValue: Number(e.target.value) }
                    }))}
                    placeholder="0"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="terms">Agreement Terms</Label>
                <Textarea
                  id="terms"
                  value={newPartnership.agreement.terms}
                  onChange={(e) => setNewPartnership(prev => ({ 
                    ...prev, 
                    agreement: { ...prev.agreement, terms: e.target.value }
                  }))}
                  placeholder="Partnership terms and conditions"
                  rows={3}
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreatePartnership}>
                  Create Partnership
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="partnerships">Active Partnerships</TabsTrigger>
          <TabsTrigger value="opportunities">Opportunities</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="partnerships" className="space-y-4">
          <div className="grid gap-4">
            {partnerships.map((partnership) => (
              <Card key={partnership.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getTypeIcon(partnership.type)}
                      <div>
                        <CardTitle className="text-lg">{partnership.name}</CardTitle>
                        <CardDescription>{partnership.contactInfo.company}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(partnership.status)}>
                        {partnership.status}
                      </Badge>
                      <Badge className={getTierColor(partnership.tier)}>
                        {partnership.tier}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Contact Information</h4>
                      <div className="space-y-1 text-sm text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <Users className="h-3 w-3" />
                          {partnership.contactInfo.primaryContact}
                        </div>
                        <div className="flex items-center gap-2">
                          <Mail className="h-3 w-3" />
                          {partnership.contactInfo.email}
                        </div>
                        {partnership.contactInfo.website && (
                          <div className="flex items-center gap-2">
                            <Globe className="h-3 w-3" />
                            <a href={partnership.contactInfo.website} target="_blank" rel="noopener noreferrer" className="hover:underline">
                              Website
                            </a>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-2">Performance</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Revenue Generated:</span>
                          <span className="font-medium">{formatCurrency(partnership.performance.revenueGenerated)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Leads Generated:</span>
                          <span className="font-medium">{partnership.performance.leadsGenerated}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Clients Referred:</span>
                          <span className="font-medium">{partnership.performance.clientsReferred}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Agreement</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Start Date:</span>
                          <span className="font-medium">{partnership.agreement.startDate.toLocaleDateString()}</span>
                        </div>
                        {partnership.agreement.contractValue && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Contract Value:</span>
                            <span className="font-medium">{formatCurrency(partnership.agreement.contractValue, partnership.agreement.currency)}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end gap-2 mt-4">
                    <Button variant="outline" size="sm">
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                    <Button variant="outline" size="sm">
                      <ExternalLink className="h-3 w-3 mr-1" />
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="opportunities" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Partnership Opportunities</CardTitle>
              <CardDescription>Potential partnerships in the pipeline</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Opportunities Yet</h3>
                <p className="text-muted-foreground mb-4">
                  Partnership opportunities will appear here as they are identified
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Opportunity
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Partnerships</CardTitle>
                <Handshake className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{partnerships.length}</div>
                <p className="text-xs text-muted-foreground">
                  {partnerships.filter(p => p.status === 'active').length} active
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(partnerships.reduce((sum, p) => sum + p.performance.revenueGenerated, 0))}
                </div>
                <p className="text-xs text-muted-foreground">
                  From all partnerships
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Leads Generated</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {partnerships.reduce((sum, p) => sum + p.performance.leadsGenerated, 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Total leads from partners
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
