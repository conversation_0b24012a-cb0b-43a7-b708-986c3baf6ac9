"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  Plus,
  Edit,
  Play,
  Pause,
  Users,
  Clock,
  CheckCircle,
  ArrowRight,
  Settings,
  Video,
  BookOpen,
  Star,
  Target,
  TrendingUp,
  Trash2
} from 'lucide-react';
import { evexaBusinessService } from '@/services/evexaBusinessService';
import type { OnboardingFlow, OnboardingStep } from '@/types/firestore';
import { useToast } from "@/hooks/use-toast";

interface OnboardingFlowBuilderProps {
  className?: string;
}

export default function OnboardingFlowBuilder({ className }: OnboardingFlowBuilderProps) {
  const { toast } = useToast();
  const [flows, setFlows] = useState<OnboardingFlow[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedFlow, setSelectedFlow] = useState<OnboardingFlow | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  useEffect(() => {
    loadFlows();
  }, []);

  const loadFlows = async () => {
    setIsLoading(true);
    try {
      const flowsData = await evexaBusinessService.getOnboardingFlows();
      setFlows(flowsData);
    } catch (error) {
      console.error('Error loading onboarding flows:', error);
      toast({
        title: "Error",
        description: "Failed to load onboarding flows",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateFlow = async (flowData: Omit<OnboardingFlow, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      await evexaBusinessService.createOnboardingFlow(flowData);
      toast({
        title: "Success",
        description: "Onboarding flow created successfully"
      });
      setIsCreateDialogOpen(false);
      loadFlows();
    } catch (error) {
      console.error('Error creating onboarding flow:', error);
      toast({
        title: "Error",
        description: "Failed to create onboarding flow",
        variant: "destructive"
      });
    }
  };

  const getAudienceColor = (audience: string) => {
    const colors = {
      new_user: 'bg-blue-100 text-blue-800',
      trial_user: 'bg-yellow-100 text-yellow-800',
      paid_user: 'bg-green-100 text-green-800',
      enterprise: 'bg-purple-100 text-purple-800'
    };
    return colors[audience as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getStepIcon = (type: string) => {
    const icons = {
      welcome: Star,
      setup: Settings,
      tutorial: BookOpen,
      demo: Video,
      completion: CheckCircle
    };
    return icons[type as keyof typeof icons] || BookOpen;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Customer Onboarding Flows</h2>
          <p className="text-muted-foreground">
            Create and manage customer onboarding experiences
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Onboarding Flow
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Create Onboarding Flow</DialogTitle>
              <DialogDescription>
                Design a new customer onboarding experience
              </DialogDescription>
            </DialogHeader>
            <OnboardingFlowForm onSubmit={handleCreateFlow} />
          </DialogContent>
        </Dialog>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Flows</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{flows.length}</div>
            <p className="text-xs text-muted-foreground">
              {flows.filter(f => f.isActive).length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Completion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {flows.length > 0 
                ? (flows.reduce((sum, flow) => sum + flow.completionRate, 0) / flows.length).toFixed(1)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Across all flows
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Completion Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {flows.length > 0 
                ? (flows.reduce((sum, flow) => sum + flow.averageCompletionTime, 0) / flows.length).toFixed(0)
                : 0} min
            </div>
            <p className="text-xs text-muted-foreground">
              Average time to complete
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Steps</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {flows.reduce((sum, flow) => sum + flow.steps.length, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all flows
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Onboarding Flows Table */}
      <Card>
        <CardHeader>
          <CardTitle>Onboarding Flows ({flows.length})</CardTitle>
          <CardDescription>
            Manage customer onboarding experiences
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Flow</TableHead>
                <TableHead>Target Audience</TableHead>
                <TableHead>Steps</TableHead>
                <TableHead>Completion Rate</TableHead>
                <TableHead>Avg Time</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {flows.map((flow) => (
                <TableRow key={flow.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{flow.name}</div>
                      <div className="text-sm text-muted-foreground">{flow.description}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary" className={getAudienceColor(flow.targetAudience)}>
                      {flow.targetAudience.replace('_', ' ')}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{flow.steps.length}</span>
                      <span className="text-sm text-muted-foreground">steps</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{flow.completionRate.toFixed(1)}%</span>
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full" 
                          style={{ width: `${flow.completionRate}%` }}
                        />
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>{flow.averageCompletionTime} min</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={flow.isActive ? "default" : "secondary"}>
                      {flow.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedFlow(flow);
                          setIsEditDialogOpen(true);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        {flow.isActive ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Edit Onboarding Flow</DialogTitle>
            <DialogDescription>
              Update your onboarding flow configuration
            </DialogDescription>
          </DialogHeader>
          {selectedFlow && (
            <OnboardingFlowForm 
              flow={selectedFlow}
              onSubmit={(data) => {
                // Handle update
                setIsEditDialogOpen(false);
                setSelectedFlow(null);
                loadFlows();
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Onboarding Flow Form Component
interface OnboardingFlowFormProps {
  flow?: OnboardingFlow;
  onSubmit: (data: any) => void;
}

function OnboardingFlowForm({ flow, onSubmit }: OnboardingFlowFormProps) {
  const [formData, setFormData] = useState({
    name: flow?.name || '',
    description: flow?.description || '',
    targetAudience: flow?.targetAudience || 'new_user',
    isActive: flow?.isActive || true,
    steps: flow?.steps || []
  });

  const [newStep, setNewStep] = useState<Partial<OnboardingStep>>({
    title: '',
    description: '',
    type: 'welcome',
    content: { text: '' },
    order: formData.steps.length + 1,
    isOptional: false,
    estimatedTime: 5
  });

  const addStep = () => {
    if (newStep.title && newStep.description) {
      const step: OnboardingStep = {
        id: `step_${Date.now()}`,
        title: newStep.title,
        description: newStep.description,
        type: newStep.type as any,
        content: newStep.content || { text: '' },
        order: formData.steps.length + 1,
        isOptional: newStep.isOptional || false,
        estimatedTime: newStep.estimatedTime || 5
      };

      setFormData({
        ...formData,
        steps: [...formData.steps, step]
      });

      setNewStep({
        title: '',
        description: '',
        type: 'welcome',
        content: { text: '' },
        order: formData.steps.length + 2,
        isOptional: false,
        estimatedTime: 5
      });
    }
  };

  const removeStep = (stepId: string) => {
    setFormData({
      ...formData,
      steps: formData.steps.filter(step => step.id !== stepId)
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const flowData = {
      ...formData,
      completionRate: flow?.completionRate || 0,
      averageCompletionTime: flow?.averageCompletionTime || 0
    };

    onSubmit(flowData);
  };

  const getStepIcon = (type: string) => {
    const icons = {
      welcome: Star,
      setup: Settings,
      tutorial: BookOpen,
      demo: Video,
      completion: CheckCircle
    };
    const Icon = icons[type as keyof typeof icons] || BookOpen;
    return <Icon className="h-4 w-4" />;
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Info */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="name">Flow Name</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            required
          />
        </div>
        <div>
          <Label htmlFor="targetAudience">Target Audience</Label>
          <Select value={formData.targetAudience} onValueChange={(value) => setFormData({ ...formData, targetAudience: value as any })}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="new_user">New User</SelectItem>
              <SelectItem value="trial_user">Trial User</SelectItem>
              <SelectItem value="paid_user">Paid User</SelectItem>
              <SelectItem value="enterprise">Enterprise</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          rows={3}
        />
      </div>

      {/* Steps */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-lg font-medium">Onboarding Steps</h4>
          <Badge variant="secondary">{formData.steps.length} steps</Badge>
        </div>

        {/* Existing Steps */}
        <div className="space-y-2">
          {formData.steps.map((step, index) => (
            <div key={step.id} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                  {index + 1}
                </div>
                <div className="flex items-center gap-2">
                  {getStepIcon(step.type)}
                  <div>
                    <div className="font-medium">{step.title}</div>
                    <div className="text-sm text-muted-foreground">{step.type}</div>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">{step.estimatedTime} min</Badge>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeStep(step.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Add New Step */}
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 space-y-4">
          <h5 className="font-medium">Add New Step</h5>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="stepTitle">Step Title</Label>
              <Input
                id="stepTitle"
                value={newStep.title}
                onChange={(e) => setNewStep({ ...newStep, title: e.target.value })}
                placeholder="e.g., Welcome to EVEXA"
              />
            </div>
            <div>
              <Label htmlFor="stepType">Step Type</Label>
              <Select value={newStep.type} onValueChange={(value) => setNewStep({ ...newStep, type: value as any })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="welcome">Welcome</SelectItem>
                  <SelectItem value="setup">Setup</SelectItem>
                  <SelectItem value="tutorial">Tutorial</SelectItem>
                  <SelectItem value="demo">Demo</SelectItem>
                  <SelectItem value="completion">Completion</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div>
            <Label htmlFor="stepDescription">Description</Label>
            <Textarea
              id="stepDescription"
              value={newStep.description}
              onChange={(e) => setNewStep({ ...newStep, description: e.target.value })}
              placeholder="Describe what happens in this step"
              rows={2}
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div>
                <Label htmlFor="estimatedTime">Estimated Time (minutes)</Label>
                <Input
                  id="estimatedTime"
                  type="number"
                  value={newStep.estimatedTime}
                  onChange={(e) => setNewStep({ ...newStep, estimatedTime: Number(e.target.value) })}
                  className="w-24"
                />
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isOptional"
                  checked={newStep.isOptional}
                  onChange={(e) => setNewStep({ ...newStep, isOptional: e.target.checked })}
                />
                <Label htmlFor="isOptional">Optional step</Label>
              </div>
            </div>
            <Button type="button" onClick={addStep}>
              <Plus className="mr-2 h-4 w-4" />
              Add Step
            </Button>
          </div>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="isActive"
          checked={formData.isActive}
          onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
        />
        <Label htmlFor="isActive">Activate this flow</Label>
      </div>

      <DialogFooter>
        <Button type="submit">
          {flow ? 'Update Flow' : 'Create Flow'}
        </Button>
      </DialogFooter>
    </form>
  );
}
