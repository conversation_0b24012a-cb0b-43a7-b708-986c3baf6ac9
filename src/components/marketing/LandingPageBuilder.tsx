"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { AdvancedDataTable, type AdvancedTableColumn, type AdvancedTableRowAction } from '@/components/ui/advanced-data-table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Plus, 
  Edit, 
  Eye, 
  Trash2, 
  Globe, 
  Bar<PERSON><PERSON>3,
  <PERSON><PERSON>,
  Settings,
  Palette,
  Layout,
  Type,
  Image as ImageIcon,
  Video,
  Star,
  Users,
  TrendingUp
} from 'lucide-react';
import { evexaBusinessService } from '@/services/evexaBusinessService';
import type { LandingPage } from '@/types/firestore';
import { useToast } from "@/hooks/use-toast";

interface LandingPageBuilderProps {
  className?: string;
}

export default function LandingPageBuilder({ className }: LandingPageBuilderProps) {
  const { toast } = useToast();
  const [pages, setPages] = useState<LandingPage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedPage, setSelectedPage] = useState<LandingPage | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  useEffect(() => {
    loadPages();
  }, []);

  const loadPages = async () => {
    setIsLoading(true);
    try {
      const pagesData = await evexaBusinessService.getLandingPages();
      setPages(pagesData);
    } catch (error) {
      console.error('Error loading landing pages:', error);
      toast({
        title: "Error",
        description: "Failed to load landing pages",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreatePage = async (pageData: Omit<LandingPage, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      await evexaBusinessService.createLandingPage(pageData);
      toast({
        title: "Success",
        description: "Landing page created successfully"
      });
      setIsCreateDialogOpen(false);
      loadPages();
    } catch (error) {
      console.error('Error creating landing page:', error);
      toast({
        title: "Error",
        description: "Failed to create landing page",
        variant: "destructive"
      });
    }
  };

  const handleUpdatePage = async (pageId: string, updates: Partial<LandingPage>) => {
    try {
      await evexaBusinessService.updateLandingPage(pageId, updates);
      toast({
        title: "Success",
        description: "Landing page updated successfully"
      });
      setIsEditDialogOpen(false);
      setSelectedPage(null);
      loadPages();
    } catch (error) {
      console.error('Error updating landing page:', error);
      toast({
        title: "Error",
        description: "Failed to update landing page",
        variant: "destructive"
      });
    }
  };

  const getTemplateIcon = (template: string) => {
    const icons = {
      product: Layout,
      pricing: BarChart3,
      demo: Video,
      contact: Users,
      feature: Star,
      industry: TrendingUp
    };
    return icons[template as keyof typeof icons] || Layout;
  };

  const getStatusColor = (isPublished: boolean) => {
    return isPublished ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
  };

  // Define columns for AdvancedDataTable
  const columns: AdvancedTableColumn<LandingPage>[] = useMemo(() => [
    {
      id: 'page',
      header: 'Page',
      accessorKey: 'name',
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.original.name}</div>
          <div className="text-sm text-muted-foreground">/{row.original.slug}</div>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      id: 'template',
      header: 'Template',
      accessorKey: 'template',
      cell: ({ row }) => {
        const TemplateIcon = getTemplateIcon(row.original.template);
        return (
          <div className="flex items-center gap-2">
            <TemplateIcon className="h-4 w-4 text-muted-foreground" />
            <span className="capitalize">{row.original.template}</span>
          </div>
        );
      },
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      id: 'status',
      header: 'Status',
      accessorKey: 'isPublished',
      cell: ({ row }) => (
        <Badge variant="secondary" className={getStatusColor(row.original.isPublished)}>
          {row.original.isPublished ? 'Published' : 'Draft'}
        </Badge>
      ),
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      id: 'views',
      header: 'Views',
      accessorKey: 'analytics.views',
      cell: ({ row }) => (
        <div className="font-medium">{row.original.analytics?.views || 0}</div>
      ),
      enableSorting: true,
      enableColumnFilter: false,
    },
    {
      id: 'conversions',
      header: 'Conversions',
      accessorKey: 'analytics.conversions',
      cell: ({ row }) => (
        <div className="font-medium">{row.original.analytics?.conversions || 0}</div>
      ),
      enableSorting: true,
      enableColumnFilter: false,
    },
    {
      id: 'conversionRate',
      header: 'Conversion Rate',
      accessorKey: 'analytics.conversionRate',
      cell: ({ row }) => (
        <div className="font-medium">{row.original.analytics?.conversionRate?.toFixed(1) || 0}%</div>
      ),
      enableSorting: true,
      enableColumnFilter: false,
    },
  ], []);

  // Define row actions
  const getRowActions = (page: LandingPage): AdvancedTableRowAction<LandingPage>[] => [
    {
      label: 'Edit Page',
      icon: Edit,
      onClick: () => {
        setSelectedPage(page);
        setIsEditDialogOpen(true);
      },
    },
    {
      label: 'Duplicate Page',
      icon: Copy,
      onClick: () => {
        // Add duplicate functionality if needed
      },
    },
  ];

  // Mobile card renderer
  const mobileCardRenderer = (page: LandingPage) => (
    <div className="p-4 space-y-2">
      <div className="flex items-center justify-between">
        <div>
          <div className="font-medium">{page.name}</div>
          <div className="text-sm text-muted-foreground">/{page.slug}</div>
        </div>
        <Badge variant="secondary" className={getStatusColor(page.isPublished)}>
          {page.isPublished ? 'Published' : 'Draft'}
        </Badge>
      </div>
      <div className="flex items-center gap-2 text-sm">
        {(() => {
          const TemplateIcon = getTemplateIcon(page.template);
          return <TemplateIcon className="h-4 w-4 text-muted-foreground" />;
        })()}
        <span className="capitalize">{page.template}</span>
      </div>
      <div className="grid grid-cols-3 gap-4 text-sm">
        <div>
          <span className="font-medium">Views:</span> {page.analytics?.views || 0}
        </div>
        <div>
          <span className="font-medium">Conversions:</span> {page.analytics?.conversions || 0}
        </div>
        <div>
          <span className="font-medium">Rate:</span> {page.analytics?.conversionRate?.toFixed(1) || 0}%
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Landing Page Builder</h2>
          <p className="text-muted-foreground">
            Create and manage EVEXA marketing landing pages
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Landing Page
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Create New Landing Page</DialogTitle>
              <DialogDescription>
                Build a new marketing landing page for EVEXA
              </DialogDescription>
            </DialogHeader>
            <LandingPageForm onSubmit={handleCreatePage} />
          </DialogContent>
        </Dialog>
      </div>

      {/* Analytics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pages</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pages.length}</div>
            <p className="text-xs text-muted-foreground">
              {pages.filter(p => p.isPublished).length} published
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {pages.reduce((sum, page) => sum + (page.analytics?.views || 0), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversions</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {pages.reduce((sum, page) => sum + (page.analytics?.conversions || 0), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total leads generated
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Conversion Rate</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {pages.length > 0 
                ? (pages.reduce((sum, page) => sum + (page.analytics?.conversionRate || 0), 0) / pages.length).toFixed(1)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Across all pages
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Landing Pages Table */}
      <Card>
        <CardHeader>
          <CardTitle>Landing Pages ({pages.length})</CardTitle>
          <CardDescription>
            Manage your EVEXA marketing landing pages
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AdvancedDataTable
            data={pages}
            columns={columns}
            loading={false}
            enableVirtualization={pages.length > 100}
            enableGlobalSearch={true}
            searchPlaceholder="Search landing pages by name, template, or status..."
            enableColumnFilters={true}
            enableRowSelection={false}
            enableColumnResizing={true}
            enableColumnVisibility={true}
            enableRowActions={true}
            rowActions={getRowActions}
            maxVisibleRowActions={2}
            enableExport={true}
            exportFormats={['csv', 'excel']}
            exportFileName="landing-pages-export"
            mobileCardRenderer={mobileCardRenderer}
            onRefresh={loadPages}
            variant="default"
            className="w-full"
            emptyMessage="No landing pages found. Create your first landing page to get started."
          />
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Edit Landing Page</DialogTitle>
            <DialogDescription>
              Update your landing page content and settings
            </DialogDescription>
          </DialogHeader>
          {selectedPage && (
            <LandingPageForm 
              page={selectedPage}
              onSubmit={(data) => handleUpdatePage(selectedPage.id!, data)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Landing Page Form Component
interface LandingPageFormProps {
  page?: LandingPage;
  onSubmit: (data: any) => void;
}

function LandingPageForm({ page, onSubmit }: LandingPageFormProps) {
  const [formData, setFormData] = useState({
    name: page?.name || '',
    slug: page?.slug || '',
    title: page?.title || '',
    description: page?.description || '',
    template: page?.template || 'product',
    isPublished: page?.isPublished || false,
    hero: {
      headline: page?.content?.hero?.headline || '',
      subheadline: page?.content?.hero?.subheadline || '',
      ctaText: page?.content?.hero?.ctaText || 'Get Started',
      ctaLink: page?.content?.hero?.ctaLink || '/demo'
    },
    seoMeta: {
      title: page?.seoMeta?.title || '',
      description: page?.seoMeta?.description || '',
      keywords: page?.seoMeta?.keywords?.join(', ') || ''
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const pageData = {
      ...formData,
      content: {
        hero: formData.hero,
        features: [],
        testimonials: []
      },
      seoMeta: {
        ...formData.seoMeta,
        keywords: formData.seoMeta.keywords.split(',').map(k => k.trim()).filter(k => k)
      },
      analytics: page?.analytics || {
        views: 0,
        conversions: 0,
        conversionRate: 0,
        lastUpdated: new Date()
      },
      createdBy: 'current-user'
    };

    onSubmit(pageData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="seo">SEO</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Page Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </div>
            <div>
              <Label htmlFor="slug">URL Slug</Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) => setFormData({ ...formData, slug: e.target.value })}
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="title">Page Title</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              required
            />
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="template">Template</Label>
            <Select value={formData.template} onValueChange={(value) => setFormData({ ...formData, template: value as any })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="product">Product Showcase</SelectItem>
                <SelectItem value="pricing">Pricing Page</SelectItem>
                <SelectItem value="demo">Demo Request</SelectItem>
                <SelectItem value="contact">Contact Us</SelectItem>
                <SelectItem value="feature">Feature Highlight</SelectItem>
                <SelectItem value="industry">Industry Solution</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <div>
            <Label htmlFor="headline">Hero Headline</Label>
            <Input
              id="headline"
              value={formData.hero.headline}
              onChange={(e) => setFormData({ 
                ...formData, 
                hero: { ...formData.hero, headline: e.target.value }
              })}
            />
          </div>

          <div>
            <Label htmlFor="subheadline">Hero Subheadline</Label>
            <Textarea
              id="subheadline"
              value={formData.hero.subheadline}
              onChange={(e) => setFormData({ 
                ...formData, 
                hero: { ...formData.hero, subheadline: e.target.value }
              })}
              rows={2}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="ctaText">CTA Button Text</Label>
              <Input
                id="ctaText"
                value={formData.hero.ctaText}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  hero: { ...formData.hero, ctaText: e.target.value }
                })}
              />
            </div>
            <div>
              <Label htmlFor="ctaLink">CTA Link</Label>
              <Input
                id="ctaLink"
                value={formData.hero.ctaLink}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  hero: { ...formData.hero, ctaLink: e.target.value }
                })}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="seo" className="space-y-4">
          <div>
            <Label htmlFor="seoTitle">SEO Title</Label>
            <Input
              id="seoTitle"
              value={formData.seoMeta.title}
              onChange={(e) => setFormData({ 
                ...formData, 
                seoMeta: { ...formData.seoMeta, title: e.target.value }
              })}
            />
          </div>

          <div>
            <Label htmlFor="seoDescription">SEO Description</Label>
            <Textarea
              id="seoDescription"
              value={formData.seoMeta.description}
              onChange={(e) => setFormData({ 
                ...formData, 
                seoMeta: { ...formData.seoMeta, description: e.target.value }
              })}
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="keywords">Keywords (comma-separated)</Label>
            <Input
              id="keywords"
              value={formData.seoMeta.keywords}
              onChange={(e) => setFormData({ 
                ...formData, 
                seoMeta: { ...formData.seoMeta, keywords: e.target.value }
              })}
            />
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isPublished"
              checked={formData.isPublished}
              onChange={(e) => setFormData({ ...formData, isPublished: e.target.checked })}
            />
            <Label htmlFor="isPublished">Publish this page</Label>
          </div>
        </TabsContent>
      </Tabs>

      <DialogFooter>
        <Button type="submit">
          {page ? 'Update Page' : 'Create Page'}
        </Button>
      </DialogFooter>
    </form>
  );
}
