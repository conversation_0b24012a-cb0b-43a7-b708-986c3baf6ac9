"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  User, 
  Building, 
  Mail, 
  Phone, 
  Globe, 
  Users, 
  Calendar,
  CheckCircle,
  ArrowRight,
  Shield,
  Star,
  Zap
} from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import { evexaBusinessService } from '@/services/evexaBusinessService';

interface CustomerRegistrationProps {
  className?: string;
  selectedPlan?: string;
  onSuccess?: () => void;
}

export default function CustomerRegistration({ className, selectedPlan, onSuccess }: CustomerRegistrationProps) {
  const { toast } = useToast();
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    jobTitle: '',
    
    // Company Information
    companyName: '',
    companySize: '',
    industry: '',
    website: '',
    country: '',
    
    // Exhibition Information
    annualExhibitions: '',
    exhibitionBudget: '',
    currentTools: '',
    painPoints: '',
    
    // Preferences
    selectedPlan: selectedPlan || 'starter',
    billingCycle: 'monthly',
    marketingConsent: false,
    termsAccepted: false
  });

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleNext = () => {
    if (validateStep(step)) {
      setStep(step + 1);
    }
  };

  const handlePrevious = () => {
    setStep(step - 1);
  };

  const validateStep = (currentStep: number): boolean => {
    switch (currentStep) {
      case 1:
        if (!formData.firstName || !formData.lastName || !formData.email) {
          toast({
            title: "Required Fields",
            description: "Please fill in all required personal information.",
            variant: "destructive"
          });
          return false;
        }
        break;
      case 2:
        if (!formData.companyName || !formData.companySize || !formData.industry) {
          toast({
            title: "Required Fields",
            description: "Please fill in all required company information.",
            variant: "destructive"
          });
          return false;
        }
        break;
      case 3:
        if (!formData.annualExhibitions) {
          toast({
            title: "Required Fields",
            description: "Please provide exhibition information.",
            variant: "destructive"
          });
          return false;
        }
        break;
      case 4:
        if (!formData.termsAccepted) {
          toast({
            title: "Terms Required",
            description: "Please accept the terms and conditions to continue.",
            variant: "destructive"
          });
          return false;
        }
        break;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateStep(4)) return;

    setLoading(true);
    try {
      // Create lead/customer record
      await evexaBusinessService.createCustomerLead({
        personalInfo: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          jobTitle: formData.jobTitle
        },
        companyInfo: {
          name: formData.companyName,
          size: formData.companySize,
          industry: formData.industry,
          website: formData.website,
          country: formData.country
        },
        exhibitionInfo: {
          annualExhibitions: formData.annualExhibitions,
          budget: formData.exhibitionBudget,
          currentTools: formData.currentTools,
          painPoints: formData.painPoints
        },
        preferences: {
          selectedPlan: formData.selectedPlan,
          billingCycle: formData.billingCycle,
          marketingConsent: formData.marketingConsent
        },
        source: 'website_registration',
        status: 'new_lead'
      });

      toast({
        title: "Registration Successful!",
        description: "Welcome to EVEXA! Check your email for next steps."
      });

      setStep(5); // Success step
      onSuccess?.();
    } catch (error) {
      console.error('Registration error:', error);
      toast({
        title: "Registration Failed",
        description: "Please try again or contact support.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return (
          <div className="space-y-4">
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold">Personal Information</h3>
              <p className="text-muted-foreground">Let's start with your basic details</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  required
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="email">Email Address *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                required
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="jobTitle">Job Title</Label>
                <Input
                  id="jobTitle"
                  value={formData.jobTitle}
                  onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                />
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-4">
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold">Company Information</h3>
              <p className="text-muted-foreground">Tell us about your organization</p>
            </div>
            
            <div>
              <Label htmlFor="companyName">Company Name *</Label>
              <Input
                id="companyName"
                value={formData.companyName}
                onChange={(e) => handleInputChange('companyName', e.target.value)}
                required
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="companySize">Company Size *</Label>
                <Select value={formData.companySize} onValueChange={(value) => handleInputChange('companySize', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select company size" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1-10">1-10 employees</SelectItem>
                    <SelectItem value="11-50">11-50 employees</SelectItem>
                    <SelectItem value="51-200">51-200 employees</SelectItem>
                    <SelectItem value="201-1000">201-1000 employees</SelectItem>
                    <SelectItem value="1000+">1000+ employees</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="industry">Industry *</Label>
                <Select value={formData.industry} onValueChange={(value) => handleInputChange('industry', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select industry" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="technology">Technology</SelectItem>
                    <SelectItem value="healthcare">Healthcare</SelectItem>
                    <SelectItem value="manufacturing">Manufacturing</SelectItem>
                    <SelectItem value="automotive">Automotive</SelectItem>
                    <SelectItem value="finance">Finance</SelectItem>
                    <SelectItem value="retail">Retail</SelectItem>
                    <SelectItem value="education">Education</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  value={formData.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="https://example.com"
                />
              </div>
              <div>
                <Label htmlFor="country">Country</Label>
                <Input
                  id="country"
                  value={formData.country}
                  onChange={(e) => handleInputChange('country', e.target.value)}
                />
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-4">
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold">Exhibition Information</h3>
              <p className="text-muted-foreground">Help us understand your exhibition needs</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="annualExhibitions">Annual Exhibitions *</Label>
                <Select value={formData.annualExhibitions} onValueChange={(value) => handleInputChange('annualExhibitions', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select number" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1-5">1-5 exhibitions</SelectItem>
                    <SelectItem value="6-15">6-15 exhibitions</SelectItem>
                    <SelectItem value="16-30">16-30 exhibitions</SelectItem>
                    <SelectItem value="30+">30+ exhibitions</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="exhibitionBudget">Annual Exhibition Budget</Label>
                <Select value={formData.exhibitionBudget} onValueChange={(value) => handleInputChange('exhibitionBudget', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select budget range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="under-100k">Under $100K</SelectItem>
                    <SelectItem value="100k-500k">$100K - $500K</SelectItem>
                    <SelectItem value="500k-1m">$500K - $1M</SelectItem>
                    <SelectItem value="1m-5m">$1M - $5M</SelectItem>
                    <SelectItem value="5m+">$5M+</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div>
              <Label htmlFor="currentTools">Current Tools/Platforms</Label>
              <Textarea
                id="currentTools"
                value={formData.currentTools}
                onChange={(e) => handleInputChange('currentTools', e.target.value)}
                placeholder="What tools do you currently use for exhibition management?"
                rows={3}
              />
            </div>
            
            <div>
              <Label htmlFor="painPoints">Main Challenges</Label>
              <Textarea
                id="painPoints"
                value={formData.painPoints}
                onChange={(e) => handleInputChange('painPoints', e.target.value)}
                placeholder="What are your biggest challenges in exhibition management?"
                rows={3}
              />
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold">Plan Selection & Preferences</h3>
              <p className="text-muted-foreground">Choose your EVEXA plan and preferences</p>
            </div>
            
            <div>
              <Label>Selected Plan</Label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
                {['starter', 'professional', 'enterprise'].map((plan) => (
                  <Card 
                    key={plan}
                    className={`cursor-pointer transition-all ${formData.selectedPlan === plan ? 'ring-2 ring-primary' : ''}`}
                    onClick={() => handleInputChange('selectedPlan', plan)}
                  >
                    <CardContent className="p-4 text-center">
                      <div className="flex items-center justify-center mb-2">
                        {plan === 'starter' && <Zap className="h-5 w-5 text-blue-600" />}
                        {plan === 'professional' && <Star className="h-5 w-5 text-purple-600" />}
                        {plan === 'enterprise' && <Building className="h-5 w-5 text-orange-600" />}
                      </div>
                      <div className="font-semibold capitalize">{plan}</div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
            
            <div>
              <Label>Billing Cycle</Label>
              <div className="flex gap-4 mt-2">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="billingCycle"
                    value="monthly"
                    checked={formData.billingCycle === 'monthly'}
                    onChange={(e) => handleInputChange('billingCycle', e.target.value)}
                  />
                  Monthly
                </label>
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="billingCycle"
                    value="yearly"
                    checked={formData.billingCycle === 'yearly'}
                    onChange={(e) => handleInputChange('billingCycle', e.target.value)}
                  />
                  Yearly (Save 17%)
                </label>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="marketingConsent"
                  checked={formData.marketingConsent}
                  onCheckedChange={(checked) => handleInputChange('marketingConsent', checked as boolean)}
                />
                <Label htmlFor="marketingConsent" className="text-sm">
                  I agree to receive marketing communications from EVEXA
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="termsAccepted"
                  checked={formData.termsAccepted}
                  onCheckedChange={(checked) => handleInputChange('termsAccepted', checked as boolean)}
                />
                <Label htmlFor="termsAccepted" className="text-sm">
                  I accept the <a href="/terms" className="text-primary hover:underline">Terms of Service</a> and <a href="/privacy" className="text-primary hover:underline">Privacy Policy</a> *
                </Label>
              </div>
            </div>
          </div>
        );

      case 5:
        return (
          <div className="text-center space-y-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-green-600">Welcome to EVEXA!</h3>
              <p className="text-muted-foreground mt-2">
                Your registration was successful. Check your email for next steps.
              </p>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg">
              <h4 className="font-semibold mb-2">What's Next?</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Check your email for account activation</li>
                <li>• Complete your profile setup</li>
                <li>• Start your 14-day free trial</li>
                <li>• Schedule an onboarding call</li>
              </ul>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (step === 5) {
    return (
      <Card className="max-w-2xl mx-auto">
        <CardContent className="p-8">
          {renderStep()}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Join EVEXA</CardTitle>
            <CardDescription>Start your exhibition management journey</CardDescription>
          </div>
          <Badge variant="outline">Step {step} of 4</Badge>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2 mt-4">
          <div 
            className="bg-primary h-2 rounded-full transition-all duration-300"
            style={{ width: `${(step / 4) * 100}%` }}
          />
        </div>
      </CardHeader>
      
      <CardContent className="p-8">
        {renderStep()}
        
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={step === 1}
          >
            Previous
          </Button>
          
          {step < 4 ? (
            <Button onClick={handleNext}>
              Next
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button onClick={handleSubmit} disabled={loading}>
              {loading ? 'Creating Account...' : 'Complete Registration'}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
