"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { 
  ArrowRight, 
  CheckCircle, 
  Star, 
  Users, 
  Building, 
  Globe,
  Zap,
  Shield,
  BarChart3,
  Calendar,
  Mail,
  Phone,
  Play,
  Award,
  TrendingUp,
  Target,
  Sparkles
} from 'lucide-react';
import { subscriptionService, type SubscriptionPlan } from '@/services/subscriptionService';
import { useToast } from "@/hooks/use-toast";
import Link from 'next/link';

interface MainLandingPageProps {
  className?: string;
}

export default function MainLandingPage({ className }: MainLandingPageProps) {
  const { toast } = useToast();
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [isYearly, setIsYearly] = useState(false);
  const [leadForm, setLeadForm] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    message: ''
  });

  useEffect(() => {
    loadPlans();
  }, []);

  const loadPlans = async () => {
    try {
      setLoading(true);
      const plansData = await subscriptionService.getAllPlans();
      setPlans(plansData.filter(plan => plan.isActive));
    } catch (error) {
      console.error('Failed to load plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLeadSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // TODO: Implement lead capture service
      toast({
        title: "Thank you!",
        description: "We'll be in touch within 24 hours."
      });
      setLeadForm({ name: '', email: '', company: '', phone: '', message: '' });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit form. Please try again.",
        variant: "destructive"
      });
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(amount);
  };

  const getPrice = (plan: SubscriptionPlan) => {
    return isYearly ? plan.pricing.yearly : plan.pricing.monthly;
  };

  const getTierIcon = (tier: SubscriptionPlan['tier']) => {
    switch (tier) {
      case 'free': return <Users className="h-6 w-6" />;
      case 'starter': return <Zap className="h-6 w-6" />;
      case 'professional': return <Star className="h-6 w-6" />;
      case 'enterprise': return <Building className="h-6 w-6" />;
      default: return <Users className="h-6 w-6" />;
    }
  };

  const features = [
    {
      icon: Calendar,
      title: "Exhibition Management",
      description: "Complete event lifecycle management from planning to post-show analysis"
    },
    {
      icon: Users,
      title: "Team Collaboration",
      description: "Seamless collaboration tools for distributed exhibition teams"
    },
    {
      icon: BarChart3,
      title: "Advanced Analytics",
      description: "Real-time insights and performance metrics for data-driven decisions"
    },
    {
      icon: Shield,
      title: "Enterprise Security",
      description: "Bank-grade security with SOC 2 compliance and data encryption"
    },
    {
      icon: Globe,
      title: "Global Reach",
      description: "Multi-language support and international compliance frameworks"
    },
    {
      icon: Sparkles,
      title: "AI-Powered Insights",
      description: "Intelligent automation and predictive analytics for exhibition success"
    }
  ];

  const testimonials = [
    {
      name: "Sarah Johnson",
      role: "Exhibition Director",
      company: "TechCorp Global",
      content: "EVEXA transformed how we manage our 50+ annual exhibitions. ROI increased by 40%.",
      rating: 5
    },
    {
      name: "Michael Chen",
      role: "Marketing Manager",
      company: "Innovation Labs",
      content: "The analytics and automation features saved us 20 hours per week on exhibition planning.",
      rating: 5
    },
    {
      name: "Emily Rodriguez",
      role: "Event Coordinator",
      company: "Global Events Inc",
      content: "Best exhibition management platform we've used. The team collaboration features are outstanding.",
      rating: 5
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <section className="relative py-20 px-6">
        <div className="max-w-7xl mx-auto text-center">
          <Badge className="mb-6 bg-primary/10 text-primary border-primary/20">
            🚀 Trusted by 500+ Exhibition Teams Worldwide
          </Badge>
          
          <h1 className="text-5xl md:text-7xl font-bold tracking-tight mb-6">
            The Future of
            <span className="bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
              {" "}Exhibition Management
            </span>
          </h1>
          
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            EVEXA empowers exhibition teams with AI-driven insights, seamless collaboration, 
            and comprehensive management tools to create extraordinary events that drive business growth.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button size="lg" className="text-lg px-8 py-6" asChild>
              <Link href="/pricing">
                Start Free Trial
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8 py-6">
              <Play className="mr-2 h-5 w-5" />
              Watch Demo
            </Button>
          </div>
          
          <div className="flex items-center justify-center gap-8 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              14-day free trial
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              No credit card required
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              Cancel anytime
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-6 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold tracking-tight mb-4">
              Everything You Need for Exhibition Success
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              From initial planning to post-event analysis, EVEXA provides comprehensive tools 
              to manage every aspect of your exhibitions.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardHeader>
                  <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 px-6 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold tracking-tight mb-4">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-muted-foreground mb-8">
              Choose the perfect plan for your exhibition management needs
            </p>
            
            <div className="flex items-center justify-center gap-4 mb-8">
              <span className={`text-sm ${!isYearly ? 'font-semibold' : 'text-muted-foreground'}`}>
                Monthly
              </span>
              <Switch
                checked={isYearly}
                onCheckedChange={setIsYearly}
              />
              <span className={`text-sm ${isYearly ? 'font-semibold' : 'text-muted-foreground'}`}>
                Yearly
              </span>
              {isYearly && (
                <Badge className="bg-green-100 text-green-800">Save 17%</Badge>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {plans.map((plan) => (
              <Card key={plan.id} className={`relative ${plan.isPopular ? 'ring-2 ring-primary scale-105' : ''} transition-all duration-300 hover:shadow-lg`}>
                {plan.isPopular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-primary text-primary-foreground px-4 py-1">
                      Most Popular
                    </Badge>
                  </div>
                )}
                <CardHeader className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    {getTierIcon(plan.tier)}
                    <Badge variant="outline">
                      {plan.displayName}
                    </Badge>
                  </div>
                  
                  <div className="mt-6">
                    <div className="text-4xl font-bold">
                      {formatCurrency(getPrice(plan))}
                      <span className="text-lg font-normal text-muted-foreground">
                        /{isYearly ? 'year' : 'month'}
                      </span>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <Button 
                    className={`w-full mb-6 ${plan.isPopular ? 'bg-primary hover:bg-primary/90' : ''}`}
                    variant={plan.isPopular ? 'default' : 'outline'}
                    asChild
                  >
                    <Link href="/pricing">
                      {plan.tier === 'free' ? 'Get Started Free' : 'Start Free Trial'}
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Link>
                  </Button>
                  
                  <div className="space-y-3">
                    <h4 className="font-semibold text-sm">What's included:</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>{plan.features.maxExhibitions === -1 ? 'Unlimited' : plan.features.maxExhibitions} exhibitions</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>{plan.features.maxUsers === -1 ? 'Unlimited' : plan.features.maxUsers} team members</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>{plan.features.storageGB === -1 ? 'Unlimited' : `${plan.features.storageGB}GB`} storage</span>
                      </div>
                      {plan.features.advancedAnalytics && (
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span>Advanced analytics</span>
                        </div>
                      )}
                      {plan.features.prioritySupport && (
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span>Priority support</span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-6 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold tracking-tight mb-4">
              Trusted by Exhibition Leaders
            </h2>
            <p className="text-xl text-muted-foreground">
              See what our customers say about EVEXA
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="border-0 shadow-lg">
                <CardContent className="pt-6">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <p className="text-muted-foreground mb-4">"{testimonial.content}"</p>
                  <div>
                    <div className="font-semibold">{testimonial.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {testimonial.role} at {testimonial.company}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6 bg-primary text-primary-foreground">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold tracking-tight mb-4">
            Ready to Transform Your Exhibitions?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Join thousands of exhibition professionals who trust EVEXA to deliver exceptional events.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="text-lg px-8 py-6" asChild>
              <Link href="/pricing">
                Start Your Free Trial
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8 py-6 border-white text-white hover:bg-white hover:text-primary">
              Contact Sales
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
