'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Heart, 
  Car, 
  Laptop, 
  Shield, 
  FileText, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  BarChart3,
  Settings
} from 'lucide-react';
import { IndustrySpecificService } from '@/services/industrySpecificService';

interface IndustrySpecificDashboardProps {
  tenantId: string;
  userRole: string;
}

export default function IndustrySpecificDashboard({ tenantId, userRole }: IndustrySpecificDashboardProps) {
  const [selectedIndustry, setSelectedIndustry] = useState<'healthcare' | 'automotive' | 'technology'>('healthcare');
  const [workflows, setWorkflows] = useState<any[]>([]);
  const [analytics, setAnalytics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const industrySpecificService = IndustrySpecificService.getInstance();

  useEffect(() => {
    loadIndustryData();
  }, [selectedIndustry, tenantId]);

  const loadIndustryData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load workflows based on selected industry
      let workflowData: any[] = [];
      switch (selectedIndustry) {
        case 'healthcare':
          workflowData = await industrySpecificService.getHealthcareComplianceWorkflows(tenantId);
          break;
        case 'automotive':
          workflowData = await industrySpecificService.getAutomotiveComplianceWorkflows(tenantId);
          break;
        case 'technology':
          workflowData = await industrySpecificService.getTechnologyComplianceWorkflows(tenantId);
          break;
      }

      setWorkflows(workflowData);

      // Load analytics
      const analyticsData = await industrySpecificService.getIndustryAnalytics(
        selectedIndustry,
        tenantId,
        'overview'
      );
      setAnalytics(analyticsData);
    } catch (err) {
      console.error('Error loading industry data:', err);
      setError('Failed to load industry data');
    } finally {
      setLoading(false);
    }
  };

  const generateWorkflow = async (workflowType: string) => {
    try {
      setLoading(true);
      const workflow = await industrySpecificService.generateIndustrySpecificWorkflow(
        selectedIndustry,
        workflowType,
        { tenantId }
      );
      
      // Refresh data after generating workflow
      await loadIndustryData();
    } catch (err) {
      console.error('Error generating workflow:', err);
      setError('Failed to generate workflow');
    } finally {
      setLoading(false);
    }
  };

  const generateReport = async (reportType: string) => {
    try {
      setLoading(true);
      const dateRange = {
        startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // 90 days ago
        endDate: new Date()
      };

      const report = await industrySpecificService.generateIndustryComplianceReport(
        selectedIndustry,
        tenantId,
        reportType,
        dateRange
      );
      
      // Handle report display or download
      console.log('Generated report:', report);
    } catch (err) {
      console.error('Error generating report:', err);
      setError('Failed to generate report');
    } finally {
      setLoading(false);
    }
  };

  const getIndustryIcon = (industry: string) => {
    switch (industry) {
      case 'healthcare': return <Heart className="h-5 w-5" />;
      case 'automotive': return <Car className="h-5 w-5" />;
      case 'technology': return <Laptop className="h-5 w-5" />;
      default: return <Settings className="h-5 w-5" />;
    }
  };

  const getComplianceStatusColor = (status: string) => {
    switch (status) {
      case 'compliant': return 'bg-green-500';
      case 'pending': return 'bg-yellow-500';
      case 'non_compliant': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Industry Selection */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Industry-Specific Compliance</h1>
        <Tabs value={selectedIndustry} onValueChange={(value) => setSelectedIndustry(value as any)}>
          <TabsList>
            <TabsTrigger value="healthcare" className="flex items-center gap-2">
              <Heart className="h-4 w-4" />
              Healthcare
            </TabsTrigger>
            <TabsTrigger value="automotive" className="flex items-center gap-2">
              <Car className="h-4 w-4" />
              Automotive
            </TabsTrigger>
            <TabsTrigger value="technology" className="flex items-center gap-2">
              <Laptop className="h-4 w-4" />
              Technology
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Workflows</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics?.totalWorkflows || 0}</div>
            <p className="text-xs text-muted-foreground">
              Active compliance workflows
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Compliance Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics?.complianceRate?.toFixed(1) || 0}%</div>
            <Progress value={analytics?.complianceRate || 0} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Risk Level</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <Badge variant={analytics?.riskLevel === 'low' ? 'default' : 'destructive'}>
                {analytics?.riskLevel || 'Medium'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              Overall risk assessment
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Issues</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {workflows.filter(w => w.complianceStatus === 'non_compliant').length}
            </div>
            <p className="text-xs text-muted-foreground">
              Requiring immediate attention
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Industry-Specific Content */}
      <Tabs value={selectedIndustry} className="space-y-6">
        <TabsContent value="healthcare" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5" />
                  Healthcare Compliance Overview
                </CardTitle>
                <CardDescription>
                  Medical device and healthcare compliance status
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>FDA Approvals</span>
                  <Badge>{analytics?.fdaApprovals || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>CE Markings</span>
                  <Badge>{analytics?.ceMarkings || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Risk Assessments</span>
                  <Badge>{analytics?.riskAssessments || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Post-Market Surveillance</span>
                  <Badge>{analytics?.postMarketSurveillance || 0}</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Generate healthcare-specific workflows and reports
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  onClick={() => generateWorkflow('medical_device_approval')}
                  className="w-full"
                  variant="outline"
                >
                  Generate Medical Device Approval Workflow
                </Button>
                <Button 
                  onClick={() => generateWorkflow('clinical_trial_management')}
                  className="w-full"
                  variant="outline"
                >
                  Generate Clinical Trial Management Workflow
                </Button>
                <Button 
                  onClick={() => generateReport('regulatory_compliance')}
                  className="w-full"
                  variant="outline"
                >
                  Generate Regulatory Compliance Report
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="automotive" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Car className="h-5 w-5" />
                  Automotive Compliance Overview
                </CardTitle>
                <CardDescription>
                  Vehicle safety and automotive compliance status
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Safety Standards</span>
                  <Badge>{analytics?.safetyStandards || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Certifications</span>
                  <Badge>{analytics?.certifications || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Testing Results</span>
                  <Badge>{analytics?.testingResults || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Recalls</span>
                  <Badge variant="destructive">{analytics?.recalls || 0}</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Generate automotive-specific workflows and reports
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  onClick={() => generateWorkflow('vehicle_type_approval')}
                  className="w-full"
                  variant="outline"
                >
                  Generate Vehicle Type Approval Workflow
                </Button>
                <Button 
                  onClick={() => generateWorkflow('functional_safety_assessment')}
                  className="w-full"
                  variant="outline"
                >
                  Generate Functional Safety Assessment
                </Button>
                <Button 
                  onClick={() => generateReport('audit_readiness')}
                  className="w-full"
                  variant="outline"
                >
                  Generate Audit Readiness Report
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="technology" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Laptop className="h-5 w-5" />
                  Technology Compliance Overview
                </CardTitle>
                <CardDescription>
                  Software security and technology compliance status
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>IP Protections</span>
                  <Badge>{analytics?.ipProtections || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Security Compliance</span>
                  <Badge>{analytics?.securityCompliance || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Data Privacy Compliance</span>
                  <Badge>{analytics?.dataPrivacyCompliance || 0}</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Generate technology-specific workflows and reports
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  onClick={() => generateWorkflow('software_security_compliance')}
                  className="w-full"
                  variant="outline"
                >
                  Generate Security Compliance Workflow
                </Button>
                <Button 
                  onClick={() => generateWorkflow('data_privacy_compliance')}
                  className="w-full"
                  variant="outline"
                >
                  Generate Data Privacy Compliance Workflow
                </Button>
                <Button 
                  onClick={() => generateReport('risk_assessment')}
                  className="w-full"
                  variant="outline"
                >
                  Generate Risk Assessment Report
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Recent Workflows */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Workflows</CardTitle>
          <CardDescription>
            Latest {selectedIndustry} compliance workflows
          </CardDescription>
        </CardHeader>
        <CardContent>
          {workflows.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">
              No workflows found. Create your first {selectedIndustry} compliance workflow.
            </p>
          ) : (
            <div className="space-y-4">
              {workflows.slice(0, 5).map((workflow, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getIndustryIcon(selectedIndustry)}
                    <div>
                      <h4 className="font-medium">
                        {workflow.deviceName || workflow.productName || `${selectedIndustry} Workflow`}
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        {workflow.deviceType || workflow.vehicleType || workflow.productType}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div 
                      className={`w-3 h-3 rounded-full ${getComplianceStatusColor(workflow.complianceStatus)}`}
                    />
                    <span className="text-sm capitalize">
                      {workflow.complianceStatus || 'Unknown'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
