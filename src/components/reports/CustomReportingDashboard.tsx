"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/hooks/useAuth';
import { useTenant } from '@/hooks/useTenant';
import { auth } from '@/lib/firebase';
import { toast } from 'sonner';
import {
  Bar<PERSON>hart,
  Bar,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import {
  FileText,
  Plus,
  Play,
  Download,
  Share2,
  Copy,
  Edit,
  Trash2,
  Eye,
  Calendar,
  Filter,
  BarChart3,
  PieChart as PieChartIcon,
  Table,
  Settings,
  Clock,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Users,
  Target,
  Zap,
  RefreshCw
} from 'lucide-react';
import { 
  CustomReport, 
  ReportTemplate, 
  ReportField, 
  ReportExecution,
  ReportVisualization 
} from '@/services/customReportingService';

interface CustomReportingDashboardProps {
  className?: string;
}

const COLORS = ['#2563eb', '#dc2626', '#059669', '#d97706', '#7c3aed', '#db2777'];

export default function CustomReportingDashboard({ className }: CustomReportingDashboardProps) {
  const { user } = useAuth();
  const { tenantId } = useTenant();
  const [loading, setLoading] = useState(true);
  const [reports, setReports] = useState<CustomReport[]>([]);
  const [templates, setTemplates] = useState<ReportTemplate[]>([]);
  const [fields, setFields] = useState<ReportField[]>([]);
  const [executions, setExecutions] = useState<ReportExecution[]>([]);
  const [selectedReport, setSelectedReport] = useState<CustomReport | null>(null);
  const [selectedTab, setSelectedTab] = useState('reports');
  const [showReportBuilder, setShowReportBuilder] = useState(false);

  // Report Builder State
  const [reportBuilder, setReportBuilder] = useState({
    name: '',
    description: '',
    category: '',
    dataSources: [],
    selectedFields: [],
    filters: [],
    sorting: [],
    grouping: [],
    visualization: {
      type: 'table' as ReportVisualization['type'],
      config: {}
    }
  });

  useEffect(() => {
    if (user && tenantId) {
      loadReportingData();
    }
  }, [user, tenantId]);

  const loadReportingData = async () => {
    try {
      setLoading(true);
      const token = await user?.getIdToken();
      
      const [reportsRes, templatesRes, fieldsRes] = await Promise.all([
        fetch(`/api/reports?action=list&tenantId=${tenantId}`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        fetch(`/api/reports?action=templates`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        fetch(`/api/reports?action=fields&tenantId=${tenantId}`, {
          headers: { Authorization: `Bearer ${token}` }
        })
      ]);

      if (reportsRes.ok) {
        const reportsData = await reportsRes.json();
        setReports(reportsData);
      }

      if (templatesRes.ok) {
        const templatesData = await templatesRes.json();
        setTemplates(templatesData);
      }

      if (fieldsRes.ok) {
        const fieldsData = await fieldsRes.json();
        setFields(fieldsData);
      }
    } catch (error) {
      console.error('Error loading reporting data:', error);
      toast.error('Failed to load reporting data');
    } finally {
      setLoading(false);
    }
  };

  const handleExecuteReport = async (reportId: string) => {
    try {
      const token = await user?.getIdToken();
      const response = await fetch('/api/reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          action: 'execute',
          reportId,
          tenantId,
          parameters: {},
          executionType: 'manual'
        })
      });

      if (response.ok) {
        const execution = await response.json();
        toast.success('Report executed successfully');
        
        // Load executions for this report
        loadReportExecutions(reportId);
      } else {
        throw new Error('Failed to execute report');
      }
    } catch (error) {
      console.error('Error executing report:', error);
      toast.error('Failed to execute report');
    }
  };

  const loadReportExecutions = async (reportId: string) => {
    try {
      const firebaseUser = auth.currentUser;
      if (!firebaseUser) {
        throw new Error('User not authenticated');
      }
      const token = await firebaseUser.getIdToken();
      const response = await fetch(`/api/reports?action=executions&reportId=${reportId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.ok) {
        const executionsData = await response.json();
        setExecutions(executionsData);
      }
    } catch (error) {
      console.error('Error loading executions:', error);
    }
  };

  const handleCreateFromTemplate = async (templateId: string) => {
    try {
      const token = await user?.getIdToken();
      const response = await fetch('/api/reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          action: 'create-from-template',
          templateId,
          tenantId,
          customizations: {}
        })
      });

      if (response.ok) {
        const newReport = await response.json();
        toast.success('Report created from template');
        loadReportingData();
      } else {
        throw new Error('Failed to create report from template');
      }
    } catch (error) {
      console.error('Error creating report from template:', error);
      toast.error('Failed to create report from template');
    }
  };

  const handleExportReport = async (executionId: string, format: 'pdf' | 'excel' | 'csv' | 'json') => {
    try {
      const token = await user?.getIdToken();
      const response = await fetch(`/api/reports?action=export&executionId=${executionId}&format=${format}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.ok) {
        const { url } = await response.json();
        window.open(url, '_blank');
        toast.success(`Report exported as ${format.toUpperCase()}`);
      } else {
        throw new Error('Failed to export report');
      }
    } catch (error) {
      console.error('Error exporting report:', error);
      toast.error('Failed to export report');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { color: string; icon: any }> = {
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      running: { color: 'bg-blue-100 text-blue-800', icon: RefreshCw },
      completed: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      failed: { color: 'bg-red-100 text-red-800', icon: AlertTriangle },
      cancelled: { color: 'bg-gray-100 text-gray-800', icon: AlertTriangle }
    };

    const config = statusConfig[status] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={config.color}>
        <Icon className="h-3 w-3 mr-1" />
        {status.toUpperCase()}
      </Badge>
    );
  };

  const getVisualizationIcon = (type: ReportVisualization['type']) => {
    const iconMap = {
      table: Table,
      chart: BarChart3,
      pivot: Target,
      card: FileText,
      gauge: TrendingUp,
      map: Users
    };
    
    return iconMap[type] || Table;
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString();
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Prepare chart data
  const reportsByCategory = reports.reduce((acc, report) => {
    acc[report.category] = (acc[report.category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const categoryChartData = Object.entries(reportsByCategory).map(([category, count], index) => ({
    name: category,
    value: count,
    color: COLORS[index % COLORS.length]
  }));

  const executionTrends = executions.slice(0, 10).map(execution => ({
    date: formatDate(execution.startTime),
    duration: execution.executionTime || 0,
    status: execution.status
  }));

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Custom Reporting Engine</h2>
            <p className="text-gray-600">Build flexible reports with industry templates and custom formulas</p>
          </div>
          <div className="flex items-center gap-2">
            <Button onClick={() => setShowReportBuilder(true)}>
              <Plus className="h-4 w-4 mr-2" />
              New Report
            </Button>
            <Button variant="outline" onClick={loadReportingData}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Reports</p>
                  <p className="text-2xl font-bold">{reports.length}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Available Templates</p>
                  <p className="text-2xl font-bold">{templates.length}</p>
                </div>
                <Target className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Recent Executions</p>
                  <p className="text-2xl font-bold">{executions.length}</p>
                </div>
                <Zap className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Available Fields</p>
                  <p className="text-2xl font-bold">{fields.length}</p>
                </div>
                <Settings className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="reports">My Reports</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="executions">Executions</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="reports" className="space-y-4">
            <Card>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b">
                      <tr>
                        <th className="p-4 text-left font-medium text-gray-900">Report Name</th>
                        <th className="p-4 text-left font-medium text-gray-900">Category</th>
                        <th className="p-4 text-left font-medium text-gray-900">Visualization</th>
                        <th className="p-4 text-left font-medium text-gray-900">Last Executed</th>
                        <th className="p-4 text-left font-medium text-gray-900">Executions</th>
                        <th className="p-4 text-left font-medium text-gray-900">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {reports.map((report) => {
                        const VisualizationIcon = getVisualizationIcon(report.visualization.type);
                        return (
                          <tr key={report.id} className="hover:bg-gray-50">
                            <td className="p-4">
                              <div className="font-medium text-gray-900">{report.name}</div>
                              <div className="text-sm text-gray-500">{report.description}</div>
                            </td>
                            <td className="p-4">
                              <Badge variant="outline">{report.category}</Badge>
                            </td>
                            <td className="p-4">
                              <div className="flex items-center gap-2">
                                <VisualizationIcon className="h-4 w-4 text-gray-600" />
                                <span className="text-sm capitalize">{report.visualization.type}</span>
                              </div>
                            </td>
                            <td className="p-4">
                              <div className="text-sm text-gray-900">
                                {report.metadata.lastExecuted ? formatDate(report.metadata.lastExecuted) : 'Never'}
                              </div>
                            </td>
                            <td className="p-4">
                              <div className="text-sm text-gray-900">{report.metadata.executionCount}</div>
                            </td>
                            <td className="p-4">
                              <div className="flex items-center gap-1">
                                <Button 
                                  size="sm" 
                                  variant="ghost"
                                  onClick={() => handleExecuteReport(report.id)}
                                >
                                  <Play className="h-4 w-4" />
                                </Button>
                                <Button 
                                  size="sm" 
                                  variant="ghost"
                                  onClick={() => setSelectedReport(report)}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button size="sm" variant="ghost">
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button size="sm" variant="ghost">
                                  <Share2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="templates" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {templates.map((template) => (
                <Card key={template.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <Badge className="bg-blue-100 text-blue-800">{template.category}</Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-4">{template.description}</p>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <span>⭐ {template.rating}/5</span>
                      <span>{template.downloadCount} downloads</span>
                    </div>
                    
                    <Button 
                      className="w-full"
                      onClick={() => handleCreateFromTemplate(template.id)}
                    >
                      Use Template
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="executions" className="space-y-4">
            <Card>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b">
                      <tr>
                        <th className="p-4 text-left font-medium text-gray-900">Report</th>
                        <th className="p-4 text-left font-medium text-gray-900">Status</th>
                        <th className="p-4 text-left font-medium text-gray-900">Started</th>
                        <th className="p-4 text-left font-medium text-gray-900">Duration</th>
                        <th className="p-4 text-left font-medium text-gray-900">Rows</th>
                        <th className="p-4 text-left font-medium text-gray-900">Executed By</th>
                        <th className="p-4 text-left font-medium text-gray-900">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {executions.map((execution) => (
                        <tr key={execution.id} className="hover:bg-gray-50">
                          <td className="p-4">
                            <div className="font-medium text-gray-900">{execution.reportId}</div>
                            <div className="text-sm text-gray-500">{execution.executionType}</div>
                          </td>
                          <td className="p-4">
                            {getStatusBadge(execution.status)}
                          </td>
                          <td className="p-4">
                            <div className="text-sm text-gray-900">
                              {formatDate(execution.startTime)}
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="text-sm text-gray-900">
                              {execution.executionTime ? formatDuration(execution.executionTime) : '-'}
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="text-sm text-gray-900">
                              {execution.rowCount?.toLocaleString() || '-'}
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="text-sm text-gray-900">{execution.executedBy}</div>
                          </td>
                          <td className="p-4">
                            <div className="flex items-center gap-1">
                              {execution.status === 'completed' && (
                                <>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => handleExportReport(execution.id, 'pdf')}
                                  >
                                    <Download className="h-4 w-4" />
                                  </Button>
                                  <Button size="sm" variant="ghost">
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                </>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Reports by Category</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={categoryChartData}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        >
                          {categoryChartData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Execution Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={executionTrends}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="duration" fill="#2563eb" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Usage Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {reports.reduce((sum, r) => sum + r.metadata.executionCount, 0)}
                    </div>
                    <div className="text-sm text-gray-600">Total Executions</div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {executions.filter(e => e.status === 'completed').length}
                    </div>
                    <div className="text-sm text-gray-600">Successful Runs</div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {reports.filter(r => r.caching.enabled).length}
                    </div>
                    <div className="text-sm text-gray-600">Cached Reports</div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Report Builder Modal */}
        {showReportBuilder && (
          <Dialog open={showReportBuilder} onOpenChange={setShowReportBuilder}>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Report Builder</DialogTitle>
              </DialogHeader>
              <div className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="report-name">Report Name</Label>
                    <Input
                      id="report-name"
                      value={reportBuilder.name}
                      onChange={(e) => setReportBuilder(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter report name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="report-category">Category</Label>
                    <Select
                      value={reportBuilder.category}
                      onValueChange={(value) => setReportBuilder(prev => ({ ...prev, category: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="exhibition">Exhibition</SelectItem>
                        <SelectItem value="financial">Financial</SelectItem>
                        <SelectItem value="marketing">Marketing</SelectItem>
                        <SelectItem value="operations">Operations</SelectItem>
                        <SelectItem value="analytics">Analytics</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="report-description">Description</Label>
                  <Textarea
                    id="report-description"
                    value={reportBuilder.description}
                    onChange={(e) => setReportBuilder(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe what this report shows"
                    rows={3}
                  />
                </div>

                {/* Data Sources */}
                <div>
                  <Label>Data Sources</Label>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    {['exhibitions', 'events', 'leads', 'tasks', 'vendors'].map(source => (
                      <label key={source} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={reportBuilder.dataSources.includes(source)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setReportBuilder(prev => ({
                                ...prev,
                                dataSources: [...prev.dataSources, source]
                              }));
                            } else {
                              setReportBuilder(prev => ({
                                ...prev,
                                dataSources: prev.dataSources.filter(ds => ds !== source)
                              }));
                            }
                          }}
                        />
                        <span className="capitalize">{source}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Fields Selection */}
                <div>
                  <Label>Fields to Include</Label>
                  <div className="grid grid-cols-3 gap-2 mt-2 max-h-32 overflow-y-auto">
                    {fields.filter(field =>
                      reportBuilder.dataSources.includes(field.source)
                    ).map(field => (
                      <label key={field.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={reportBuilder.selectedFields.includes(field.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setReportBuilder(prev => ({
                                ...prev,
                                selectedFields: [...prev.selectedFields, field.id]
                              }));
                            } else {
                              setReportBuilder(prev => ({
                                ...prev,
                                selectedFields: prev.selectedFields.filter(f => f !== field.id)
                              }));
                            }
                          }}
                        />
                        <span className="text-sm">{field.displayName}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Visualization Type */}
                <div>
                  <Label>Visualization Type</Label>
                  <Select
                    value={reportBuilder.visualization.type}
                    onValueChange={(value: ReportVisualization['type']) =>
                      setReportBuilder(prev => ({
                        ...prev,
                        visualization: { ...prev.visualization, type: value }
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="table">Table</SelectItem>
                      <SelectItem value="chart">Chart</SelectItem>
                      <SelectItem value="pivot">Pivot Table</SelectItem>
                      <SelectItem value="card">Card</SelectItem>
                      <SelectItem value="gauge">Gauge</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowReportBuilder(false)}>
                    Cancel
                  </Button>
                  <Button onClick={() => {
                    // Handle report creation
                    toast.success('Report builder functionality coming soon!');
                    setShowReportBuilder(false);
                  }}>
                    Create Report
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}

        {/* Report Details Modal */}
        {selectedReport && (
          <Dialog open={!!selectedReport} onOpenChange={() => setSelectedReport(null)}>
            <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>{selectedReport.name}</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <p className="text-gray-600">{selectedReport.description}</p>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium mb-2">Report Details</h3>
                    <div className="space-y-1 text-sm">
                      <div>Category: {selectedReport.category}</div>
                      <div>Visualization: {selectedReport.visualization.type}</div>
                      <div>Fields: {selectedReport.fields.length}</div>
                      <div>Filters: {selectedReport.filters.length}</div>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium mb-2">Performance</h3>
                    <div className="space-y-1 text-sm">
                      <div>Executions: {selectedReport.metadata.executionCount}</div>
                      <div>Avg Duration: {selectedReport.metadata.avgExecutionTime ? formatDuration(selectedReport.metadata.avgExecutionTime) : 'N/A'}</div>
                      <div>Caching: {selectedReport.caching.enabled ? 'Enabled' : 'Disabled'}</div>
                      <div>Last Run: {selectedReport.metadata.lastExecuted ? formatDate(selectedReport.metadata.lastExecuted) : 'Never'}</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium mb-2">Data Sources</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedReport.dataSources.map(ds => (
                      <Badge key={ds.id} variant="outline">{ds.name}</Badge>
                    ))}
                  </div>
                </div>

                {selectedReport.tags.length > 0 && (
                  <div>
                    <h3 className="font-medium mb-2">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {selectedReport.tags.map(tag => (
                        <Badge key={tag} className="bg-blue-100 text-blue-800">{tag}</Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </div>
  );
}
