/**
 * Simple error handler for development
 * Logs errors without showing error boundaries
 */

export const logError = (error: any, context?: string) => {
  if (process.env.NODE_ENV === 'development') {
    console.group(`🚨 Error ${context ? `in ${context}` : ''}`);
    console.error('Error:', error);
    console.error('Message:', error?.message);
    console.error('Stack:', error?.stack);
    console.groupEnd();
  }
};

export const safeCall = <T extends (...args: any[]) => any>(
  fn: T | undefined,
  ...args: Parameters<T>
): ReturnType<T> | undefined => {
  try {
    if (typeof fn === 'function') {
      return fn(...args);
    }
    return undefined;
  } catch (error) {
    logError(error, 'safeCall');
    return undefined;
  }
};

export const safeAsyncCall = async <T extends (...args: any[]) => Promise<any>>(
  fn: T | undefined,
  ...args: Parameters<T>
): Promise<Awaited<ReturnType<T>> | undefined> => {
  try {
    if (typeof fn === 'function') {
      return await fn(...args);
    }
    return undefined;
  } catch (error) {
    logError(error, 'safeAsyncCall');
    return undefined;
  }
};

// Global error handler for unhandled errors
if (typeof window !== 'undefined') {
  window.addEventListener('error', (event) => {
    logError(event.error, 'Global Error Handler');
    // Prevent the error from bubbling up and causing a white screen
    event.preventDefault();
  });

  window.addEventListener('unhandledrejection', (event) => {
    logError(event.reason, 'Unhandled Promise Rejection');
    // Prevent the error from bubbling up
    event.preventDefault();
  });

  // Handle webpack module loading errors specifically
  const originalConsoleError = console.error;
  console.error = (...args) => {
    const message = args.join(' ');
    if (message.includes('Cannot read properties of undefined (reading \'call\')')) {
      logError(new Error('Webpack module loading error detected'), 'Module Loading Error');
      // Try to reload the page after a short delay
      setTimeout(() => {
        if (confirm('A module loading error occurred. Would you like to refresh the page?')) {
          window.location.reload();
        }
      }, 1000);
    }
    originalConsoleError.apply(console, args);
  };
}
