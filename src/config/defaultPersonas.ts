/**
 * Default Persona Templates
 * Predefined personas with module permissions for different user types
 */

import { 
  PersonaTemplate, 
  EVEXA_MODULES, 
  type EvexaModule,
  type ModulePermission,
  type PermissionAction 
} from '@/types/personas';

// ===== HELPER FUNCTIONS =====

function createModulePermission(
  module: EvexaModule, 
  actions: PermissionAction[], 
  description: string
): ModulePermission {
  return { module, actions, description };
}

function createFullAccess(modules: EvexaModule[]): ModulePermission[] {
  return modules.map(module => 
    createModulePermission(module, ['read', 'write', 'delete', 'admin'], `Full access to ${module}`)
  );
}

function createReadWriteAccess(modules: EvexaModule[]): ModulePermission[] {
  return modules.map(module => 
    createModulePermission(module, ['read', 'write'], `Read and write access to ${module}`)
  );
}

function createReadOnlyAccess(modules: EvexaModule[]): ModulePermission[] {
  return modules.map(module => 
    createModulePermission(module, ['read'], `Read-only access to ${module}`)
  );
}

// ===== DEFAULT PERSONA TEMPLATES =====

export const DEFAULT_PERSONAS: Record<string, PersonaTemplate> = {
  // ===== ADMIN PERSONA =====
  admin: {
    id: 'admin',
    name: 'Administrator',
    description: 'Full system access with all administrative privileges. Can manage users, settings, and all modules.',
    category: 'default',
    isActive: true,
    permissions: {
      modules: createFullAccess([
        // Core modules
        EVEXA_MODULES.EXHIBITIONS,
        EVEXA_MODULES.EVENTS,
        EVEXA_MODULES.TASKS,
        EVEXA_MODULES.LEADS,
        EVEXA_MODULES.CONTACTS,
        EVEXA_MODULES.CRM,
        
        // Financial modules
        EVEXA_MODULES.BUDGETS,
        EVEXA_MODULES.EXPENSES,
        EVEXA_MODULES.FINANCIAL_REPORTS,
        EVEXA_MODULES.PURCHASE_ORDERS,
        
        // Vendor & Logistics
        EVEXA_MODULES.VENDORS,
        EVEXA_MODULES.LOGISTICS,
        EVEXA_MODULES.SHIPPING,
        EVEXA_MODULES.INVENTORY,
        
        // Marketing
        EVEXA_MODULES.MARKETING,
        EVEXA_MODULES.SOCIAL_MEDIA,
        EVEXA_MODULES.EMAIL_CAMPAIGNS,
        EVEXA_MODULES.CONTENT_MANAGEMENT,
        
        // Analytics
        EVEXA_MODULES.ANALYTICS,
        EVEXA_MODULES.REPORTS,
        EVEXA_MODULES.DASHBOARDS,
        EVEXA_MODULES.PERFORMANCE,
        
        // Travel
        EVEXA_MODULES.TRAVEL,
        EVEXA_MODULES.ACCOMMODATION,
        EVEXA_MODULES.VISA_PASSPORT,
        
        // Admin
        EVEXA_MODULES.SETTINGS,
        EVEXA_MODULES.USER_MANAGEMENT,
        EVEXA_MODULES.INTEGRATIONS,
        EVEXA_MODULES.SECURITY,
        EVEXA_MODULES.SUPPORT,
        EVEXA_MODULES.DOCUMENTATION,
        EVEXA_MODULES.TRAINING
      ]),
      systemPermissions: {
        canManageUsers: true,
        canManageSettings: true,
        canViewAnalytics: true,
        canExportData: true,
        canManageIntegrations: true,
        canAccessSupport: true
      }
    },
    targetUsers: ['Tenant administrators', 'System administrators', 'IT managers'],
    createdAt: new Date(),
    updatedAt: new Date()
  },

  // ===== EXHIBITION MANAGER PERSONA =====
  exhibitionManager: {
    id: 'exhibition-manager',
    name: 'Exhibition Manager',
    description: 'Comprehensive exhibition management with full access to core exhibition features, events, tasks, and vendor coordination.',
    category: 'default',
    isActive: true,
    permissions: {
      modules: [
        // Full access to core exhibition modules
        ...createFullAccess([
          EVEXA_MODULES.EXHIBITIONS,
          EVEXA_MODULES.EVENTS,
          EVEXA_MODULES.TASKS,
          EVEXA_MODULES.VENDORS,
          EVEXA_MODULES.LOGISTICS
        ]),
        
        // Read/write access to supporting modules
        ...createReadWriteAccess([
          EVEXA_MODULES.LEADS,
          EVEXA_MODULES.CONTACTS,
          EVEXA_MODULES.BUDGETS,
          EVEXA_MODULES.EXPENSES,
          EVEXA_MODULES.TRAVEL,
          EVEXA_MODULES.ACCOMMODATION
        ]),
        
        // Read-only access to analytics and reports
        ...createReadOnlyAccess([
          EVEXA_MODULES.ANALYTICS,
          EVEXA_MODULES.REPORTS,
          EVEXA_MODULES.DASHBOARDS,
          EVEXA_MODULES.FINANCIAL_REPORTS
        ])
      ],
      systemPermissions: {
        canManageUsers: false,
        canManageSettings: false,
        canViewAnalytics: true,
        canExportData: true,
        canManageIntegrations: false,
        canAccessSupport: true
      }
    },
    targetUsers: ['Exhibition managers', 'Event coordinators', 'Project managers'],
    limitations: {
      maxExhibitions: 50,
      maxBudget: 1000000
    },
    createdAt: new Date(),
    updatedAt: new Date()
  },

  // ===== MARKETING SPECIALIST PERSONA =====
  marketingSpecialist: {
    id: 'marketing-specialist',
    name: 'Marketing Specialist',
    description: 'Focused on marketing activities, lead management, and promotional campaigns with access to marketing tools and analytics.',
    category: 'default',
    isActive: true,
    permissions: {
      modules: [
        // Full access to marketing modules
        ...createFullAccess([
          EVEXA_MODULES.MARKETING,
          EVEXA_MODULES.SOCIAL_MEDIA,
          EVEXA_MODULES.EMAIL_CAMPAIGNS,
          EVEXA_MODULES.CONTENT_MANAGEMENT,
          EVEXA_MODULES.LEADS,
          EVEXA_MODULES.CONTACTS,
          EVEXA_MODULES.CRM
        ]),
        
        // Read/write access to related modules
        ...createReadWriteAccess([
          EVEXA_MODULES.EVENTS,
          EVEXA_MODULES.ANALYTICS,
          EVEXA_MODULES.REPORTS
        ]),
        
        // Read-only access to exhibition data
        ...createReadOnlyAccess([
          EVEXA_MODULES.EXHIBITIONS,
          EVEXA_MODULES.TASKS,
          EVEXA_MODULES.DASHBOARDS
        ])
      ],
      systemPermissions: {
        canManageUsers: false,
        canManageSettings: false,
        canViewAnalytics: true,
        canExportData: true,
        canManageIntegrations: false,
        canAccessSupport: true
      }
    },
    targetUsers: ['Marketing specialists', 'Digital marketers', 'Content creators', 'Social media managers'],
    limitations: {
      maxLeads: 10000
    },
    createdAt: new Date(),
    updatedAt: new Date()
  },

  // ===== FINANCIAL CONTROLLER PERSONA =====
  financialController: {
    id: 'financial-controller',
    name: 'Financial Controller',
    description: 'Complete financial oversight with access to budgets, expenses, financial reports, and purchase order management.',
    category: 'default',
    isActive: true,
    permissions: {
      modules: [
        // Full access to financial modules
        ...createFullAccess([
          EVEXA_MODULES.BUDGETS,
          EVEXA_MODULES.EXPENSES,
          EVEXA_MODULES.FINANCIAL_REPORTS,
          EVEXA_MODULES.PURCHASE_ORDERS,
          EVEXA_MODULES.VENDORS
        ]),
        
        // Read/write access to analytics
        ...createReadWriteAccess([
          EVEXA_MODULES.ANALYTICS,
          EVEXA_MODULES.REPORTS,
          EVEXA_MODULES.DASHBOARDS
        ]),
        
        // Read-only access to operational modules
        ...createReadOnlyAccess([
          EVEXA_MODULES.EXHIBITIONS,
          EVEXA_MODULES.EVENTS,
          EVEXA_MODULES.TASKS,
          EVEXA_MODULES.LOGISTICS,
          EVEXA_MODULES.TRAVEL,
          EVEXA_MODULES.ACCOMMODATION
        ])
      ],
      systemPermissions: {
        canManageUsers: false,
        canManageSettings: false,
        canViewAnalytics: true,
        canExportData: true,
        canManageIntegrations: false,
        canAccessSupport: true
      }
    },
    targetUsers: ['Financial controllers', 'Accountants', 'Budget managers', 'Finance directors'],
    createdAt: new Date(),
    updatedAt: new Date()
  },

  // ===== LOGISTICS COORDINATOR PERSONA =====
  logisticsCoordinator: {
    id: 'logistics-coordinator',
    name: 'Logistics Coordinator',
    description: 'Specialized in logistics, shipping, inventory, and vendor management with travel coordination capabilities.',
    category: 'default',
    isActive: true,
    permissions: {
      modules: [
        // Full access to logistics modules
        ...createFullAccess([
          EVEXA_MODULES.LOGISTICS,
          EVEXA_MODULES.SHIPPING,
          EVEXA_MODULES.INVENTORY,
          EVEXA_MODULES.VENDORS,
          EVEXA_MODULES.TRAVEL,
          EVEXA_MODULES.ACCOMMODATION,
          EVEXA_MODULES.VISA_PASSPORT
        ]),
        
        // Read/write access to related modules
        ...createReadWriteAccess([
          EVEXA_MODULES.TASKS,
          EVEXA_MODULES.PURCHASE_ORDERS,
          EVEXA_MODULES.EXPENSES
        ]),
        
        // Read-only access to planning modules
        ...createReadOnlyAccess([
          EVEXA_MODULES.EXHIBITIONS,
          EVEXA_MODULES.EVENTS,
          EVEXA_MODULES.BUDGETS,
          EVEXA_MODULES.REPORTS
        ])
      ],
      systemPermissions: {
        canManageUsers: false,
        canManageSettings: false,
        canViewAnalytics: false,
        canExportData: true,
        canManageIntegrations: false,
        canAccessSupport: true
      }
    },
    targetUsers: ['Logistics coordinators', 'Shipping managers', 'Inventory managers', 'Travel coordinators'],
    createdAt: new Date(),
    updatedAt: new Date()
  },

  // ===== BASIC USER PERSONA =====
  basicUser: {
    id: 'basic-user',
    name: 'Basic User',
    description: 'Limited access for general users with read-only access to most modules and basic task management.',
    category: 'default',
    isActive: true,
    permissions: {
      modules: [
        // Read/write access to personal tasks
        ...createReadWriteAccess([
          EVEXA_MODULES.TASKS
        ]),
        
        // Read-only access to core modules
        ...createReadOnlyAccess([
          EVEXA_MODULES.EXHIBITIONS,
          EVEXA_MODULES.EVENTS,
          EVEXA_MODULES.LEADS,
          EVEXA_MODULES.CONTACTS,
          EVEXA_MODULES.VENDORS,
          EVEXA_MODULES.DASHBOARDS,
          EVEXA_MODULES.SUPPORT,
          EVEXA_MODULES.DOCUMENTATION
        ])
      ],
      systemPermissions: {
        canManageUsers: false,
        canManageSettings: false,
        canViewAnalytics: false,
        canExportData: false,
        canManageIntegrations: false,
        canAccessSupport: true
      }
    },
    targetUsers: ['General staff', 'Temporary workers', 'Interns', 'External collaborators'],
    limitations: {
      maxExhibitions: 5,
      maxLeads: 100
    },
    createdAt: new Date(),
    updatedAt: new Date()
  }
};

// ===== PERSONA CATEGORIES =====

export const PERSONA_CATEGORIES = {
  MANAGEMENT: {
    id: 'management',
    name: 'Management',
    description: 'Leadership and management roles',
    personas: ['admin', 'exhibition-manager']
  },
  SPECIALIST: {
    id: 'specialist',
    name: 'Specialist',
    description: 'Specialized functional roles',
    personas: ['marketing-specialist', 'financial-controller', 'logistics-coordinator']
  },
  GENERAL: {
    id: 'general',
    name: 'General',
    description: 'General access roles',
    personas: ['basic-user']
  }
} as const;

// ===== UTILITY FUNCTIONS =====

/**
 * Get persona by ID
 */
export function getPersonaById(personaId: string): PersonaTemplate | null {
  return DEFAULT_PERSONAS[personaId] || null;
}

/**
 * Get personas by category
 */
export function getPersonasByCategory(category: keyof typeof PERSONA_CATEGORIES): PersonaTemplate[] {
  const categoryConfig = PERSONA_CATEGORIES[category];
  return categoryConfig.personas.map(id => DEFAULT_PERSONAS[id]).filter(Boolean);
}

/**
 * Get all default personas
 */
export function getAllDefaultPersonas(): PersonaTemplate[] {
  return Object.values(DEFAULT_PERSONAS);
}

/**
 * Check if persona has access to module
 */
export function personaHasModuleAccess(
  persona: PersonaTemplate, 
  module: EvexaModule, 
  action: PermissionAction
): boolean {
  const modulePermission = persona.permissions.modules.find(m => m.module === module);
  return modulePermission ? modulePermission.actions.includes(action) : false;
}

/**
 * Get persona's module permissions
 */
export function getPersonaModulePermissions(
  persona: PersonaTemplate, 
  module: EvexaModule
): PermissionAction[] {
  const modulePermission = persona.permissions.modules.find(m => m.module === module);
  return modulePermission ? modulePermission.actions : [];
}

/**
 * Get modules accessible by persona
 */
export function getPersonaAccessibleModules(persona: PersonaTemplate): EvexaModule[] {
  return persona.permissions.modules.map(m => m.module as EvexaModule);
}
