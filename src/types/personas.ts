/**
 * Persona and Permission Types
 * Defines the structure for user personas and module-based permissions
 */

// ===== CORE PERMISSION TYPES =====

export type PermissionAction = 'read' | 'write' | 'delete' | 'admin';

export interface ModulePermission {
  module: string;
  actions: PermissionAction[];
  description: string;
}

export interface PersonaPermissions {
  modules: ModulePermission[];
  systemPermissions: {
    canManageUsers: boolean;
    canManageSettings: boolean;
    canViewAnalytics: boolean;
    canExportData: boolean;
    canManageIntegrations: boolean;
    canAccessSupport: boolean;
  };
}

// ===== PERSONA DEFINITION =====

export interface PersonaTemplate {
  id: string;
  name: string;
  description: string;
  category: 'default' | 'custom';
  isActive: boolean;
  permissions: PersonaPermissions;
  targetUsers: string[];
  limitations?: {
    maxExhibitions?: number;
    maxLeads?: number;
    maxBudget?: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface TenantPersona extends PersonaTemplate {
  tenantId: string;
  customizations?: {
    moduleVisibility?: Record<string, boolean>;
    featureFlags?: Record<string, boolean>;
    customFields?: Record<string, any>;
  };
}

// ===== MODULE DEFINITIONS =====

export const EVEXA_MODULES = {
  // Core Exhibition Management
  EXHIBITIONS: 'exhibitions',
  EVENTS: 'events', 
  TASKS: 'tasks',
  
  // Lead & Contact Management
  LEADS: 'leads',
  CONTACTS: 'contacts',
  CRM: 'crm',
  
  // Financial Management
  BUDGETS: 'budgets',
  EXPENSES: 'expenses',
  FINANCIAL_REPORTS: 'financial_reports',
  PURCHASE_ORDERS: 'purchase_orders',
  
  // Vendor & Logistics
  VENDORS: 'vendors',
  LOGISTICS: 'logistics',
  SHIPPING: 'shipping',
  INVENTORY: 'inventory',
  
  // Marketing & Communications
  MARKETING: 'marketing',
  SOCIAL_MEDIA: 'social_media',
  EMAIL_CAMPAIGNS: 'email_campaigns',
  CONTENT_MANAGEMENT: 'content_management',
  
  // Analytics & Reporting
  ANALYTICS: 'analytics',
  REPORTS: 'reports',
  DASHBOARDS: 'dashboards',
  PERFORMANCE: 'performance',
  
  // Travel & Accommodation
  TRAVEL: 'travel',
  ACCOMMODATION: 'accommodation',
  VISA_PASSPORT: 'visa_passport',
  
  // Settings & Administration
  SETTINGS: 'settings',
  USER_MANAGEMENT: 'user_management',
  INTEGRATIONS: 'integrations',
  SECURITY: 'security',
  
  // Support & Documentation
  SUPPORT: 'support',
  DOCUMENTATION: 'documentation',
  TRAINING: 'training'
} as const;

export type EvexaModule = typeof EVEXA_MODULES[keyof typeof EVEXA_MODULES];

// ===== MODULE METADATA =====

export interface ModuleMetadata {
  id: EvexaModule;
  name: string;
  description: string;
  category: 'core' | 'financial' | 'marketing' | 'logistics' | 'analytics' | 'admin';
  icon: string;
  color: string;
  dependencies?: EvexaModule[];
  isPremium: boolean;
  minimumPlan: 'basic' | 'professional' | 'enterprise';
}

export const MODULE_METADATA: Record<EvexaModule, ModuleMetadata> = {
  // Core Exhibition Management
  [EVEXA_MODULES.EXHIBITIONS]: {
    id: EVEXA_MODULES.EXHIBITIONS,
    name: 'Exhibitions',
    description: 'Manage exhibitions, venues, and event planning',
    category: 'core',
    icon: 'calendar',
    color: 'blue',
    isPremium: false,
    minimumPlan: 'basic'
  },
  [EVEXA_MODULES.EVENTS]: {
    id: EVEXA_MODULES.EVENTS,
    name: 'Events',
    description: 'Schedule and manage exhibition events',
    category: 'core',
    icon: 'clock',
    color: 'green',
    dependencies: [EVEXA_MODULES.EXHIBITIONS],
    isPremium: false,
    minimumPlan: 'basic'
  },
  [EVEXA_MODULES.TASKS]: {
    id: EVEXA_MODULES.TASKS,
    name: 'Tasks',
    description: 'Task management and assignment',
    category: 'core',
    icon: 'check-square',
    color: 'purple',
    isPremium: false,
    minimumPlan: 'basic'
  },
  
  // Lead & Contact Management
  [EVEXA_MODULES.LEADS]: {
    id: EVEXA_MODULES.LEADS,
    name: 'Leads',
    description: 'Lead capture and management',
    category: 'core',
    icon: 'users',
    color: 'orange',
    isPremium: false,
    minimumPlan: 'basic'
  },
  [EVEXA_MODULES.CONTACTS]: {
    id: EVEXA_MODULES.CONTACTS,
    name: 'Contacts',
    description: 'Contact database and relationship management',
    category: 'core',
    icon: 'address-book',
    color: 'teal',
    dependencies: [EVEXA_MODULES.LEADS],
    isPremium: false,
    minimumPlan: 'professional'
  },
  [EVEXA_MODULES.CRM]: {
    id: EVEXA_MODULES.CRM,
    name: 'CRM',
    description: 'Customer relationship management',
    category: 'core',
    icon: 'heart',
    color: 'red',
    dependencies: [EVEXA_MODULES.LEADS, EVEXA_MODULES.CONTACTS],
    isPremium: true,
    minimumPlan: 'professional'
  },
  
  // Financial Management
  [EVEXA_MODULES.BUDGETS]: {
    id: EVEXA_MODULES.BUDGETS,
    name: 'Budgets',
    description: 'Budget planning and allocation',
    category: 'financial',
    icon: 'dollar-sign',
    color: 'green',
    isPremium: false,
    minimumPlan: 'basic'
  },
  [EVEXA_MODULES.EXPENSES]: {
    id: EVEXA_MODULES.EXPENSES,
    name: 'Expenses',
    description: 'Expense tracking and approval',
    category: 'financial',
    icon: 'receipt',
    color: 'yellow',
    dependencies: [EVEXA_MODULES.BUDGETS],
    isPremium: false,
    minimumPlan: 'basic'
  },
  [EVEXA_MODULES.FINANCIAL_REPORTS]: {
    id: EVEXA_MODULES.FINANCIAL_REPORTS,
    name: 'Financial Reports',
    description: 'Financial reporting and analysis',
    category: 'financial',
    icon: 'chart-line',
    color: 'blue',
    dependencies: [EVEXA_MODULES.BUDGETS, EVEXA_MODULES.EXPENSES],
    isPremium: true,
    minimumPlan: 'professional'
  },
  [EVEXA_MODULES.PURCHASE_ORDERS]: {
    id: EVEXA_MODULES.PURCHASE_ORDERS,
    name: 'Purchase Orders',
    description: 'Purchase order management',
    category: 'financial',
    icon: 'shopping-cart',
    color: 'indigo',
    dependencies: [EVEXA_MODULES.VENDORS],
    isPremium: true,
    minimumPlan: 'professional'
  },
  
  // Vendor & Logistics
  [EVEXA_MODULES.VENDORS]: {
    id: EVEXA_MODULES.VENDORS,
    name: 'Vendors',
    description: 'Vendor management and relationships',
    category: 'logistics',
    icon: 'truck',
    color: 'gray',
    isPremium: false,
    minimumPlan: 'basic'
  },
  [EVEXA_MODULES.LOGISTICS]: {
    id: EVEXA_MODULES.LOGISTICS,
    name: 'Logistics',
    description: 'Logistics planning and coordination',
    category: 'logistics',
    icon: 'map',
    color: 'brown',
    dependencies: [EVEXA_MODULES.VENDORS],
    isPremium: true,
    minimumPlan: 'professional'
  },
  [EVEXA_MODULES.SHIPPING]: {
    id: EVEXA_MODULES.SHIPPING,
    name: 'Shipping',
    description: 'Shipping and freight management',
    category: 'logistics',
    icon: 'package',
    color: 'orange',
    dependencies: [EVEXA_MODULES.LOGISTICS],
    isPremium: true,
    minimumPlan: 'professional'
  },
  [EVEXA_MODULES.INVENTORY]: {
    id: EVEXA_MODULES.INVENTORY,
    name: 'Inventory',
    description: 'Inventory and asset management',
    category: 'logistics',
    icon: 'boxes',
    color: 'cyan',
    isPremium: true,
    minimumPlan: 'professional'
  },
  
  // Marketing & Communications
  [EVEXA_MODULES.MARKETING]: {
    id: EVEXA_MODULES.MARKETING,
    name: 'Marketing',
    description: 'Marketing campaign management',
    category: 'marketing',
    icon: 'megaphone',
    color: 'pink',
    isPremium: true,
    minimumPlan: 'professional'
  },
  [EVEXA_MODULES.SOCIAL_MEDIA]: {
    id: EVEXA_MODULES.SOCIAL_MEDIA,
    name: 'Social Media',
    description: 'Social media management',
    category: 'marketing',
    icon: 'share-2',
    color: 'blue',
    dependencies: [EVEXA_MODULES.MARKETING],
    isPremium: true,
    minimumPlan: 'professional'
  },
  [EVEXA_MODULES.EMAIL_CAMPAIGNS]: {
    id: EVEXA_MODULES.EMAIL_CAMPAIGNS,
    name: 'Email Campaigns',
    description: 'Email marketing and campaigns',
    category: 'marketing',
    icon: 'mail',
    color: 'green',
    dependencies: [EVEXA_MODULES.MARKETING, EVEXA_MODULES.LEADS],
    isPremium: true,
    minimumPlan: 'professional'
  },
  [EVEXA_MODULES.CONTENT_MANAGEMENT]: {
    id: EVEXA_MODULES.CONTENT_MANAGEMENT,
    name: 'Content Management',
    description: 'Content creation and management',
    category: 'marketing',
    icon: 'edit',
    color: 'purple',
    dependencies: [EVEXA_MODULES.MARKETING],
    isPremium: true,
    minimumPlan: 'professional'
  },
  
  // Analytics & Reporting
  [EVEXA_MODULES.ANALYTICS]: {
    id: EVEXA_MODULES.ANALYTICS,
    name: 'Analytics',
    description: 'Data analytics and insights',
    category: 'analytics',
    icon: 'bar-chart',
    color: 'blue',
    isPremium: true,
    minimumPlan: 'professional'
  },
  [EVEXA_MODULES.REPORTS]: {
    id: EVEXA_MODULES.REPORTS,
    name: 'Reports',
    description: 'Custom reporting and exports',
    category: 'analytics',
    icon: 'file-text',
    color: 'gray',
    isPremium: true,
    minimumPlan: 'professional'
  },
  [EVEXA_MODULES.DASHBOARDS]: {
    id: EVEXA_MODULES.DASHBOARDS,
    name: 'Dashboards',
    description: 'Custom dashboards and widgets',
    category: 'analytics',
    icon: 'layout',
    color: 'indigo',
    dependencies: [EVEXA_MODULES.ANALYTICS],
    isPremium: true,
    minimumPlan: 'professional'
  },
  [EVEXA_MODULES.PERFORMANCE]: {
    id: EVEXA_MODULES.PERFORMANCE,
    name: 'Performance',
    description: 'Performance tracking and KPIs',
    category: 'analytics',
    icon: 'trending-up',
    color: 'green',
    dependencies: [EVEXA_MODULES.ANALYTICS],
    isPremium: true,
    minimumPlan: 'enterprise'
  },
  
  // Travel & Accommodation
  [EVEXA_MODULES.TRAVEL]: {
    id: EVEXA_MODULES.TRAVEL,
    name: 'Travel',
    description: 'Travel planning and booking',
    category: 'logistics',
    icon: 'plane',
    color: 'sky',
    isPremium: true,
    minimumPlan: 'professional'
  },
  [EVEXA_MODULES.ACCOMMODATION]: {
    id: EVEXA_MODULES.ACCOMMODATION,
    name: 'Accommodation',
    description: 'Hotel and accommodation management',
    category: 'logistics',
    icon: 'home',
    color: 'amber',
    dependencies: [EVEXA_MODULES.TRAVEL],
    isPremium: true,
    minimumPlan: 'professional'
  },
  [EVEXA_MODULES.VISA_PASSPORT]: {
    id: EVEXA_MODULES.VISA_PASSPORT,
    name: 'Visa & Passport',
    description: 'Visa and passport management',
    category: 'logistics',
    icon: 'credit-card',
    color: 'red',
    dependencies: [EVEXA_MODULES.TRAVEL],
    isPremium: true,
    minimumPlan: 'professional'
  },
  
  // Settings & Administration
  [EVEXA_MODULES.SETTINGS]: {
    id: EVEXA_MODULES.SETTINGS,
    name: 'Settings',
    description: 'System settings and configuration',
    category: 'admin',
    icon: 'settings',
    color: 'gray',
    isPremium: false,
    minimumPlan: 'basic'
  },
  [EVEXA_MODULES.USER_MANAGEMENT]: {
    id: EVEXA_MODULES.USER_MANAGEMENT,
    name: 'User Management',
    description: 'User and permission management',
    category: 'admin',
    icon: 'users',
    color: 'blue',
    isPremium: false,
    minimumPlan: 'basic'
  },
  [EVEXA_MODULES.INTEGRATIONS]: {
    id: EVEXA_MODULES.INTEGRATIONS,
    name: 'Integrations',
    description: 'Third-party integrations',
    category: 'admin',
    icon: 'link',
    color: 'green',
    isPremium: true,
    minimumPlan: 'professional'
  },
  [EVEXA_MODULES.SECURITY]: {
    id: EVEXA_MODULES.SECURITY,
    name: 'Security',
    description: 'Security settings and audit logs',
    category: 'admin',
    icon: 'shield',
    color: 'red',
    isPremium: true,
    minimumPlan: 'enterprise'
  },
  
  // Support & Documentation
  [EVEXA_MODULES.SUPPORT]: {
    id: EVEXA_MODULES.SUPPORT,
    name: 'Support',
    description: 'Help and support resources',
    category: 'admin',
    icon: 'help-circle',
    color: 'blue',
    isPremium: false,
    minimumPlan: 'basic'
  },
  [EVEXA_MODULES.DOCUMENTATION]: {
    id: EVEXA_MODULES.DOCUMENTATION,
    name: 'Documentation',
    description: 'User guides and documentation',
    category: 'admin',
    icon: 'book',
    color: 'green',
    isPremium: false,
    minimumPlan: 'basic'
  },
  [EVEXA_MODULES.TRAINING]: {
    id: EVEXA_MODULES.TRAINING,
    name: 'Training',
    description: 'Training materials and courses',
    category: 'admin',
    icon: 'graduation-cap',
    color: 'purple',
    isPremium: true,
    minimumPlan: 'professional'
  }
};

// ===== UTILITY TYPES =====

export interface PersonaAssignment {
  userId: string;
  personaId: string;
  tenantId: string;
  assignedBy: string;
  assignedAt: Date;
  isActive: boolean;
}

export interface ModuleAccess {
  module: EvexaModule;
  hasAccess: boolean;
  permissions: PermissionAction[];
  reason?: 'persona' | 'subscription' | 'custom';
}
