/**
 * SendGrid Webhook Handler
 * Handles email events from SendGrid (opens, clicks, bounces, etc.)
 */

import { NextRequest, NextResponse } from 'next/server';
import { updateDoc, doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import crypto from 'crypto';

// ===== TYPES =====

interface SendGridEvent {
  email: string;
  timestamp: number;
  event: 'delivered' | 'open' | 'click' | 'bounce' | 'dropped' | 'deferred' | 'processed' | 'spam_report' | 'unsubscribe' | 'group_unsubscribe' | 'group_resubscribe';
  sg_event_id: string;
  sg_message_id: string;
  useragent?: string;
  ip?: string;
  url?: string;
  reason?: string;
  status?: string;
  response?: string;
  attempt?: string;
  category?: string[];
  asm_group_id?: number;
  unique_args?: Record<string, string>;
}

// ===== WEBHOOK VERIFICATION =====

function verifyWebhookSignature(
  payload: string,
  signature: string,
  timestamp: string,
  publicKey: string
): boolean {
  try {
    // SendGrid webhook signature verification
    const timestampPayload = timestamp + payload;
    const expectedSignature = crypto
      .createHmac('sha256', publicKey)
      .update(timestampPayload)
      .digest('base64');

    return crypto.timingSafeEqual(
      Buffer.from(signature, 'base64'),
      Buffer.from(expectedSignature, 'base64')
    );
  } catch (error) {
    console.error('Error verifying webhook signature:', error);
    return false;
  }
}

// ===== EVENT HANDLERS =====

async function handleEmailEvent(event: SendGridEvent): Promise<void> {
  try {
    const { unique_args, sg_message_id, email, timestamp, event: eventType } = event;
    
    // Extract tenant ID and other metadata from unique_args
    const tenantId = unique_args?.tenantId;
    const messageId = unique_args?.messageId || sg_message_id;

    if (!tenantId || !messageId) {
      console.warn('Missing tenantId or messageId in webhook event:', event);
      return;
    }

    // Find the email analytics record
    const analyticsRef = doc(db, COLLECTIONS.EMAIL_ANALYTICS, messageId);
    const analyticsDoc = await getDoc(analyticsRef);

    if (!analyticsDoc.exists()) {
      console.warn('Email analytics record not found:', messageId);
      return;
    }

    const analyticsData = analyticsDoc.data();
    const eventDate = new Date(timestamp * 1000);

    // Update analytics based on event type
    const updates: any = {
      updatedAt: new Date()
    };

    switch (eventType) {
      case 'delivered':
        updates.status = 'delivered';
        updates.deliveredAt = eventDate;
        break;

      case 'open':
        updates.status = 'opened';
        updates.openedAt = eventDate;
        if (event.useragent) updates.userAgent = event.useragent;
        if (event.ip) updates.ipAddress = event.ip;
        break;

      case 'click':
        updates.status = 'clicked';
        updates.clickedAt = eventDate;
        if (event.url) updates.clickedUrl = event.url;
        if (event.useragent) updates.userAgent = event.useragent;
        if (event.ip) updates.ipAddress = event.ip;
        break;

      case 'bounce':
        updates.status = 'bounced';
        updates.bouncedAt = eventDate;
        if (event.reason) updates.bounceReason = event.reason;
        if (event.response) updates.bounceResponse = event.response;
        break;

      case 'dropped':
        updates.status = 'failed';
        updates.failedAt = eventDate;
        if (event.reason) updates.errorMessage = event.reason;
        break;

      case 'spam_report':
        updates.status = 'spam_reported';
        updates.spamReportedAt = eventDate;
        break;

      case 'unsubscribe':
        updates.unsubscribedAt = eventDate;
        break;

      default:
        console.log('Unhandled event type:', eventType);
        return;
    }

    // Update the analytics record
    await updateDoc(analyticsRef, updates);

    // Log the event for debugging
    console.log(`Processed ${eventType} event for ${email} (${messageId})`);

  } catch (error) {
    console.error('Error handling email event:', error);
    throw error;
  }
}

// ===== API ROUTE HANDLER =====

export async function POST(request: NextRequest) {
  try {
    // Get the raw body
    const body = await request.text();
    
    // Get headers for signature verification
    const signature = request.headers.get('x-twilio-email-event-webhook-signature');
    const timestamp = request.headers.get('x-twilio-email-event-webhook-timestamp');

    // Verify webhook signature (optional but recommended for production)
    if (process.env.SENDGRID_WEBHOOK_PUBLIC_KEY && signature && timestamp) {
      const isValid = verifyWebhookSignature(
        body,
        signature,
        timestamp,
        process.env.SENDGRID_WEBHOOK_PUBLIC_KEY
      );

      if (!isValid) {
        console.error('Invalid webhook signature');
        return NextResponse.json(
          { error: 'Invalid signature' },
          { status: 401 }
        );
      }
    }

    // Parse the events
    const events: SendGridEvent[] = JSON.parse(body);

    // Process each event
    const results = await Promise.allSettled(
      events.map(event => handleEmailEvent(event))
    );

    // Log any failures
    const failures = results.filter(result => result.status === 'rejected');
    if (failures.length > 0) {
      console.error('Some events failed to process:', failures);
    }

    // Return success response
    return NextResponse.json({
      success: true,
      processed: events.length,
      failures: failures.length
    });

  } catch (error) {
    console.error('Webhook processing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// ===== HEALTH CHECK =====

export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    service: 'sendgrid-webhook',
    timestamp: new Date().toISOString()
  });
}
