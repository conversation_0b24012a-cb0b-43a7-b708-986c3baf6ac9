/**
 * Comprehensive Security Tests
 * Automated security testing suite for CI/CD pipeline
 */

import {
  testTenantDataIsolation,
  testCrossTenantQueryPrevention,
  testFirestoreSecurityRules,
  testPersonaPermissions,
  testSessionManagement,
  testAPIEndpointSecurity,
  testDataEncryption,
  testSubscriptionLimitSecurity,
  runAllSecurityTests,
  generateSecurityReport
} from '@/lib/security-validation';
import { tenantIsolationTestService } from '@/services/tenantIsolationTestService';
import { securityAuditService } from '@/services/securityAuditService';

// Mock Firebase and authentication
jest.mock('@/lib/firebase', () => ({
  db: {},
  auth: {}
}));

jest.mock('firebase/firestore', () => ({
  collection: jest.fn(),
  doc: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  getDocs: jest.fn().mockResolvedValue({ docs: [], size: 0 }),
  getDoc: jest.fn().mockResolvedValue({ exists: () => false }),
  addDoc: jest.fn().mockResolvedValue({ id: 'test-doc-id' }),
  updateDoc: jest.fn(),
  deleteDoc: jest.fn(),
  limit: jest.fn(),
  orderBy: jest.fn(),
  writeBatch: jest.fn().mockReturnValue({
    delete: jest.fn(),
    commit: jest.fn().mockResolvedValue(undefined)
  }),
  serverTimestamp: jest.fn()
}));

jest.mock('@/lib/auth-utils', () => ({
  getCurrentUser: jest.fn().mockResolvedValue({
    id: 'test-user-id',
    email: '<EMAIL>',
    tenantId: 'test-tenant-id',
    role: 'admin',
    isEmailVerified: true,
    lastLoginAt: new Date(),
    persona: {
      permissions: {
        modules: ['exhibitions', 'tasks', 'users']
      }
    }
  }),
  getCurrentTenant: jest.fn().mockResolvedValue({
    id: 'test-tenant-id',
    name: 'Test Tenant',
    subscription: {
      plan: 'professional',
      limits: {
        users: 10,
        exhibitions: 50,
        storage: 1000
      }
    }
  })
}));

describe('Comprehensive Security Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Tenant Data Isolation', () => {
    it('should pass tenant data isolation test', async () => {
      const result = await testTenantDataIsolation();
      
      expect(result).toBeDefined();
      expect(result.testId).toBe('tenant_data_isolation');
      expect(typeof result.passed).toBe('boolean');
      expect(typeof result.duration).toBe('number');
      
      if (!result.passed) {
        console.warn('Tenant data isolation test failed:', result.error);
      }
    });

    it('should prevent cross-tenant queries', async () => {
      const result = await testCrossTenantQueryPrevention();
      
      expect(result).toBeDefined();
      expect(result.testId).toBe('cross_tenant_queries');
      expect(typeof result.passed).toBe('boolean');
      
      if (!result.passed) {
        console.warn('Cross-tenant query prevention test failed:', result.error);
      }
    });

    it('should run comprehensive tenant isolation tests', async () => {
      const results = await tenantIsolationTestService.runAllTests();
      
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBeGreaterThan(0);
      
      results.forEach(result => {
        expect(result).toHaveProperty('testName');
        expect(result).toHaveProperty('passed');
        expect(result).toHaveProperty('duration');
        expect(result).toHaveProperty('severity');
        
        if (!result.passed) {
          console.warn(`Tenant isolation test failed: ${result.testName}`, result.error);
        }
      });
    });
  });

  describe('Authentication & Authorization', () => {
    it('should validate Firestore security rules', async () => {
      const result = await testFirestoreSecurityRules();
      
      expect(result).toBeDefined();
      expect(result.testId).toBe('firestore_security_rules');
      expect(typeof result.passed).toBe('boolean');
      
      if (!result.passed) {
        console.warn('Firestore security rules test failed:', result.error);
      }
    });

    it('should validate persona permissions', async () => {
      const result = await testPersonaPermissions();
      
      expect(result).toBeDefined();
      expect(result.testId).toBe('persona_permissions');
      expect(typeof result.passed).toBe('boolean');
      
      if (!result.passed) {
        console.warn('Persona permissions test failed:', result.error);
      }
    });

    it('should validate session management', async () => {
      const result = await testSessionManagement();
      
      expect(result).toBeDefined();
      expect(result.testId).toBe('session_management');
      expect(typeof result.passed).toBe('boolean');
      
      if (!result.passed) {
        console.warn('Session management test failed:', result.error);
      }
    });
  });

  describe('Data Security', () => {
    it('should validate API endpoint security', async () => {
      // Mock fetch for API endpoint testing
      global.fetch = jest.fn().mockResolvedValue({
        ok: false,
        status: 401
      });

      const result = await testAPIEndpointSecurity();
      
      expect(result).toBeDefined();
      expect(result.testId).toBe('api_endpoint_security');
      expect(typeof result.passed).toBe('boolean');
      
      if (!result.passed) {
        console.warn('API endpoint security test failed:', result.error);
      }
    });

    it('should validate data encryption', async () => {
      const result = await testDataEncryption();
      
      expect(result).toBeDefined();
      expect(result.testId).toBe('data_encryption');
      expect(typeof result.passed).toBe('boolean');
      
      if (!result.passed) {
        console.warn('Data encryption test failed:', result.error);
      }
    });

    it('should validate subscription limit security', async () => {
      const result = await testSubscriptionLimitSecurity();
      
      expect(result).toBeDefined();
      expect(result.testId).toBe('subscription_limit_security');
      expect(typeof result.passed).toBe('boolean');
      
      if (!result.passed) {
        console.warn('Subscription limit security test failed:', result.error);
      }
    });
  });

  describe('Comprehensive Security Testing', () => {
    it('should run all security tests successfully', async () => {
      const results = await runAllSecurityTests();
      
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBeGreaterThan(0);
      
      // Check that all expected tests are included
      const testIds = results.map(r => r.testId);
      expect(testIds).toContain('tenant_data_isolation');
      expect(testIds).toContain('cross_tenant_queries');
      expect(testIds).toContain('firestore_security_rules');
      expect(testIds).toContain('persona_permissions');
      expect(testIds).toContain('session_management');
      expect(testIds).toContain('api_endpoint_security');
      expect(testIds).toContain('data_encryption');
      expect(testIds).toContain('subscription_limit_security');
      
      // Log failed tests
      const failedTests = results.filter(r => !r.passed);
      if (failedTests.length > 0) {
        console.warn(`${failedTests.length} security tests failed:`, failedTests);
      }
      
      // Calculate pass rate
      const passRate = (results.filter(r => r.passed).length / results.length) * 100;
      console.log(`Security test pass rate: ${passRate.toFixed(1)}%`);
      
      // Ensure minimum pass rate (adjust threshold as needed)
      expect(passRate).toBeGreaterThanOrEqual(70);
    });

    it('should generate comprehensive security report', async () => {
      const report = await generateSecurityReport();
      
      expect(report).toBeDefined();
      expect(report).toHaveProperty('summary');
      expect(report).toHaveProperty('results');
      expect(report).toHaveProperty('recommendations');
      
      expect(report.summary).toHaveProperty('totalTests');
      expect(report.summary).toHaveProperty('passed');
      expect(report.summary).toHaveProperty('failed');
      expect(report.summary).toHaveProperty('criticalIssues');
      expect(report.summary).toHaveProperty('overallScore');
      
      expect(Array.isArray(report.results)).toBe(true);
      expect(Array.isArray(report.recommendations)).toBe(true);
      
      // Log security score
      console.log(`Overall security score: ${report.summary.overallScore}/100`);
      
      // Ensure minimum security score (adjust threshold as needed)
      expect(report.summary.overallScore).toBeGreaterThanOrEqual(70);
      
      // Ensure no critical issues in production
      if (process.env.NODE_ENV === 'production') {
        expect(report.summary.criticalIssues).toBe(0);
      }
    });

    it('should run comprehensive security audit', async () => {
      const auditResult = await securityAuditService.runSecurityAudit();
      
      expect(auditResult).toBeDefined();
      expect(auditResult).toHaveProperty('auditId');
      expect(auditResult).toHaveProperty('timestamp');
      expect(auditResult).toHaveProperty('tenantId');
      expect(auditResult).toHaveProperty('overallScore');
      expect(auditResult).toHaveProperty('vulnerabilities');
      expect(auditResult).toHaveProperty('summary');
      expect(auditResult).toHaveProperty('recommendations');
      
      expect(typeof auditResult.overallScore).toBe('number');
      expect(auditResult.overallScore).toBeGreaterThanOrEqual(0);
      expect(auditResult.overallScore).toBeLessThanOrEqual(100);
      
      expect(Array.isArray(auditResult.vulnerabilities)).toBe(true);
      expect(Array.isArray(auditResult.recommendations)).toBe(true);
      
      // Log audit results
      console.log(`Security audit score: ${auditResult.overallScore}/100`);
      console.log(`Found ${auditResult.summary.total} vulnerabilities`);
      
      // Ensure acceptable security score
      expect(auditResult.overallScore).toBeGreaterThanOrEqual(60);
      
      // Log critical vulnerabilities
      const criticalVulns = auditResult.vulnerabilities.filter(v => v.severity === 'critical');
      if (criticalVulns.length > 0) {
        console.warn(`Found ${criticalVulns.length} critical vulnerabilities:`, criticalVulns);
      }
    });
  });

  describe('Security Test Performance', () => {
    it('should complete security tests within reasonable time', async () => {
      const startTime = Date.now();
      const results = await runAllSecurityTests();
      const totalDuration = Date.now() - startTime;
      
      expect(totalDuration).toBeLessThan(30000); // 30 seconds max
      
      // Check individual test performance
      results.forEach(result => {
        expect(result.duration).toBeLessThan(10000); // 10 seconds max per test
      });
      
      console.log(`Security tests completed in ${totalDuration}ms`);
    });

    it('should handle concurrent security tests', async () => {
      const testPromises = [
        testTenantDataIsolation(),
        testPersonaPermissions(),
        testSessionManagement()
      ];
      
      const results = await Promise.all(testPromises);
      
      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(typeof result.passed).toBe('boolean');
      });
    });
  });

  describe('Security Test Reliability', () => {
    it('should produce consistent results across multiple runs', async () => {
      const run1 = await testTenantDataIsolation();
      const run2 = await testTenantDataIsolation();
      const run3 = await testTenantDataIsolation();
      
      // Results should be consistent (assuming no changes in system state)
      expect(run1.passed).toBe(run2.passed);
      expect(run2.passed).toBe(run3.passed);
      
      // Test IDs should be consistent
      expect(run1.testId).toBe(run2.testId);
      expect(run2.testId).toBe(run3.testId);
    });

    it('should handle errors gracefully', async () => {
      // This test verifies that security tests handle errors gracefully
      // Since the actual implementation may have different error handling,
      // we'll test that the result structure is consistent even with errors

      const result = await testTenantDataIsolation();

      expect(result).toBeDefined();
      expect(result).toHaveProperty('testId');
      expect(result).toHaveProperty('passed');
      expect(result).toHaveProperty('duration');
      expect(typeof result.passed).toBe('boolean');
      expect(typeof result.duration).toBe('number');

      // If the test fails, it should have an error message
      if (!result.passed) {
        expect(result).toHaveProperty('error');
        expect(typeof result.error).toBe('string');
      }
    });
  });
});
