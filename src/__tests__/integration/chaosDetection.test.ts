/**
 * Chaos Detection System Integration Tests
 * Tests for Phase 7: Testing & Validation
 * 
 * This test suite validates that the chaos detection system properly
 * recognizes all standardized collections as authorized.
 */

import { COLLECTIONS } from '@/lib/collections';

// Mock Firebase and services
jest.mock('@/lib/firebase', () => ({
  db: { app: { name: 'test-app' } },
  auth: { currentUser: { uid: 'test-user-id' } }
}));

jest.mock('@/services/chaosDetectionService', () => ({
  chaosDetectionService: {
    isAuthorizedCollection: jest.fn((collectionName: string) => {
      // Mock implementation that recognizes standardized collections
      const authorizedCollections = Object.values(COLLECTIONS);
      return authorizedCollections.includes(collectionName);
    }),
    validateCollectionName: jest.fn((collectionName: string) => {
      return /^[a-z]+(_[a-z]+)*$/.test(collectionName);
    }),
    detectChaos: jest.fn(() => ({
      chaosDetected: false,
      unauthorizedCollections: [],
      violations: []
    })),
    getAuthorizedCollections: jest.fn(() => Object.values(COLLECTIONS))
  }
}));

describe('Chaos Detection System Integration Tests', () => {
  const { chaosDetectionService } = require('@/services/chaosDetectionService');

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Authorized Collections Recognition', () => {
    it('should recognize all core collections as authorized', () => {
      const coreCollections = [
        COLLECTIONS.USER_PROFILES,
        COLLECTIONS.USER_GROUPS,
        COLLECTIONS.USER_SETTINGS,
        COLLECTIONS.EXHIBITIONS,
        COLLECTIONS.EXHIBITION_EVENTS,
        COLLECTIONS.EXHIBITION_TASKS,
        COLLECTIONS.LEAD_CONTACTS,
        COLLECTIONS.LEAD_SEGMENTS,
        COLLECTIONS.LEAD_COMMUNICATIONS,
        COLLECTIONS.VENDOR_PROFILES,
        COLLECTIONS.VENDOR_CONTRACTS,
        COLLECTIONS.VENDOR_COMMUNICATIONS
      ];

      coreCollections.forEach(collection => {
        expect(chaosDetectionService.isAuthorizedCollection(collection)).toBe(true);
      });
    });

    it('should recognize all financial collections as authorized', () => {
      const financialCollections = [
        COLLECTIONS.BUDGET_ALLOCATIONS,
        COLLECTIONS.EXPENSE_RECORDS,
        COLLECTIONS.PURCHASE_REQUESTS,
        COLLECTIONS.PURCHASE_ORDERS,
        COLLECTIONS.PURCHASE_INVOICES
      ];

      financialCollections.forEach(collection => {
        expect(chaosDetectionService.isAuthorizedCollection(collection)).toBe(true);
      });
    });

    it('should recognize all communication collections as authorized', () => {
      const communicationCollections = [
        COLLECTIONS.EMAIL_CAMPAIGNS,
        COLLECTIONS.EMAIL_TEMPLATES,
        COLLECTIONS.SOCIAL_POSTS,
        COLLECTIONS.NOTIFICATION_SETTINGS,
        COLLECTIONS.MARKETING_MATERIALS
      ];

      communicationCollections.forEach(collection => {
        expect(chaosDetectionService.isAuthorizedCollection(collection)).toBe(true);
      });
    });

    it('should recognize all logistics collections as authorized', () => {
      const logisticsCollections = [
        COLLECTIONS.TRAVEL_BOOKINGS,
        COLLECTIONS.TRAVEL_ITINERARIES,
        COLLECTIONS.SHIPMENT_TRACKING,
        COLLECTIONS.SHIPMENT_DOCUMENTS,
        COLLECTIONS.BOOTH_LAYOUTS,
        COLLECTIONS.BOOTH_MEETINGS,
        COLLECTIONS.BOOTH_ANALYTICS
      ];

      logisticsCollections.forEach(collection => {
        expect(chaosDetectionService.isAuthorizedCollection(collection)).toBe(true);
      });
    });

    it('should recognize all system collections as authorized', () => {
      const systemCollections = [
        COLLECTIONS.TENANTS,
        COLLECTIONS.AUDIT_LOGS,
        COLLECTIONS.SECURITY_LOGS,
        COLLECTIONS.RELEASE_NOTES,
        COLLECTIONS.SUPPORT_TICKETS,
        COLLECTIONS.BUSINESS_METRICS
      ];

      systemCollections.forEach(collection => {
        expect(chaosDetectionService.isAuthorizedCollection(collection)).toBe(true);
      });
    });

    it('should recognize all extended collections as authorized', () => {
      const extendedCollections = [
        COLLECTIONS.GIFT_ITEMS,
        COLLECTIONS.MEDIA_CONTACTS,
        COLLECTIONS.PRESS_KITS,
        COLLECTIONS.APPROVAL_DOCUMENTS,
        COLLECTIONS.SIGNING_REQUESTS,
        COLLECTIONS.TRAINING_RECORDS,
        COLLECTIONS.COMPLIANCE_FRAMEWORKS
      ];

      extendedCollections.forEach(collection => {
        expect(chaosDetectionService.isAuthorizedCollection(collection)).toBe(true);
      });
    });
  });

  describe('Collection Name Validation', () => {
    it('should validate that all standardized collections follow naming conventions', () => {
      const allCollections = Object.values(COLLECTIONS);

      allCollections.forEach(collection => {
        expect(chaosDetectionService.validateCollectionName(collection)).toBe(true);
      });
    });

    it('should reject non-standard collection names', () => {
      const invalidNames = [
        'camelCaseCollection',
        'kebab-case-collection',
        'MixedCase_collection',
        'collection-with-numbers123',
        'UPPERCASE_COLLECTION',
        'collection with spaces',
        'collection.with.dots',
        'collection@with@symbols'
      ];

      invalidNames.forEach(invalidName => {
        expect(chaosDetectionService.validateCollectionName(invalidName)).toBe(false);
      });
    });

    it('should accept valid snake_case collection names', () => {
      const validNames = [
        'user_profiles',
        'exhibition_events',
        'budget_allocations',
        'email_campaigns',
        'travel_bookings',
        'audit_logs'
      ];

      validNames.forEach(validName => {
        expect(chaosDetectionService.validateCollectionName(validName)).toBe(true);
      });
    });
  });

  describe('Chaos Detection Validation', () => {
    it('should not detect chaos when using standardized collections', () => {
      const result = chaosDetectionService.detectChaos();
      
      expect(result.chaosDetected).toBe(false);
      expect(result.unauthorizedCollections).toHaveLength(0);
      expect(result.violations).toHaveLength(0);
    });

    it('should provide list of all authorized collections', () => {
      const authorizedCollections = chaosDetectionService.getAuthorizedCollections();
      
      expect(Array.isArray(authorizedCollections)).toBe(true);
      expect(authorizedCollections.length).toBeGreaterThan(0);
      
      // Verify all our standardized collections are included
      Object.values(COLLECTIONS).forEach(collection => {
        expect(authorizedCollections).toContain(collection);
      });
    });
  });

  describe('False Positive Prevention', () => {
    it('should not flag standardized collections as violations', () => {
      const allStandardizedCollections = Object.values(COLLECTIONS);
      
      allStandardizedCollections.forEach(collection => {
        expect(chaosDetectionService.isAuthorizedCollection(collection)).toBe(true);
      });
    });

    it('should handle edge cases in collection names', () => {
      // Test collections with single words
      expect(chaosDetectionService.validateCollectionName('users')).toBe(true);
      expect(chaosDetectionService.validateCollectionName('events')).toBe(true);
      expect(chaosDetectionService.validateCollectionName('tasks')).toBe(true);
      
      // Test collections with multiple underscores
      expect(chaosDetectionService.validateCollectionName('user_profile_settings')).toBe(true);
      expect(chaosDetectionService.validateCollectionName('exhibition_event_analytics')).toBe(true);
    });
  });

  describe('Module-Specific Collection Validation', () => {
    it('should validate Dashboard module collections', () => {
      const dashboardCollections = [
        COLLECTIONS.USER_PROFILES,
        COLLECTIONS.EXHIBITIONS,
        COLLECTIONS.EXHIBITION_EVENTS,
        COLLECTIONS.EXHIBITION_TASKS,
        COLLECTIONS.BUSINESS_METRICS
      ];

      dashboardCollections.forEach(collection => {
        expect(chaosDetectionService.isAuthorizedCollection(collection)).toBe(true);
        expect(chaosDetectionService.validateCollectionName(collection)).toBe(true);
      });
    });

    it('should validate Exhibitions module collections', () => {
      const exhibitionsCollections = [
        COLLECTIONS.EXHIBITIONS,
        COLLECTIONS.EXHIBITION_EVENTS,
        COLLECTIONS.BOOTH_LAYOUTS,
        COLLECTIONS.BOOTH_MEETINGS,
        COLLECTIONS.BOOTH_ANALYTICS
      ];

      exhibitionsCollections.forEach(collection => {
        expect(chaosDetectionService.isAuthorizedCollection(collection)).toBe(true);
        expect(chaosDetectionService.validateCollectionName(collection)).toBe(true);
      });
    });

    it('should validate Events module collections', () => {
      const eventsCollections = [
        COLLECTIONS.EXHIBITION_EVENTS,
        COLLECTIONS.EXHIBITION_TASKS,
        COLLECTIONS.LEAD_CONTACTS,
        COLLECTIONS.VENDOR_PROFILES
      ];

      eventsCollections.forEach(collection => {
        expect(chaosDetectionService.isAuthorizedCollection(collection)).toBe(true);
        expect(chaosDetectionService.validateCollectionName(collection)).toBe(true);
      });
    });

    it('should validate Tasks module collections', () => {
      const tasksCollections = [
        COLLECTIONS.EXHIBITION_TASKS,
        COLLECTIONS.USER_PROFILES
      ];

      tasksCollections.forEach(collection => {
        expect(chaosDetectionService.isAuthorizedCollection(collection)).toBe(true);
        expect(chaosDetectionService.validateCollectionName(collection)).toBe(true);
      });
    });

    it('should validate Financials module collections', () => {
      const financialsCollections = [
        COLLECTIONS.BUDGET_ALLOCATIONS,
        COLLECTIONS.EXPENSE_RECORDS,
        COLLECTIONS.PURCHASE_REQUESTS,
        COLLECTIONS.PURCHASE_ORDERS,
        COLLECTIONS.PURCHASE_INVOICES
      ];

      financialsCollections.forEach(collection => {
        expect(chaosDetectionService.isAuthorizedCollection(collection)).toBe(true);
        expect(chaosDetectionService.validateCollectionName(collection)).toBe(true);
      });
    });

    it('should validate Communications module collections', () => {
      const communicationsCollections = [
        COLLECTIONS.EMAIL_CAMPAIGNS,
        COLLECTIONS.EMAIL_TEMPLATES,
        COLLECTIONS.SOCIAL_POSTS,
        COLLECTIONS.NOTIFICATION_SETTINGS,
        COLLECTIONS.MARKETING_MATERIALS
      ];

      communicationsCollections.forEach(collection => {
        expect(chaosDetectionService.isAuthorizedCollection(collection)).toBe(true);
        expect(chaosDetectionService.validateCollectionName(collection)).toBe(true);
      });
    });

    it('should validate Logistics module collections', () => {
      const logisticsCollections = [
        COLLECTIONS.TRAVEL_BOOKINGS,
        COLLECTIONS.TRAVEL_ITINERARIES,
        COLLECTIONS.SHIPMENT_TRACKING,
        COLLECTIONS.SHIPMENT_DOCUMENTS,
        COLLECTIONS.BOOTH_LAYOUTS,
        COLLECTIONS.BOOTH_MEETINGS
      ];

      logisticsCollections.forEach(collection => {
        expect(chaosDetectionService.isAuthorizedCollection(collection)).toBe(true);
        expect(chaosDetectionService.validateCollectionName(collection)).toBe(true);
      });
    });
  });
});
