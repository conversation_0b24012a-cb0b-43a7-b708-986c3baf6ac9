/**
 * Professional Data Manager Integration Tests
 * Tests for Phase 7: Testing & Validation
 * 
 * This test suite validates the Professional Data Manager functionality
 * with standardized collections and metadata.
 */

import { professionalDataManager } from '@/lib/professionalDataManager';
import { COLLECTIONS } from '@/lib/collections';

// Mock Firebase completely for testing
jest.mock('@/lib/firebase', () => ({
  db: {
    app: { name: 'test-app' }
  },
  auth: {
    currentUser: {
      uid: 'test-user-id',
      email: '<EMAIL>'
    }
  }
}));

describe('Professional Data Manager Integration Tests', () => {
  const testTenantId = 'evexa-development-tenant';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Data Manager Initialization', () => {
    it('should initialize without errors', () => {
      expect(() => {
        // Test that the professional data manager can be imported
        expect(professionalDataManager).toBeDefined();
      }).not.toThrow();
    });

    it('should have access to all standardized collections', () => {
      // Test that all collection constants are accessible
      expect(COLLECTIONS.USER_PROFILES).toBe('user_profiles');
      expect(COLLECTIONS.EXHIBITIONS).toBe('exhibitions');
      expect(COLLECTIONS.EXHIBITION_EVENTS).toBe('exhibition_events');
      expect(COLLECTIONS.EXHIBITION_TASKS).toBe('exhibition_tasks');
    });
  });

  describe('Collection Management', () => {
    it('should support core business collections', async () => {
      const coreCollections = [
        COLLECTIONS.USER_PROFILES,
        COLLECTIONS.USER_GROUPS,
        COLLECTIONS.USER_SETTINGS,
        COLLECTIONS.EXHIBITIONS,
        COLLECTIONS.EXHIBITION_EVENTS,
        COLLECTIONS.EXHIBITION_TASKS,
        COLLECTIONS.LEAD_CONTACTS,
        COLLECTIONS.LEAD_SEGMENTS,
        COLLECTIONS.LEAD_COMMUNICATIONS,
        COLLECTIONS.VENDOR_PROFILES,
        COLLECTIONS.VENDOR_CONTRACTS,
        COLLECTIONS.VENDOR_COMMUNICATIONS
      ];

      coreCollections.forEach(collection => {
        expect(collection).toMatch(/^[a-z]+(_[a-z]+)*$/); // snake_case validation
        expect(collection).toBeDefined();
        expect(typeof collection).toBe('string');
      });
    });

    it('should support financial collections', async () => {
      const financialCollections = [
        COLLECTIONS.BUDGET_ALLOCATIONS,
        COLLECTIONS.EXPENSE_RECORDS,
        COLLECTIONS.PURCHASE_REQUESTS,
        COLLECTIONS.PURCHASE_ORDERS,
        COLLECTIONS.PURCHASE_INVOICES
      ];

      financialCollections.forEach(collection => {
        expect(collection).toMatch(/^[a-z]+(_[a-z]+)*$/); // snake_case validation
        expect(collection).toBeDefined();
        expect(typeof collection).toBe('string');
      });
    });

    it('should support communication collections', async () => {
      const communicationCollections = [
        COLLECTIONS.EMAIL_CAMPAIGNS,
        COLLECTIONS.EMAIL_TEMPLATES,
        COLLECTIONS.SOCIAL_POSTS,
        COLLECTIONS.NOTIFICATION_SETTINGS,
        COLLECTIONS.MARKETING_MATERIALS
      ];

      communicationCollections.forEach(collection => {
        expect(collection).toMatch(/^[a-z]+(_[a-z]+)*$/); // snake_case validation
        expect(collection).toBeDefined();
        expect(typeof collection).toBe('string');
      });
    });

    it('should support logistics collections', async () => {
      const logisticsCollections = [
        COLLECTIONS.TRAVEL_BOOKINGS,
        COLLECTIONS.TRAVEL_ITINERARIES,
        COLLECTIONS.SHIPMENT_TRACKING,
        COLLECTIONS.SHIPMENT_DOCUMENTS,
        COLLECTIONS.BOOTH_LAYOUTS,
        COLLECTIONS.BOOTH_MEETINGS,
        COLLECTIONS.BOOTH_ANALYTICS
      ];

      logisticsCollections.forEach(collection => {
        expect(collection).toMatch(/^[a-z]+(_[a-z]+)*$/); // snake_case validation
        expect(collection).toBeDefined();
        expect(typeof collection).toBe('string');
      });
    });

    it('should support system collections', async () => {
      const systemCollections = [
        COLLECTIONS.TENANTS,
        COLLECTIONS.AUDIT_LOGS,
        COLLECTIONS.SECURITY_LOGS,
        COLLECTIONS.RELEASE_NOTES,
        COLLECTIONS.SUPPORT_TICKETS,
        COLLECTIONS.BUSINESS_METRICS
      ];

      systemCollections.forEach(collection => {
        expect(collection).toMatch(/^[a-z]+(_[a-z]+)*$/); // snake_case validation
        expect(collection).toBeDefined();
        expect(typeof collection).toBe('string');
      });
    });
  });

  describe('Standard Metadata Validation', () => {
    it('should define StandardMetadata interface requirements', () => {
      // Test that the standard metadata structure is properly defined
      const mockDocument = {
        id: 'test-id',
        tenantId: testTenantId,
        createdAt: new Date(),
        updatedAt: new Date(),
        version: 1,
        // Additional document fields
        name: 'Test Document',
        status: 'active'
      };

      // Validate required metadata fields
      expect(mockDocument.tenantId).toBe(testTenantId);
      expect(mockDocument.createdAt).toBeInstanceOf(Date);
      expect(mockDocument.updatedAt).toBeInstanceOf(Date);
      expect(mockDocument.version).toBe(1);
    });

    it('should support tenant isolation through tenantId', () => {
      const testDocument = {
        id: 'test-doc',
        tenantId: testTenantId,
        createdAt: new Date(),
        updatedAt: new Date(),
        version: 1
      };

      expect(testDocument.tenantId).toBe(testTenantId);
      expect(testDocument.tenantId).toMatch(/^[a-z0-9-]+$/); // Valid tenant ID format
    });
  });

  describe('Data Validation', () => {
    it('should validate collection names follow standards', () => {
      const allCollections = Object.values(COLLECTIONS);
      
      allCollections.forEach(collectionName => {
        // Must be snake_case
        expect(collectionName).toMatch(/^[a-z]+(_[a-z]+)*$/);
        // Must not be empty
        expect(collectionName.length).toBeGreaterThan(0);
        // Must not contain special characters except underscore
        expect(collectionName).not.toMatch(/[^a-z_]/);
      });
    });

    it('should ensure no duplicate collection names', () => {
      const allCollections = Object.values(COLLECTIONS);
      const uniqueCollections = [...new Set(allCollections)];
      
      expect(allCollections.length).toBe(uniqueCollections.length);
    });

    it('should validate collection name lengths are reasonable', () => {
      const allCollections = Object.values(COLLECTIONS);
      
      allCollections.forEach(collectionName => {
        expect(collectionName.length).toBeGreaterThan(3); // Minimum meaningful length
        expect(collectionName.length).toBeLessThan(50); // Maximum reasonable length
      });
    });
  });

  describe('Module Integration Support', () => {
    it('should support Dashboard module requirements', () => {
      const dashboardCollections = [
        COLLECTIONS.USER_PROFILES,
        COLLECTIONS.EXHIBITIONS,
        COLLECTIONS.EXHIBITION_EVENTS,
        COLLECTIONS.EXHIBITION_TASKS,
        COLLECTIONS.BUSINESS_METRICS
      ];

      dashboardCollections.forEach(collection => {
        expect(collection).toBeDefined();
        expect(typeof collection).toBe('string');
      });
    });

    it('should support Exhibitions module requirements', () => {
      const exhibitionsCollections = [
        COLLECTIONS.EXHIBITIONS,
        COLLECTIONS.EXHIBITION_EVENTS,
        COLLECTIONS.BOOTH_LAYOUTS,
        COLLECTIONS.BOOTH_MEETINGS,
        COLLECTIONS.BOOTH_ANALYTICS
      ];

      exhibitionsCollections.forEach(collection => {
        expect(collection).toBeDefined();
        expect(typeof collection).toBe('string');
      });
    });

    it('should support Events module requirements', () => {
      const eventsCollections = [
        COLLECTIONS.EXHIBITION_EVENTS,
        COLLECTIONS.EXHIBITION_TASKS,
        COLLECTIONS.LEAD_CONTACTS,
        COLLECTIONS.VENDOR_PROFILES
      ];

      eventsCollections.forEach(collection => {
        expect(collection).toBeDefined();
        expect(typeof collection).toBe('string');
      });
    });

    it('should support Tasks module requirements', () => {
      const tasksCollections = [
        COLLECTIONS.EXHIBITION_TASKS,
        COLLECTIONS.USER_PROFILES,
        COLLECTIONS.EXHIBITIONS,
        COLLECTIONS.EXHIBITION_EVENTS
      ];

      tasksCollections.forEach(collection => {
        expect(collection).toBeDefined();
        expect(typeof collection).toBe('string');
      });
    });

    it('should support Financials module requirements', () => {
      const financialsCollections = [
        COLLECTIONS.BUDGET_ALLOCATIONS,
        COLLECTIONS.EXPENSE_RECORDS,
        COLLECTIONS.PURCHASE_REQUESTS,
        COLLECTIONS.PURCHASE_ORDERS,
        COLLECTIONS.PURCHASE_INVOICES
      ];

      financialsCollections.forEach(collection => {
        expect(collection).toBeDefined();
        expect(typeof collection).toBe('string');
      });
    });

    it('should support Communications module requirements', () => {
      const communicationsCollections = [
        COLLECTIONS.EMAIL_CAMPAIGNS,
        COLLECTIONS.EMAIL_TEMPLATES,
        COLLECTIONS.SOCIAL_POSTS,
        COLLECTIONS.NOTIFICATION_SETTINGS,
        COLLECTIONS.MARKETING_MATERIALS
      ];

      communicationsCollections.forEach(collection => {
        expect(collection).toBeDefined();
        expect(typeof collection).toBe('string');
      });
    });

    it('should support Logistics module requirements', () => {
      const logisticsCollections = [
        COLLECTIONS.TRAVEL_BOOKINGS,
        COLLECTIONS.TRAVEL_ITINERARIES,
        COLLECTIONS.SHIPMENT_TRACKING,
        COLLECTIONS.SHIPMENT_DOCUMENTS,
        COLLECTIONS.BOOTH_LAYOUTS,
        COLLECTIONS.BOOTH_MEETINGS
      ];

      logisticsCollections.forEach(collection => {
        expect(collection).toBeDefined();
        expect(typeof collection).toBe('string');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle missing collections gracefully', () => {
      expect(() => {
        // Test accessing a non-existent collection
        const nonExistent = (COLLECTIONS as any).NON_EXISTENT_COLLECTION;
        expect(nonExistent).toBeUndefined();
      }).not.toThrow();
    });

    it('should validate collection access patterns', () => {
      // Test that collections can be accessed safely
      expect(() => {
        Object.keys(COLLECTIONS).forEach(key => {
          const collection = (COLLECTIONS as any)[key];
          expect(typeof collection).toBe('string');
        });
      }).not.toThrow();
    });
  });
});
