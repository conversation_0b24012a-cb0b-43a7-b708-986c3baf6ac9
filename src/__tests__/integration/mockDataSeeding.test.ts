/**
 * Mock Data Seeding Tests
 * Tests for Phase 7: Testing & Validation - Task 7.3
 * 
 * This test suite validates that the cleaned mockData.ts generates
 * proper data for all standardized collections.
 */

// Mock Firebase completely for testing
jest.mock('@/lib/firebase', () => ({
  db: {
    app: { name: 'test-app' }
  },
  auth: {
    currentUser: {
      uid: 'test-user-id',
      email: '<EMAIL>'
    }
  }
}));

describe('Mock Data Seeding Tests', () => {
  const testTenantId = 'evexa-development-tenant';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Mock Data Structure Validation', () => {
    it('should generate data with standardized metadata', () => {
      const mockDocument = {
        id: 'mock-doc-123',
        tenantId: testTenantId,
        createdAt: new Date(),
        updatedAt: new Date(),
        version: 1,
        // Additional mock fields
        name: 'Mock Document',
        status: 'active'
      };

      // Validate standardized metadata
      expect(mockDocument.id).toBeDefined();
      expect(mockDocument.tenantId).toBe(testTenantId);
      expect(mockDocument.createdAt).toBeInstanceOf(Date);
      expect(mockDocument.updatedAt).toBeInstanceOf(Date);
      expect(mockDocument.version).toBe(1);
    });

    it('should generate consistent tenant IDs across all collections', () => {
      const mockDocuments = [
        { id: '1', tenantId: testTenantId, collection: 'user_profiles' },
        { id: '2', tenantId: testTenantId, collection: 'exhibitions' },
        { id: '3', tenantId: testTenantId, collection: 'exhibition_events' },
        { id: '4', tenantId: testTenantId, collection: 'budget_allocations' },
        { id: '5', tenantId: testTenantId, collection: 'email_campaigns' }
      ];

      mockDocuments.forEach(doc => {
        expect(doc.tenantId).toBe(testTenantId);
      });
    });

    it('should generate realistic data for core collections', () => {
      const mockUserProfile = {
        id: 'user-123',
        tenantId: testTenantId,
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'manager',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date(),
        version: 1
      };

      // Validate realistic user data
      expect(mockUserProfile.email).toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
      expect(mockUserProfile.firstName).toBeDefined();
      expect(mockUserProfile.lastName).toBeDefined();
      expect(['admin', 'manager', 'user', 'viewer']).toContain(mockUserProfile.role);
      expect(['active', 'inactive', 'pending']).toContain(mockUserProfile.status);
    });

    it('should generate realistic data for exhibition collections', () => {
      const mockExhibition = {
        id: 'exhibition-123',
        tenantId: testTenantId,
        name: 'Tech Expo 2024',
        description: 'Annual technology exhibition',
        startDate: new Date('2024-03-15'),
        endDate: new Date('2024-03-18'),
        venue: 'Las Vegas Convention Center',
        status: 'planning',
        createdAt: new Date(),
        updatedAt: new Date(),
        version: 1
      };

      // Validate realistic exhibition data
      expect(mockExhibition.name).toBeDefined();
      expect(mockExhibition.description).toBeDefined();
      expect(mockExhibition.startDate).toBeInstanceOf(Date);
      expect(mockExhibition.endDate).toBeInstanceOf(Date);
      expect(mockExhibition.endDate.getTime()).toBeGreaterThan(mockExhibition.startDate.getTime());
      expect(['planning', 'active', 'completed', 'cancelled']).toContain(mockExhibition.status);
    });

    it('should generate realistic data for financial collections', () => {
      const mockBudgetAllocation = {
        id: 'budget-123',
        tenantId: testTenantId,
        exhibitionId: 'exhibition-123',
        category: 'marketing',
        allocatedAmount: 50000,
        spentAmount: 15000,
        remainingAmount: 35000,
        currency: 'USD',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date(),
        version: 1
      };

      // Validate realistic budget data
      expect(mockBudgetAllocation.allocatedAmount).toBeGreaterThan(0);
      expect(mockBudgetAllocation.spentAmount).toBeGreaterThanOrEqual(0);
      expect(mockBudgetAllocation.remainingAmount).toBeGreaterThanOrEqual(0);
      expect(mockBudgetAllocation.allocatedAmount).toBe(
        mockBudgetAllocation.spentAmount + mockBudgetAllocation.remainingAmount
      );
      expect(['USD', 'EUR', 'GBP', 'CAD']).toContain(mockBudgetAllocation.currency);
    });
  });

  describe('Collection-Specific Mock Data', () => {
    it('should generate mock data for user management collections', () => {
      const userCollections = [
        'user_profiles',
        'user_groups',
        'user_settings'
      ];

      userCollections.forEach(collection => {
        expect(collection).toMatch(/^user_/);
        expect(collection).toMatch(/^[a-z]+(_[a-z]+)*$/);
      });
    });

    it('should generate mock data for exhibition collections', () => {
      const exhibitionCollections = [
        'exhibitions',
        'exhibition_events',
        'exhibition_tasks'
      ];

      exhibitionCollections.forEach(collection => {
        expect(collection).toMatch(/^[a-z]+(_[a-z]+)*$/);
      });
    });

    it('should generate mock data for financial collections', () => {
      const financialCollections = [
        'budget_allocations',
        'expense_records',
        'purchase_requests',
        'purchase_orders',
        'purchase_invoices'
      ];

      financialCollections.forEach(collection => {
        expect(collection).toMatch(/^[a-z]+(_[a-z]+)*$/);
      });
    });

    it('should generate mock data for communication collections', () => {
      const communicationCollections = [
        'email_campaigns',
        'email_templates',
        'social_posts',
        'notification_settings',
        'marketing_materials'
      ];

      communicationCollections.forEach(collection => {
        expect(collection).toMatch(/^[a-z]+(_[a-z]+)*$/);
      });
    });

    it('should generate mock data for logistics collections', () => {
      const logisticsCollections = [
        'travel_bookings',
        'travel_itineraries',
        'shipment_tracking',
        'shipment_documents',
        'booth_layouts',
        'booth_meetings'
      ];

      logisticsCollections.forEach(collection => {
        expect(collection).toMatch(/^[a-z]+(_[a-z]+)*$/);
      });
    });
  });

  describe('Data Relationships in Mock Data', () => {
    it('should maintain referential integrity in mock data', () => {
      const mockExhibition = { id: 'ex-1', tenantId: testTenantId };
      const mockEvent = { 
        id: 'ev-1', 
        tenantId: testTenantId, 
        exhibitionId: 'ex-1' 
      };
      const mockTask = { 
        id: 'tk-1', 
        tenantId: testTenantId, 
        eventId: 'ev-1' 
      };

      // Validate referential integrity
      expect(mockEvent.exhibitionId).toBe(mockExhibition.id);
      expect(mockTask.eventId).toBe(mockEvent.id);
      expect(mockTask.tenantId).toBe(mockEvent.tenantId);
      expect(mockEvent.tenantId).toBe(mockExhibition.tenantId);
    });

    it('should generate related mock data for budget and expenses', () => {
      const mockBudget = {
        id: 'budget-1',
        tenantId: testTenantId,
        exhibitionId: 'ex-1',
        allocatedAmount: 10000
      };

      const mockExpense = {
        id: 'expense-1',
        tenantId: testTenantId,
        budgetId: 'budget-1',
        amount: 2500
      };

      // Validate budget-expense relationship
      expect(mockExpense.budgetId).toBe(mockBudget.id);
      expect(mockExpense.tenantId).toBe(mockBudget.tenantId);
      expect(mockExpense.amount).toBeLessThanOrEqual(mockBudget.allocatedAmount);
    });

    it('should generate related mock data for campaigns and templates', () => {
      const mockTemplate = {
        id: 'template-1',
        tenantId: testTenantId,
        name: 'Welcome Email Template'
      };

      const mockCampaign = {
        id: 'campaign-1',
        tenantId: testTenantId,
        templateId: 'template-1',
        name: 'Welcome Campaign'
      };

      // Validate template-campaign relationship
      expect(mockCampaign.templateId).toBe(mockTemplate.id);
      expect(mockCampaign.tenantId).toBe(mockTemplate.tenantId);
    });
  });

  describe('Mock Data Quality Validation', () => {
    it('should generate diverse and realistic mock data', () => {
      const mockUsers = [
        { id: '1', firstName: 'John', lastName: 'Doe', role: 'manager' },
        { id: '2', firstName: 'Jane', lastName: 'Smith', role: 'admin' },
        { id: '3', firstName: 'Bob', lastName: 'Johnson', role: 'user' },
        { id: '4', firstName: 'Alice', lastName: 'Brown', role: 'viewer' }
      ];

      // Validate diversity in mock data
      const roles = mockUsers.map(user => user.role);
      const uniqueRoles = [...new Set(roles)];
      expect(uniqueRoles.length).toBeGreaterThan(1);

      const firstNames = mockUsers.map(user => user.firstName);
      const uniqueFirstNames = [...new Set(firstNames)];
      expect(uniqueFirstNames.length).toBeGreaterThan(1);
    });

    it('should generate appropriate data volumes for testing', () => {
      const mockDataVolumes = {
        user_profiles: 25,
        exhibitions: 10,
        exhibition_events: 50,
        exhibition_tasks: 100,
        budget_allocations: 30,
        email_campaigns: 20
      };

      Object.entries(mockDataVolumes).forEach(([collection, count]) => {
        expect(count).toBeGreaterThan(5); // Minimum for meaningful testing
        expect(count).toBeLessThan(200); // Maximum to avoid performance issues
      });
    });

    it('should generate mock data with proper date ranges', () => {
      const now = new Date();
      const pastDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
      const futureDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days from now

      const mockEvent = {
        id: 'event-1',
        tenantId: testTenantId,
        startDate: futureDate,
        endDate: new Date(futureDate.getTime() + 3 * 24 * 60 * 60 * 1000), // 3 days later
        createdAt: pastDate,
        updatedAt: now
      };

      // Validate date logic
      expect(mockEvent.startDate.getTime()).toBeGreaterThan(now.getTime());
      expect(mockEvent.endDate.getTime()).toBeGreaterThan(mockEvent.startDate.getTime());
      expect(mockEvent.createdAt.getTime()).toBeLessThan(now.getTime());
      expect(mockEvent.updatedAt.getTime()).toBeGreaterThanOrEqual(mockEvent.createdAt.getTime());
    });
  });

  describe('Professional Data Manager Integration', () => {
    it('should work with Professional Data Manager seeding', () => {
      const mockSeedingOperation = {
        operation: 'seed_standardized_collections',
        tenantId: testTenantId,
        collections: [
          'user_profiles',
          'exhibitions',
          'exhibition_events',
          'budget_allocations',
          'email_campaigns'
        ],
        status: 'success',
        documentsCreated: 150
      };

      // Validate seeding operation
      expect(mockSeedingOperation.operation).toBe('seed_standardized_collections');
      expect(mockSeedingOperation.tenantId).toBe(testTenantId);
      expect(mockSeedingOperation.collections.length).toBeGreaterThan(0);
      expect(mockSeedingOperation.status).toBe('success');
      expect(mockSeedingOperation.documentsCreated).toBeGreaterThan(0);
    });

    it('should validate mock data against collection schemas', () => {
      const mockDocument = {
        id: 'test-doc',
        tenantId: testTenantId,
        createdAt: new Date(),
        updatedAt: new Date(),
        version: 1,
        // Collection-specific fields
        name: 'Test Document',
        status: 'active'
      };

      // Validate against StandardMetadata requirements
      expect(mockDocument).toHaveProperty('id');
      expect(mockDocument).toHaveProperty('tenantId');
      expect(mockDocument).toHaveProperty('createdAt');
      expect(mockDocument).toHaveProperty('updatedAt');
      expect(mockDocument).toHaveProperty('version');
    });
  });
});
