/**
 * Standardized Collections Integration Tests
 * Tests for Phase 7: Testing & Validation
 * 
 * This test suite validates that all standardized collections work correctly
 * with the new professional data architecture.
 */

import { COLLECTIONS } from '@/lib/collections';
import { EVEXA_DATA_SCHEMA } from '@/lib/professionalDataManager';
import { firestoreService } from '@/services/firestoreService';
import { tenantAwareFirestoreService } from '@/services/tenantAwareFirestoreService';

// Mock Firebase completely for testing
jest.mock('@/lib/firebase', () => ({
  db: {
    app: { name: 'test-app' }
  },
  auth: {
    currentUser: {
      uid: 'test-user-id',
      email: '<EMAIL>'
    }
  }
}));

describe('Standardized Collections Integration Tests', () => {
  const testTenantId = 'evexa-development-tenant';

  beforeEach(() => {
    // Reset any cached data
    jest.clearAllMocks();
  });

  describe('Collection Constants Validation', () => {
    it('should have all core collections defined', () => {
      expect(COLLECTIONS.USER_PROFILES).toBe('user_profiles');
      expect(COLLECTIONS.EXHIBITIONS).toBe('exhibitions');
      expect(COLLECTIONS.EXHIBITION_EVENTS).toBe('exhibition_events');
      expect(COLLECTIONS.EXHIBITION_TASKS).toBe('exhibition_tasks');
      expect(COLLECTIONS.LEAD_CONTACTS).toBe('lead_contacts');
      expect(COLLECTIONS.VENDOR_PROFILES).toBe('vendor_profiles');
    });

    it('should have all financial collections defined', () => {
      expect(COLLECTIONS.BUDGET_ALLOCATIONS).toBe('budget_allocations');
      expect(COLLECTIONS.EXPENSE_RECORDS).toBe('expense_records');
      expect(COLLECTIONS.PURCHASE_REQUESTS).toBe('purchase_requests');
      expect(COLLECTIONS.PURCHASE_ORDERS).toBe('purchase_orders');
      expect(COLLECTIONS.PURCHASE_INVOICES).toBe('purchase_invoices');
    });

    it('should have all communication collections defined', () => {
      expect(COLLECTIONS.EMAIL_CAMPAIGNS).toBe('email_campaigns');
      expect(COLLECTIONS.SOCIAL_POSTS).toBe('social_posts');
      expect(COLLECTIONS.NOTIFICATION_SETTINGS).toBe('notification_settings');
      expect(COLLECTIONS.MARKETING_MATERIALS).toBe('marketing_materials');
    });

    it('should have all logistics collections defined', () => {
      expect(COLLECTIONS.TRAVEL_BOOKINGS).toBe('travel_bookings');
      expect(COLLECTIONS.SHIPMENT_TRACKING).toBe('shipment_tracking');
      expect(COLLECTIONS.BOOTH_LAYOUTS).toBe('booth_layouts');
      expect(COLLECTIONS.BOOTH_MEETINGS).toBe('booth_meetings');
    });

    it('should have all system collections defined', () => {
      expect(COLLECTIONS.TENANTS).toBe('tenants');
      expect(COLLECTIONS.AUDIT_LOGS).toBe('audit_logs');
      expect(COLLECTIONS.SECURITY_LOGS).toBe('security_logs');
      expect(COLLECTIONS.RELEASE_NOTES).toBe('release_notes');
      expect(COLLECTIONS.SUPPORT_TICKETS).toBe('support_tickets');
    });
  });

  describe('Professional Data Schema Validation', () => {
    it('should have properly structured core collections schema', () => {
      expect(EVEXA_DATA_SCHEMA.CORE_COLLECTIONS).toBeDefined();
      expect(EVEXA_DATA_SCHEMA.CORE_COLLECTIONS.user_profiles).toBe('user_profiles');
      expect(EVEXA_DATA_SCHEMA.CORE_COLLECTIONS.exhibitions).toBe('exhibitions');
      expect(EVEXA_DATA_SCHEMA.CORE_COLLECTIONS.exhibition_events).toBe('exhibition_events');
    });

    it('should have properly structured financial collections schema', () => {
      expect(EVEXA_DATA_SCHEMA.FINANCIAL_COLLECTIONS).toBeDefined();
      expect(EVEXA_DATA_SCHEMA.FINANCIAL_COLLECTIONS.budget_allocations).toBe('budget_allocations');
      expect(EVEXA_DATA_SCHEMA.FINANCIAL_COLLECTIONS.expense_records).toBe('expense_records');
    });

    it('should have properly structured communication collections schema', () => {
      expect(EVEXA_DATA_SCHEMA.COMMUNICATION_COLLECTIONS).toBeDefined();
      expect(EVEXA_DATA_SCHEMA.COMMUNICATION_COLLECTIONS.email_campaigns).toBe('email_campaigns');
      expect(EVEXA_DATA_SCHEMA.COMMUNICATION_COLLECTIONS.social_posts).toBe('social_posts');
    });

    it('should have properly structured logistics collections schema', () => {
      expect(EVEXA_DATA_SCHEMA.LOGISTICS_COLLECTIONS).toBeDefined();
      expect(EVEXA_DATA_SCHEMA.LOGISTICS_COLLECTIONS.travel_bookings).toBe('travel_bookings');
      expect(EVEXA_DATA_SCHEMA.LOGISTICS_COLLECTIONS.shipment_tracking).toBe('shipment_tracking');
    });

    it('should have properly structured system collections schema', () => {
      expect(EVEXA_DATA_SCHEMA.SYSTEM_COLLECTIONS).toBeDefined();
      expect(EVEXA_DATA_SCHEMA.SYSTEM_COLLECTIONS.tenants).toBe('tenants');
      expect(EVEXA_DATA_SCHEMA.SYSTEM_COLLECTIONS.audit_logs).toBe('audit_logs');
    });
  });

  describe('Firestore Service Integration', () => {
    it('should use standardized collection names in firestoreService', () => {
      // Test that the service can reference collections without errors
      expect(() => {
        const collectionRef = COLLECTIONS.USER_PROFILES;
        expect(collectionRef).toBe('user_profiles');
      }).not.toThrow();
    });

    it('should use standardized collection names in tenantAwareFirestoreService', () => {
      // Test that tenant-aware service can reference collections
      expect(() => {
        const collectionRef = COLLECTIONS.EXHIBITIONS;
        expect(collectionRef).toBe('exhibitions');
      }).not.toThrow();
    });
  });

  describe('Collection Naming Standards', () => {
    it('should follow snake_case naming convention', () => {
      const allCollections = Object.values(COLLECTIONS);
      
      allCollections.forEach(collectionName => {
        // Check that collection name follows snake_case
        expect(collectionName).toMatch(/^[a-z]+(_[a-z]+)*$/);
        // Check that it doesn't contain camelCase
        expect(collectionName).not.toMatch(/[A-Z]/);
        // Check that it doesn't contain kebab-case
        expect(collectionName).not.toMatch(/-/);
      });
    });

    it('should not have duplicate collection names', () => {
      const allCollections = Object.values(COLLECTIONS);
      const uniqueCollections = [...new Set(allCollections)];
      
      expect(allCollections.length).toBe(uniqueCollections.length);
    });

    it('should have meaningful collection names', () => {
      // Test that collection names are descriptive
      expect(COLLECTIONS.USER_PROFILES).toContain('user');
      expect(COLLECTIONS.EXHIBITION_EVENTS).toContain('exhibition');
      expect(COLLECTIONS.BUDGET_ALLOCATIONS).toContain('budget');
      expect(COLLECTIONS.EMAIL_CAMPAIGNS).toContain('email');
      expect(COLLECTIONS.TRAVEL_BOOKINGS).toContain('travel');
    });
  });

  describe('Data Structure Validation', () => {
    it('should validate that all collections support StandardMetadata', () => {
      // Test that the schema includes metadata requirements
      expect(EVEXA_DATA_SCHEMA).toBeDefined();
      expect(EVEXA_DATA_SCHEMA.CORE_COLLECTIONS).toBeDefined();
      expect(EVEXA_DATA_SCHEMA.FINANCIAL_COLLECTIONS).toBeDefined();
      expect(EVEXA_DATA_SCHEMA.COMMUNICATION_COLLECTIONS).toBeDefined();
      expect(EVEXA_DATA_SCHEMA.LOGISTICS_COLLECTIONS).toBeDefined();
      expect(EVEXA_DATA_SCHEMA.SYSTEM_COLLECTIONS).toBeDefined();
    });
  });

  describe('Module Integration Tests', () => {
    it('should support Dashboard module collections', () => {
      // Test collections used by Dashboard module
      expect(COLLECTIONS.USER_PROFILES).toBeDefined();
      expect(COLLECTIONS.EXHIBITIONS).toBeDefined();
      expect(COLLECTIONS.EXHIBITION_EVENTS).toBeDefined();
      expect(COLLECTIONS.EXHIBITION_TASKS).toBeDefined();
    });

    it('should support Exhibitions module collections', () => {
      // Test collections used by Exhibitions module
      expect(COLLECTIONS.EXHIBITIONS).toBeDefined();
      expect(COLLECTIONS.EXHIBITION_EVENTS).toBeDefined();
      expect(COLLECTIONS.BOOTH_LAYOUTS).toBeDefined();
      expect(COLLECTIONS.BOOTH_MEETINGS).toBeDefined();
    });

    it('should support Events module collections', () => {
      // Test collections used by Events module
      expect(COLLECTIONS.EXHIBITION_EVENTS).toBeDefined();
      expect(COLLECTIONS.EXHIBITION_TASKS).toBeDefined();
      expect(COLLECTIONS.LEAD_CONTACTS).toBeDefined();
    });

    it('should support Tasks module collections', () => {
      // Test collections used by Tasks module
      expect(COLLECTIONS.EXHIBITION_TASKS).toBeDefined();
      expect(COLLECTIONS.USER_PROFILES).toBeDefined();
    });

    it('should support Financials module collections', () => {
      // Test collections used by Financials module
      expect(COLLECTIONS.BUDGET_ALLOCATIONS).toBeDefined();
      expect(COLLECTIONS.EXPENSE_RECORDS).toBeDefined();
      expect(COLLECTIONS.PURCHASE_REQUESTS).toBeDefined();
      expect(COLLECTIONS.PURCHASE_ORDERS).toBeDefined();
      expect(COLLECTIONS.PURCHASE_INVOICES).toBeDefined();
    });

    it('should support Communications module collections', () => {
      // Test collections used by Communications module
      expect(COLLECTIONS.EMAIL_CAMPAIGNS).toBeDefined();
      expect(COLLECTIONS.SOCIAL_POSTS).toBeDefined();
      expect(COLLECTIONS.NOTIFICATION_SETTINGS).toBeDefined();
      expect(COLLECTIONS.MARKETING_MATERIALS).toBeDefined();
    });

    it('should support Logistics module collections', () => {
      // Test collections used by Logistics module
      expect(COLLECTIONS.TRAVEL_BOOKINGS).toBeDefined();
      expect(COLLECTIONS.TRAVEL_ITINERARIES).toBeDefined();
      expect(COLLECTIONS.SHIPMENT_TRACKING).toBeDefined();
      expect(COLLECTIONS.SHIPMENT_DOCUMENTS).toBeDefined();
      expect(COLLECTIONS.BOOTH_LAYOUTS).toBeDefined();
      expect(COLLECTIONS.BOOTH_MEETINGS).toBeDefined();
    });
  });

  describe('Legacy Collection Mapping', () => {
    it('should maintain backward compatibility through legacy mappings', () => {
      // Import legacy mappings
      const { LEGACY_COLLECTIONS } = require('@/lib/collections');
      
      // Test that legacy names map to new standardized names
      expect(LEGACY_COLLECTIONS.users).toBe(COLLECTIONS.USER_PROFILES);
      expect(LEGACY_COLLECTIONS.events).toBe(COLLECTIONS.EXHIBITION_EVENTS);
      expect(LEGACY_COLLECTIONS.tasks).toBe(COLLECTIONS.EXHIBITION_TASKS);
      expect(LEGACY_COLLECTIONS.leads).toBe(COLLECTIONS.LEAD_CONTACTS);
      expect(LEGACY_COLLECTIONS.vendors).toBe(COLLECTIONS.VENDOR_PROFILES);
    });
  });
});
