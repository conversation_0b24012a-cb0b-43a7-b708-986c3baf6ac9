/**
 * Orphaned Records Detection Tests
 * Tests for detecting and handling orphaned records across collections
 */

import { DataIntegrityChecker } from '@/services/dataIntegrityChecker';
import { COLLECTIONS } from '@/lib/collections';

// Mock Firebase
jest.mock('@/lib/firebase', () => ({
  db: {
    collection: jest.fn(() => ({
      doc: jest.fn(() => ({
        get: jest.fn(),
        set: jest.fn(),
        update: jest.fn(),
        delete: jest.fn()
      })),
      where: jest.fn(() => ({
        get: jest.fn().mockResolvedValue({
          docs: [],
          size: 0,
          empty: true
        })
      })),
      get: jest.fn().mockResolvedValue({
        docs: [],
        size: 0,
        empty: true
      })
    }))
  }
}));

jest.mock('firebase/firestore', () => ({
  collection: jest.fn(),
  doc: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  getDocs: jest.fn(),
  getDoc: jest.fn(),
  deleteDoc: jest.fn(),
  writeBatch: jest.fn(() => ({
    delete: jest.fn(),
    commit: jest.fn().mockResolvedValue(undefined)
  })),
  Timestamp: {
    fromDate: jest.fn((date) => date),
    now: jest.fn(() => new Date())
  },
  serverTimestamp: jest.fn()
}));

describe('Orphaned Records Detection Tests', () => {
  const testTenantId = 'test-tenant-123';
  let integrityChecker: DataIntegrityChecker;

  beforeEach(() => {
    jest.clearAllMocks();
    integrityChecker = new DataIntegrityChecker(testTenantId);
  });

  describe('Exhibition-Related Orphaned Records', () => {
    test('should detect orphaned tasks without valid exhibitions', async () => {
      // Mock orphaned tasks
      const mockOrphanedTasks = [
        {
          id: 'task-1',
          data: () => ({
            exhibitionId: 'non-existent-exhibition-1',
            title: 'Orphaned Task 1',
            tenantId: testTenantId
          })
        },
        {
          id: 'task-2',
          data: () => ({
            exhibitionId: 'non-existent-exhibition-2',
            title: 'Orphaned Task 2',
            tenantId: testTenantId
          })
        },
        {
          id: 'task-3',
          data: () => ({
            exhibitionId: 'valid-exhibition',
            title: 'Valid Task',
            tenantId: testTenantId
          })
        }
      ];

      const { db } = require('@/lib/firebase');
      
      // Mock tasks collection
      db.collection.mockImplementation((collectionName: string) => {
        if (collectionName === COLLECTIONS.EXHIBITION_TASKS) {
          return {
            get: jest.fn().mockResolvedValue({
              docs: mockOrphanedTasks,
              size: mockOrphanedTasks.length
            })
          };
        }
        
        // Mock exhibitions collection - only 'valid-exhibition' exists
        if (collectionName === COLLECTIONS.EXHIBITIONS) {
          return {
            doc: jest.fn((docId: string) => ({
              get: jest.fn().mockResolvedValue({
                exists: docId === 'valid-exhibition',
                data: () => docId === 'valid-exhibition' ? { id: docId, tenantId: testTenantId } : null
              })
            }))
          };
        }
        
        return {
          get: jest.fn().mockResolvedValue({ docs: [], size: 0 })
        };
      });

      // Run orphaned record check
      const result = await integrityChecker.runIntegrityCheck({
        collections: [COLLECTIONS.EXHIBITION_TASKS],
        includeOrphanCheck: true
      });

      // Should detect 2 orphaned tasks
      expect(result.summary.totalIssues).toBeGreaterThan(0);
      expect(result.collectionResults.some(r => r.orphanedDocuments >= 2)).toBe(true);
    });

    test('should detect orphaned events without valid exhibitions', async () => {
      const mockOrphanedEvents = [
        {
          id: 'event-1',
          data: () => ({
            exhibitionId: 'missing-exhibition',
            title: 'Orphaned Event',
            tenantId: testTenantId
          })
        }
      ];

      const { db } = require('@/lib/firebase');
      
      db.collection.mockImplementation((collectionName: string) => {
        if (collectionName === COLLECTIONS.EXHIBITION_EVENTS) {
          return {
            get: jest.fn().mockResolvedValue({
              docs: mockOrphanedEvents,
              size: mockOrphanedEvents.length
            })
          };
        }
        
        if (collectionName === COLLECTIONS.EXHIBITIONS) {
          return {
            doc: jest.fn(() => ({
              get: jest.fn().mockResolvedValue({
                exists: false // Exhibition doesn't exist
              })
            }))
          };
        }
        
        return {
          get: jest.fn().mockResolvedValue({ docs: [], size: 0 })
        };
      });

      const result = await integrityChecker.runIntegrityCheck({
        collections: [COLLECTIONS.EXHIBITION_EVENTS],
        includeOrphanCheck: true
      });

      expect(result.summary.totalIssues).toBeGreaterThan(0);
    });

    test('should detect orphaned leads without valid exhibitions', async () => {
      const mockOrphanedLeads = [
        {
          id: 'lead-1',
          data: () => ({
            exhibitionId: 'deleted-exhibition',
            company: 'Orphaned Lead Company',
            tenantId: testTenantId
          })
        }
      ];

      const { db } = require('@/lib/firebase');
      
      db.collection.mockImplementation((collectionName: string) => {
        if (collectionName === COLLECTIONS.LEAD_CONTACTS) {
          return {
            get: jest.fn().mockResolvedValue({
              docs: mockOrphanedLeads,
              size: mockOrphanedLeads.length
            })
          };
        }
        
        if (collectionName === COLLECTIONS.EXHIBITIONS) {
          return {
            doc: jest.fn(() => ({
              get: jest.fn().mockResolvedValue({
                exists: false
              })
            }))
          };
        }
        
        return {
          get: jest.fn().mockResolvedValue({ docs: [], size: 0 })
        };
      });

      const result = await integrityChecker.runIntegrityCheck({
        collections: [COLLECTIONS.LEAD_CONTACTS],
        includeOrphanCheck: true
      });

      expect(result.summary.totalIssues).toBeGreaterThan(0);
    });
  });

  describe('User-Related Orphaned Records', () => {
    test('should detect tasks assigned to non-existent users', async () => {
      const mockTasksWithInvalidUsers = [
        {
          id: 'task-1',
          data: () => ({
            assignedTo: 'deleted-user-1',
            title: 'Task with deleted user',
            tenantId: testTenantId
          })
        },
        {
          id: 'task-2',
          data: () => ({
            assignedTo: 'valid-user',
            title: 'Task with valid user',
            tenantId: testTenantId
          })
        }
      ];

      const { db } = require('@/lib/firebase');
      
      db.collection.mockImplementation((collectionName: string) => {
        if (collectionName === COLLECTIONS.EXHIBITION_TASKS) {
          return {
            get: jest.fn().mockResolvedValue({
              docs: mockTasksWithInvalidUsers,
              size: mockTasksWithInvalidUsers.length
            })
          };
        }
        
        if (collectionName === COLLECTIONS.USER_PROFILES) {
          return {
            doc: jest.fn((userId: string) => ({
              get: jest.fn().mockResolvedValue({
                exists: userId === 'valid-user',
                data: () => userId === 'valid-user' ? { id: userId, tenantId: testTenantId } : null
              })
            }))
          };
        }
        
        return {
          get: jest.fn().mockResolvedValue({ docs: [], size: 0 })
        };
      });

      const result = await integrityChecker.runIntegrityCheck({
        collections: [COLLECTIONS.EXHIBITION_TASKS],
        includeOrphanCheck: true
      });

      expect(result.summary.totalIssues).toBeGreaterThan(0);
    });

    test('should detect events with non-existent organizers', async () => {
      const mockEventsWithInvalidOrganizers = [
        {
          id: 'event-1',
          data: () => ({
            organizer: 'non-existent-user',
            title: 'Event with invalid organizer',
            tenantId: testTenantId
          })
        }
      ];

      const { db } = require('@/lib/firebase');
      
      db.collection.mockImplementation((collectionName: string) => {
        if (collectionName === COLLECTIONS.EXHIBITION_EVENTS) {
          return {
            get: jest.fn().mockResolvedValue({
              docs: mockEventsWithInvalidOrganizers,
              size: mockEventsWithInvalidOrganizers.length
            })
          };
        }
        
        if (collectionName === COLLECTIONS.USER_PROFILES) {
          return {
            doc: jest.fn(() => ({
              get: jest.fn().mockResolvedValue({
                exists: false
              })
            }))
          };
        }
        
        return {
          get: jest.fn().mockResolvedValue({ docs: [], size: 0 })
        };
      });

      const result = await integrityChecker.runIntegrityCheck({
        collections: [COLLECTIONS.EXHIBITION_EVENTS],
        includeOrphanCheck: true
      });

      expect(result.summary.totalIssues).toBeGreaterThan(0);
    });
  });

  describe('Financial Orphaned Records', () => {
    test('should detect budget allocations without valid exhibitions', async () => {
      const mockOrphanedBudgets = [
        {
          id: 'budget-1',
          data: () => ({
            exhibitionId: 'cancelled-exhibition',
            amount: 5000,
            tenantId: testTenantId
          })
        }
      ];

      const { db } = require('@/lib/firebase');
      
      db.collection.mockImplementation((collectionName: string) => {
        if (collectionName === 'budget_allocations') {
          return {
            get: jest.fn().mockResolvedValue({
              docs: mockOrphanedBudgets,
              size: mockOrphanedBudgets.length
            })
          };
        }
        
        if (collectionName === COLLECTIONS.EXHIBITIONS) {
          return {
            doc: jest.fn(() => ({
              get: jest.fn().mockResolvedValue({
                exists: false
              })
            }))
          };
        }
        
        return {
          get: jest.fn().mockResolvedValue({ docs: [], size: 0 })
        };
      });

      const result = await integrityChecker.runIntegrityCheck({
        collections: ['budget_allocations'],
        includeOrphanCheck: true
      });

      expect(result.summary.totalIssues).toBeGreaterThan(0);
    });

    test('should detect expense records without valid exhibitions', async () => {
      const mockOrphanedExpenses = [
        {
          id: 'expense-1',
          data: () => ({
            exhibitionId: 'removed-exhibition',
            amount: 250.00,
            tenantId: testTenantId
          })
        }
      ];

      const { db } = require('@/lib/firebase');
      
      db.collection.mockImplementation((collectionName: string) => {
        if (collectionName === 'expense_records') {
          return {
            get: jest.fn().mockResolvedValue({
              docs: mockOrphanedExpenses,
              size: mockOrphanedExpenses.length
            })
          };
        }
        
        if (collectionName === COLLECTIONS.EXHIBITIONS) {
          return {
            doc: jest.fn(() => ({
              get: jest.fn().mockResolvedValue({
                exists: false
              })
            }))
          };
        }
        
        return {
          get: jest.fn().mockResolvedValue({ docs: [], size: 0 })
        };
      });

      const result = await integrityChecker.runIntegrityCheck({
        collections: ['expense_records'],
        includeOrphanCheck: true
      });

      expect(result.summary.totalIssues).toBeGreaterThan(0);
    });
  });

  describe('Orphaned Record Cleanup', () => {
    test('should provide cleanup suggestions for orphaned records', async () => {
      const mockOrphanedTasks = [
        {
          id: 'task-1',
          data: () => ({
            exhibitionId: 'missing-exhibition',
            title: 'Orphaned Task',
            tenantId: testTenantId
          })
        }
      ];

      const { db } = require('@/lib/firebase');
      
      db.collection.mockImplementation((collectionName: string) => {
        if (collectionName === COLLECTIONS.EXHIBITION_TASKS) {
          return {
            get: jest.fn().mockResolvedValue({
              docs: mockOrphanedTasks,
              size: mockOrphanedTasks.length
            })
          };
        }
        
        if (collectionName === COLLECTIONS.EXHIBITIONS) {
          return {
            doc: jest.fn(() => ({
              get: jest.fn().mockResolvedValue({
                exists: false
              })
            }))
          };
        }
        
        return {
          get: jest.fn().mockResolvedValue({ docs: [], size: 0 })
        };
      });

      const result = await integrityChecker.runIntegrityCheck({
        collections: [COLLECTIONS.EXHIBITION_TASKS],
        includeOrphanCheck: true,
        autoFix: false
      });

      // Should provide suggestions for fixing orphaned records
      expect(result.collectionResults.length).toBeGreaterThan(0);
      const taskResult = result.collectionResults.find(r => r.collectionName === COLLECTIONS.EXHIBITION_TASKS);
      expect(taskResult?.issues.some(issue => issue.type === 'orphaned_document')).toBe(true);
    });

    test('should handle auto-fix for orphaned records when enabled', async () => {
      const mockOrphanedTasks = [
        {
          id: 'task-1',
          ref: { delete: jest.fn() },
          data: () => ({
            exhibitionId: 'missing-exhibition',
            title: 'Orphaned Task',
            tenantId: testTenantId
          })
        }
      ];

      const { db } = require('@/lib/firebase');
      
      db.collection.mockImplementation((collectionName: string) => {
        if (collectionName === COLLECTIONS.EXHIBITION_TASKS) {
          return {
            get: jest.fn().mockResolvedValue({
              docs: mockOrphanedTasks,
              size: mockOrphanedTasks.length
            })
          };
        }
        
        if (collectionName === COLLECTIONS.EXHIBITIONS) {
          return {
            doc: jest.fn(() => ({
              get: jest.fn().mockResolvedValue({
                exists: false
              })
            }))
          };
        }
        
        return {
          get: jest.fn().mockResolvedValue({ docs: [], size: 0 })
        };
      });

      const result = await integrityChecker.runIntegrityCheck({
        collections: [COLLECTIONS.EXHIBITION_TASKS],
        includeOrphanCheck: true,
        autoFix: true,
        dryRun: false
      });

      // Should attempt to fix orphaned records
      expect(result.summary.totalIssues).toBeGreaterThan(0);
    });
  });

  describe('Cross-Collection Referential Integrity', () => {
    test('should validate complex referential relationships', async () => {
      // Mock a complex scenario with multiple levels of references
      const mockExhibition = {
        id: 'exhibition-1',
        data: () => ({ id: 'exhibition-1', tenantId: testTenantId })
      };

      const mockEvent = {
        id: 'event-1',
        data: () => ({ 
          id: 'event-1', 
          exhibitionId: 'exhibition-1', 
          tenantId: testTenantId 
        })
      };

      const mockTaskWithValidEvent = {
        id: 'task-1',
        data: () => ({ 
          id: 'task-1', 
          eventId: 'event-1', 
          exhibitionId: 'exhibition-1',
          tenantId: testTenantId 
        })
      };

      const mockTaskWithInvalidEvent = {
        id: 'task-2',
        data: () => ({ 
          id: 'task-2', 
          eventId: 'non-existent-event', 
          exhibitionId: 'exhibition-1',
          tenantId: testTenantId 
        })
      };

      const { db } = require('@/lib/firebase');
      
      db.collection.mockImplementation((collectionName: string) => {
        if (collectionName === COLLECTIONS.EXHIBITIONS) {
          return {
            doc: jest.fn(() => ({
              get: jest.fn().mockResolvedValue({
                exists: true,
                data: () => mockExhibition.data()
              })
            })),
            get: jest.fn().mockResolvedValue({
              docs: [mockExhibition],
              size: 1
            })
          };
        }
        
        if (collectionName === COLLECTIONS.EXHIBITION_EVENTS) {
          return {
            doc: jest.fn((eventId: string) => ({
              get: jest.fn().mockResolvedValue({
                exists: eventId === 'event-1',
                data: () => eventId === 'event-1' ? mockEvent.data() : null
              })
            })),
            get: jest.fn().mockResolvedValue({
              docs: [mockEvent],
              size: 1
            })
          };
        }
        
        if (collectionName === COLLECTIONS.EXHIBITION_TASKS) {
          return {
            get: jest.fn().mockResolvedValue({
              docs: [mockTaskWithValidEvent, mockTaskWithInvalidEvent],
              size: 2
            })
          };
        }
        
        return {
          get: jest.fn().mockResolvedValue({ docs: [], size: 0 })
        };
      });

      const result = await integrityChecker.runIntegrityCheck({
        collections: [COLLECTIONS.EXHIBITION_TASKS],
        includeOrphanCheck: true
      });

      // Should detect the task with invalid event reference
      expect(result.summary.totalIssues).toBeGreaterThan(0);
    });
  });

  describe('Performance and Scalability', () => {
    test('should handle large datasets efficiently', async () => {
      // Mock large dataset with some orphaned records
      const mockLargeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: `task-${i}`,
        data: () => ({
          exhibitionId: i < 950 ? 'valid-exhibition' : `missing-exhibition-${i}`,
          title: `Task ${i}`,
          tenantId: testTenantId
        })
      }));

      const { db } = require('@/lib/firebase');
      
      db.collection.mockImplementation((collectionName: string) => {
        if (collectionName === COLLECTIONS.EXHIBITION_TASKS) {
          return {
            get: jest.fn().mockResolvedValue({
              docs: mockLargeDataset,
              size: mockLargeDataset.length
            })
          };
        }
        
        if (collectionName === COLLECTIONS.EXHIBITIONS) {
          return {
            doc: jest.fn((exhibitionId: string) => ({
              get: jest.fn().mockResolvedValue({
                exists: exhibitionId === 'valid-exhibition',
                data: () => exhibitionId === 'valid-exhibition' ? { id: exhibitionId, tenantId: testTenantId } : null
              })
            }))
          };
        }
        
        return {
          get: jest.fn().mockResolvedValue({ docs: [], size: 0 })
        };
      });

      const startTime = Date.now();
      const result = await integrityChecker.runIntegrityCheck({
        collections: [COLLECTIONS.EXHIBITION_TASKS],
        includeOrphanCheck: true
      });
      const duration = Date.now() - startTime;

      // Should complete within reasonable time
      expect(duration).toBeLessThan(10000); // 10 seconds for mock
      expect(result.summary.totalIssues).toBeGreaterThan(0);
    });
  });
});
