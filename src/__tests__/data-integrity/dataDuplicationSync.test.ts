/**
 * Data Duplication Sync Tests
 * Tests for data duplication synchronization mechanisms
 */

import { DataDuplicationService, DUPLICATION_RULES } from '@/services/dataDuplicationService';
import { COLLECTIONS } from '@/lib/collections';

// Mock Firebase
jest.mock('@/lib/firebase', () => ({
  db: {
    collection: jest.fn(() => ({
      doc: jest.fn(() => ({
        get: jest.fn(),
        set: jest.fn(),
        update: jest.fn()
      })),
      where: jest.fn(() => ({
        get: jest.fn().mockResolvedValue({
          docs: [],
          size: 0,
          empty: true
        })
      })),
      get: jest.fn().mockResolvedValue({
        docs: [],
        size: 0,
        empty: true
      })
    }))
  }
}));

jest.mock('firebase/firestore', () => ({
  collection: jest.fn(),
  doc: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  getDocs: jest.fn(),
  getDoc: jest.fn(),
  updateDoc: jest.fn(),
  writeBatch: jest.fn(() => ({
    update: jest.fn(),
    commit: jest.fn().mockResolvedValue(undefined)
  })),
  Timestamp: {
    fromDate: jest.fn((date) => date),
    now: jest.fn(() => new Date())
  },
  serverTimestamp: jest.fn()
}));

describe('Data Duplication Sync Tests', () => {
  const testTenantId = 'test-tenant-123';
  let duplicationService: DataDuplicationService;

  beforeEach(() => {
    jest.clearAllMocks();
    duplicationService = new DataDuplicationService(testTenantId);
  });

  describe('Exhibition Data Duplication', () => {
    test('should sync exhibition name changes to related documents', async () => {
      const exhibitionId = 'exhibition-123';
      const updatedFields = {
        name: 'Updated Exhibition Name',
        venue: 'New Venue Location'
      };

      // Mock related documents that store duplicated exhibition data
      const mockRelatedDocs = [
        {
          id: 'task-1',
          data: () => ({
            exhibitionId,
            exhibitionName: 'Old Exhibition Name',
            tenantId: testTenantId
          })
        },
        {
          id: 'event-1',
          data: () => ({
            exhibitionId,
            exhibitionName: 'Old Exhibition Name',
            tenantId: testTenantId
          })
        }
      ];

      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockRelatedDocs,
            size: mockRelatedDocs.length
          })
        }))
      });

      // Execute sync operation
      const results = await duplicationService.syncDuplicatedData(
        COLLECTIONS.EXHIBITIONS,
        exhibitionId,
        updatedFields
      );

      // Verify sync was attempted
      expect(results).toBeDefined();
      expect(Array.isArray(results)).toBe(true);
    });

    test('should handle exhibition venue changes', async () => {
      const exhibitionId = 'exhibition-456';
      const updatedFields = {
        venue: 'Convention Center Hall A',
        city: 'New York',
        country: 'USA'
      };

      const mockRelatedDocs = [
        {
          id: 'lead-1',
          data: () => ({
            exhibitionId,
            exhibitionVenue: 'Old Venue',
            tenantId: testTenantId
          })
        }
      ];

      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockRelatedDocs,
            size: mockRelatedDocs.length
          })
        }))
      });

      const results = await duplicationService.syncDuplicatedData(
        COLLECTIONS.EXHIBITIONS,
        exhibitionId,
        updatedFields
      );

      expect(results).toBeDefined();
    });

    test('should skip sync when no duplicated fields change', async () => {
      const exhibitionId = 'exhibition-789';
      const updatedFields = {
        description: 'Updated description',
        status: 'active'
      };

      // These fields are not in the duplication rules, so no sync should occur
      const results = await duplicationService.syncDuplicatedData(
        COLLECTIONS.EXHIBITIONS,
        exhibitionId,
        updatedFields
      );

      expect(results).toHaveLength(0);
    });
  });

  describe('User Data Duplication', () => {
    test('should sync user display name changes', async () => {
      const userId = 'user-123';
      const updatedFields = {
        displayName: 'Updated User Name',
        email: '<EMAIL>'
      };

      const mockRelatedDocs = [
        {
          id: 'task-1',
          data: () => ({
            assignedTo: userId,
            assignedToName: 'Old User Name',
            tenantId: testTenantId
          })
        },
        {
          id: 'event-1',
          data: () => ({
            organizer: userId,
            organizerName: 'Old User Name',
            tenantId: testTenantId
          })
        }
      ];

      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockRelatedDocs,
            size: mockRelatedDocs.length
          })
        }))
      });

      const results = await duplicationService.syncDuplicatedData(
        COLLECTIONS.USER_PROFILES,
        userId,
        updatedFields
      );

      expect(results).toBeDefined();
      expect(Array.isArray(results)).toBe(true);
    });

    test('should handle user profile updates across multiple collections', async () => {
      const userId = 'user-456';
      const updatedFields = {
        displayName: 'John Smith',
        avatar: 'https://example.com/avatar.jpg'
      };

      // Mock multiple collections that reference user data
      const mockCollections = {
        exhibition_tasks: [
          { id: 'task-1', data: () => ({ assignedTo: userId, tenantId: testTenantId }) }
        ],
        exhibition_events: [
          { id: 'event-1', data: () => ({ organizer: userId, tenantId: testTenantId }) }
        ],
        expense_records: [
          { id: 'expense-1', data: () => ({ submittedBy: userId, tenantId: testTenantId }) }
        ]
      };

      const { db } = require('@/lib/firebase');
      db.collection.mockImplementation((collectionName: string) => ({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockCollections[collectionName as keyof typeof mockCollections] || [],
            size: mockCollections[collectionName as keyof typeof mockCollections]?.length || 0
          })
        }))
      }));

      const results = await duplicationService.syncDuplicatedData(
        COLLECTIONS.USER_PROFILES,
        userId,
        updatedFields
      );

      expect(results).toBeDefined();
    });
  });

  describe('Vendor Data Duplication', () => {
    test('should sync vendor company name changes', async () => {
      const vendorId = 'vendor-123';
      const updatedFields = {
        companyName: 'Updated Vendor Company',
        contactEmail: '<EMAIL>'
      };

      const mockRelatedDocs = [
        {
          id: 'purchase-1',
          data: () => ({
            vendorId,
            vendorName: 'Old Vendor Company',
            tenantId: testTenantId
          })
        }
      ];

      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockRelatedDocs,
            size: mockRelatedDocs.length
          })
        }))
      });

      const results = await duplicationService.syncDuplicatedData(
        'vendor_profiles',
        vendorId,
        updatedFields
      );

      expect(results).toBeDefined();
    });
  });

  describe('Duplication Rule Management', () => {
    test('should validate duplication rules configuration', () => {
      // Verify all duplication rules have required properties
      Object.entries(DUPLICATION_RULES).forEach(([ruleKey, rule]) => {
        expect(rule.sourceCollection).toBeDefined();
        expect(rule.sourceField || rule.sourceFields).toBeDefined();
        expect(rule.targetCollections).toBeDefined();
        expect(Array.isArray(rule.targetCollections)).toBe(true);
        expect(rule.targetCollections.length).toBeGreaterThan(0);
        expect(rule.description).toBeDefined();
        expect(typeof rule.enabled).toBe('boolean');
      });
    });

    test('should handle disabled duplication rules', async () => {
      // Mock a scenario where a rule is disabled
      const originalRule = DUPLICATION_RULES.exhibitionName;
      DUPLICATION_RULES.exhibitionName = { ...originalRule, enabled: false };

      const exhibitionId = 'exhibition-123';
      const updatedFields = { name: 'New Name' };

      const results = await duplicationService.syncDuplicatedData(
        COLLECTIONS.EXHIBITIONS,
        exhibitionId,
        updatedFields
      );

      // Should return empty results for disabled rules
      expect(results).toHaveLength(0);

      // Restore original rule
      DUPLICATION_RULES.exhibitionName = originalRule;
    });

    test('should validate target collection configurations', () => {
      Object.entries(DUPLICATION_RULES).forEach(([ruleKey, rule]) => {
        rule.targetCollections.forEach(target => {
          expect(target.collection).toBeDefined();
          expect(target.targetField).toBeDefined();
          expect(target.relationshipField).toBeDefined();
        });
      });
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle database connection errors gracefully', async () => {
      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockRejectedValue(new Error('Database connection failed'))
        }))
      });

      const exhibitionId = 'exhibition-123';
      const updatedFields = { name: 'New Name' };

      // Should not throw error
      await expect(
        duplicationService.syncDuplicatedData(COLLECTIONS.EXHIBITIONS, exhibitionId, updatedFields)
      ).resolves.not.toThrow();
    });

    test('should handle empty result sets', async () => {
      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: [],
            size: 0,
            empty: true
          })
        }))
      });

      const exhibitionId = 'exhibition-123';
      const updatedFields = { name: 'New Name' };

      const results = await duplicationService.syncDuplicatedData(
        COLLECTIONS.EXHIBITIONS,
        exhibitionId,
        updatedFields
      );

      expect(results).toBeDefined();
      expect(Array.isArray(results)).toBe(true);
    });

    test('should handle batch operation failures', async () => {
      const { writeBatch } = require('firebase/firestore');
      writeBatch.mockReturnValue({
        update: jest.fn(),
        commit: jest.fn().mockRejectedValue(new Error('Batch commit failed'))
      });

      const exhibitionId = 'exhibition-123';
      const updatedFields = { name: 'New Name' };

      // Should handle batch failures gracefully
      await expect(
        duplicationService.syncDuplicatedData(COLLECTIONS.EXHIBITIONS, exhibitionId, updatedFields)
      ).resolves.not.toThrow();
    });

    test('should validate tenant isolation in sync operations', async () => {
      const exhibitionId = 'exhibition-123';
      const wrongTenantId = 'wrong-tenant-456';
      const updatedFields = { name: 'New Name' };

      // Mock documents from different tenants
      const mockMixedDocs = [
        {
          id: 'task-1',
          data: () => ({ exhibitionId, tenantId: testTenantId }) // Correct tenant
        },
        {
          id: 'task-2',
          data: () => ({ exhibitionId, tenantId: wrongTenantId }) // Wrong tenant
        }
      ];

      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockMixedDocs.filter(doc => doc.data().tenantId === testTenantId),
            size: 1 // Only documents from correct tenant
          })
        }))
      });

      const results = await duplicationService.syncDuplicatedData(
        COLLECTIONS.EXHIBITIONS,
        exhibitionId,
        updatedFields
      );

      // Should only process documents from correct tenant
      expect(results).toBeDefined();
    });
  });

  describe('Performance and Optimization', () => {
    test('should handle large datasets efficiently', async () => {
      const exhibitionId = 'exhibition-123';
      const updatedFields = { name: 'New Name' };

      // Mock large dataset
      const mockLargeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: `doc-${i}`,
        data: () => ({
          exhibitionId,
          exhibitionName: 'Old Name',
          tenantId: testTenantId
        })
      }));

      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockLargeDataset,
            size: mockLargeDataset.length
          })
        }))
      });

      const startTime = Date.now();
      const results = await duplicationService.syncDuplicatedData(
        COLLECTIONS.EXHIBITIONS,
        exhibitionId,
        updatedFields
      );
      const duration = Date.now() - startTime;

      // Should complete within reasonable time (less than 5 seconds for mock)
      expect(duration).toBeLessThan(5000);
      expect(results).toBeDefined();
    });

    test('should batch updates efficiently', async () => {
      const exhibitionId = 'exhibition-123';
      const updatedFields = { name: 'New Name' };

      // Mock moderate dataset that requires batching
      const mockDataset = Array.from({ length: 150 }, (_, i) => ({
        id: `doc-${i}`,
        data: () => ({
          exhibitionId,
          exhibitionName: 'Old Name',
          tenantId: testTenantId
        })
      }));

      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockDataset,
            size: mockDataset.length
          })
        }))
      });

      const results = await duplicationService.syncDuplicatedData(
        COLLECTIONS.EXHIBITIONS,
        exhibitionId,
        updatedFields
      );

      // Should handle batching appropriately
      expect(results).toBeDefined();
    });
  });
});
