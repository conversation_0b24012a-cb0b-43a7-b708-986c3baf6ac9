/**
 * Cloud Function Data Integrity Tests
 * Tests for cascade deletes, data duplication sync, and referential integrity
 */

import { DataIntegrityChecker } from '@/services/dataIntegrityChecker';
import { DataDuplicationService } from '@/services/dataDuplicationService';
import { COLLECTIONS } from '@/lib/collections';

// Mock Firebase Admin
jest.mock('firebase-admin', () => ({
  apps: [],
  initializeApp: jest.fn(),
  firestore: jest.fn(() => ({
    collection: jest.fn(),
    doc: jest.fn(),
    batch: jest.fn(() => ({
      delete: jest.fn(),
      update: jest.fn(),
      set: jest.fn(),
      commit: jest.fn().mockResolvedValue(undefined)
    })),
    FieldValue: {
      serverTimestamp: jest.fn(() => new Date()),
      delete: jest.fn()
    }
  }))
}));

// Mock Firestore
jest.mock('@/lib/firebase', () => ({
  db: {
    collection: jest.fn(() => ({
      doc: jest.fn(() => ({
        get: jest.fn(),
        set: jest.fn(),
        update: jest.fn(),
        delete: jest.fn()
      })),
      where: jest.fn(() => ({
        get: jest.fn().mockResolvedValue({
          docs: [],
          size: 0,
          empty: true
        })
      })),
      get: jest.fn().mockResolvedValue({
        docs: [],
        size: 0,
        empty: true
      })
    }))
  }
}));

describe('Cloud Function Data Integrity Tests', () => {
  const testTenantId = 'test-tenant-123';
  let integrityChecker: DataIntegrityChecker;
  let duplicationService: DataDuplicationService;

  beforeEach(() => {
    jest.clearAllMocks();
    integrityChecker = new DataIntegrityChecker(testTenantId);
    duplicationService = new DataDuplicationService(testTenantId);
  });

  describe('Cascade Delete Operations', () => {
    test('should cascade delete all related documents when exhibition is deleted', async () => {
      const exhibitionId = 'exhibition-123';
      const mockExhibitionData = {
        id: exhibitionId,
        tenantId: testTenantId,
        name: 'Test Exhibition',
        startDate: new Date(),
        endDate: new Date()
      };

      // Mock related documents that should be cascade deleted
      const mockRelatedDocs = {
        exhibition_events: [
          { id: 'event-1', exhibitionId, tenantId: testTenantId },
          { id: 'event-2', exhibitionId, tenantId: testTenantId }
        ],
        exhibition_tasks: [
          { id: 'task-1', exhibitionId, tenantId: testTenantId },
          { id: 'task-2', exhibitionId, tenantId: testTenantId }
        ],
        lead_contacts: [
          { id: 'lead-1', exhibitionId, tenantId: testTenantId }
        ],
        budget_allocations: [
          { id: 'budget-1', exhibitionId, tenantId: testTenantId }
        ],
        expense_records: [
          { id: 'expense-1', exhibitionId, tenantId: testTenantId }
        ]
      };

      // Mock Firestore queries to return related documents
      const { db } = require('@/lib/firebase');
      db.collection.mockImplementation((collectionName: string) => ({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockRelatedDocs[collectionName as keyof typeof mockRelatedDocs]?.map(doc => ({
              id: doc.id,
              ref: { delete: jest.fn() },
              data: () => doc
            })) || [],
            size: mockRelatedDocs[collectionName as keyof typeof mockRelatedDocs]?.length || 0
          })
        }))
      }));

      // Simulate the cascade delete function
      const cascadeCollections = [
        'exhibition_events',
        'exhibition_tasks',
        'lead_contacts',
        'budget_allocations',
        'expense_records'
      ];

      let totalDeletedDocs = 0;
      for (const collectionName of cascadeCollections) {
        const query = db.collection(collectionName).where('exhibitionId', '==', exhibitionId);
        const snapshot = await query.get();
        totalDeletedDocs += snapshot.size;
      }

      // Verify that all related documents would be deleted
      expect(totalDeletedDocs).toBe(6); // 2 events + 2 tasks + 1 lead + 1 budget + 1 expense
    });

    test('should handle cascade delete with tenant isolation', async () => {
      const exhibitionId = 'exhibition-123';
      const wrongTenantId = 'wrong-tenant-456';

      // Mock documents from different tenants
      const mockDocs = [
        { id: 'doc-1', exhibitionId, tenantId: testTenantId }, // Should be deleted
        { id: 'doc-2', exhibitionId, tenantId: wrongTenantId }, // Should NOT be deleted
        { id: 'doc-3', exhibitionId, tenantId: testTenantId }  // Should be deleted
      ];

      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockDocs
              .filter(doc => doc.tenantId === testTenantId) // Only return docs from correct tenant
              .map(doc => ({
                id: doc.id,
                ref: { delete: jest.fn() },
                data: () => doc
              })),
            size: 2 // Only 2 docs from correct tenant
          })
        }))
      });

      const query = db.collection('exhibition_tasks')
        .where('exhibitionId', '==', exhibitionId)
        .where('tenantId', '==', testTenantId);
      
      const snapshot = await query.get();
      
      // Verify only documents from correct tenant are included
      expect(snapshot.size).toBe(2);
    });

    test('should handle user deletion with reference cleanup', async () => {
      const userId = 'user-123';
      const mockUserData = {
        id: userId,
        tenantId: testTenantId,
        displayName: 'Test User'
      };

      // Mock documents that reference the user
      const mockReferencingDocs = [
        { id: 'task-1', assignedTo: userId, tenantId: testTenantId },
        { id: 'event-1', organizer: userId, tenantId: testTenantId },
        { id: 'expense-1', submittedBy: userId, tenantId: testTenantId }
      ];

      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockReferencingDocs.map(doc => ({
              id: doc.id,
              ref: { update: jest.fn() },
              data: () => doc
            })),
            size: mockReferencingDocs.length
          })
        }))
      });

      // Simulate user reference cleanup
      const collectionsWithUserRefs = [
        { collection: 'exhibition_tasks', field: 'assignedTo' },
        { collection: 'exhibition_events', field: 'organizer' },
        { collection: 'expense_records', field: 'submittedBy' }
      ];

      let totalUpdatedDocs = 0;
      for (const { collection: collectionName, field } of collectionsWithUserRefs) {
        const query = db.collection(collectionName)
          .where(field, '==', userId)
          .where('tenantId', '==', testTenantId);
        
        const snapshot = await query.get();
        totalUpdatedDocs += snapshot.size;
      }

      // Verify all referencing documents would be updated
      expect(totalUpdatedDocs).toBe(3);
    });
  });

  describe('Data Duplication Sync Mechanisms', () => {
    test('should sync exhibition name changes to related documents', async () => {
      const exhibitionId = 'exhibition-123';
      const oldName = 'Old Exhibition Name';
      const newName = 'New Exhibition Name';

      const updatedFields = {
        name: newName,
        updatedAt: new Date()
      };

      // Mock related documents that store duplicated exhibition name
      const mockRelatedDocs = [
        { id: 'task-1', exhibitionId, exhibitionName: oldName, tenantId: testTenantId },
        { id: 'event-1', exhibitionId, exhibitionName: oldName, tenantId: testTenantId },
        { id: 'lead-1', exhibitionId, exhibitionName: oldName, tenantId: testTenantId }
      ];

      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockRelatedDocs.map(doc => ({
              id: doc.id,
              ref: { update: jest.fn() },
              data: () => doc
            })),
            size: mockRelatedDocs.length
          })
        }))
      });

      // Simulate data duplication sync
      const results = await duplicationService.syncDuplicatedData(
        COLLECTIONS.EXHIBITIONS,
        exhibitionId,
        updatedFields
      );

      // Verify sync operation was attempted
      expect(results).toBeDefined();
      expect(Array.isArray(results)).toBe(true);
    });

    test('should sync user display name changes to related documents', async () => {
      const userId = 'user-123';
      const oldDisplayName = 'Old Name';
      const newDisplayName = 'New Name';

      const updatedFields = {
        displayName: newDisplayName,
        updatedAt: new Date()
      };

      // Mock documents that store duplicated user display name
      const mockRelatedDocs = [
        { id: 'task-1', assignedTo: userId, assignedToName: oldDisplayName, tenantId: testTenantId },
        { id: 'event-1', organizer: userId, organizerName: oldDisplayName, tenantId: testTenantId }
      ];

      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockRelatedDocs.map(doc => ({
              id: doc.id,
              ref: { update: jest.fn() },
              data: () => doc
            })),
            size: mockRelatedDocs.length
          })
        }))
      });

      // Simulate user display name sync
      const results = await duplicationService.syncDuplicatedData(
        COLLECTIONS.USER_PROFILES,
        userId,
        updatedFields
      );

      // Verify sync operation was attempted
      expect(results).toBeDefined();
      expect(Array.isArray(results)).toBe(true);
    });

    test('should handle sync failures gracefully', async () => {
      const exhibitionId = 'exhibition-123';
      const updatedFields = { name: 'New Name' };

      // Mock database error
      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockRejectedValue(new Error('Database connection failed'))
        }))
      });

      // Sync should handle errors gracefully
      await expect(
        duplicationService.syncDuplicatedData(COLLECTIONS.EXHIBITIONS, exhibitionId, updatedFields)
      ).resolves.not.toThrow();
    });
  });

  describe('Orphaned Record Detection', () => {
    test('should detect orphaned tasks without valid exhibitions', async () => {
      // Mock orphaned tasks
      const mockOrphanedTasks = [
        { id: 'task-1', exhibitionId: 'non-existent-exhibition', tenantId: testTenantId },
        { id: 'task-2', exhibitionId: 'another-missing-exhibition', tenantId: testTenantId }
      ];

      const { db } = require('@/lib/firebase');
      
      // Mock tasks collection to return orphaned tasks
      db.collection.mockImplementation((collectionName: string) => {
        if (collectionName === 'exhibition_tasks') {
          return {
            get: jest.fn().mockResolvedValue({
              docs: mockOrphanedTasks.map(task => ({
                id: task.id,
                data: () => task
              }))
            })
          };
        }
        
        // Mock exhibitions collection to return empty (no matching exhibitions)
        if (collectionName === 'exhibitions') {
          return {
            doc: jest.fn(() => ({
              get: jest.fn().mockResolvedValue({
                exists: false // Exhibition doesn't exist
              })
            }))
          };
        }
        
        return { get: jest.fn().mockResolvedValue({ docs: [], size: 0 }) };
      });

      // Run orphaned document check
      const result = await integrityChecker.runIntegrityCheck({
        collections: ['exhibition_tasks'],
        includeOrphanCheck: true
      });

      // Verify orphaned documents were detected
      expect(result.summary.totalIssues).toBeGreaterThan(0);
      expect(result.collectionResults.some(r => r.orphanedDocuments > 0)).toBe(true);
    });

    test('should detect documents missing tenantId', async () => {
      // Mock documents missing tenantId
      const mockDocsWithoutTenantId = [
        { id: 'doc-1', name: 'Document 1' }, // Missing tenantId
        { id: 'doc-2', name: 'Document 2' }, // Missing tenantId
        { id: 'doc-3', name: 'Document 3', tenantId: testTenantId } // Has tenantId
      ];

      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        get: jest.fn().mockResolvedValue({
          docs: mockDocsWithoutTenantId.map(doc => ({
            id: doc.id,
            data: () => doc
          }))
        })
      });

      // Run integrity check
      const result = await integrityChecker.runIntegrityCheck({
        collections: ['exhibitions'],
        expectedTenantId: testTenantId
      });

      // Verify missing tenantId issues were detected
      expect(result.summary.totalIssues).toBeGreaterThan(0);
      expect(result.collectionResults.some(r => r.missingTenantId > 0)).toBe(true);
    });

    test('should validate referential integrity across collections', async () => {
      // Mock data with broken references
      const mockTasks = [
        { id: 'task-1', exhibitionId: 'valid-exhibition', tenantId: testTenantId },
        { id: 'task-2', exhibitionId: 'invalid-exhibition', tenantId: testTenantId }
      ];

      const mockExhibitions = [
        { id: 'valid-exhibition', tenantId: testTenantId }
        // 'invalid-exhibition' is missing
      ];

      const { db } = require('@/lib/firebase');
      db.collection.mockImplementation((collectionName: string) => {
        if (collectionName === 'exhibition_tasks') {
          return {
            get: jest.fn().mockResolvedValue({
              docs: mockTasks.map(task => ({
                id: task.id,
                data: () => task
              }))
            })
          };
        }
        
        if (collectionName === 'exhibitions') {
          return {
            doc: jest.fn((docId: string) => ({
              get: jest.fn().mockResolvedValue({
                exists: mockExhibitions.some(ex => ex.id === docId),
                data: () => mockExhibitions.find(ex => ex.id === docId)
              })
            }))
          };
        }
        
        return { get: jest.fn().mockResolvedValue({ docs: [], size: 0 }) };
      });

      // Run referential integrity check
      const result = await integrityChecker.runIntegrityCheck({
        collections: ['exhibition_tasks'],
        includeOrphanCheck: true
      });

      // Should detect the broken reference
      expect(result.summary.totalIssues).toBeGreaterThan(0);
    });
  });

  describe('Data Consistency Validation', () => {
    test('should validate tenant isolation in all operations', async () => {
      const wrongTenantId = 'wrong-tenant-456';

      // Mock mixed tenant data
      const mockMixedData = [
        { id: 'doc-1', tenantId: testTenantId },
        { id: 'doc-2', tenantId: wrongTenantId },
        { id: 'doc-3', tenantId: testTenantId }
      ];

      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        get: jest.fn().mockResolvedValue({
          docs: mockMixedData.map(doc => ({
            id: doc.id,
            data: () => doc
          }))
        })
      });

      // Run integrity check with specific tenant
      const result = await integrityChecker.runIntegrityCheck({
        collections: ['exhibitions'],
        expectedTenantId: testTenantId
      });

      // Should detect documents with wrong tenantId
      expect(result.summary.totalIssues).toBeGreaterThan(0);
      expect(result.collectionResults.some(r => r.invalidTenantId > 0)).toBe(true);
    });

    test('should handle batch operation limits correctly', async () => {
      // Mock large number of documents (exceeding Firestore batch limit)
      const mockLargeDataset = Array.from({ length: 600 }, (_, i) => ({
        id: `doc-${i}`,
        tenantId: testTenantId,
        exhibitionId: 'exhibition-123'
      }));

      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockResolvedValue({
            docs: mockLargeDataset.map(doc => ({
              id: doc.id,
              ref: { delete: jest.fn() },
              data: () => doc
            })),
            size: mockLargeDataset.length
          })
        }))
      });

      // Simulate cascade delete with large dataset
      const query = db.collection('exhibition_tasks')
        .where('exhibitionId', '==', 'exhibition-123');

      const snapshot = await query.get();

      // Should handle documents exceeding batch limit (500)
      expect(snapshot.size).toBe(600);

      // In real implementation, this would be split into multiple batches
      const batchLimit = 500;
      const batchCount = Math.ceil(snapshot.size / batchLimit);
      expect(batchCount).toBe(2); // Should require 2 batches
    });

    test('should validate data duplication consistency', async () => {
      const exhibitionId = 'exhibition-123';
      const exhibitionName = 'Test Exhibition';

      // Mock exhibition document
      const mockExhibition = {
        id: exhibitionId,
        tenantId: testTenantId,
        name: exhibitionName
      };

      // Mock related documents with potentially inconsistent duplicated data
      const mockRelatedDocs = [
        { id: 'task-1', exhibitionId, exhibitionName, tenantId: testTenantId }, // Consistent
        { id: 'task-2', exhibitionId, exhibitionName: 'Old Name', tenantId: testTenantId }, // Inconsistent
        { id: 'event-1', exhibitionId, exhibitionName, tenantId: testTenantId } // Consistent
      ];

      const { db } = require('@/lib/firebase');
      db.collection.mockImplementation((collectionName: string) => {
        if (collectionName === 'exhibitions') {
          return {
            doc: jest.fn(() => ({
              get: jest.fn().mockResolvedValue({
                exists: true,
                data: () => mockExhibition
              })
            }))
          };
        }

        return {
          where: jest.fn(() => ({
            get: jest.fn().mockResolvedValue({
              docs: mockRelatedDocs.map(doc => ({
                id: doc.id,
                data: () => doc
              })),
              size: mockRelatedDocs.length
            })
          }))
        };
      });

      // Run duplication consistency check
      const result = await duplicationService.validateDuplicatedData('exhibitionName');

      // Should detect inconsistent duplicated data
      expect(result).toBeDefined();
    });
  });

  describe('Performance and Error Handling', () => {
    test('should handle Cloud Function timeouts gracefully', async () => {
      // Mock slow database operations
      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn().mockImplementation(() =>
            new Promise(resolve => setTimeout(resolve, 10000)) // 10 second delay
          )
        }))
      });

      // Set a reasonable timeout for the test
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Operation timeout')), 1000)
      );

      const operationPromise = integrityChecker.runIntegrityCheck({
        collections: ['exhibitions']
      });

      // Should handle timeout appropriately
      await expect(Promise.race([operationPromise, timeoutPromise]))
        .rejects.toThrow('Operation timeout');
    });

    test('should handle Firestore permission errors', async () => {
      // Mock permission denied error
      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        get: jest.fn().mockRejectedValue(
          new Error('Missing or insufficient permissions')
        )
      });

      // Should handle permission errors gracefully
      await expect(
        integrityChecker.runIntegrityCheck({ collections: ['exhibitions'] })
      ).resolves.not.toThrow();
    });

    test('should handle network connectivity issues', async () => {
      // Mock network error
      const { db } = require('@/lib/firebase');
      db.collection.mockReturnValue({
        get: jest.fn().mockRejectedValue(
          new Error('UNAVAILABLE: The service is currently unavailable')
        )
      });

      // Should handle network errors gracefully
      await expect(
        duplicationService.syncDuplicatedData(COLLECTIONS.EXHIBITIONS, 'test-id', { name: 'Test' })
      ).resolves.not.toThrow();
    });
  });
});
