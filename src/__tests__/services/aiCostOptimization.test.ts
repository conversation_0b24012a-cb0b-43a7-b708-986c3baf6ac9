/**
 * AI Cost Optimization Service Tests
 * Tests for AI cost management, caching, and optimization functionality
 */

import { aiCostOptimizationService } from '@/services/aiCostOptimizationService';
import { aiCacheStrategyService } from '@/services/aiCacheStrategyService';
import { aiCostPredictionService } from '@/services/aiCostPredictionService';

describe('AI Cost Optimization Service', () => {
  beforeEach(() => {
    // Reset service state before each test
    aiCostOptimizationService.clearCache();
  });

  describe('Cost Tracking', () => {
    it('should track AI usage costs correctly', async () => {
      const usageRecord = {
        service: 'workflow-automation',
        prompt: 'Analyze workflow efficiency',
        promptTokens: 50,
        completionTokens: 100,
        totalTokens: 150,
        cost: 0.015,
        cacheHit: false,
        responseTime: 1200,
        model: 'groq-llama',
        provider: 'Groq'
      };

      await aiCostOptimizationService.recordUsage(usageRecord);
      const analytics = await aiCostOptimizationService.getCostAnalytics();

      expect(analytics.totalCost).toBe(0.015);
      expect(analytics.totalRequests).toBe(1);
      expect(analytics.costByService['workflow-automation']).toBe(0.015);
    });

    it('should enforce daily cost limits', async () => {
      // Set low daily limit for testing
      const config = aiCostOptimizationService.getConfiguration();
      if (config) {
        config.maxDailyCost = 0.01;
        (aiCostOptimizationService as any).updateConfig(config);
      }

      const result = await aiCostOptimizationService.canMakeRequest('test-service', 0.02);
      
      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('Daily cost limit');
    });

    it('should allow requests within cost limits', async () => {
      const result = await aiCostOptimizationService.canMakeRequest('test-service', 0.001);
      
      expect(result.allowed).toBe(true);
    });
  });

  describe('Caching Functionality', () => {
    it('should cache AI responses correctly', () => {
      const service = 'budget-prediction';
      const prompt = 'Predict budget for Q4';
      const response = { prediction: '$50,000', confidence: 0.85 };

      aiCostOptimizationService.cacheResponse(service, prompt, response);
      const cachedResponse = aiCostOptimizationService.getCachedResponse(service, prompt);

      expect(cachedResponse).toEqual(response);
    });

    it('should respect cache TTL', (done) => {
      const service = 'test-service';
      const prompt = 'test prompt';
      const response = { result: 'test' };

      // Cache with very short TTL (1ms)
      aiCostOptimizationService.cacheResponse(service, prompt, response, undefined, 0.001);

      setTimeout(() => {
        const cachedResponse = aiCostOptimizationService.getCachedResponse(service, prompt);
        expect(cachedResponse).toBeNull();
        done();
      }, 100);
    });

    it('should calculate cache hit rate correctly', async () => {
      // Record some cache hits and misses
      await aiCostOptimizationService.recordUsage({
        service: 'test',
        prompt: 'test1',
        promptTokens: 10,
        completionTokens: 20,
        totalTokens: 30,
        cost: 0.003,
        cacheHit: true,
        responseTime: 50,
        model: 'test',
        provider: 'test'
      });

      await aiCostOptimizationService.recordUsage({
        service: 'test',
        prompt: 'test2',
        promptTokens: 10,
        completionTokens: 20,
        totalTokens: 30,
        cost: 0.003,
        cacheHit: false,
        responseTime: 1000,
        model: 'test',
        provider: 'test'
      });

      const analytics = await aiCostOptimizationService.getCostAnalytics();
      expect(analytics.cacheHitRate).toBe(0.5); // 50% hit rate
    });
  });

  describe('Cache Strategy Service', () => {
    it('should select optimal cache strategy based on service type', () => {
      const workflowStrategy = aiCacheStrategyService.getOptimalStrategy(
        'workflow-automation',
        'analyze workflow',
        {}
      );
      
      const budgetStrategy = aiCacheStrategyService.getOptimalStrategy(
        'budget-prediction',
        'predict budget',
        {}
      );

      expect(workflowStrategy.name).toBe('workflow_automation');
      expect(workflowStrategy.ttl).toBe(24 * 60); // 24 hours
      expect(budgetStrategy.name).toBe('budget_prediction');
      expect(budgetStrategy.ttl).toBe(12 * 60); // 12 hours
    });

    it('should generate smart cache keys', () => {
      const key1 = aiCacheStrategyService.generateSmartCacheKey(
        'test-service',
        'This is a test prompt',
        { userId: '123' }
      );
      
      const key2 = aiCacheStrategyService.generateSmartCacheKey(
        'test-service',
        'This is a test prompt',
        { userId: '123' }
      );

      expect(key1).toBe(key2); // Same inputs should generate same key
      expect(key1).toContain('test-service');
    });

    it('should adjust TTL based on context', () => {
      const timeSensitiveContext = { timestamp: Date.now(), current: true };
      const staticContext = { template: 'standard', reference: 'docs' };

      const timeSensitiveTTL = aiCacheStrategyService.getContextAwareTTL(
        'test-service',
        timeSensitiveContext
      );
      
      const staticTTL = aiCacheStrategyService.getContextAwareTTL(
        'test-service',
        staticContext
      );

      expect(timeSensitiveTTL).toBeLessThanOrEqual(30); // Max 30 minutes for time-sensitive
      expect(staticTTL).toBeGreaterThanOrEqual(240); // Min 4 hours for static
    });
  });

  describe('Cost Prediction Service', () => {
    it('should provide cost predictions with low confidence for insufficient data', async () => {
      const prediction = await aiCostPredictionService.predictCosts();
      
      expect(prediction.confidence).toBeLessThan(0.5);
      expect(prediction.factors).toContain('Insufficient historical data');
      expect(prediction.riskLevel).toBe('low');
    });

    it('should generate optimization recommendations', () => {
      const mockUsageHistory = [
        {
          id: '1',
          timestamp: new Date(),
          service: 'high-volume-service',
          prompt: 'test',
          promptTokens: 100,
          completionTokens: 200,
          totalTokens: 300,
          cost: 0.03,
          cacheHit: false,
          responseTime: 2000,
          model: 'expensive-model',
          provider: 'test'
        }
      ];

      const mockAnalytics = {
        dailyCost: 10,
        cacheHitRate: 0.3,
        topExpensiveQueries: mockUsageHistory
      };

      const recommendations = aiCostPredictionService.generateRecommendations(
        mockUsageHistory,
        mockAnalytics
      );

      expect(recommendations.length).toBeGreaterThan(0);
      expect(recommendations[0]).toHaveProperty('type');
      expect(recommendations[0]).toHaveProperty('potentialSavings');
      expect(recommendations[0]).toHaveProperty('implementationEffort');
    });
  });

  describe('Integration Tests', () => {
    it('should work together to optimize AI costs', async () => {
      // Simulate a workflow automation request
      const service = 'workflow-automation';
      const prompt = 'Analyze current workflow efficiency and suggest improvements';
      const context = { workflowId: '123', userId: 'user1' };

      // Get optimal caching strategy
      const strategy = aiCacheStrategyService.getOptimalStrategy(service, prompt, context);
      expect(strategy.name).toBe('workflow_automation');

      // Check if request is allowed
      const canMakeRequest = await aiCostOptimizationService.canMakeRequest(service, 0.01);
      expect(canMakeRequest.allowed).toBe(true);

      // Simulate caching the response
      const response = { analysis: 'Workflow is 85% efficient', suggestions: ['Automate step 3'] };
      const contextAwareTTL = aiCacheStrategyService.getContextAwareTTL(service, context);
      
      aiCostOptimizationService.cacheResponse(service, prompt, response, context, contextAwareTTL);
      aiCacheStrategyService.storeInSemanticCache(prompt, response);

      // Verify response is cached
      const cachedResponse = aiCostOptimizationService.getCachedResponse(service, prompt, context);
      expect(cachedResponse).toEqual(response);

      // Record usage
      await aiCostOptimizationService.recordUsage({
        service,
        prompt,
        promptTokens: 50,
        completionTokens: 100,
        totalTokens: 150,
        cost: 0.01,
        cacheHit: false,
        responseTime: 1500,
        model: 'groq-llama',
        provider: 'Groq'
      });

      // Check analytics
      const analytics = await aiCostOptimizationService.getCostAnalytics();
      expect(analytics.totalCost).toBe(0.01);
      expect(analytics.costByService[service]).toBe(0.01);
    });

    it('should handle semantic cache hits', async () => {
      const originalPrompt = 'Analyze workflow efficiency for project ABC';
      const similarPrompt = 'Analyze workflow performance for project ABC';
      const response = { efficiency: '85%', recommendations: ['Optimize step 2'] };

      // Store original response in semantic cache
      aiCacheStrategyService.storeInSemanticCache(originalPrompt, response);

      // Check for semantic similarity (this is simplified for testing)
      const semanticHit = await aiCacheStrategyService.checkSemanticCache(similarPrompt, 0.7);
      
      // In a real implementation with proper embeddings, this would work
      // For now, we just verify the method exists and doesn't throw
      expect(semanticHit).toBeDefined();
    });
  });

  describe('Performance Metrics', () => {
    it('should calculate optimization score correctly', () => {
      const mockData = {
        hitRate: 80,
        averageResponseTime: 500,
        costSavings: 5
      };

      const performanceMetrics = aiCacheStrategyService.getPerformanceMetrics();
      expect(performanceMetrics).toHaveProperty('hitRate');
      expect(performanceMetrics).toHaveProperty('averageResponseTime');
      expect(performanceMetrics).toHaveProperty('costSavings');
      expect(performanceMetrics).toHaveProperty('optimizationScore');
    });

    it('should provide cache performance insights', () => {
      const cacheStats = aiCostOptimizationService.getCacheStats();
      
      expect(cacheStats).toHaveProperty('totalSize');
      expect(cacheStats).toHaveProperty('totalEntries');
      expect(cacheStats).toHaveProperty('hitRate');
      expect(cacheStats).toHaveProperty('averageResponseTime');
    });
  });
});

describe('Error Handling', () => {
  it('should handle invalid cache operations gracefully', () => {
    expect(() => {
      aiCostOptimizationService.cacheResponse('', '', null);
    }).not.toThrow();
  });

  it('should handle cost prediction errors gracefully', async () => {
    // This should not throw even with no data
    const prediction = await aiCostPredictionService.predictCosts();
    expect(prediction).toBeDefined();
    expect(prediction.riskLevel).toBeDefined();
  });

  it('should handle cache strategy errors gracefully', () => {
    expect(() => {
      aiCacheStrategyService.getOptimalStrategy('', '', undefined);
    }).not.toThrow();
  });
});
