/**
 * Comprehensive Subscription Limit Testing
 * Tests user limit enforcement, invitation blocking, and upgrade prompts
 */

import { SubscriptionEnforcementService } from '@/services/subscriptionEnforcementService';
import { SubscriptionService } from '@/services/subscriptionService';
import { InvitationManagementService } from '@/services/invitationManagementService';
import { SUBSCRIPTION_TIERS } from '@/lib/subscription-limits';
import { db } from '@/lib/firebase';

// Mock Firebase
jest.mock('@/lib/firebase', () => ({
  db: {}
}));

jest.mock('firebase/firestore', () => ({
  collection: jest.fn(),
  doc: jest.fn(),
  getDocs: jest.fn(),
  getDoc: jest.fn(),
  addDoc: jest.fn(),
  updateDoc: jest.fn(),
  deleteDoc: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  orderBy: jest.fn(),
  limit: jest.fn(),
  writeBatch: jest.fn(() => ({
    delete: jest.fn(),
    commit: jest.fn()
  })),
  Timestamp: {
    fromDate: jest.fn((date) => date),
    now: jest.fn(() => new Date())
  },
  serverTimestamp: jest.fn()
}));

// Mock services
jest.mock('@/services/subscriptionService');
jest.mock('@/services/invitationManagementService');
jest.mock('@/services/tenantPersonaService');
jest.mock('@/services/tenantIdHelperService');

describe('Subscription Limit Testing', () => {
  const testTenantId = 'test-tenant-123';
  let enforcementService: SubscriptionEnforcementService;
  let subscriptionService: jest.Mocked<SubscriptionService>;
  let invitationService: jest.Mocked<InvitationManagementService>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create mocked services
    subscriptionService = new SubscriptionService(testTenantId) as jest.Mocked<SubscriptionService>;
    invitationService = new InvitationManagementService() as jest.Mocked<InvitationManagementService>;
    enforcementService = new SubscriptionEnforcementService(testTenantId);
  });

  describe('User Limit Enforcement - Basic Plan', () => {
    beforeEach(() => {
      // Mock Basic plan subscription
      subscriptionService.getTenantSubscription.mockResolvedValue({
        id: 'sub-123',
        tenantId: testTenantId,
        planId: 'basic',
        status: 'active',
        billing: {
          interval: 'monthly',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        },
        createdAt: new Date(),
        updatedAt: new Date()
      });

      subscriptionService.getPlan.mockResolvedValue({
        id: 'basic',
        name: 'Basic',
        price: 2900,
        features: {
          users: 10,
          exhibitions: 25,
          events: 50,
          storage: 1000,
          customBranding: false,
          advancedAnalytics: false
        },
        limits: {
          users: 10,
          exhibitions: 25,
          events: 50
        }
      });
    });

    it('should allow user creation when under limit', async () => {
      // Mock 5 current users (under 10 limit)
      const { getDocs } = require('firebase/firestore');
      getDocs.mockResolvedValue({
        size: 5,
        docs: Array(5).fill(null).map((_, i) => ({
          id: `user-${i}`,
          data: () => ({ status: 'active' })
        }))
      });

      const result = await enforcementService.checkUserLimit(1);

      expect(result.allowed).toBe(true);
      expect(result.currentUsers).toBe(5);
      expect(result.maxUsers).toBe(10);
      expect(result.availableSlots).toBe(5);
    });

    it('should block user creation when at limit', async () => {
      // Mock 10 current users (at 10 limit)
      const { getDocs } = require('firebase/firestore');
      getDocs.mockResolvedValue({
        size: 10,
        docs: Array(10).fill(null).map((_, i) => ({
          id: `user-${i}`,
          data: () => ({ status: 'active' })
        }))
      });

      const result = await enforcementService.checkUserLimit(1);

      expect(result.allowed).toBe(false);
      expect(result.currentUsers).toBe(10);
      expect(result.maxUsers).toBe(10);
      expect(result.availableSlots).toBe(0);
      expect(result.upgradeRequired).toBe(true);
      expect(result.suggestedPlan).toBeDefined();
    });

    it('should block bulk user creation when would exceed limit', async () => {
      // Mock 8 current users, trying to add 5 more (would exceed 10 limit)
      const { getDocs } = require('firebase/firestore');
      getDocs.mockResolvedValue({
        size: 8,
        docs: Array(8).fill(null).map((_, i) => ({
          id: `user-${i}`,
          data: () => ({ status: 'active' })
        }))
      });

      const result = await enforcementService.checkUserLimit(5);

      expect(result.allowed).toBe(false);
      expect(result.currentUsers).toBe(8);
      expect(result.maxUsers).toBe(10);
      expect(result.availableSlots).toBe(2);
      expect(result.upgradeRequired).toBe(true);
    });

    it('should show warning when near limit (80%)', async () => {
      // Mock 8 current users (80% of 10 limit)
      const { getDocs } = require('firebase/firestore');
      getDocs.mockResolvedValue({
        size: 8,
        docs: Array(8).fill(null).map((_, i) => ({
          id: `user-${i}`,
          data: () => ({ status: 'active' })
        }))
      });

      const action = await enforcementService.enforceUserLimit(1);

      expect(action.type).toBe('warn');
      expect(action.message).toContain('80%');
      expect(action.upgradeUrl).toBeDefined();
    });

    it('should include pending invitations in user count when specified', async () => {
      // Mock 7 active users + 2 pending invitations = 9 total (under 10 limit)
      const { getDocs } = require('firebase/firestore');
      getDocs
        .mockResolvedValueOnce({
          size: 7,
          docs: Array(7).fill(null).map((_, i) => ({
            id: `user-${i}`,
            data: () => ({ status: 'active' })
          }))
        })
        .mockResolvedValueOnce({
          size: 2,
          docs: Array(2).fill(null).map((_, i) => ({
            id: `invitation-${i}`,
            data: () => ({ status: 'pending' })
          }))
        });

      const breakdown = await enforcementService.getUserUsageBreakdown({ includePending: true });

      expect(breakdown.activeUsers).toBe(7);
      expect(breakdown.pendingInvitations).toBe(2);
      expect(breakdown.totalSlots).toBe(10);
      expect(breakdown.availableSlots).toBe(1);
      expect(breakdown.usagePercentage).toBe(90);
      expect(breakdown.isNearLimit).toBe(true);
      expect(breakdown.isAtLimit).toBe(false);
    });
  });

  describe('User Limit Enforcement - Professional Plan', () => {
    beforeEach(() => {
      // Mock Professional plan subscription
      subscriptionService.getTenantSubscription.mockResolvedValue({
        id: 'sub-456',
        tenantId: testTenantId,
        planId: 'professional',
        status: 'active',
        billing: {
          interval: 'monthly',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        },
        createdAt: new Date(),
        updatedAt: new Date()
      });

      subscriptionService.getPlan.mockResolvedValue({
        id: 'professional',
        name: 'Professional',
        price: 7900,
        features: {
          users: 10,
          exhibitions: 100,
          events: 200,
          storage: 5000,
          customBranding: true,
          advancedAnalytics: true
        },
        limits: {
          users: 10,
          exhibitions: 100,
          events: 200
        }
      });
    });

    it('should allow user creation when under limit', async () => {
      // Mock 6 current users (under 10 limit)
      const { getDocs } = require('firebase/firestore');
      getDocs.mockResolvedValue({
        size: 6,
        docs: Array(6).fill(null).map((_, i) => ({
          id: `user-${i}`,
          data: () => ({ status: 'active' })
        }))
      });

      const result = await enforcementService.checkUserLimit(1);

      expect(result.allowed).toBe(true);
      expect(result.currentUsers).toBe(6);
      expect(result.maxUsers).toBe(10);
      expect(result.availableSlots).toBe(4);
    });

    it('should block user creation when at limit', async () => {
      // Mock 10 current users (at 10 limit)
      const { getDocs } = require('firebase/firestore');
      getDocs.mockResolvedValue({
        size: 10,
        docs: Array(10).fill(null).map((_, i) => ({
          id: `user-${i}`,
          data: () => ({ status: 'active' })
        }))
      });

      const result = await enforcementService.checkUserLimit(1);

      expect(result.allowed).toBe(false);
      expect(result.currentUsers).toBe(10);
      expect(result.maxUsers).toBe(10);
      expect(result.availableSlots).toBe(0);
      expect(result.upgradeRequired).toBe(true);
      expect(result.suggestedPlan).toBe('enterprise');
    });
  });
});
