/**
 * Comprehensive Performance Tests
 * Automated performance testing suite for query optimization and scalability validation
 */

import { performanceTestingService } from '@/services/performanceTestingService';
import { indexingStrategyTestService, performanceBenchmarkService } from '@/services/indexingStrategyTestService';

// Mock Firebase and authentication
jest.mock('@/lib/firebase', () => ({
  db: {},
  auth: {}
}));

jest.mock('firebase/firestore', () => ({
  collection: jest.fn(),
  doc: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  orderBy: jest.fn(),
  limit: jest.fn(),
  startAfter: jest.fn(),
  getDocs: jest.fn().mockResolvedValue({
    docs: Array.from({ length: 20 }, (_, i) => ({
      id: `doc-${i}`,
      data: () => ({
        id: `doc-${i}`,
        tenantId: 'test-tenant-id',
        name: `Test Document ${i}`,
        status: 'active',
        createdAt: new Date(),
        testData: true
      })
    })),
    size: 20
  }),
  getDoc: jest.fn().mockResolvedValue({ exists: () => true, data: () => ({}) }),
  addDoc: jest.fn().mockResolvedValue({ id: 'test-doc-id' }),
  writeBatch: jest.fn().mockReturnValue({
    set: jest.fn(),
    commit: jest.fn().mockResolvedValue(undefined)
  }),
  serverTimestamp: jest.fn(),
  enableNetwork: jest.fn(),
  disableNetwork: jest.fn()
}));

jest.mock('@/services/tenantIdHelperService', () => ({
  validateTenantId: jest.fn()
}));

jest.mock('@/services/performanceOptimizedQueryService', () => ({
  performanceOptimizedQueryService: {
    getOptimizedList: jest.fn().mockResolvedValue({
      items: Array.from({ length: 20 }, (_, i) => ({
        id: `doc-${i}`,
        name: `Test Document ${i}`,
        status: 'active'
      })),
      hasMore: false,
      fetchTime: 150,
      cacheHit: false
    })
  }
}));

describe('Comprehensive Performance Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock performance.now() for consistent timing
    let mockTime = 0;
    global.performance = {
      now: jest.fn(() => {
        mockTime += 100; // Consistent 100ms increments
        return mockTime;
      })
    } as any;
  });

  describe('Flat Collection Performance', () => {
    it('should test flat collection query performance', async () => {
      const result = await performanceTestingService.testFlatCollectionPerformance();
      
      expect(result).toBeDefined();
      expect(result.testName).toBe('Flat Collection Query Performance');
      expect(typeof result.passed).toBe('boolean');
      expect(typeof result.duration).toBe('number');
      expect(result.metrics).toBeDefined();
      expect(result.metrics.queryTime).toBeDefined();
      expect(result.metrics.documentsRead).toBeDefined();
      
      if (!result.passed) {
        console.warn('Flat collection performance test failed:', result.error);
        expect(result.recommendations).toBeDefined();
        expect(Array.isArray(result.recommendations)).toBe(true);
      }
    });

    it('should validate query time thresholds', async () => {
      const result = await performanceTestingService.testFlatCollectionPerformance();
      
      if (result.passed) {
        expect(result.metrics.queryTime).toBeLessThan(1000); // 1 second threshold
        expect(result.metrics.documentsRead).toBeLessThanOrEqual(100);
      }
    });
  });

  describe('Multi-Tenant Query Performance', () => {
    it('should test multi-tenant query isolation performance', async () => {
      const result = await performanceTestingService.testMultiTenantQueryPerformance();
      
      expect(result).toBeDefined();
      expect(result.testName).toBe('Multi-Tenant Query Performance');
      expect(typeof result.passed).toBe('boolean');
      expect(result.metrics).toBeDefined();
      expect(result.metrics.queryTime).toBeDefined();
      expect(result.metrics.networkRequests).toBeDefined();
      
      if (!result.passed) {
        console.warn('Multi-tenant query performance test failed:', result.error);
      }
    });

    it('should ensure tenant isolation does not impact performance', async () => {
      const result = await performanceTestingService.testMultiTenantQueryPerformance();
      
      if (result.passed) {
        expect(result.metrics.queryTime).toBeLessThan(500); // Average query time threshold
      }
    });
  });

  describe('Pagination Performance', () => {
    it('should test pagination query performance', async () => {
      const result = await performanceTestingService.testPaginationPerformance();
      
      expect(result).toBeDefined();
      expect(result.testName).toBe('Pagination Performance');
      expect(typeof result.passed).toBe('boolean');
      expect(result.metrics).toBeDefined();
      expect(result.metrics.queryTime).toBeDefined();
      expect(result.metrics.networkRequests).toBeDefined();
      
      if (!result.passed) {
        console.warn('Pagination performance test failed:', result.error);
      }
    });

    it('should maintain consistent pagination performance', async () => {
      const result = await performanceTestingService.testPaginationPerformance();
      
      if (result.passed) {
        expect(result.metrics.queryTime).toBeLessThan(300); // Average pagination time
      }
    });
  });

  describe('Complex Query Performance', () => {
    it('should test complex query performance', async () => {
      const result = await performanceTestingService.testComplexQueryPerformance();
      
      expect(result).toBeDefined();
      expect(result.testName).toBe('Complex Query Performance');
      expect(typeof result.passed).toBe('boolean');
      expect(result.metrics).toBeDefined();
      expect(result.metrics.queryTime).toBeDefined();
      expect(result.metrics.documentsRead).toBeDefined();
      
      if (!result.passed) {
        console.warn('Complex query performance test failed:', result.error);
      }
    });

    it('should handle complex queries within acceptable time limits', async () => {
      const result = await performanceTestingService.testComplexQueryPerformance();
      
      if (result.passed) {
        expect(result.metrics.queryTime).toBeLessThan(1500); // Complex query threshold
      }
    });
  });

  describe('Scalability Testing', () => {
    it('should test scalability with increasing data sizes', async () => {
      const results = await performanceTestingService.testScalability();
      
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBeGreaterThan(0);
      
      results.forEach(result => {
        expect(result).toHaveProperty('testName');
        expect(result).toHaveProperty('dataSize');
        expect(result).toHaveProperty('queryTime');
        expect(result).toHaveProperty('throughput');
        expect(result).toHaveProperty('scalabilityScore');
        expect(result).toHaveProperty('passed');
        
        expect(typeof result.dataSize).toBe('number');
        expect(typeof result.queryTime).toBe('number');
        expect(typeof result.throughput).toBe('number');
        expect(typeof result.scalabilityScore).toBe('number');
        expect(typeof result.passed).toBe('boolean');
        
        if (!result.passed) {
          console.warn(`Scalability test failed for ${result.dataSize} documents:`, result);
        }
      });
    });

    it('should maintain performance as data size increases', async () => {
      const results = await performanceTestingService.testScalability();
      
      // Check that performance doesn't degrade too much with larger datasets
      const sortedResults = results.sort((a, b) => a.dataSize - b.dataSize);
      
      if (sortedResults.length >= 2) {
        const smallestDataset = sortedResults[0];
        const largestDataset = sortedResults[sortedResults.length - 1];
        
        // Query time shouldn't increase more than 5x for 10x data increase
        const dataIncreaseFactor = largestDataset.dataSize / smallestDataset.dataSize;
        const timeIncreaseFactor = largestDataset.queryTime / smallestDataset.queryTime;
        
        if (dataIncreaseFactor > 1) {
          expect(timeIncreaseFactor).toBeLessThan(dataIncreaseFactor * 0.5);
        }
      }
    });
  });

  describe('Indexing Strategy Testing', () => {
    it('should test indexing strategies for all collections', async () => {
      const results = await indexingStrategyTestService.testAllIndexes('test-tenant-id');
      
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBeGreaterThan(0);
      
      results.forEach(result => {
        expect(result).toHaveProperty('indexName');
        expect(result).toHaveProperty('collection');
        expect(result).toHaveProperty('fields');
        expect(result).toHaveProperty('testResults');
        expect(result).toHaveProperty('performance');
        expect(result).toHaveProperty('recommendations');
        
        expect(Array.isArray(result.fields)).toBe(true);
        expect(Array.isArray(result.recommendations)).toBe(true);
        expect(['excellent', 'good', 'poor', 'critical']).toContain(result.performance);
        
        if (result.performance === 'critical') {
          console.warn(`Critical index performance for ${result.indexName}:`, result);
        }
      });
    });

    it('should validate index configuration recommendations', async () => {
      const validation = await indexingStrategyTestService.validateIndexConfiguration();
      
      expect(validation).toBeDefined();
      expect(validation).toHaveProperty('missingIndexes');
      expect(validation).toHaveProperty('redundantIndexes');
      expect(validation).toHaveProperty('recommendations');
      
      expect(Array.isArray(validation.missingIndexes)).toBe(true);
      expect(Array.isArray(validation.redundantIndexes)).toBe(true);
      expect(Array.isArray(validation.recommendations)).toBe(true);
      
      if (validation.missingIndexes.length > 0) {
        console.warn('Missing indexes detected:', validation.missingIndexes);
      }
    });

    it('should generate proper index configuration', () => {
      const config = indexingStrategyTestService.generateIndexConfiguration();
      
      expect(config).toBeDefined();
      expect(config).toHaveProperty('indexes');
      expect(config).toHaveProperty('fieldOverrides');
      
      expect(Array.isArray(config.indexes)).toBe(true);
      expect(Array.isArray(config.fieldOverrides)).toBe(true);
      
      config.indexes.forEach((index: any) => {
        expect(index).toHaveProperty('collectionGroup');
        expect(index).toHaveProperty('queryScope');
        expect(index).toHaveProperty('fields');
        expect(Array.isArray(index.fields)).toBe(true);
        
        index.fields.forEach((field: any) => {
          expect(field).toHaveProperty('fieldPath');
          expect(field).toHaveProperty('order');
          expect(['ASCENDING', 'DESCENDING']).toContain(field.order);
        });
      });
    });
  });

  describe('Performance Benchmarking', () => {
    it('should benchmark flat vs nested collection performance', async () => {
      const benchmark = await performanceBenchmarkService.benchmarkFlatVsNested('test-tenant-id');
      
      expect(benchmark).toBeDefined();
      expect(benchmark).toHaveProperty('flatCollection');
      expect(benchmark).toHaveProperty('nestedCollection');
      expect(benchmark).toHaveProperty('improvement');
      
      expect(typeof benchmark.improvement).toBe('number');
      expect(benchmark.flatCollection.queryTime).toBeLessThan(benchmark.nestedCollection.queryTime);
      expect(benchmark.improvement).toBeGreaterThan(0);
      
      console.log(`Flat collections are ${benchmark.improvement.toFixed(1)}% faster than nested`);
    });

    it('should benchmark data duplication vs joins performance', async () => {
      const benchmark = await performanceBenchmarkService.benchmarkDataDuplicationVsJoins('test-tenant-id');
      
      expect(benchmark).toBeDefined();
      expect(benchmark).toHaveProperty('withDuplication');
      expect(benchmark).toHaveProperty('withJoins');
      expect(benchmark).toHaveProperty('improvement');
      
      expect(typeof benchmark.improvement).toBe('number');
      expect(benchmark.withDuplication.networkRequests).toBeLessThan(benchmark.withJoins.networkRequests);
      expect(benchmark.improvement).toBeGreaterThan(0);
      
      console.log(`Data duplication improves performance by ${benchmark.improvement.toFixed(1)}%`);
    });
  });

  describe('Comprehensive Performance Testing', () => {
    it('should run all performance tests successfully', async () => {
      const results = await performanceTestingService.runAllPerformanceTests();
      
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBeGreaterThan(0);
      
      // Check that all expected tests are included
      const testNames = results.map(r => r.testName);
      expect(testNames).toContain('Flat Collection Query Performance');
      expect(testNames).toContain('Multi-Tenant Query Performance');
      expect(testNames).toContain('Pagination Performance');
      expect(testNames).toContain('Complex Query Performance');
      
      // Calculate pass rate
      const passedTests = results.filter(r => r.passed);
      const passRate = (passedTests.length / results.length) * 100;
      
      console.log(`Performance test pass rate: ${passRate.toFixed(1)}%`);
      
      // Log failed tests
      const failedTests = results.filter(r => !r.passed);
      if (failedTests.length > 0) {
        console.warn(`${failedTests.length} performance tests failed:`, failedTests);
      }
      
      // Ensure minimum pass rate (adjust threshold as needed)
      expect(passRate).toBeGreaterThanOrEqual(75);
    });

    it('should complete performance tests within reasonable time', async () => {
      const startTime = Date.now();
      const results = await performanceTestingService.runAllPerformanceTests();
      const totalDuration = Date.now() - startTime;
      
      expect(totalDuration).toBeLessThan(60000); // 60 seconds max
      
      // Check individual test performance
      results.forEach(result => {
        expect(result.duration).toBeLessThan(30000); // 30 seconds max per test
      });
      
      console.log(`Performance tests completed in ${totalDuration}ms`);
    });

    it('should provide actionable recommendations for failed tests', async () => {
      const results = await performanceTestingService.runAllPerformanceTests();
      
      results.forEach(result => {
        if (!result.passed) {
          expect(result.recommendations).toBeDefined();
          expect(Array.isArray(result.recommendations)).toBe(true);
          expect(result.recommendations.length).toBeGreaterThan(0);
          
          result.recommendations.forEach(recommendation => {
            expect(typeof recommendation).toBe('string');
            expect(recommendation.length).toBeGreaterThan(10);
          });
        }
      });
    });
  });

  describe('Performance Test Reliability', () => {
    it('should produce consistent results across multiple runs', async () => {
      const run1 = await performanceTestingService.testFlatCollectionPerformance();
      const run2 = await performanceTestingService.testFlatCollectionPerformance();
      
      // Results should be consistent (allowing for some variance in timing)
      expect(run1.testName).toBe(run2.testName);
      expect(run1.passed).toBe(run2.passed);
      
      if (run1.metrics.queryTime && run2.metrics.queryTime) {
        const variance = Math.abs(run1.metrics.queryTime - run2.metrics.queryTime);
        const averageTime = (run1.metrics.queryTime + run2.metrics.queryTime) / 2;
        const variancePercentage = (variance / averageTime) * 100;
        
        // Variance should be less than 100% of average time (allowing for test environment variability)
        expect(variancePercentage).toBeLessThan(100);
      }
    });

    it('should handle errors gracefully', async () => {
      // Mock a failing scenario
      const originalGetOptimizedList = require('@/services/performanceOptimizedQueryService').performanceOptimizedQueryService.getOptimizedList;
      require('@/services/performanceOptimizedQueryService').performanceOptimizedQueryService.getOptimizedList = jest.fn().mockRejectedValue(new Error('Test error'));
      
      const result = await performanceTestingService.testFlatCollectionPerformance();
      
      expect(result).toBeDefined();
      expect(result.passed).toBe(false);
      expect(result.error).toContain('Test error');
      
      // Restore original function
      require('@/services/performanceOptimizedQueryService').performanceOptimizedQueryService.getOptimizedList = originalGetOptimizedList;
    });
  });
});
