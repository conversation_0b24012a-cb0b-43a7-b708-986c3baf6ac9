"use client";

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import {
  collection,
  doc,
  onSnapshot,
  updateDoc,
  setDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from '@/hooks/useAuth';

// Types for collaboration features
export interface CollaborationUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  color: string;
  lastSeen: Date;
}

export interface UserCursor {
  userId: string;
  x: number;
  y: number;
  taskId?: string;
  timestamp: Date;
}

export interface TaskEditSession {
  taskId: string;
  userId: string;
  startTime: Date;
  lastActivity: Date;
}

export interface CollaborationState {
  // Current user
  currentUser: CollaborationUser | null;
  
  // Active users on the task board
  activeUsers: CollaborationUser[];
  
  // Live cursors
  cursors: UserCursor[];
  
  // Active editing sessions
  editingSessions: TaskEditSession[];
  
  // Conflict detection
  conflicts: string[]; // task IDs with conflicts
}

interface CollaborationContextType {
  state: CollaborationState;
  
  // User presence
  setCurrentUser: (user: CollaborationUser) => void;
  updateUserPresence: () => void;
  
  // Cursor tracking
  updateCursor: (x: number, y: number, taskId?: string) => void;
  
  // Edit sessions
  startEditingTask: (taskId: string) => void;
  stopEditingTask: (taskId: string) => void;
  
  // Conflict resolution
  resolveConflict: (taskId: string) => void;
  
  // Real-time updates
  broadcastUpdate: (type: string, data: any) => void;

  // Enhanced real-time features
  subscribeToTaskUpdates: (taskId: string, callback: (update: any) => void) => () => void;
  notifyTaskChange: (taskId: string, changeType: string, data: any) => void;
  getActiveEditorsForTask: (taskId: string) => CollaborationUser[];
  isTaskBeingEdited: (taskId: string) => boolean;
}

const CollaborationContext = createContext<CollaborationContextType | undefined>(undefined);

// Generate a consistent color for a user based on their ID
const getUserColor = (userId: string): string => {
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ];
  
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    hash = userId.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  return colors[Math.abs(hash) % colors.length];
};

export function CollaborationProvider({ children }: { children: React.ReactNode }) {
  const { user: authUser } = useAuth();
  const [state, setState] = useState<CollaborationState>({
    currentUser: null,
    activeUsers: [],
    cursors: [],
    editingSessions: [],
    conflicts: []
  });

  // Session ID for this browser session
  const [sessionId] = useState(() => uuidv4());

  // Real-time listeners cleanup functions
  const [unsubscribeFunctions, setUnsubscribeFunctions] = useState<(() => void)[]>([]);

  // Initialize current user from authentication and set up real-time presence
  useEffect(() => {
    if (authUser) {
      const currentUser: CollaborationUser = {
        id: authUser.id || sessionId,
        name: authUser.displayName || 'Current User',
        email: authUser.email || '<EMAIL>',
        color: getUserColor(authUser.id || sessionId),
        lastSeen: new Date()
      };

      setState(prev => ({
        ...prev,
        currentUser
      }));

      // Set up real-time presence in Firebase
      setupUserPresence(currentUser);
    }
  }, [authUser, sessionId]);

  const setCurrentUser = useCallback((user: CollaborationUser) => {
    setState(prev => ({
      ...prev,
      currentUser: {
        ...user,
        color: getUserColor(user.id),
        lastSeen: new Date()
      }
    }));
  }, []);

  // Set up real-time user presence with Firebase
  const setupUserPresence = useCallback(async (user: CollaborationUser) => {
    try {
      // Create or update user presence document
      const presenceRef = doc(db, 'user_presence', user.id);
      await setDoc(presenceRef, {
        userId: user.id,
        name: user.name,
        email: user.email,
        status: 'online',
        lastSeen: serverTimestamp(),
        sessionId: sessionId,
        currentPage: window.location.pathname,
        color: user.color
      }, { merge: true });

      // Set up real-time listener for all active users
      setupActiveUsersListener();

      // Update presence every 30 seconds
      const presenceInterval = setInterval(async () => {
        try {
          await updateDoc(presenceRef, {
            lastSeen: serverTimestamp(),
            currentPage: window.location.pathname
          });
        } catch (error) {
          console.error('Error updating presence:', error);
        }
      }, 30000);

      // Clean up presence when user leaves
      const handleBeforeUnload = async () => {
        try {
          await updateDoc(presenceRef, {
            status: 'offline',
            lastSeen: serverTimestamp()
          });
        } catch (error) {
          console.error('Error updating presence on unload:', error);
        }
      };

      window.addEventListener('beforeunload', handleBeforeUnload);

      // Store cleanup functions
      setUnsubscribeFunctions(prev => [...prev, () => {
        clearInterval(presenceInterval);
        window.removeEventListener('beforeunload', handleBeforeUnload);
        handleBeforeUnload();
      }]);

    } catch (error) {
      console.error('Error setting up user presence:', error);
    }
  }, [sessionId]);

  const updateUserPresence = useCallback(() => {
    if (!state.currentUser) return;

    setState(prev => ({
      ...prev,
      currentUser: prev.currentUser ? {
        ...prev.currentUser,
        lastSeen: new Date()
      } : null
    }));

    // Update presence in Firebase
    if (state.currentUser) {
      const presenceRef = doc(db, 'user_presence', state.currentUser.id);
      updateDoc(presenceRef, {
        lastSeen: serverTimestamp(),
        currentPage: window.location.pathname
      }).catch(error => console.error('Error updating presence:', error));
    }
  }, [state.currentUser]);

  // Set up real-time listener for active users
  const setupActiveUsersListener = useCallback(() => {
    try {
      // Query for users who have been active in the last 5 minutes
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      const activeUsersQuery = query(
        collection(db, 'user_presence'),
        where('status', '==', 'online'),
        orderBy('lastSeen', 'desc'),
        limit(50)
      );

      const unsubscribe = onSnapshot(activeUsersQuery, (snapshot) => {
        const activeUsers: CollaborationUser[] = [];

        const editingSessions: TaskEditSession[] = [];

        snapshot.forEach((doc) => {
          const data = doc.data();
          const lastSeen = data.lastSeen?.toDate() || new Date();

          // Only include users active in the last 5 minutes
          if (lastSeen > fiveMinutesAgo) {
            activeUsers.push({
              id: data.userId,
              name: data.name || 'Unknown User',
              email: data.email || '',
              color: data.color || getUserColor(data.userId),
              lastSeen: lastSeen
            });

            // Track editing sessions
            if (data.editingSession && data.editingSession.taskId) {
              editingSessions.push({
                taskId: data.editingSession.taskId,
                userId: data.userId,
                startTime: data.editingSession.startTime?.toDate() || new Date(),
                lastActivity: lastSeen
              });
            }
          }
        });

        setState(prev => ({
          ...prev,
          activeUsers,
          editingSessions: editingSessions
        }));
      }, (error) => {
        console.error('Error listening to active users:', error);
      });

      // Store cleanup function
      setUnsubscribeFunctions(prev => [...prev, unsubscribe]);

    } catch (error) {
      console.error('Error setting up active users listener:', error);
    }
  }, []);

  const updateCursor = useCallback((x: number, y: number, taskId?: string) => {
    if (!state.currentUser) return;
    
    const newCursor: UserCursor = {
      userId: state.currentUser.id,
      x,
      y,
      taskId,
      timestamp: new Date()
    };
    
    setState(prev => ({
      ...prev,
      cursors: [
        ...prev.cursors.filter(c => c.userId !== state.currentUser!.id),
        newCursor
      ]
    }));
    
    // Update cursor position in Firebase for real-time collaboration
    if (state.currentUser) {
      const presenceRef = doc(db, 'user_presence', state.currentUser.id);
      updateDoc(presenceRef, {
        cursor: { x, y, taskId },
        lastSeen: serverTimestamp()
      }).catch(error => console.error('Error updating cursor:', error));
    }
  }, [state.currentUser]);

  const startEditingTask = useCallback((taskId: string) => {
    if (!state.currentUser) return;
    
    const session: TaskEditSession = {
      taskId,
      userId: state.currentUser.id,
      startTime: new Date(),
      lastActivity: new Date()
    };
    
    setState(prev => {
      // Check for conflicts
      const existingSession = prev.editingSessions.find(s => s.taskId === taskId && s.userId !== state.currentUser!.id);
      const newConflicts = existingSession ? [...prev.conflicts, taskId] : prev.conflicts.filter(id => id !== taskId);
      
      return {
        ...prev,
        editingSessions: [
          ...prev.editingSessions.filter(s => !(s.taskId === taskId && s.userId === state.currentUser!.id)),
          session
        ],
        conflicts: newConflicts
      };
    });
    
    // Update editing session in Firebase
    if (state.currentUser) {
      const presenceRef = doc(db, 'user_presence', state.currentUser.id);
      updateDoc(presenceRef, {
        editingSession: {
          taskId,
          startTime: serverTimestamp()
        },
        lastSeen: serverTimestamp()
      }).catch(error => console.error('Error updating editing session:', error));
    }
  }, [state.currentUser]);

  const stopEditingTask = useCallback((taskId: string) => {
    if (!state.currentUser) return;
    
    setState(prev => ({
      ...prev,
      editingSessions: prev.editingSessions.filter(
        s => !(s.taskId === taskId && s.userId === state.currentUser!.id)
      ),
      conflicts: prev.conflicts.filter(id => id !== taskId)
    }));
    
    // Clear editing session in Firebase
    if (state.currentUser) {
      const presenceRef = doc(db, 'user_presence', state.currentUser.id);
      updateDoc(presenceRef, {
        editingSession: null,
        lastSeen: serverTimestamp()
      }).catch(error => console.error('Error clearing editing session:', error));
    }
  }, [state.currentUser]);

  const resolveConflict = useCallback((taskId: string) => {
    setState(prev => ({
      ...prev,
      conflicts: prev.conflicts.filter(id => id !== taskId)
    }));
  }, []);

  // Task update subscriptions for real-time collaboration
  const [taskSubscriptions] = useState<Map<string, Set<(update: any) => void>>>(new Map());

  const subscribeToTaskUpdates = useCallback((taskId: string, callback: (update: any) => void) => {
    if (!taskSubscriptions.has(taskId)) {
      taskSubscriptions.set(taskId, new Set());
    }
    taskSubscriptions.get(taskId)!.add(callback);

    // Return unsubscribe function
    return () => {
      const callbacks = taskSubscriptions.get(taskId);
      if (callbacks) {
        callbacks.delete(callback);
        if (callbacks.size === 0) {
          taskSubscriptions.delete(taskId);
        }
      }
    };
  }, [taskSubscriptions]);

  const notifyTaskChange = useCallback((taskId: string, changeType: string, data: any) => {
    const callbacks = taskSubscriptions.get(taskId);
    if (callbacks) {
      const update = {
        taskId,
        changeType,
        data,
        timestamp: new Date(),
        userId: state.currentUser?.id
      };

      callbacks.forEach(callback => {
        try {
          callback(update);
        } catch (error) {
          console.error('Error in task update callback:', error);
        }
      });
    }

    // Real-time updates are handled through Firebase listeners
    // No need for additional broadcasting
  }, [taskSubscriptions, state.currentUser?.id]);

  const getActiveEditorsForTask = useCallback((taskId: string) => {
    const editingSessions = state.editingSessions.filter(s => s.taskId === taskId);
    return editingSessions
      .map(session => state.activeUsers.find(u => u.id === session.userId))
      .filter(Boolean) as CollaborationUser[];
  }, [state.editingSessions, state.activeUsers]);

  const isTaskBeingEdited = useCallback((taskId: string) => {
    return state.editingSessions.some(s => s.taskId === taskId);
  }, [state.editingSessions]);

  const broadcastUpdate = useCallback((type: string, data: any) => {
    // Real-time broadcasting using Firebase
    console.log(`[Collaboration] Broadcasting ${type}:`, data);

    // Real-time updates are now handled through Firebase listeners
    // No need for simulation - Firebase provides real-time synchronization

    // Simulate occasional conflicts for demonstration (can be removed in production)
    if (type === 'task-change' && Math.random() > 0.9) { // 10% chance
      setTimeout(() => {
        setState(prev => ({
          ...prev,
          conflicts: [...prev.conflicts, data.taskId].filter((id, index, arr) => arr.indexOf(id) === index)
        }));
      }, 200 + Math.random() * 500);
    }
  }, []);

  // Handle page visibility changes to update user status
  useEffect(() => {
    const handleVisibilityChange = async () => {
      if (!state.currentUser) return;

      const status = document.hidden ? 'away' : 'online';

      try {
        const presenceRef = doc(db, 'user_presence', state.currentUser.id);
        await updateDoc(presenceRef, {
          status,
          lastSeen: serverTimestamp()
        });
      } catch (error) {
        console.error('Error updating visibility status:', error);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [state.currentUser]);

  // Clean up all Firebase listeners when component unmounts
  useEffect(() => {
    return () => {
      unsubscribeFunctions.forEach(unsubscribe => {
        try {
          unsubscribe();
        } catch (error) {
          console.error('Error cleaning up Firebase listener:', error);
        }
      });
    };
  }, [unsubscribeFunctions]);

  // Cleanup inactive users and cursors
  useEffect(() => {
    const cleanup = setInterval(() => {
      const now = new Date();
      const INACTIVE_THRESHOLD = 30000; // 30 seconds
      
      setState(prev => ({
        ...prev,
        activeUsers: prev.activeUsers.filter(
          user => now.getTime() - user.lastSeen.getTime() < INACTIVE_THRESHOLD
        ),
        cursors: prev.cursors.filter(
          cursor => now.getTime() - cursor.timestamp.getTime() < 5000 // 5 seconds for cursors
        ),
        editingSessions: prev.editingSessions.filter(
          session => now.getTime() - session.lastActivity.getTime() < INACTIVE_THRESHOLD
        )
      }));
    }, 5000);
    
    return () => clearInterval(cleanup);
  }, []);

  // Update presence periodically
  useEffect(() => {
    const presenceInterval = setInterval(updateUserPresence, 10000); // Every 10 seconds
    return () => clearInterval(presenceInterval);
  }, [updateUserPresence]);

  const contextValue: CollaborationContextType = {
    state,
    setCurrentUser,
    updateUserPresence,
    updateCursor,
    startEditingTask,
    stopEditingTask,
    resolveConflict,
    broadcastUpdate,
    subscribeToTaskUpdates,
    notifyTaskChange,
    getActiveEditorsForTask,
    isTaskBeingEdited
  };

  return (
    <CollaborationContext.Provider value={contextValue}>
      {children}
    </CollaborationContext.Provider>
  );
}

export function useCollaboration() {
  const context = useContext(CollaborationContext);
  if (context === undefined) {
    throw new Error('useCollaboration must be used within a CollaborationProvider');
  }
  return context;
}
