/* ============================================================================ */
/* CENTRALIZED FORM DESIGN SYSTEM - SINGLE SOURCE OF TRUTH */
/* ============================================================================ */
/* This file replaces all scattered form styling across the application */
/* Theme-aware, consistent, and maintainable form styling */
/* Used by: Events, Exhibitions, and all other forms in EVEXA */
/* ============================================================================ */

/* Form Input Enhancements - Theme Aware */
.form-input-enhanced {
  @apply h-11 transition-all duration-200;
  @apply bg-background/80 border-border/60;
  @apply hover:bg-background hover:border-border;
  @apply focus:ring-2 focus:ring-primary/20 focus:border-primary/50;
  @apply placeholder:text-muted-foreground/60;
}

.form-input-enhanced-large {
  @apply h-12 text-lg transition-all duration-200;
  @apply bg-background/80 border-border/60;
  @apply hover:bg-background hover:border-border;
  @apply focus:ring-2 focus:ring-primary/20 focus:border-primary/50;
  @apply placeholder:text-muted-foreground/60;
}

/* Form Textarea Enhancements */
.form-textarea-enhanced {
  @apply transition-all duration-200 resize-none;
  @apply bg-background/80 border-border/60;
  @apply hover:bg-background hover:border-border;
  @apply focus:ring-2 focus:ring-primary/20 focus:border-primary/50;
  @apply placeholder:text-muted-foreground/60;
}

/* Form Select Enhancements */
.form-select-enhanced {
  @apply transition-all duration-200;
  @apply bg-background border-border;
  @apply hover:bg-accent/50 hover:border-border;
  @apply focus:ring-2 focus:ring-primary/20 focus:border-primary/50;
}

/* Radix Select Trigger Enhancements */
[data-radix-select-trigger].form-select-enhanced,
.form-select-trigger-enhanced {
  @apply bg-background border-border hover:bg-accent/50 hover:border-border;
  @apply transition-all duration-200;
  @apply focus:ring-2 focus:ring-primary/20 focus:border-primary/50;
}

/* Form Button Enhancements */
.form-button-enhanced {
  @apply transition-all duration-200;
  @apply bg-background/80 hover:bg-background;
  @apply border-border/60 hover:border-border;
}

.form-button-enhanced-primary {
  @apply transition-all duration-200;
  @apply bg-primary hover:bg-primary/90;
  @apply text-primary-foreground;
  @apply border-primary hover:border-primary/80;
}

/* Form Section Cards - Theme Aware */
.form-section-card {
  @apply relative p-6 rounded-xl border shadow-sm;
  @apply bg-card border-border;
  @apply hover:shadow-md hover:border-border/80;
  @apply transition-all duration-200;
}

.form-section-card-primary {
  @apply relative p-6 rounded-xl border shadow-sm;
  @apply bg-card border-primary/30;
  @apply hover:shadow-md hover:border-primary/40;
  @apply transition-all duration-200;
}

.form-section-card-secondary {
  @apply relative p-6 rounded-xl border shadow-sm;
  @apply bg-card border-primary/20;
  @apply hover:shadow-md hover:border-primary/30;
  @apply transition-all duration-200;
}

.form-section-card-success {
  @apply relative p-6 rounded-xl border shadow-sm;
  @apply bg-card border-green-500/30;
  @apply hover:shadow-md hover:border-green-500/40;
  @apply transition-all duration-200;
}

/* Form Section Card Gradients - Theme Aware */
.form-section-card::before,
.form-section-card-primary::before,
.form-section-card-secondary::before,
.form-section-card-success::before {
  content: '';
  @apply absolute inset-0 rounded-xl pointer-events-none;
}

.form-section-card::before {
  @apply bg-gradient-to-br from-primary/[0.01] to-primary/[0.03];
}

.form-section-card-primary::before {
  @apply bg-gradient-to-br from-primary/[0.03] to-primary/[0.06];
}

.form-section-card-secondary::before {
  @apply bg-gradient-to-br from-primary/[0.02] to-primary/[0.05];
}

.form-section-card-success::before {
  @apply bg-gradient-to-br from-green-500/[0.02] to-green-500/[0.05];
}

/* Ensure content is above pseudo-elements */
.form-section-card > *,
.form-section-card-primary > *,
.form-section-card-secondary > *,
.form-section-card-success > * {
  @apply relative z-10;
}

/* Form Section Headers */
.form-section-header {
  @apply flex items-center text-lg font-semibold mb-4;
  @apply text-foreground;
}

.form-section-header-primary {
  @apply flex items-center text-lg font-semibold mb-4;
  @apply text-primary;
}

.form-section-header-secondary {
  @apply flex items-center text-lg font-semibold mb-4;
  @apply text-primary/80;
}

.form-section-header-success {
  @apply flex items-center text-lg font-semibold mb-4;
  @apply text-green-600 dark:text-green-400;
}

/* Form Section Header Icons */
.form-section-header-icon {
  @apply p-2 rounded-lg mr-3 transition-colors;
  @apply bg-muted text-muted-foreground;
}

.form-section-header-primary .form-section-header-icon {
  @apply bg-primary/10 text-primary;
}

.form-section-header-secondary .form-section-header-icon {
  @apply bg-primary/5 text-primary/80;
}

.form-section-header-success .form-section-header-icon {
  @apply bg-green-100 dark:bg-green-900;
  @apply text-green-600 dark:text-green-400;
}

/* Form Labels */
.form-label-enhanced {
  @apply text-base font-medium text-foreground/90;
}

.form-label-enhanced-large {
  @apply text-lg font-semibold text-foreground;
}

.form-label-enhanced-small {
  @apply text-sm font-medium text-foreground/90;
}

/* Form Descriptions */
.form-description-enhanced {
  @apply text-sm text-muted-foreground/80;
}

/* Form Grid Layouts */
.form-grid-1 {
  @apply space-y-4;
}

.form-grid-2 {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.form-grid-3 {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.form-grid-4 {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

/* Form Container */
.form-container-enhanced {
  @apply space-y-6 max-w-4xl mx-auto;
}

/* Add Item Button with Sliding Animation */
.form-add-button {
  @apply relative overflow-hidden transition-all duration-200;
  @apply hover:scale-105 hover:shadow-lg focus:ring-2 focus:outline-none;
  @apply bg-background/80 hover:bg-background border-border/60 hover:border-border;
}

.form-add-button .sliding-icon {
  @apply absolute transition-all;
  left: -100%;
}

.form-add-button:hover .sliding-icon {
  left: 0.5rem;
}

.form-add-button .sliding-text {
  @apply text-sm font-medium transition-all;
}

.form-add-button:hover .sliding-text {
  margin-left: 0.5rem;
}

/* Remove Item Button */
.form-remove-button {
  @apply absolute top-2 right-2 h-7 w-7;
  @apply transition-all duration-200 hover:scale-110;
  @apply focus:ring-2 focus:outline-none;
}

/* Checkbox Enhancements */
.form-checkbox-enhanced {
  @apply border-border/60 hover:border-border;
  @apply data-[state=checked]:bg-primary data-[state=checked]:border-primary;
  @apply transition-all duration-200;
}

/* Popover Enhancements */
[data-radix-popover-content].form-popover-enhanced {
  @apply bg-background/95 backdrop-blur-sm border-border/60 shadow-xl;
}

/* Dark Mode Specific Enhancements */
.dark .form-select-enhanced,
.dark [data-radix-select-trigger].form-select-enhanced {
  @apply bg-background border-border hover:bg-accent/30;
}

.dark [data-radix-select-content] {
  @apply bg-popover border-border;
}

.dark [data-radix-select-item] {
  @apply text-foreground hover:bg-accent hover:text-accent-foreground;
}

.dark .form-section-card,
.dark .form-section-card-primary,
.dark .form-section-card-secondary,
.dark .form-section-card-success {
  @apply bg-card/30 border-border/40;
}

/* Force better contrast for select values */
[data-radix-select-value] {
  @apply text-foreground;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .form-grid-2,
  .form-grid-3,
  .form-grid-4 {
    @apply grid-cols-1 gap-4;
  }
  
  .form-section-card,
  .form-section-card-primary,
  .form-section-card-secondary,
  .form-section-card-success {
    @apply p-4;
  }
}

/* Animation Keyframes */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.form-slide-in {
  animation: slideIn 0.2s ease-out;
}
