/* ========================================
   SURGICAL FIXES - MINIMAL TARGETED CHANGES
   ======================================== */

/* ONLY fix the specific issues mentioned:
   1. Sidebar active item contrast
   2. Header notification/search icon alignment
   DO NOT touch anything else!
*/

/* ========================================
   SIDEBAR ACTIVE STATE CONTRAST FIX
   ======================================== */

/* Add a subtle left border indicator for active sidebar items */
[data-sidebar="menu-button"][data-active="true"] {
  position: relative;
  font-weight: 600;
}

[data-sidebar="menu-button"][data-active="true"]::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 70%;
  background: hsl(var(--primary));
  border-radius: 0 2px 2px 0;
}

/* ========================================
   HEADER ICON ALIGNMENT FIX
   ======================================== */

/* Fix notification and search button icon alignment in header */
header button[class*="rounded-full"],
[data-header] button[class*="rounded-full"],
.header button[class*="rounded-full"] {
  display: flex;
  align-items: center;
  justify-content: center;
}

header button[class*="rounded-full"] svg,
[data-header] button[class*="rounded-full"] svg,
.header button[class*="rounded-full"] svg {
  width: 1.125rem;
  height: 1.125rem;
}

/* ========================================
   HIGH CONTRAST MODE SUPPORT
   ======================================== */

@media (prefers-contrast: high) {
  [data-sidebar="menu-button"][data-active="true"]::before {
    background: currentColor;
    width: 4px;
  }
  
  [data-sidebar="menu-button"][data-active="true"] {
    background-color: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
  }
}

/* ========================================
   THEME COMPATIBILITY
   ======================================== */

/* Ensure the active indicator works with all themes */
[data-theme="dark"] [data-sidebar="menu-button"][data-active="true"]::before,
[data-theme="blue"] [data-sidebar="menu-button"][data-active="true"]::before,
[data-theme="gray"] [data-sidebar="menu-button"][data-active="true"]::before,
[data-theme="gold"] [data-sidebar="menu-button"][data-active="true"]::before {
  background: hsl(var(--primary));
}

/* That's it - no other changes! */
