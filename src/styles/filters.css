/* Enhanced Filter Styles */

/* Remove ugly scrollbars and improve filter containers */
.filter-container {
  @apply space-y-4;
}

.filter-section {
  @apply space-y-3;
}

.filter-section-title {
  @apply text-sm font-medium mb-3 block flex items-center gap-2;
}

.filter-badges-grid {
  @apply grid grid-cols-2 gap-2;
}

.filter-badge {
  @apply cursor-pointer justify-center py-2 px-3 hover:scale-105 transition-all text-xs;
}

.filter-badge:hover {
  @apply bg-muted;
}

.filter-badge-active {
  @apply scale-105;
}

.filter-checkboxes-grid {
  @apply grid grid-cols-2 sm:grid-cols-3 gap-3;
}

.filter-checkbox-item {
  @apply flex items-center space-x-2;
}

.filter-checkbox-label {
  @apply text-sm cursor-pointer;
}

.filter-range-grid {
  @apply grid grid-cols-2 gap-3;
}

.filter-range-input {
  @apply h-9;
}

.filter-range-label {
  @apply text-xs text-muted-foreground;
}

/* Popover improvements */
.filter-popover-content {
  @apply w-[500px] p-0;
}

.filter-popover-body {
  @apply space-y-6 max-h-[70vh] overflow-y-auto;
}

/* Remove scrollbar styling for webkit browsers */
.filter-popover-body::-webkit-scrollbar {
  width: 6px;
}

.filter-popover-body::-webkit-scrollbar-track {
  @apply bg-transparent;
}

.filter-popover-body::-webkit-scrollbar-thumb {
  @apply bg-border rounded-full;
}

.filter-popover-body::-webkit-scrollbar-thumb:hover {
  @apply bg-border/80;
}

/* Active filter display */
.active-filters-container {
  @apply flex items-center gap-2 flex-wrap mt-4;
}

.active-filter-badge {
  @apply gap-1;
}

.active-filter-remove {
  @apply h-3 w-3 cursor-pointer hover:text-destructive;
}

/* Search input improvements */
.filter-search-container {
  @apply relative;
}

.filter-search-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground;
}

.filter-search-input {
  @apply pl-10 h-10;
}

/* Button improvements */
.filter-action-buttons {
  @apply flex justify-between pt-2;
}

.filter-clear-button {
  @apply gap-2;
}

.filter-apply-button {
  @apply gap-2;
}

/* Compact mode styles */
.filter-compact-container {
  @apply flex items-center gap-4;
}

.filter-compact-search {
  @apply relative flex-1;
}

.filter-compact-trigger {
  @apply gap-2;
}

.filter-count-badge {
  @apply ml-2 h-5 w-5 p-0 flex items-center justify-center text-xs;
}

/* Animation improvements */
.filter-badge-hover {
  @apply transition-all duration-200 ease-in-out;
}

.filter-badge-hover:hover {
  @apply scale-105 shadow-sm;
}

/* Responsive improvements */
@media (max-width: 640px) {
  .filter-badges-grid {
    @apply grid-cols-1;
  }
  
  .filter-checkboxes-grid {
    @apply grid-cols-1;
  }
  
  .filter-range-grid {
    @apply grid-cols-1;
  }
  
  .filter-popover-content {
    @apply w-[90vw];
  }
}

/* Focus improvements */
.filter-badge:focus-visible {
  @apply outline-none ring-2 ring-primary ring-offset-2;
}

.filter-checkbox-item input:focus-visible {
  @apply outline-none ring-2 ring-primary ring-offset-2;
}

/* Loading states */
.filter-loading {
  @apply opacity-50 pointer-events-none;
}

.filter-skeleton {
  @apply animate-pulse bg-muted rounded;
}

/* Error states */
.filter-error {
  @apply border-destructive text-destructive;
}

/* Success states */
.filter-success {
  @apply border-green-500 text-green-700;
}
