/**
 * EVEXA Accessibility Enhancements
 * Improves contrast, readability, and accessibility across the application
 */

/* Enhanced Form Element Contrast */
select,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
textarea {
  @apply text-foreground bg-background;
  color: hsl(var(--foreground)) !important;
  background-color: hsl(var(--background)) !important;
}

/* Select dropdown options */
select option {
  @apply text-foreground bg-background;
  color: hsl(var(--foreground)) !important;
  background-color: hsl(var(--background)) !important;
}

/* Enhanced button contrast */
button:not([class*="bg-"]) {
  @apply text-foreground;
}

/* Improved muted text contrast */
.text-muted-foreground {
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.8;
}

/* Enhanced placeholder text */
::placeholder {
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.7;
}

/* Better disabled state contrast */
:disabled {
  opacity: 0.6 !important;
}

/* Enhanced focus states for better accessibility */
:focus-visible {
  outline: 2px solid hsl(var(--ring)) !important;
  outline-offset: 2px !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .text-muted-foreground {
    opacity: 1 !important;
    color: hsl(var(--foreground)) !important;
  }
  
  ::placeholder {
    opacity: 0.9 !important;
  }
  
  select option {
    color: hsl(var(--foreground)) !important;
    background-color: hsl(var(--background)) !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Enhanced table readability */
table {
  @apply text-foreground;
}

table th {
  @apply text-foreground font-semibold;
}

table td {
  @apply text-foreground;
}

/* Better card contrast */
.card {
  @apply bg-card text-card-foreground;
}

/* Fix card contrast issues - especially light colored cards */
.rounded-lg.border[class*="bg-blue-50"],
.rounded-lg.border[class*="bg-green-50"],
.rounded-lg.border[class*="bg-yellow-50"],
.rounded-lg.border[class*="bg-red-50"],
.rounded-lg.border[class*="bg-purple-50"],
.rounded-lg.border[class*="bg-gray-50"] {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
}

/* Fix muted text in light cards */
.rounded-lg.border[class*="bg-blue-50"] .text-muted-foreground,
.rounded-lg.border[class*="bg-green-50"] .text-muted-foreground,
.rounded-lg.border[class*="bg-yellow-50"] .text-muted-foreground,
.rounded-lg.border[class*="bg-red-50"] .text-muted-foreground,
.rounded-lg.border[class*="bg-purple-50"] .text-muted-foreground,
.rounded-lg.border[class*="bg-gray-50"] .text-muted-foreground {
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.8 !important;
}

/* Fix card headings in light backgrounds */
.rounded-lg.border[class*="bg-blue-50"] h4,
.rounded-lg.border[class*="bg-green-50"] h4,
.rounded-lg.border[class*="bg-yellow-50"] h4,
.rounded-lg.border[class*="bg-red-50"] h4,
.rounded-lg.border[class*="bg-purple-50"] h4,
.rounded-lg.border[class*="bg-gray-50"] h4 {
  color: hsl(var(--card-foreground)) !important;
  font-weight: 600 !important;
}

/* Enhanced badge contrast */
.badge {
  font-weight: 500 !important;
}

/* Better link contrast */
a:not([class*="button"]):not([class*="btn"]) {
  @apply text-primary hover:text-primary/80;
  text-decoration: underline;
  text-decoration-color: transparent;
  transition: text-decoration-color 0.2s ease;
}

a:not([class*="button"]):not([class*="btn"]):hover {
  text-decoration-color: currentColor;
}

/* Enhanced form labels */
label {
  @apply text-foreground font-medium;
}

/* Better error message contrast */
.text-destructive {
  color: hsl(var(--destructive)) !important;
  font-weight: 500;
}

/* Enhanced success message contrast */
.text-success {
  color: hsl(var(--success)) !important;
  font-weight: 500;
}

/* Better warning message contrast */
.text-warning {
  color: hsl(var(--warning)) !important;
  font-weight: 500;
}

/* Improved code block contrast */
code,
pre {
  @apply bg-muted text-foreground;
  border: 1px solid hsl(var(--border));
}

/* Enhanced blockquote contrast */
blockquote {
  @apply text-foreground border-l-primary;
  border-left-width: 4px;
  padding-left: 1rem;
}

/* Better tooltip contrast */
[role="tooltip"] {
  @apply bg-popover text-popover-foreground;
  border: 1px solid hsl(var(--border));
}

/* Enhanced dropdown menu contrast */
[role="menu"],
[role="listbox"] {
  @apply bg-popover text-popover-foreground;
  border: 1px solid hsl(var(--border));
}

/* Better modal contrast */
[role="dialog"] {
  @apply bg-background text-foreground;
  border: 1px solid hsl(var(--border));
}

/* Enhanced alert contrast */
[role="alert"] {
  font-weight: 500;
  border: 1px solid currentColor;
}

/* ========================================
   CENTRALIZED THEME-AWARE SYSTEM
   ======================================== */

/* ========================================
   THEME-AWARE ACCESSIBILITY SYSTEM
   ======================================== */

/* Only keep essential accessibility improvements that work with themes */

/* Ensure proper contrast for active sidebar items */
[data-sidebar="menu-button"][data-active="true"] {
  /* Let the theme system handle colors, just ensure visibility */
  font-weight: 500;
}

/* Ensure form elements have good contrast */
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  * {
    border-color: currentColor !important;
  }

  button {
    border-width: 2px !important;
  }
}

/* Fix tab buttons with icons */
button[role="tab"] svg,
[role="tab"] svg {
  width: 1.25rem !important; /* 20px */
  height: 1.25rem !important; /* 20px */
}

/* Fix all small buttons with icons (h-9) */
.h-9 svg,
button.h-9 svg,
[class*="h-9"] svg {
  width: 1.125rem !important; /* 18px */
  height: 1.125rem !important; /* 18px */
}

/* Mobile responsiveness for all button types */
@media (max-width: 640px) {
  /* Make h-20 buttons smaller on mobile */
  .h-20.flex.flex-col,
  button.h-20.flex.flex-col,
  [class*="h-20"][class*="flex"][class*="flex-col"] {
    height: 4rem !important; /* 64px */
    gap: 0.25rem !important;
    padding: 0.25rem !important;
  }

  /* Smaller icons on mobile for h-20 buttons */
  .h-20.flex.flex-col svg,
  button.h-20.flex.flex-col svg,
  [class*="h-20"][class*="flex"][class*="flex-col"] svg {
    width: 2rem !important;  /* 32px */
    height: 2rem !important; /* 32px */
  }

  /* Smaller text on mobile */
  .h-20.flex.flex-col span,
  button.h-20.flex.flex-col span,
  [class*="h-20"][class*="flex"][class*="flex-col"] span {
    font-size: 0.75rem !important; /* 12px */
    line-height: 1.2 !important;
    text-align: center !important;
  }

  /* Reduce grid gaps on mobile */
  .grid.gap-4 {
    gap: 0.5rem !important;
  }

  .grid.gap-2 {
    gap: 0.25rem !important;
  }
}

/* ========================================
   ADDITIONAL BUTTON FIXES
   ======================================== */

/* Fix buttons with specific icon classes */
button .lucide,
.lucide {
  flex-shrink: 0 !important;
}

/* Fix buttons in card headers */
.flex.items-center.justify-between button svg,
.flex.items-center.gap-2 button svg {
  width: 1.125rem !important; /* 18px */
  height: 1.125rem !important; /* 18px */
}

/* Fix toolbar buttons */
.flex.items-center.gap-2 button,
.toolbar button,
[class*="toolbar"] button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
}

/* Fix dropdown trigger buttons */
button[class*="chevron-down"],
button:has(.lucide-chevron-down) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Fix settings and action buttons */
button:has(.lucide-settings),
button:has(.lucide-refresh-cw),
button:has(.lucide-plus),
button:has(.lucide-layout-grid) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
}

/* Ensure text in buttons doesn't wrap awkwardly */
button span {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* Fix button text in flex-col buttons */
.flex-col button span,
button.flex-col span {
  text-align: center !important;
  line-height: 1.2 !important;
  font-weight: 500 !important;
}

/* CRITICAL: Fix light background contrast issues */

/* Fix any element with light background colors */
[class*="bg-blue-50"],
[class*="bg-green-50"],
[class*="bg-yellow-50"],
[class*="bg-red-50"],
[class*="bg-purple-50"],
[class*="bg-gray-50"],
[class*="bg-slate-50"],
[class*="bg-zinc-50"] {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
}

/* Fix muted text on light backgrounds */
[class*="bg-blue-50"] .text-muted-foreground,
[class*="bg-green-50"] .text-muted-foreground,
[class*="bg-yellow-50"] .text-muted-foreground,
[class*="bg-red-50"] .text-muted-foreground,
[class*="bg-purple-50"] .text-muted-foreground,
[class*="bg-gray-50"] .text-muted-foreground,
[class*="bg-slate-50"] .text-muted-foreground,
[class*="bg-zinc-50"] .text-muted-foreground {
  color: hsl(var(--foreground)) !important;
  opacity: 0.7 !important;
}

/* Fix headings on light backgrounds */
[class*="bg-blue-50"] h1,
[class*="bg-blue-50"] h2,
[class*="bg-blue-50"] h3,
[class*="bg-blue-50"] h4,
[class*="bg-blue-50"] h5,
[class*="bg-blue-50"] h6,
[class*="bg-green-50"] h1,
[class*="bg-green-50"] h2,
[class*="bg-green-50"] h3,
[class*="bg-green-50"] h4,
[class*="bg-green-50"] h5,
[class*="bg-green-50"] h6,
[class*="bg-yellow-50"] h1,
[class*="bg-yellow-50"] h2,
[class*="bg-yellow-50"] h3,
[class*="bg-yellow-50"] h4,
[class*="bg-yellow-50"] h5,
[class*="bg-yellow-50"] h6 {
  color: hsl(var(--foreground)) !important;
  font-weight: 600 !important;
}

/* Fix badges on light backgrounds */
[class*="bg-blue-50"] .badge,
[class*="bg-green-50"] .badge,
[class*="bg-yellow-50"] .badge {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

/* Enhanced breadcrumb contrast */
.breadcrumb {
  @apply text-muted-foreground;
}

.breadcrumb a {
  @apply text-foreground hover:text-primary;
}

/* Better pagination contrast */
.pagination button {
  @apply text-foreground hover:text-primary;
}

/* Enhanced tab contrast */
[role="tab"] {
  @apply text-muted-foreground;
}

[role="tab"][aria-selected="true"] {
  @apply text-foreground font-semibold;
}

/* Better progress bar contrast */
.progress {
  @apply bg-secondary;
}

.progress-indicator {
  @apply bg-primary;
}

/* Enhanced skeleton loading contrast */
.skeleton {
  @apply bg-muted;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Better separator contrast */
.separator {
  @apply bg-border;
}

/* Enhanced switch contrast */
.switch {
  @apply bg-input;
}

.switch[data-state="checked"] {
  @apply bg-primary;
}

/* Better checkbox contrast */
.checkbox {
  @apply border-input bg-background;
}

.checkbox[data-state="checked"] {
  @apply bg-primary border-primary;
}

/* Enhanced radio button contrast */
.radio {
  @apply border-input bg-background;
}

.radio[data-state="checked"] {
  @apply border-primary;
}

/* Better slider contrast */
.slider-track {
  @apply bg-secondary;
}

.slider-range {
  @apply bg-primary;
}

.slider-thumb {
  @apply bg-background border-primary;
}

/* Enhanced calendar contrast */
.calendar {
  @apply bg-background text-foreground;
}

.calendar-day {
  @apply text-foreground hover:bg-accent;
}

.calendar-day[data-selected] {
  @apply bg-primary text-primary-foreground;
}

/* Better command palette contrast */
.command {
  @apply bg-popover text-popover-foreground;
}

.command-item {
  @apply text-foreground hover:bg-accent;
}

/* Enhanced context menu contrast */
.context-menu {
  @apply bg-popover text-popover-foreground;
}

.context-menu-item {
  @apply text-foreground hover:bg-accent;
}
