/**
 * EVEXA System Email Templates
 * Professional email templates for user invitations, welcome messages, and system notifications
 * with EVEXA branding and tenant customization support
 */

import { EmailTemplate, EmailVariable } from '@/types/email-templates';

// ===== TEMPLATE VARIABLES =====

const commonVariables: EmailVariable[] = [
  {
    name: 'recipientName',
    label: 'Recipient Name',
    type: 'text',
    required: true,
    description: 'First name or full name of the recipient'
  },
  {
    name: 'senderName',
    label: 'Sender Name',
    type: 'text',
    required: true,
    description: 'Name of the person sending the invitation'
  },
  {
    name: 'tenantName',
    label: 'Company Name',
    type: 'text',
    required: true,
    description: 'Name of the tenant organization'
  },
  {
    name: 'tenantLogo',
    label: 'Company Logo URL',
    type: 'image',
    required: false,
    description: 'URL to the tenant company logo'
  },
  {
    name: 'supportEmail',
    label: 'Support Email',
    type: 'email',
    required: true,
    defaultValue: '<EMAIL>',
    description: 'Support contact email'
  }
];

const invitationVariables: EmailVariable[] = [
  ...commonVariables,
  {
    name: 'invitationUrl',
    label: 'Invitation URL',
    type: 'url',
    required: true,
    description: 'Secure URL for accepting the invitation'
  },
  {
    name: 'roleName',
    label: 'Role Name',
    type: 'text',
    required: true,
    description: 'Name of the assigned role/persona'
  },
  {
    name: 'expirationDate',
    label: 'Expiration Date',
    type: 'date',
    required: true,
    description: 'When the invitation expires'
  }
];

// ===== EVEXA BRANDING STYLES =====

const evexaBrandingStyles = `
  <style>
    .evexa-container {
      max-width: 600px;
      margin: 0 auto;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #333333;
    }
    .evexa-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 40px 30px;
      text-align: center;
      border-radius: 8px 8px 0 0;
    }
    .evexa-logo {
      max-height: 60px;
      margin-bottom: 20px;
    }
    .evexa-content {
      background: #ffffff;
      padding: 40px 30px;
      border-left: 1px solid #e1e5e9;
      border-right: 1px solid #e1e5e9;
    }
    .evexa-button {
      display: inline-block;
      background: #667eea;
      color: white;
      padding: 16px 32px;
      text-decoration: none;
      border-radius: 6px;
      font-weight: 600;
      margin: 20px 0;
      transition: background-color 0.3s ease;
    }
    .evexa-button:hover {
      background: #5a6fd8;
    }
    .evexa-footer {
      background: #f8f9fa;
      padding: 30px;
      text-align: center;
      border-radius: 0 0 8px 8px;
      border: 1px solid #e1e5e9;
      border-top: none;
    }
    .evexa-divider {
      height: 1px;
      background: #e1e5e9;
      margin: 30px 0;
    }
    .tenant-logo {
      max-height: 50px;
      margin-bottom: 15px;
    }
    .security-notice {
      background: #f8f9fa;
      border-left: 4px solid #667eea;
      padding: 15px;
      margin: 20px 0;
      border-radius: 4px;
    }
  </style>
`;

// ===== SYSTEM EMAIL TEMPLATES =====

export const systemEmailTemplates: EmailTemplate[] = [
  // USER INVITATION TEMPLATE
  {
    id: 'user_invitation_v1',
    name: 'User Invitation - Professional',
    description: 'Professional invitation email for new users to join a tenant organization',
    category: 'transactional',
    type: 'invitation',
    subject: 'You\'re invited to join {{tenantName}} on EVEXA',
    preheader: '{{senderName}} has invited you to collaborate on exhibition management',
    htmlContent: `
      ${evexaBrandingStyles}
      <div class="evexa-container">
        <div class="evexa-header">
          <img src="https://evexa.com/assets/logo-white.png" alt="EVEXA" class="evexa-logo">
          <h1 style="margin: 0; font-size: 28px; font-weight: 600;">You're Invited!</h1>
          <p style="margin: 10px 0 0; font-size: 16px; opacity: 0.9;">Join {{tenantName}} on EVEXA</p>
        </div>
        
        <div class="evexa-content">
          {{#if tenantLogo}}
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="{{tenantLogo}}" alt="{{tenantName}}" class="tenant-logo">
          </div>
          {{/if}}
          
          <h2 style="color: #333; margin-bottom: 20px;">Hi {{recipientName}},</h2>
          
          <p><strong>{{senderName}}</strong> has invited you to join <strong>{{tenantName}}</strong> on EVEXA, the leading exhibition management platform.</p>
          
          <p>You've been assigned the role of <strong>{{roleName}}</strong>, which will give you access to the tools and features you need to collaborate effectively.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{invitationUrl}}" class="evexa-button">Accept Invitation</a>
          </div>
          
          <div class="security-notice">
            <p style="margin: 0; font-size: 14px;"><strong>Security Notice:</strong> This invitation is secure and expires on {{expirationDate}}. If you didn't expect this invitation, you can safely ignore this email.</p>
          </div>
          
          <div class="evexa-divider"></div>
          
          <h3 style="color: #667eea; margin-bottom: 15px;">What you'll get with EVEXA:</h3>
          <ul style="padding-left: 20px;">
            <li>Streamlined exhibition planning and management</li>
            <li>Real-time collaboration with your team</li>
            <li>Comprehensive analytics and reporting</li>
            <li>Integrated task and project management</li>
            <li>Professional communication tools</li>
          </ul>
          
          <p>If you have any questions, feel free to reach out to <a href="mailto:{{supportEmail}}" style="color: #667eea;">{{supportEmail}}</a>.</p>
          
          <p style="margin-top: 30px;">Welcome to the team!<br>
          The EVEXA Team</p>
        </div>
        
        <div class="evexa-footer">
          <p style="margin: 0; font-size: 14px; color: #666;">
            This invitation was sent by {{senderName}} from {{tenantName}}<br>
            <a href="https://evexa.com" style="color: #667eea;">EVEXA</a> - Professional Exhibition Management
          </p>
        </div>
      </div>
    `,
    textContent: `
You're invited to join {{tenantName}} on EVEXA

Hi {{recipientName}},

{{senderName}} has invited you to join {{tenantName}} on EVEXA, the leading exhibition management platform.

You've been assigned the role of {{roleName}}, which will give you access to the tools and features you need to collaborate effectively.

Accept your invitation: {{invitationUrl}}

Security Notice: This invitation is secure and expires on {{expirationDate}}. If you didn't expect this invitation, you can safely ignore this email.

What you'll get with EVEXA:
- Streamlined exhibition planning and management
- Real-time collaboration with your team
- Comprehensive analytics and reporting
- Integrated task and project management
- Professional communication tools

If you have any questions, feel free to reach out to {{supportEmail}}.

Welcome to the team!
The EVEXA Team

This invitation was sent by {{senderName}} from {{tenantName}}
EVEXA - Professional Exhibition Management
https://evexa.com
    `,
    variables: invitationVariables,
    tags: ['invitation', 'user-onboarding', 'system'],
    isActive: true,
    isPremium: false,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    createdBy: 'system',
    usageCount: 0,
    rating: 5.0,
    industry: ['general']
  },

  // WELCOME EMAIL TEMPLATE
  {
    id: 'welcome_new_user_v1',
    name: 'Welcome New User - Complete',
    description: 'Comprehensive welcome email for users who have accepted their invitation',
    category: 'transactional',
    type: 'welcome',
    subject: 'Welcome to {{tenantName}} - Let\'s get started!',
    preheader: 'Your account is ready. Here\'s everything you need to know.',
    htmlContent: `
      ${evexaBrandingStyles}
      <div class="evexa-container">
        <div class="evexa-header">
          <img src="https://evexa.com/assets/logo-white.png" alt="EVEXA" class="evexa-logo">
          <h1 style="margin: 0; font-size: 28px; font-weight: 600;">Welcome to EVEXA!</h1>
          <p style="margin: 10px 0 0; font-size: 16px; opacity: 0.9;">Your account is ready</p>
        </div>
        
        <div class="evexa-content">
          {{#if tenantLogo}}
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="{{tenantLogo}}" alt="{{tenantName}}" class="tenant-logo">
          </div>
          {{/if}}
          
          <h2 style="color: #333; margin-bottom: 20px;">Welcome {{recipientName}}!</h2>
          
          <p>Your EVEXA account has been successfully created and you're now part of the <strong>{{tenantName}}</strong> team.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{loginUrl}}" class="evexa-button">Access Your Dashboard</a>
          </div>
          
          <div class="evexa-divider"></div>
          
          <h3 style="color: #667eea; margin-bottom: 15px;">Quick Start Guide:</h3>
          
          <div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
            <h4 style="margin-top: 0; color: #333;">1. Complete Your Profile</h4>
            <p style="margin-bottom: 0;">Add your photo and contact information to help your team recognize you.</p>
          </div>
          
          <div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
            <h4 style="margin-top: 0; color: #333;">2. Explore Your Dashboard</h4>
            <p style="margin-bottom: 0;">Get familiar with your personalized dashboard and available tools.</p>
          </div>
          
          <div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
            <h4 style="margin-top: 0; color: #333;">3. Join Your First Project</h4>
            <p style="margin-bottom: 0;">Start collaborating on exhibitions and events with your team.</p>
          </div>
          
          <div class="evexa-divider"></div>
          
          <h3 style="color: #667eea; margin-bottom: 15px;">Need Help?</h3>
          <p>Our support team is here to help you succeed:</p>
          <ul style="padding-left: 20px;">
            <li><a href="https://help.evexa.com" style="color: #667eea;">Help Center</a> - Comprehensive guides and tutorials</li>
            <li><a href="mailto:{{supportEmail}}" style="color: #667eea;">{{supportEmail}}</a> - Direct support contact</li>
            <li><a href="https://evexa.com/training" style="color: #667eea;">Training Resources</a> - Video tutorials and best practices</li>
          </ul>
          
          <p style="margin-top: 30px;">We're excited to have you on board!<br>
          The EVEXA Team</p>
        </div>
        
        <div class="evexa-footer">
          <p style="margin: 0; font-size: 14px; color: #666;">
            {{tenantName}} on <a href="https://evexa.com" style="color: #667eea;">EVEXA</a><br>
            Professional Exhibition Management Platform
          </p>
        </div>
      </div>
    `,
    textContent: `
Welcome to EVEXA!

Hi {{recipientName}},

Your EVEXA account has been successfully created and you're now part of the {{tenantName}} team.

Access your dashboard: {{loginUrl}}

Quick Start Guide:

1. Complete Your Profile
   Add your photo and contact information to help your team recognize you.

2. Explore Your Dashboard
   Get familiar with your personalized dashboard and available tools.

3. Join Your First Project
   Start collaborating on exhibitions and events with your team.

Need Help?
Our support team is here to help you succeed:

- Help Center: https://help.evexa.com
- Support Email: {{supportEmail}}
- Training Resources: https://evexa.com/training

We're excited to have you on board!
The EVEXA Team

{{tenantName}} on EVEXA
Professional Exhibition Management Platform
https://evexa.com
    `,
    variables: [
      ...commonVariables,
      {
        name: 'loginUrl',
        label: 'Login URL',
        type: 'url',
        required: true,
        description: 'Direct URL to the user dashboard'
      }
    ],
    tags: ['welcome', 'onboarding', 'system'],
    isActive: true,
    isPremium: false,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    createdBy: 'system',
    usageCount: 0,
    rating: 5.0,
    industry: ['general']
  },

  // PASSWORD RESET TEMPLATE
  {
    id: 'password_reset_v1',
    name: 'Password Reset - Secure',
    description: 'Secure password reset email with time-limited reset link',
    category: 'transactional',
    type: 'alert',
    subject: 'Reset your EVEXA password',
    preheader: 'Secure password reset for your {{tenantName}} account',
    htmlContent: `
      ${evexaBrandingStyles}
      <div class="evexa-container">
        <div class="evexa-header">
          <img src="https://evexa.com/assets/logo-white.png" alt="EVEXA" class="evexa-logo">
          <h1 style="margin: 0; font-size: 28px; font-weight: 600;">Password Reset</h1>
          <p style="margin: 10px 0 0; font-size: 16px; opacity: 0.9;">Secure access to your account</p>
        </div>

        <div class="evexa-content">
          <h2 style="color: #333; margin-bottom: 20px;">Hi {{recipientName}},</h2>

          <p>We received a request to reset the password for your EVEXA account at <strong>{{tenantName}}</strong>.</p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="{{resetUrl}}" class="evexa-button">Reset Password</a>
          </div>

          <div class="security-notice">
            <p style="margin: 0; font-size: 14px;"><strong>Security Information:</strong></p>
            <ul style="margin: 10px 0 0; font-size: 14px; padding-left: 20px;">
              <li>This link expires in 1 hour for security</li>
              <li>If you didn't request this reset, ignore this email</li>
              <li>Your password remains unchanged until you create a new one</li>
            </ul>
          </div>

          <p style="margin-top: 30px;">If you're having trouble with the button above, copy and paste this URL into your browser:</p>
          <p style="word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 14px;">{{resetUrl}}</p>

          <p>If you need additional help, contact our support team at <a href="mailto:{{supportEmail}}" style="color: #667eea;">{{supportEmail}}</a>.</p>
        </div>

        <div class="evexa-footer">
          <p style="margin: 0; font-size: 14px; color: #666;">
            {{tenantName}} on <a href="https://evexa.com" style="color: #667eea;">EVEXA</a><br>
            This is an automated security email
          </p>
        </div>
      </div>
    `,
    textContent: `
Password Reset - EVEXA

Hi {{recipientName}},

We received a request to reset the password for your EVEXA account at {{tenantName}}.

Reset your password: {{resetUrl}}

Security Information:
- This link expires in 1 hour for security
- If you didn't request this reset, ignore this email
- Your password remains unchanged until you create a new one

If you need additional help, contact our support team at {{supportEmail}}.

{{tenantName}} on EVEXA
This is an automated security email
https://evexa.com
    `,
    variables: [
      ...commonVariables,
      {
        name: 'resetUrl',
        label: 'Password Reset URL',
        type: 'url',
        required: true,
        description: 'Secure URL for password reset'
      }
    ],
    tags: ['password-reset', 'security', 'system'],
    isActive: true,
    isPremium: false,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    createdBy: 'system',
    usageCount: 0,
    rating: 5.0,
    industry: ['general']
  },

  // ACCOUNT SUSPENDED NOTIFICATION
  {
    id: 'account_suspended_v1',
    name: 'Account Suspended - Professional',
    description: 'Professional notification when user account is suspended',
    category: 'transactional',
    type: 'alert',
    subject: 'Your {{tenantName}} account has been suspended',
    preheader: 'Important account security notification',
    htmlContent: `
      ${evexaBrandingStyles}
      <div class="evexa-container">
        <div class="evexa-header" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
          <img src="https://evexa.com/assets/logo-white.png" alt="EVEXA" class="evexa-logo">
          <h1 style="margin: 0; font-size: 28px; font-weight: 600;">Account Suspended</h1>
          <p style="margin: 10px 0 0; font-size: 16px; opacity: 0.9;">Important security notification</p>
        </div>

        <div class="evexa-content">
          <h2 style="color: #333; margin-bottom: 20px;">Hi {{recipientName}},</h2>

          <p>Your EVEXA account at <strong>{{tenantName}}</strong> has been temporarily suspended.</p>

          <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 6px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #856404;">Reason for Suspension:</h3>
            <p style="margin-bottom: 0;">{{suspensionReason}}</p>
          </div>

          <h3 style="color: #667eea; margin-bottom: 15px;">What happens next:</h3>
          <ul style="padding-left: 20px;">
            <li>Your access to EVEXA has been temporarily disabled</li>
            <li>Your data remains secure and unchanged</li>
            <li>You will receive updates on the resolution process</li>
          </ul>

          <p>If you believe this suspension was made in error or if you have questions, please contact our support team immediately.</p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="mailto:{{supportEmail}}" class="evexa-button" style="background: #e74c3c;">Contact Support</a>
          </div>

          <p>We appreciate your understanding and cooperation.</p>
        </div>

        <div class="evexa-footer">
          <p style="margin: 0; font-size: 14px; color: #666;">
            {{tenantName}} on <a href="https://evexa.com" style="color: #667eea;">EVEXA</a><br>
            This is an automated security notification
          </p>
        </div>
      </div>
    `,
    textContent: `
Account Suspended - EVEXA

Hi {{recipientName}},

Your EVEXA account at {{tenantName}} has been temporarily suspended.

Reason for Suspension:
{{suspensionReason}}

What happens next:
- Your access to EVEXA has been temporarily disabled
- Your data remains secure and unchanged
- You will receive updates on the resolution process

If you believe this suspension was made in error or if you have questions, please contact our support team immediately at {{supportEmail}}.

We appreciate your understanding and cooperation.

{{tenantName}} on EVEXA
This is an automated security notification
https://evexa.com
    `,
    variables: [
      ...commonVariables,
      {
        name: 'suspensionReason',
        label: 'Suspension Reason',
        type: 'text',
        required: true,
        description: 'Reason for account suspension'
      }
    ],
    tags: ['account-suspension', 'security', 'system'],
    isActive: true,
    isPremium: false,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    createdBy: 'system',
    usageCount: 0,
    rating: 5.0,
    industry: ['general']
  }
];

// ===== TEMPLATE UTILITIES =====

/**
 * Get template by ID
 */
export function getSystemTemplate(templateId: string): EmailTemplate | null {
  return systemEmailTemplates.find(template => template.id === templateId) || null;
}

/**
 * Get templates by category
 */
export function getSystemTemplatesByCategory(category: string): EmailTemplate[] {
  return systemEmailTemplates.filter(template => template.category === category);
}

/**
 * Get templates by type
 */
export function getSystemTemplatesByType(type: string): EmailTemplate[] {
  return systemEmailTemplates.filter(template => template.type === type);
}

/**
 * Replace template variables with actual values
 */
export function replaceTemplateVariables(content: string, variables: Record<string, string>): string {
  let processedContent = content;
  
  Object.entries(variables).forEach(([key, value]) => {
    // Handle Handlebars-style variables {{variable}}
    const handlebarsRegex = new RegExp(`{{${key}}}`, 'g');
    processedContent = processedContent.replace(handlebarsRegex, value || '');
    
    // Handle conditional blocks {{#if variable}}
    const conditionalRegex = new RegExp(`{{#if ${key}}}([\\s\\S]*?){{/if}}`, 'g');
    processedContent = processedContent.replace(conditionalRegex, (match, content) => {
      return value ? content : '';
    });
  });
  
  // Clean up any remaining empty conditional blocks
  processedContent = processedContent.replace(/{{#if \w+}}[\s\S]*?{{\/if}}/g, '');
  
  return processedContent;
}
