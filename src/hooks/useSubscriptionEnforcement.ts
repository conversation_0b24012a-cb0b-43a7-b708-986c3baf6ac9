/**
 * Subscription Enforcement Hooks
 * React hooks for subscription limit enforcement and user management
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { 
  SubscriptionEnforcementService,
  createSubscriptionEnforcementService,
  UserLimitCheck,
  InvitationLimitCheck,
  UserUsageBreakdown,
  EnforcementAction,
  SubscriptionEnforcementOptions
} from '@/services/subscriptionEnforcementService';

// ===== TYPES =====

interface UseSubscriptionEnforcementReturn {
  service: SubscriptionEnforcementService | null;
  isLoading: boolean;
  error: string | null;
  checkUserLimit: (requestedUsers?: number, options?: SubscriptionEnforcementOptions) => Promise<UserLimitCheck>;
  checkInvitationLimit: (requestedInvitations?: number) => Promise<InvitationLimitCheck>;
  enforceUserLimit: (requestedUsers?: number, options?: SubscriptionEnforcementOptions) => Promise<EnforcementAction>;
  getUserUsageBreakdown: (options?: SubscriptionEnforcementOptions) => Promise<UserUsageBreakdown>;
  updateUserCount: () => Promise<void>;
  refreshData: () => Promise<void>;
}

interface UseUserLimitsReturn {
  userLimits: UserLimitCheck | null;
  usageBreakdown: UserUsageBreakdown | null;
  isLoading: boolean;
  error: string | null;
  canAddUsers: (count?: number) => boolean;
  availableSlots: number;
  usagePercentage: number;
  isNearLimit: boolean;
  isAtLimit: boolean;
  refresh: () => Promise<void>;
}

interface UseInvitationLimitsReturn {
  invitationLimits: InvitationLimitCheck | null;
  isLoading: boolean;
  error: string | null;
  canSendInvitations: (count?: number) => boolean;
  pendingInvitations: number;
  maxInvitations: number;
  refresh: () => Promise<void>;
}

interface UseEnforcementActionsReturn {
  checkAction: (requestedUsers?: number) => Promise<EnforcementAction>;
  isLoading: boolean;
  error: string | null;
  lastAction: EnforcementAction | null;
}

// ===== MAIN SUBSCRIPTION ENFORCEMENT HOOK =====

export function useSubscriptionEnforcement(): UseSubscriptionEnforcementReturn {
  const { tenantId } = useAuth();
  const [service, setService] = useState<SubscriptionEnforcementService | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize service
  useEffect(() => {
    if (!tenantId) {
      setService(null);
      setIsLoading(false);
      return;
    }

    try {
      const enforcementService = createSubscriptionEnforcementService(tenantId);
      setService(enforcementService);
      setError(null);
    } catch (err) {
      console.error('Error initializing subscription enforcement service:', err);
      setError(err instanceof Error ? err.message : 'Failed to initialize service');
      setService(null);
    } finally {
      setIsLoading(false);
    }
  }, [tenantId]);

  // Service methods
  const checkUserLimit = useCallback(async (
    requestedUsers: number = 1,
    options: SubscriptionEnforcementOptions = {}
  ): Promise<UserLimitCheck> => {
    if (!service) {
      return {
        allowed: false,
        currentUsers: 0,
        maxUsers: 0,
        availableSlots: 0,
        reason: 'Service not available'
      };
    }

    try {
      return await service.checkUserLimit(requestedUsers, options);
    } catch (err) {
      console.error('Error checking user limit:', err);
      return {
        allowed: false,
        currentUsers: 0,
        maxUsers: 0,
        availableSlots: 0,
        reason: 'Error checking limits'
      };
    }
  }, [service]);

  const checkInvitationLimit = useCallback(async (
    requestedInvitations: number = 1
  ): Promise<InvitationLimitCheck> => {
    if (!service) {
      return {
        allowed: false,
        currentInvitations: 0,
        maxInvitations: 0,
        pendingInvitations: 0,
        reason: 'Service not available'
      };
    }

    try {
      return await service.checkInvitationLimit(requestedInvitations);
    } catch (err) {
      console.error('Error checking invitation limit:', err);
      return {
        allowed: false,
        currentInvitations: 0,
        maxInvitations: 0,
        pendingInvitations: 0,
        reason: 'Error checking limits'
      };
    }
  }, [service]);

  const enforceUserLimit = useCallback(async (
    requestedUsers: number = 1,
    options: SubscriptionEnforcementOptions = {}
  ): Promise<EnforcementAction> => {
    if (!service) {
      return {
        type: 'block',
        message: 'Service not available'
      };
    }

    try {
      return await service.enforceUserLimit(requestedUsers, options);
    } catch (err) {
      console.error('Error enforcing user limit:', err);
      return {
        type: 'block',
        message: 'Error checking limits'
      };
    }
  }, [service]);

  const getUserUsageBreakdown = useCallback(async (
    options: SubscriptionEnforcementOptions = {}
  ): Promise<UserUsageBreakdown> => {
    if (!service) {
      return {
        activeUsers: 0,
        inactiveUsers: 0,
        pendingInvitations: 0,
        totalSlots: 0,
        availableSlots: 0,
        usagePercentage: 0,
        isNearLimit: false,
        isAtLimit: false
      };
    }

    try {
      return await service.getUserUsageBreakdown(options);
    } catch (err) {
      console.error('Error getting usage breakdown:', err);
      return {
        activeUsers: 0,
        inactiveUsers: 0,
        pendingInvitations: 0,
        totalSlots: 0,
        availableSlots: 0,
        usagePercentage: 0,
        isNearLimit: false,
        isAtLimit: false
      };
    }
  }, [service]);

  const updateUserCount = useCallback(async (): Promise<void> => {
    if (!service) return;

    try {
      await service.updateUserCount();
    } catch (err) {
      console.error('Error updating user count:', err);
    }
  }, [service]);

  const refreshData = useCallback(async (): Promise<void> => {
    // This would trigger a refresh of cached data
    await updateUserCount();
  }, [updateUserCount]);

  return {
    service,
    isLoading,
    error,
    checkUserLimit,
    checkInvitationLimit,
    enforceUserLimit,
    getUserUsageBreakdown,
    updateUserCount,
    refreshData
  };
}

// ===== USER LIMITS HOOK =====

export function useUserLimits(): UseUserLimitsReturn {
  const { checkUserLimit, getUserUsageBreakdown, isLoading: serviceLoading, error: serviceError } = useSubscriptionEnforcement();
  const [userLimits, setUserLimits] = useState<UserLimitCheck | null>(null);
  const [usageBreakdown, setUsageBreakdown] = useState<UserUsageBreakdown | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadData = useCallback(async () => {
    if (serviceLoading) return;

    try {
      setIsLoading(true);
      setError(null);

      const [limits, breakdown] = await Promise.all([
        checkUserLimit(0),
        getUserUsageBreakdown()
      ]);

      setUserLimits(limits);
      setUsageBreakdown(breakdown);
    } catch (err) {
      console.error('Error loading user limits:', err);
      setError(err instanceof Error ? err.message : 'Failed to load limits');
    } finally {
      setIsLoading(false);
    }
  }, [checkUserLimit, getUserUsageBreakdown, serviceLoading]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const canAddUsers = useCallback((count: number = 1): boolean => {
    if (!userLimits) return false;
    return userLimits.availableSlots === -1 || userLimits.availableSlots >= count;
  }, [userLimits]);

  const availableSlots = useMemo(() => {
    return userLimits?.availableSlots || 0;
  }, [userLimits]);

  const usagePercentage = useMemo(() => {
    return usageBreakdown?.usagePercentage || 0;
  }, [usageBreakdown]);

  const isNearLimit = useMemo(() => {
    return usageBreakdown?.isNearLimit || false;
  }, [usageBreakdown]);

  const isAtLimit = useMemo(() => {
    return usageBreakdown?.isAtLimit || false;
  }, [usageBreakdown]);

  const refresh = useCallback(async () => {
    await loadData();
  }, [loadData]);

  return {
    userLimits,
    usageBreakdown,
    isLoading: isLoading || serviceLoading,
    error: error || serviceError,
    canAddUsers,
    availableSlots,
    usagePercentage,
    isNearLimit,
    isAtLimit,
    refresh
  };
}

// ===== INVITATION LIMITS HOOK =====

export function useInvitationLimits(): UseInvitationLimitsReturn {
  const { checkInvitationLimit, isLoading: serviceLoading, error: serviceError } = useSubscriptionEnforcement();
  const [invitationLimits, setInvitationLimits] = useState<InvitationLimitCheck | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadData = useCallback(async () => {
    if (serviceLoading) return;

    try {
      setIsLoading(true);
      setError(null);

      const limits = await checkInvitationLimit(0);
      setInvitationLimits(limits);
    } catch (err) {
      console.error('Error loading invitation limits:', err);
      setError(err instanceof Error ? err.message : 'Failed to load limits');
    } finally {
      setIsLoading(false);
    }
  }, [checkInvitationLimit, serviceLoading]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const canSendInvitations = useCallback((count: number = 1): boolean => {
    if (!invitationLimits) return false;
    return invitationLimits.maxInvitations === -1 || invitationLimits.maxInvitations >= count;
  }, [invitationLimits]);

  const pendingInvitations = useMemo(() => {
    return invitationLimits?.pendingInvitations || 0;
  }, [invitationLimits]);

  const maxInvitations = useMemo(() => {
    return invitationLimits?.maxInvitations || 0;
  }, [invitationLimits]);

  const refresh = useCallback(async () => {
    await loadData();
  }, [loadData]);

  return {
    invitationLimits,
    isLoading: isLoading || serviceLoading,
    error: error || serviceError,
    canSendInvitations,
    pendingInvitations,
    maxInvitations,
    refresh
  };
}

// ===== ENFORCEMENT ACTIONS HOOK =====

export function useEnforcementActions(): UseEnforcementActionsReturn {
  const { enforceUserLimit, isLoading: serviceLoading, error: serviceError } = useSubscriptionEnforcement();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastAction, setLastAction] = useState<EnforcementAction | null>(null);

  const checkAction = useCallback(async (requestedUsers: number = 1): Promise<EnforcementAction> => {
    try {
      setIsLoading(true);
      setError(null);

      const action = await enforceUserLimit(requestedUsers);
      setLastAction(action);
      return action;
    } catch (err) {
      console.error('Error checking enforcement action:', err);
      const errorAction: EnforcementAction = {
        type: 'block',
        message: 'Error checking limits'
      };
      setError(err instanceof Error ? err.message : 'Failed to check action');
      setLastAction(errorAction);
      return errorAction;
    } finally {
      setIsLoading(false);
    }
  }, [enforceUserLimit]);

  return {
    checkAction,
    isLoading: isLoading || serviceLoading,
    error: error || serviceError,
    lastAction
  };
}
