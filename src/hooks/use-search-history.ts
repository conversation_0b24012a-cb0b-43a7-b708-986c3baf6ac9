"use client";

import { useState, useEffect, useCallback } from 'react';

const STORAGE_KEY = 'evexa-search-history';
const MAX_HISTORY_ITEMS = 50;

export interface SearchHistoryItem {
  id: string;
  query: string;
  timestamp: Date;
  filters?: Record<string, any>;
  resultCount?: number;
}

export function useSearchHistory() {
  const [searchHistory, setSearchHistory] = useState<SearchHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load search history from localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Convert timestamp strings back to Date objects
        const history = parsed.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        }));
        setSearchHistory(history);
      }
    } catch (error) {
      console.error('Failed to load search history:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save to localStorage whenever searchHistory changes
  const saveToStorage = useCallback((history: SearchHistoryItem[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(history));
    } catch (error) {
      console.error('Failed to save search history to localStorage:', error);
    }
  }, []);

  // Add a search to history
  const addToHistory = useCallback((
    query: string, 
    filters?: Record<string, any>, 
    resultCount?: number
  ) => {
    if (!query.trim()) return;

    const newItem: SearchHistoryItem = {
      id: `search-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      query: query.trim(),
      timestamp: new Date(),
      filters,
      resultCount
    };

    setSearchHistory(prev => {
      // Remove any existing identical searches
      const filtered = prev.filter(item => 
        item.query !== newItem.query || 
        JSON.stringify(item.filters) !== JSON.stringify(newItem.filters)
      );
      
      // Add new item at the beginning and limit to MAX_HISTORY_ITEMS
      const updated = [newItem, ...filtered].slice(0, MAX_HISTORY_ITEMS);
      saveToStorage(updated);
      return updated;
    });

    return newItem;
  }, [saveToStorage]);

  // Remove a specific item from history
  const removeFromHistory = useCallback((id: string) => {
    setSearchHistory(prev => {
      const updated = prev.filter(item => item.id !== id);
      saveToStorage(updated);
      return updated;
    });
  }, [saveToStorage]);

  // Clear all search history
  const clearHistory = useCallback(() => {
    setSearchHistory([]);
    localStorage.removeItem(STORAGE_KEY);
  }, []);

  // Get recent searches (unique queries only)
  const recentSearches = useCallback((limit: number = 10) => {
    const uniqueQueries = new Set<string>();
    return searchHistory
      .filter(item => {
        if (uniqueQueries.has(item.query)) {
          return false;
        }
        uniqueQueries.add(item.query);
        return true;
      })
      .slice(0, limit);
  }, [searchHistory]);

  // Get popular searches (most frequently searched)
  const popularSearches = useCallback((limit: number = 5) => {
    const queryCount = new Map<string, number>();
    
    searchHistory.forEach(item => {
      const count = queryCount.get(item.query) || 0;
      queryCount.set(item.query, count + 1);
    });

    return Array.from(queryCount.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([query, count]) => ({
        query,
        count,
        lastSearched: searchHistory.find(item => item.query === query)?.timestamp || new Date()
      }));
  }, [searchHistory]);

  // Search within history
  const searchHistory_internal = useCallback((searchQuery: string) => {
    if (!searchQuery.trim()) return searchHistory;
    
    const lowerQuery = searchQuery.toLowerCase();
    return searchHistory.filter(item => 
      item.query.toLowerCase().includes(lowerQuery)
    );
  }, [searchHistory]);

  // Get searches from a specific date range
  const getSearchesByDateRange = useCallback((startDate: Date, endDate: Date) => {
    return searchHistory.filter(item => 
      item.timestamp >= startDate && item.timestamp <= endDate
    );
  }, [searchHistory]);

  // Get search statistics
  const getStatistics = useCallback(() => {
    const totalSearches = searchHistory.length;
    const uniqueQueries = new Set(searchHistory.map(item => item.query)).size;
    const averageResultCount = searchHistory
      .filter(item => item.resultCount !== undefined)
      .reduce((sum, item) => sum + (item.resultCount || 0), 0) / 
      searchHistory.filter(item => item.resultCount !== undefined).length || 0;

    const searchesThisWeek = searchHistory.filter(item => {
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return item.timestamp >= weekAgo;
    }).length;

    const searchesToday = searchHistory.filter(item => {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return item.timestamp >= today;
    }).length;

    return {
      totalSearches,
      uniqueQueries,
      averageResultCount: Math.round(averageResultCount * 100) / 100,
      searchesThisWeek,
      searchesToday
    };
  }, [searchHistory]);

  // Export search history
  const exportHistory = useCallback(() => {
    const dataStr = JSON.stringify(searchHistory, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `evexa-search-history-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }, [searchHistory]);

  return {
    searchHistory,
    isLoading,
    addToHistory,
    removeFromHistory,
    clearHistory,
    recentSearches,
    popularSearches,
    searchHistory: searchHistory_internal,
    getSearchesByDateRange,
    getStatistics,
    exportHistory
  };
}
