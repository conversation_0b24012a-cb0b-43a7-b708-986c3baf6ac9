/**
 * Persona Permissions Hook
 * React hook for managing persona-based permissions and module access
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { 
  createTenantPersonaService,
  type TenantPersonaService 
} from '@/services/tenantPersonaService';
import { 
  TenantPersona,
  PersonaAssignment,
  ModuleAccess,
  EvexaModule,
  PermissionAction,
  MODULE_METADATA
} from '@/types/personas';

// ===== HOOK TYPES =====

interface PersonaPermissionsState {
  userPersona: TenantPersona | null;
  moduleAccess: ModuleAccess[];
  isLoading: boolean;
  error: string | null;
}

interface PersonaPermissionsActions {
  checkModuleAccess: (module: EvexaModule, action: PermissionAction) => boolean;
  getModulePermissions: (module: EvexaModule) => PermissionAction[];
  hasSystemPermission: (permission: keyof TenantPersona['permissions']['systemPermissions']) => boolean;
  refreshPermissions: () => Promise<void>;
  assignPersona: (personaId: string) => Promise<void>;
}

// ===== MAIN HOOK =====

export function usePersonaPermissions(): PersonaPermissionsState & PersonaPermissionsActions {
  const { user, tenantId } = useAuth();
  const [state, setState] = useState<PersonaPermissionsState>({
    userPersona: null,
    moduleAccess: [],
    isLoading: true,
    error: null
  });

  // Create persona service instance
  const personaService = useMemo(() => {
    if (!tenantId) return null;
    return createTenantPersonaService(tenantId);
  }, [tenantId]);

  // Load user's persona and permissions
  const loadPersonaPermissions = useCallback(async () => {
    if (!personaService || !user?.uid) {
      setState(prev => ({ ...prev, isLoading: false }));
      return;
    }

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const [userPersona, moduleAccess] = await Promise.all([
        personaService.getUserPersona(user.uid),
        personaService.getUserAccessibleModules(user.uid)
      ]);

      setState({
        userPersona,
        moduleAccess,
        isLoading: false,
        error: null
      });

    } catch (error) {
      console.error('Error loading persona permissions:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load permissions'
      }));
    }
  }, [personaService, user?.uid]);

  // Load permissions on mount and when dependencies change
  useEffect(() => {
    loadPersonaPermissions();
  }, [loadPersonaPermissions]);

  // ===== PERMISSION CHECKING FUNCTIONS =====

  const checkModuleAccess = useCallback((
    module: EvexaModule, 
    action: PermissionAction
  ): boolean => {
    if (!state.userPersona) return false;

    const modulePermission = state.userPersona.permissions.modules.find(m => m.module === module);
    return modulePermission ? modulePermission.actions.includes(action) : false;
  }, [state.userPersona]);

  const getModulePermissions = useCallback((module: EvexaModule): PermissionAction[] => {
    if (!state.userPersona) return [];

    const modulePermission = state.userPersona.permissions.modules.find(m => m.module === module);
    return modulePermission ? modulePermission.actions : [];
  }, [state.userPersona]);

  const hasSystemPermission = useCallback((
    permission: keyof TenantPersona['permissions']['systemPermissions']
  ): boolean => {
    if (!state.userPersona) return false;
    return state.userPersona.permissions.systemPermissions[permission];
  }, [state.userPersona]);

  const refreshPermissions = useCallback(async () => {
    await loadPersonaPermissions();
  }, [loadPersonaPermissions]);

  const assignPersona = useCallback(async (personaId: string) => {
    if (!personaService || !user?.uid) {
      throw new Error('Cannot assign persona: missing service or user');
    }

    try {
      await personaService.assignPersonaToUser(personaId, user.uid, user.uid);
      await refreshPermissions();
    } catch (error) {
      console.error('Error assigning persona:', error);
      throw error;
    }
  }, [personaService, user?.uid, refreshPermissions]);

  return {
    // State
    userPersona: state.userPersona,
    moduleAccess: state.moduleAccess,
    isLoading: state.isLoading,
    error: state.error,

    // Actions
    checkModuleAccess,
    getModulePermissions,
    hasSystemPermission,
    refreshPermissions,
    assignPersona
  };
}

// ===== SPECIALIZED HOOKS =====

/**
 * Hook for checking specific module access
 */
export function useModuleAccess(module: EvexaModule, action: PermissionAction = 'read') {
  const { checkModuleAccess, getModulePermissions, isLoading } = usePersonaPermissions();

  const hasAccess = useMemo(() => 
    checkModuleAccess(module, action), 
    [checkModuleAccess, module, action]
  );

  const permissions = useMemo(() => 
    getModulePermissions(module), 
    [getModulePermissions, module]
  );

  const moduleMetadata = useMemo(() => 
    MODULE_METADATA[module], 
    [module]
  );

  return {
    hasAccess,
    permissions,
    moduleMetadata,
    isLoading,
    canRead: permissions.includes('read'),
    canWrite: permissions.includes('write'),
    canDelete: permissions.includes('delete'),
    canAdmin: permissions.includes('admin')
  };
}

/**
 * Hook for checking system permissions
 */
export function useSystemPermissions() {
  const { hasSystemPermission, userPersona, isLoading } = usePersonaPermissions();

  const permissions = useMemo(() => {
    if (!userPersona) {
      return {
        canManageUsers: false,
        canManageSettings: false,
        canViewAnalytics: false,
        canExportData: false,
        canManageIntegrations: false,
        canAccessSupport: false
      };
    }

    return userPersona.permissions.systemPermissions;
  }, [userPersona]);

  return {
    permissions,
    isLoading,
    hasSystemPermission,
    canManageUsers: permissions.canManageUsers,
    canManageSettings: permissions.canManageSettings,
    canViewAnalytics: permissions.canViewAnalytics,
    canExportData: permissions.canExportData,
    canManageIntegrations: permissions.canManageIntegrations,
    canAccessSupport: permissions.canAccessSupport
  };
}

/**
 * Hook for persona management (admin only)
 */
export function usePersonaManagement() {
  const { tenantId } = useAuth();
  const { hasSystemPermission } = usePersonaPermissions();
  const [personas, setPersonas] = useState<TenantPersona[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const personaService = useMemo(() => {
    if (!tenantId) return null;
    return createTenantPersonaService(tenantId);
  }, [tenantId]);

  const canManagePersonas = hasSystemPermission('canManageUsers');

  const loadPersonas = useCallback(async () => {
    if (!personaService || !canManagePersonas) return;

    try {
      setIsLoading(true);
      setError(null);
      const allPersonas = await personaService.getAllPersonas();
      setPersonas(allPersonas);
    } catch (err) {
      console.error('Error loading personas:', err);
      setError(err instanceof Error ? err.message : 'Failed to load personas');
    } finally {
      setIsLoading(false);
    }
  }, [personaService, canManagePersonas]);

  const createPersona = useCallback(async (
    personaData: Omit<TenantPersona, 'id' | 'tenantId' | 'category' | 'createdAt' | 'updatedAt'>
  ) => {
    if (!personaService || !canManagePersonas) {
      throw new Error('Access denied');
    }

    const newPersona = await personaService.createCustomPersona(personaData);
    await loadPersonas();
    return newPersona;
  }, [personaService, canManagePersonas, loadPersonas]);

  const updatePersona = useCallback(async (
    personaId: string,
    updates: Partial<TenantPersona>
  ) => {
    if (!personaService || !canManagePersonas) {
      throw new Error('Access denied');
    }

    await personaService.updateCustomPersona(personaId, updates);
    await loadPersonas();
  }, [personaService, canManagePersonas, loadPersonas]);

  const deletePersona = useCallback(async (personaId: string) => {
    if (!personaService || !canManagePersonas) {
      throw new Error('Access denied');
    }

    await personaService.deleteCustomPersona(personaId);
    await loadPersonas();
  }, [personaService, canManagePersonas, loadPersonas]);

  const assignPersonaToUser = useCallback(async (
    userId: string,
    personaId: string,
    assignedBy: string
  ) => {
    if (!personaService || !canManagePersonas) {
      throw new Error('Access denied');
    }

    return personaService.assignPersonaToUser(userId, personaId, assignedBy);
  }, [personaService, canManagePersonas]);

  useEffect(() => {
    if (canManagePersonas) {
      loadPersonas();
    }
  }, [canManagePersonas, loadPersonas]);

  return {
    personas,
    isLoading,
    error,
    canManagePersonas,
    loadPersonas,
    createPersona,
    updatePersona,
    deletePersona,
    assignPersonaToUser
  };
}

// ===== UTILITY HOOKS =====

/**
 * Hook to get accessible modules for current user
 */
export function useAccessibleModules() {
  const { moduleAccess, isLoading } = usePersonaPermissions();

  const accessibleModules = useMemo(() => 
    moduleAccess.filter(m => m.hasAccess),
    [moduleAccess]
  );

  const modulesByCategory = useMemo(() => {
    const categories: Record<string, ModuleAccess[]> = {};
    
    accessibleModules.forEach(moduleAccess => {
      const metadata = MODULE_METADATA[moduleAccess.module];
      if (!categories[metadata.category]) {
        categories[metadata.category] = [];
      }
      categories[metadata.category].push(moduleAccess);
    });

    return categories;
  }, [accessibleModules]);

  return {
    accessibleModules,
    modulesByCategory,
    isLoading,
    totalAccessibleModules: accessibleModules.length
  };
}
