/**
 * Subscription Limits Hook
 * React hook for managing subscription limits and enforcement
 */

"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { 
  getSubscriptionTier,
  checkLimits,
  canPerformAction,
  type SubscriptionTier,
  type UsageStats,
  type LimitCheckResult,
  type SubscriptionLimits
} from '@/lib/subscription-limits';
import { 
  collection, 
  query, 
  where, 
  getDocs, 
  getCountFromServer 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

interface SubscriptionLimitsHook {
  subscriptionTier: SubscriptionTier | null;
  usage: UsageStats;
  limits: Record<keyof SubscriptionLimits, LimitCheckResult>;
  isLoading: boolean;
  error: string | null;
  canCreateUser: () => LimitCheckResult;
  canCreateExhibition: () => LimitCheckResult;
  canCreateEvent: () => LimitCheckResult;
  canUseFeature: (feature: keyof SubscriptionLimits) => LimitCheckResult;
  refreshUsage: () => Promise<void>;
  getUsagePercentage: (limitType: keyof SubscriptionLimits) => number;
  isNearLimit: (limitType: keyof SubscriptionLimits, threshold?: number) => boolean;
  isAtLimit: (limitType: keyof SubscriptionLimits) => boolean;
}

export function useSubscriptionLimits(): SubscriptionLimitsHook {
  const { user, tenant } = useAuth();
  const [subscriptionTier, setSubscriptionTier] = useState<SubscriptionTier | null>(null);
  const [usage, setUsage] = useState<UsageStats>({
    users: 0,
    exhibitions: 0,
    events: 0,
    storage: 0,
    apiCalls: 0
  });
  const [limits, setLimits] = useState<Record<keyof SubscriptionLimits, LimitCheckResult>>({} as any);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch current usage from Firestore
  const fetchUsage = useCallback(async (): Promise<UsageStats> => {
    if (!tenant?.id) {
      throw new Error('No tenant ID available');
    }

    try {
      // Count users
      const usersQuery = query(
        collection(db, 'users'),
        where('tenantId', '==', tenant.id)
      );
      const usersSnapshot = await getCountFromServer(usersQuery);
      const usersCount = usersSnapshot.data().count;

      // Count exhibitions
      const exhibitionsQuery = query(
        collection(db, 'exhibitions'),
        where('tenantId', '==', tenant.id)
      );
      const exhibitionsSnapshot = await getCountFromServer(exhibitionsQuery);
      const exhibitionsCount = exhibitionsSnapshot.data().count;

      // Count events
      const eventsQuery = query(
        collection(db, 'events'),
        where('tenantId', '==', tenant.id)
      );
      const eventsSnapshot = await getCountFromServer(eventsQuery);
      const eventsCount = eventsSnapshot.data().count;

      // Calculate storage usage (simplified - in real implementation, sum file sizes)
      const storageUsage = Math.floor(Math.random() * 500); // Mock data for now

      // Get API calls for current month (simplified)
      const apiCalls = Math.floor(Math.random() * 100); // Mock data for now

      return {
        users: usersCount,
        exhibitions: exhibitionsCount,
        events: eventsCount,
        storage: storageUsage,
        apiCalls: apiCalls
      };

    } catch (error) {
      console.error('Error fetching usage:', error);
      throw error;
    }
  }, [tenant?.id]);

  // Refresh usage data
  const refreshUsage = useCallback(async () => {
    if (!tenant?.id) return;

    setIsLoading(true);
    setError(null);

    try {
      const newUsage = await fetchUsage();
      setUsage(newUsage);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch usage');
    } finally {
      setIsLoading(false);
    }
  }, [fetchUsage, tenant?.id]);

  // Initialize subscription tier and usage
  useEffect(() => {
    if (!tenant) return;

    // Get subscription tier (default to basic if not set)
    const tierData = getSubscriptionTier(tenant.subscriptionTier || 'basic');
    setSubscriptionTier(tierData);

    // Fetch initial usage
    refreshUsage();
  }, [tenant, refreshUsage]);

  // Update limits when tier or usage changes
  useEffect(() => {
    if (!subscriptionTier) return;

    const limitResults = checkLimits(usage, subscriptionTier);
    setLimits(limitResults);
  }, [subscriptionTier, usage]);

  // Helper functions
  const canCreateUser = useCallback((): LimitCheckResult => {
    if (!subscriptionTier) return { allowed: false, reason: 'No subscription tier' };
    return canPerformAction('create_user', usage, subscriptionTier);
  }, [subscriptionTier, usage]);

  const canCreateExhibition = useCallback((): LimitCheckResult => {
    if (!subscriptionTier) return { allowed: false, reason: 'No subscription tier' };
    return canPerformAction('create_exhibition', usage, subscriptionTier);
  }, [subscriptionTier, usage]);

  const canCreateEvent = useCallback((): LimitCheckResult => {
    if (!subscriptionTier) return { allowed: false, reason: 'No subscription tier' };
    return canPerformAction('create_event', usage, subscriptionTier);
  }, [subscriptionTier, usage]);

  const canUseFeature = useCallback((feature: keyof SubscriptionLimits): LimitCheckResult => {
    if (!subscriptionTier) return { allowed: false, reason: 'No subscription tier' };
    return canPerformAction('use_feature', usage, subscriptionTier, feature);
  }, [subscriptionTier, usage]);

  const getUsagePercentage = useCallback((limitType: keyof SubscriptionLimits): number => {
    if (!subscriptionTier) return 0;
    
    const limit = subscriptionTier.limits[limitType];
    const currentUsage = usage[limitType as keyof UsageStats] || 0;
    
    if (typeof limit !== 'number' || limit === -1) return 0;
    return Math.min(Math.round((currentUsage / limit) * 100), 100);
  }, [subscriptionTier, usage]);

  const isNearLimit = useCallback((limitType: keyof SubscriptionLimits, threshold = 80): boolean => {
    const percentage = getUsagePercentage(limitType);
    return percentage >= threshold;
  }, [getUsagePercentage]);

  const isAtLimit = useCallback((limitType: keyof SubscriptionLimits): boolean => {
    const limitResult = limits[limitType];
    return limitResult ? !limitResult.allowed : false;
  }, [limits]);

  return {
    subscriptionTier,
    usage,
    limits,
    isLoading,
    error,
    canCreateUser,
    canCreateExhibition,
    canCreateEvent,
    canUseFeature,
    refreshUsage,
    getUsagePercentage,
    isNearLimit,
    isAtLimit
  };
}

/**
 * Hook for checking specific subscription limits
 */
export function useSubscriptionLimit(limitType: keyof SubscriptionLimits) {
  const {
    subscriptionTier,
    usage,
    limits,
    isLoading,
    getUsagePercentage,
    isNearLimit,
    isAtLimit
  } = useSubscriptionLimits();

  const limitResult = limits[limitType];
  const percentage = getUsagePercentage(limitType);
  const nearLimit = isNearLimit(limitType);
  const atLimit = isAtLimit(limitType);

  return {
    subscriptionTier,
    limitResult,
    percentage,
    nearLimit,
    atLimit,
    isLoading,
    currentUsage: usage[limitType as keyof UsageStats] || 0,
    limit: subscriptionTier?.limits[limitType]
  };
}

/**
 * Hook for feature availability checking
 */
export function useFeatureAvailability(feature: keyof SubscriptionLimits) {
  const { subscriptionTier, canUseFeature, isLoading } = useSubscriptionLimits();
  
  const featureResult = canUseFeature(feature);
  const isAvailable = featureResult.allowed;
  const requiresUpgrade = featureResult.upgradeRequired;

  return {
    isAvailable,
    requiresUpgrade,
    subscriptionTier,
    isLoading,
    reason: featureResult.reason,
    suggestedTier: featureResult.suggestedTier
  };
}
