"use client";

import React, { useState, useCallback, useRef } from 'react';

export interface FileUploadFile {
  id: string;
  file: File;
  preview?: string;
  progress?: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

export interface UseFileUploadOptions {
  accept?: string;
  maxSize?: number; // in bytes
  maxFiles?: number;
  multiple?: boolean;
  onUpload?: (files: FileUploadFile[]) => Promise<void>;
  onError?: (error: string) => void;
}

export interface UseFileUploadReturn {
  files: FileUploadFile[];
  isDragging: boolean;
  errors: string[];
  isUploading: boolean;
  handleDragEnter: (e: React.DragEvent) => void;
  handleDragLeave: (e: React.DragEvent) => void;
  handleDragOver: (e: React.DragEvent) => void;
  handleDrop: (e: React.DragEvent) => void;
  openFileDialog: () => void;
  removeFile: (id: string) => void;
  clearFiles: () => void;
  getInputProps: () => React.InputHTMLAttributes<HTMLInputElement>;
  uploadFiles: () => Promise<void>;
}

export function useFileUpload(options: UseFileUploadOptions = {}): [
  { files: FileUploadFile[]; isDragging: boolean; errors: string[]; isUploading: boolean },
  {
    handleDragEnter: (e: React.DragEvent) => void;
    handleDragLeave: (e: React.DragEvent) => void;
    handleDragOver: (e: React.DragEvent) => void;
    handleDrop: (e: React.DragEvent) => void;
    openFileDialog: () => void;
    removeFile: (id: string) => void;
    clearFiles: () => void;
    getInputProps: () => React.InputHTMLAttributes<HTMLInputElement>;
    uploadFiles: () => Promise<void>;
  }
] {
  const {
    accept = '*/*',
    maxSize = 10 * 1024 * 1024, // 10MB default
    maxFiles = 1,
    multiple = false,
    onUpload,
    onError
  } = options;

  const [files, setFiles] = useState<FileUploadFile[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dragCounterRef = useRef(0);

  const validateFile = useCallback((file: File): string | null => {
    // Check file size
    if (maxSize && file.size > maxSize) {
      const maxSizeMB = (maxSize / (1024 * 1024)).toFixed(1);
      return `File size must be less than ${maxSizeMB}MB`;
    }

    // Check file type
    if (accept !== '*/*') {
      const acceptedTypes = accept.split(',').map(type => type.trim());
      const isAccepted = acceptedTypes.some(acceptedType => {
        if (acceptedType.startsWith('.')) {
          return file.name.toLowerCase().endsWith(acceptedType.toLowerCase());
        }
        if (acceptedType.includes('*')) {
          const baseType = acceptedType.split('/')[0];
          return file.type.startsWith(baseType);
        }
        return file.type === acceptedType;
      });

      if (!isAccepted) {
        return `File type not supported. Accepted types: ${accept}`;
      }
    }

    return null;
  }, [accept, maxSize]);

  const createFileUploadFile = useCallback((file: File): FileUploadFile => {
    const id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Create preview for images
    let preview: string | undefined;
    if (file.type.startsWith('image/')) {
      preview = URL.createObjectURL(file);
    }

    return {
      id,
      file,
      preview,
      status: 'pending'
    };
  }, []);

  const addFiles = useCallback((newFiles: File[]) => {
    const validFiles: FileUploadFile[] = [];
    const newErrors: string[] = [];

    newFiles.forEach(file => {
      const error = validateFile(file);
      if (error) {
        newErrors.push(`${file.name}: ${error}`);
      } else {
        validFiles.push(createFileUploadFile(file));
      }
    });

    setFiles(prev => {
      const combined = multiple ? [...prev, ...validFiles] : validFiles;
      return combined.slice(0, maxFiles);
    });

    setErrors(newErrors);
    if (newErrors.length > 0) {
      onError?.(newErrors[0]);
    }
  }, [validateFile, createFileUploadFile, multiple, maxFiles, onError]);

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current++;
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragging(true);
    }
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current--;
    if (dragCounterRef.current === 0) {
      setIsDragging(false);
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    dragCounterRef.current = 0;

    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length > 0) {
      addFiles(droppedFiles);
    }
  }, [addFiles]);

  const openFileDialog = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || []);
    if (selectedFiles.length > 0) {
      addFiles(selectedFiles);
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  }, [addFiles]);

  const removeFile = useCallback((id: string) => {
    setFiles(prev => {
      const fileToRemove = prev.find(f => f.id === id);
      if (fileToRemove?.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
      return prev.filter(f => f.id !== id);
    });
    setErrors([]);
  }, []);

  const clearFiles = useCallback(() => {
    files.forEach(file => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview);
      }
    });
    setFiles([]);
    setErrors([]);
  }, [files]);

  const uploadFiles = useCallback(async () => {
    if (!onUpload || files.length === 0) return;

    setIsUploading(true);
    try {
      await onUpload(files);
      setFiles(prev => prev.map(file => ({ ...file, status: 'completed' })));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setFiles(prev => prev.map(file => ({ ...file, status: 'error', error: errorMessage })));
      setErrors([errorMessage]);
      onError?.(errorMessage);
    } finally {
      setIsUploading(false);
    }
  }, [files, onUpload, onError]);

  const getInputProps = useCallback((): React.InputHTMLAttributes<HTMLInputElement> => ({
    ref: fileInputRef,
    type: 'file',
    accept,
    multiple: multiple && maxFiles > 1,
    onChange: handleFileInputChange,
    style: { display: 'none' }
  }), [accept, multiple, maxFiles, handleFileInputChange]);

  // Cleanup previews on unmount
  React.useEffect(() => {
    return () => {
      files.forEach(file => {
        if (file.preview) {
          URL.revokeObjectURL(file.preview);
        }
      });
    };
  }, []);

  return [
    { files, isDragging, errors, isUploading },
    {
      handleDragEnter,
      handleDragLeave,
      handleDragOver,
      handleDrop,
      openFileDialog,
      removeFile,
      clearFiles,
      getInputProps,
      uploadFiles
    }
  ];
}
