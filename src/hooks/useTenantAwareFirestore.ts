/**
 * Tenant-Aware Firestore Hook
 * Provides easy-to-use hooks for tenant-scoped database operations
 */

import { useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import type { WhereFilterOp } from 'firebase/firestore';
import type { TenantAwareEntity } from '@/types/firestore';
import {
  validateTenantId,
  getTenantDocuments,
  getTenantDocumentsWithFilters,
  getTenantDocumentById,
  addTenantDocument,
  updateTenantDocument,
  deleteTenantDocument,
  countTenantDocuments,
  TenantAwareBatch
} from '@/services/tenantIdHelperService';

/**
 * Hook for tenant-aware Firestore operations
 * Automatically uses the current user's tenantId for all operations
 */
export function useTenantAwareFirestore() {
  const { tenantId } = useAuth();

  // Validate tenant context is available
  const ensureTenantId = useCallback((): string => {
    if (!tenantId) {
      throw new Error('Tenant context is required but not available. User must be authenticated with a valid tenant.');
    }
    return tenantId;
  }, [tenantId]);

  // ===== READ OPERATIONS =====

  /**
   * Get all documents from a collection for the current tenant
   */
  const getDocuments = useCallback(async <T extends TenantAwareEntity>(
    collectionName: string,
    sortField?: string,
    sortDirection: 'asc' | 'desc' = 'desc',
    limitCount?: number
  ): Promise<T[]> => {
    const currentTenantId = ensureTenantId();
    return getTenantDocuments<T>(currentTenantId, collectionName, sortField, sortDirection, limitCount);
  }, [ensureTenantId]);

  /**
   * Get documents with custom filters for the current tenant
   */
  const getDocumentsWithFilters = useCallback(async <T extends TenantAwareEntity>(
    collectionName: string,
    filters: Array<{ field: string; operator: WhereFilterOp; value: any }>,
    sortField?: string,
    sortDirection: 'asc' | 'desc' = 'desc',
    limitCount?: number
  ): Promise<T[]> => {
    const currentTenantId = ensureTenantId();
    return getTenantDocumentsWithFilters<T>(
      currentTenantId,
      collectionName,
      filters,
      sortField,
      sortDirection,
      limitCount
    );
  }, [ensureTenantId]);

  /**
   * Get a single document by ID for the current tenant
   */
  const getDocumentById = useCallback(async <T extends TenantAwareEntity>(
    collectionName: string,
    documentId: string
  ): Promise<T | null> => {
    const currentTenantId = ensureTenantId();
    return getTenantDocumentById<T>(currentTenantId, collectionName, documentId);
  }, [ensureTenantId]);

  /**
   * Count documents in a collection for the current tenant
   */
  const countDocuments = useCallback(async (
    collectionName: string,
    filters: Array<{ field: string; operator: WhereFilterOp; value: any }> = []
  ): Promise<number> => {
    const currentTenantId = ensureTenantId();
    return countTenantDocuments(currentTenantId, collectionName, filters);
  }, [ensureTenantId]);

  // ===== WRITE OPERATIONS =====

  /**
   * Add a document with automatic tenantId stamping
   */
  const addDocument = useCallback(async <T extends TenantAwareEntity>(
    collectionName: string,
    data: Omit<T, 'id' | 'tenantId' | 'createdAt' | 'updatedAt'>
  ): Promise<T> => {
    const currentTenantId = ensureTenantId();
    return addTenantDocument<T>(currentTenantId, collectionName, data);
  }, [ensureTenantId]);

  /**
   * Update a document with tenant validation
   */
  const updateDocument = useCallback(async <T extends TenantAwareEntity>(
    collectionName: string,
    documentId: string,
    updates: Partial<Omit<T, 'id' | 'tenantId' | 'createdAt'>>
  ): Promise<void> => {
    const currentTenantId = ensureTenantId();
    return updateTenantDocument<T>(currentTenantId, collectionName, documentId, updates);
  }, [ensureTenantId]);

  /**
   * Delete a document with tenant validation
   */
  const deleteDocument = useCallback(async (
    collectionName: string,
    documentId: string
  ): Promise<void> => {
    const currentTenantId = ensureTenantId();
    return deleteTenantDocument(currentTenantId, collectionName, documentId);
  }, [ensureTenantId]);

  // ===== BATCH OPERATIONS =====

  /**
   * Create a tenant-aware batch for multiple operations
   */
  const createBatch = useCallback((): TenantAwareBatch => {
    const currentTenantId = ensureTenantId();
    return new TenantAwareBatch(currentTenantId);
  }, [ensureTenantId]);

  // ===== UTILITY FUNCTIONS =====

  /**
   * Check if tenant context is available
   */
  const hasTenantContext = useCallback((): boolean => {
    return !!tenantId;
  }, [tenantId]);

  /**
   * Get current tenant ID (throws if not available)
   */
  const getCurrentTenantId = useCallback((): string => {
    return ensureTenantId();
  }, [ensureTenantId]);

  /**
   * Validate that data belongs to current tenant
   */
  const validateDocumentOwnership = useCallback(<T extends TenantAwareEntity>(
    document: T
  ): void => {
    const currentTenantId = ensureTenantId();
    if (document.tenantId !== currentTenantId) {
      throw new Error(`Document belongs to different tenant. Expected: ${currentTenantId}, Got: ${document.tenantId}`);
    }
  }, [ensureTenantId]);

  return {
    // Read operations
    getDocuments,
    getDocumentsWithFilters,
    getDocumentById,
    countDocuments,
    
    // Write operations
    addDocument,
    updateDocument,
    deleteDocument,
    
    // Batch operations
    createBatch,
    
    // Utility functions
    hasTenantContext,
    getCurrentTenantId,
    validateDocumentOwnership,
    
    // Current tenant info
    tenantId
  };
}

/**
 * Hook for specific collection operations
 * Provides type-safe operations for a specific collection
 */
export function useTenantCollection<T extends TenantAwareEntity>(collectionName: string) {
  const {
    getDocuments,
    getDocumentsWithFilters,
    getDocumentById,
    addDocument,
    updateDocument,
    deleteDocument,
    countDocuments,
    validateDocumentOwnership,
    tenantId
  } = useTenantAwareFirestore();

  return {
    // Read operations
    getAll: (sortField?: string, sortDirection: 'asc' | 'desc' = 'desc', limitCount?: number) =>
      getDocuments<T>(collectionName, sortField, sortDirection, limitCount),
    
    getWithFilters: (
      filters: Array<{ field: string; operator: WhereFilterOp; value: any }>,
      sortField?: string,
      sortDirection: 'asc' | 'desc' = 'desc',
      limitCount?: number
    ) => getDocumentsWithFilters<T>(collectionName, filters, sortField, sortDirection, limitCount),
    
    getById: (documentId: string) => getDocumentById<T>(collectionName, documentId),
    
    count: (filters: Array<{ field: string; operator: WhereFilterOp; value: any }> = []) =>
      countDocuments(collectionName, filters),
    
    // Write operations
    add: (data: Omit<T, 'id' | 'tenantId' | 'createdAt' | 'updatedAt'>) =>
      addDocument<T>(collectionName, data),
    
    update: (documentId: string, updates: Partial<Omit<T, 'id' | 'tenantId' | 'createdAt'>>) =>
      updateDocument<T>(collectionName, documentId, updates),
    
    delete: (documentId: string) => deleteDocument(collectionName, documentId),
    
    // Utility
    validateOwnership: validateDocumentOwnership,
    collectionName,
    tenantId
  };
}

/**
 * Hook for common collection patterns
 */
export function useCommonCollections() {
  const exhibitions = useTenantCollection('exhibitions');
  const events = useTenantCollection('exhibition_events');
  const tasks = useTenantCollection('exhibition_tasks');
  const leads = useTenantCollection('lead_contacts');
  const vendors = useTenantCollection('vendor_profiles');
  const users = useTenantCollection('user_profiles');
  const budgets = useTenantCollection('budget_allocations');
  const expenses = useTenantCollection('expense_records');
  const purchaseOrders = useTenantCollection('purchase_orders');

  return {
    exhibitions,
    events,
    tasks,
    leads,
    vendors,
    users,
    budgets,
    expenses,
    purchaseOrders
  };
}
