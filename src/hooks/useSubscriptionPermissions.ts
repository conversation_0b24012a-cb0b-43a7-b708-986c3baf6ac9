/**
 * Subscription Permissions Hook
 * Combines persona-based permissions with subscription-based access control
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { usePersonaPermissions } from './usePersonaPermissions';
import { SubscriptionService } from '@/services/subscriptionService';
import { EvexaModule, PermissionAction, ModuleAccess } from '@/types/personas';

// ===== HOOK TYPES =====

interface SubscriptionPermissionsState {
  isLoading: boolean;
  error: string | null;
  subscriptionTier: string | null;
  hasActiveSubscription: boolean;
  moduleAccess: EnhancedModuleAccess[];
  subscriptionLimits: {
    maxUsers: number;
    maxExhibitions: number;
    maxStorage: number;
    currentUsers: number;
    currentExhibitions: number;
    currentStorage: number;
  } | null;
}

interface EnhancedModuleAccess extends ModuleAccess {
  subscriptionAllowed: boolean;
  subscriptionReason?: string;
  upgradeRequired?: string;
  finalAccess: boolean; // Combined persona + subscription access
}

interface SubscriptionPermissionsActions {
  checkModuleAccess: (module: EvexaModule, action: PermissionAction) => boolean;
  checkFeatureAccess: (feature: string) => boolean;
  checkUsageLimits: () => Promise<{ allowed: boolean; reason?: string }>;
  refreshSubscription: () => Promise<void>;
  canAssignPersona: (userCount?: number) => Promise<boolean>;
}

// ===== MAIN HOOK =====

export function useSubscriptionPermissions(): SubscriptionPermissionsState & SubscriptionPermissionsActions {
  const { tenantId } = useAuth();
  const {
    userPersona,
    moduleAccess: personaModuleAccess,
    checkModuleAccess: checkPersonaAccess,
    isLoading: personaLoading
  } = usePersonaPermissions();

  const [state, setState] = useState<SubscriptionPermissionsState>({
    isLoading: true,
    error: null,
    subscriptionTier: null,
    hasActiveSubscription: false,
    moduleAccess: [],
    subscriptionLimits: null
  });

  // Create subscription service instance
  const subscriptionService = useMemo(() => {
    if (!tenantId) return null;
    return new SubscriptionService(tenantId);
  }, [tenantId]);

  // Load subscription data
  const loadSubscriptionData = useCallback(async () => {
    if (!subscriptionService || !tenantId) {
      setState(prev => ({ ...prev, isLoading: false }));
      return;
    }

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // SUPER ADMIN BYPASS: Super admin gets unlimited access
      let isSuperAdmin = false;
      try {
        const { SUPER_ADMIN_USER_IDS } = require('@/services/superAdminService');
        isSuperAdmin = user && SUPER_ADMIN_USER_IDS.includes(user.id);
      } catch {
        // Continue with normal checks if super admin service fails
      }

      if (isSuperAdmin) {
        // Super admin gets unlimited access to everything
        const enhancedModuleAccess: EnhancedModuleAccess[] = personaModuleAccess.map(personaAccess => ({
          ...personaAccess,
          subscriptionAllowed: true,
          subscriptionReason: 'Super admin - unlimited access',
          upgradeRequired: undefined,
          finalAccess: true
        }));

        setState({
          isLoading: false,
          error: null,
          subscriptionTier: 'enterprise', // Super admin gets enterprise tier
          hasActiveSubscription: true,
          moduleAccess: enhancedModuleAccess,
          subscriptionLimits: {
            maxUsers: -1, // Unlimited
            maxExhibitions: -1, // Unlimited
            maxStorage: -1, // Unlimited
            currentUsers: 0,
            currentExhibitions: 0,
            currentStorage: 0
          }
        });
        return;
      }

      const [subscription, plan, usage] = await Promise.all([
        subscriptionService.getCurrentSubscription(),
        subscriptionService.getCurrentPlan(),
        subscriptionService.getCurrentUsage()
      ]);

      const hasActiveSubscription = !!subscription && subscription.status === 'active';
      const subscriptionTier = plan?.tier || null;

      // Get accessible modules from subscription
      const accessibleModules = hasActiveSubscription
        ? await subscriptionService.getAccessibleModules()
        : [];

      // Combine persona and subscription access
      const enhancedModuleAccess: EnhancedModuleAccess[] = personaModuleAccess.map(personaAccess => {
        const subscriptionAllowed = accessibleModules.includes(personaAccess.module);
        const finalAccess = personaAccess.hasAccess && subscriptionAllowed;

        return {
          ...personaAccess,
          subscriptionAllowed,
          subscriptionReason: subscriptionAllowed ? undefined : 'Module not included in subscription plan',
          upgradeRequired: subscriptionAllowed ? undefined : 'professional', // Would be determined by module metadata
          finalAccess
        };
      });

      const subscriptionLimits = plan && usage ? {
        maxUsers: plan.features.maxUsers,
        maxExhibitions: plan.features.maxExhibitions,
        maxStorage: plan.features.storageGB,
        currentUsers: usage.users || 0,
        currentExhibitions: usage.exhibitions || 0,
        currentStorage: usage.storageUsed || 0
      } : null;

      setState({
        isLoading: false,
        error: null,
        subscriptionTier,
        hasActiveSubscription,
        moduleAccess: enhancedModuleAccess,
        subscriptionLimits
      });

    } catch (error) {
      console.error('Error loading subscription data:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load subscription data'
      }));
    }
  }, [subscriptionService, tenantId, personaModuleAccess]);

  // Load data when dependencies change
  useEffect(() => {
    if (!personaLoading) {
      loadSubscriptionData();
    }
  }, [loadSubscriptionData, personaLoading]);

  // ===== PERMISSION CHECKING FUNCTIONS =====

  const checkModuleAccess = useCallback((
    module: EvexaModule,
    action: PermissionAction
  ): boolean => {
    // SUPER ADMIN BYPASS: Super admin has access to everything
    try {
      const { SUPER_ADMIN_USER_IDS } = require('@/services/superAdminService');
      if (user && SUPER_ADMIN_USER_IDS.includes(user.id)) {
        return true;
      }
    } catch {
      // Continue with normal checks if super admin service fails
    }

    // Check persona permission first
    const hasPersonaAccess = checkPersonaAccess(module, action);
    if (!hasPersonaAccess) {
      return false;
    }

    // Check subscription access
    const moduleAccess = state.moduleAccess.find(m => m.module === module);
    return moduleAccess ? moduleAccess.finalAccess : false;
  }, [checkPersonaAccess, state.moduleAccess, user]);

  const checkFeatureAccess = useCallback((feature: string): boolean => {
    // SUPER ADMIN BYPASS: Super admin has access to all features
    try {
      const { SUPER_ADMIN_USER_IDS } = require('@/services/superAdminService');
      if (user && SUPER_ADMIN_USER_IDS.includes(user.id)) {
        return true;
      }
    } catch {
      // Continue with normal checks if super admin service fails
    }

    if (!subscriptionService || !state.hasActiveSubscription) {
      return false;
    }

    // This would check against subscription plan features
    // For now, return true for basic features
    const basicFeatures = ['basic_analytics', 'email_support', 'standard_reports'];
    return basicFeatures.includes(feature);
  }, [subscriptionService, state.hasActiveSubscription, user]);

  const checkUsageLimits = useCallback(async (): Promise<{ allowed: boolean; reason?: string }> => {
    if (!subscriptionService) {
      return { allowed: false, reason: 'No subscription service available' };
    }

    try {
      const limits = await subscriptionService.checkPersonaAssignmentLimits(0);
      return {
        allowed: limits.allowed,
        reason: limits.reason
      };
    } catch (error) {
      return { allowed: false, reason: 'Error checking usage limits' };
    }
  }, [subscriptionService]);

  const refreshSubscription = useCallback(async () => {
    await loadSubscriptionData();
  }, [loadSubscriptionData]);

  const canAssignPersona = useCallback(async (userCount: number = 1): Promise<boolean> => {
    // SUPER ADMIN BYPASS: Super admin can assign unlimited personas
    try {
      const { SUPER_ADMIN_USER_IDS } = require('@/services/superAdminService');
      if (user && SUPER_ADMIN_USER_IDS.includes(user.id)) {
        return true;
      }
    } catch {
      // Continue with normal checks if super admin service fails
    }

    if (!subscriptionService) {
      return false;
    }

    try {
      const limits = await subscriptionService.checkPersonaAssignmentLimits(userCount);
      return limits.allowed;
    } catch (error) {
      console.error('Error checking persona assignment limits:', error);
      return false;
    }
  }, [subscriptionService]);

  return {
    // State
    isLoading: state.isLoading || personaLoading,
    error: state.error,
    subscriptionTier: state.subscriptionTier,
    hasActiveSubscription: state.hasActiveSubscription,
    moduleAccess: state.moduleAccess,
    subscriptionLimits: state.subscriptionLimits,

    // Actions
    checkModuleAccess,
    checkFeatureAccess,
    checkUsageLimits,
    refreshSubscription,
    canAssignPersona
  };
}

// ===== SPECIALIZED HOOKS =====

/**
 * Hook for checking specific module access with subscription
 */
export function useModuleSubscriptionAccess(module: EvexaModule, action: PermissionAction = 'read') {
  const { 
    checkModuleAccess, 
    moduleAccess, 
    isLoading, 
    hasActiveSubscription,
    subscriptionTier 
  } = useSubscriptionPermissions();

  const moduleInfo = useMemo(() => 
    moduleAccess.find(m => m.module === module),
    [moduleAccess, module]
  );

  const hasAccess = useMemo(() => 
    checkModuleAccess(module, action),
    [checkModuleAccess, module, action]
  );

  return {
    hasAccess,
    moduleInfo,
    isLoading,
    hasActiveSubscription,
    subscriptionTier,
    subscriptionAllowed: moduleInfo?.subscriptionAllowed || false,
    personaAllowed: moduleInfo?.hasAccess || false,
    upgradeRequired: moduleInfo?.upgradeRequired,
    accessReason: moduleInfo?.subscriptionReason || moduleInfo?.reason
  };
}

/**
 * Hook for subscription limits and usage
 */
export function useSubscriptionLimits() {
  const { 
    subscriptionLimits, 
    isLoading, 
    hasActiveSubscription,
    checkUsageLimits,
    canAssignPersona 
  } = useSubscriptionPermissions();

  const usagePercentages = useMemo(() => {
    if (!subscriptionLimits) return null;

    return {
      users: (subscriptionLimits.currentUsers / subscriptionLimits.maxUsers) * 100,
      exhibitions: (subscriptionLimits.currentExhibitions / subscriptionLimits.maxExhibitions) * 100,
      storage: (subscriptionLimits.currentStorage / subscriptionLimits.maxStorage) * 100
    };
  }, [subscriptionLimits]);

  const isNearLimit = useMemo(() => {
    if (!usagePercentages) return false;
    return Object.values(usagePercentages).some(percentage => percentage > 80);
  }, [usagePercentages]);

  const isOverLimit = useMemo(() => {
    if (!usagePercentages) return false;
    return Object.values(usagePercentages).some(percentage => percentage >= 100);
  }, [usagePercentages]);

  return {
    subscriptionLimits,
    usagePercentages,
    isNearLimit,
    isOverLimit,
    isLoading,
    hasActiveSubscription,
    checkUsageLimits,
    canAssignPersona
  };
}

/**
 * Hook for feature access checking
 */
export function useFeatureAccess(feature: string) {
  const { 
    checkFeatureAccess, 
    isLoading, 
    hasActiveSubscription,
    subscriptionTier 
  } = useSubscriptionPermissions();

  const hasAccess = useMemo(() => 
    checkFeatureAccess(feature),
    [checkFeatureAccess, feature]
  );

  return {
    hasAccess,
    isLoading,
    hasActiveSubscription,
    subscriptionTier
  };
}
