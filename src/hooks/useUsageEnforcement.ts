'use client';

import { useState, useCallback } from 'react';
import { useTenantAware } from './useTenantAware';
import { subscriptionService, type SubscriptionPlan } from '@/services/subscriptionService';
import { automatedBillingService } from '@/services/automatedBillingService';

export interface UsageCheckResult {
  allowed: boolean;
  current: number;
  limit: number;
  percentage: number;
  message?: string;
  requiresUpgrade?: boolean;
}

export interface UsageEnforcementOptions {
  showWarnings?: boolean;
  trackUsage?: boolean;
  resourceId?: string;
  metadata?: Record<string, any>;
}

/**
 * Hook for enforcing usage limits and tracking resource consumption
 */
export function useUsageEnforcement() {
  const { tenant, getCurrentUsage, getUsageLimits } = useTenantAware();
  const [isChecking, setIsChecking] = useState(false);

  /**
   * Check if a specific usage limit allows the action
   */
  const checkUsageLimit = useCallback(async (
    limitType: keyof SubscriptionPlan['features'],
    options: UsageEnforcementOptions = {}
  ): Promise<UsageCheckResult> => {
    if (!tenant) {
      return {
        allowed: false,
        current: 0,
        limit: 0,
        percentage: 0,
        message: 'No tenant context available',
        requiresUpgrade: true
      };
    }

    try {
      setIsChecking(true);
      
      const result = await subscriptionService.checkUsageLimit(tenant.id, limitType);
      const percentage = result.limit === -1 ? 0 : (result.current / result.limit) * 100;

      let message = '';
      let requiresUpgrade = false;

      if (!result.allowed) {
        message = `You've reached your ${getLimitDisplayName(limitType)} limit (${result.current}/${result.limit}). Upgrade to continue.`;
        requiresUpgrade = true;
      } else if (percentage >= 80 && options.showWarnings) {
        message = `You're using ${percentage.toFixed(0)}% of your ${getLimitDisplayName(limitType)} limit.`;
      }

      return {
        allowed: result.allowed,
        current: result.current,
        limit: result.limit,
        percentage,
        message,
        requiresUpgrade
      };
    } catch (error) {
      console.error('Error checking usage limit:', error);
      return {
        allowed: false,
        current: 0,
        limit: 0,
        percentage: 0,
        message: 'Error checking usage limit',
        requiresUpgrade: true
      };
    } finally {
      setIsChecking(false);
    }
  }, [tenant]);

  /**
   * Check and enforce usage limit before performing an action
   */
  const enforceUsageLimit = useCallback(async (
    limitType: keyof SubscriptionPlan['features'],
    action: () => Promise<void> | void,
    options: UsageEnforcementOptions = {}
  ): Promise<{ success: boolean; result: UsageCheckResult }> => {
    const result = await checkUsageLimit(limitType, options);

    if (!result.allowed) {
      return { success: false, result };
    }

    try {
      await action();

      // Track usage if enabled
      if (options.trackUsage && tenant) {
        await trackResourceUsage(limitType, options.resourceId, options.metadata);
      }

      return { success: true, result };
    } catch (error) {
      console.error('Error executing action:', error);
      return { 
        success: false, 
        result: { 
          ...result, 
          message: 'Error executing action',
          allowed: false 
        } 
      };
    }
  }, [checkUsageLimit, tenant]);

  /**
   * Track resource usage
   */
  const trackResourceUsage = useCallback(async (
    limitType: keyof SubscriptionPlan['features'],
    resourceId?: string,
    metadata: Record<string, any> = {}
  ) => {
    if (!tenant) return;

    try {
      const eventType = getEventTypeFromLimit(limitType);
      if (eventType) {
        await automatedBillingService.trackUsage(
          tenant.id,
          eventType,
          resourceId || `${limitType}_${Date.now()}`,
          1,
          metadata
        );
      }
    } catch (error) {
      console.error('Error tracking usage:', error);
    }
  }, [tenant]);

  /**
   * Get current usage status for all limits
   */
  const getUsageStatus = useCallback(async () => {
    if (!tenant) return null;

    const usage = getCurrentUsage();
    const limits = getUsageLimits();

    if (!usage || !limits) return null;

    const status = {
      exhibitions: {
        current: usage.exhibitions || 0,
        limit: limits.exhibitions || 0,
        percentage: limits.exhibitions === -1 ? 0 : ((usage.exhibitions || 0) / limits.exhibitions) * 100
      },
      events: {
        current: usage.events || 0,
        limit: limits.events || 0,
        percentage: limits.events === -1 ? 0 : ((usage.events || 0) / limits.events) * 100
      },
      users: {
        current: usage.users || 0,
        limit: limits.users || 0,
        percentage: limits.users === -1 ? 0 : ((usage.users || 0) / limits.users) * 100
      },
      tasks: {
        current: usage.tasks || 0,
        limit: limits.tasks || 0,
        percentage: limits.tasks === -1 ? 0 : ((usage.tasks || 0) / limits.tasks) * 100
      },
      leads: {
        current: usage.leads || 0,
        limit: limits.leads || 0,
        percentage: limits.leads === -1 ? 0 : ((usage.leads || 0) / limits.leads) * 100
      },
      vendors: {
        current: usage.vendors || 0,
        limit: limits.vendors || 0,
        percentage: limits.vendors === -1 ? 0 : ((usage.vendors || 0) / limits.vendors) * 100
      },
      storage: {
        current: usage.storageUsedGB || 0,
        limit: limits.storageGB || 0,
        percentage: limits.storageGB === -1 ? 0 : ((usage.storageUsedGB || 0) / limits.storageGB) * 100
      }
    };

    return status;
  }, [tenant, getCurrentUsage, getUsageLimits]);

  /**
   * Check if any usage limits are approaching or exceeded
   */
  const getUsageAlerts = useCallback(async () => {
    const status = await getUsageStatus();
    if (!status) return [];

    const alerts = [];

    Object.entries(status).forEach(([key, data]) => {
      if (data.limit === -1) return; // Skip unlimited

      if (data.percentage >= 100) {
        alerts.push({
          type: 'limit_exceeded',
          severity: 'critical',
          resource: key,
          message: `${getLimitDisplayName(key as any)} limit exceeded (${data.current}/${data.limit})`,
          percentage: data.percentage
        });
      } else if (data.percentage >= 80) {
        alerts.push({
          type: 'approaching_limit',
          severity: 'warning',
          resource: key,
          message: `Approaching ${getLimitDisplayName(key as any)} limit (${data.percentage.toFixed(0)}% used)`,
          percentage: data.percentage
        });
      }
    });

    return alerts.sort((a, b) => b.percentage - a.percentage);
  }, [getUsageStatus]);

  return {
    checkUsageLimit,
    enforceUsageLimit,
    trackResourceUsage,
    getUsageStatus,
    getUsageAlerts,
    isChecking
  };
}

// Helper functions
function getLimitDisplayName(limitType: string): string {
  const displayNames: Record<string, string> = {
    maxExhibitions: 'Exhibitions',
    maxEvents: 'Events',
    maxUsers: 'Users',
    maxTasks: 'Tasks',
    maxLeads: 'Leads',
    maxVendors: 'Vendors',
    storageGB: 'Storage',
    apiCallsPerMonth: 'API Calls',
    emailsPerMonth: 'Emails'
  };

  return displayNames[limitType] || limitType;
}

function getEventTypeFromLimit(limitType: keyof SubscriptionPlan['features']): string | null {
  const eventTypeMap: Record<string, string> = {
    maxExhibitions: 'exhibition_created',
    maxEvents: 'event_created',
    maxUsers: 'user_added',
    maxTasks: 'task_created',
    maxLeads: 'lead_created',
    maxVendors: 'vendor_added',
    storageGB: 'file_uploaded',
    apiCallsPerMonth: 'api_call',
    emailsPerMonth: 'email_sent'
  };

  return eventTypeMap[limitType as string] || null;
}

// Usage examples:
// const { enforceUsageLimit, checkUsageLimit } = useUsageEnforcement();
//
// // Check before creating an exhibition
// const result = await checkUsageLimit('maxExhibitions');
// if (!result.allowed) {
//   showUpgradePrompt();
//   return;
// }
//
// // Enforce limit and track usage
// const { success } = await enforceUsageLimit(
//   'maxExhibitions',
//   () => createExhibition(data),
//   { trackUsage: true, resourceId: exhibitionId }
// );
