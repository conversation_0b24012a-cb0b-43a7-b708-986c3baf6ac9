"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';

export interface BreadcrumbSettings {
  showContextualActions: boolean;
  breadcrumbStyle: 'default' | 'compact' | 'pill' | 'minimal' | 'elevated';
  maxItems: number;
  enableSharing: boolean;
  showIcons: boolean;
  enableCollapsing: boolean;
  showKeyboardShortcuts: boolean;
  enableQuickActions: boolean;
  showPageHistory: boolean;
  enableNotifications: boolean;
  maxActionsPerItem: number;
}

const DEFAULT_SETTINGS: BreadcrumbSettings = {
  showContextualActions: true,
  breadcrumbStyle: 'elevated',
  maxItems: 4,
  enableSharing: false, // Default to private for business use
  showIcons: true,
  enableCollapsing: true,
  showKeyboardShortcuts: true,
  enableQuickActions: true,
  showPageHistory: true,
  enableNotifications: false, // Default off to avoid spam
  maxActionsPerItem: 8
};

export function useBreadcrumbSettings() {
  const { user } = useAuth();
  const [settings, setSettings] = useState<BreadcrumbSettings>(DEFAULT_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadSettings = async () => {
      if (!user?.uid) {
        setSettings(DEFAULT_SETTINGS);
        setIsLoading(false);
        return;
      }

      try {
        // TODO: Load from Firebase user preferences
        // For now, use localStorage as fallback
        const savedSettings = localStorage.getItem(`breadcrumb-settings-${user.uid}`);
        if (savedSettings) {
          const parsed = JSON.parse(savedSettings);
          setSettings({ ...DEFAULT_SETTINGS, ...parsed });
        } else {
          setSettings(DEFAULT_SETTINGS);
        }
      } catch (error) {
        console.error('Failed to load breadcrumb settings:', error);
        setSettings(DEFAULT_SETTINGS);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, [user?.uid]);

  const updateSettings = async (newSettings: Partial<BreadcrumbSettings>) => {
    if (!user?.uid) return;

    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);

    try {
      // TODO: Save to Firebase user preferences
      // For now, use localStorage as fallback
      localStorage.setItem(`breadcrumb-settings-${user.uid}`, JSON.stringify(updatedSettings));
    } catch (error) {
      console.error('Failed to save breadcrumb settings:', error);
    }
  };

  return {
    settings,
    updateSettings,
    isLoading
  };
}
