/**
 * System Email Hooks
 * React hooks for sending system emails with EVEXA templates
 */

import { useState, useCallback, useMemo } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { 
  SystemEmailService,
  createSystemEmailService,
  EmailSendRequest,
  EmailSendResult
} from '@/services/systemEmailService';

// ===== TYPES =====

interface UseSystemEmailReturn {
  service: SystemEmailService | null;
  isLoading: boolean;
  error: string | null;
  sendEmail: (request: Omit<EmailSendRequest, 'tenantId'>) => Promise<EmailSendResult>;
  sendUserInvitation: (
    recipientEmail: string,
    recipientName: string,
    senderName: string,
    roleName: string,
    invitationUrl: string,
    expirationDate: string
  ) => Promise<EmailSendResult>;
  sendWelcomeEmail: (
    recipientEmail: string,
    recipientName: string,
    loginUrl: string
  ) => Promise<EmailSendResult>;
  sendPasswordReset: (
    recipientEmail: string,
    recipientName: string,
    resetUrl: string
  ) => Promise<EmailSendResult>;
  sendAccountSuspension: (
    recipientEmail: string,
    recipientName: string,
    suspensionReason: string
  ) => Promise<EmailSendResult>;
  getAvailableTemplates: () => any[];
  clearError: () => void;
}

interface UseEmailTemplatesReturn {
  templates: any[];
  isLoading: boolean;
  error: string | null;
  getTemplate: (templateId: string) => any | null;
  getTemplatesByCategory: (category: string) => any[];
  getTemplatesByType: (type: string) => any[];
}

interface UseEmailSendingReturn {
  sendEmail: (request: Omit<EmailSendRequest, 'tenantId'>) => Promise<EmailSendResult>;
  isSending: boolean;
  lastResult: EmailSendResult | null;
  error: string | null;
  clearError: () => void;
}

// ===== MAIN SYSTEM EMAIL HOOK =====

export function useSystemEmail(): UseSystemEmailReturn {
  const { tenantId } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create service instance
  const service = useMemo(() => {
    if (!tenantId) return null;
    
    try {
      return createSystemEmailService(tenantId);
    } catch (err) {
      console.error('Error creating system email service:', err);
      setError(err instanceof Error ? err.message : 'Failed to initialize email service');
      return null;
    }
  }, [tenantId]);

  // Send email function
  const sendEmail = useCallback(async (
    request: Omit<EmailSendRequest, 'tenantId'>
  ): Promise<EmailSendResult> => {
    if (!service || !tenantId) {
      const result = {
        success: false,
        error: 'Email service not available'
      };
      setError(result.error);
      return result;
    }

    try {
      setIsLoading(true);
      setError(null);

      const result = await service.sendEmail({
        ...request,
        tenantId
      });

      if (!result.success) {
        setError(result.error || 'Failed to send email');
      }

      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  }, [service, tenantId]);

  // Convenience methods
  const sendUserInvitation = useCallback(async (
    recipientEmail: string,
    recipientName: string,
    senderName: string,
    roleName: string,
    invitationUrl: string,
    expirationDate: string
  ): Promise<EmailSendResult> => {
    if (!service) {
      const result = {
        success: false,
        error: 'Email service not available'
      };
      setError(result.error);
      return result;
    }

    try {
      setIsLoading(true);
      setError(null);

      const result = await service.sendUserInvitation(
        recipientEmail,
        recipientName,
        senderName,
        roleName,
        invitationUrl,
        expirationDate
      );

      if (!result.success) {
        setError(result.error || 'Failed to send invitation');
      }

      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  }, [service]);

  const sendWelcomeEmail = useCallback(async (
    recipientEmail: string,
    recipientName: string,
    loginUrl: string
  ): Promise<EmailSendResult> => {
    if (!service) {
      const result = {
        success: false,
        error: 'Email service not available'
      };
      setError(result.error);
      return result;
    }

    try {
      setIsLoading(true);
      setError(null);

      const result = await service.sendWelcomeEmail(
        recipientEmail,
        recipientName,
        loginUrl
      );

      if (!result.success) {
        setError(result.error || 'Failed to send welcome email');
      }

      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  }, [service]);

  const sendPasswordReset = useCallback(async (
    recipientEmail: string,
    recipientName: string,
    resetUrl: string
  ): Promise<EmailSendResult> => {
    if (!service) {
      const result = {
        success: false,
        error: 'Email service not available'
      };
      setError(result.error);
      return result;
    }

    try {
      setIsLoading(true);
      setError(null);

      const result = await service.sendPasswordReset(
        recipientEmail,
        recipientName,
        resetUrl
      );

      if (!result.success) {
        setError(result.error || 'Failed to send password reset');
      }

      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  }, [service]);

  const sendAccountSuspension = useCallback(async (
    recipientEmail: string,
    recipientName: string,
    suspensionReason: string
  ): Promise<EmailSendResult> => {
    if (!service) {
      const result = {
        success: false,
        error: 'Email service not available'
      };
      setError(result.error);
      return result;
    }

    try {
      setIsLoading(true);
      setError(null);

      const result = await service.sendAccountSuspension(
        recipientEmail,
        recipientName,
        suspensionReason
      );

      if (!result.success) {
        setError(result.error || 'Failed to send suspension notification');
      }

      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  }, [service]);

  const getAvailableTemplates = useCallback(() => {
    if (!service) return [];
    return service.getAvailableTemplates();
  }, [service]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    service,
    isLoading,
    error,
    sendEmail,
    sendUserInvitation,
    sendWelcomeEmail,
    sendPasswordReset,
    sendAccountSuspension,
    getAvailableTemplates,
    clearError
  };
}

// ===== EMAIL TEMPLATES HOOK =====

export function useEmailTemplates(): UseEmailTemplatesReturn {
  const { getAvailableTemplates } = useSystemEmail();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const templates = useMemo(() => {
    try {
      return getAvailableTemplates();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load templates');
      return [];
    }
  }, [getAvailableTemplates]);

  const getTemplate = useCallback((templateId: string) => {
    return templates.find(template => template.id === templateId) || null;
  }, [templates]);

  const getTemplatesByCategory = useCallback((category: string) => {
    return templates.filter(template => template.category === category);
  }, [templates]);

  const getTemplatesByType = useCallback((type: string) => {
    return templates.filter(template => template.type === type);
  }, [templates]);

  return {
    templates,
    isLoading,
    error,
    getTemplate,
    getTemplatesByCategory,
    getTemplatesByType
  };
}

// ===== EMAIL SENDING HOOK =====

export function useEmailSending(): UseEmailSendingReturn {
  const { sendEmail: sendEmailService } = useSystemEmail();
  const [isSending, setIsSending] = useState(false);
  const [lastResult, setLastResult] = useState<EmailSendResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const sendEmail = useCallback(async (
    request: Omit<EmailSendRequest, 'tenantId'>
  ): Promise<EmailSendResult> => {
    try {
      setIsSending(true);
      setError(null);

      const result = await sendEmailService(request);
      setLastResult(result);

      if (!result.success) {
        setError(result.error || 'Failed to send email');
      }

      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      const result = {
        success: false,
        error: errorMessage
      };
      setLastResult(result);
      return result;
    } finally {
      setIsSending(false);
    }
  }, [sendEmailService]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    sendEmail,
    isSending,
    lastResult,
    error,
    clearError
  };
}
