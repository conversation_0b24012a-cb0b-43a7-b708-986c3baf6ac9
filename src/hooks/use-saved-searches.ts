"use client";

import { useState, useEffect, useCallback } from 'react';
import { SavedSearch } from '@/components/ui/advanced-search';

const STORAGE_KEY = 'evexa-saved-searches';

export function useSavedSearches() {
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load saved searches from localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Convert date strings back to Date objects
        const searches = parsed.map((search: any) => ({
          ...search,
          createdAt: new Date(search.createdAt)
        }));
        setSavedSearches(searches);
      }
    } catch (error) {
      console.error('Failed to load saved searches:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save to localStorage whenever savedSearches changes
  const saveToStorage = useCallback((searches: SavedSearch[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(searches));
    } catch (error) {
      console.error('Failed to save searches to localStorage:', error);
    }
  }, []);

  // Add a new saved search
  const addSavedSearch = useCallback((search: Omit<SavedSearch, 'id' | 'createdAt'>) => {
    const newSearch: SavedSearch = {
      ...search,
      id: `search-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date()
    };

    setSavedSearches(prev => {
      const updated = [...prev, newSearch];
      saveToStorage(updated);
      return updated;
    });

    return newSearch;
  }, [saveToStorage]);

  // Update an existing saved search
  const updateSavedSearch = useCallback((id: string, updates: Partial<SavedSearch>) => {
    setSavedSearches(prev => {
      const updated = prev.map(search => 
        search.id === id ? { ...search, ...updates } : search
      );
      saveToStorage(updated);
      return updated;
    });
  }, [saveToStorage]);

  // Delete a saved search
  const deleteSavedSearch = useCallback((id: string) => {
    setSavedSearches(prev => {
      const updated = prev.filter(search => search.id !== id);
      saveToStorage(updated);
      return updated;
    });
  }, [saveToStorage]);

  // Toggle favorite status
  const toggleFavorite = useCallback((id: string) => {
    setSavedSearches(prev => {
      const updated = prev.map(search => 
        search.id === id ? { ...search, favorite: !search.favorite } : search
      );
      saveToStorage(updated);
      return updated;
    });
  }, [saveToStorage]);

  // Get favorite searches
  const favoriteSearches = savedSearches.filter(search => search.favorite);

  // Get recent searches (last 10)
  const recentSearches = [...savedSearches]
    .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
    .slice(0, 10);

  // Clear all saved searches
  const clearAllSearches = useCallback(() => {
    setSavedSearches([]);
    localStorage.removeItem(STORAGE_KEY);
  }, []);

  // Export searches as JSON
  const exportSearches = useCallback(() => {
    const dataStr = JSON.stringify(savedSearches, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `evexa-saved-searches-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }, [savedSearches]);

  // Import searches from JSON
  const importSearches = useCallback((file: File) => {
    return new Promise<void>((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string;
          const imported = JSON.parse(content);
          
          if (!Array.isArray(imported)) {
            throw new Error('Invalid file format');
          }

          // Validate and convert imported searches
          const validSearches = imported
            .filter((search: any) => 
              search.id && search.name && search.query !== undefined && search.filters
            )
            .map((search: any) => ({
              ...search,
              id: `imported-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`, // Generate new IDs
              createdAt: new Date(search.createdAt || new Date())
            }));

          setSavedSearches(prev => {
            const updated = [...prev, ...validSearches];
            saveToStorage(updated);
            return updated;
          });

          resolve();
        } catch (error) {
          reject(new Error('Failed to parse imported file'));
        }
      };

      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }, [saveToStorage]);

  // Search within saved searches
  const searchSavedSearches = useCallback((query: string) => {
    if (!query.trim()) return savedSearches;
    
    const lowerQuery = query.toLowerCase();
    return savedSearches.filter(search => 
      search.name.toLowerCase().includes(lowerQuery) ||
      search.query.toLowerCase().includes(lowerQuery)
    );
  }, [savedSearches]);

  return {
    savedSearches,
    favoriteSearches,
    recentSearches,
    isLoading,
    addSavedSearch,
    updateSavedSearch,
    deleteSavedSearch,
    toggleFavorite,
    clearAllSearches,
    exportSearches,
    importSearches,
    searchSavedSearches
  };
}
