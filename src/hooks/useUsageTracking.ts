/**
 * Usage Tracking Hook for EVEXA
 * Provides easy-to-use methods for tracking usage throughout the application
 */

import { useCallback } from 'react';
import { useAuth } from './useAuth';
import { useTenant } from './useTenant';
import { auth } from '@/lib/firebase';
import { UsageMetric } from '@/services/advancedUsageAnalyticsService';

interface UseUsageTrackingReturn {
  trackUsage: (
    category: UsageMetric['category'],
    action: string,
    quantity?: number,
    options?: {
      subcategory?: string;
      unit?: string;
      cost?: number;
      metadata?: Record<string, any>;
      tags?: string[];
    }
  ) => Promise<void>;
  
  trackExhibitionUsage: (action: string, exhibitionId?: string, quantity?: number) => Promise<void>;
  trackEventUsage: (action: string, eventId?: string, quantity?: number) => Promise<void>;
  trackUserUsage: (action: string, targetUserId?: string, quantity?: number) => Promise<void>;
  trackTaskUsage: (action: string, taskId?: string, quantity?: number) => Promise<void>;
  trackLeadUsage: (action: string, leadId?: string, quantity?: number) => Promise<void>;
  trackVendorUsage: (action: string, vendorId?: string, quantity?: number) => Promise<void>;
  trackStorageUsage: (action: string, sizeInMB: number, fileType?: string) => Promise<void>;
  trackApiUsage: (endpoint: string, method?: string) => Promise<void>;
  trackEmailUsage: (action: string, recipientCount?: number, templateId?: string) => Promise<void>;
  trackAiUsage: (feature: string, tokensUsed?: number, model?: string) => Promise<void>;
  
  bulkTrackUsage: (events: Array<{
    category: UsageMetric['category'];
    action: string;
    quantity?: number;
    options?: any;
  }>) => Promise<void>;
}

export function useUsageTracking(): UseUsageTrackingReturn {
  const { user } = useAuth();
  const { tenantId } = useTenant();

  const trackUsage = useCallback(async (
    category: UsageMetric['category'],
    action: string,
    quantity: number = 1,
    options: {
      subcategory?: string;
      unit?: string;
      cost?: number;
      metadata?: Record<string, any>;
      tags?: string[];
    } = {}
  ) => {
    if (!user || !tenantId) {
      console.warn('Cannot track usage: user or tenantId not available');
      return;
    }

    try {
      const firebaseUser = auth.currentUser;
      if (!firebaseUser) {
        throw new Error('User not authenticated');
      }
      const token = await firebaseUser.getIdToken();
      
      await fetch('/api/usage-analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          action: 'track-usage',
          tenantId,
          category,
          actionName: action,
          quantity,
          options: {
            ...options,
            metadata: {
              userAgent: navigator.userAgent,
              timestamp: new Date().toISOString(),
              ...options.metadata
            }
          }
        })
      });
    } catch (error) {
      console.error('Error tracking usage:', error);
      // Don't throw error to avoid breaking user experience
    }
  }, [user, tenantId]);

  const trackExhibitionUsage = useCallback(async (
    action: string,
    exhibitionId?: string,
    quantity: number = 1
  ) => {
    await trackUsage('exhibitions', action, quantity, {
      metadata: {
        resourceId: exhibitionId,
        resourceType: 'exhibition'
      }
    });
  }, [trackUsage]);

  const trackEventUsage = useCallback(async (
    action: string,
    eventId?: string,
    quantity: number = 1
  ) => {
    await trackUsage('events', action, quantity, {
      metadata: {
        resourceId: eventId,
        resourceType: 'event'
      }
    });
  }, [trackUsage]);

  const trackUserUsage = useCallback(async (
    action: string,
    targetUserId?: string,
    quantity: number = 1
  ) => {
    await trackUsage('users', action, quantity, {
      metadata: {
        resourceId: targetUserId,
        resourceType: 'user'
      }
    });
  }, [trackUsage]);

  const trackTaskUsage = useCallback(async (
    action: string,
    taskId?: string,
    quantity: number = 1
  ) => {
    await trackUsage('tasks', action, quantity, {
      metadata: {
        resourceId: taskId,
        resourceType: 'task'
      }
    });
  }, [trackUsage]);

  const trackLeadUsage = useCallback(async (
    action: string,
    leadId?: string,
    quantity: number = 1
  ) => {
    await trackUsage('leads', action, quantity, {
      metadata: {
        resourceId: leadId,
        resourceType: 'lead'
      }
    });
  }, [trackUsage]);

  const trackVendorUsage = useCallback(async (
    action: string,
    vendorId?: string,
    quantity: number = 1
  ) => {
    await trackUsage('vendors', action, quantity, {
      metadata: {
        resourceId: vendorId,
        resourceType: 'vendor'
      }
    });
  }, [trackUsage]);

  const trackStorageUsage = useCallback(async (
    action: string,
    sizeInMB: number,
    fileType?: string
  ) => {
    await trackUsage('storage', action, sizeInMB, {
      unit: 'MB',
      metadata: {
        fileType,
        resourceType: 'file'
      }
    });
  }, [trackUsage]);

  const trackApiUsage = useCallback(async (
    endpoint: string,
    method: string = 'GET'
  ) => {
    await trackUsage('api_calls', `${method} ${endpoint}`, 1, {
      metadata: {
        endpoint,
        method,
        resourceType: 'api_call'
      }
    });
  }, [trackUsage]);

  const trackEmailUsage = useCallback(async (
    action: string,
    recipientCount: number = 1,
    templateId?: string
  ) => {
    await trackUsage('emails', action, recipientCount, {
      metadata: {
        templateId,
        recipientCount,
        resourceType: 'email'
      }
    });
  }, [trackUsage]);

  const trackAiUsage = useCallback(async (
    feature: string,
    tokensUsed: number = 1,
    model?: string
  ) => {
    await trackUsage('ai_requests', feature, tokensUsed, {
      unit: 'tokens',
      metadata: {
        model,
        feature,
        resourceType: 'ai_request'
      }
    });
  }, [trackUsage]);

  const bulkTrackUsage = useCallback(async (events: Array<{
    category: UsageMetric['category'];
    action: string;
    quantity?: number;
    options?: any;
  }>) => {
    if (!user || !tenantId) {
      console.warn('Cannot track usage: user or tenantId not available');
      return;
    }

    try {
      const firebaseUser = auth.currentUser;
      if (!firebaseUser) {
        throw new Error('User not authenticated');
      }
      const token = await firebaseUser.getIdToken();
      
      const usageEvents = events.map(event => ({
        category: event.category,
        action: event.action,
        quantity: event.quantity || 1,
        options: {
          ...event.options,
          metadata: {
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString(),
            ...event.options?.metadata
          }
        }
      }));

      await fetch('/api/usage-analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          action: 'bulk-track-usage',
          tenantId,
          usageEvents
        })
      });
    } catch (error) {
      console.error('Error bulk tracking usage:', error);
      // Don't throw error to avoid breaking user experience
    }
  }, [user, tenantId]);

  return {
    trackUsage,
    trackExhibitionUsage,
    trackEventUsage,
    trackUserUsage,
    trackTaskUsage,
    trackLeadUsage,
    trackVendorUsage,
    trackStorageUsage,
    trackApiUsage,
    trackEmailUsage,
    trackAiUsage,
    bulkTrackUsage
  };
}
