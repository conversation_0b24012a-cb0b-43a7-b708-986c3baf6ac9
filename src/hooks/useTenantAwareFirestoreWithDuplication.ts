/**
 * Tenant-Aware Firestore Hook with Data Duplication
 * Extends the basic tenant-aware Firestore operations with automatic data duplication sync
 */

import { useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import type { WhereFilterOp } from 'firebase/firestore';
import type { TenantAwareEntity } from '@/types/firestore';
import {
  validateTenantId,
  getTenantDocuments,
  getTenantDocumentsWithFilters,
  getTenantDocumentById,
  addTenantDocument,
  updateTenantDocument,
  deleteTenantDocument,
  countTenantDocuments,
  TenantAwareBatch
} from '@/services/tenantIdHelperService';
import {
  syncDocumentDuplication,
  createDataDuplicationService,
  DUPLICATION_RULES,
  type DuplicationSyncResult
} from '@/services/dataDuplicationService';

/**
 * Enhanced hook for tenant-aware Firestore operations with data duplication
 */
export function useTenantAwareFirestoreWithDuplication() {
  const { tenantId } = useAuth();

  // Validate tenant context is available
  const ensureTenantId = useCallback((): string => {
    if (!tenantId) {
      throw new Error('Tenant context is required but not available. User must be authenticated with a valid tenant.');
    }
    return tenantId;
  }, [tenantId]);

  // ===== READ OPERATIONS (unchanged from base hook) =====

  const getDocuments = useCallback(async <T extends TenantAwareEntity>(
    collectionName: string,
    sortField?: string,
    sortDirection: 'asc' | 'desc' = 'desc',
    limitCount?: number
  ): Promise<T[]> => {
    const currentTenantId = ensureTenantId();
    return getTenantDocuments<T>(currentTenantId, collectionName, sortField, sortDirection, limitCount);
  }, [ensureTenantId]);

  const getDocumentsWithFilters = useCallback(async <T extends TenantAwareEntity>(
    collectionName: string,
    filters: Array<{ field: string; operator: WhereFilterOp; value: any }>,
    sortField?: string,
    sortDirection: 'asc' | 'desc' = 'desc',
    limitCount?: number
  ): Promise<T[]> => {
    const currentTenantId = ensureTenantId();
    return getTenantDocumentsWithFilters<T>(
      currentTenantId,
      collectionName,
      filters,
      sortField,
      sortDirection,
      limitCount
    );
  }, [ensureTenantId]);

  const getDocumentById = useCallback(async <T extends TenantAwareEntity>(
    collectionName: string,
    documentId: string
  ): Promise<T | null> => {
    const currentTenantId = ensureTenantId();
    return getTenantDocumentById<T>(currentTenantId, collectionName, documentId);
  }, [ensureTenantId]);

  const countDocuments = useCallback(async (
    collectionName: string,
    filters: Array<{ field: string; operator: WhereFilterOp; value: any }> = []
  ): Promise<number> => {
    const currentTenantId = ensureTenantId();
    return countTenantDocuments(currentTenantId, collectionName, filters);
  }, [ensureTenantId]);

  // ===== WRITE OPERATIONS WITH DUPLICATION =====

  /**
   * Add a document with automatic tenantId stamping and duplication sync
   */
  const addDocument = useCallback(async <T extends TenantAwareEntity>(
    collectionName: string,
    data: Omit<T, 'id' | 'tenantId' | 'createdAt' | 'updatedAt'>
  ): Promise<{ document: T; duplicationResults: DuplicationSyncResult[] }> => {
    const currentTenantId = ensureTenantId();
    
    // Add the document first
    const document = await addTenantDocument<T>(currentTenantId, collectionName, data);
    
    // Sync duplicated data if this collection is a source for duplication
    const duplicationResults = await syncDocumentDuplication(
      currentTenantId,
      collectionName,
      document.id,
      document as Record<string, any>
    );
    
    return { document, duplicationResults };
  }, [ensureTenantId]);

  /**
   * Update a document with tenant validation and duplication sync
   */
  const updateDocument = useCallback(async <T extends TenantAwareEntity>(
    collectionName: string,
    documentId: string,
    updates: Partial<Omit<T, 'id' | 'tenantId' | 'createdAt'>>
  ): Promise<{ duplicationResults: DuplicationSyncResult[] }> => {
    const currentTenantId = ensureTenantId();
    
    // Update the document first
    await updateTenantDocument<T>(currentTenantId, collectionName, documentId, updates);
    
    // Sync duplicated data if any duplicated fields were updated
    const duplicationResults = await syncDocumentDuplication(
      currentTenantId,
      collectionName,
      documentId,
      updates as Record<string, any>
    );
    
    return { duplicationResults };
  }, [ensureTenantId]);

  /**
   * Delete a document with tenant validation and duplication cleanup
   */
  const deleteDocument = useCallback(async (
    collectionName: string,
    documentId: string
  ): Promise<{ duplicationResults: DuplicationSyncResult[] }> => {
    const currentTenantId = ensureTenantId();
    
    // Get the document data before deletion for duplication cleanup
    const documentData = await getTenantDocumentById(currentTenantId, collectionName, documentId);
    
    // Delete the document
    await deleteTenantDocument(currentTenantId, collectionName, documentId);
    
    // Handle duplication cleanup if needed
    const duplicationResults: DuplicationSyncResult[] = [];
    
    // If this was a source document for duplication, we might need to clean up
    // For now, we'll rely on Cloud Functions to handle cascade operations
    
    return { duplicationResults };
  }, [ensureTenantId]);

  // ===== BATCH OPERATIONS WITH DUPLICATION =====

  /**
   * Create a tenant-aware batch with duplication support
   */
  const createBatchWithDuplication = useCallback((): EnhancedTenantAwareBatch => {
    const currentTenantId = ensureTenantId();
    return new EnhancedTenantAwareBatch(currentTenantId);
  }, [ensureTenantId]);

  // ===== DUPLICATION MANAGEMENT =====

  /**
   * Initialize duplicated data for a specific rule
   */
  const initializeDuplicatedData = useCallback(async (
    ruleKey: string,
    batchSize: number = 100
  ): Promise<DuplicationSyncResult> => {
    const currentTenantId = ensureTenantId();
    const service = createDataDuplicationService(currentTenantId);
    return service.initializeDuplicatedData(ruleKey, batchSize);
  }, [ensureTenantId]);

  /**
   * Validate duplicated data consistency
   */
  const validateDuplicatedData = useCallback(async (ruleKey: string) => {
    const currentTenantId = ensureTenantId();
    const service = createDataDuplicationService(currentTenantId);
    return service.validateDuplicatedData(ruleKey);
  }, [ensureTenantId]);

  /**
   * Get duplication rules that apply to a collection
   */
  const getDuplicationRulesForCollection = useCallback((collectionName: string) => {
    return Object.entries(DUPLICATION_RULES)
      .filter(([_, rule]) => rule.sourceCollection === collectionName && rule.enabled)
      .map(([key, rule]) => ({ key, rule }));
  }, []);

  /**
   * Check if a collection has duplicated fields
   */
  const hasDuplicatedFields = useCallback((collectionName: string): boolean => {
    return Object.values(DUPLICATION_RULES).some(rule => 
      rule.sourceCollection === collectionName && rule.enabled
    );
  }, []);

  // ===== UTILITY FUNCTIONS =====

  const hasTenantContext = useCallback((): boolean => {
    return !!tenantId;
  }, [tenantId]);

  const getCurrentTenantId = useCallback((): string => {
    return ensureTenantId();
  }, [ensureTenantId]);

  const validateDocumentOwnership = useCallback(<T extends TenantAwareEntity>(
    document: T
  ): void => {
    const currentTenantId = ensureTenantId();
    if (document.tenantId !== currentTenantId) {
      throw new Error(`Document belongs to different tenant. Expected: ${currentTenantId}, Got: ${document.tenantId}`);
    }
  }, [ensureTenantId]);

  return {
    // Read operations
    getDocuments,
    getDocumentsWithFilters,
    getDocumentById,
    countDocuments,
    
    // Write operations with duplication
    addDocument,
    updateDocument,
    deleteDocument,
    
    // Batch operations
    createBatch: createBatchWithDuplication,
    
    // Duplication management
    initializeDuplicatedData,
    validateDuplicatedData,
    getDuplicationRulesForCollection,
    hasDuplicatedFields,
    
    // Utility functions
    hasTenantContext,
    getCurrentTenantId,
    validateDocumentOwnership,
    
    // Current tenant info
    tenantId
  };
}

/**
 * Enhanced Tenant-Aware Batch with duplication support
 */
export class EnhancedTenantAwareBatch extends TenantAwareBatch {
  private duplicationOperations: Array<{
    collectionName: string;
    documentId: string;
    operation: 'add' | 'update';
    data: Record<string, any>;
  }> = [];

  /**
   * Add document to batch with duplication tracking
   */
  add<T extends TenantAwareEntity>(
    collectionName: string,
    data: Omit<T, 'id' | 'tenantId' | 'createdAt' | 'updatedAt'>
  ) {
    const docRef = super.add(collectionName, data);
    
    // Track for duplication sync after commit
    this.duplicationOperations.push({
      collectionName,
      documentId: docRef.id,
      operation: 'add',
      data: data as Record<string, any>
    });
    
    return docRef;
  }

  /**
   * Update document in batch with duplication tracking
   */
  update<T extends TenantAwareEntity>(
    collectionName: string,
    documentId: string,
    updates: Partial<Omit<T, 'id' | 'tenantId' | 'createdAt'>>
  ): void {
    super.update(collectionName, documentId, updates);
    
    // Track for duplication sync after commit
    this.duplicationOperations.push({
      collectionName,
      documentId,
      operation: 'update',
      data: updates as Record<string, any>
    });
  }

  /**
   * Commit the batch and sync duplicated data
   */
  async commitWithDuplication(): Promise<{ duplicationResults: DuplicationSyncResult[] }> {
    // Commit the batch first
    await this.commit();
    
    // Then sync duplicated data
    const duplicationResults: DuplicationSyncResult[] = [];
    
    for (const operation of this.duplicationOperations) {
      try {
        const results = await syncDocumentDuplication(
          this.tenantId,
          operation.collectionName,
          operation.documentId,
          operation.data
        );
        duplicationResults.push(...results);
      } catch (error) {
        console.error(`Error syncing duplication for ${operation.collectionName}/${operation.documentId}:`, error);
      }
    }
    
    return { duplicationResults };
  }
}

/**
 * Hook for specific collection operations with duplication
 */
export function useTenantCollectionWithDuplication<T extends TenantAwareEntity>(collectionName: string) {
  const {
    getDocuments,
    getDocumentsWithFilters,
    getDocumentById,
    addDocument,
    updateDocument,
    deleteDocument,
    countDocuments,
    validateDocumentOwnership,
    getDuplicationRulesForCollection,
    hasDuplicatedFields,
    tenantId
  } = useTenantAwareFirestoreWithDuplication();

  return {
    // Read operations
    getAll: (sortField?: string, sortDirection: 'asc' | 'desc' = 'desc', limitCount?: number) =>
      getDocuments<T>(collectionName, sortField, sortDirection, limitCount),
    
    getWithFilters: (
      filters: Array<{ field: string; operator: WhereFilterOp; value: any }>,
      sortField?: string,
      sortDirection: 'asc' | 'desc' = 'desc',
      limitCount?: number
    ) => getDocumentsWithFilters<T>(collectionName, filters, sortField, sortDirection, limitCount),
    
    getById: (documentId: string) => getDocumentById<T>(collectionName, documentId),
    
    count: (filters: Array<{ field: string; operator: WhereFilterOp; value: any }> = []) =>
      countDocuments(collectionName, filters),
    
    // Write operations with duplication
    add: (data: Omit<T, 'id' | 'tenantId' | 'createdAt' | 'updatedAt'>) =>
      addDocument<T>(collectionName, data),
    
    update: (documentId: string, updates: Partial<Omit<T, 'id' | 'tenantId' | 'createdAt'>>) =>
      updateDocument<T>(collectionName, documentId, updates),
    
    delete: (documentId: string) => deleteDocument(collectionName, documentId),
    
    // Duplication info
    getDuplicationRules: () => getDuplicationRulesForCollection(collectionName),
    hasDuplicatedFields: () => hasDuplicatedFields(collectionName),
    
    // Utility
    validateOwnership: validateDocumentOwnership,
    collectionName,
    tenantId
  };
}
