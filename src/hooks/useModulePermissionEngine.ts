/**
 * Enhanced Module Permission Hooks
 * React hooks for the Module Permission Engine with caching and real-time updates
 */

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { 
  ModulePermissionEngine, 
  createModulePermissionEngine, 
  createPermissionContext,
  PermissionContext,
  BulkPermissionResult,
  PermissionCheckOptions
} from '@/services/modulePermissionEngine';
import { 
  EvexaModule, 
  PermissionAction, 
  TenantPersona, 
  ModuleAccess 
} from '@/types/personas';
import { createTenantPersonaService } from '@/services/tenantPersonaService';

// ===== TYPES =====

interface UseModulePermissionEngineReturn {
  engine: ModulePermissionEngine | null;
  context: PermissionContext | null;
  isLoading: boolean;
  error: string | null;
  checkPermission: (module: EvexaModule, action: PermissionAction, options?: PermissionCheckOptions) => Promise<boolean>;
  checkBulkPermissions: (permissions: Array<{ module: EvexaModule; action: PermissionAction }>) => Promise<BulkPermissionResult>;
  getUserModuleAccess: () => Promise<ModuleAccess[]>;
  invalidateCache: (pattern?: string) => void;
  refreshContext: () => Promise<void>;
}

interface UseModuleAccessEnhancedReturn {
  hasAccess: boolean;
  permissions: PermissionAction[];
  isLoading: boolean;
  error: string | null;
  canRead: boolean;
  canWrite: boolean;
  canDelete: boolean;
  canAdmin: boolean;
  moduleMetadata: any;
  checkAction: (action: PermissionAction) => boolean;
  refresh: () => Promise<void>;
}

interface UseBulkPermissionsReturn {
  results: BulkPermissionResult | null;
  isLoading: boolean;
  error: string | null;
  hasAllPermissions: boolean;
  deniedPermissions: Array<{ module: EvexaModule; action: PermissionAction }>;
  refresh: () => Promise<void>;
}

// ===== MAIN PERMISSION ENGINE HOOK =====

export function useModulePermissionEngine(): UseModulePermissionEngineReturn {
  const { user, tenantId } = useAuth();
  const [engine, setEngine] = useState<ModulePermissionEngine | null>(null);
  const [context, setContext] = useState<PermissionContext | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userPersona, setUserPersona] = useState<TenantPersona | null>(null);

  // Initialize engine and context
  useEffect(() => {
    if (!tenantId || !user || !user.uid) {
      console.log('🔍 Permission engine: Missing required data', { tenantId, user: !!user, uid: user?.uid });
      setEngine(null);
      setContext(null);
      setIsLoading(false);
      return;
    }

    const initializeEngine = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Create engine
        const permissionEngine = createModulePermissionEngine(tenantId);
        setEngine(permissionEngine);

        // Load user persona
        const personaService = createTenantPersonaService(tenantId);
        const persona = await personaService.getUserPersona(user.uid);
        setUserPersona(persona);

        // Create context
        const permissionContext = createPermissionContext(
          user.uid,
          tenantId,
          persona || undefined,
          'professional', // TODO: Get from user subscription
          [] // TODO: Get custom overrides
        );
        setContext(permissionContext);

      } catch (err) {
        console.error('Error initializing permission engine:', err);
        setError(err instanceof Error ? err.message : 'Failed to initialize permissions');
      } finally {
        setIsLoading(false);
      }
    };

    initializeEngine();
  }, [tenantId, user]);

  // Permission checking functions
  const checkPermission = useCallback(async (
    module: EvexaModule,
    action: PermissionAction,
    options: PermissionCheckOptions = {}
  ): Promise<boolean> => {
    if (!engine || !context) return false;
    
    try {
      return await engine.checkPermission(context, module, action, options);
    } catch (err) {
      console.error('Permission check error:', err);
      return false;
    }
  }, [engine, context]);

  const checkBulkPermissions = useCallback(async (
    permissions: Array<{ module: EvexaModule; action: PermissionAction }>
  ): Promise<BulkPermissionResult> => {
    if (!engine || !context) {
      return {
        userId: context?.userId || '',
        results: permissions.map(p => ({ ...p, hasPermission: false })),
        overallAccess: false
      };
    }

    try {
      return await engine.checkBulkPermissions(context, permissions);
    } catch (err) {
      console.error('Bulk permission check error:', err);
      return {
        userId: context.userId,
        results: permissions.map(p => ({ ...p, hasPermission: false })),
        overallAccess: false
      };
    }
  }, [engine, context]);

  const getUserModuleAccess = useCallback(async (): Promise<ModuleAccess[]> => {
    if (!engine || !context) return [];
    
    try {
      return await engine.getUserModuleAccess(context);
    } catch (err) {
      console.error('Module access error:', err);
      return [];
    }
  }, [engine, context]);

  const invalidateCache = useCallback((pattern?: string) => {
    if (engine) {
      engine.invalidateCache(pattern);
    }
  }, [engine]);

  const refreshContext = useCallback(async () => {
    if (!tenantId || !user) return;

    try {
      const personaService = createTenantPersonaService(tenantId);
      const persona = await personaService.getUserPersona(user.uid);
      setUserPersona(persona);

      const newContext = createPermissionContext(
        user.uid,
        tenantId,
        persona || undefined,
        'professional',
        []
      );
      setContext(newContext);
      
      // Invalidate cache when context changes
      invalidateCache();
    } catch (err) {
      console.error('Error refreshing context:', err);
      setError(err instanceof Error ? err.message : 'Failed to refresh permissions');
    }
  }, [tenantId, user, invalidateCache]);

  return {
    engine,
    context,
    isLoading,
    error,
    checkPermission,
    checkBulkPermissions,
    getUserModuleAccess,
    invalidateCache,
    refreshContext
  };
}

// ===== ENHANCED MODULE ACCESS HOOK =====

export function useModuleAccessEnhanced(
  module: EvexaModule,
  action: PermissionAction = 'read'
): UseModuleAccessEnhancedReturn {
  const { checkPermission, getUserModuleAccess, isLoading: engineLoading, error: engineError } = useModulePermissionEngine();
  const [hasAccess, setHasAccess] = useState(false);
  const [permissions, setPermissions] = useState<PermissionAction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const lastModule = useRef<EvexaModule | null>(null);
  const lastAction = useRef<PermissionAction | null>(null);

  // Load permissions for the module
  const loadPermissions = useCallback(async () => {
    if (engineLoading || !checkPermission || !getUserModuleAccess) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Get all module access to find permissions for this module
      const moduleAccess = await getUserModuleAccess();
      const moduleData = moduleAccess.find(m => m.module === module);
      
      if (moduleData) {
        setPermissions(moduleData.permissions);
        setHasAccess(moduleData.permissions.includes(action));
      } else {
        setPermissions([]);
        setHasAccess(false);
      }

    } catch (err) {
      console.error('Error loading module permissions:', err);
      setError(err instanceof Error ? err.message : 'Failed to load permissions');
      setHasAccess(false);
      setPermissions([]);
    } finally {
      setIsLoading(false);
    }
  }, [module, action, checkPermission, getUserModuleAccess, engineLoading]);

  // Load permissions when dependencies change
  useEffect(() => {
    if (lastModule.current !== module || lastAction.current !== action) {
      lastModule.current = module;
      lastAction.current = action;
      loadPermissions();
    }
  }, [module, action, loadPermissions]);

  // Helper functions
  const checkAction = useCallback((actionToCheck: PermissionAction): boolean => {
    return permissions.includes(actionToCheck);
  }, [permissions]);

  const refresh = useCallback(async () => {
    await loadPermissions();
  }, [loadPermissions]);

  // Import module metadata
  const moduleMetadata = useMemo(() => {
    // This would import MODULE_METADATA but avoiding circular imports
    return {
      id: module,
      name: module.charAt(0).toUpperCase() + module.slice(1),
      description: `${module} module`,
      category: 'core',
      icon: 'box',
      color: 'blue',
      isPremium: false,
      minimumPlan: 'basic'
    };
  }, [module]);

  return {
    hasAccess,
    permissions,
    isLoading: isLoading || engineLoading,
    error: error || engineError,
    canRead: permissions.includes('read'),
    canWrite: permissions.includes('write'),
    canDelete: permissions.includes('delete'),
    canAdmin: permissions.includes('admin'),
    moduleMetadata,
    checkAction,
    refresh
  };
}

// ===== BULK PERMISSIONS HOOK =====

export function useBulkPermissions(
  permissions: Array<{ module: EvexaModule; action: PermissionAction }>
): UseBulkPermissionsReturn {
  const { checkBulkPermissions, isLoading: engineLoading, error: engineError } = useModulePermissionEngine();
  const [results, setResults] = useState<BulkPermissionResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadPermissions = useCallback(async () => {
    if (engineLoading || !checkBulkPermissions || permissions.length === 0) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const result = await checkBulkPermissions(permissions);
      setResults(result);

    } catch (err) {
      console.error('Error checking bulk permissions:', err);
      setError(err instanceof Error ? err.message : 'Failed to check permissions');
      setResults(null);
    } finally {
      setIsLoading(false);
    }
  }, [permissions, checkBulkPermissions, engineLoading]);

  useEffect(() => {
    loadPermissions();
  }, [loadPermissions]);

  const hasAllPermissions = useMemo(() => {
    return results?.overallAccess || false;
  }, [results]);

  const deniedPermissions = useMemo(() => {
    return results?.results.filter(r => !r.hasPermission).map(r => ({ module: r.module, action: r.action })) || [];
  }, [results]);

  const refresh = useCallback(async () => {
    await loadPermissions();
  }, [loadPermissions]);

  return {
    results,
    isLoading: isLoading || engineLoading,
    error: error || engineError,
    hasAllPermissions,
    deniedPermissions,
    refresh
  };
}
