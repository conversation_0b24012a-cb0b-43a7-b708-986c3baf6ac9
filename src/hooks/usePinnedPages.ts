"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';

export interface PinnedPage {
  id: string;
  label: string;
  href: string;
  iconName?: string; // Store icon name instead of React element
  pinnedAt: string;
}

export function usePinnedPages() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [pinnedPages, setPinnedPages] = useState<PinnedPage[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadPinnedPages = async () => {
      if (!user?.uid) {
        setPinnedPages([]);
        setIsLoading(false);
        return;
      }

      try {
        // TODO: Load from Firebase user preferences
        // For now, use localStorage as fallback
        const savedPages = localStorage.getItem(`pinned-pages-${user.uid}`);
        if (savedPages) {
          const parsed = JSON.parse(savedPages);
          setPinnedPages(parsed);
        }
      } catch (error) {
        console.error('Failed to load pinned pages:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPinnedPages();
  }, [user?.uid]);

  const pinPage = async (page: Omit<PinnedPage, 'id' | 'pinnedAt'>) => {
    if (!user?.uid) return;

    const newPage: PinnedPage = {
      ...page,
      id: `${page.href}-${Date.now()}`,
      pinnedAt: new Date().toISOString()
    };

    // Check if already pinned
    const isAlreadyPinned = pinnedPages.some(p => p.href === page.href);

    if (isAlreadyPinned) {
      toast({
        title: "Already Pinned",
        description: `"${page.label}" is already in your quick access menu.`,
        variant: "default",
      });
      return;
    }

    // Limit to 10 pinned pages
    const updatedPages = [newPage, ...pinnedPages].slice(0, 10);
    setPinnedPages(updatedPages);

    try {
      // TODO: Save to Firebase user preferences
      // For now, use localStorage as fallback
      localStorage.setItem(`pinned-pages-${user.uid}`, JSON.stringify(updatedPages));

      toast({
        title: "Page Pinned",
        description: `"${page.label}" has been added to your quick access menu.`,
      });
    } catch (error) {
      console.error('Failed to save pinned page:', error);
      toast({
        title: "Error",
        description: "Failed to pin page. Please try again.",
        variant: "destructive",
      });
    }
  };

  const unpinPage = async (pageId: string) => {
    if (!user?.uid) return;

    const pageToRemove = pinnedPages.find(p => p.id === pageId);
    const updatedPages = pinnedPages.filter(p => p.id !== pageId);
    setPinnedPages(updatedPages);

    try {
      // TODO: Save to Firebase user preferences
      // For now, use localStorage as fallback
      localStorage.setItem(`pinned-pages-${user.uid}`, JSON.stringify(updatedPages));
      
      if (pageToRemove) {
        toast({
          title: "Page Unpinned",
          description: `"${pageToRemove.label}" has been removed from your quick access menu.`,
        });
      }
    } catch (error) {
      console.error('Failed to remove pinned page:', error);
      toast({
        title: "Error",
        description: "Failed to unpin page. Please try again.",
        variant: "destructive",
      });
    }
  };

  const isPinned = (href: string) => {
    return pinnedPages.some(p => p.href === href);
  };

  const clearAllPinned = async () => {
    if (!user?.uid) return;

    setPinnedPages([]);

    try {
      localStorage.removeItem(`pinned-pages-${user.uid}`);
      toast({
        title: "All Pages Unpinned",
        description: "Your quick access menu has been cleared.",
      });
    } catch (error) {
      console.error('Failed to clear pinned pages:', error);
    }
  };

  return {
    pinnedPages,
    pinPage,
    unpinPage,
    isPinned,
    clearAllPinned,
    isLoading
  };
}
