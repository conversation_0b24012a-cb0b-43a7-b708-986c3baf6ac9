"use client";

import { useMemo } from 'react';
import { useAuth } from '@/contexts/auth-context';
import type { PersonaId, ModuleId } from '@/types/user-personas';

// ===== PERSONA-BASED ACCESS CONTROL HOOK =====

export interface AccessControlResult {
  hasAccess: boolean;
  reason?: string;
  upgradeRequired?: boolean;
  requiredPersona?: PersonaId;
  requiredModule?: ModuleId;
}

export interface DashboardWidgetAccess {
  canView: boolean;
  canInteract: boolean;
  canCustomize: boolean;
  showUpgradePrompt: boolean;
  restrictionReason?: string;
}

// Widget access rules based on persona and modules
const WIDGET_ACCESS_RULES: Record<string, {
  requiredModules: ModuleId[];
  requiredPersonas?: PersonaId[];
  description: string;
}> = {
  // Management widgets
  project_overview: {
    requiredModules: ['exhibitions', 'tasks'],
    description: 'Project overview and management'
  },
  team_management: {
    requiredModules: ['access_control'],
    requiredPersonas: ['admin', 'exhibition_manager'],
    description: 'Team and user management'
  },
  
  // Financial widgets
  budget_summary: {
    requiredModules: ['financials'],
    description: 'Budget tracking and financial overview'
  },
  expense_tracking: {
    requiredModules: ['financials'],
    description: 'Expense management and tracking'
  },
  financial_analytics: {
    requiredModules: ['financials', 'analytics'],
    description: 'Advanced financial analytics'
  },
  
  // Marketing widgets
  lead_management: {
    requiredModules: ['leads'],
    description: 'Lead tracking and management'
  },
  social_media_hub: {
    requiredModules: ['social_media'],
    description: 'Social media management and analytics'
  },
  marketing_analytics: {
    requiredModules: ['marketing', 'analytics'],
    description: 'Marketing performance analytics'
  },
  
  // Operations widgets
  task_management: {
    requiredModules: ['tasks'],
    description: 'Task tracking and management'
  },
  vendor_management: {
    requiredModules: ['vendors'],
    description: 'Vendor relationships and contracts'
  },
  logistics_tracking: {
    requiredModules: ['logistics'],
    description: 'Logistics and shipping management'
  },
  
  // Analytics widgets
  performance_analytics: {
    requiredModules: ['analytics'],
    description: 'Performance insights and analytics'
  },
  ai_insights: {
    requiredModules: ['analytics'],
    requiredPersonas: ['admin', 'exhibition_manager'],
    description: 'AI-powered insights and recommendations'
  },
  
  // Communication widgets
  communications: {
    requiredModules: ['communications'],
    description: 'Communication management'
  },
  approvals: {
    requiredModules: ['approvals'],
    description: 'Approval workflows and management'
  }
};

export function usePersonaAccessControl() {
  const { user, tenant } = useAuth();

  // Get user's current persona and module access
  const userPersona = user?.persona as PersonaId;
  const userModules = user?.moduleAccess || [];
  const subscriptionTier = tenant?.subscriptionTier || 'basic';

  /**
   * Check if user has access to a specific module
   */
  const hasModuleAccess = useMemo(() => {
    return (moduleId: ModuleId): AccessControlResult => {
      if (!user || !userModules) {
        return {
          hasAccess: false,
          reason: 'User not authenticated or no module access defined'
        };
      }

      const hasAccess = userModules.includes(moduleId);
      
      if (!hasAccess) {
        return {
          hasAccess: false,
          reason: `Module '${moduleId}' not included in your current persona`,
          upgradeRequired: true,
          requiredModule: moduleId
        };
      }

      return { hasAccess: true };
    };
  }, [user, userModules]);

  /**
   * Check if user has access to a specific dashboard widget
   */
  const hasWidgetAccess = useMemo(() => {
    return (widgetId: string): DashboardWidgetAccess => {
      const widgetRule = WIDGET_ACCESS_RULES[widgetId];
      
      if (!widgetRule) {
        // If no rule defined, allow access (backward compatibility)
        return {
          canView: true,
          canInteract: true,
          canCustomize: true,
          showUpgradePrompt: false
        };
      }

      if (!user || !userPersona) {
        return {
          canView: false,
          canInteract: false,
          canCustomize: false,
          showUpgradePrompt: false,
          restrictionReason: 'User not authenticated'
        };
      }

      // Check persona requirements
      if (widgetRule.requiredPersonas && !widgetRule.requiredPersonas.includes(userPersona)) {
        return {
          canView: false,
          canInteract: false,
          canCustomize: false,
          showUpgradePrompt: true,
          restrictionReason: `Requires ${widgetRule.requiredPersonas.join(' or ')} persona`
        };
      }

      // Check module requirements
      const missingModules = widgetRule.requiredModules.filter(
        moduleId => !userModules.includes(moduleId)
      );

      if (missingModules.length > 0) {
        return {
          canView: true, // Allow viewing but with limited functionality
          canInteract: false,
          canCustomize: false,
          showUpgradePrompt: true,
          restrictionReason: `Requires access to: ${missingModules.join(', ')}`
        };
      }

      // Full access granted
      return {
        canView: true,
        canInteract: true,
        canCustomize: true,
        showUpgradePrompt: false
      };
    };
  }, [user, userPersona, userModules]);

  /**
   * Get filtered widgets based on user access
   */
  const getAccessibleWidgets = useMemo(() => {
    return (availableWidgets: string[]): Array<{
      widgetId: string;
      access: DashboardWidgetAccess;
    }> => {
      return availableWidgets.map(widgetId => ({
        widgetId,
        access: hasWidgetAccess(widgetId)
      }));
    };
  }, [hasWidgetAccess]);

  /**
   * Check if user can access analytics features
   */
  const canAccessAnalytics = useMemo(() => {
    return hasModuleAccess('analytics').hasAccess;
  }, [hasModuleAccess]);

  /**
   * Check if user can manage team/users
   */
  const canManageTeam = useMemo(() => {
    return hasModuleAccess('access_control').hasAccess && 
           (userPersona === 'admin' || userPersona === 'exhibition_manager');
  }, [hasModuleAccess, userPersona]);

  /**
   * Check if user can access financial data
   */
  const canAccessFinancials = useMemo(() => {
    return hasModuleAccess('financials').hasAccess;
  }, [hasModuleAccess]);

  /**
   * Get upgrade suggestions based on missing access
   */
  const getUpgradeSuggestions = useMemo(() => {
    return (requestedWidgets: string[]): Array<{
      widgetId: string;
      missingModules: ModuleId[];
      suggestedPersona?: PersonaId;
      description: string;
    }> => {
      return requestedWidgets
        .map(widgetId => {
          const rule = WIDGET_ACCESS_RULES[widgetId];
          if (!rule) return null;

          const missingModules = rule.requiredModules.filter(
            moduleId => !userModules.includes(moduleId)
          );

          const needsPersonaUpgrade = rule.requiredPersonas && 
            !rule.requiredPersonas.includes(userPersona);

          if (missingModules.length > 0 || needsPersonaUpgrade) {
            return {
              widgetId,
              missingModules,
              suggestedPersona: needsPersonaUpgrade ? rule.requiredPersonas?.[0] : undefined,
              description: rule.description
            };
          }

          return null;
        })
        .filter(Boolean) as Array<{
          widgetId: string;
          missingModules: ModuleId[];
          suggestedPersona?: PersonaId;
          description: string;
        }>;
    };
  }, [userModules, userPersona]);

  /**
   * Check subscription limits
   */
  const checkSubscriptionLimits = useMemo(() => {
    return {
      canAddUsers: () => {
        const currentUsers = tenant?.userCount || 0;
        const maxUsers = subscriptionTier === 'basic' ? 3 : 
                        subscriptionTier === 'professional' ? 10 : 
                        Infinity;
        
        return {
          canAdd: currentUsers < maxUsers,
          currentCount: currentUsers,
          maxCount: maxUsers,
          upgradeRequired: currentUsers >= maxUsers
        };
      },
      
      canAccessPremiumFeatures: () => {
        return subscriptionTier !== 'basic';
      }
    };
  }, [tenant, subscriptionTier]);

  return {
    // Core access checks
    hasModuleAccess,
    hasWidgetAccess,
    getAccessibleWidgets,
    
    // Convenience checks
    canAccessAnalytics,
    canManageTeam,
    canAccessFinancials,
    
    // Upgrade and suggestions
    getUpgradeSuggestions,
    checkSubscriptionLimits,
    
    // User context
    userPersona,
    userModules,
    subscriptionTier,
    
    // Widget access rules (for reference)
    widgetAccessRules: WIDGET_ACCESS_RULES
  };
}
