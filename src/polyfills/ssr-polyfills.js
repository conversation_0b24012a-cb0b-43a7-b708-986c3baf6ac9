/**
 * SSR Polyfills for browser globals
 * Fixes "self is not defined" and other SSR issues
 */

// Polyfill for 'self' in Node.js environment
if (typeof global !== 'undefined' && typeof global.self === 'undefined') {
  global.self = global;
}

// Polyfill for 'window' in Node.js environment
if (typeof global !== 'undefined' && typeof global.window === 'undefined') {
  global.window = {};
}

// Polyfill for 'document' in Node.js environment
if (typeof global !== 'undefined' && typeof global.document === 'undefined') {
  global.document = {};
}

// Polyfill for 'navigator' in Node.js environment
if (typeof global !== 'undefined' && typeof global.navigator === 'undefined') {
  global.navigator = {
    userAgent: 'Node.js'
  };
}

// Polyfill for 'location' in Node.js environment
if (typeof global !== 'undefined' && typeof global.location === 'undefined') {
  global.location = {
    href: '',
    origin: '',
    protocol: 'http:',
    host: 'localhost',
    hostname: 'localhost',
    port: '',
    pathname: '/',
    search: '',
    hash: ''
  };
}
