import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

export interface ExhibitionOrganizerPartner {
  id?: string;
  organizationName: string;
  contactInfo: {
    primaryContact: string;
    email: string;
    phone: string;
    website?: string;
    address: {
      street: string;
      city: string;
      state: string;
      country: string;
      zipCode: string;
    };
  };
  partnershipTier: 'platinum' | 'gold' | 'silver' | 'bronze';
  status: 'pending' | 'active' | 'suspended' | 'terminated';
  whiteLabelConfig: {
    enabled: boolean;
    brandName?: string;
    logo?: string;
    primaryColor?: string;
    secondaryColor?: string;
    customDomain?: string;
    customEmailDomain?: string;
  };
  revenueSharing: {
    model: 'percentage' | 'flat_fee' | 'tiered' | 'hybrid';
    percentage?: number;
    flatFee?: number;
    tieredRates?: Array<{
      minClients: number;
      maxClients?: number;
      rate: number;
    }>;
    paymentSchedule: 'monthly' | 'quarterly' | 'annually';
    minimumPayout: number;
  };
  apiAccess: {
    enabled: boolean;
    apiKey?: string;
    webhookUrl?: string;
    allowedEndpoints: string[];
    rateLimit: number;
  };
  clientLimits: {
    maxClients: number;
    currentClients: number;
    maxExhibitionsPerClient: number;
  };
  features: {
    whiteLabeling: boolean;
    customBranding: boolean;
    apiAccess: boolean;
    prioritySupport: boolean;
    customReporting: boolean;
    dedicatedAccount: boolean;
    trainingProgram: boolean;
    marketingSupport: boolean;
  };
  performance: {
    clientsReferred: number;
    totalRevenue: number;
    averageClientValue: number;
    retentionRate: number;
    satisfactionScore: number;
    lastReviewDate?: Date;
  };
  agreement: {
    startDate: Date;
    endDate?: Date;
    renewalDate?: Date;
    contractTerms: string;
    signedBy: string;
    signedDate: Date;
  };
  onboardingStatus: {
    currentStep: number;
    totalSteps: number;
    completedSteps: string[];
    nextAction: string;
    assignedTo: string;
  };
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    lastModifiedBy: string;
  };
}

export interface PartnershipApplication {
  id?: string;
  organizationName: string;
  contactInfo: {
    primaryContact: string;
    email: string;
    phone: string;
    website?: string;
  };
  businessInfo: {
    yearsInBusiness: number;
    numberOfEvents: number;
    averageEventSize: number;
    targetMarkets: string[];
    currentSoftware?: string;
  };
  partnershipInterest: {
    desiredTier: ExhibitionOrganizerPartner['partnershipTier'];
    expectedClients: number;
    revenueExpectation: number;
    whiteLabelInterest: boolean;
    apiIntegrationNeeds: boolean;
  };
  status: 'submitted' | 'under_review' | 'approved' | 'rejected';
  reviewNotes?: string;
  submittedAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
}

export interface RevenueShareCalculation {
  partnerId: string;
  period: {
    startDate: Date;
    endDate: Date;
  };
  clientRevenue: Array<{
    clientId: string;
    clientName: string;
    revenue: number;
    subscriptionTier: string;
  }>;
  totalRevenue: number;
  partnerShare: number;
  partnerPercentage: number;
  fees: {
    processingFee: number;
    platformFee: number;
    other: number;
  };
  netPayout: number;
  payoutStatus: 'pending' | 'processed' | 'paid' | 'failed';
  payoutDate?: Date;
}

class ExhibitionOrganizerPartnershipService {
  private partnersCollection = 'exhibition_organizer_partners';
  private applicationsCollection = 'partnership_applications';
  private revenueShareCollection = 'revenue_share_calculations';

  // Partnership Applications
  async submitApplication(application: Omit<PartnershipApplication, 'id' | 'submittedAt' | 'status'>): Promise<PartnershipApplication> {
    const now = new Date();
    const applicationData: Omit<PartnershipApplication, 'id'> = {
      ...application,
      status: 'submitted',
      submittedAt: now
    };

    const docRef = await addDoc(collection(db, this.applicationsCollection), {
      ...applicationData,
      submittedAt: Timestamp.fromDate(now)
    });

    return { ...applicationData, id: docRef.id };
  }

  async getApplications(): Promise<PartnershipApplication[]> {
    const q = query(
      collection(db, this.applicationsCollection),
      orderBy('submittedAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        submittedAt: data.submittedAt.toDate(),
        reviewedAt: data.reviewedAt?.toDate()
      } as PartnershipApplication;
    });
  }

  async reviewApplication(
    applicationId: string, 
    status: 'approved' | 'rejected', 
    reviewNotes: string,
    reviewedBy: string
  ): Promise<void> {
    const docRef = doc(db, this.applicationsCollection, applicationId);
    await updateDoc(docRef, {
      status,
      reviewNotes,
      reviewedBy,
      reviewedAt: Timestamp.fromDate(new Date())
    });
  }

  // Partner Management
  async createPartner(partner: Omit<ExhibitionOrganizerPartner, 'id' | 'metadata'>): Promise<ExhibitionOrganizerPartner> {
    const now = new Date();
    const partnerData: Omit<ExhibitionOrganizerPartner, 'id'> = {
      ...partner,
      metadata: {
        createdAt: now,
        updatedAt: now,
        createdBy: 'system', // TODO: Get from auth context
        lastModifiedBy: 'system'
      }
    };

    const docRef = await addDoc(collection(db, this.partnersCollection), {
      ...partnerData,
      'agreement.startDate': Timestamp.fromDate(partnerData.agreement.startDate),
      'agreement.endDate': partnerData.agreement.endDate ? Timestamp.fromDate(partnerData.agreement.endDate) : null,
      'agreement.renewalDate': partnerData.agreement.renewalDate ? Timestamp.fromDate(partnerData.agreement.renewalDate) : null,
      'agreement.signedDate': Timestamp.fromDate(partnerData.agreement.signedDate),
      'performance.lastReviewDate': partnerData.performance.lastReviewDate ? Timestamp.fromDate(partnerData.performance.lastReviewDate) : null,
      'metadata.createdAt': Timestamp.fromDate(now),
      'metadata.updatedAt': Timestamp.fromDate(now)
    });

    return { ...partnerData, id: docRef.id };
  }

  async getAllPartners(): Promise<ExhibitionOrganizerPartner[]> {
    const q = query(
      collection(db, this.partnersCollection),
      orderBy('metadata.createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        agreement: {
          ...data.agreement,
          startDate: data.agreement.startDate.toDate(),
          endDate: data.agreement.endDate?.toDate(),
          renewalDate: data.agreement.renewalDate?.toDate(),
          signedDate: data.agreement.signedDate.toDate()
        },
        performance: {
          ...data.performance,
          lastReviewDate: data.performance.lastReviewDate?.toDate()
        },
        metadata: {
          ...data.metadata,
          createdAt: data.metadata.createdAt.toDate(),
          updatedAt: data.metadata.updatedAt.toDate()
        }
      } as ExhibitionOrganizerPartner;
    });
  }

  async getPartner(id: string): Promise<ExhibitionOrganizerPartner | null> {
    const docRef = doc(db, this.partnersCollection, id);
    const docSnap = await getDoc(docRef);
    
    if (!docSnap.exists()) return null;
    
    const data = docSnap.data();
    return {
      id: docSnap.id,
      ...data,
      agreement: {
        ...data.agreement,
        startDate: data.agreement.startDate.toDate(),
        endDate: data.agreement.endDate?.toDate(),
        renewalDate: data.agreement.renewalDate?.toDate(),
        signedDate: data.agreement.signedDate.toDate()
      },
      performance: {
        ...data.performance,
        lastReviewDate: data.performance.lastReviewDate?.toDate()
      },
      metadata: {
        ...data.metadata,
        createdAt: data.metadata.createdAt.toDate(),
        updatedAt: data.metadata.updatedAt.toDate()
      }
    } as ExhibitionOrganizerPartner;
  }

  async updatePartner(id: string, updates: Partial<ExhibitionOrganizerPartner>): Promise<void> {
    const docRef = doc(db, this.partnersCollection, id);
    const updateData = {
      ...updates,
      'metadata.updatedAt': Timestamp.fromDate(new Date()),
      'metadata.lastModifiedBy': 'system' // TODO: Get from auth context
    };

    // Handle date conversions
    if (updates.agreement?.startDate) {
      updateData['agreement.startDate'] = Timestamp.fromDate(updates.agreement.startDate);
    }
    if (updates.agreement?.endDate) {
      updateData['agreement.endDate'] = Timestamp.fromDate(updates.agreement.endDate);
    }
    if (updates.agreement?.renewalDate) {
      updateData['agreement.renewalDate'] = Timestamp.fromDate(updates.agreement.renewalDate);
    }
    if (updates.agreement?.signedDate) {
      updateData['agreement.signedDate'] = Timestamp.fromDate(updates.agreement.signedDate);
    }
    if (updates.performance?.lastReviewDate) {
      updateData['performance.lastReviewDate'] = Timestamp.fromDate(updates.performance.lastReviewDate);
    }

    await updateDoc(docRef, updateData);
  }

  // Revenue Sharing
  async calculateRevenueShare(partnerId: string, startDate: Date, endDate: Date): Promise<RevenueShareCalculation> {
    const partner = await this.getPartner(partnerId);
    if (!partner) throw new Error('Partner not found');

    // Query actual client revenue data from Firebase
    // TODO: Implement real revenue tracking from tenant subscriptions
    const clientRevenue: Array<{
      clientId: string;
      clientName: string;
      revenue: number;
      subscriptionTier: string;
    }> = [];

    const totalRevenue = clientRevenue.reduce((sum, client) => sum + client.revenue, 0);
    
    let partnerShare = 0;
    const revenueModel = partner.revenueSharing;
    
    switch (revenueModel.model) {
      case 'percentage':
        partnerShare = totalRevenue * (revenueModel.percentage || 0) / 100;
        break;
      case 'flat_fee':
        partnerShare = revenueModel.flatFee || 0;
        break;
      case 'tiered':
        // Calculate based on tiered rates
        const clientCount = clientRevenue.length;
        const applicableRate = revenueModel.tieredRates?.find(rate => 
          clientCount >= rate.minClients && (!rate.maxClients || clientCount <= rate.maxClients)
        );
        partnerShare = totalRevenue * (applicableRate?.rate || 0) / 100;
        break;
    }

    const fees = {
      processingFee: partnerShare * 0.03, // 3% processing fee
      platformFee: partnerShare * 0.02,   // 2% platform fee
      other: 0
    };

    const totalFees = fees.processingFee + fees.platformFee + fees.other;
    const netPayout = Math.max(0, partnerShare - totalFees);

    const calculation: RevenueShareCalculation = {
      partnerId,
      period: { startDate, endDate },
      clientRevenue: clientRevenue,
      totalRevenue,
      partnerShare,
      partnerPercentage: (partnerShare / totalRevenue) * 100,
      fees,
      netPayout,
      payoutStatus: netPayout >= revenueModel.minimumPayout ? 'pending' : 'pending'
    };

    // Save calculation to database
    const docRef = await addDoc(collection(db, this.revenueShareCollection), {
      ...calculation,
      'period.startDate': Timestamp.fromDate(startDate),
      'period.endDate': Timestamp.fromDate(endDate)
    });

    return { ...calculation, id: docRef.id };
  }

  // Partner Portal Features
  async generateApiKey(partnerId: string): Promise<string> {
    const apiKey = `evexa_${partnerId}_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    
    await this.updatePartner(partnerId, {
      apiAccess: {
        enabled: true,
        apiKey,
        allowedEndpoints: ['GET /api/clients', 'POST /api/clients', 'GET /api/exhibitions'],
        rateLimit: 1000
      }
    });

    return apiKey;
  }

  async configureWhiteLabel(partnerId: string, config: ExhibitionOrganizerPartner['whiteLabelConfig']): Promise<void> {
    await this.updatePartner(partnerId, { whiteLabelConfig: config });
  }

  // Analytics and Reporting
  async getPartnershipStats(): Promise<{
    totalPartners: number;
    activePartners: number;
    totalRevenue: number;
    averagePartnerValue: number;
    topPerformers: ExhibitionOrganizerPartner[];
  }> {
    const partners = await this.getAllPartners();
    
    const activePartners = partners.filter(p => p.status === 'active');
    const totalRevenue = partners.reduce((sum, p) => sum + p.performance.totalRevenue, 0);
    const averagePartnerValue = partners.length > 0 ? totalRevenue / partners.length : 0;
    const topPerformers = partners
      .sort((a, b) => b.performance.totalRevenue - a.performance.totalRevenue)
      .slice(0, 5);

    return {
      totalPartners: partners.length,
      activePartners: activePartners.length,
      totalRevenue,
      averagePartnerValue,
      topPerformers
    };
  }
}

export const exhibitionOrganizerPartnershipService = new ExhibitionOrganizerPartnershipService();
export default exhibitionOrganizerPartnershipService;
