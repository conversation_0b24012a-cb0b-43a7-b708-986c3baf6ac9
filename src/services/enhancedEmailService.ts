/**
 * Enhanced Email Service
 * Production-ready email service with quota management, rate limiting, and analytics
 */

import { sendEmailWithSendGrid, SendGridEmailOptions } from '@/lib/firebase-email-service';
import { EmailConfigService } from './emailConfigService';
import { SystemEmailService } from './systemEmailService';
import { addDocument } from './firestoreService';
import { COLLECTIONS } from '@/lib/collections';
import { validateTenantId } from './tenantIdHelperService';

// ===== TYPES =====

export interface EnhancedEmailRequest {
  tenantId: string;
  to: string | string[];
  subject: string;
  html: string;
  text?: string;
  templateId?: string;
  variables?: Record<string, any>;
  priority?: 'low' | 'normal' | 'high';
  scheduledAt?: Date;
  trackOpens?: boolean;
  trackClicks?: boolean;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface EnhancedEmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
  quotaRemaining?: {
    daily: number;
    monthly: number;
  };
  deliveryStatus?: 'queued' | 'sent' | 'failed';
}

export interface EmailAnalytics {
  tenantId: string;
  messageId: string;
  recipientEmail: string;
  subject: string;
  templateId?: string;
  status: 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced' | 'failed';
  sentAt: Date;
  deliveredAt?: Date;
  openedAt?: Date;
  clickedAt?: Date;
  bouncedAt?: Date;
  failedAt?: Date;
  errorMessage?: string;
  userAgent?: string;
  ipAddress?: string;
  location?: {
    country?: string;
    region?: string;
    city?: string;
  };
  tags?: string[];
  metadata?: Record<string, any>;
}

// ===== ENHANCED EMAIL SERVICE =====

export class EnhancedEmailService {
  private configService: EmailConfigService;
  private systemEmailService: SystemEmailService;

  constructor(private tenantId: string) {
    validateTenantId(tenantId);
    this.configService = new EmailConfigService(tenantId);
    this.systemEmailService = new SystemEmailService(tenantId);
  }

  /**
   * Send email with full quota and configuration management
   */
  async sendEmail(request: EnhancedEmailRequest): Promise<EnhancedEmailResult> {
    try {
      // Validate tenant ID matches
      if (request.tenantId !== this.tenantId) {
        return {
          success: false,
          error: 'Tenant ID mismatch'
        };
      }

      // Check email quota
      const quotaCheck = await this.configService.canSendEmail();
      if (!quotaCheck.allowed) {
        return {
          success: false,
          error: quotaCheck.reason,
          deliveryStatus: 'failed'
        };
      }

      // Get tenant and global configuration
      const [tenantConfig, globalConfig] = await Promise.all([
        this.configService.getTenantConfig(),
        this.configService.getGlobalConfig()
      ]);

      // Prepare email options
      const emailOptions: SendGridEmailOptions = {
        to: request.to,
        subject: request.subject,
        html: request.html,
        text: request.text,
        from: {
          email: tenantConfig?.fromEmail || globalConfig.fromEmail,
          name: tenantConfig?.fromName || tenantConfig?.companyName || globalConfig.fromName
        },
        replyTo: tenantConfig?.replyToEmail || globalConfig.replyToEmail,
        trackingSettings: {
          openTracking: { enable: request.trackOpens ?? globalConfig.enableTracking },
          clickTracking: { enable: request.trackClicks ?? globalConfig.enableTracking }
        },
        customArgs: {
          tenantId: this.tenantId,
          templateId: request.templateId || 'custom',
          priority: request.priority || 'normal',
          source: 'enhanced_email_service',
          ...(request.metadata || {})
        }
      };

      // Send email via SendGrid
      const result = await sendEmailWithSendGrid(emailOptions);

      if (result.success) {
        // Increment usage counter
        await this.configService.incrementEmailUsage();

        // Log analytics
        await this.logEmailAnalytics({
          tenantId: this.tenantId,
          messageId: result.messageId || `msg_${Date.now()}`,
          recipientEmail: Array.isArray(request.to) ? request.to[0] : request.to,
          subject: request.subject,
          templateId: request.templateId,
          status: 'sent',
          sentAt: new Date(),
          tags: request.tags,
          metadata: request.metadata
        });

        // Get remaining quota
        const quota = await this.configService.getEmailQuota();
        
        return {
          success: true,
          messageId: result.messageId,
          deliveryStatus: 'sent',
          quotaRemaining: {
            daily: quota.dailyLimit - quota.dailyUsed,
            monthly: quota.monthlyLimit - quota.monthlyUsed
          }
        };
      } else {
        // Log failed attempt
        await this.logEmailAnalytics({
          tenantId: this.tenantId,
          messageId: `failed_${Date.now()}`,
          recipientEmail: Array.isArray(request.to) ? request.to[0] : request.to,
          subject: request.subject,
          templateId: request.templateId,
          status: 'failed',
          sentAt: new Date(),
          failedAt: new Date(),
          errorMessage: result.error,
          tags: request.tags,
          metadata: request.metadata
        });

        return {
          success: false,
          error: result.error,
          deliveryStatus: 'failed'
        };
      }

    } catch (error) {
      console.error('Enhanced email service error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        deliveryStatus: 'failed'
      };
    }
  }

  /**
   * Send invitation email using system templates
   */
  async sendInvitationEmail(
    recipientEmail: string,
    recipientName: string,
    senderName: string,
    roleName: string,
    invitationUrl: string,
    expirationDate: string
  ): Promise<EnhancedEmailResult> {
    try {
      const result = await this.systemEmailService.sendUserInvitation(
        recipientEmail,
        recipientName,
        senderName,
        roleName,
        invitationUrl,
        expirationDate
      );

      if (result.success) {
        // Get remaining quota
        const quota = await this.configService.getEmailQuota();
        
        return {
          success: true,
          messageId: result.messageId,
          deliveryStatus: 'sent',
          quotaRemaining: {
            daily: quota.dailyLimit - quota.dailyUsed,
            monthly: quota.monthlyLimit - quota.monthlyUsed
          }
        };
      } else {
        return {
          success: false,
          error: result.error,
          deliveryStatus: 'failed'
        };
      }
    } catch (error) {
      console.error('Error sending invitation email:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to send invitation',
        deliveryStatus: 'failed'
      };
    }
  }

  /**
   * Get email analytics for tenant
   */
  async getEmailAnalytics(
    startDate?: Date,
    endDate?: Date,
    limit: number = 100
  ): Promise<EmailAnalytics[]> {
    try {
      // This would typically use Firestore queries with date filters
      // For now, return empty array - implement based on your analytics needs
      return [];
    } catch (error) {
      console.error('Error getting email analytics:', error);
      return [];
    }
  }

  /**
   * Log email analytics event
   */
  private async logEmailAnalytics(analytics: EmailAnalytics): Promise<void> {
    try {
      await addDocument(COLLECTIONS.EMAIL_ANALYTICS, {
        ...analytics,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Error logging email analytics:', error);
      // Don't throw error - analytics logging shouldn't break email sending
    }
  }

  /**
   * Get email quota status
   */
  async getQuotaStatus(): Promise<{
    dailyUsed: number;
    dailyLimit: number;
    monthlyUsed: number;
    monthlyLimit: number;
    isBlocked: boolean;
    blockReason?: string;
  }> {
    try {
      const quota = await this.configService.getEmailQuota();
      return {
        dailyUsed: quota.dailyUsed,
        dailyLimit: quota.dailyLimit,
        monthlyUsed: quota.monthlyUsed,
        monthlyLimit: quota.monthlyLimit,
        isBlocked: quota.isBlocked,
        blockReason: quota.blockReason
      };
    } catch (error) {
      console.error('Error getting quota status:', error);
      throw error;
    }
  }
}
