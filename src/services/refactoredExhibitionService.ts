/**
 * Refactored Exhibition Service
 * Optimized for flat collection architecture with tenantId filtering
 * Implements strategic data duplication for performance
 */

import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  Timestamp,
  writeBatch,
  runTransaction,
  type DocumentSnapshot,
  type QueryConstraint
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { 
  validateTenantId, 
  addTenantDocument, 
  getTenantDocument, 
  updateTenantDocument,
  deleteTenantDocument,
  queryTenantCollection
} from './tenantIdHelperService';
import { syncDocumentDuplication } from './dataDuplicationService';
import type { Exhibition, TenantAwareEntity } from '@/types/firestore';

// ===== TYPES =====

export interface ExhibitionQueryOptions {
  limit?: number;
  orderBy?: {
    field: keyof Exhibition;
    direction: 'asc' | 'desc';
  };
  startAfter?: DocumentSnapshot;
  filters?: {
    field: keyof Exhibition;
    operator: '==' | '!=' | '<' | '<=' | '>' | '>=' | 'in' | 'not-in';
    value: any;
  }[];
}

export interface ExhibitionWithRelatedData extends Exhibition {
  taskCount?: number;
  eventCount?: number;
  leadCount?: number;
  totalBudget?: number;
  totalSpent?: number;
}

// ===== REFACTORED EXHIBITION SERVICE =====

export class RefactoredExhibitionService {
  private readonly collectionName = COLLECTIONS.EXHIBITIONS;

  /**
   * Get all exhibitions for tenant with optimized query
   */
  async getAll(
    tenantId: string, 
    options: ExhibitionQueryOptions = {}
  ): Promise<Exhibition[]> {
    try {
      validateTenantId(tenantId);

      const constraints: QueryConstraint[] = [
        where('tenantId', '==', tenantId)
      ];

      // Add filters
      if (options.filters) {
        options.filters.forEach(filter => {
          constraints.push(where(filter.field as string, filter.operator, filter.value));
        });
      }

      // Add ordering
      if (options.orderBy) {
        constraints.push(orderBy(options.orderBy.field as string, options.orderBy.direction));
      } else {
        // Default ordering by start date
        constraints.push(orderBy('startDate', 'desc'));
      }

      // Add limit
      if (options.limit) {
        constraints.push(limit(options.limit));
      }

      // Add pagination
      if (options.startAfter) {
        constraints.push(startAfter(options.startAfter));
      }

      const q = query(collection(db, this.collectionName), ...constraints);
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Exhibition));

    } catch (error) {
      console.error('Error getting exhibitions:', error);
      throw error;
    }
  }

  /**
   * Get exhibition by ID with tenant validation
   */
  async getById(tenantId: string, exhibitionId: string): Promise<Exhibition | null> {
    try {
      validateTenantId(tenantId);
      return await getTenantDocument<Exhibition>(tenantId, this.collectionName, exhibitionId);
    } catch (error) {
      console.error('Error getting exhibition by ID:', error);
      return null;
    }
  }

  /**
   * Create new exhibition with tenant stamping and data duplication
   */
  async create(tenantId: string, exhibitionData: Omit<Exhibition, 'id' | 'tenantId'>): Promise<Exhibition> {
    try {
      validateTenantId(tenantId);

      // Add tenant ID and timestamps
      const exhibitionWithTenant: Omit<Exhibition, 'id'> = {
        ...exhibitionData,
        tenantId,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      // Create exhibition using tenant-aware helper
      const exhibition = await addTenantDocument<Exhibition>(
        tenantId, 
        this.collectionName, 
        exhibitionWithTenant
      );

      // Trigger strategic data duplication
      await this.syncRelatedData(exhibition);

      return exhibition;

    } catch (error) {
      console.error('Error creating exhibition:', error);
      throw error;
    }
  }

  /**
   * Update exhibition with data duplication sync
   */
  async update(
    tenantId: string, 
    exhibitionId: string, 
    updates: Partial<Exhibition>
  ): Promise<void> {
    try {
      validateTenantId(tenantId);

      // Update exhibition
      await updateTenantDocument<Exhibition>(
        tenantId, 
        this.collectionName, 
        exhibitionId, 
        {
          ...updates,
          updatedAt: Timestamp.now()
        }
      );

      // If exhibition name changed, sync duplicated data
      if (updates.name) {
        const exhibition = await this.getById(tenantId, exhibitionId);
        if (exhibition) {
          await this.syncRelatedData(exhibition);
        }
      }

    } catch (error) {
      console.error('Error updating exhibition:', error);
      throw error;
    }
  }

  /**
   * Delete exhibition with cascade operations
   */
  async delete(tenantId: string, exhibitionId: string): Promise<void> {
    try {
      validateTenantId(tenantId);

      // Use transaction for cascade delete
      await runTransaction(db, async (transaction) => {
        // Delete exhibition
        const exhibitionRef = doc(db, this.collectionName, exhibitionId);
        transaction.delete(exhibitionRef);

        // Note: Cloud Functions will handle cascade deletes for related data
        // This includes tasks, events, expenses, etc.
      });

    } catch (error) {
      console.error('Error deleting exhibition:', error);
      throw error;
    }
  }

  /**
   * Get upcoming exhibitions with performance optimization
   */
  async getUpcoming(tenantId: string, limitCount: number = 10): Promise<Exhibition[]> {
    return this.getAll(tenantId, {
      filters: [
        { field: 'startDate', operator: '>', value: Timestamp.now() }
      ],
      orderBy: { field: 'startDate', direction: 'asc' },
      limit: limitCount
    });
  }

  /**
   * Get exhibitions by status
   */
  async getByStatus(tenantId: string, status: string): Promise<Exhibition[]> {
    return this.getAll(tenantId, {
      filters: [
        { field: 'status', operator: '==', value: status }
      ],
      orderBy: { field: 'startDate', direction: 'desc' }
    });
  }

  /**
   * Get exhibitions for date range
   */
  async getByDateRange(
    tenantId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<Exhibition[]> {
    return this.getAll(tenantId, {
      filters: [
        { field: 'startDate', operator: '>=', value: Timestamp.fromDate(startDate) },
        { field: 'startDate', operator: '<=', value: Timestamp.fromDate(endDate) }
      ],
      orderBy: { field: 'startDate', direction: 'asc' }
    });
  }

  /**
   * Get exhibitions with aggregated related data
   * Uses strategic data duplication to avoid expensive joins
   */
  async getWithRelatedData(tenantId: string): Promise<ExhibitionWithRelatedData[]> {
    try {
      const exhibitions = await this.getAll(tenantId);
      
      // Use strategic data duplication - related counts are already embedded
      // or can be efficiently queried from flat collections
      const exhibitionsWithData: ExhibitionWithRelatedData[] = await Promise.all(
        exhibitions.map(async (exhibition) => {
          // These queries are optimized with tenantId + exhibitionId indexes
          const [taskCount, eventCount, leadCount] = await Promise.all([
            this.getRelatedCount(tenantId, 'exhibition_tasks', exhibition.id!),
            this.getRelatedCount(tenantId, 'exhibition_events', exhibition.id!),
            this.getRelatedCount(tenantId, 'lead_contacts', exhibition.id!)
          ]);

          return {
            ...exhibition,
            taskCount,
            eventCount,
            leadCount
          };
        })
      );

      return exhibitionsWithData;

    } catch (error) {
      console.error('Error getting exhibitions with related data:', error);
      return [];
    }
  }

  /**
   * Private helper to get related document count
   */
  private async getRelatedCount(
    tenantId: string, 
    collectionName: string, 
    exhibitionId: string
  ): Promise<number> {
    try {
      const q = query(
        collection(db, collectionName),
        where('tenantId', '==', tenantId),
        where('exhibitionId', '==', exhibitionId)
      );
      
      const snapshot = await getDocs(q);
      return snapshot.size;
    } catch (error) {
      console.error(`Error getting ${collectionName} count:`, error);
      return 0;
    }
  }

  /**
   * Sync strategic data duplication for exhibition
   */
  private async syncRelatedData(exhibition: Exhibition): Promise<void> {
    try {
      // Sync exhibition name to related collections
      await syncDocumentDuplication({
        sourceCollection: this.collectionName,
        sourceDocumentId: exhibition.id!,
        sourceData: { exhibitionName: exhibition.name },
        targetMappings: [
          {
            collection: COLLECTIONS.EXHIBITION_TASKS,
            field: 'exhibitionName',
            filterField: 'exhibitionId'
          },
          {
            collection: COLLECTIONS.EXHIBITION_EVENTS,
            field: 'exhibitionName',
            filterField: 'exhibitionId'
          },
          {
            collection: COLLECTIONS.LEAD_CONTACTS,
            field: 'exhibitionName',
            filterField: 'exhibitionId'
          }
        ]
      });
    } catch (error) {
      console.error('Error syncing related data:', error);
      // Don't throw - this is a background operation
    }
  }
}

// Export singleton instance
export const refactoredExhibitionService = new RefactoredExhibitionService();
