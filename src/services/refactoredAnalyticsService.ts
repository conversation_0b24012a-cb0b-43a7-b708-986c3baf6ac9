/**
 * Refactored Analytics Service
 * Updated to leverage flat collection structure for powerful cross-exhibition and company-wide reporting
 * Implements tenant-aware analytics with optimized queries and caching
 */

import {
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { validateTenantId } from './tenantIdHelperService';
import { refactoredExhibitionService } from './refactoredExhibitionService';
import { refactoredTaskService } from './refactoredTaskService';
import { refactoredVendorService } from './refactoredVendorService';
import { refactoredFinancialService } from './refactoredFinancialService';
import type { 
  Exhibition, 
  Event as EvexEvent, 
  Lead, 
  Task, 
  Vendor,
  TenantAwareEntity 
} from '@/types/firestore';
import type { FinancialDocument } from './refactoredFinancialService';

// ===== ANALYTICS INTERFACES =====

export interface TenantAnalyticsMetrics {
  tenantId: string;
  period: {
    start: Date;
    end: Date;
    label: string;
  };
  
  // Core metrics
  exhibitions: {
    total: number;
    active: number;
    completed: number;
    upcoming: number;
    averageBudget: number;
    totalBudget: number;
  };
  
  events: {
    total: number;
    byType: Record<string, number>;
    averageAttendance: number;
    totalAttendance: number;
  };
  
  leads: {
    total: number;
    qualified: number;
    converted: number;
    conversionRate: number;
    averageValue: number;
    totalValue: number;
  };
  
  tasks: {
    total: number;
    completed: number;
    overdue: number;
    completionRate: number;
    averageCompletionTime: number;
  };
  
  financial: {
    totalBudgets: number;
    totalExpenses: number;
    totalPurchaseRequests: number;
    totalPurchaseOrders: number;
    budgetUtilization: number;
    pendingApprovals: number;
    overBudgetActivities: string[];
    costPerLead: number;
    roi: number;
  };
  
  vendors: {
    total: number;
    active: number;
    bySpecialization: Record<string, number>;
    averageRating: number;
    totalContracts: number;
  };
}

export interface CrossExhibitionAnalytics {
  tenantId: string;
  exhibitions: Exhibition[];
  
  // Performance comparison
  performanceRanking: Array<{
    exhibitionId: string;
    exhibitionName: string;
    score: number;
    metrics: {
      leadGeneration: number;
      budgetEfficiency: number;
      taskCompletion: number;
      vendorPerformance: number;
    };
  }>;
  
  // Trend analysis
  trends: {
    leadGeneration: TrendData[];
    budgetUtilization: TrendData[];
    taskCompletion: TrendData[];
    vendorPerformance: TrendData[];
  };
  
  // Best practices identification
  bestPractices: Array<{
    category: string;
    practice: string;
    exhibitionId: string;
    impact: number;
    recommendation: string;
  }>;
}

export interface CompanyWideReporting {
  tenantId: string;
  reportingPeriod: {
    start: Date;
    end: Date;
    label: string;
  };
  
  // Executive summary
  executiveSummary: {
    totalRevenue: number;
    totalCosts: number;
    netProfit: number;
    roi: number;
    totalLeads: number;
    conversionRate: number;
    activeExhibitions: number;
    completedProjects: number;
  };
  
  // Department performance
  departmentPerformance: Array<{
    department: string;
    budget: number;
    spent: number;
    leads: number;
    conversions: number;
    efficiency: number;
  }>;
  
  // Geographic analysis
  geographicAnalysis: Array<{
    region: string;
    exhibitions: number;
    totalBudget: number;
    totalLeads: number;
    averageROI: number;
  }>;
  
  // Vendor analysis
  vendorAnalysis: {
    totalVendors: number;
    topPerformers: Array<{
      vendorId: string;
      vendorName: string;
      totalContracts: number;
      totalValue: number;
      averageRating: number;
    }>;
    costSavings: number;
  };
}

interface TrendData {
  period: string;
  value: number;
  change: number;
  changePercent: number;
}

// ===== REFACTORED ANALYTICS SERVICE =====

class RefactoredAnalyticsService {
  /**
   * Get comprehensive tenant analytics metrics
   */
  async getTenantAnalytics(
    tenantId: string, 
    startDate?: Date, 
    endDate?: Date
  ): Promise<TenantAnalyticsMetrics> {
    validateTenantId(tenantId);
    
    const period = {
      start: startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      end: endDate || new Date(),
      label: this.formatPeriodLabel(startDate, endDate)
    };

    // Fetch all data in parallel using refactored services
    const [exhibitions, events, leads, tasks, financialSummary, vendors] = await Promise.all([
      refactoredExhibitionService.getAll({ tenantId, limit: 1000 }),
      this.getEvents(tenantId, period.start, period.end),
      this.getLeads(tenantId, period.start, period.end),
      refactoredTaskService.getAll({ tenantId, limit: 1000 }),
      refactoredFinancialService.getFinancialSummary(tenantId),
      refactoredVendorService.getAll({ tenantId, limit: 1000 })
    ]);

    // Calculate exhibition metrics
    const exhibitionMetrics = this.calculateExhibitionMetrics(exhibitions, period);
    
    // Calculate event metrics
    const eventMetrics = this.calculateEventMetrics(events);
    
    // Calculate lead metrics
    const leadMetrics = this.calculateLeadMetrics(leads);
    
    // Calculate task metrics
    const taskMetrics = this.calculateTaskMetrics(tasks);
    
    // Calculate vendor metrics
    const vendorMetrics = this.calculateVendorMetrics(vendors);
    
    // Enhanced financial metrics with ROI and cost per lead
    const enhancedFinancialMetrics = {
      ...financialSummary,
      costPerLead: leadMetrics.total > 0 ? financialSummary.totalExpenses / leadMetrics.total : 0,
      roi: this.calculateROI(leadMetrics.totalValue, financialSummary.totalExpenses)
    };

    return {
      tenantId,
      period,
      exhibitions: exhibitionMetrics,
      events: eventMetrics,
      leads: leadMetrics,
      tasks: taskMetrics,
      financial: enhancedFinancialMetrics,
      vendors: vendorMetrics
    };
  }

  /**
   * Get cross-exhibition analytics for performance comparison
   */
  async getCrossExhibitionAnalytics(tenantId: string): Promise<CrossExhibitionAnalytics> {
    validateTenantId(tenantId);
    
    const exhibitions = await refactoredExhibitionService.getAll({ tenantId, limit: 1000 });
    
    // Calculate performance ranking for each exhibition
    const performanceRanking = await Promise.all(
      exhibitions.map(async (exhibition) => {
        const [tasks, financials, leads] = await Promise.all([
          refactoredTaskService.getByActivity(tenantId, exhibition.id!),
          refactoredFinancialService.getAll({ tenantId, activityId: exhibition.id! }),
          this.getLeadsByActivity(tenantId, exhibition.id!)
        ]);

        const metrics = {
          leadGeneration: this.calculateLeadGenerationScore(leads, exhibition),
          budgetEfficiency: this.calculateBudgetEfficiencyScore(financials, exhibition),
          taskCompletion: this.calculateTaskCompletionScore(tasks),
          vendorPerformance: this.calculateVendorPerformanceScore(financials)
        };

        const score = (metrics.leadGeneration + metrics.budgetEfficiency + 
                      metrics.taskCompletion + metrics.vendorPerformance) / 4;

        return {
          exhibitionId: exhibition.id!,
          exhibitionName: exhibition.name,
          score,
          metrics
        };
      })
    );

    // Sort by performance score
    performanceRanking.sort((a, b) => b.score - a.score);

    // Calculate trends (simplified for now)
    const trends = {
      leadGeneration: this.calculateTrendData(exhibitions, 'leads'),
      budgetUtilization: this.calculateTrendData(exhibitions, 'budget'),
      taskCompletion: this.calculateTrendData(exhibitions, 'tasks'),
      vendorPerformance: this.calculateTrendData(exhibitions, 'vendors')
    };

    // Identify best practices
    const bestPractices = this.identifyBestPractices(performanceRanking);

    return {
      tenantId,
      exhibitions,
      performanceRanking,
      trends,
      bestPractices
    };
  }

  /**
   * Generate company-wide reporting
   */
  async getCompanyWideReporting(
    tenantId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<CompanyWideReporting> {
    validateTenantId(tenantId);
    
    const reportingPeriod = {
      start: startDate || new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // 90 days ago
      end: endDate || new Date(),
      label: this.formatPeriodLabel(startDate, endDate)
    };

    // Get comprehensive data
    const [tenantAnalytics, crossExhibitionAnalytics] = await Promise.all([
      this.getTenantAnalytics(tenantId, reportingPeriod.start, reportingPeriod.end),
      this.getCrossExhibitionAnalytics(tenantId)
    ]);

    // Calculate executive summary
    const executiveSummary = {
      totalRevenue: tenantAnalytics.leads.totalValue,
      totalCosts: tenantAnalytics.financial.totalExpenses,
      netProfit: tenantAnalytics.leads.totalValue - tenantAnalytics.financial.totalExpenses,
      roi: tenantAnalytics.financial.roi,
      totalLeads: tenantAnalytics.leads.total,
      conversionRate: tenantAnalytics.leads.conversionRate,
      activeExhibitions: tenantAnalytics.exhibitions.active,
      completedProjects: tenantAnalytics.exhibitions.completed
    };

    // Calculate department performance (simplified)
    const departmentPerformance = await this.calculateDepartmentPerformance(tenantId, reportingPeriod);
    
    // Calculate geographic analysis
    const geographicAnalysis = await this.calculateGeographicAnalysis(tenantId, reportingPeriod);
    
    // Calculate vendor analysis
    const vendorAnalysis = await this.calculateVendorAnalysis(tenantId);

    return {
      tenantId,
      reportingPeriod,
      executiveSummary,
      departmentPerformance,
      geographicAnalysis,
      vendorAnalysis
    };
  }

  // ===== PRIVATE HELPER METHODS =====

  private async getEvents(tenantId: string, startDate: Date, endDate: Date): Promise<EvexEvent[]> {
    const eventsQuery = query(
      collection(db, COLLECTIONS.EXHIBITION_EVENTS),
      where('tenantId', '==', tenantId),
      where('startDate', '>=', Timestamp.fromDate(startDate)),
      where('startDate', '<=', Timestamp.fromDate(endDate)),
      orderBy('startDate', 'desc')
    );
    
    const snapshot = await getDocs(eventsQuery);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as EvexEvent));
  }

  private async getLeads(tenantId: string, startDate: Date, endDate: Date): Promise<Lead[]> {
    const leadsQuery = query(
      collection(db, COLLECTIONS.LEAD_CONTACTS),
      where('tenantId', '==', tenantId),
      where('createdAt', '>=', Timestamp.fromDate(startDate)),
      where('createdAt', '<=', Timestamp.fromDate(endDate)),
      orderBy('createdAt', 'desc')
    );
    
    const snapshot = await getDocs(leadsQuery);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Lead));
  }

  private async getLeadsByActivity(tenantId: string, activityId: string): Promise<Lead[]> {
    const leadsQuery = query(
      collection(db, COLLECTIONS.LEAD_CONTACTS),
      where('tenantId', '==', tenantId),
      where('exhibitionId', '==', activityId)
    );
    
    const snapshot = await getDocs(leadsQuery);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Lead));
  }

  private calculateExhibitionMetrics(exhibitions: Exhibition[], period: any) {
    const now = new Date();
    const active = exhibitions.filter(ex => 
      new Date(ex.startDate as any) <= now && new Date(ex.endDate as any) >= now
    ).length;
    
    const completed = exhibitions.filter(ex => 
      new Date(ex.endDate as any) < now
    ).length;
    
    const upcoming = exhibitions.filter(ex => 
      new Date(ex.startDate as any) > now
    ).length;

    const totalBudget = exhibitions.reduce((sum, ex) => sum + (ex.estimatedBudget || 0), 0);
    const averageBudget = exhibitions.length > 0 ? totalBudget / exhibitions.length : 0;

    return {
      total: exhibitions.length,
      active,
      completed,
      upcoming,
      averageBudget,
      totalBudget
    };
  }

  private calculateEventMetrics(events: EvexEvent[]) {
    const byType: Record<string, number> = {};
    let totalAttendance = 0;

    events.forEach(event => {
      byType[event.eventType || 'unknown'] = (byType[event.eventType || 'unknown'] || 0) + 1;
      totalAttendance += event.expectedAttendees || 0;
    });

    return {
      total: events.length,
      byType,
      averageAttendance: events.length > 0 ? totalAttendance / events.length : 0,
      totalAttendance
    };
  }

  private calculateLeadMetrics(leads: Lead[]) {
    const qualified = leads.filter(lead => lead.leadScore && lead.leadScore >= 70).length;
    const converted = leads.filter(lead => lead.status === 'Converted').length;
    const conversionRate = leads.length > 0 ? (converted / leads.length) * 100 : 0;
    
    const totalValue = leads.reduce((sum, lead) => sum + (lead.estimatedValue || 5000), 0);
    const averageValue = leads.length > 0 ? totalValue / leads.length : 0;

    return {
      total: leads.length,
      qualified,
      converted,
      conversionRate,
      averageValue,
      totalValue
    };
  }

  private calculateTaskMetrics(tasks: Task[]) {
    const completed = tasks.filter(task => task.status === 'Completed').length;
    const overdue = tasks.filter(task => 
      task.dueDate && new Date(task.dueDate as any) < new Date() && task.status !== 'Completed'
    ).length;
    
    const completionRate = tasks.length > 0 ? (completed / tasks.length) * 100 : 0;
    
    // Calculate average completion time (simplified)
    const completedTasks = tasks.filter(task => task.status === 'Completed');
    const averageCompletionTime = completedTasks.length > 0 ? 5 : 0; // Placeholder

    return {
      total: tasks.length,
      completed,
      overdue,
      completionRate,
      averageCompletionTime
    };
  }

  private calculateVendorMetrics(vendors: Vendor[]) {
    const active = vendors.filter(vendor => vendor.status === 'Active').length;
    
    const bySpecialization: Record<string, number> = {};
    vendors.forEach(vendor => {
      if (vendor.specialization) {
        bySpecialization[vendor.specialization] = (bySpecialization[vendor.specialization] || 0) + 1;
      }
    });

    const totalRating = vendors.reduce((sum, vendor) => sum + (vendor.rating || 0), 0);
    const averageRating = vendors.length > 0 ? totalRating / vendors.length : 0;

    return {
      total: vendors.length,
      active,
      bySpecialization,
      averageRating,
      totalContracts: vendors.reduce((sum, vendor) => sum + (vendor.contractsCount || 0), 0)
    };
  }

  private calculateROI(revenue: number, costs: number): number {
    return costs > 0 ? ((revenue - costs) / costs) * 100 : 0;
  }

  private formatPeriodLabel(startDate?: Date, endDate?: Date): string {
    if (!startDate || !endDate) return 'Last 30 days';
    return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
  }

  // Placeholder methods for complex calculations
  private calculateLeadGenerationScore(leads: Lead[], exhibition: Exhibition): number {
    const leadGoal = exhibition.leadGoal || 100;
    return Math.min(100, (leads.length / leadGoal) * 100);
  }

  private calculateBudgetEfficiencyScore(financials: FinancialDocument[], exhibition: Exhibition): number {
    const budgets = financials.filter(f => f.type === 'budget');
    const expenses = financials.filter(f => f.type === 'expense');
    
    const totalBudget = budgets.reduce((sum, b) => sum + (b.totalBudget || 0), 0);
    const totalExpenses = expenses.reduce((sum, e) => sum + e.amount, 0);
    
    if (totalBudget === 0) return 0;
    const utilization = (totalExpenses / totalBudget) * 100;
    
    // Score is higher when utilization is close to 100% but not over
    return utilization <= 100 ? utilization : Math.max(0, 200 - utilization);
  }

  private calculateTaskCompletionScore(tasks: Task[]): number {
    if (tasks.length === 0) return 0;
    const completed = tasks.filter(task => task.status === 'Completed').length;
    return (completed / tasks.length) * 100;
  }

  private calculateVendorPerformanceScore(financials: FinancialDocument[]): number {
    // Simplified vendor performance based on purchase orders
    const purchaseOrders = financials.filter(f => f.type === 'purchase_order');
    const completedOrders = purchaseOrders.filter(po => po.status === 'Fulfilled').length;
    
    if (purchaseOrders.length === 0) return 100; // No orders = perfect score
    return (completedOrders / purchaseOrders.length) * 100;
  }

  private calculateTrendData(exhibitions: Exhibition[], category: string): TrendData[] {
    // Simplified trend calculation - would be more complex in real implementation
    return exhibitions.slice(0, 6).map((ex, index) => ({
      period: `Month ${index + 1}`,
      value: Math.random() * 100,
      change: Math.random() * 20 - 10,
      changePercent: Math.random() * 20 - 10
    }));
  }

  private identifyBestPractices(performanceRanking: any[]): any[] {
    // Identify best practices from top performers
    const topPerformers = performanceRanking.slice(0, 3);
    
    return topPerformers.map(performer => ({
      category: 'Lead Generation',
      practice: 'Effective booth design and engagement strategy',
      exhibitionId: performer.exhibitionId,
      impact: performer.metrics.leadGeneration,
      recommendation: 'Implement similar engagement strategies in other exhibitions'
    }));
  }

  private async calculateDepartmentPerformance(tenantId: string, period: any): Promise<any[]> {
    // Simplified department performance calculation
    return [
      {
        department: 'Marketing',
        budget: 100000,
        spent: 85000,
        leads: 150,
        conversions: 23,
        efficiency: 85
      },
      {
        department: 'Sales',
        budget: 75000,
        spent: 72000,
        leads: 200,
        conversions: 45,
        efficiency: 96
      }
    ];
  }

  private async calculateGeographicAnalysis(tenantId: string, period: any): Promise<any[]> {
    // Simplified geographic analysis
    return [
      {
        region: 'North America',
        exhibitions: 5,
        totalBudget: 250000,
        totalLeads: 300,
        averageROI: 125
      },
      {
        region: 'Europe',
        exhibitions: 3,
        totalBudget: 180000,
        totalLeads: 220,
        averageROI: 110
      }
    ];
  }

  private async calculateVendorAnalysis(tenantId: string): Promise<any> {
    const vendors = await refactoredVendorService.getAll({ tenantId, limit: 1000 });
    
    const topPerformers = vendors
      .sort((a, b) => (b.rating || 0) - (a.rating || 0))
      .slice(0, 5)
      .map(vendor => ({
        vendorId: vendor.id!,
        vendorName: vendor.companyName,
        totalContracts: vendor.contractsCount || 0,
        totalValue: vendor.totalContractValue || 0,
        averageRating: vendor.rating || 0
      }));

    return {
      totalVendors: vendors.length,
      topPerformers,
      costSavings: 25000 // Placeholder
    };
  }
}

// Export singleton instance
export const refactoredAnalyticsService = new RefactoredAnalyticsService();
