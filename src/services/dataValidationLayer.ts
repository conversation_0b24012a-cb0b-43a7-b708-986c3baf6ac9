/**
 * Data Validation Layer
 * Backend validation functions that ensure all documents have valid tenantId before write operations
 */

import { 
  DocumentData,
  Timestamp,
  FieldValue
} from 'firebase/firestore';
import { validateTenantId } from './tenantIdHelperService';
import type { TenantAwareEntity } from '@/types/firestore';

// ===== VALIDATION TYPES =====

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  code: string;
  message: string;
  severity: 'error' | 'critical';
}

export interface ValidationWarning {
  field: string;
  code: string;
  message: string;
  suggestion?: string;
}

export interface ValidationRule {
  field: string;
  required: boolean;
  type?: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object';
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  customValidator?: (value: any, document: any) => ValidationResult;
}

export interface CollectionValidationSchema {
  collectionName: string;
  rules: ValidationRule[];
  customValidators?: Array<(document: any) => ValidationResult>;
  requireTenantId: boolean;
  allowedTenants?: string[];
}

// ===== CORE VALIDATION FUNCTIONS =====

/**
 * Validate tenant ID in document
 */
export function validateDocumentTenantId(
  document: any,
  expectedTenantId?: string
): ValidationResult {
  const result: ValidationResult = {
    valid: true,
    errors: [],
    warnings: []
  };

  // Check if tenantId exists
  if (!document.tenantId) {
    result.valid = false;
    result.errors.push({
      field: 'tenantId',
      code: 'MISSING_TENANT_ID',
      message: 'Document must include a valid tenantId',
      severity: 'critical'
    });
    return result;
  }

  // Validate tenantId format
  try {
    validateTenantId(document.tenantId);
  } catch (error) {
    result.valid = false;
    result.errors.push({
      field: 'tenantId',
      code: 'INVALID_TENANT_ID',
      message: `Invalid tenantId format: ${error}`,
      severity: 'critical'
    });
    return result;
  }

  // Check if tenantId matches expected value
  if (expectedTenantId && document.tenantId !== expectedTenantId) {
    result.valid = false;
    result.errors.push({
      field: 'tenantId',
      code: 'TENANT_ID_MISMATCH',
      message: `Document tenantId (${document.tenantId}) does not match expected tenantId (${expectedTenantId})`,
      severity: 'critical'
    });
  }

  return result;
}

/**
 * Validate document against schema rules
 */
export function validateDocumentSchema(
  document: any,
  schema: CollectionValidationSchema
): ValidationResult {
  const result: ValidationResult = {
    valid: true,
    errors: [],
    warnings: []
  };

  // Validate tenant ID if required
  if (schema.requireTenantId) {
    const tenantValidation = validateDocumentTenantId(document);
    result.errors.push(...tenantValidation.errors);
    result.warnings.push(...tenantValidation.warnings);
    
    if (!tenantValidation.valid) {
      result.valid = false;
    }

    // Check allowed tenants
    if (schema.allowedTenants && document.tenantId && !schema.allowedTenants.includes(document.tenantId)) {
      result.valid = false;
      result.errors.push({
        field: 'tenantId',
        code: 'TENANT_NOT_ALLOWED',
        message: `Tenant ${document.tenantId} is not allowed for collection ${schema.collectionName}`,
        severity: 'error'
      });
    }
  }

  // Validate individual fields
  for (const rule of schema.rules) {
    const fieldValidation = validateField(document[rule.field], rule, document);
    result.errors.push(...fieldValidation.errors);
    result.warnings.push(...fieldValidation.warnings);
    
    if (!fieldValidation.valid) {
      result.valid = false;
    }
  }

  // Run custom validators
  if (schema.customValidators) {
    for (const validator of schema.customValidators) {
      const customValidation = validator(document);
      result.errors.push(...customValidation.errors);
      result.warnings.push(...customValidation.warnings);
      
      if (!customValidation.valid) {
        result.valid = false;
      }
    }
  }

  return result;
}

/**
 * Validate individual field
 */
export function validateField(
  value: any,
  rule: ValidationRule,
  document?: any
): ValidationResult {
  const result: ValidationResult = {
    valid: true,
    errors: [],
    warnings: []
  };

  // Check required fields
  if (rule.required && (value === undefined || value === null || value === '')) {
    result.valid = false;
    result.errors.push({
      field: rule.field,
      code: 'REQUIRED_FIELD_MISSING',
      message: `Field ${rule.field} is required`,
      severity: 'error'
    });
    return result;
  }

  // Skip further validation if field is not present and not required
  if (value === undefined || value === null) {
    return result;
  }

  // Type validation
  if (rule.type) {
    const typeValidation = validateFieldType(value, rule.type, rule.field);
    result.errors.push(...typeValidation.errors);
    result.warnings.push(...typeValidation.warnings);
    
    if (!typeValidation.valid) {
      result.valid = false;
    }
  }

  // Length validation for strings and arrays
  if (rule.minLength !== undefined || rule.maxLength !== undefined) {
    const lengthValidation = validateFieldLength(value, rule);
    result.errors.push(...lengthValidation.errors);
    result.warnings.push(...lengthValidation.warnings);
    
    if (!lengthValidation.valid) {
      result.valid = false;
    }
  }

  // Pattern validation for strings
  if (rule.pattern && typeof value === 'string') {
    if (!rule.pattern.test(value)) {
      result.valid = false;
      result.errors.push({
        field: rule.field,
        code: 'PATTERN_MISMATCH',
        message: `Field ${rule.field} does not match required pattern`,
        severity: 'error'
      });
    }
  }

  // Custom field validator
  if (rule.customValidator) {
    const customValidation = rule.customValidator(value, document);
    result.errors.push(...customValidation.errors);
    result.warnings.push(...customValidation.warnings);
    
    if (!customValidation.valid) {
      result.valid = false;
    }
  }

  return result;
}

/**
 * Validate field type
 */
function validateFieldType(
  value: any,
  expectedType: string,
  fieldName: string
): ValidationResult {
  const result: ValidationResult = {
    valid: true,
    errors: [],
    warnings: []
  };

  let actualType = typeof value;
  
  // Handle special cases
  if (value instanceof Date || value instanceof Timestamp) {
    actualType = 'date';
  } else if (Array.isArray(value)) {
    actualType = 'array';
  } else if (value instanceof FieldValue) {
    // Skip validation for Firestore FieldValue objects
    return result;
  }

  if (actualType !== expectedType) {
    result.valid = false;
    result.errors.push({
      field: fieldName,
      code: 'TYPE_MISMATCH',
      message: `Field ${fieldName} expected type ${expectedType} but got ${actualType}`,
      severity: 'error'
    });
  }

  return result;
}

/**
 * Validate field length
 */
function validateFieldLength(
  value: any,
  rule: ValidationRule
): ValidationResult {
  const result: ValidationResult = {
    valid: true,
    errors: [],
    warnings: []
  };

  let length: number;
  
  if (typeof value === 'string') {
    length = value.length;
  } else if (Array.isArray(value)) {
    length = value.length;
  } else {
    // Skip length validation for non-string/array types
    return result;
  }

  if (rule.minLength !== undefined && length < rule.minLength) {
    result.valid = false;
    result.errors.push({
      field: rule.field,
      code: 'MIN_LENGTH_VIOLATION',
      message: `Field ${rule.field} must be at least ${rule.minLength} characters/items long`,
      severity: 'error'
    });
  }

  if (rule.maxLength !== undefined && length > rule.maxLength) {
    result.valid = false;
    result.errors.push({
      field: rule.field,
      code: 'MAX_LENGTH_VIOLATION',
      message: `Field ${rule.field} must be no more than ${rule.maxLength} characters/items long`,
      severity: 'error'
    });
  }

  return result;
}

// ===== BATCH VALIDATION =====

/**
 * Validate multiple documents
 */
export function validateDocumentBatch(
  documents: any[],
  schema: CollectionValidationSchema
): {
  valid: boolean;
  results: Array<{ documentIndex: number; validation: ValidationResult }>;
  summary: {
    totalDocuments: number;
    validDocuments: number;
    invalidDocuments: number;
    totalErrors: number;
    totalWarnings: number;
  };
} {
  const results: Array<{ documentIndex: number; validation: ValidationResult }> = [];
  let validCount = 0;
  let totalErrors = 0;
  let totalWarnings = 0;

  for (let i = 0; i < documents.length; i++) {
    const validation = validateDocumentSchema(documents[i], schema);
    results.push({ documentIndex: i, validation });
    
    if (validation.valid) {
      validCount++;
    }
    
    totalErrors += validation.errors.length;
    totalWarnings += validation.warnings.length;
  }

  return {
    valid: validCount === documents.length,
    results,
    summary: {
      totalDocuments: documents.length,
      validDocuments: validCount,
      invalidDocuments: documents.length - validCount,
      totalErrors,
      totalWarnings
    }
  };
}

// ===== VALIDATION MIDDLEWARE =====

/**
 * Create validation middleware for write operations
 */
export function createValidationMiddleware(schema: CollectionValidationSchema) {
  return {
    /**
     * Validate before document creation
     */
    validateCreate: (document: any): ValidationResult => {
      // Ensure timestamps are set for new documents
      const documentWithTimestamps = {
        ...document,
        createdAt: document.createdAt || new Date(),
        updatedAt: document.updatedAt || new Date()
      };
      
      return validateDocumentSchema(documentWithTimestamps, schema);
    },

    /**
     * Validate before document update
     */
    validateUpdate: (updates: any, existingDocument?: any): ValidationResult => {
      // For updates, we validate the merged document
      const mergedDocument = existingDocument 
        ? { ...existingDocument, ...updates, updatedAt: new Date() }
        : { ...updates, updatedAt: new Date() };
      
      return validateDocumentSchema(mergedDocument, schema);
    },

    /**
     * Validate before document deletion
     */
    validateDelete: (document: any): ValidationResult => {
      // Basic validation for delete operations
      return validateDocumentTenantId(document);
    }
  };
}

// ===== UTILITY FUNCTIONS =====

/**
 * Format validation errors for display
 */
export function formatValidationErrors(errors: ValidationError[]): string[] {
  return errors.map(error => 
    `[${error.severity.toUpperCase()}] ${error.field}: ${error.message} (${error.code})`
  );
}

/**
 * Format validation warnings for display
 */
export function formatValidationWarnings(warnings: ValidationWarning[]): string[] {
  return warnings.map(warning => 
    `[WARNING] ${warning.field}: ${warning.message} (${warning.code})${warning.suggestion ? ` - ${warning.suggestion}` : ''}`
  );
}

/**
 * Check if validation result has critical errors
 */
export function hasCriticalErrors(result: ValidationResult): boolean {
  return result.errors.some(error => error.severity === 'critical');
}

/**
 * Sanitize document for safe storage
 */
export function sanitizeDocument(document: any): any {
  const sanitized = { ...document };
  
  // Remove undefined values
  Object.keys(sanitized).forEach(key => {
    if (sanitized[key] === undefined) {
      delete sanitized[key];
    }
  });
  
  // Ensure proper timestamp format
  if (sanitized.createdAt && !(sanitized.createdAt instanceof Date) && !(sanitized.createdAt instanceof Timestamp)) {
    sanitized.createdAt = new Date(sanitized.createdAt);
  }
  
  if (sanitized.updatedAt && !(sanitized.updatedAt instanceof Date) && !(sanitized.updatedAt instanceof Timestamp)) {
    sanitized.updatedAt = new Date(sanitized.updatedAt);
  }
  
  return sanitized;
}
