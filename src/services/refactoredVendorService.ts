/**
 * Refactored Vendor Service
 * Optimized for flat collection architecture with tenantId filtering
 * Implements strategic data duplication for performance
 */

import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  Timestamp,
  writeBatch,
  runTransaction,
  type DocumentSnapshot,
  type QueryConstraint
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { 
  validateTenantId, 
  addTenantDocument, 
  getTenantDocument, 
  updateTenantDocument,
  deleteTenantDocument
} from './tenantIdHelperService';
import { syncDocumentDuplication } from './dataDuplicationService';
import type { Vendor, TenantAwareEntity, ContractorSpecialization, VendorStatus } from '@/types/firestore';

// ===== TYPES =====

export interface VendorQueryOptions {
  limit?: number;
  orderBy?: {
    field: keyof Vendor;
    direction: 'asc' | 'desc';
  };
  startAfter?: DocumentSnapshot;
  filters?: {
    field: keyof Vendor;
    operator: '==' | '!=' | '<' | '<=' | '>' | '>=' | 'in' | 'not-in' | 'array-contains';
    value: any;
  }[];
}

export interface VendorWithRelatedData extends Vendor {
  activeProjectsCount?: number;
  totalInvoicesAmount?: number;
  lastProjectDate?: Date;
  performanceScore?: number;
}

export interface VendorSummary {
  total: number;
  bySpecialization: Record<ContractorSpecialization, number>;
  byStatus: Record<VendorStatus, number>;
  byRegion: Record<string, number>;
  preferred: number;
  averageRating: number;
}

// ===== REFACTORED VENDOR SERVICE =====

export class RefactoredVendorService {
  private readonly collectionName = COLLECTIONS.VENDOR_PROFILES;

  /**
   * Get all vendors for tenant with optimized query
   */
  async getAll(
    tenantId: string, 
    options: VendorQueryOptions = {}
  ): Promise<Vendor[]> {
    try {
      validateTenantId(tenantId);

      const constraints: QueryConstraint[] = [
        where('tenantId', '==', tenantId)
      ];

      // Add filters
      if (options.filters) {
        options.filters.forEach(filter => {
          constraints.push(where(filter.field as string, filter.operator, filter.value));
        });
      }

      // Add ordering
      if (options.orderBy) {
        constraints.push(orderBy(options.orderBy.field as string, options.orderBy.direction));
      } else {
        // Default ordering by name
        constraints.push(orderBy('name', 'asc'));
      }

      // Add limit
      if (options.limit) {
        constraints.push(limit(options.limit));
      }

      // Add pagination
      if (options.startAfter) {
        constraints.push(startAfter(options.startAfter));
      }

      const q = query(collection(db, this.collectionName), ...constraints);
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Vendor));

    } catch (error) {
      console.error('Error getting vendors:', error);
      throw error;
    }
  }

  /**
   * Get vendor by ID with tenant validation
   */
  async getById(tenantId: string, vendorId: string): Promise<Vendor | null> {
    try {
      validateTenantId(tenantId);
      return await getTenantDocument<Vendor>(tenantId, this.collectionName, vendorId);
    } catch (error) {
      console.error('Error getting vendor by ID:', error);
      return null;
    }
  }

  /**
   * Create new vendor with tenant stamping and data duplication
   */
  async create(tenantId: string, vendorData: Omit<Vendor, 'id' | 'tenantId'>): Promise<Vendor> {
    try {
      validateTenantId(tenantId);

      // Add tenant ID and timestamps
      const vendorWithTenant: Omit<Vendor, 'id'> = {
        ...vendorData,
        tenantId,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      // Create vendor using tenant-aware helper
      const vendor = await addTenantDocument<Vendor>(
        tenantId, 
        this.collectionName, 
        vendorWithTenant
      );

      // Trigger strategic data duplication
      await this.syncRelatedData(vendor);

      return vendor;

    } catch (error) {
      console.error('Error creating vendor:', error);
      throw error;
    }
  }

  /**
   * Update vendor with data duplication sync
   */
  async update(
    tenantId: string, 
    vendorId: string, 
    updates: Partial<Vendor>
  ): Promise<void> {
    try {
      validateTenantId(tenantId);

      // Update vendor
      await updateTenantDocument<Vendor>(
        tenantId, 
        this.collectionName, 
        vendorId, 
        {
          ...updates,
          updatedAt: Timestamp.now()
        }
      );

      // If vendor name changed, sync duplicated data
      if (updates.name) {
        const vendor = await this.getById(tenantId, vendorId);
        if (vendor) {
          await this.syncRelatedData(vendor);
        }
      }

    } catch (error) {
      console.error('Error updating vendor:', error);
      throw error;
    }
  }

  /**
   * Delete vendor with cascade operations
   */
  async delete(tenantId: string, vendorId: string): Promise<void> {
    try {
      validateTenantId(tenantId);

      // Use transaction for cascade delete
      await runTransaction(db, async (transaction) => {
        // Delete vendor
        const vendorRef = doc(db, this.collectionName, vendorId);
        transaction.delete(vendorRef);

        // Note: Cloud Functions will handle cascade deletes for related data
        // This includes purchase orders, invoices, performance reviews, etc.
      });

    } catch (error) {
      console.error('Error deleting vendor:', error);
      throw error;
    }
  }

  /**
   * Get vendors by specialization
   */
  async getBySpecialization(tenantId: string, specialization: ContractorSpecialization): Promise<Vendor[]> {
    return this.getAll(tenantId, {
      filters: [
        { field: 'specialization', operator: '==', value: specialization }
      ],
      orderBy: { field: 'name', direction: 'asc' }
    });
  }

  /**
   * Get vendors by status
   */
  async getByStatus(tenantId: string, status: VendorStatus): Promise<Vendor[]> {
    return this.getAll(tenantId, {
      filters: [
        { field: 'status', operator: '==', value: status }
      ],
      orderBy: { field: 'name', direction: 'asc' }
    });
  }

  /**
   * Get preferred vendors
   */
  async getPreferred(tenantId: string): Promise<Vendor[]> {
    return this.getAll(tenantId, {
      filters: [
        { field: 'isPreferred', operator: '==', value: true },
        { field: 'status', operator: '==', value: 'Active' }
      ],
      orderBy: { field: 'name', direction: 'asc' }
    });
  }

  /**
   * Get vendors by region
   */
  async getByRegion(tenantId: string, region: string): Promise<Vendor[]> {
    return this.getAll(tenantId, {
      filters: [
        { field: 'regionsOfOperation', operator: 'array-contains', value: region },
        { field: 'status', operator: '==', value: 'Active' }
      ],
      orderBy: { field: 'name', direction: 'asc' }
    });
  }

  /**
   * Search vendors by name or company
   */
  async search(tenantId: string, searchTerm: string): Promise<Vendor[]> {
    try {
      validateTenantId(tenantId);

      // Note: Firestore doesn't support full-text search natively
      // This is a simplified implementation that gets all vendors and filters client-side
      // For production, consider using Algolia or similar search service
      
      const allVendors = await this.getAll(tenantId);
      const searchTermLower = searchTerm.toLowerCase();

      return allVendors.filter(vendor => 
        vendor.name.toLowerCase().includes(searchTermLower) ||
        vendor.companyName?.toLowerCase().includes(searchTermLower) ||
        vendor.contactPerson.toLowerCase().includes(searchTermLower) ||
        vendor.email.toLowerCase().includes(searchTermLower)
      );

    } catch (error) {
      console.error('Error searching vendors:', error);
      return [];
    }
  }

  /**
   * Get vendor summary statistics
   */
  async getSummary(tenantId: string): Promise<VendorSummary> {
    try {
      const vendors = await this.getAll(tenantId);

      const summary: VendorSummary = {
        total: vendors.length,
        bySpecialization: {
          'Logistics': 0,
          'Stand Construction': 0,
          'AV Equipment': 0,
          'Catering': 0,
          'Printing': 0,
          'Marketing Agency': 0,
          'Staffing': 0,
          'Security': 0,
          'Software Provider': 0,
          'Consulting': 0,
          'Travel Agency': 0,
          'Merchandise': 0,
          'Other': 0
        },
        byStatus: {
          'Active': 0,
          'Archived': 0
        },
        byRegion: {},
        preferred: 0,
        averageRating: 0
      };

      let totalRating = 0;
      let ratedVendors = 0;

      vendors.forEach(vendor => {
        // Count by specialization
        summary.bySpecialization[vendor.specialization]++;

        // Count by status
        summary.byStatus[vendor.status]++;

        // Count preferred
        if (vendor.isPreferred) {
          summary.preferred++;
        }

        // Count by regions
        vendor.regionsOfOperation?.forEach(region => {
          summary.byRegion[region] = (summary.byRegion[region] || 0) + 1;
        });

        // Calculate average rating
        if (vendor.averageRating && vendor.averageRating > 0) {
          totalRating += vendor.averageRating;
          ratedVendors++;
        }
      });

      summary.averageRating = ratedVendors > 0 ? totalRating / ratedVendors : 0;

      return summary;

    } catch (error) {
      console.error('Error getting vendor summary:', error);
      throw error;
    }
  }

  /**
   * Get vendors for select dropdown (optimized)
   */
  async getForSelect(tenantId: string): Promise<{ value: string; label: string }[]> {
    try {
      const vendors = await this.getAll(tenantId, {
        filters: [
          { field: 'status', operator: '==', value: 'Active' }
        ],
        orderBy: { field: 'name', direction: 'asc' }
      });

      return vendors.map(vendor => ({
        value: vendor.id!,
        label: vendor.name
      }));

    } catch (error) {
      console.error('Error getting vendors for select:', error);
      return [];
    }
  }

  /**
   * Sync strategic data duplication for vendor
   */
  private async syncRelatedData(vendor: Vendor): Promise<void> {
    try {
      // Sync vendor name to related collections
      await syncDocumentDuplication({
        sourceCollection: this.collectionName,
        sourceDocumentId: vendor.id!,
        sourceData: { vendorName: vendor.name },
        targetMappings: [
          {
            collection: COLLECTIONS.PURCHASE_ORDERS,
            field: 'vendorName',
            filterField: 'vendorId'
          },
          {
            collection: COLLECTIONS.INVOICES,
            field: 'vendorName',
            filterField: 'vendorId'
          },
          {
            collection: COLLECTIONS.EXHIBITION_TASKS,
            field: 'assignedVendorName',
            filterField: 'assignedVendorId'
          }
        ]
      });
    } catch (error) {
      console.error('Error syncing related data:', error);
      // Don't throw - this is a background operation
    }
  }
}

// Export singleton instance
export const refactoredVendorService = new RefactoredVendorService();
