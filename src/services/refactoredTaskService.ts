/**
 * Refactored Task Service
 * Optimized for flat collection architecture with tenantId and exhibitionId filtering
 * Implements strategic data duplication for performance
 */

import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  Timestamp,
  writeBatch,
  runTransaction,
  type DocumentSnapshot,
  type QueryConstraint
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { 
  validateTenantId, 
  addTenantDocument, 
  getTenantDocument, 
  updateTenantDocument,
  deleteTenantDocument
} from './tenantIdHelperService';
import { syncDocumentDuplication } from './dataDuplicationService';
import type { Task, TenantAwareEntity, TaskStatus } from '@/types/firestore';

// ===== TYPES =====

export interface TaskQueryOptions {
  limit?: number;
  orderBy?: {
    field: keyof Task;
    direction: 'asc' | 'desc';
  };
  startAfter?: DocumentSnapshot;
  filters?: {
    field: keyof Task;
    operator: '==' | '!=' | '<' | '<=' | '>' | '>=' | 'in' | 'not-in' | 'array-contains';
    value: any;
  }[];
}

export interface TaskWithRelatedData extends Task {
  exhibitionName?: string;
  assigneeName?: string;
  timeEntryCount?: number;
  totalTimeSpent?: number;
  dependentTaskCount?: number;
}

export interface TaskSummary {
  total: number;
  byStatus: Record<TaskStatus, number>;
  byPriority: Record<string, number>;
  overdue: number;
  dueToday: number;
  dueThisWeek: number;
}

// ===== REFACTORED TASK SERVICE =====

export class RefactoredTaskService {
  private readonly collectionName = COLLECTIONS.EXHIBITION_TASKS;

  /**
   * Get all tasks for tenant with optimized query
   */
  async getAll(
    tenantId: string, 
    options: TaskQueryOptions = {}
  ): Promise<Task[]> {
    try {
      validateTenantId(tenantId);

      const constraints: QueryConstraint[] = [
        where('tenantId', '==', tenantId)
      ];

      // Add filters
      if (options.filters) {
        options.filters.forEach(filter => {
          constraints.push(where(filter.field as string, filter.operator, filter.value));
        });
      }

      // Add ordering
      if (options.orderBy) {
        constraints.push(orderBy(options.orderBy.field as string, options.orderBy.direction));
      } else {
        // Default ordering by due date
        constraints.push(orderBy('dueDate', 'asc'));
      }

      // Add limit
      if (options.limit) {
        constraints.push(limit(options.limit));
      }

      // Add pagination
      if (options.startAfter) {
        constraints.push(startAfter(options.startAfter));
      }

      const q = query(collection(db, this.collectionName), ...constraints);
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Task));

    } catch (error) {
      console.error('Error getting tasks:', error);
      throw error;
    }
  }

  /**
   * Get task by ID with tenant validation
   */
  async getById(tenantId: string, taskId: string): Promise<Task | null> {
    try {
      validateTenantId(tenantId);
      return await getTenantDocument<Task>(tenantId, this.collectionName, taskId);
    } catch (error) {
      console.error('Error getting task by ID:', error);
      return null;
    }
  }

  /**
   * Create new task with tenant stamping and data duplication
   */
  async create(tenantId: string, taskData: Omit<Task, 'id' | 'tenantId'>): Promise<Task> {
    try {
      validateTenantId(tenantId);

      // Add tenant ID and timestamps
      const taskWithTenant: Omit<Task, 'id'> = {
        ...taskData,
        tenantId,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      // Create task using tenant-aware helper
      const task = await addTenantDocument<Task>(
        tenantId, 
        this.collectionName, 
        taskWithTenant
      );

      // Trigger strategic data duplication if needed
      await this.syncRelatedData(task);

      return task;

    } catch (error) {
      console.error('Error creating task:', error);
      throw error;
    }
  }

  /**
   * Update task with data duplication sync
   */
  async update(
    tenantId: string, 
    taskId: string, 
    updates: Partial<Task>
  ): Promise<void> {
    try {
      validateTenantId(tenantId);

      // Update task
      await updateTenantDocument<Task>(
        tenantId, 
        this.collectionName, 
        taskId, 
        {
          ...updates,
          updatedAt: Timestamp.now()
        }
      );

      // If task title changed, sync duplicated data
      if (updates.title) {
        const task = await this.getById(tenantId, taskId);
        if (task) {
          await this.syncRelatedData(task);
        }
      }

    } catch (error) {
      console.error('Error updating task:', error);
      throw error;
    }
  }

  /**
   * Delete task with cascade operations
   */
  async delete(tenantId: string, taskId: string): Promise<void> {
    try {
      validateTenantId(tenantId);

      // Use transaction for cascade delete
      await runTransaction(db, async (transaction) => {
        // Delete task
        const taskRef = doc(db, this.collectionName, taskId);
        transaction.delete(taskRef);

        // Note: Cloud Functions will handle cascade deletes for related data
        // This includes time entries, dependencies, etc.
      });

    } catch (error) {
      console.error('Error deleting task:', error);
      throw error;
    }
  }

  /**
   * Get tasks by exhibition with performance optimization
   */
  async getByExhibition(tenantId: string, exhibitionId: string): Promise<Task[]> {
    return this.getAll(tenantId, {
      filters: [
        { field: 'parentActivityId', operator: '==', value: exhibitionId },
        { field: 'parentActivityType', operator: '==', value: 'Exhibition' }
      ],
      orderBy: { field: 'dueDate', direction: 'asc' }
    });
  }

  /**
   * Get tasks by assignee
   */
  async getByAssignee(tenantId: string, assigneeId: string): Promise<Task[]> {
    return this.getAll(tenantId, {
      filters: [
        { field: 'assigneeId', operator: '==', value: assigneeId }
      ],
      orderBy: { field: 'dueDate', direction: 'asc' }
    });
  }

  /**
   * Get tasks by status
   */
  async getByStatus(tenantId: string, status: TaskStatus): Promise<Task[]> {
    return this.getAll(tenantId, {
      filters: [
        { field: 'status', operator: '==', value: status }
      ],
      orderBy: { field: 'dueDate', direction: 'asc' }
    });
  }

  /**
   * Get overdue tasks
   */
  async getOverdue(tenantId: string): Promise<Task[]> {
    const now = Timestamp.now();
    return this.getAll(tenantId, {
      filters: [
        { field: 'dueDate', operator: '<', value: now },
        { field: 'status', operator: '!=', value: 'Done' }
      ],
      orderBy: { field: 'dueDate', direction: 'asc' }
    });
  }

  /**
   * Get tasks due today
   */
  async getDueToday(tenantId: string): Promise<Task[]> {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    return this.getAll(tenantId, {
      filters: [
        { field: 'dueDate', operator: '>=', value: Timestamp.fromDate(startOfDay) },
        { field: 'dueDate', operator: '<', value: Timestamp.fromDate(endOfDay) },
        { field: 'status', operator: '!=', value: 'Done' }
      ],
      orderBy: { field: 'dueDate', direction: 'asc' }
    });
  }

  /**
   * Get task summary statistics
   */
  async getSummary(tenantId: string): Promise<TaskSummary> {
    try {
      const tasks = await this.getAll(tenantId);
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

      const summary: TaskSummary = {
        total: tasks.length,
        byStatus: {
          'To Do': 0,
          'In Progress': 0,
          'Awaiting Approval': 0,
          'Done': 0,
          'Blocked': 0,
          'Cancelled': 0
        },
        byPriority: {
          'Low': 0,
          'Medium': 0,
          'High': 0,
          'Urgent': 0
        },
        overdue: 0,
        dueToday: 0,
        dueThisWeek: 0
      };

      tasks.forEach(task => {
        // Count by status
        summary.byStatus[task.status]++;

        // Count by priority
        summary.byPriority[task.priority]++;

        // Count overdue, due today, due this week
        if (task.dueDate) {
          const dueDate = new Date(task.dueDate as string);
          
          if (dueDate < today && task.status !== 'Done') {
            summary.overdue++;
          }
          
          if (dueDate >= today && dueDate < new Date(today.getTime() + 24 * 60 * 60 * 1000) && task.status !== 'Done') {
            summary.dueToday++;
          }
          
          if (dueDate >= today && dueDate < weekFromNow && task.status !== 'Done') {
            summary.dueThisWeek++;
          }
        }
      });

      return summary;

    } catch (error) {
      console.error('Error getting task summary:', error);
      throw error;
    }
  }

  /**
   * Sync strategic data duplication for task
   */
  private async syncRelatedData(task: Task): Promise<void> {
    try {
      // Sync task title to related collections if needed
      // This could include time entries, dependencies, etc.
      // Implementation depends on specific data duplication strategy
    } catch (error) {
      console.error('Error syncing related data:', error);
      // Don't throw - this is a background operation
    }
  }
}

// Export singleton instance
export const refactoredTaskService = new RefactoredTaskService();
