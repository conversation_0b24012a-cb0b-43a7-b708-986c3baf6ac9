import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  getDoc,
  setDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { EvexaModule, MODULE_METADATA } from '@/types/personas';
import { validateTenantId } from './tenantIdHelperService';

export interface SubscriptionPlan {
  id: string;
  name: string;
  displayName: string;
  description: string;
  tier: 'free' | 'starter' | 'professional' | 'enterprise' | 'custom';
  pricing: {
    monthly: number;
    yearly: number;
    currency: string;
    yearlyDiscount: number; // percentage
  };
  stripePriceId?: string;
  stripeYearlyPriceId?: string;
  features: {
    maxExhibitions: number;
    maxEvents: number;
    maxUsers: number;
    maxTasks: number;
    maxLeads: number;
    maxVendors: number;
    storageGB: number;
    apiCallsPerMonth: number;
    emailsPerMonth: number;
    customBranding: boolean;
    advancedAnalytics: boolean;
    prioritySupport: boolean;
    whiteLabeling: boolean;
    apiAccess: boolean;
    customIntegrations: boolean;
    dedicatedAccount: boolean;
    ssoIntegration: boolean;
    auditLogs: boolean;
    dataExport: boolean;
    customReports: boolean;
    workflowAutomation: boolean;
    aiFeatures: boolean;
    mobileApp: boolean;
    offlineSync: boolean;
  };
  limitations: {
    exhibitionDuration: number; // days
    fileUploadSizeMB: number;
    concurrentUsers: number;
    dataRetentionDays: number;
    supportResponseTime: string;
    backupFrequency: string;
  };
  addOns: Array<{
    id: string;
    name: string;
    description: string;
    price: number;
    unit: string;
  }>;
  isActive: boolean;
  isPopular: boolean;
  metadata: {
    createdAt: Date;
    updatedAt: Date;
  };
}

export interface TenantSubscription {
  id?: string;
  tenantId: string;
  planId: string;
  status: 'active' | 'trialing' | 'past_due' | 'canceled' | 'unpaid' | 'paused';
  billingCycle: 'monthly' | 'yearly';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  trialStart?: Date;
  trialEnd?: Date;
  canceledAt?: Date;
  cancelAtPeriodEnd: boolean;
  billing: {
    customerId?: string;
    subscriptionId?: string;
    paymentMethodId?: string;
    lastPaymentDate?: Date;
    nextPaymentDate?: Date;
    amountDue: number;
    currency: string;
  };
  usage: {
    exhibitions: number;
    events: number;
    users: number;
    tasks: number;
    leads: number;
    vendors: number;
    storageUsedGB: number;
    apiCallsThisMonth: number;
    emailsSentThisMonth: number;
    lastUsageUpdate: Date;
  };
  addOns: Array<{
    addOnId: string;
    quantity: number;
    price: number;
  }>;
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    lastModifiedBy: string;
  };
}

export interface UsageEvent {
  id?: string;
  tenantId: string;
  eventType: 'exhibition_created' | 'event_created' | 'user_added' | 'task_created' | 'lead_added' | 'vendor_added' | 'api_call' | 'email_sent' | 'storage_used';
  resourceId: string;
  quantity: number;
  metadata: Record<string, any>;
  timestamp: Date;
}

export interface BillingInvoice {
  id?: string;
  tenantId: string;
  subscriptionId: string;
  invoiceNumber: string;
  status: 'draft' | 'open' | 'paid' | 'void' | 'uncollectible';
  amount: number;
  currency: string;
  dueDate: Date;
  paidAt?: Date;
  lineItems: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    amount: number;
  }>;
  metadata: {
    createdAt: Date;
    updatedAt: Date;
  };
}

export class SubscriptionService {
  private plansCollection = 'subscription_plans';
  private subscriptionsCollection = 'tenant_subscriptions';
  private usageEventsCollection = 'usage_events';
  private invoicesCollection = 'billing_invoices';

  // Default subscription plans
  private defaultPlans: SubscriptionPlan[] = [
    {
      id: 'free',
      name: 'free',
      displayName: 'Free',
      description: 'Perfect for getting started with basic exhibition management',
      tier: 'free',
      pricing: {
        monthly: 0,
        yearly: 0,
        currency: 'USD',
        yearlyDiscount: 0
      },
      features: {
        maxExhibitions: 2,
        maxEvents: 5,
        maxUsers: 3,
        maxTasks: 50,
        maxLeads: 100,
        maxVendors: 10,
        storageGB: 1,
        apiCallsPerMonth: 1000,
        emailsPerMonth: 100,
        customBranding: false,
        advancedAnalytics: false,
        prioritySupport: false,
        whiteLabeling: false,
        apiAccess: false,
        customIntegrations: false,
        dedicatedAccount: false,
        ssoIntegration: false,
        auditLogs: false,
        dataExport: true,
        customReports: false,
        workflowAutomation: false,
        aiFeatures: false,
        mobileApp: true,
        offlineSync: false
      },
      limitations: {
        exhibitionDuration: 30,
        fileUploadSizeMB: 10,
        concurrentUsers: 3,
        dataRetentionDays: 90,
        supportResponseTime: '48 hours',
        backupFrequency: 'weekly'
      },
      addOns: [],
      isActive: true,
      isPopular: false,
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date()
      }
    },
    {
      id: 'starter',
      name: 'starter',
      displayName: 'Starter',
      description: 'Ideal for small teams and growing exhibition businesses',
      tier: 'starter',
      pricing: {
        monthly: 49,
        yearly: 490,
        currency: 'USD',
        yearlyDiscount: 17
      },
      stripePriceId: process.env.STRIPE_STARTER_MONTHLY_PRICE_ID || 'price_starter_monthly',
      stripeYearlyPriceId: process.env.STRIPE_STARTER_YEARLY_PRICE_ID || 'price_starter_yearly',
      features: {
        maxExhibitions: 10,
        maxEvents: 25,
        maxUsers: 10,
        maxTasks: 500,
        maxLeads: 1000,
        maxVendors: 50,
        storageGB: 10,
        apiCallsPerMonth: 10000,
        emailsPerMonth: 1000,
        customBranding: true,
        advancedAnalytics: true,
        prioritySupport: false,
        whiteLabeling: false,
        apiAccess: true,
        customIntegrations: false,
        dedicatedAccount: false,
        ssoIntegration: false,
        auditLogs: false,
        dataExport: true,
        customReports: true,
        workflowAutomation: true,
        aiFeatures: true,
        mobileApp: true,
        offlineSync: true
      },
      limitations: {
        exhibitionDuration: 90,
        fileUploadSizeMB: 50,
        concurrentUsers: 10,
        dataRetentionDays: 365,
        supportResponseTime: '24 hours',
        backupFrequency: 'daily'
      },
      addOns: [
        {
          id: 'extra_users',
          name: 'Additional Users',
          description: 'Add more team members',
          price: 5,
          unit: 'per user/month'
        },
        {
          id: 'extra_storage',
          name: 'Additional Storage',
          description: 'Increase storage capacity',
          price: 2,
          unit: 'per GB/month'
        }
      ],
      isActive: true,
      isPopular: true,
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date()
      }
    },
    {
      id: 'professional',
      name: 'professional',
      displayName: 'Professional',
      description: 'Advanced features for established exhibition companies',
      tier: 'professional',
      pricing: {
        monthly: 149,
        yearly: 1490,
        currency: 'USD',
        yearlyDiscount: 17
      },
      stripePriceId: process.env.STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID || 'price_professional_monthly',
      stripeYearlyPriceId: process.env.STRIPE_PROFESSIONAL_YEARLY_PRICE_ID || 'price_professional_yearly',
      features: {
        maxExhibitions: 50,
        maxEvents: 100,
        maxUsers: 50,
        maxTasks: 5000,
        maxLeads: 10000,
        maxVendors: 200,
        storageGB: 100,
        apiCallsPerMonth: 100000,
        emailsPerMonth: 10000,
        customBranding: true,
        advancedAnalytics: true,
        prioritySupport: true,
        whiteLabeling: true,
        apiAccess: true,
        customIntegrations: true,
        dedicatedAccount: false,
        ssoIntegration: true,
        auditLogs: true,
        dataExport: true,
        customReports: true,
        workflowAutomation: true,
        aiFeatures: true,
        mobileApp: true,
        offlineSync: true
      },
      limitations: {
        exhibitionDuration: 365,
        fileUploadSizeMB: 200,
        concurrentUsers: 50,
        dataRetentionDays: 1095,
        supportResponseTime: '4 hours',
        backupFrequency: 'daily'
      },
      addOns: [
        {
          id: 'extra_users',
          name: 'Additional Users',
          description: 'Add more team members',
          price: 4,
          unit: 'per user/month'
        },
        {
          id: 'extra_storage',
          name: 'Additional Storage',
          description: 'Increase storage capacity',
          price: 1.5,
          unit: 'per GB/month'
        },
        {
          id: 'premium_support',
          name: 'Premium Support',
          description: '1-hour response time',
          price: 99,
          unit: 'per month'
        }
      ],
      isActive: true,
      isPopular: false,
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date()
      }
    },
    {
      id: 'enterprise',
      name: 'enterprise',
      displayName: 'Enterprise',
      description: 'Complete solution for large exhibition organizations',
      tier: 'enterprise',
      pricing: {
        monthly: 499,
        yearly: 4990,
        currency: 'USD',
        yearlyDiscount: 17
      },
      stripePriceId: process.env.STRIPE_ENTERPRISE_MONTHLY_PRICE_ID || 'price_enterprise_monthly',
      stripeYearlyPriceId: process.env.STRIPE_ENTERPRISE_YEARLY_PRICE_ID || 'price_enterprise_yearly',
      features: {
        maxExhibitions: -1, // unlimited
        maxEvents: -1,
        maxUsers: -1,
        maxTasks: -1,
        maxLeads: -1,
        maxVendors: -1,
        storageGB: 1000,
        apiCallsPerMonth: 1000000,
        emailsPerMonth: 100000,
        customBranding: true,
        advancedAnalytics: true,
        prioritySupport: true,
        whiteLabeling: true,
        apiAccess: true,
        customIntegrations: true,
        dedicatedAccount: true,
        ssoIntegration: true,
        auditLogs: true,
        dataExport: true,
        customReports: true,
        workflowAutomation: true,
        aiFeatures: true,
        mobileApp: true,
        offlineSync: true
      },
      limitations: {
        exhibitionDuration: -1, // unlimited
        fileUploadSizeMB: 1000,
        concurrentUsers: -1,
        dataRetentionDays: -1,
        supportResponseTime: '1 hour',
        backupFrequency: 'real-time'
      },
      addOns: [
        {
          id: 'extra_storage',
          name: 'Additional Storage',
          description: 'Increase storage capacity',
          price: 1,
          unit: 'per GB/month'
        },
        {
          id: 'dedicated_support',
          name: 'Dedicated Support Manager',
          description: 'Personal support manager',
          price: 500,
          unit: 'per month'
        },
        {
          id: 'custom_development',
          name: 'Custom Development',
          description: 'Custom feature development',
          price: 200,
          unit: 'per hour'
        }
      ],
      isActive: true,
      isPopular: false,
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date()
      }
    }
  ];

  // Plan Management
  async getAllPlans(): Promise<SubscriptionPlan[]> {
    try {
      const q = query(
        collection(db, this.plansCollection),
        where('isActive', '==', true),
        orderBy('pricing.monthly', 'asc')
      );

      const querySnapshot = await getDocs(q);
      if (querySnapshot.empty) {
        // Initialize default plans if none exist
        await this.initializeDefaultPlans();
        // Return fresh query after initialization
        const newQuerySnapshot = await getDocs(q);
        return newQuerySnapshot.docs.map(docSnapshot => {
          const data = docSnapshot.data();
          return {
            ...data,
            id: docSnapshot.id,
            metadata: {
              createdAt: data.metadata.createdAt.toDate(),
              updatedAt: data.metadata.updatedAt.toDate()
            }
          } as SubscriptionPlan;
        });
      }

      return querySnapshot.docs.map(docSnapshot => {
        const data = docSnapshot.data();
        return {
          ...data,
          id: docSnapshot.id, // Ensure the ID matches the document ID
          metadata: {
            createdAt: data.metadata.createdAt.toDate(),
            updatedAt: data.metadata.updatedAt.toDate()
          }
        } as SubscriptionPlan;
      });
    } catch (error) {
      console.error('Failed to get plans:', error);
      // Only return default plans if there's a real error, not empty collection
      return [];
    }
  }

  async getPlan(planId: string): Promise<SubscriptionPlan | null> {
    try {
      const docRef = doc(db, this.plansCollection, planId);
      const docSnap = await getDoc(docRef);
      
      if (!docSnap.exists()) {
        return this.defaultPlans.find(p => p.id === planId) || null;
      }
      
      const data = docSnap.data();
      return {
        ...data,
        id: docSnap.id, // Ensure the ID matches the document ID
        metadata: {
          createdAt: data.metadata.createdAt.toDate(),
          updatedAt: data.metadata.updatedAt.toDate()
        }
      } as SubscriptionPlan;
    } catch (error) {
      console.error('Failed to get plan:', error);
      return this.defaultPlans.find(p => p.id === planId) || null;
    }
  }

  private async initializeDefaultPlans(): Promise<void> {
    try {
      for (const plan of this.defaultPlans) {
        // Check if plan document already exists using document ID
        const docRef = doc(db, this.plansCollection, plan.id);
        const docSnap = await getDoc(docRef);

        if (!docSnap.exists()) {
          // Use the plan ID as the document ID to prevent duplicates
          await setDoc(docRef, {
            ...plan,
            'metadata.createdAt': Timestamp.fromDate(plan.metadata.createdAt),
            'metadata.updatedAt': Timestamp.fromDate(plan.metadata.updatedAt)
          });
          console.log(`Initialized plan: ${plan.displayName}`);
        } else {
          console.log(`Plan already exists: ${plan.displayName}`);
        }
      }
    } catch (error) {
      console.error('Failed to initialize default plans:', error);
    }
  }

  // Subscription Management
  async createSubscription(subscription: Omit<TenantSubscription, 'id' | 'metadata'>): Promise<TenantSubscription> {
    const now = new Date();
    const subscriptionData: Omit<TenantSubscription, 'id'> = {
      ...subscription,
      metadata: {
        createdAt: now,
        updatedAt: now,
        createdBy: 'system',
        lastModifiedBy: 'system'
      }
    };

    const docRef = await addDoc(collection(db, this.subscriptionsCollection), {
      ...subscriptionData,
      currentPeriodStart: Timestamp.fromDate(subscriptionData.currentPeriodStart),
      currentPeriodEnd: Timestamp.fromDate(subscriptionData.currentPeriodEnd),
      trialStart: subscriptionData.trialStart ? Timestamp.fromDate(subscriptionData.trialStart) : null,
      trialEnd: subscriptionData.trialEnd ? Timestamp.fromDate(subscriptionData.trialEnd) : null,
      canceledAt: subscriptionData.canceledAt ? Timestamp.fromDate(subscriptionData.canceledAt) : null,
      'billing.lastPaymentDate': subscriptionData.billing.lastPaymentDate ? Timestamp.fromDate(subscriptionData.billing.lastPaymentDate) : null,
      'billing.nextPaymentDate': subscriptionData.billing.nextPaymentDate ? Timestamp.fromDate(subscriptionData.billing.nextPaymentDate) : null,
      'usage.lastUsageUpdate': Timestamp.fromDate(subscriptionData.usage.lastUsageUpdate),
      'metadata.createdAt': Timestamp.fromDate(now),
      'metadata.updatedAt': Timestamp.fromDate(now)
    });

    return { ...subscriptionData, id: docRef.id };
  }

  async getTenantSubscription(tenantId: string): Promise<TenantSubscription | null> {
    try {
      const q = query(
        collection(db, this.subscriptionsCollection),
        where('tenantId', '==', tenantId),
        where('status', 'in', ['active', 'trialing', 'past_due']),
        limit(1)
      );
      
      const querySnapshot = await getDocs(q);
      if (querySnapshot.empty) return null;
      
      const doc = querySnapshot.docs[0];
      const data = doc.data();
      
      return {
        id: doc.id,
        ...data,
        currentPeriodStart: data.currentPeriodStart.toDate(),
        currentPeriodEnd: data.currentPeriodEnd.toDate(),
        trialStart: data.trialStart?.toDate(),
        trialEnd: data.trialEnd?.toDate(),
        canceledAt: data.canceledAt?.toDate(),
        billing: {
          ...data.billing,
          lastPaymentDate: data.billing.lastPaymentDate?.toDate(),
          nextPaymentDate: data.billing.nextPaymentDate?.toDate()
        },
        usage: {
          ...data.usage,
          lastUsageUpdate: data.usage.lastUsageUpdate.toDate()
        },
        metadata: {
          ...data.metadata,
          createdAt: data.metadata.createdAt.toDate(),
          updatedAt: data.metadata.updatedAt.toDate()
        }
      } as TenantSubscription;
    } catch (error) {
      console.error('Failed to get tenant subscription:', error);
      return null;
    }
  }

  // Usage Tracking
  async trackUsage(tenantId: string, eventType: UsageEvent['eventType'], resourceId: string, quantity: number = 1, metadata: Record<string, any> = {}): Promise<void> {
    try {
      // Record usage event
      await addDoc(collection(db, this.usageEventsCollection), {
        tenantId,
        eventType,
        resourceId,
        quantity,
        metadata,
        timestamp: Timestamp.fromDate(new Date())
      });

      // Update subscription usage
      await this.updateSubscriptionUsage(tenantId, eventType, quantity);
    } catch (error) {
      console.error('Failed to track usage:', error);
    }
  }

  private async updateSubscriptionUsage(tenantId: string, eventType: UsageEvent['eventType'], quantity: number): Promise<void> {
    const subscription = await this.getTenantSubscription(tenantId);
    if (!subscription) return;

    const usageField = this.getUsageFieldForEventType(eventType);
    if (!usageField) return;

    const updateData: any = {
      [`usage.${usageField}`]: subscription.usage[usageField as keyof typeof subscription.usage] + quantity,
      'usage.lastUsageUpdate': Timestamp.fromDate(new Date()),
      'metadata.updatedAt': Timestamp.fromDate(new Date())
    };

    const docRef = doc(db, this.subscriptionsCollection, subscription.id!);
    await updateDoc(docRef, updateData);
  }

  private getUsageFieldForEventType(eventType: UsageEvent['eventType']): string | null {
    const mapping: Record<UsageEvent['eventType'], string> = {
      'exhibition_created': 'exhibitions',
      'event_created': 'events',
      'user_added': 'users',
      'task_created': 'tasks',
      'lead_added': 'leads',
      'vendor_added': 'vendors',
      'api_call': 'apiCallsThisMonth',
      'email_sent': 'emailsSentThisMonth',
      'storage_used': 'storageUsedGB'
    };
    
    return mapping[eventType] || null;
  }

  // Feature Access Control
  async checkFeatureAccess(tenantId: string, feature: keyof SubscriptionPlan['features']): Promise<boolean> {
    try {
      const subscription = await this.getTenantSubscription(tenantId);
      if (!subscription) return false;

      const plan = await this.getPlan(subscription.planId);
      if (!plan) return false;

      return plan.features[feature];
    } catch (error) {
      console.error('Failed to check feature access:', error);
      return false;
    }
  }

  async checkUsageLimit(tenantId: string, limitType: keyof SubscriptionPlan['features']): Promise<{ allowed: boolean; current: number; limit: number }> {
    try {
      const subscription = await this.getTenantSubscription(tenantId);
      if (!subscription) return { allowed: false, current: 0, limit: 0 };

      const plan = await this.getPlan(subscription.planId);
      if (!plan) return { allowed: false, current: 0, limit: 0 };

      const limit = plan.features[limitType] as number;
      const current = this.getCurrentUsageForLimit(subscription, limitType);

      return {
        allowed: limit === -1 || current < limit,
        current,
        limit
      };
    } catch (error) {
      console.error('Failed to check usage limit:', error);
      return { allowed: false, current: 0, limit: 0 };
    }
  }

  private getCurrentUsageForLimit(subscription: TenantSubscription, limitType: keyof SubscriptionPlan['features']): number {
    const usageMapping: Record<string, keyof TenantSubscription['usage']> = {
      'maxExhibitions': 'exhibitions',
      'maxEvents': 'events',
      'maxUsers': 'users',
      'maxTasks': 'tasks',
      'maxLeads': 'leads',
      'maxVendors': 'vendors',
      'storageGB': 'storageUsedGB',
      'apiCallsPerMonth': 'apiCallsThisMonth',
      'emailsPerMonth': 'emailsSentThisMonth'
    };

    const usageField = usageMapping[limitType as string];
    return usageField ? subscription.usage[usageField] : 0;
  }

  // Additional methods for billing integration
  async updateSubscription(subscriptionId: string, updates: Partial<TenantSubscription>): Promise<void> {
    try {
      const updateData: any = {
        ...updates,
        'metadata.updatedAt': Timestamp.fromDate(new Date())
      };

      // Convert Date fields to Timestamps
      if (updates.currentPeriodStart) {
        updateData.currentPeriodStart = Timestamp.fromDate(updates.currentPeriodStart);
      }
      if (updates.currentPeriodEnd) {
        updateData.currentPeriodEnd = Timestamp.fromDate(updates.currentPeriodEnd);
      }
      if (updates.canceledAt) {
        updateData.canceledAt = Timestamp.fromDate(updates.canceledAt);
      }

      const docRef = doc(db, this.subscriptionsCollection, subscriptionId);
      await updateDoc(docRef, updateData);
    } catch (error) {
      console.error('Failed to update subscription:', error);
      throw error;
    }
  }

  async getAvailablePlans(): Promise<SubscriptionPlan[]> {
    return this.getAllPlans();
  }

  async getPlanById(planId: string): Promise<SubscriptionPlan | null> {
    return this.getPlan(planId);
  }

  // Plan CRUD Operations
  async createPlan(planData: Omit<SubscriptionPlan, 'id' | 'metadata'>): Promise<SubscriptionPlan> {
    try {
      const now = new Date();
      const plan: Omit<SubscriptionPlan, 'id'> = {
        ...planData,
        metadata: {
          createdAt: now,
          updatedAt: now
        }
      };

      // Use the plan name as document ID to prevent duplicates
      const docRef = doc(db, this.plansCollection, planData.name);
      await setDoc(docRef, {
        ...plan,
        'metadata.createdAt': Timestamp.fromDate(now),
        'metadata.updatedAt': Timestamp.fromDate(now)
      });

      return {
        ...plan,
        id: planData.name
      };
    } catch (error) {
      console.error('Failed to create plan:', error);
      throw error;
    }
  }

  async updatePlan(planId: string, updates: Partial<Omit<SubscriptionPlan, 'id' | 'metadata'>>): Promise<SubscriptionPlan> {
    try {
      const docRef = doc(db, this.plansCollection, planId);
      const now = new Date();

      await updateDoc(docRef, {
        ...updates,
        'metadata.updatedAt': Timestamp.fromDate(now)
      });

      // Return updated plan
      const updatedPlan = await this.getPlan(planId);
      if (!updatedPlan) {
        throw new Error('Plan not found after update');
      }

      return updatedPlan;
    } catch (error) {
      console.error('Failed to update plan:', error);
      throw error;
    }
  }

  async deletePlan(planId: string): Promise<void> {
    try {
      // Don't actually delete, just mark as inactive
      await this.updatePlan(planId, { isActive: false });
    } catch (error) {
      console.error('Failed to delete plan:', error);
      throw error;
    }
  }

  async duplicatePlan(planId: string, newPlanData: { name: string; displayName: string }): Promise<SubscriptionPlan> {
    try {
      const originalPlan = await this.getPlan(planId);
      if (!originalPlan) {
        throw new Error('Original plan not found');
      }

      const duplicatedPlan = await this.createPlan({
        ...originalPlan,
        name: newPlanData.name,
        displayName: newPlanData.displayName,
        id: newPlanData.name,
        isPopular: false // Reset popular flag for duplicated plans
      });

      return duplicatedPlan;
    } catch (error) {
      console.error('Failed to duplicate plan:', error);
      throw error;
    }
  }

  // Utility function to clean up duplicate plans
  async cleanupDuplicatePlans(): Promise<void> {
    try {
      const q = query(collection(db, this.plansCollection));
      const querySnapshot = await getDocs(q);

      const planGroups: { [key: string]: any[] } = {};

      // Group plans by their name/id
      querySnapshot.docs.forEach(doc => {
        const data = doc.data();
        const planName = data.name || data.id;
        if (!planGroups[planName]) {
          planGroups[planName] = [];
        }
        planGroups[planName].push({ docId: doc.id, data });
      });

      // Remove duplicates, keeping the most recent one
      for (const [planName, plans] of Object.entries(planGroups)) {
        if (plans.length > 1) {
          console.log(`Found ${plans.length} duplicates for plan: ${planName}`);

          // Sort by creation date, keep the most recent
          plans.sort((a, b) => {
            const aDate = a.data.metadata?.createdAt?.toDate() || new Date(0);
            const bDate = b.data.metadata?.createdAt?.toDate() || new Date(0);
            return bDate.getTime() - aDate.getTime();
          });

          // Delete all but the first (most recent)
          for (let i = 1; i < plans.length; i++) {
            await deleteDoc(doc(db, this.plansCollection, plans[i].docId));
            console.log(`Deleted duplicate plan document: ${plans[i].docId}`);
          }
        }
      }
    } catch (error) {
      console.error('Failed to cleanup duplicate plans:', error);
    }
  }

  async getAllActiveSubscriptions(): Promise<TenantSubscription[]> {
    try {
      const q = query(
        collection(db, this.subscriptionsCollection),
        where('status', 'in', ['active', 'trialing'])
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          currentPeriodStart: data.currentPeriodStart.toDate(),
          currentPeriodEnd: data.currentPeriodEnd.toDate(),
          trialStart: data.trialStart ? data.trialStart.toDate() : undefined,
          trialEnd: data.trialEnd ? data.trialEnd.toDate() : undefined,
          canceledAt: data.canceledAt ? data.canceledAt.toDate() : undefined,
          billing: {
            ...data.billing,
            lastPaymentDate: data.billing.lastPaymentDate ? data.billing.lastPaymentDate.toDate() : undefined,
            nextPaymentDate: data.billing.nextPaymentDate ? data.billing.nextPaymentDate.toDate() : undefined
          },
          usage: {
            ...data.usage,
            lastUsageUpdate: data.usage.lastUsageUpdate.toDate()
          },
          metadata: {
            createdAt: data.metadata.createdAt.toDate(),
            updatedAt: data.metadata.updatedAt.toDate(),
            createdBy: data.metadata.createdBy,
            lastModifiedBy: data.metadata.lastModifiedBy
          }
        };
      }) as TenantSubscription[];
    } catch (error) {
      console.error('Failed to get active subscriptions:', error);
      return [];
    }
  }

  async getTenantUsage(tenantId: string): Promise<TenantSubscription['usage'] | null> {
    try {
      const subscription = await this.getTenantSubscription(tenantId);
      return subscription?.usage || null;
    } catch (error) {
      console.error('Failed to get tenant usage:', error);
      return null;
    }
  }

  async getTenantInvoices(tenantId: string): Promise<BillingInvoice[]> {
    try {
      const q = query(
        collection(db, this.invoicesCollection),
        where('tenantId', '==', tenantId),
        orderBy('metadata.createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          dueDate: data.dueDate.toDate(),
          paidAt: data.paidAt ? data.paidAt.toDate() : undefined,
          metadata: {
            createdAt: data.metadata.createdAt.toDate(),
            updatedAt: data.metadata.updatedAt.toDate()
          }
        };
      }) as BillingInvoice[];
    } catch (error) {
      console.error('Failed to get tenant invoices:', error);
      return [];
    }
  }

  async createInvoice(invoice: Omit<BillingInvoice, 'id' | 'metadata'>): Promise<BillingInvoice> {
    const now = new Date();
    const invoiceData: Omit<BillingInvoice, 'id'> = {
      ...invoice,
      metadata: {
        createdAt: now,
        updatedAt: now
      }
    };

    const docRef = await addDoc(collection(db, this.invoicesCollection), {
      ...invoiceData,
      dueDate: Timestamp.fromDate(invoiceData.dueDate),
      paidAt: invoiceData.paidAt ? Timestamp.fromDate(invoiceData.paidAt) : null,
      'metadata.createdAt': Timestamp.fromDate(now),
      'metadata.updatedAt': Timestamp.fromDate(now)
    });

    return { ...invoiceData, id: docRef.id };
  }

  // ===== PERSONA-SUBSCRIPTION INTEGRATION =====

  /**
   * Check if current user is super admin
   */
  private isSuperAdmin(): boolean {
    try {
      const { SUPER_ADMIN_USER_IDS } = require('@/services/superAdminService');
      const { auth } = require('@/lib/firebase');
      return auth.currentUser && SUPER_ADMIN_USER_IDS.includes(auth.currentUser.uid);
    } catch {
      return false;
    }
  }

  /**
   * Check if tenant has access to a specific module based on subscription
   */
  async hasModuleAccess(module: EvexaModule): Promise<boolean> {
    try {
      // SUPER ADMIN BYPASS: Super admin has access to everything
      if (this.isSuperAdmin()) {
        return true;
      }

      const subscription = await this.getCurrentSubscription();
      if (!subscription) {
        return false; // No subscription = no access
      }

      const moduleMetadata = MODULE_METADATA[module];
      if (!moduleMetadata) {
        return false; // Invalid module
      }

      // Check if module requires premium and subscription supports it
      if (moduleMetadata.isPremium) {
        const plan = await this.getSubscriptionPlan(subscription.planId);
        if (!plan) return false;

        // Check if plan tier meets minimum requirement
        const tierHierarchy = { 'free': 0, 'starter': 1, 'professional': 2, 'enterprise': 3, 'custom': 4 };
        const currentTierLevel = tierHierarchy[plan.tier] || 0;
        const requiredTierLevel = tierHierarchy[moduleMetadata.minimumPlan] || 0;

        return currentTierLevel >= requiredTierLevel;
      }

      return true; // Non-premium modules are available to all subscriptions
    } catch (error) {
      console.error('Error checking module access:', error);
      return false;
    }
  }

  /**
   * Get accessible modules for current subscription
   */
  async getAccessibleModules(): Promise<EvexaModule[]> {
    const accessibleModules: EvexaModule[] = [];

    for (const moduleInfo of Object.values(MODULE_METADATA)) {
      const hasAccess = await this.hasModuleAccess(moduleInfo.id);
      if (hasAccess) {
        accessibleModules.push(moduleInfo.id);
      }
    }

    return accessibleModules;
  }

  /**
   * Check subscription limits for persona assignments
   */
  async checkPersonaAssignmentLimits(requestedUsers: number = 1): Promise<{
    allowed: boolean;
    reason?: string;
    currentUsers: number;
    maxUsers: number;
  }> {
    try {
      const subscription = await this.getCurrentSubscription();
      if (!subscription) {
        return {
          allowed: false,
          reason: 'No active subscription found',
          currentUsers: 0,
          maxUsers: 0
        };
      }

      const plan = await this.getSubscriptionPlan(subscription.planId);
      if (!plan) {
        return {
          allowed: false,
          reason: 'Invalid subscription plan',
          currentUsers: 0,
          maxUsers: 0
        };
      }

      const currentUsers = subscription.usage?.users || 0;
      const maxUsers = plan.features.maxUsers;

      if (currentUsers + requestedUsers > maxUsers) {
        return {
          allowed: false,
          reason: `User limit would be exceeded. Current: ${currentUsers}, Max: ${maxUsers}, Requested: ${requestedUsers}`,
          currentUsers,
          maxUsers
        };
      }

      return {
        allowed: true,
        currentUsers,
        maxUsers
      };
    } catch (error) {
      console.error('Error checking persona assignment limits:', error);
      return {
        allowed: false,
        reason: 'Error checking subscription limits',
        currentUsers: 0,
        maxUsers: 0
      };
    }
  }

  /**
   * Enforce subscription-based feature access
   */
  async enforceFeatureAccess(feature: keyof SubscriptionPlan['features']): Promise<{
    allowed: boolean;
    reason?: string;
    upgradeRequired?: string;
  }> {
    try {
      // SUPER ADMIN BYPASS: Super admin has access to everything
      if (this.isSuperAdmin()) {
        return { allowed: true, reason: 'Super admin access' };
      }

      const subscription = await this.getCurrentSubscription();
      if (!subscription) {
        return {
          allowed: false,
          reason: 'No active subscription',
          upgradeRequired: 'starter'
        };
      }

      const plan = await this.getSubscriptionPlan(subscription.planId);
      if (!plan) {
        return {
          allowed: false,
          reason: 'Invalid subscription plan',
          upgradeRequired: 'starter'
        };
      }

      const hasFeature = plan.features[feature];

      if (typeof hasFeature === 'boolean') {
        return {
          allowed: hasFeature,
          reason: hasFeature ? undefined : `Feature not available in ${plan.displayName} plan`,
          upgradeRequired: hasFeature ? undefined : this.getMinimumPlanForFeature(feature)
        };
      }

      // For numeric features, assume access if value > 0
      return {
        allowed: (hasFeature as number) > 0,
        reason: (hasFeature as number) > 0 ? undefined : `Feature not available in ${plan.displayName} plan`,
        upgradeRequired: (hasFeature as number) > 0 ? undefined : this.getMinimumPlanForFeature(feature)
      };
    } catch (error) {
      console.error('Error enforcing feature access:', error);
      return {
        allowed: false,
        reason: 'Error checking feature access'
      };
    }
  }

  /**
   * Get minimum plan required for a feature
   */
  private getMinimumPlanForFeature(feature: keyof SubscriptionPlan['features']): string {
    // This would typically be configured based on your pricing strategy
    const featurePlanMapping: Record<string, string> = {
      'advancedAnalytics': 'professional',
      'customBranding': 'professional',
      'apiAccess': 'professional',
      'prioritySupport': 'professional',
      'whiteLabeling': 'enterprise',
      'customIntegrations': 'enterprise',
      'dedicatedAccount': 'enterprise',
      'ssoIntegration': 'enterprise',
      'auditLogs': 'enterprise'
    };

    return featurePlanMapping[feature] || 'professional';
  }

  /**
   * Update subscription usage for persona assignments
   */
  async updatePersonaUsage(userCount: number): Promise<void> {
    try {
      const subscription = await this.getCurrentSubscription();
      if (!subscription) {
        return;
      }

      await this.updateUsage({
        users: userCount
      });
    } catch (error) {
      console.error('Error updating persona usage:', error);
    }
  }
}

export const subscriptionService = new SubscriptionService();
export default subscriptionService;
