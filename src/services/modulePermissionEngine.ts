/**
 * Module Permission Engine
 * Enhanced permission checking system with caching, bulk operations, and real-time updates
 */

import { 
  EvexaModule, 
  PermissionAction, 
  TenantPersona, 
  ModuleAccess,
  MODULE_METADATA,
  PersonaAssignment 
} from '@/types/personas';
import { createTenantPersonaService } from './tenantPersonaService';
import { createPermissionCheckingService } from './permissionCheckingService';

// ===== TYPES =====

export interface PermissionContext {
  userId: string;
  tenantId: string;
  persona?: TenantPersona;
  subscriptionTier?: 'basic' | 'professional' | 'enterprise';
  customOverrides?: ModuleOverride[];
}

export interface ModuleOverride {
  module: EvexaModule;
  actions: PermissionAction[];
  reason: string;
  expiresAt?: Date;
  grantedBy: string;
}

export interface PermissionCheckOptions {
  useCache?: boolean;
  includeReason?: boolean;
  checkSubscription?: boolean;
  throwOnDenied?: boolean;
}

export interface BulkPermissionResult {
  userId: string;
  results: Array<{
    module: EvexaModule;
    action: PermissionAction;
    hasPermission: boolean;
    reason?: string;
    metadata?: any;
  }>;
  overallAccess: boolean;
}

export interface PermissionRule {
  id: string;
  name: string;
  description: string;
  condition: (context: PermissionContext) => boolean;
  modules: EvexaModule[];
  actions: PermissionAction[];
  priority: number;
}

// ===== PERMISSION CACHE =====

class PermissionCache {
  private cache = new Map<string, { result: any; timestamp: number; ttl: number }>();
  private readonly defaultTTL = 5 * 60 * 1000; // 5 minutes

  set(key: string, value: any, ttl: number = this.defaultTTL): void {
    this.cache.set(key, {
      result: value,
      timestamp: Date.now(),
      ttl
    });
  }

  get(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.result;
  }

  invalidate(pattern?: string): void {
    if (!pattern) {
      this.cache.clear();
      return;
    }

    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }

  getStats(): { size: number; hitRate: number } {
    return {
      size: this.cache.size,
      hitRate: 0.85 // Placeholder - would track actual hits/misses
    };
  }
}

// ===== MODULE PERMISSION ENGINE =====

export class ModulePermissionEngine {
  private cache = new PermissionCache();
  private rules: PermissionRule[] = [];
  private personaService: any;
  private permissionService: any;

  constructor(private tenantId: string) {
    this.personaService = createTenantPersonaService(tenantId);
    this.permissionService = createPermissionCheckingService(tenantId);
    this.initializeDefaultRules();
  }

  // ===== CORE PERMISSION CHECKING =====

  /**
   * Check if user has permission for specific module and action
   */
  async checkPermission(
    context: PermissionContext,
    module: EvexaModule,
    action: PermissionAction,
    options: PermissionCheckOptions = {}
  ): Promise<boolean> {
    const cacheKey = `${context.userId}:${module}:${action}`;
    
    // Check cache first
    if (options.useCache !== false) {
      const cached = this.cache.get(cacheKey);
      if (cached !== null) return cached;
    }

    const result = await this.performPermissionCheck(context, module, action, options);
    
    // Cache the result
    if (options.useCache !== false) {
      this.cache.set(cacheKey, result);
    }

    if (options.throwOnDenied && !result) {
      throw new Error(`Access denied: ${module}:${action}`);
    }

    return result;
  }

  /**
   * Check multiple permissions at once
   */
  async checkBulkPermissions(
    context: PermissionContext,
    permissions: Array<{ module: EvexaModule; action: PermissionAction }>,
    options: PermissionCheckOptions = {}
  ): Promise<BulkPermissionResult> {
    const results = await Promise.all(
      permissions.map(async ({ module, action }) => {
        const hasPermission = await this.checkPermission(context, module, action, options);
        return {
          module,
          action,
          hasPermission,
          reason: options.includeReason ? await this.getPermissionReason(context, module, action) : undefined
        };
      })
    );

    return {
      userId: context.userId,
      results,
      overallAccess: results.every(r => r.hasPermission)
    };
  }

  /**
   * Get all accessible modules for user
   */
  async getUserModuleAccess(context: PermissionContext): Promise<ModuleAccess[]> {
    const moduleAccess: ModuleAccess[] = [];

    for (const [moduleId, metadata] of Object.entries(MODULE_METADATA)) {
      const module = moduleId as EvexaModule;
      const permissions: PermissionAction[] = [];

      // Check each permission level
      for (const action of ['read', 'write', 'delete', 'admin'] as PermissionAction[]) {
        if (await this.checkPermission(context, module, action, { useCache: true })) {
          permissions.push(action);
        }
      }

      moduleAccess.push({
        module,
        hasAccess: permissions.length > 0,
        permissions,
        reason: await this.getPermissionReason(context, module, 'read')
      });
    }

    return moduleAccess;
  }

  // ===== PERMISSION RULES ENGINE =====

  /**
   * Add custom permission rule
   */
  addRule(rule: PermissionRule): void {
    this.rules.push(rule);
    this.rules.sort((a, b) => b.priority - a.priority); // Higher priority first
    this.cache.invalidate(); // Clear cache when rules change
  }

  /**
   * Remove permission rule
   */
  removeRule(ruleId: string): void {
    this.rules = this.rules.filter(rule => rule.id !== ruleId);
    this.cache.invalidate();
  }

  /**
   * Evaluate custom rules for permission
   */
  private evaluateRules(context: PermissionContext, module: EvexaModule, action: PermissionAction): boolean {
    for (const rule of this.rules) {
      if (rule.modules.includes(module) && rule.actions.includes(action)) {
        if (rule.condition(context)) {
          return true;
        }
      }
    }
    return false;
  }

  // ===== PRIVATE METHODS =====

  private async performPermissionCheck(
    context: PermissionContext,
    module: EvexaModule,
    action: PermissionAction,
    options: PermissionCheckOptions
  ): Promise<boolean> {
    // 1. Check custom rules first (highest priority)
    if (this.evaluateRules(context, module, action)) {
      return true;
    }

    // 2. Check custom overrides
    if (context.customOverrides) {
      const override = context.customOverrides.find(o => 
        o.module === module && o.actions.includes(action)
      );
      if (override && (!override.expiresAt || override.expiresAt > new Date())) {
        return true;
      }
    }

    // 3. Check persona permissions
    if (context.persona) {
      const modulePermission = context.persona.permissions.modules.find(m => m.module === module);
      if (modulePermission && modulePermission.actions.includes(action)) {
        // 4. Check subscription limits if required
        if (options.checkSubscription !== false) {
          const metadata = MODULE_METADATA[module];
          if (metadata.isPremium && !this.hasSubscriptionAccess(context.subscriptionTier, metadata.minimumPlan)) {
            return false;
          }
        }
        return true;
      }
    }

    return false;
  }

  private async getPermissionReason(
    context: PermissionContext,
    module: EvexaModule,
    action: PermissionAction
  ): Promise<string> {
    if (this.evaluateRules(context, module, action)) {
      return 'custom_rule';
    }

    if (context.customOverrides?.some(o => o.module === module && o.actions.includes(action))) {
      return 'override';
    }

    if (context.persona?.permissions.modules.some(m => m.module === module && m.actions.includes(action))) {
      return 'persona';
    }

    return 'denied';
  }

  private hasSubscriptionAccess(userTier?: string, requiredTier?: string): boolean {
    if (!requiredTier) return true;
    if (!userTier) return false;

    const tierLevels = { basic: 1, professional: 2, enterprise: 3 };
    return tierLevels[userTier as keyof typeof tierLevels] >= tierLevels[requiredTier as keyof typeof tierLevels];
  }

  private initializeDefaultRules(): void {
    // Add default permission rules
    this.addRule({
      id: 'super_admin_rule',
      name: 'Super Admin Access',
      description: 'Super admins have access to all modules',
      condition: (context) => context.persona?.id === 'super_admin',
      modules: Object.values(MODULE_METADATA).map(m => m.id),
      actions: ['read', 'write', 'delete', 'admin'],
      priority: 1000
    });

    this.addRule({
      id: 'emergency_access_rule',
      name: 'Emergency Access',
      description: 'Emergency access for critical operations',
      condition: (context) => {
        // Check if user has emergency access flag
        return context.persona?.customizations?.featureFlags?.emergencyAccess === true;
      },
      modules: ['exhibitions', 'tasks', 'contacts'],
      actions: ['read', 'write'],
      priority: 900
    });
  }

  // ===== CACHE MANAGEMENT =====

  /**
   * Invalidate cache for specific user or pattern
   */
  invalidateCache(pattern?: string): void {
    this.cache.invalidate(pattern);
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; hitRate: number } {
    return this.cache.getStats();
  }
}

// ===== FACTORY FUNCTION =====

/**
 * Create module permission engine instance
 */
export function createModulePermissionEngine(tenantId: string): ModulePermissionEngine {
  return new ModulePermissionEngine(tenantId);
}

// ===== PERMISSION UTILITIES =====

/**
 * Create permission context from user data
 */
export function createPermissionContext(
  userId: string,
  tenantId: string,
  persona?: TenantPersona,
  subscriptionTier?: string,
  customOverrides?: ModuleOverride[]
): PermissionContext {
  return {
    userId,
    tenantId,
    persona,
    subscriptionTier: subscriptionTier as any,
    customOverrides: customOverrides || []
  };
}

/**
 * Validate permission requirements
 */
export async function validatePermissions(
  engine: ModulePermissionEngine,
  context: PermissionContext,
  requirements: Array<{ module: EvexaModule; action: PermissionAction }>
): Promise<void> {
  const results = await engine.checkBulkPermissions(context, requirements, { throwOnDenied: false });
  const denied = results.results.filter(r => !r.hasPermission);

  if (denied.length > 0) {
    const deniedList = denied.map(d => `${d.module}:${d.action}`).join(', ');
    throw new Error(`Access denied. Missing permissions: ${deniedList}`);
  }
}
