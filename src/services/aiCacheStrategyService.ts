/**
 * AI Cache Strategy Service for EVEXA
 * Advanced caching strategies to minimize AI API costs while maintaining performance
 */

import { dataCache } from './dataCache';

export interface CacheStrategy {
  name: string;
  description: string;
  ttl: number;
  priority: number;
  conditions: (service: string, prompt: string, context?: any) => boolean;
}

export interface CachePerformanceMetrics {
  hitRate: number;
  missRate: number;
  averageResponseTime: number;
  costSavings: number;
  totalRequests: number;
  cacheSize: number;
  topCachedQueries: Array<{
    key: string;
    hits: number;
    savings: number;
  }>;
}

export interface SmartCacheConfig {
  enableSemanticCaching: boolean;
  enablePredictiveCaching: boolean;
  enableContextAwareCaching: boolean;
  maxCacheSize: number; // MB
  compressionThreshold: number; // bytes
  adaptiveTTL: boolean;
}

class AICacheStrategyService {
  private strategies: CacheStrategy[] = [];
  private performanceMetrics: CachePerformanceMetrics;
  private config: SmartCacheConfig;
  private semanticCache = new Map<string, { embedding: number[]; response: any; timestamp: Date }>();

  constructor() {
    this.config = {
      enableSemanticCaching: true,
      enablePredictiveCaching: true,
      enableContextAwareCaching: true,
      maxCacheSize: 500, // 500MB
      compressionThreshold: 1024, // 1KB
      adaptiveTTL: true
    };

    this.performanceMetrics = {
      hitRate: 0,
      missRate: 0,
      averageResponseTime: 0,
      costSavings: 0,
      totalRequests: 0,
      cacheSize: 0,
      topCachedQueries: []
    };

    this.initializeStrategies();
  }

  /**
   * Initialize caching strategies
   */
  private initializeStrategies(): void {
    this.strategies = [
      {
        name: 'workflow_automation',
        description: 'Cache workflow automation analysis for 24 hours',
        ttl: 24 * 60, // 24 hours
        priority: 1,
        conditions: (service) => service.includes('workflow') || service.includes('automation')
      },
      {
        name: 'budget_prediction',
        description: 'Cache budget predictions for 12 hours',
        ttl: 12 * 60, // 12 hours
        priority: 2,
        conditions: (service) => service.includes('budget') || service.includes('predict')
      },
      {
        name: 'content_generation',
        description: 'Cache content generation for 6 hours',
        ttl: 6 * 60, // 6 hours
        priority: 3,
        conditions: (service) => service.includes('content') || service.includes('generate')
      },
      {
        name: 'performance_insights',
        description: 'Cache performance insights for 4 hours',
        ttl: 4 * 60, // 4 hours
        priority: 4,
        conditions: (service) => service.includes('performance') || service.includes('insight')
      },
      {
        name: 'lead_scoring',
        description: 'Cache lead scoring for 2 hours',
        ttl: 2 * 60, // 2 hours
        priority: 5,
        conditions: (service) => service.includes('lead') || service.includes('score')
      },
      {
        name: 'general_analysis',
        description: 'Cache general analysis for 1 hour',
        ttl: 60, // 1 hour
        priority: 6,
        conditions: () => true // Default strategy
      }
    ];
  }

  /**
   * Get optimal cache strategy for a request
   */
  getOptimalStrategy(service: string, prompt: string, context?: any): CacheStrategy {
    // Find the first matching strategy with highest priority
    const matchingStrategy = this.strategies.find(strategy => 
      strategy.conditions(service, prompt, context)
    );

    return matchingStrategy || this.strategies[this.strategies.length - 1]; // Fallback to general
  }

  /**
   * Generate smart cache key with semantic awareness
   */
  generateSmartCacheKey(service: string, prompt: string, context?: any): string {
    // Normalize prompt for better cache hits
    const normalizedPrompt = this.normalizePrompt(prompt);
    
    // Create base key
    let baseKey = `${service}:${this.hashString(normalizedPrompt)}`;
    
    // Add context hash if available
    if (context) {
      const contextHash = this.hashString(JSON.stringify(context));
      baseKey += `:${contextHash}`;
    }
    
    return baseKey;
  }

  /**
   * Check semantic cache for similar queries
   */
  async checkSemanticCache(prompt: string, threshold: number = 0.85): Promise<any | null> {
    if (!this.config.enableSemanticCaching) {
      return null;
    }

    // This is a simplified semantic similarity check
    // In production, you'd use actual embeddings
    const promptEmbedding = this.generateSimpleEmbedding(prompt);
    
    for (const [key, cached] of this.semanticCache.entries()) {
      const similarity = this.calculateCosineSimilarity(promptEmbedding, cached.embedding);
      
      if (similarity >= threshold) {
        // Check if cache entry is still valid
        const age = Date.now() - cached.timestamp.getTime();
        if (age < 60 * 60 * 1000) { // 1 hour TTL for semantic cache
          console.log(`🎯 Semantic cache hit: ${similarity.toFixed(2)} similarity`);
          return cached.response;
        }
      }
    }
    
    return null;
  }

  /**
   * Store response in semantic cache
   */
  storeInSemanticCache(prompt: string, response: any): void {
    if (!this.config.enableSemanticCaching) {
      return;
    }

    const embedding = this.generateSimpleEmbedding(prompt);
    const key = this.hashString(prompt);
    
    this.semanticCache.set(key, {
      embedding,
      response,
      timestamp: new Date()
    });

    // Cleanup old entries if cache is too large
    if (this.semanticCache.size > 1000) {
      this.cleanupSemanticCache();
    }
  }

  /**
   * Predictive caching based on usage patterns
   */
  async predictiveCache(usageHistory: any[] = [], getCachedResponse?: (service: string, prompt: string, context?: any) => any): Promise<void> {
    if (!this.config.enablePredictiveCaching) {
      return;
    }

    const commonQueries = this.identifyCommonQueries(usageHistory);

    // Pre-cache common queries during low-usage periods
    for (const query of commonQueries) {
      const cacheKey = this.generateSmartCacheKey(query.service, query.prompt, query.context);

      // Check if not already cached
      const cached = getCachedResponse ? getCachedResponse(query.service, query.prompt, query.context) : null;
      if (!cached) {
        console.log(`🔮 Predictive caching: ${query.service}`);
        // This would trigger a background AI request to pre-populate cache
        // Implementation depends on your AI service architecture
      }
    }
  }

  /**
   * Context-aware caching
   */
  getContextAwareTTL(service: string, context?: any): number {
    if (!this.config.enableContextAwareCaching) {
      return this.getOptimalStrategy(service, '', context).ttl;
    }

    let baseTTL = this.getOptimalStrategy(service, '', context).ttl;
    
    // Adjust TTL based on context
    if (context) {
      // If context includes time-sensitive data, reduce TTL
      if (this.hasTimeSensitiveData(context)) {
        baseTTL = Math.min(baseTTL, 30); // Max 30 minutes for time-sensitive data
      }
      
      // If context includes user-specific data, reduce TTL
      if (this.hasUserSpecificData(context)) {
        baseTTL = Math.min(baseTTL, 60); // Max 1 hour for user-specific data
      }
      
      // If context includes static reference data, increase TTL
      if (this.hasStaticData(context)) {
        baseTTL = Math.max(baseTTL, 240); // Min 4 hours for static data
      }
    }
    
    return baseTTL;
  }

  /**
   * Get cache performance metrics
   */
  getPerformanceMetrics(cacheStats?: any): CachePerformanceMetrics {
    const stats = cacheStats || { hitRate: 0, averageResponseTime: 0, totalRequests: 0, totalSize: 0 };

    return {
      hitRate: stats.hitRate || 0,
      missRate: 1 - (stats.hitRate || 0),
      averageResponseTime: stats.averageResponseTime || 0,
      costSavings: this.calculateCostSavings(),
      totalRequests: stats.totalRequests || 0,
      cacheSize: stats.totalSize || 0,
      topCachedQueries: this.getTopCachedQueries()
    };
  }

  /**
   * Optimize cache configuration based on performance
   */
  optimizeCacheConfiguration(): void {
    const metrics = this.getPerformanceMetrics();
    
    // If hit rate is low, increase TTL for common queries
    if (metrics.hitRate < 0.4) {
      this.strategies.forEach(strategy => {
        if (strategy.priority <= 3) { // High priority strategies
          strategy.ttl = Math.min(strategy.ttl * 1.5, 24 * 60); // Max 24 hours
        }
      });
      console.log('📈 Increased TTL for high-priority cache strategies');
    }
    
    // If cache size is too large, enable compression
    if (metrics.cacheSize > this.config.maxCacheSize * 0.8) {
      this.config.compressionThreshold = Math.max(this.config.compressionThreshold * 0.5, 256);
      console.log('🗜️ Enabled aggressive compression due to cache size');
    }
    
    // If cost savings are high, enable more aggressive caching
    if (metrics.costSavings > 50) { // $50 savings
      this.config.enablePredictiveCaching = true;
      this.config.enableSemanticCaching = true;
      console.log('💰 Enabled advanced caching due to high cost savings');
    }
  }

  // Helper methods
  private normalizePrompt(prompt: string): string {
    return prompt
      .toLowerCase()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s]/g, '')
      .trim();
  }

  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  private generateSimpleEmbedding(text: string): number[] {
    // Simplified embedding generation for demo
    // In production, use actual embedding models
    const words = text.toLowerCase().split(/\s+/);
    const embedding = new Array(100).fill(0);
    
    words.forEach((word, index) => {
      const hash = this.hashString(word);
      const position = parseInt(hash, 36) % 100;
      embedding[position] += 1 / (index + 1);
    });
    
    return embedding;
  }

  private calculateCosineSimilarity(a: number[], b: number[]): number {
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    
    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  private cleanupSemanticCache(): void {
    const entries = Array.from(this.semanticCache.entries());
    entries.sort((a, b) => b[1].timestamp.getTime() - a[1].timestamp.getTime());
    
    // Keep only the most recent 500 entries
    this.semanticCache.clear();
    entries.slice(0, 500).forEach(([key, value]) => {
      this.semanticCache.set(key, value);
    });
  }

  private identifyCommonQueries(usageHistory: any[]): any[] {
    const queryFrequency = new Map<string, { count: number; query: any }>();
    
    usageHistory.forEach(record => {
      const key = `${record.service}:${record.prompt.substring(0, 100)}`;
      const existing = queryFrequency.get(key);
      
      if (existing) {
        existing.count++;
      } else {
        queryFrequency.set(key, { count: 1, query: record });
      }
    });
    
    return Array.from(queryFrequency.values())
      .filter(item => item.count >= 3)
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
      .map(item => item.query);
  }

  private hasTimeSensitiveData(context: any): boolean {
    const timeSensitiveKeys = ['timestamp', 'date', 'time', 'now', 'current', 'today'];
    const contextStr = JSON.stringify(context).toLowerCase();
    return timeSensitiveKeys.some(key => contextStr.includes(key));
  }

  private hasUserSpecificData(context: any): boolean {
    const userSpecificKeys = ['user', 'userId', 'profile', 'personal', 'private'];
    const contextStr = JSON.stringify(context).toLowerCase();
    return userSpecificKeys.some(key => contextStr.includes(key));
  }

  private hasStaticData(context: any): boolean {
    const staticKeys = ['template', 'config', 'settings', 'reference', 'constant'];
    const contextStr = JSON.stringify(context).toLowerCase();
    return staticKeys.some(key => contextStr.includes(key));
  }

  private calculateCostSavings(): number {
    const cacheStats = aiCostOptimizationService.getCacheStats();
    const averageCostPerRequest = 0.01; // Estimated average cost
    return (cacheStats.hits || 0) * averageCostPerRequest;
  }

  private getTopCachedQueries(): Array<{ key: string; hits: number; savings: number }> {
    // This would be implemented based on your cache statistics
    // For now, return mock data
    return [
      { key: 'workflow:analysis', hits: 45, savings: 0.45 },
      { key: 'budget:prediction', hits: 32, savings: 0.32 },
      { key: 'content:generation', hits: 28, savings: 0.28 }
    ];
  }
}

export const aiCacheStrategyService = new AICacheStrategyService();
