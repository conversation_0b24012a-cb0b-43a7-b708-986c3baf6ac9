/**
 * Real Firebase Authentication Service
 * 
 * Replaces all mock authentication with real Firebase Auth
 * Provides proper user registration, login, and session management
 */

import { 
  getAuth, 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser,
  updateProfile,
  sendEmailVerification,
  sendPasswordResetEmail
} from 'firebase/auth';
import { 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  collection, 
  query, 
  where, 
  getDocs 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { EvexUser } from '@/types/firestore';

const auth = getAuth();

// Standard tenant ID for development
const EVEXA_TENANT_ID = 'evexa-development-company';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  displayName: string;
  jobTitle?: string;
  department?: string;
  role?: 'user' | 'manager' | 'admin';
}

export interface AuthResult {
  success: boolean;
  user?: EvexUser;
  error?: string;
}

/**
 * Register a new user with Firebase Auth and create user profile
 */
export async function registerUser(data: RegisterData): Promise<AuthResult> {
  try {
    // Create Firebase Auth user
    const userCredential = await createUserWithEmailAndPassword(auth, data.email, data.password);
    const firebaseUser = userCredential.user;

    // Update Firebase Auth profile
    await updateProfile(firebaseUser, {
      displayName: data.displayName
    });

    // Send email verification
    await sendEmailVerification(firebaseUser);

    // Create user profile in Firestore
    const userProfile: Omit<EvexUser, 'id'> = {
      email: data.email,
      displayName: data.displayName,
      role: data.role || 'user',
      status: 'active',
      tenantId: EVEXA_TENANT_ID,
      jobTitle: data.jobTitle || '',
      department: data.department || '',
      phone: '',
      profileImageUrl: `https://api.dicebear.com/7.x/avataaars/svg?seed=${data.displayName}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      lastLoginAt: new Date(),
      isEmailVerified: false,
      preferences: {
        theme: 'system',
        notifications: true,
        language: 'en'
      }
    };

    // Save to Firestore
    await setDoc(doc(db, 'user_profiles', firebaseUser.uid), {
      ...userProfile,
      id: firebaseUser.uid
    });

    return {
      success: true,
      user: { ...userProfile, id: firebaseUser.uid }
    };

  } catch (error: any) {
    console.error('Registration error:', error);
    return {
      success: false,
      error: error.message || 'Registration failed'
    };
  }
}

/**
 * Login user with email and password
 */
export async function loginUser(credentials: LoginCredentials): Promise<AuthResult> {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, credentials.email, credentials.password);
    const firebaseUser = userCredential.user;

    // Get user profile from Firestore
    const userDoc = await getDoc(doc(db, 'user_profiles', firebaseUser.uid));
    
    if (!userDoc.exists()) {
      throw new Error('User profile not found');
    }

    const userProfile = userDoc.data() as EvexUser;

    // Update last login time
    await updateDoc(doc(db, 'user_profiles', firebaseUser.uid), {
      lastLoginAt: new Date(),
      updatedAt: new Date()
    });

    return {
      success: true,
      user: userProfile
    };

  } catch (error: any) {
    console.error('Login error:', error);
    return {
      success: false,
      error: error.message || 'Login failed'
    };
  }
}

/**
 * Logout current user
 */
export async function logoutUser(): Promise<{ success: boolean; error?: string }> {
  try {
    await signOut(auth);
    return { success: true };
  } catch (error: any) {
    console.error('Logout error:', error);
    return {
      success: false,
      error: error.message || 'Logout failed'
    };
  }
}

/**
 * Get current authenticated user
 */
export async function getCurrentUser(): Promise<EvexUser | null> {
  const firebaseUser = auth.currentUser;
  
  if (!firebaseUser) {
    return null;
  }

  try {
    const userDoc = await getDoc(doc(db, 'user_profiles', firebaseUser.uid));
    
    if (!userDoc.exists()) {
      return null;
    }

    return userDoc.data() as EvexUser;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Check if user has specific role
 */
export function hasRole(user: EvexUser | null, role: string): boolean {
  if (!user) return false;
  
  if (role === 'super_admin') {
    return user.role === 'super_admin';
  }
  
  if (role === 'admin') {
    return user.role === 'admin' || user.role === 'super_admin';
  }
  
  if (role === 'manager') {
    return ['manager', 'admin', 'super_admin'].includes(user.role);
  }
  
  return true; // Regular users can access basic features
}

/**
 * Send password reset email
 */
export async function resetPassword(email: string): Promise<{ success: boolean; error?: string }> {
  try {
    await sendPasswordResetEmail(auth, email);
    return { success: true };
  } catch (error: any) {
    console.error('Password reset error:', error);
    return {
      success: false,
      error: error.message || 'Password reset failed'
    };
  }
}

/**
 * Listen to authentication state changes
 */
export function onAuthStateChange(callback: (user: EvexUser | null) => void) {
  return onAuthStateChanged(auth, async (firebaseUser) => {
    if (firebaseUser) {
      try {
        const userDoc = await getDoc(doc(db, 'user_profiles', firebaseUser.uid));
        if (userDoc.exists()) {
          callback(userDoc.data() as EvexUser);
        } else {
          callback(null);
        }
      } catch (error) {
        console.error('Error in auth state change:', error);
        callback(null);
      }
    } else {
      callback(null);
    }
  });
}

/**
 * Create default super admin user (for initial setup)
 */
export async function createSuperAdmin(): Promise<AuthResult> {
  const superAdminData: RegisterData = {
    email: '<EMAIL>',
    password: 'SuperAdmin123!',
    displayName: 'Superman EVEXA',
    jobTitle: 'Master Super Admin',
    department: 'EVEXA Development Company',
    role: 'admin' // Will be upgraded to super_admin in Firestore
  };

  try {
    const result = await registerUser(superAdminData);
    
    if (result.success && result.user) {
      // Upgrade to super_admin role
      await updateDoc(doc(db, 'user_profiles', result.user.id), {
        role: 'super_admin',
        updatedAt: new Date()
      });

      return {
        success: true,
        user: { ...result.user, role: 'super_admin' }
      };
    }

    return result;
  } catch (error: any) {
    console.error('Super admin creation error:', error);
    return {
      success: false,
      error: error.message || 'Super admin creation failed'
    };
  }
}
