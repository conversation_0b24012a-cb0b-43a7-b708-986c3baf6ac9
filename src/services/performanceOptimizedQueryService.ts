/**
 * Performance-Optimized Query Service
 * Implements strategic data duplication, embedded field queries, and efficient list views
 * Minimizes database reads through intelligent query patterns and caching
 */

import {
  collection,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  getDocs,
  getDoc,
  doc,
  Timestamp,
  type DocumentSnapshot,
  type QueryConstraint
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { validateTenantId } from './tenantIdHelperService';
import { dataCache } from './dataCache';
import type { 
  Exhibition, 
  Task, 
  Lead, 
  Event as EvexEvent,
  Vendor,
  TenantAwareEntity 
} from '@/types/firestore';

// ===== PERFORMANCE-OPTIMIZED INTERFACES =====

export interface OptimizedListQuery {
  tenantId: string;
  collection: string;
  filters?: Array<{
    field: string;
    operator: '==' | '!=' | '<' | '<=' | '>' | '>=' | 'array-contains' | 'in' | 'not-in';
    value: any;
  }>;
  orderByField?: string;
  orderDirection?: 'asc' | 'desc';
  limitCount?: number;
  startAfterDoc?: DocumentSnapshot;
  useEmbeddedData?: boolean;
}

export interface OptimizedListResult<T> {
  items: T[];
  hasMore: boolean;
  lastDoc?: DocumentSnapshot;
  totalCount?: number;
  fetchTime: number;
  cacheHit: boolean;
}

export interface EmbeddedDataQuery {
  tenantId: string;
  parentId: string;
  parentType: 'exhibition' | 'event' | 'vendor' | 'user';
  embeddedFields: string[];
  includeRelatedCounts?: boolean;
}

// ===== PERFORMANCE-OPTIMIZED QUERY SERVICE =====

class PerformanceOptimizedQueryService {
  
  /**
   * Optimized list query with pagination, caching, and embedded data
   */
  async getOptimizedList<T extends TenantAwareEntity>(
    queryOptions: OptimizedListQuery
  ): Promise<OptimizedListResult<T>> {
    const startTime = performance.now();
    validateTenantId(queryOptions.tenantId);
    
    // Generate cache key based on query parameters
    const cacheKey = this.generateCacheKey(queryOptions);

    // Check cache first
    const cached = dataCache.getCached(cacheKey);
    if (cached) {
      return {
        ...cached,
        fetchTime: performance.now() - startTime,
        cacheHit: true
      };
    }
    
    // Build optimized query
    const constraints: QueryConstraint[] = [
      where('tenantId', '==', queryOptions.tenantId)
    ];
    
    // Add filters
    if (queryOptions.filters) {
      queryOptions.filters.forEach(filter => {
        constraints.push(where(filter.field, filter.operator as any, filter.value));
      });
    }
    
    // Add ordering
    if (queryOptions.orderByField) {
      constraints.push(orderBy(queryOptions.orderByField, queryOptions.orderDirection || 'desc'));
    }
    
    // Add pagination
    if (queryOptions.startAfterDoc) {
      constraints.push(startAfter(queryOptions.startAfterDoc));
    }
    
    // Add limit (default to 50 for performance)
    const limitCount = queryOptions.limitCount || 50;
    constraints.push(limit(limitCount + 1)); // +1 to check if there are more items
    
    // Execute query
    const queryRef = query(collection(db, queryOptions.collection), ...constraints);
    const snapshot = await getDocs(queryRef);
    
    // Process results
    const items = snapshot.docs.slice(0, limitCount).map(doc => ({
      id: doc.id,
      ...doc.data()
    } as T));
    
    const hasMore = snapshot.docs.length > limitCount;
    const lastDoc = hasMore ? snapshot.docs[limitCount - 1] : snapshot.docs[snapshot.docs.length - 1];
    
    const result: OptimizedListResult<T> = {
      items,
      hasMore,
      lastDoc,
      fetchTime: performance.now() - startTime,
      cacheHit: false
    };
    
    // Cache the result (5 minute TTL for list queries)
    dataCache.set(cacheKey, result, { ttl: 5 * 60 * 1000 });
    
    return result;
  }
  
  /**
   * Get tasks with embedded exhibition names (no joins required)
   */
  async getTasksWithEmbeddedData(
    tenantId: string,
    options: {
      exhibitionId?: string;
      assigneeId?: string;
      status?: string;
      limit?: number;
      startAfter?: DocumentSnapshot;
    } = {}
  ): Promise<OptimizedListResult<Task & { exhibitionName?: string; assigneeName?: string }>> {
    return this.getOptimizedList<Task & { exhibitionName?: string; assigneeName?: string }>({
      tenantId,
      collection: COLLECTIONS.EXHIBITION_TASKS,
      filters: [
        ...(options.exhibitionId ? [{ field: 'exhibitionId', operator: '==' as const, value: options.exhibitionId }] : []),
        ...(options.assigneeId ? [{ field: 'assignedTo', operator: '==' as const, value: options.assigneeId }] : []),
        ...(options.status ? [{ field: 'status', operator: '==' as const, value: options.status }] : [])
      ],
      orderByField: 'dueDate',
      orderDirection: 'asc',
      limitCount: options.limit,
      startAfterDoc: options.startAfter,
      useEmbeddedData: true
    });
  }
  
  /**
   * Get leads with embedded exhibition names (no joins required)
   */
  async getLeadsWithEmbeddedData(
    tenantId: string,
    options: {
      exhibitionId?: string;
      status?: string;
      assigneeId?: string;
      limit?: number;
      startAfter?: DocumentSnapshot;
    } = {}
  ): Promise<OptimizedListResult<Lead & { exhibitionName?: string; assigneeName?: string }>> {
    return this.getOptimizedList<Lead & { exhibitionName?: string; assigneeName?: string }>({
      tenantId,
      collection: COLLECTIONS.LEAD_CONTACTS,
      filters: [
        ...(options.exhibitionId ? [{ field: 'exhibitionId', operator: '==' as const, value: options.exhibitionId }] : []),
        ...(options.status ? [{ field: 'status', operator: '==' as const, value: options.status }] : []),
        ...(options.assigneeId ? [{ field: 'assignedTo', operator: '==' as const, value: options.assigneeId }] : [])
      ],
      orderByField: 'createdAt',
      orderDirection: 'desc',
      limitCount: options.limit,
      startAfterDoc: options.startAfter,
      useEmbeddedData: true
    });
  }
  
  /**
   * Get events with embedded exhibition names (no joins required)
   */
  async getEventsWithEmbeddedData(
    tenantId: string,
    options: {
      exhibitionId?: string;
      eventType?: string;
      startDate?: Date;
      endDate?: Date;
      limit?: number;
      startAfter?: DocumentSnapshot;
    } = {}
  ): Promise<OptimizedListResult<EvexEvent & { exhibitionName?: string }>> {
    const filters: OptimizedListQuery['filters'] = [];
    
    if (options.exhibitionId) {
      filters.push({ field: 'exhibitionId', operator: '==', value: options.exhibitionId });
    }
    
    if (options.eventType) {
      filters.push({ field: 'eventType', operator: '==', value: options.eventType });
    }
    
    if (options.startDate) {
      filters.push({ field: 'startDate', operator: '>=', value: Timestamp.fromDate(options.startDate) });
    }
    
    if (options.endDate) {
      filters.push({ field: 'startDate', operator: '<=', value: Timestamp.fromDate(options.endDate) });
    }
    
    return this.getOptimizedList<EvexEvent & { exhibitionName?: string }>({
      tenantId,
      collection: COLLECTIONS.EXHIBITION_EVENTS,
      filters,
      orderByField: 'startDate',
      orderDirection: 'asc',
      limitCount: options.limit,
      startAfterDoc: options.startAfter,
      useEmbeddedData: true
    });
  }
  
  /**
   * Get exhibitions with embedded related counts (no separate queries required)
   */
  async getExhibitionsWithCounts(
    tenantId: string,
    options: {
      status?: string;
      startDate?: Date;
      endDate?: Date;
      limit?: number;
      startAfter?: DocumentSnapshot;
    } = {}
  ): Promise<OptimizedListResult<Exhibition & { taskCount?: number; leadCount?: number; eventCount?: number }>> {
    const cacheKey = `exhibitions_with_counts:${tenantId}:${JSON.stringify(options)}`;

    // Check cache first
    const cached = dataCache.getCached(cacheKey);
    if (cached) {
      return { ...cached, cacheHit: true };
    }
    
    const startTime = performance.now();
    
    // Get exhibitions first
    const exhibitionsResult = await this.getOptimizedList<Exhibition>({
      tenantId,
      collection: COLLECTIONS.EXHIBITIONS,
      filters: [
        ...(options.status ? [{ field: 'status', operator: '==' as const, value: options.status }] : []),
        ...(options.startDate ? [{ field: 'startDate', operator: '>=' as const, value: Timestamp.fromDate(options.startDate) }] : []),
        ...(options.endDate ? [{ field: 'endDate', operator: '<=' as const, value: Timestamp.fromDate(options.endDate) }] : [])
      ],
      orderByField: 'startDate',
      orderDirection: 'desc',
      limitCount: options.limit,
      startAfterDoc: options.startAfter
    });
    
    // For each exhibition, get counts in parallel (these should be pre-calculated and stored)
    const exhibitionsWithCounts = await Promise.all(
      exhibitionsResult.items.map(async (exhibition) => {
        // These counts should ideally be stored as fields in the exhibition document
        // or calculated periodically and cached
        const [taskCount, leadCount, eventCount] = await Promise.all([
          this.getCachedCount(tenantId, COLLECTIONS.EXHIBITION_TASKS, 'exhibitionId', exhibition.id!),
          this.getCachedCount(tenantId, COLLECTIONS.LEAD_CONTACTS, 'exhibitionId', exhibition.id!),
          this.getCachedCount(tenantId, COLLECTIONS.EXHIBITION_EVENTS, 'exhibitionId', exhibition.id!)
        ]);
        
        return {
          ...exhibition,
          taskCount,
          leadCount,
          eventCount
        };
      })
    );
    
    const result: OptimizedListResult<Exhibition & { taskCount?: number; leadCount?: number; eventCount?: number }> = {
      ...exhibitionsResult,
      items: exhibitionsWithCounts,
      fetchTime: performance.now() - startTime,
      cacheHit: false
    };
    
    // Cache for 10 minutes
    dataCache.set(cacheKey, result, { ttl: 10 * 60 * 1000 });
    
    return result;
  }
  
  /**
   * Batch query for dashboard widgets (single query for multiple data types)
   */
  async getDashboardData(
    tenantId: string,
    options: {
      includeUpcoming?: boolean;
      includeRecent?: boolean;
      includeCounts?: boolean;
      limit?: number;
    } = {}
  ): Promise<{
    upcomingExhibitions?: Exhibition[];
    recentTasks?: Task[];
    recentLeads?: Lead[];
    counts?: {
      totalExhibitions: number;
      totalTasks: number;
      totalLeads: number;
      totalEvents: number;
    };
    fetchTime: number;
  }> {
    const startTime = performance.now();
    const cacheKey = `dashboard_data:${tenantId}:${JSON.stringify(options)}`;

    // Check cache first
    const cached = dataCache.getCached(cacheKey);
    if (cached) {
      return { ...cached, fetchTime: performance.now() - startTime };
    }
    
    const queries: Promise<any>[] = [];
    
    if (options.includeUpcoming) {
      queries.push(
        this.getOptimizedList<Exhibition>({
          tenantId,
          collection: COLLECTIONS.EXHIBITIONS,
          filters: [
            { field: 'startDate', operator: '>', value: Timestamp.now() }
          ],
          orderByField: 'startDate',
          orderDirection: 'asc',
          limitCount: options.limit || 5
        })
      );
    }
    
    if (options.includeRecent) {
      queries.push(
        this.getOptimizedList<Task>({
          tenantId,
          collection: COLLECTIONS.EXHIBITION_TASKS,
          orderByField: 'createdAt',
          orderDirection: 'desc',
          limitCount: options.limit || 10
        }),
        this.getOptimizedList<Lead>({
          tenantId,
          collection: COLLECTIONS.LEAD_CONTACTS,
          orderByField: 'createdAt',
          orderDirection: 'desc',
          limitCount: options.limit || 10
        })
      );
    }
    
    if (options.includeCounts) {
      queries.push(
        this.getCachedCount(tenantId, COLLECTIONS.EXHIBITIONS),
        this.getCachedCount(tenantId, COLLECTIONS.EXHIBITION_TASKS),
        this.getCachedCount(tenantId, COLLECTIONS.LEAD_CONTACTS),
        this.getCachedCount(tenantId, COLLECTIONS.EXHIBITION_EVENTS)
      );
    }
    
    const results = await Promise.all(queries);
    
    let resultIndex = 0;
    const dashboardData: any = {};
    
    if (options.includeUpcoming) {
      dashboardData.upcomingExhibitions = results[resultIndex++].items;
    }
    
    if (options.includeRecent) {
      dashboardData.recentTasks = results[resultIndex++].items;
      dashboardData.recentLeads = results[resultIndex++].items;
    }
    
    if (options.includeCounts) {
      dashboardData.counts = {
        totalExhibitions: results[resultIndex++],
        totalTasks: results[resultIndex++],
        totalLeads: results[resultIndex++],
        totalEvents: results[resultIndex++]
      };
    }
    
    dashboardData.fetchTime = performance.now() - startTime;
    
    // Cache for 5 minutes
    dataCache.set(cacheKey, dashboardData, { ttl: 5 * 60 * 1000 });
    
    return dashboardData;
  }
  
  // ===== PRIVATE HELPER METHODS =====
  
  private generateCacheKey(queryOptions: OptimizedListQuery): string {
    const keyParts = [
      queryOptions.collection,
      queryOptions.tenantId,
      JSON.stringify(queryOptions.filters || []),
      queryOptions.orderByField || 'default',
      queryOptions.orderDirection || 'desc',
      queryOptions.limitCount || 50,
      queryOptions.startAfterDoc?.id || 'start'
    ];
    
    return keyParts.join(':');
  }
  
  private async getCachedCount(
    tenantId: string, 
    collectionName: string, 
    filterField?: string, 
    filterValue?: string
  ): Promise<number> {
    const cacheKey = `count:${collectionName}:${tenantId}:${filterField || 'all'}:${filterValue || 'all'}`;
    
    // Check cache first (counts are cached for 15 minutes)
    const cached = dataCache.getCached(cacheKey);
    if (cached !== undefined) {
      return cached;
    }
    
    // Build query
    const constraints: QueryConstraint[] = [where('tenantId', '==', tenantId)];
    
    if (filterField && filterValue) {
      constraints.push(where(filterField, '==', filterValue));
    }
    
    const queryRef = query(collection(db, collectionName), ...constraints);
    const snapshot = await getDocs(queryRef);
    const count = snapshot.size;
    
    // Cache for 15 minutes
    dataCache.set(cacheKey, count, { ttl: 15 * 60 * 1000 });
    
    return count;
  }
}

// Export singleton instance
export const performanceOptimizedQueryService = new PerformanceOptimizedQueryService();
