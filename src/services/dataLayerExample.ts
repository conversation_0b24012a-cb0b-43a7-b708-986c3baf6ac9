/**
 * Example Service Implementation using EVEXA Data Layer
 *
 * This demonstrates the industry-standard pattern for implementing
 * services that work with Firebase data only.
 */

import {
  collection,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  doc
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// Example: Exhibition Service
export interface Exhibition {
  id?: string;
  name: string;
  startDate: Date;
  endDate: Date;
  location: string;
  status: 'planning' | 'active' | 'completed';
}

/**
 * Get all exhibitions - Firebase Only Implementation
 */
export async function getExhibitions(): Promise<Exhibition[]> {
  try {
    const snapshot = await getDocs(collection(db, 'exhibitions'));
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      startDate: doc.data().startDate?.toDate() || new Date(),
      endDate: doc.data().endDate?.toDate() || new Date(),
    })) as Exhibition[];
  } catch (error) {
    console.error('Error fetching exhibitions:', error);
    return [];
  }
}

/**
 * Create exhibition
 */
export async function createExhibition(exhibition: Omit<Exhibition, 'id'>): Promise<string> {
  try {
    const docRef = await addDoc(collection(db, 'exhibitions'), {
      ...exhibition,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error creating exhibition:', error);
    throw error;
  }
}

/**
 * Update exhibition
 */
export async function updateExhibition(id: string, updates: Partial<Exhibition>): Promise<void> {
  try {
    await updateDoc(doc(db, 'exhibitions', id), {
      ...updates,
      updatedAt: new Date()
    });
  } catch (error) {
    console.error('Error updating exhibition:', error);
    throw error;
  }
}

/**
 * Delete exhibition
 */
export async function deleteExhibition(id: string): Promise<void> {
  try {
    await deleteDoc(doc(db, 'exhibitions', id));
  } catch (error) {
    console.error('Error deleting exhibition:', error);
    throw error;
  }
}
