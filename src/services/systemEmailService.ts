/**
 * System Email Service
 * Real email sending service for EVEXA system notifications, invitations, and communications
 * Integrates with Firebase Email Extension and tenant branding
 */

import {
  collection,
  doc,
  addDoc,
  updateDoc,
  getDoc,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import {
  systemEmailTemplates,
  getSystemTemplate,
  replaceTemplateVariables
} from '@/templates/system-email-templates';
import { validateTenantId } from './tenantIdHelperService';
import { sendEmailWithSendGrid } from '@/lib/firebase-email-service';

// ===== TYPES =====

export interface EmailSendRequest {
  to: string;
  templateId: string;
  variables: Record<string, string>;
  tenantId: string;
  priority?: 'high' | 'normal' | 'low';
  scheduledAt?: Date;
  trackOpens?: boolean;
  trackClicks?: boolean;
}

export interface EmailSendResult {
  success: boolean;
  messageId?: string;
  error?: string;
  queuedAt?: Date;
}

export interface TenantEmailBranding {
  companyName: string;
  logoUrl?: string;
  primaryColor?: string;
  secondaryColor?: string;
  supportEmail?: string;
  fromName?: string;
  fromEmail?: string;
}

export interface EmailQueueItem {
  id: string;
  tenantId: string;
  to: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  templateId: string;
  variables: Record<string, string>;
  status: 'queued' | 'sending' | 'sent' | 'failed' | 'bounced';
  priority: 'high' | 'normal' | 'low';
  attempts: number;
  maxAttempts: number;
  scheduledAt?: Date;
  sentAt?: Date;
  failedAt?: Date;
  error?: string;
  trackingEnabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// ===== SYSTEM EMAIL SERVICE =====

export class SystemEmailService {
  constructor(private tenantId: string) {
    validateTenantId(tenantId);
  }

  /**
   * Send system email using template
   */
  async sendEmail(request: EmailSendRequest): Promise<EmailSendResult> {
    try {
      // Validate template exists
      const template = getSystemTemplate(request.templateId);
      if (!template) {
        return {
          success: false,
          error: `Template not found: ${request.templateId}`
        };
      }

      // Get tenant branding
      const branding = await this.getTenantBranding();
      
      // Merge variables with tenant branding
      const mergedVariables = {
        ...request.variables,
        tenantName: branding.companyName,
        tenantLogo: branding.logoUrl || '',
        supportEmail: branding.supportEmail || '<EMAIL>'
      };

      // Process template content
      const subject = replaceTemplateVariables(template.subject, mergedVariables);
      const htmlContent = replaceTemplateVariables(template.htmlContent, mergedVariables);
      const textContent = replaceTemplateVariables(template.textContent, mergedVariables);

      // Create email queue item
      const queueItem: Omit<EmailQueueItem, 'id'> = {
        tenantId: this.tenantId,
        to: request.to,
        subject,
        htmlContent,
        textContent,
        templateId: request.templateId,
        variables: mergedVariables,
        status: 'queued',
        priority: request.priority || 'normal',
        attempts: 0,
        maxAttempts: 3,
        scheduledAt: request.scheduledAt,
        trackingEnabled: request.trackOpens || request.trackClicks || false,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Add to email queue
      const queueRef = await addDoc(collection(db, COLLECTIONS.EMAIL_QUEUE), {
        ...queueItem,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // If not scheduled, process immediately
      if (!request.scheduledAt) {
        await this.processEmailQueue(queueRef.id);
      }

      return {
        success: true,
        messageId: queueRef.id,
        queuedAt: new Date()
      };

    } catch (error) {
      console.error('Error sending system email:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send user invitation email
   */
  async sendUserInvitation(
    recipientEmail: string,
    recipientName: string,
    senderName: string,
    roleName: string,
    invitationUrl: string,
    expirationDate: string
  ): Promise<EmailSendResult> {
    return this.sendEmail({
      to: recipientEmail,
      templateId: 'user_invitation_v1',
      variables: {
        recipientName,
        senderName,
        roleName,
        invitationUrl,
        expirationDate
      },
      tenantId: this.tenantId,
      priority: 'high',
      trackOpens: true,
      trackClicks: true
    });
  }

  /**
   * Send welcome email to new user
   */
  async sendWelcomeEmail(
    recipientEmail: string,
    recipientName: string,
    loginUrl: string
  ): Promise<EmailSendResult> {
    return this.sendEmail({
      to: recipientEmail,
      templateId: 'welcome_new_user_v1',
      variables: {
        recipientName,
        loginUrl,
        senderName: 'EVEXA Team'
      },
      tenantId: this.tenantId,
      priority: 'high',
      trackOpens: true
    });
  }

  /**
   * Send password reset email
   */
  async sendPasswordReset(
    recipientEmail: string,
    recipientName: string,
    resetUrl: string
  ): Promise<EmailSendResult> {
    return this.sendEmail({
      to: recipientEmail,
      templateId: 'password_reset_v1',
      variables: {
        recipientName,
        resetUrl,
        senderName: 'EVEXA Security'
      },
      tenantId: this.tenantId,
      priority: 'high',
      trackOpens: true
    });
  }

  /**
   * Send account suspension notification
   */
  async sendAccountSuspension(
    recipientEmail: string,
    recipientName: string,
    suspensionReason: string
  ): Promise<EmailSendResult> {
    return this.sendEmail({
      to: recipientEmail,
      templateId: 'account_suspended_v1',
      variables: {
        recipientName,
        suspensionReason,
        senderName: 'EVEXA Security'
      },
      tenantId: this.tenantId,
      priority: 'high',
      trackOpens: true
    });
  }

  /**
   * Process email queue item (send actual email)
   */
  private async processEmailQueue(queueItemId: string): Promise<void> {
    try {
      const queueRef = doc(db, COLLECTIONS.EMAIL_QUEUE, queueItemId);
      const queueDoc = await getDoc(queueRef);
      
      if (!queueDoc.exists()) {
        throw new Error('Queue item not found');
      }

      const queueItem = queueDoc.data() as EmailQueueItem;
      
      // Update status to sending
      await updateDoc(queueRef, {
        status: 'sending',
        updatedAt: serverTimestamp()
      });

      // Get tenant branding for from address
      const branding = await this.getTenantBranding();

      // Send via SendGrid
      const result = await sendEmailWithSendGrid({
        to: queueItem.to,
        subject: queueItem.subject,
        html: queueItem.htmlContent,
        text: queueItem.textContent,
        from: {
          email: branding.fromEmail || process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
          name: branding.fromName || branding.companyName || process.env.SENDGRID_FROM_NAME || 'EVEXA'
        },
        replyTo: branding.supportEmail || process.env.SENDGRID_REPLY_TO || '<EMAIL>',
        trackingSettings: {
          openTracking: { enable: queueItem.trackingEnabled },
          clickTracking: { enable: queueItem.trackingEnabled }
        },
        customArgs: {
          tenantId: this.tenantId,
          templateId: queueItem.templateId,
          queueItemId: queueItemId,
          priority: queueItem.priority
        }
      });

      if (result.success) {
        // Update queue item status with success
        await updateDoc(queueRef, {
          status: 'sent',
          sentAt: serverTimestamp(),
          attempts: queueItem.attempts + 1,
          sendgridMessageId: result.messageId,
          statusCode: result.statusCode,
          updatedAt: serverTimestamp()
        });
      } else {
        // Handle SendGrid error
        throw new Error(result.error || 'Failed to send email via SendGrid');
      }

    } catch (error) {
      console.error('Error processing email queue:', error);
      
      // Update queue item with error
      const queueRef = doc(db, COLLECTIONS.EMAIL_QUEUE, queueItemId);
      await updateDoc(queueRef, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        failedAt: serverTimestamp(),
        attempts: (await getDoc(queueRef)).data()?.attempts + 1 || 1,
        updatedAt: serverTimestamp()
      });
    }
  }

  /**
   * Get tenant branding configuration
   */
  private async getTenantBranding(): Promise<TenantEmailBranding> {
    try {
      const tenantDoc = await getDoc(doc(db, COLLECTIONS.TENANTS, this.tenantId));
      
      if (!tenantDoc.exists()) {
        throw new Error('Tenant not found');
      }

      const tenantData = tenantDoc.data();
      const branding = tenantData.branding || {};

      return {
        companyName: branding.companyName || tenantData.name || 'Your Organization',
        logoUrl: branding.logoUrl,
        primaryColor: branding.primaryColor || '#667eea',
        secondaryColor: branding.secondaryColor || '#764ba2',
        supportEmail: branding.supportEmail || tenantData.contactEmail || '<EMAIL>',
        fromName: branding.fromName || branding.companyName || 'EVEXA',
        fromEmail: branding.fromEmail || '<EMAIL>'
      };

    } catch (error) {
      console.error('Error getting tenant branding:', error);
      
      // Return default branding
      return {
        companyName: 'Your Organization',
        supportEmail: '<EMAIL>',
        fromName: 'EVEXA',
        fromEmail: '<EMAIL>'
      };
    }
  }

  /**
   * Get available system templates
   */
  getAvailableTemplates() {
    return systemEmailTemplates.map(template => ({
      id: template.id,
      name: template.name,
      description: template.description,
      category: template.category,
      type: template.type,
      variables: template.variables.map(v => ({
        name: v.name,
        label: v.label,
        type: v.type,
        required: v.required
      }))
    }));
  }
}

// ===== FACTORY FUNCTION =====

/**
 * Create system email service instance
 */
export function createSystemEmailService(tenantId: string): SystemEmailService {
  return new SystemEmailService(tenantId);
}
