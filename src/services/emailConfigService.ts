/**
 * Email Configuration Service
 * Manages email service settings, rate limiting, and provider configuration
 */

import { doc, getDoc, setDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { validateTenantId } from './tenantIdHelperService';

// ===== TYPES =====

export interface EmailServiceConfig {
  provider: 'sendgrid' | 'ses' | 'mailgun' | 'smtp';
  apiKey?: string;
  fromEmail: string;
  fromName: string;
  replyToEmail: string;
  rateLimitPerMinute: number;
  retryAttempts: number;
  enableTracking: boolean;
  enableQueue: boolean;
  webhookUrl?: string;
  customDomain?: string;
  settings: {
    bounceHandling: boolean;
    spamCompliance: boolean;
    unsubscribeHandling: boolean;
    suppressionList: boolean;
  };
}

export interface TenantEmailConfig {
  tenantId: string;
  companyName: string;
  logoUrl?: string;
  brandColor?: string;
  fromEmail?: string;
  fromName?: string;
  replyToEmail?: string;
  supportEmail?: string;
  customFooter?: string;
  emailSignature?: string;
  unsubscribeUrl?: string;
  privacyPolicyUrl?: string;
  termsOfServiceUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface EmailQuota {
  tenantId: string;
  dailyLimit: number;
  monthlyLimit: number;
  dailyUsed: number;
  monthlyUsed: number;
  lastResetDate: Date;
  isBlocked: boolean;
  blockReason?: string;
}

// ===== EMAIL CONFIG SERVICE =====

export class EmailConfigService {
  constructor(private tenantId: string) {
    validateTenantId(tenantId);
  }

  /**
   * Get global email service configuration
   */
  async getGlobalConfig(): Promise<EmailServiceConfig> {
    try {
      const configDoc = await getDoc(doc(db, 'system_config', 'email_service'));
      
      if (configDoc.exists()) {
        return configDoc.data() as EmailServiceConfig;
      }

      // Return default configuration
      return this.getDefaultConfig();
    } catch (error) {
      console.error('Error getting global email config:', error);
      return this.getDefaultConfig();
    }
  }

  /**
   * Update global email service configuration (admin only)
   */
  async updateGlobalConfig(config: Partial<EmailServiceConfig>): Promise<void> {
    try {
      const configRef = doc(db, 'system_config', 'email_service');
      await updateDoc(configRef, {
        ...config,
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Error updating global email config:', error);
      throw error;
    }
  }

  /**
   * Get tenant-specific email configuration
   */
  async getTenantConfig(): Promise<TenantEmailConfig | null> {
    try {
      const configDoc = await getDoc(doc(db, COLLECTIONS.TENANT_EMAIL_CONFIG, this.tenantId));
      
      if (configDoc.exists()) {
        return configDoc.data() as TenantEmailConfig;
      }

      return null;
    } catch (error) {
      console.error('Error getting tenant email config:', error);
      return null;
    }
  }

  /**
   * Update tenant email configuration
   */
  async updateTenantConfig(config: Partial<TenantEmailConfig>): Promise<void> {
    try {
      const configRef = doc(db, COLLECTIONS.TENANT_EMAIL_CONFIG, this.tenantId);
      const existingConfig = await this.getTenantConfig();

      const updatedConfig: TenantEmailConfig = {
        tenantId: this.tenantId,
        companyName: config.companyName || 'EVEXA Client',
        ...existingConfig,
        ...config,
        updatedAt: new Date()
      };

      if (!existingConfig) {
        updatedConfig.createdAt = new Date();
      }

      await setDoc(configRef, updatedConfig);
    } catch (error) {
      console.error('Error updating tenant email config:', error);
      throw error;
    }
  }

  /**
   * Get email quota for tenant
   */
  async getEmailQuota(): Promise<EmailQuota> {
    try {
      const quotaDoc = await getDoc(doc(db, COLLECTIONS.EMAIL_QUOTAS, this.tenantId));
      
      if (quotaDoc.exists()) {
        return quotaDoc.data() as EmailQuota;
      }

      // Create default quota
      const defaultQuota: EmailQuota = {
        tenantId: this.tenantId,
        dailyLimit: 1000,
        monthlyLimit: 10000,
        dailyUsed: 0,
        monthlyUsed: 0,
        lastResetDate: new Date(),
        isBlocked: false
      };

      await setDoc(doc(db, COLLECTIONS.EMAIL_QUOTAS, this.tenantId), defaultQuota);
      return defaultQuota;
    } catch (error) {
      console.error('Error getting email quota:', error);
      throw error;
    }
  }

  /**
   * Check if tenant can send email (quota check)
   */
  async canSendEmail(): Promise<{ allowed: boolean; reason?: string }> {
    try {
      const quota = await this.getEmailQuota();

      if (quota.isBlocked) {
        return {
          allowed: false,
          reason: quota.blockReason || 'Account is blocked from sending emails'
        };
      }

      if (quota.dailyUsed >= quota.dailyLimit) {
        return {
          allowed: false,
          reason: 'Daily email limit exceeded'
        };
      }

      if (quota.monthlyUsed >= quota.monthlyLimit) {
        return {
          allowed: false,
          reason: 'Monthly email limit exceeded'
        };
      }

      return { allowed: true };
    } catch (error) {
      console.error('Error checking email quota:', error);
      return {
        allowed: false,
        reason: 'Error checking email quota'
      };
    }
  }

  /**
   * Increment email usage counter
   */
  async incrementEmailUsage(): Promise<void> {
    try {
      const quotaRef = doc(db, COLLECTIONS.EMAIL_QUOTAS, this.tenantId);
      const quota = await this.getEmailQuota();

      // Check if we need to reset daily counter
      const today = new Date();
      const lastReset = quota.lastResetDate;
      const shouldResetDaily = today.getDate() !== lastReset.getDate() || 
                              today.getMonth() !== lastReset.getMonth() || 
                              today.getFullYear() !== lastReset.getFullYear();

      // Check if we need to reset monthly counter
      const shouldResetMonthly = today.getMonth() !== lastReset.getMonth() || 
                                today.getFullYear() !== lastReset.getFullYear();

      const updatedQuota = {
        ...quota,
        dailyUsed: shouldResetDaily ? 1 : quota.dailyUsed + 1,
        monthlyUsed: shouldResetMonthly ? 1 : quota.monthlyUsed + 1,
        lastResetDate: shouldResetDaily ? today : quota.lastResetDate
      };

      await updateDoc(quotaRef, updatedQuota);
    } catch (error) {
      console.error('Error incrementing email usage:', error);
      throw error;
    }
  }

  /**
   * Get default email service configuration
   */
  private getDefaultConfig(): EmailServiceConfig {
    return {
      provider: 'sendgrid',
      fromEmail: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
      fromName: process.env.SENDGRID_FROM_NAME || 'EVEXA Platform',
      replyToEmail: process.env.SENDGRID_REPLY_TO || '<EMAIL>',
      rateLimitPerMinute: parseInt(process.env.EMAIL_RATE_LIMIT_PER_MINUTE || '100'),
      retryAttempts: parseInt(process.env.EMAIL_RETRY_ATTEMPTS || '3'),
      enableTracking: true,
      enableQueue: process.env.EMAIL_QUEUE_ENABLED === 'true',
      settings: {
        bounceHandling: true,
        spamCompliance: true,
        unsubscribeHandling: true,
        suppressionList: true
      }
    };
  }
}
