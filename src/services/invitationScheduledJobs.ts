/**
 * Invitation Scheduled Jobs Service
 * Handles periodic tasks for invitation management
 */

import { 
  collection, 
  query, 
  where, 
  getDocs, 
  Timestamp,
  writeBatch,
  doc
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { InvitationManagementService } from './invitationManagementService';
import { EnhancedEmailService } from './enhancedEmailService';

// ===== TYPES =====

export interface JobResult {
  success: boolean;
  processed: number;
  errors: string[];
}

export interface ReminderJobResult extends JobResult {
  remindersSent: number;
}

// ===== INVITATION SCHEDULED JOBS SERVICE =====

export class InvitationScheduledJobsService {
  
  /**
   * Process expired invitations across all tenants
   * Should be run daily via cron job or Cloud Function
   */
  static async processExpiredInvitations(): Promise<JobResult> {
    const errors: string[] = [];
    let processed = 0;

    try {
      console.log('Starting expired invitations processing...');

      // Get all pending invitations that have expired
      const now = new Date();
      const q = query(
        collection(db, COLLECTIONS.USER_INVITATIONS),
        where('status', '==', 'pending'),
        where('expiresAt', '<', Timestamp.fromDate(now))
      );

      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        console.log('No expired invitations found');
        return { success: true, processed: 0, errors: [] };
      }

      // Process in batches to avoid Firestore limits
      const batch = writeBatch(db);
      const batchSize = 500;
      let batchCount = 0;

      for (const docSnapshot of snapshot.docs) {
        try {
          const invitationRef = doc(db, COLLECTIONS.USER_INVITATIONS, docSnapshot.id);
          
          batch.update(invitationRef, {
            status: 'expired',
            expiredAt: Timestamp.fromDate(now),
            updatedAt: Timestamp.fromDate(now)
          });

          batchCount++;
          processed++;

          // Commit batch when it reaches the limit
          if (batchCount >= batchSize) {
            await batch.commit();
            batchCount = 0;
          }

        } catch (error) {
          const errorMsg = `Failed to expire invitation ${docSnapshot.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      // Commit remaining items in batch
      if (batchCount > 0) {
        await batch.commit();
      }

      console.log(`Expired invitations processing completed. Processed: ${processed}, Errors: ${errors.length}`);

      return {
        success: errors.length === 0,
        processed,
        errors
      };

    } catch (error) {
      const errorMsg = `Failed to process expired invitations: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error(errorMsg);
      errors.push(errorMsg);

      return {
        success: false,
        processed,
        errors
      };
    }
  }

  /**
   * Send reminder emails for pending invitations
   * Should be run daily or every few days
   */
  static async sendInvitationReminders(): Promise<ReminderJobResult> {
    const errors: string[] = [];
    let processed = 0;
    let remindersSent = 0;

    try {
      console.log('Starting invitation reminders processing...');

      // Get pending invitations that are 3 days old and haven't had a reminder in the last 2 days
      const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000);
      const twoDaysAgo = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000);

      const q = query(
        collection(db, COLLECTIONS.USER_INVITATIONS),
        where('status', '==', 'pending'),
        where('sentAt', '<', Timestamp.fromDate(threeDaysAgo))
      );

      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        console.log('No invitations found for reminders');
        return { success: true, processed: 0, remindersSent: 0, errors: [] };
      }

      // Group invitations by tenant for efficient processing
      const invitationsByTenant = new Map<string, any[]>();

      for (const docSnapshot of snapshot.docs) {
        const data = docSnapshot.data();
        const invitation = {
          id: docSnapshot.id,
          ...data,
          sentAt: data.sentAt?.toDate() || new Date(),
          expiresAt: data.expiresAt?.toDate() || new Date(),
          lastReminderSent: data.lastReminderSent?.toDate(),
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date()
        };

        // Skip if reminder was sent recently
        if (invitation.lastReminderSent && invitation.lastReminderSent > twoDaysAgo) {
          continue;
        }

        // Skip if invitation has expired
        if (invitation.expiresAt < new Date()) {
          continue;
        }

        // Skip if too many reminders have been sent
        if (invitation.reminderCount >= 2) {
          continue;
        }

        if (!invitationsByTenant.has(invitation.tenantId)) {
          invitationsByTenant.set(invitation.tenantId, []);
        }
        invitationsByTenant.get(invitation.tenantId)!.push(invitation);
      }

      // Process reminders for each tenant
      for (const [tenantId, invitations] of invitationsByTenant) {
        try {
          const emailService = new EnhancedEmailService(tenantId);

          for (const invitation of invitations) {
            try {
              processed++;

              // Send reminder email
              const result = await emailService.sendInvitationEmail(
                invitation.email,
                `${invitation.firstName} ${invitation.lastName}`,
                invitation.invitedByName,
                invitation.roleName,
                invitation.invitationUrl,
                invitation.expiresAt.toLocaleDateString()
              );

              if (result.success) {
                // Update invitation with reminder info
                await this.updateInvitationReminder(invitation.id, result.messageId);
                remindersSent++;
                console.log(`Reminder sent to ${invitation.email} for tenant ${tenantId}`);
              } else {
                const errorMsg = `Failed to send reminder to ${invitation.email}: ${result.error}`;
                console.error(errorMsg);
                errors.push(errorMsg);
              }

            } catch (error) {
              const errorMsg = `Failed to process reminder for invitation ${invitation.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
              console.error(errorMsg);
              errors.push(errorMsg);
            }
          }

        } catch (error) {
          const errorMsg = `Failed to process reminders for tenant ${tenantId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      console.log(`Invitation reminders processing completed. Processed: ${processed}, Sent: ${remindersSent}, Errors: ${errors.length}`);

      return {
        success: errors.length === 0,
        processed,
        remindersSent,
        errors
      };

    } catch (error) {
      const errorMsg = `Failed to send invitation reminders: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error(errorMsg);
      errors.push(errorMsg);

      return {
        success: false,
        processed,
        remindersSent: 0,
        errors
      };
    }
  }

  /**
   * Clean up old invitation records
   * Should be run weekly or monthly
   */
  static async cleanupOldInvitations(daysOld: number = 90): Promise<JobResult> {
    const errors: string[] = [];
    let processed = 0;

    try {
      console.log(`Starting cleanup of invitations older than ${daysOld} days...`);

      const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);
      
      // Get old invitations that are not accepted (we keep accepted ones for audit)
      const q = query(
        collection(db, COLLECTIONS.USER_INVITATIONS),
        where('status', 'in', ['expired', 'cancelled']),
        where('createdAt', '<', Timestamp.fromDate(cutoffDate))
      );

      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        console.log('No old invitations found for cleanup');
        return { success: true, processed: 0, errors: [] };
      }

      // Delete in batches
      const batch = writeBatch(db);
      const batchSize = 500;
      let batchCount = 0;

      for (const docSnapshot of snapshot.docs) {
        try {
          batch.delete(doc(db, COLLECTIONS.USER_INVITATIONS, docSnapshot.id));
          batchCount++;
          processed++;

          // Commit batch when it reaches the limit
          if (batchCount >= batchSize) {
            await batch.commit();
            batchCount = 0;
          }

        } catch (error) {
          const errorMsg = `Failed to delete invitation ${docSnapshot.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      // Commit remaining items in batch
      if (batchCount > 0) {
        await batch.commit();
      }

      console.log(`Invitation cleanup completed. Processed: ${processed}, Errors: ${errors.length}`);

      return {
        success: errors.length === 0,
        processed,
        errors
      };

    } catch (error) {
      const errorMsg = `Failed to cleanup old invitations: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error(errorMsg);
      errors.push(errorMsg);

      return {
        success: false,
        processed,
        errors
      };
    }
  }

  /**
   * Update invitation with reminder information
   */
  private static async updateInvitationReminder(invitationId: string, messageId?: string): Promise<void> {
    const invitationRef = doc(db, COLLECTIONS.USER_INVITATIONS, invitationId);
    
    // Get current reminder count
    const invitationDoc = await getDocs(query(
      collection(db, COLLECTIONS.USER_INVITATIONS),
      where('__name__', '==', invitationId)
    ));

    const currentData = invitationDoc.docs[0]?.data();
    const currentReminderCount = currentData?.reminderCount || 0;

    const updates: any = {
      lastReminderSent: Timestamp.fromDate(new Date()),
      reminderCount: currentReminderCount + 1,
      updatedAt: Timestamp.fromDate(new Date())
    };

    if (messageId) {
      updates.lastReminderMessageId = messageId;
    }

    await writeBatch(db).update(invitationRef, updates).commit();
  }

  /**
   * Get job statistics
   */
  static async getJobStatistics(): Promise<{
    pendingInvitations: number;
    expiredInvitations: number;
    invitationsNeedingReminders: number;
    oldInvitationsForCleanup: number;
  }> {
    try {
      const now = new Date();
      const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000);
      const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);

      const [pendingSnapshot, expiredSnapshot, reminderSnapshot, cleanupSnapshot] = await Promise.all([
        // Pending invitations
        getDocs(query(
          collection(db, COLLECTIONS.USER_INVITATIONS),
          where('status', '==', 'pending')
        )),
        
        // Expired invitations that need status update
        getDocs(query(
          collection(db, COLLECTIONS.USER_INVITATIONS),
          where('status', '==', 'pending'),
          where('expiresAt', '<', Timestamp.fromDate(now))
        )),
        
        // Invitations needing reminders
        getDocs(query(
          collection(db, COLLECTIONS.USER_INVITATIONS),
          where('status', '==', 'pending'),
          where('sentAt', '<', Timestamp.fromDate(threeDaysAgo))
        )),
        
        // Old invitations for cleanup
        getDocs(query(
          collection(db, COLLECTIONS.USER_INVITATIONS),
          where('status', 'in', ['expired', 'cancelled']),
          where('createdAt', '<', Timestamp.fromDate(ninetyDaysAgo))
        ))
      ]);

      return {
        pendingInvitations: pendingSnapshot.size,
        expiredInvitations: expiredSnapshot.size,
        invitationsNeedingReminders: reminderSnapshot.size,
        oldInvitationsForCleanup: cleanupSnapshot.size
      };

    } catch (error) {
      console.error('Error getting job statistics:', error);
      return {
        pendingInvitations: 0,
        expiredInvitations: 0,
        invitationsNeedingReminders: 0,
        oldInvitationsForCleanup: 0
      };
    }
  }
}
