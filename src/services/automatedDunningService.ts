/**
 * Automated Dunning Management Service for EVEXA
 * Handles failed payment recovery, smart retry logic, customer communication, and account suspension
 */

import { db } from '@/lib/firebase';
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { stripePaymentService } from './stripePaymentService';
import { subscriptionService } from './subscriptionService';

export interface DunningCampaign {
  id: string;
  tenantId: string;
  customerId: string;
  subscriptionId: string;
  invoiceId: string;
  status: 'active' | 'paused' | 'completed' | 'failed' | 'cancelled';
  
  // Payment Details
  payment: {
    amount: number;
    currency: string;
    dueDate: Date;
    failureReason: string;
    attemptCount: number;
    lastAttemptDate: Date;
    nextRetryDate: Date;
  };
  
  // Campaign Configuration
  config: {
    maxRetries: number;
    retryIntervals: number[]; // Days between retries
    escalationEnabled: boolean;
    suspensionEnabled: boolean;
    gracePeriodDays: number;
  };
  
  // Communication History
  communications: Array<{
    id: string;
    type: 'email' | 'sms' | 'in_app' | 'phone_call';
    template: string;
    sentAt: Date;
    status: 'sent' | 'delivered' | 'opened' | 'clicked' | 'failed';
    response?: string;
  }>;
  
  // Actions Taken
  actions: Array<{
    id: string;
    type: 'retry_payment' | 'send_reminder' | 'escalate' | 'suspend_account' | 'manual_intervention';
    executedAt: Date;
    result: 'success' | 'failed' | 'pending';
    details: string;
    automatedBy?: string;
  }>;
  
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    completedAt?: Date;
    createdBy: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
  };
}

export interface DunningTemplate {
  id: string;
  name: string;
  type: 'email' | 'sms' | 'in_app';
  stage: 'initial' | 'reminder_1' | 'reminder_2' | 'final_notice' | 'suspension_notice' | 'reactivation';
  subject?: string;
  content: string;
  variables: string[]; // Available template variables
  isActive: boolean;
  metadata: {
    createdAt: Date;
    updatedAt: Date;
  };
}

export interface DunningRule {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  
  // Trigger Conditions
  triggers: {
    paymentFailureTypes: string[];
    amountThreshold?: number;
    customerSegments?: string[];
    subscriptionTypes?: string[];
  };
  
  // Campaign Configuration
  campaign: {
    maxRetries: number;
    retryIntervals: number[];
    gracePeriodDays: number;
    escalationEnabled: boolean;
    suspensionEnabled: boolean;
    communicationChannels: ('email' | 'sms' | 'in_app')[];
  };
  
  // Templates for each stage
  templates: {
    initial: string;
    reminder_1: string;
    reminder_2: string;
    final_notice: string;
    suspension_notice: string;
    reactivation: string;
  };
  
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    lastUsed?: Date;
    successRate: number;
  };
}

export interface DunningAnalytics {
  period: { start: Date; end: Date };
  totalCampaigns: number;
  activeCampaigns: number;
  completedCampaigns: number;
  
  recovery: {
    totalRecovered: number;
    recoveryRate: number;
    averageRecoveryTime: number; // days
    recoveryByStage: {
      initial: number;
      reminder_1: number;
      reminder_2: number;
      final_notice: number;
    };
  };
  
  communication: {
    totalSent: number;
    deliveryRate: number;
    openRate: number;
    clickRate: number;
    responseRate: number;
    byChannel: {
      email: { sent: number; delivered: number; opened: number; clicked: number };
      sms: { sent: number; delivered: number; opened: number; clicked: number };
      in_app: { sent: number; delivered: number; opened: number; clicked: number };
    };
  };
  
  suspension: {
    totalSuspensions: number;
    averageSuspensionDuration: number;
    reactivationRate: number;
    churnRate: number;
  };
  
  topFailureReasons: Array<{
    reason: string;
    count: number;
    recoveryRate: number;
  }>;
}

class AutomatedDunningService {
  private campaignsCollection = 'dunning_campaigns';
  private templatesCollection = 'dunning_templates';
  private rulesCollection = 'dunning_rules';
  private analyticsCollection = 'dunning_analytics';

  // Campaign Management
  async createDunningCampaign(
    tenantId: string,
    customerId: string,
    subscriptionId: string,
    invoiceId: string,
    paymentDetails: {
      amount: number;
      currency: string;
      dueDate: Date;
      failureReason: string;
    }
  ): Promise<DunningCampaign> {
    // Get applicable dunning rule
    const rule = await this.getApplicableDunningRule(tenantId, paymentDetails);
    
    const now = new Date();
    const nextRetryDate = new Date(now.getTime() + (rule.campaign.retryIntervals[0] * 24 * 60 * 60 * 1000));
    
    const campaign: Omit<DunningCampaign, 'id'> = {
      tenantId,
      customerId,
      subscriptionId,
      invoiceId,
      status: 'active',
      payment: {
        ...paymentDetails,
        attemptCount: 0,
        lastAttemptDate: now,
        nextRetryDate
      },
      config: {
        maxRetries: rule.campaign.maxRetries,
        retryIntervals: rule.campaign.retryIntervals,
        escalationEnabled: rule.campaign.escalationEnabled,
        suspensionEnabled: rule.campaign.suspensionEnabled,
        gracePeriodDays: rule.campaign.gracePeriodDays
      },
      communications: [],
      actions: [],
      metadata: {
        createdAt: now,
        updatedAt: now,
        createdBy: 'system',
        priority: this.calculatePriority(paymentDetails.amount, paymentDetails.failureReason)
      }
    };

    const docRef = await addDoc(collection(db, this.campaignsCollection), {
      ...campaign,
      'payment.dueDate': Timestamp.fromDate(paymentDetails.dueDate),
      'payment.lastAttemptDate': Timestamp.fromDate(now),
      'payment.nextRetryDate': Timestamp.fromDate(nextRetryDate),
      'metadata.createdAt': Timestamp.fromDate(now),
      'metadata.updatedAt': Timestamp.fromDate(now)
    });

    const createdCampaign = { ...campaign, id: docRef.id };

    // Send initial communication
    await this.sendDunningCommunication(createdCampaign, 'initial');

    return createdCampaign;
  }

  async processDunningCampaigns(): Promise<void> {
    const now = new Date();
    
    // Get active campaigns that need processing
    const q = query(
      collection(db, this.campaignsCollection),
      where('status', '==', 'active'),
      where('payment.nextRetryDate', '<=', Timestamp.fromDate(now))
    );

    const querySnapshot = await getDocs(q);
    const campaigns = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      payment: {
        ...doc.data().payment,
        dueDate: doc.data().payment.dueDate.toDate(),
        lastAttemptDate: doc.data().payment.lastAttemptDate.toDate(),
        nextRetryDate: doc.data().payment.nextRetryDate.toDate()
      },
      metadata: {
        ...doc.data().metadata,
        createdAt: doc.data().metadata.createdAt.toDate(),
        updatedAt: doc.data().metadata.updatedAt.toDate(),
        completedAt: doc.data().metadata.completedAt?.toDate()
      }
    })) as DunningCampaign[];

    for (const campaign of campaigns) {
      await this.processCampaign(campaign);
    }
  }

  private async processCampaign(campaign: DunningCampaign): Promise<void> {
    try {
      // Check if payment has been resolved
      const paymentResolved = await this.checkPaymentStatus(campaign.invoiceId);
      
      if (paymentResolved) {
        await this.completeCampaign(campaign.id, 'Payment resolved');
        return;
      }

      // Determine next action based on attempt count
      const attemptCount = campaign.payment.attemptCount;
      const maxRetries = campaign.config.maxRetries;

      if (attemptCount >= maxRetries) {
        // Max retries reached - escalate or suspend
        if (campaign.config.suspensionEnabled) {
          await this.suspendAccount(campaign);
        } else if (campaign.config.escalationEnabled) {
          await this.escalateCampaign(campaign);
        } else {
          await this.completeCampaign(campaign.id, 'Max retries reached');
        }
        return;
      }

      // Retry payment
      const retryResult = await this.retryPayment(campaign);
      
      if (retryResult.success) {
        await this.completeCampaign(campaign.id, 'Payment successful');
      } else {
        // Payment failed - send reminder and schedule next retry
        await this.handleFailedRetry(campaign, retryResult.error);
      }

    } catch (error) {
      console.error(`Error processing dunning campaign ${campaign.id}:`, error);
      await this.logCampaignAction(campaign.id, 'manual_intervention', 'failed', 
        `Error processing campaign: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async retryPayment(campaign: DunningCampaign): Promise<{ success: boolean; error?: string }> {
    try {
      // Attempt to retry the payment via Stripe
      const result = await stripePaymentService.retryInvoicePayment(campaign.invoiceId);
      
      await this.logCampaignAction(campaign.id, 'retry_payment', 'success', 
        `Payment retry attempt ${campaign.payment.attemptCount + 1}`);
      
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      await this.logCampaignAction(campaign.id, 'retry_payment', 'failed', 
        `Payment retry failed: ${errorMessage}`);
      
      return { success: false, error: errorMessage };
    }
  }

  private async handleFailedRetry(campaign: DunningCampaign, error: string): Promise<void> {
    const attemptCount = campaign.payment.attemptCount + 1;
    const nextRetryIndex = Math.min(attemptCount, campaign.config.retryIntervals.length - 1);
    const nextRetryDays = campaign.config.retryIntervals[nextRetryIndex];
    const nextRetryDate = new Date(Date.now() + (nextRetryDays * 24 * 60 * 60 * 1000));

    // Update campaign
    await updateDoc(doc(db, this.campaignsCollection, campaign.id), {
      'payment.attemptCount': attemptCount,
      'payment.lastAttemptDate': Timestamp.fromDate(new Date()),
      'payment.nextRetryDate': Timestamp.fromDate(nextRetryDate),
      'payment.failureReason': error,
      'metadata.updatedAt': Timestamp.fromDate(new Date())
    });

    // Send appropriate reminder based on attempt count
    const stage = this.getDunningStage(attemptCount, campaign.config.maxRetries);
    await this.sendDunningCommunication({ ...campaign, payment: { ...campaign.payment, attemptCount } }, stage);
  }

  private getDunningStage(attemptCount: number, maxRetries: number): DunningTemplate['stage'] {
    const progress = attemptCount / maxRetries;

    if (progress <= 0.25) return 'reminder_1';
    if (progress <= 0.5) return 'reminder_2';
    if (progress <= 0.75) return 'final_notice';
    return 'suspension_notice';
  }

  // Communication Management
  private async sendDunningCommunication(
    campaign: DunningCampaign,
    stage: DunningTemplate['stage']
  ): Promise<void> {
    try {
      // Get customer details
      const subscription = await subscriptionService.getTenantSubscription(campaign.tenantId);
      if (!subscription) return;

      // Get appropriate template
      const template = await this.getDunningTemplate(stage);
      if (!template) return;

      // Prepare template variables
      const variables = {
        customerName: subscription.billing.customerName || 'Valued Customer',
        companyName: 'EVEXA',
        amount: this.formatCurrency(campaign.payment.amount, campaign.payment.currency),
        dueDate: campaign.payment.dueDate.toLocaleDateString(),
        invoiceId: campaign.invoiceId,
        attemptCount: campaign.payment.attemptCount,
        maxRetries: campaign.config.maxRetries,
        gracePeriodDays: campaign.config.gracePeriodDays,
        paymentUrl: `${process.env.NEXT_PUBLIC_APP_URL}/billing/payment/${campaign.invoiceId}`,
        supportEmail: '<EMAIL>',
        supportPhone: '+****************'
      };

      // Render template content
      const content = this.renderTemplate(template.content, variables);
      const subject = template.subject ? this.renderTemplate(template.subject, variables) : undefined;

      // Send communication based on type
      let communicationResult;
      switch (template.type) {
        case 'email':
          communicationResult = await this.sendEmail(
            subscription.billing.customerEmail || '',
            subject || `Payment Reminder - ${variables.companyName}`,
            content
          );
          break;
        case 'sms':
          communicationResult = await this.sendSMS(
            subscription.billing.customerPhone || '',
            content
          );
          break;
        case 'in_app':
          communicationResult = await this.sendInAppNotification(
            campaign.tenantId,
            subject || 'Payment Reminder',
            content
          );
          break;
      }

      // Log communication
      const communication = {
        id: `comm_${Date.now()}`,
        type: template.type,
        template: template.id,
        sentAt: new Date(),
        status: communicationResult.success ? 'sent' : 'failed'
      };

      await this.addCommunicationToCampaign(campaign.id, communication);
      await this.logCampaignAction(campaign.id, 'send_reminder',
        communicationResult.success ? 'success' : 'failed',
        `${stage} communication via ${template.type}`);

    } catch (error) {
      console.error('Error sending dunning communication:', error);
      await this.logCampaignAction(campaign.id, 'send_reminder', 'failed',
        `Failed to send ${stage} communication: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async sendEmail(to: string, subject: string, content: string): Promise<{ success: boolean; error?: string }> {
    try {
      // This would integrate with your email service (SendGrid, AWS SES, etc.)
      console.log(`Sending email to ${to}: ${subject}`);

      // Simulate email sending
      await new Promise(resolve => setTimeout(resolve, 100));

      return { success: true };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  private async sendSMS(to: string, content: string): Promise<{ success: boolean; error?: string }> {
    try {
      // This would integrate with your SMS service (Twilio, AWS SNS, etc.)
      console.log(`Sending SMS to ${to}: ${content}`);

      // Simulate SMS sending
      await new Promise(resolve => setTimeout(resolve, 100));

      return { success: true };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  private async sendInAppNotification(
    tenantId: string,
    title: string,
    content: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // This would integrate with your in-app notification system
      console.log(`Sending in-app notification to ${tenantId}: ${title}`);

      // Add notification to database
      await addDoc(collection(db, 'notifications'), {
        tenantId,
        title,
        content,
        type: 'payment_reminder',
        priority: 'high',
        read: false,
        createdAt: Timestamp.fromDate(new Date())
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  // Account Suspension Management
  private async suspendAccount(campaign: DunningCampaign): Promise<void> {
    try {
      // Suspend the subscription
      await subscriptionService.suspendSubscription(
        campaign.tenantId,
        `Payment failure - Invoice ${campaign.invoiceId}`
      );

      // Send suspension notice
      await this.sendDunningCommunication(campaign, 'suspension_notice');

      // Log suspension action
      await this.logCampaignAction(campaign.id, 'suspend_account', 'success',
        `Account suspended due to payment failure after ${campaign.payment.attemptCount} attempts`);

      // Update campaign status
      await updateDoc(doc(db, this.campaignsCollection, campaign.id), {
        status: 'completed',
        'metadata.updatedAt': Timestamp.fromDate(new Date()),
        'metadata.completedAt': Timestamp.fromDate(new Date())
      });

      // Schedule reactivation check
      await this.scheduleReactivationCheck(campaign);

    } catch (error) {
      console.error('Error suspending account:', error);
      await this.logCampaignAction(campaign.id, 'suspend_account', 'failed',
        `Failed to suspend account: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async scheduleReactivationCheck(campaign: DunningCampaign): Promise<void> {
    const checkDate = new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)); // Check in 7 days

    await addDoc(collection(db, 'scheduled_tasks'), {
      type: 'reactivation_check',
      tenantId: campaign.tenantId,
      campaignId: campaign.id,
      scheduledFor: Timestamp.fromDate(checkDate),
      status: 'pending',
      createdAt: Timestamp.fromDate(new Date())
    });
  }

  async processReactivationChecks(): Promise<void> {
    const now = new Date();

    const q = query(
      collection(db, 'scheduled_tasks'),
      where('type', '==', 'reactivation_check'),
      where('status', '==', 'pending'),
      where('scheduledFor', '<=', Timestamp.fromDate(now))
    );

    const querySnapshot = await getDocs(q);

    for (const taskDoc of querySnapshot.docs) {
      const task = taskDoc.data();

      try {
        // Check if payment has been resolved
        const campaign = await this.getCampaign(task.campaignId);
        if (!campaign) continue;

        const paymentResolved = await this.checkPaymentStatus(campaign.invoiceId);

        if (paymentResolved) {
          // Reactivate account
          await subscriptionService.reactivateSubscription(task.tenantId);
          await this.sendDunningCommunication(campaign, 'reactivation');

          // Mark task as completed
          await updateDoc(doc(db, 'scheduled_tasks', taskDoc.id), {
            status: 'completed',
            completedAt: Timestamp.fromDate(new Date())
          });
        } else {
          // Reschedule check for another week
          const nextCheck = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000));
          await updateDoc(doc(db, 'scheduled_tasks', taskDoc.id), {
            scheduledFor: Timestamp.fromDate(nextCheck)
          });
        }
      } catch (error) {
        console.error('Error processing reactivation check:', error);
        await updateDoc(doc(db, 'scheduled_tasks', taskDoc.id), {
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Helper Methods
  private async checkPaymentStatus(invoiceId: string): Promise<boolean> {
    try {
      const invoice = await stripePaymentService.getInvoice(invoiceId);
      return invoice.status === 'paid';
    } catch (error) {
      console.error('Error checking payment status:', error);
      return false;
    }
  }

  private async completeCampaign(campaignId: string, reason: string): Promise<void> {
    await updateDoc(doc(db, this.campaignsCollection, campaignId), {
      status: 'completed',
      'metadata.updatedAt': Timestamp.fromDate(new Date()),
      'metadata.completedAt': Timestamp.fromDate(new Date())
    });

    await this.logCampaignAction(campaignId, 'manual_intervention', 'success', reason);
  }

  private async escalateCampaign(campaign: DunningCampaign): Promise<void> {
    // Create a high-priority task for manual intervention
    await addDoc(collection(db, 'manual_tasks'), {
      type: 'dunning_escalation',
      tenantId: campaign.tenantId,
      campaignId: campaign.id,
      priority: 'high',
      description: `Payment failure escalation for invoice ${campaign.invoiceId}`,
      assignedTo: 'billing_team',
      status: 'pending',
      createdAt: Timestamp.fromDate(new Date())
    });

    await this.logCampaignAction(campaign.id, 'escalate', 'success', 'Campaign escalated for manual intervention');
  }

  private async logCampaignAction(
    campaignId: string,
    type: DunningCampaign['actions'][0]['type'],
    result: DunningCampaign['actions'][0]['result'],
    details: string
  ): Promise<void> {
    const action = {
      id: `action_${Date.now()}`,
      type,
      executedAt: new Date(),
      result,
      details,
      automatedBy: 'dunning_service'
    };

    // Add action to campaign
    const campaignRef = doc(db, this.campaignsCollection, campaignId);
    const campaignDoc = await getDoc(campaignRef);

    if (campaignDoc.exists()) {
      const currentActions = campaignDoc.data().actions || [];
      await updateDoc(campaignRef, {
        actions: [...currentActions, {
          ...action,
          executedAt: Timestamp.fromDate(action.executedAt)
        }],
        'metadata.updatedAt': Timestamp.fromDate(new Date())
      });
    }
  }

  private async addCommunicationToCampaign(
    campaignId: string,
    communication: DunningCampaign['communications'][0]
  ): Promise<void> {
    const campaignRef = doc(db, this.campaignsCollection, campaignId);
    const campaignDoc = await getDoc(campaignRef);

    if (campaignDoc.exists()) {
      const currentCommunications = campaignDoc.data().communications || [];
      await updateDoc(campaignRef, {
        communications: [...currentCommunications, {
          ...communication,
          sentAt: Timestamp.fromDate(communication.sentAt)
        }],
        'metadata.updatedAt': Timestamp.fromDate(new Date())
      });
    }
  }

  private async getCampaign(campaignId: string): Promise<DunningCampaign | null> {
    const campaignDoc = await getDoc(doc(db, this.campaignsCollection, campaignId));

    if (!campaignDoc.exists()) return null;

    const data = campaignDoc.data();
    return {
      id: campaignDoc.id,
      ...data,
      payment: {
        ...data.payment,
        dueDate: data.payment.dueDate.toDate(),
        lastAttemptDate: data.payment.lastAttemptDate.toDate(),
        nextRetryDate: data.payment.nextRetryDate.toDate()
      },
      communications: data.communications?.map((comm: any) => ({
        ...comm,
        sentAt: comm.sentAt.toDate()
      })) || [],
      actions: data.actions?.map((action: any) => ({
        ...action,
        executedAt: action.executedAt.toDate()
      })) || [],
      metadata: {
        ...data.metadata,
        createdAt: data.metadata.createdAt.toDate(),
        updatedAt: data.metadata.updatedAt.toDate(),
        completedAt: data.metadata.completedAt?.toDate()
      }
    } as DunningCampaign;
  }

  private async getDunningTemplate(stage: DunningTemplate['stage']): Promise<DunningTemplate | null> {
    const q = query(
      collection(db, this.templatesCollection),
      where('stage', '==', stage),
      where('isActive', '==', true),
      limit(1)
    );

    const querySnapshot = await getDocs(q);
    if (querySnapshot.empty) return null;

    const doc = querySnapshot.docs[0];
    const data = doc.data();

    return {
      id: doc.id,
      ...data,
      metadata: {
        createdAt: data.metadata.createdAt.toDate(),
        updatedAt: data.metadata.updatedAt.toDate()
      }
    } as DunningTemplate;
  }

  private renderTemplate(template: string, variables: Record<string, any>): string {
    let rendered = template;

    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      rendered = rendered.replace(new RegExp(placeholder, 'g'), String(value));
    });

    return rendered;
  }

  private formatCurrency(amount: number, currency: string): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(amount);
  }

  private calculatePriority(amount: number, failureReason: string): DunningCampaign['metadata']['priority'] {
    if (amount >= 1000) return 'critical';
    if (amount >= 500) return 'high';
    if (amount >= 100) return 'medium';
    return 'low';
  }

  private async getApplicableDunningRule(
    tenantId: string,
    paymentDetails: { amount: number; failureReason: string }
  ): Promise<DunningRule> {
    // Get active dunning rules
    const q = query(
      collection(db, this.rulesCollection),
      where('isActive', '==', true)
    );

    const querySnapshot = await getDocs(q);
    const rules = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      metadata: {
        ...doc.data().metadata,
        createdAt: doc.data().metadata.createdAt.toDate(),
        updatedAt: doc.data().metadata.updatedAt.toDate(),
        lastUsed: doc.data().metadata.lastUsed?.toDate()
      }
    })) as DunningRule[];

    // Find the most applicable rule
    const applicableRule = rules.find(rule => {
      // Check amount threshold
      if (rule.triggers.amountThreshold && paymentDetails.amount < rule.triggers.amountThreshold) {
        return false;
      }

      // Check failure type
      if (rule.triggers.paymentFailureTypes.length > 0 &&
          !rule.triggers.paymentFailureTypes.includes(paymentDetails.failureReason)) {
        return false;
      }

      return true;
    });

    // Return applicable rule or default rule
    return applicableRule || this.getDefaultDunningRule();
  }

  private getDefaultDunningRule(): DunningRule {
    return {
      id: 'default',
      name: 'Default Dunning Rule',
      description: 'Default dunning configuration',
      isActive: true,
      triggers: {
        paymentFailureTypes: [],
        amountThreshold: 0
      },
      campaign: {
        maxRetries: 4,
        retryIntervals: [1, 3, 7, 14], // Days
        gracePeriodDays: 3,
        escalationEnabled: true,
        suspensionEnabled: true,
        communicationChannels: ['email', 'in_app']
      },
      templates: {
        initial: 'initial_template',
        reminder_1: 'reminder_1_template',
        reminder_2: 'reminder_2_template',
        final_notice: 'final_notice_template',
        suspension_notice: 'suspension_notice_template',
        reactivation: 'reactivation_template'
      },
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        successRate: 0.75
      }
    };
  }

  // Public API Methods
  async getDunningCampaigns(tenantId: string, status?: DunningCampaign['status']): Promise<DunningCampaign[]> {
    let q = query(
      collection(db, this.campaignsCollection),
      where('tenantId', '==', tenantId),
      orderBy('metadata.createdAt', 'desc')
    );

    if (status) {
      q = query(q, where('status', '==', status));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        payment: {
          ...data.payment,
          dueDate: data.payment.dueDate.toDate(),
          lastAttemptDate: data.payment.lastAttemptDate.toDate(),
          nextRetryDate: data.payment.nextRetryDate.toDate()
        },
        communications: data.communications?.map((comm: any) => ({
          ...comm,
          sentAt: comm.sentAt.toDate()
        })) || [],
        actions: data.actions?.map((action: any) => ({
          ...action,
          executedAt: action.executedAt.toDate()
        })) || [],
        metadata: {
          ...data.metadata,
          createdAt: data.metadata.createdAt.toDate(),
          updatedAt: data.metadata.updatedAt.toDate(),
          completedAt: data.metadata.completedAt?.toDate()
        }
      } as DunningCampaign;
    });
  }
}

export const automatedDunningService = new AutomatedDunningService();
