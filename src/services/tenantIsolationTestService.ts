/**
 * Tenant Isolation Test Service
 * Comprehensive testing service for tenant data isolation and security
 */

import {
  collection,
  doc,
  query,
  where,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  limit,
  orderBy,
  writeBatch,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import type { TenantAwareEntity } from '@/types/firestore';

export interface TenantIsolationTestResult {
  testName: string;
  passed: boolean;
  error?: string;
  details?: string;
  duration: number;
  severity: 'critical' | 'high' | 'medium' | 'low';
  recommendations?: string[];
}

export interface CrossTenantTestData {
  tenantId: string;
  testDocuments: Array<{
    collection: string;
    documentId: string;
    data: any;
  }>;
}

export class TenantIsolationTestService {
  private testTenantIds: string[] = [];
  private testData: Map<string, CrossTenantTestData> = new Map();

  /**
   * Initialize test environment with multiple test tenants
   */
  async initializeTestEnvironment(): Promise<void> {
    // Create test tenant IDs
    this.testTenantIds = [
      `test-tenant-1-${Date.now()}`,
      `test-tenant-2-${Date.now()}`,
      `test-tenant-3-${Date.now()}`
    ];

    // Create test data for each tenant
    for (const tenantId of this.testTenantIds) {
      await this.createTestDataForTenant(tenantId);
    }
  }

  /**
   * Create test data for a specific tenant
   */
  private async createTestDataForTenant(tenantId: string): Promise<void> {
    const testCollections = [
      COLLECTIONS.EXHIBITIONS,
      COLLECTIONS.EXHIBITION_TASKS,
      COLLECTIONS.LEAD_CONTACTS,
      COLLECTIONS.USER_PROFILES,
      COLLECTIONS.VENDOR_PROFILES
    ];

    const testDocuments: Array<{ collection: string; documentId: string; data: any }> = [];

    for (const collectionName of testCollections) {
      try {
        const testDoc = {
          tenantId,
          name: `Test ${collectionName} for ${tenantId}`,
          testData: true,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };

        const docRef = await addDoc(collection(db, collectionName), testDoc);
        
        testDocuments.push({
          collection: collectionName,
          documentId: docRef.id,
          data: testDoc
        });
      } catch (error) {
        console.warn(`Failed to create test document in ${collectionName}:`, error);
      }
    }

    this.testData.set(tenantId, {
      tenantId,
      testDocuments
    });
  }

  /**
   * Test that queries are properly filtered by tenantId
   */
  async testQueryTenantFiltering(): Promise<TenantIsolationTestResult> {
    const startTime = Date.now();
    
    try {
      const violations: string[] = [];
      const testCollections = [
        COLLECTIONS.EXHIBITIONS,
        COLLECTIONS.EXHIBITION_TASKS,
        COLLECTIONS.LEAD_CONTACTS,
        COLLECTIONS.USER_PROFILES
      ];

      for (const collectionName of testCollections) {
        // Test 1: Query without tenantId filter (should be prevented by security rules)
        try {
          const unfiltered = query(collection(db, collectionName), limit(10));
          const unfilteredSnapshot = await getDocs(unfiltered);
          
          // Check if any documents from different tenants are returned
          const crossTenantDocs = unfilteredSnapshot.docs.filter(doc => {
            const data = doc.data();
            return data.tenantId && !this.testTenantIds.includes(data.tenantId);
          });

          if (crossTenantDocs.length > 0) {
            violations.push(`Collection ${collectionName}: Unfiltered query returned ${crossTenantDocs.length} cross-tenant documents`);
          }
        } catch (error) {
          // Security rules blocking unfiltered queries is expected and good
        }

        // Test 2: Query with specific tenantId filter
        for (const tenantId of this.testTenantIds) {
          const filtered = query(
            collection(db, collectionName),
            where('tenantId', '==', tenantId),
            limit(5)
          );
          
          const filteredSnapshot = await getDocs(filtered);
          
          // All returned documents should belong to the specified tenant
          filteredSnapshot.docs.forEach(doc => {
            const data = doc.data();
            if (data.tenantId !== tenantId) {
              violations.push(`Collection ${collectionName}: Document ${doc.id} has tenantId ${data.tenantId} but was returned for tenant ${tenantId}`);
            }
          });
        }
      }

      if (violations.length > 0) {
        return {
          testName: 'Query Tenant Filtering',
          passed: false,
          error: 'Tenant filtering violations detected',
          details: violations.join('; '),
          duration: Date.now() - startTime,
          severity: 'critical',
          recommendations: [
            'Ensure all queries include tenantId filters',
            'Review Firestore security rules',
            'Implement query middleware for automatic tenant filtering'
          ]
        };
      }

      return {
        testName: 'Query Tenant Filtering',
        passed: true,
        details: `Tested ${testCollections.length} collections across ${this.testTenantIds.length} tenants - all properly filtered`,
        duration: Date.now() - startTime,
        severity: 'critical'
      };

    } catch (error) {
      return {
        testName: 'Query Tenant Filtering',
        passed: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime,
        severity: 'critical'
      };
    }
  }

  /**
   * Test that document access is restricted by tenantId
   */
  async testDocumentAccessControl(): Promise<TenantIsolationTestResult> {
    const startTime = Date.now();
    
    try {
      const violations: string[] = [];

      // Test accessing documents from different tenants
      for (const [tenantId, testData] of this.testData.entries()) {
        for (const testDoc of testData.testDocuments) {
          // Try to access this document as if we're from a different tenant
          const otherTenantIds = this.testTenantIds.filter(id => id !== tenantId);
          
          for (const otherTenantId of otherTenantIds) {
            try {
              const docRef = doc(db, testDoc.collection, testDoc.documentId);
              const docSnap = await getDoc(docRef);
              
              if (docSnap.exists()) {
                const data = docSnap.data();
                
                // If we can access a document from another tenant, that's a violation
                if (data.tenantId === tenantId) {
                  violations.push(`Document ${testDoc.documentId} from tenant ${tenantId} accessible by tenant ${otherTenantId}`);
                }
              }
            } catch (error) {
              // Security rules blocking access is expected and good
            }
          }
        }
      }

      if (violations.length > 0) {
        return {
          testName: 'Document Access Control',
          passed: false,
          error: 'Cross-tenant document access violations detected',
          details: violations.join('; '),
          duration: Date.now() - startTime,
          severity: 'critical',
          recommendations: [
            'Implement document-level security rules',
            'Add tenantId validation in document access functions',
            'Review authentication middleware'
          ]
        };
      }

      return {
        testName: 'Document Access Control',
        passed: true,
        details: `Tested document access across ${this.testTenantIds.length} tenants - all properly isolated`,
        duration: Date.now() - startTime,
        severity: 'critical'
      };

    } catch (error) {
      return {
        testName: 'Document Access Control',
        passed: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime,
        severity: 'critical'
      };
    }
  }

  /**
   * Test that write operations are properly validated
   */
  async testWriteOperationValidation(): Promise<TenantIsolationTestResult> {
    const startTime = Date.now();
    
    try {
      const violations: string[] = [];

      // Test 1: Try to create documents without tenantId
      try {
        const invalidDoc = {
          name: 'Invalid Document Without TenantId',
          testData: true,
          createdAt: serverTimestamp()
          // Missing tenantId
        };

        await addDoc(collection(db, COLLECTIONS.EXHIBITIONS), invalidDoc);
        violations.push('Document created without tenantId');
      } catch (error) {
        // Security rules blocking documents without tenantId is expected and good
      }

      // Test 2: Try to create documents with wrong tenantId
      for (const tenantId of this.testTenantIds) {
        const otherTenantIds = this.testTenantIds.filter(id => id !== tenantId);
        
        for (const wrongTenantId of otherTenantIds) {
          try {
            const invalidDoc = {
              tenantId: wrongTenantId,
              name: `Document with wrong tenantId`,
              testData: true,
              createdAt: serverTimestamp()
            };

            // This should fail if security rules are properly configured
            await addDoc(collection(db, COLLECTIONS.EXHIBITIONS), invalidDoc);
            violations.push(`Document created with wrong tenantId: ${wrongTenantId} when authenticated as ${tenantId}`);
          } catch (error) {
            // Security rules blocking wrong tenantId is expected and good
          }
        }
      }

      if (violations.length > 0) {
        return {
          testName: 'Write Operation Validation',
          passed: false,
          error: 'Write operation validation failures detected',
          details: violations.join('; '),
          duration: Date.now() - startTime,
          severity: 'high',
          recommendations: [
            'Implement server-side validation for tenantId',
            'Add security rules to prevent invalid writes',
            'Validate tenantId in all write operations'
          ]
        };
      }

      return {
        testName: 'Write Operation Validation',
        passed: true,
        details: 'All write operations properly validated',
        duration: Date.now() - startTime,
        severity: 'high'
      };

    } catch (error) {
      return {
        testName: 'Write Operation Validation',
        passed: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime,
        severity: 'high'
      };
    }
  }

  /**
   * Run all tenant isolation tests
   */
  async runAllTests(): Promise<TenantIsolationTestResult[]> {
    await this.initializeTestEnvironment();

    const tests = [
      () => this.testQueryTenantFiltering(),
      () => this.testDocumentAccessControl(),
      () => this.testWriteOperationValidation()
    ];

    const results: TenantIsolationTestResult[] = [];

    for (const test of tests) {
      try {
        const result = await test();
        results.push(result);
      } catch (error) {
        results.push({
          testName: 'Unknown Test',
          passed: false,
          error: error instanceof Error ? error.message : 'Test execution failed',
          duration: 0,
          severity: 'high'
        });
      }
    }

    // Clean up test data
    await this.cleanupTestData();

    return results;
  }

  /**
   * Clean up test data
   */
  private async cleanupTestData(): Promise<void> {
    const batch = writeBatch(db);
    let operationCount = 0;

    for (const [tenantId, testData] of this.testData.entries()) {
      for (const testDoc of testData.testDocuments) {
        try {
          const docRef = doc(db, testDoc.collection, testDoc.documentId);
          batch.delete(docRef);
          operationCount++;

          // Firestore batch limit is 500 operations
          if (operationCount >= 450) {
            await batch.commit();
            operationCount = 0;
          }
        } catch (error) {
          console.warn(`Failed to delete test document ${testDoc.documentId}:`, error);
        }
      }
    }

    if (operationCount > 0) {
      await batch.commit();
    }

    this.testData.clear();
    this.testTenantIds = [];
  }
}

export const tenantIsolationTestService = new TenantIsolationTestService();
