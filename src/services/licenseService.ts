/**
 * EVEXA License Validation Service
 * Provides license validation and anti-piracy protection
 */

import { doc, getDoc, setDoc, updateDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// License types
export type LicenseType = 'trial' | 'basic' | 'professional' | 'enterprise' | 'developer';

export interface LicenseInfo {
  id: string;
  type: LicenseType;
  organizationName: string;
  contactEmail: string;
  issuedDate: Date;
  expiryDate: Date;
  maxUsers: number;
  maxTenants: number;
  features: string[];
  isActive: boolean;
  hardwareFingerprint?: string;
  domainRestrictions?: string[];
  ipRestrictions?: string[];
  lastValidated: Date;
  validationCount: number;
}

export interface LicenseValidationResult {
  isValid: boolean;
  license?: LicenseInfo;
  reason?: string;
  remainingDays?: number;
  warnings?: string[];
}

export class LicenseService {
  private static instance: LicenseService;
  private licenseCache: Map<string, { license: LicenseInfo; timestamp: number }> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  static getInstance(): LicenseService {
    if (!LicenseService.instance) {
      LicenseService.instance = new LicenseService();
    }
    return LicenseService.instance;
  }

  /**
   * Validate license for the current deployment
   */
  async validateLicense(licenseKey: string): Promise<LicenseValidationResult> {
    try {
      // Check cache first
      const cached = this.licenseCache.get(licenseKey);
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
        return this.validateCachedLicense(cached.license);
      }

      // Fetch license from database
      const licenseDoc = await getDoc(doc(db, 'licenses', licenseKey));
      
      if (!licenseDoc.exists()) {
        return {
          isValid: false,
          reason: 'License key not found'
        };
      }

      const licenseData = licenseDoc.data();
      const license: LicenseInfo = {
        id: licenseDoc.id,
        type: licenseData.type,
        organizationName: licenseData.organizationName,
        contactEmail: licenseData.contactEmail,
        issuedDate: licenseData.issuedDate.toDate(),
        expiryDate: licenseData.expiryDate.toDate(),
        maxUsers: licenseData.maxUsers,
        maxTenants: licenseData.maxTenants,
        features: licenseData.features || [],
        isActive: licenseData.isActive,
        hardwareFingerprint: licenseData.hardwareFingerprint,
        domainRestrictions: licenseData.domainRestrictions || [],
        ipRestrictions: licenseData.ipRestrictions || [],
        lastValidated: licenseData.lastValidated?.toDate() || new Date(),
        validationCount: licenseData.validationCount || 0
      };

      // Cache the license
      this.licenseCache.set(licenseKey, { license, timestamp: Date.now() });

      // Perform validation checks
      const validationResult = await this.performLicenseValidation(license);

      // Update validation statistics
      if (validationResult.isValid) {
        await this.updateValidationStats(licenseKey);
      }

      return validationResult;

    } catch (error) {
      console.error('License validation error:', error);
      return {
        isValid: false,
        reason: 'License validation failed'
      };
    }
  }

  /**
   * Validate cached license
   */
  private validateCachedLicense(license: LicenseInfo): LicenseValidationResult {
    const now = new Date();
    const remainingDays = Math.ceil((license.expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    if (!license.isActive) {
      return {
        isValid: false,
        reason: 'License is inactive'
      };
    }

    if (license.expiryDate < now) {
      return {
        isValid: false,
        reason: 'License has expired'
      };
    }

    const warnings: string[] = [];
    if (remainingDays <= 30) {
      warnings.push(`License expires in ${remainingDays} days`);
    }

    return {
      isValid: true,
      license,
      remainingDays,
      warnings
    };
  }

  /**
   * Perform comprehensive license validation
   */
  private async performLicenseValidation(license: LicenseInfo): Promise<LicenseValidationResult> {
    const now = new Date();
    const warnings: string[] = [];

    // Check if license is active
    if (!license.isActive) {
      return {
        isValid: false,
        reason: 'License is inactive'
      };
    }

    // Check expiry date
    if (license.expiryDate < now) {
      return {
        isValid: false,
        reason: 'License has expired'
      };
    }

    // Check domain restrictions
    if (license.domainRestrictions && license.domainRestrictions.length > 0) {
      const currentDomain = this.getCurrentDomain();
      if (!license.domainRestrictions.includes(currentDomain)) {
        return {
          isValid: false,
          reason: `License not valid for domain: ${currentDomain}`
        };
      }
    }

    // Check IP restrictions
    if (license.ipRestrictions && license.ipRestrictions.length > 0) {
      const currentIP = await this.getCurrentIP();
      if (currentIP && !license.ipRestrictions.includes(currentIP)) {
        return {
          isValid: false,
          reason: `License not valid for IP: ${currentIP}`
        };
      }
    }

    // Check hardware fingerprint (for on-premise deployments)
    if (license.hardwareFingerprint) {
      const currentFingerprint = await this.generateHardwareFingerprint();
      if (currentFingerprint !== license.hardwareFingerprint) {
        return {
          isValid: false,
          reason: 'Hardware fingerprint mismatch'
        };
      }
    }

    // Calculate remaining days
    const remainingDays = Math.ceil((license.expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    // Add warnings
    if (remainingDays <= 30) {
      warnings.push(`License expires in ${remainingDays} days`);
    }

    if (remainingDays <= 7) {
      warnings.push('License expiring soon - please renew');
    }

    return {
      isValid: true,
      license,
      remainingDays,
      warnings
    };
  }

  /**
   * Get current domain
   */
  private getCurrentDomain(): string {
    if (typeof window !== 'undefined') {
      return window.location.hostname;
    }
    return process.env.VERCEL_URL || process.env.DOMAIN || 'localhost';
  }

  /**
   * Get current IP address
   */
  private async getCurrentIP(): Promise<string | null> {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch {
      return null;
    }
  }

  /**
   * Generate hardware fingerprint for on-premise deployments
   */
  private async generateHardwareFingerprint(): Promise<string> {
    try {
      // In browser environment, use available APIs
      if (typeof window !== 'undefined') {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.textBaseline = 'top';
          ctx.font = '14px Arial';
          ctx.fillText('EVEXA Fingerprint', 2, 2);
          const canvasFingerprint = canvas.toDataURL();
          
          const fingerprint = [
            navigator.userAgent,
            navigator.language,
            screen.width + 'x' + screen.height,
            new Date().getTimezoneOffset(),
            canvasFingerprint.slice(-50) // Last 50 chars of canvas fingerprint
          ].join('|');
          
          // Generate hash
          const encoder = new TextEncoder();
          const data = encoder.encode(fingerprint);
          const hashBuffer = await crypto.subtle.digest('SHA-256', data);
          const hashArray = Array.from(new Uint8Array(hashBuffer));
          return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        }
      }
      
      // Fallback fingerprint
      return 'fallback-fingerprint';
    } catch {
      return 'error-fingerprint';
    }
  }

  /**
   * Update validation statistics
   */
  private async updateValidationStats(licenseKey: string): Promise<void> {
    try {
      const licenseRef = doc(db, 'licenses', licenseKey);
      await updateDoc(licenseRef, {
        lastValidated: Timestamp.now(),
        validationCount: (await getDoc(licenseRef)).data()?.validationCount + 1 || 1
      });
    } catch (error) {
      console.warn('Failed to update validation stats:', error);
    }
  }

  /**
   * Check if feature is enabled in license
   */
  isFeatureEnabled(license: LicenseInfo, feature: string): boolean {
    return license.features.includes(feature) || license.features.includes('*');
  }

  /**
   * Get license type capabilities
   */
  getLicenseCapabilities(type: LicenseType): { maxUsers: number; maxTenants: number; features: string[] } {
    const capabilities = {
      trial: { maxUsers: 5, maxTenants: 1, features: ['basic'] },
      basic: { maxUsers: 25, maxTenants: 1, features: ['basic', 'analytics'] },
      professional: { maxUsers: 100, maxTenants: 3, features: ['basic', 'analytics', 'ai', 'automation'] },
      enterprise: { maxUsers: -1, maxTenants: -1, features: ['*'] },
      developer: { maxUsers: 10, maxTenants: 5, features: ['*'] }
    };

    return capabilities[type] || capabilities.trial;
  }

  /**
   * Clear license cache
   */
  clearCache(): void {
    this.licenseCache.clear();
  }
}

export const licenseService = LicenseService.getInstance();
