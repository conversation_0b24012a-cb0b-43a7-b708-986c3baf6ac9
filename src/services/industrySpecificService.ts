import { doc, getDoc, setDoc, collection, query, where, getDocs, addDoc, Timestamp, orderBy, limit, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// Healthcare Industry Module
export interface HealthcareComplianceWorkflow {
  id: string;
  tenantId: string;
  deviceType: 'Class I' | 'Class II' | 'Class III' | 'IVD' | 'Software';
  deviceName: string;
  manufacturer: string;
  fdaNumber?: string;
  ceMarking?: string;
  isoStandards: string[];
  complianceStatus: 'compliant' | 'pending' | 'non_compliant' | 'expired';
  certifications: HealthcareCertification[];
  auditTrail: HealthcareAuditEntry[];
  riskAssessment: MedicalDeviceRiskAssessment;
  qualityManagement: QualityManagementSystem;
  postMarketSurveillance: PostMarketSurveillanceData;
  createdAt: Date;
  updatedAt: Date;
}

export interface HealthcareCertification {
  id: string;
  type: 'FDA_510K' | 'FDA_PMA' | 'CE_MDR' | 'ISO_13485' | 'ISO_14971' | 'IEC_62304';
  certificationNumber: string;
  issuedBy: string;
  issuedDate: Date;
  expiryDate: Date;
  status: 'active' | 'expired' | 'suspended' | 'pending_renewal';
  documents: string[];
  renewalReminders: Date[];
}

export interface HealthcareAuditEntry {
  id: string;
  timestamp: Date;
  action: string;
  performedBy: string;
  details: string;
  complianceImpact: 'none' | 'low' | 'medium' | 'high' | 'critical';
  documentationRequired: boolean;
  followUpRequired: boolean;
  followUpDate?: Date;
}

export interface MedicalDeviceRiskAssessment {
  id: string;
  deviceId: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  hazardAnalysis: HazardAnalysis[];
  riskControlMeasures: RiskControlMeasure[];
  residualRisk: string;
  riskBenefitAnalysis: string;
  lastReview: Date;
  nextReview: Date;
  approvedBy: string;
}

export interface HazardAnalysis {
  id: string;
  hazardType: string;
  potentialHarm: string;
  severity: 1 | 2 | 3 | 4 | 5;
  probability: 1 | 2 | 3 | 4 | 5;
  riskScore: number;
  mitigationMeasures: string[];
}

export interface RiskControlMeasure {
  id: string;
  hazardId: string;
  controlType: 'inherent_safety' | 'protective_measures' | 'information_for_safety';
  description: string;
  effectiveness: 'high' | 'medium' | 'low';
  verification: string;
  validation: string;
  implementationStatus: 'planned' | 'implemented' | 'verified' | 'validated';
}

export interface QualityManagementSystem {
  id: string;
  iso13485Compliance: boolean;
  documentControl: DocumentControlSystem;
  managementResponsibility: ManagementResponsibility;
  resourceManagement: ResourceManagement;
  productRealization: ProductRealization;
  measurementAnalysis: MeasurementAnalysis;
  lastAudit: Date;
  nextAudit: Date;
  nonConformities: NonConformity[];
}

export interface DocumentControlSystem {
  procedures: ControlledDocument[];
  revisionControl: boolean;
  distributionControl: boolean;
  obsoleteDocumentControl: boolean;
  externalDocumentControl: boolean;
}

export interface ControlledDocument {
  id: string;
  title: string;
  documentNumber: string;
  revision: string;
  effectiveDate: Date;
  reviewDate: Date;
  approvedBy: string;
  distributionList: string[];
  status: 'draft' | 'active' | 'obsolete' | 'under_review';
}

export interface ManagementResponsibility {
  qualityPolicy: string;
  qualityObjectives: QualityObjective[];
  managementReview: ManagementReview[];
  customerFocus: boolean;
  regulatoryRequirements: string[];
}

export interface QualityObjective {
  id: string;
  objective: string;
  measurableTarget: string;
  timeframe: string;
  responsible: string;
  status: 'planned' | 'in_progress' | 'achieved' | 'not_achieved';
  progress: number;
}

export interface ManagementReview {
  id: string;
  reviewDate: Date;
  attendees: string[];
  inputsReviewed: string[];
  decisionsActions: string[];
  followUpItems: FollowUpItem[];
  nextReviewDate: Date;
}

export interface FollowUpItem {
  id: string;
  description: string;
  assignedTo: string;
  dueDate: Date;
  status: 'open' | 'in_progress' | 'completed' | 'overdue';
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export interface ResourceManagement {
  humanResources: HumanResourceRequirement[];
  infrastructure: InfrastructureRequirement[];
  workEnvironment: WorkEnvironmentRequirement[];
  trainingRecords: TrainingRecord[];
}

export interface HumanResourceRequirement {
  role: string;
  competencyRequirements: string[];
  trainingRequirements: string[];
  certificationRequirements: string[];
  currentStaffing: number;
  requiredStaffing: number;
}

export interface InfrastructureRequirement {
  type: 'equipment' | 'facility' | 'software' | 'support_services';
  description: string;
  specifications: string[];
  maintenanceSchedule: MaintenanceSchedule;
  calibrationSchedule?: CalibrationSchedule;
  status: 'operational' | 'maintenance' | 'out_of_service' | 'calibration_due';
}

export interface MaintenanceSchedule {
  frequency: string;
  lastMaintenance: Date;
  nextMaintenance: Date;
  maintenanceProvider: string;
  maintenanceRecords: MaintenanceRecord[];
}

export interface MaintenanceRecord {
  id: string;
  date: Date;
  type: 'preventive' | 'corrective' | 'emergency';
  description: string;
  performedBy: string;
  partsReplaced: string[];
  cost: number;
  downtime: number; // hours
}

export interface CalibrationSchedule {
  frequency: string;
  lastCalibration: Date;
  nextCalibration: Date;
  calibrationProvider: string;
  certificateNumber: string;
  calibrationRecords: CalibrationRecord[];
}

export interface CalibrationRecord {
  id: string;
  date: Date;
  certificateNumber: string;
  performedBy: string;
  results: string;
  deviations: string[];
  adjustmentsMade: string[];
  nextCalibrationDate: Date;
}

export interface WorkEnvironmentRequirement {
  area: string;
  requirements: string[];
  monitoringParameters: EnvironmentalParameter[];
  controlMeasures: string[];
  complianceStatus: 'compliant' | 'non_compliant' | 'monitoring_required';
}

export interface EnvironmentalParameter {
  parameter: string;
  specification: string;
  currentValue: string;
  lastMeasurement: Date;
  measurementFrequency: string;
  outOfSpecificationActions: string[];
}

export interface TrainingRecord {
  employeeId: string;
  employeeName: string;
  trainingType: string;
  trainingDate: Date;
  trainer: string;
  competencyAssessment: CompetencyAssessment;
  certificationExpiry?: Date;
  refresherRequired: boolean;
  refresherDueDate?: Date;
}

export interface CompetencyAssessment {
  assessmentDate: Date;
  assessor: string;
  competencyAreas: CompetencyArea[];
  overallRating: 'competent' | 'developing' | 'not_competent';
  developmentPlan?: string[];
  reassessmentDate?: Date;
}

export interface CompetencyArea {
  area: string;
  rating: 'competent' | 'developing' | 'not_competent';
  evidence: string[];
  improvementActions: string[];
}

export interface ProductRealization {
  designControls: DesignControl[];
  purchasingControls: PurchasingControl[];
  productionControls: ProductionControl[];
  serviceControls: ServiceControl[];
}

export interface DesignControl {
  id: string;
  designPhase: 'planning' | 'input' | 'output' | 'review' | 'verification' | 'validation' | 'transfer' | 'change';
  requirements: string[];
  deliverables: string[];
  reviewCriteria: string[];
  approvalCriteria: string[];
  status: 'planned' | 'in_progress' | 'completed' | 'approved';
  documents: string[];
}

export interface PurchasingControl {
  supplierId: string;
  supplierName: string;
  supplierType: 'critical' | 'important' | 'standard';
  evaluationCriteria: string[];
  approvalStatus: 'approved' | 'conditional' | 'not_approved' | 'under_evaluation';
  auditSchedule: SupplierAudit[];
  performanceMetrics: SupplierPerformanceMetric[];
}

export interface SupplierAudit {
  id: string;
  auditDate: Date;
  auditType: 'initial' | 'surveillance' | 'special' | 'remote';
  auditors: string[];
  scope: string[];
  findings: AuditFinding[];
  overallRating: 'satisfactory' | 'minor_nonconformity' | 'major_nonconformity' | 'critical_nonconformity';
  nextAuditDate: Date;
}

export interface AuditFinding {
  id: string;
  type: 'observation' | 'minor_nonconformity' | 'major_nonconformity' | 'critical_nonconformity';
  description: string;
  requirement: string;
  evidence: string;
  correctiveAction: string;
  preventiveAction: string;
  dueDate: Date;
  status: 'open' | 'in_progress' | 'closed' | 'verified';
}

export interface SupplierPerformanceMetric {
  metric: string;
  target: number;
  actual: number;
  period: string;
  trend: 'improving' | 'stable' | 'declining';
  actions: string[];
}

export interface ProductionControl {
  processId: string;
  processName: string;
  processParameters: ProcessParameter[];
  controlMethods: string[];
  monitoringFrequency: string;
  acceptanceCriteria: string[];
  nonConformanceHandling: NonConformanceHandling;
}

export interface ProcessParameter {
  parameter: string;
  specification: string;
  controlMethod: string;
  monitoringFrequency: string;
  recordingRequirement: string;
  outOfSpecificationActions: string[];
}

export interface NonConformanceHandling {
  identificationMethods: string[];
  evaluationProcess: string;
  dispositionOptions: string[];
  correctiveActionProcess: string;
  preventiveActionProcess: string;
  recordKeepingRequirements: string[];
}

export interface ServiceControl {
  serviceType: string;
  serviceRequirements: string[];
  deliveryMethods: string[];
  performanceIndicators: string[];
  customerFeedbackMethods: string[];
  improvementProcess: string;
}

export interface MeasurementAnalysis {
  customerSatisfaction: CustomerSatisfactionData;
  internalAudits: InternalAudit[];
  processMonitoring: ProcessMonitoringData[];
  productMonitoring: ProductMonitoringData[];
  dataAnalysis: DataAnalysisResult[];
  improvementActions: ImprovementAction[];
}

export interface CustomerSatisfactionData {
  surveyResults: SurveyResult[];
  complaintData: ComplaintData[];
  feedbackTrends: FeedbackTrend[];
  satisfactionScore: number;
  improvementOpportunities: string[];
}

export interface SurveyResult {
  id: string;
  surveyDate: Date;
  respondentType: string;
  questions: SurveyQuestion[];
  overallSatisfaction: number;
  recommendations: string[];
}

export interface SurveyQuestion {
  question: string;
  response: string | number;
  category: string;
}

export interface ComplaintData {
  id: string;
  complaintDate: Date;
  complaintType: string;
  description: string;
  investigation: string;
  rootCause: string;
  correctiveActions: string[];
  preventiveActions: string[];
  status: 'open' | 'investigating' | 'resolved' | 'closed';
  customerSatisfied: boolean;
}

export interface FeedbackTrend {
  category: string;
  trend: 'improving' | 'stable' | 'declining';
  data: TrendDataPoint[];
  actions: string[];
}

export interface TrendDataPoint {
  period: string;
  value: number;
  target: number;
}

export interface InternalAudit {
  id: string;
  auditDate: Date;
  auditScope: string[];
  auditors: string[];
  auditCriteria: string[];
  findings: AuditFinding[];
  overallEffectiveness: 'effective' | 'partially_effective' | 'ineffective';
  improvementOpportunities: string[];
  nextAuditDate: Date;
}

export interface ProcessMonitoringData {
  processId: string;
  processName: string;
  kpis: ProcessKPI[];
  trends: ProcessTrend[];
  controlStatus: 'in_control' | 'out_of_control' | 'monitoring_required';
  improvementOpportunities: string[];
}

export interface ProcessKPI {
  kpi: string;
  target: number;
  actual: number;
  period: string;
  status: 'meeting_target' | 'below_target' | 'above_target';
}

export interface ProcessTrend {
  parameter: string;
  trend: 'improving' | 'stable' | 'declining';
  data: TrendDataPoint[];
  controlLimits: ControlLimit[];
}

export interface ControlLimit {
  type: 'upper_control' | 'lower_control' | 'upper_warning' | 'lower_warning';
  value: number;
}

export interface ProductMonitoringData {
  productId: string;
  productName: string;
  qualityMetrics: QualityMetric[];
  defectRates: DefectRate[];
  customerReturns: CustomerReturn[];
  fieldPerformance: FieldPerformanceData[];
}

export interface QualityMetric {
  metric: string;
  specification: string;
  actual: number;
  period: string;
  status: 'conforming' | 'non_conforming' | 'marginal';
}

export interface DefectRate {
  defectType: string;
  rate: number;
  period: string;
  trend: 'improving' | 'stable' | 'declining';
  rootCauses: string[];
  correctiveActions: string[];
}

export interface CustomerReturn {
  id: string;
  returnDate: Date;
  reason: string;
  productLot: string;
  investigation: string;
  rootCause: string;
  correctiveActions: string[];
  preventiveActions: string[];
  status: 'open' | 'investigating' | 'resolved' | 'closed';
}

export interface FieldPerformanceData {
  metric: string;
  value: number;
  period: string;
  benchmark: number;
  status: 'meeting_expectations' | 'below_expectations' | 'exceeding_expectations';
}

export interface DataAnalysisResult {
  analysisType: string;
  analysisDate: Date;
  dataSource: string[];
  methodology: string;
  findings: string[];
  conclusions: string[];
  recommendations: string[];
  actionItems: ActionItem[];
}

export interface ActionItem {
  id: string;
  description: string;
  assignedTo: string;
  dueDate: Date;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'completed' | 'overdue';
  resources: string[];
}

export interface ImprovementAction {
  id: string;
  type: 'corrective' | 'preventive' | 'improvement';
  description: string;
  rootCause: string;
  proposedSolution: string;
  implementationPlan: ImplementationStep[];
  assignedTo: string;
  dueDate: Date;
  status: 'planned' | 'in_progress' | 'implemented' | 'verified' | 'closed';
  effectiveness: 'effective' | 'partially_effective' | 'ineffective' | 'not_evaluated';
}

export interface ImplementationStep {
  id: string;
  step: string;
  assignedTo: string;
  dueDate: Date;
  status: 'planned' | 'in_progress' | 'completed';
  resources: string[];
  dependencies: string[];
}

export interface NonConformity {
  id: string;
  identificationDate: Date;
  identifiedBy: string;
  description: string;
  severity: 'minor' | 'major' | 'critical';
  area: string;
  requirement: string;
  immediateAction: string;
  rootCauseAnalysis: RootCauseAnalysis;
  correctiveAction: CorrectiveAction;
  preventiveAction: PreventiveAction;
  status: 'open' | 'investigating' | 'implementing' | 'verifying' | 'closed';
  verificationDate?: Date;
  verifiedBy?: string;
  effectiveness: 'effective' | 'partially_effective' | 'ineffective' | 'not_evaluated';
}

export interface RootCauseAnalysis {
  methodology: string;
  analysis: string;
  rootCauses: string[];
  contributingFactors: string[];
  evidence: string[];
}

export interface CorrectiveAction {
  description: string;
  assignedTo: string;
  dueDate: Date;
  implementationSteps: ImplementationStep[];
  status: 'planned' | 'in_progress' | 'implemented' | 'verified';
  verificationMethod: string;
  verificationCriteria: string[];
}

export interface PreventiveAction {
  description: string;
  assignedTo: string;
  dueDate: Date;
  implementationSteps: ImplementationStep[];
  status: 'planned' | 'in_progress' | 'implemented' | 'verified';
  verificationMethod: string;
  verificationCriteria: string[];
  monitoringPlan: string;
}

export interface PostMarketSurveillanceData {
  id: string;
  deviceId: string;
  surveillancePlan: SurveillancePlan;
  adverseEvents: AdverseEvent[];
  fieldSafetyNotices: FieldSafetyNotice[];
  periodicSafetyUpdates: PeriodicSafetyUpdate[];
  riskBenefitAssessment: RiskBenefitAssessment;
  regulatoryReporting: RegulatoryReporting[];
}

export interface SurveillancePlan {
  objectives: string[];
  methods: string[];
  dataCollection: DataCollectionMethod[];
  reportingSchedule: string;
  reviewSchedule: string;
  responsiblePersons: string[];
}

export interface DataCollectionMethod {
  method: string;
  frequency: string;
  dataSource: string;
  responsiblePerson: string;
  reportingCriteria: string[];
}

export interface AdverseEvent {
  id: string;
  reportDate: Date;
  eventDate: Date;
  reportedBy: string;
  patientInfo: PatientInfo;
  deviceInfo: DeviceInfo;
  eventDescription: string;
  severity: 'death' | 'serious_injury' | 'malfunction' | 'other';
  investigation: AdverseEventInvestigation;
  regulatoryReporting: RegulatoryReporting[];
  correctiveActions: string[];
  preventiveActions: string[];
  status: 'reported' | 'investigating' | 'closed';
}

export interface PatientInfo {
  age?: number;
  gender?: string;
  medicalHistory?: string[];
  concomitantMedications?: string[];
  relevantConditions?: string[];
}

export interface DeviceInfo {
  serialNumber: string;
  lotNumber: string;
  modelNumber: string;
  softwareVersion?: string;
  implantDate?: Date;
  deviceHistory?: string[];
}

export interface AdverseEventInvestigation {
  investigator: string;
  investigationDate: Date;
  methodology: string;
  findings: string[];
  rootCause: string;
  deviceAnalysis: string;
  conclusions: string[];
  recommendations: string[];
}

export interface FieldSafetyNotice {
  id: string;
  noticeDate: Date;
  noticeType: 'urgent' | 'important' | 'informational';
  affectedDevices: string[];
  safetyIssue: string;
  riskAssessment: string;
  recommendedActions: string[];
  distributionList: string[];
  acknowledgmentRequired: boolean;
  acknowledgmentDeadline?: Date;
  followUpRequired: boolean;
  followUpDate?: Date;
}

export interface PeriodicSafetyUpdate {
  id: string;
  reportingPeriod: {
    startDate: Date;
    endDate: Date;
  };
  devicesCovered: string[];
  safetyData: SafetyDataSummary;
  riskBenefitAnalysis: string;
  conclusions: string[];
  recommendedActions: string[];
  nextReportDue: Date;
  submittedTo: string[];
  submissionDate: Date;
}

export interface SafetyDataSummary {
  totalDevicesInField: number;
  adverseEventsReported: number;
  seriousAdverseEvents: number;
  deviceMalfunctions: number;
  safetyTrends: SafetyTrend[];
  comparativeData: ComparativeData[];
}

export interface SafetyTrend {
  parameter: string;
  trend: 'improving' | 'stable' | 'worsening';
  data: TrendDataPoint[];
  analysis: string;
}

export interface ComparativeData {
  comparison: string;
  ourData: number;
  benchmarkData: number;
  analysis: string;
}

export interface RiskBenefitAssessment {
  assessmentDate: Date;
  assessor: string;
  clinicalBenefits: string[];
  identifiedRisks: string[];
  riskMitigationMeasures: string[];
  overallAssessment: 'favorable' | 'acceptable' | 'unfavorable';
  recommendations: string[];
  nextAssessmentDate: Date;
}

export interface RegulatoryReporting {
  id: string;
  reportType: string;
  regulatoryBody: string;
  reportingDeadline: Date;
  submissionDate?: Date;
  reportNumber?: string;
  status: 'required' | 'submitted' | 'acknowledged' | 'follow_up_required';
  followUpActions: string[];
  documents: string[];
}

// Automotive Industry Module
export interface AutomotiveComplianceWorkflow {
  id: string;
  tenantId: string;
  vehicleType: 'passenger_car' | 'commercial_vehicle' | 'motorcycle' | 'electric_vehicle' | 'autonomous_vehicle';
  componentType: 'engine' | 'transmission' | 'braking_system' | 'safety_system' | 'electronics' | 'body' | 'chassis';
  manufacturer: string;
  modelNumber: string;
  safetyStandards: AutomotiveSafetyStandard[];
  certifications: AutomotiveCertification[];
  testingResults: AutomotiveTestResult[];
  qualityAssurance: AutomotiveQualityAssurance;
  recallManagement: RecallManagement;
  environmentalCompliance: EnvironmentalCompliance;
  cybersecurityCompliance: CybersecurityCompliance;
  createdAt: Date;
  updatedAt: Date;
}

export interface AutomotiveSafetyStandard {
  id: string;
  standard: 'ISO_26262' | 'FMVSS' | 'ECE_R' | 'NCAP' | 'IIHS' | 'ASIL' | 'SIL';
  description: string;
  applicableComponents: string[];
  complianceLevel: 'A' | 'B' | 'C' | 'D' | 'QM';
  assessmentDate: Date;
  assessor: string;
  complianceStatus: 'compliant' | 'non_compliant' | 'pending_assessment' | 'conditional';
  evidence: string[];
  nextAssessment: Date;
}

export interface AutomotiveCertification {
  id: string;
  type: 'type_approval' | 'homologation' | 'crash_test' | 'emissions' | 'cybersecurity' | 'functional_safety';
  certificationBody: string;
  certificateNumber: string;
  issuedDate: Date;
  expiryDate: Date;
  scope: string[];
  conditions: string[];
  status: 'active' | 'expired' | 'suspended' | 'pending_renewal';
  renewalProcess: RenewalProcess;
}

export interface RenewalProcess {
  renewalRequired: boolean;
  renewalStartDate: Date;
  renewalDeadline: Date;
  requiredDocuments: string[];
  requiredTests: string[];
  estimatedCost: number;
  responsiblePerson: string;
  status: 'not_started' | 'in_progress' | 'submitted' | 'approved' | 'rejected';
}

export interface AutomotiveTestResult {
  id: string;
  testType: 'crash_test' | 'emissions_test' | 'durability_test' | 'performance_test' | 'cybersecurity_test' | 'functional_safety_test';
  testStandard: string;
  testDate: Date;
  testFacility: string;
  testConditions: TestCondition[];
  results: TestMeasurement[];
  passFailStatus: 'pass' | 'fail' | 'conditional_pass' | 'retest_required';
  deviations: TestDeviation[];
  correctiveActions: string[];
  retestRequired: boolean;
  retestDate?: Date;
  certificationImpact: 'none' | 'minor' | 'major' | 'critical';
}

export interface TestCondition {
  parameter: string;
  specifiedValue: string;
  actualValue: string;
  tolerance: string;
  withinTolerance: boolean;
}

export interface TestMeasurement {
  measurement: string;
  value: number;
  unit: string;
  requirement: string;
  result: 'pass' | 'fail' | 'marginal';
  notes: string;
}

export interface TestDeviation {
  id: string;
  description: string;
  severity: 'minor' | 'major' | 'critical';
  impact: string;
  rootCause: string;
  correctiveAction: string;
  preventiveAction: string;
  implementationDate: Date;
  verificationRequired: boolean;
  verificationDate?: Date;
}

export interface AutomotiveQualityAssurance {
  id: string;
  iatf16949Compliance: boolean;
  productionPartApproval: PPAP;
  statisticalProcessControl: SPC;
  measurementSystemAnalysis: MSA;
  failureModeAnalysis: FMEA;
  controlPlan: ControlPlan;
  layeredProcessAudit: LPA;
  supplierQuality: SupplierQuality;
}

export interface PPAP {
  level: 1 | 2 | 3 | 4 | 5;
  submissionDate: Date;
  approvalDate?: Date;
  status: 'submitted' | 'approved' | 'rejected' | 'conditional_approval';
  requiredDocuments: PPAPDocument[];
  customerRequirements: string[];
  deviations: string[];
  correctiveActions: string[];
}

export interface PPAPDocument {
  documentType: string;
  documentNumber: string;
  revision: string;
  submissionDate: Date;
  approvalStatus: 'approved' | 'rejected' | 'pending' | 'conditional';
  comments: string[];
}

export interface SPC {
  processId: string;
  processName: string;
  controlCharts: ControlChart[];
  processCapability: ProcessCapability;
  controlLimits: ControlLimit[];
  outOfControlActions: string[];
  lastReview: Date;
  nextReview: Date;
}

export interface ControlChart {
  chartType: 'X-bar_R' | 'X-bar_S' | 'I-MR' | 'p' | 'np' | 'c' | 'u';
  characteristic: string;
  sampleSize: number;
  frequency: string;
  controlLimits: {
    upperControlLimit: number;
    lowerControlLimit: number;
    centerLine: number;
  };
  dataPoints: ChartDataPoint[];
  outOfControlPoints: OutOfControlPoint[];
}

export interface ChartDataPoint {
  timestamp: Date;
  value: number;
  sampleNumber: number;
  operator: string;
  notes?: string;
}

export interface OutOfControlPoint {
  timestamp: Date;
  value: number;
  rule: string;
  investigation: string;
  correctiveAction: string;
  preventiveAction: string;
}

export interface ProcessCapability {
  cp: number;
  cpk: number;
  pp: number;
  ppk: number;
  sigma: number;
  target: number;
  upperSpecLimit: number;
  lowerSpecLimit: number;
  interpretation: string;
  improvementActions: string[];
}

export interface MSA {
  studyType: 'GRR' | 'bias' | 'linearity' | 'stability';
  characteristic: string;
  measurementSystem: string;
  studyDate: Date;
  operators: string[];
  parts: string[];
  results: MSAResult;
  acceptanceCriteria: MSAAcceptanceCriteria;
  recommendations: string[];
  followUpActions: string[];
}

export interface MSAResult {
  repeatability: number;
  reproducibility: number;
  grr: number;
  partVariation: number;
  totalVariation: number;
  studyVariation: number;
  discrimination: number;
}

export interface MSAAcceptanceCriteria {
  grrLimit: number;
  repeatabilityLimit: number;
  reproducibilityLimit: number;
  discriminationLimit: number;
  status: 'acceptable' | 'marginal' | 'unacceptable';
}

export interface FMEA {
  id: string;
  type: 'DFMEA' | 'PFMEA' | 'SFMEA';
  product: string;
  process: string;
  team: string[];
  revisionDate: Date;
  failureModes: FailureMode[];
  riskPriorityNumber: number;
  actionPlan: FMEAActionPlan[];
}

export interface FailureMode {
  id: string;
  function: string;
  failureMode: string;
  effects: string[];
  causes: string[];
  currentControls: string[];
  severity: number;
  occurrence: number;
  detection: number;
  rpn: number;
  recommendedActions: string[];
  responsibility: string;
  targetDate: Date;
  actionsTaken: string[];
  newSeverity?: number;
  newOccurrence?: number;
  newDetection?: number;
  newRPN?: number;
}

export interface FMEAActionPlan {
  id: string;
  failureModeId: string;
  action: string;
  assignedTo: string;
  targetDate: Date;
  status: 'planned' | 'in_progress' | 'completed' | 'verified';
  effectiveness: 'effective' | 'partially_effective' | 'ineffective' | 'not_evaluated';
  verificationMethod: string;
  verificationDate?: Date;
}

export interface ControlPlan {
  id: string;
  product: string;
  process: string;
  revision: string;
  date: Date;
  controlItems: ControlItem[];
  approvedBy: string;
  effectiveDate: Date;
  nextReview: Date;
}

export interface ControlItem {
  id: string;
  processStep: string;
  characteristic: string;
  specification: string;
  controlMethod: string;
  sampleSize: string;
  frequency: string;
  responsiblePerson: string;
  reactionPlan: string;
  recordingRequirement: string;
}

export interface LPA {
  id: string;
  processArea: string;
  auditDate: Date;
  auditor: string;
  layer: 1 | 2 | 3 | 4 | 5;
  questions: LPAQuestion[];
  overallScore: number;
  findings: string[];
  correctiveActions: string[];
  nextAuditDate: Date;
}

export interface LPAQuestion {
  id: string;
  question: string;
  expectedAnswer: string;
  actualAnswer: string;
  score: number;
  evidence: string;
  nonConformance?: string;
  correctiveAction?: string;
}

export interface SupplierQuality {
  supplierId: string;
  supplierName: string;
  qualityRating: number;
  performanceMetrics: SupplierMetric[];
  auditResults: SupplierAuditResult[];
  developmentPrograms: SupplierDevelopmentProgram[];
  qualityAgreements: QualityAgreement[];
}

export interface SupplierMetric {
  metric: string;
  target: number;
  actual: number;
  period: string;
  trend: 'improving' | 'stable' | 'declining';
  status: 'meeting_target' | 'below_target' | 'above_target';
}

export interface SupplierAuditResult {
  auditDate: Date;
  auditType: string;
  score: number;
  findings: string[];
  correctiveActions: string[];
  followUpDate: Date;
  status: 'open' | 'closed' | 'in_progress';
}

export interface SupplierDevelopmentProgram {
  programName: string;
  objectives: string[];
  activities: string[];
  timeline: string;
  resources: string[];
  progress: number;
  status: 'planned' | 'active' | 'completed' | 'on_hold';
}

export interface QualityAgreement {
  agreementNumber: string;
  effectiveDate: Date;
  expiryDate: Date;
  requirements: string[];
  performanceTargets: string[];
  penalties: string[];
  incentives: string[];
  status: 'active' | 'expired' | 'under_negotiation';
}

export interface RecallManagement {
  id: string;
  recallNumber?: string;
  recallType: 'safety' | 'emissions' | 'voluntary' | 'regulatory';
  severity: 'low' | 'medium' | 'high' | 'critical';
  affectedVehicles: AffectedVehicle[];
  defectDescription: string;
  safetyRisk: string;
  rootCause: string;
  correctiveAction: string;
  notificationPlan: NotificationPlan;
  remedyPlan: RemedyPlan;
  timeline: RecallTimeline;
  costs: RecallCosts;
  status: 'investigation' | 'decision_pending' | 'announced' | 'in_progress' | 'completed';
}

export interface AffectedVehicle {
  vin: string;
  model: string;
  year: number;
  productionDate: Date;
  customerInfo: CustomerInfo;
  remedyStatus: 'pending' | 'scheduled' | 'completed' | 'customer_declined';
  remedyDate?: Date;
}

export interface CustomerInfo {
  name: string;
  address: string;
  phone: string;
  email: string;
  preferredContact: 'phone' | 'email' | 'mail';
  notificationSent: boolean;
  notificationDate?: Date;
  responseReceived: boolean;
  responseDate?: Date;
}

export interface NotificationPlan {
  regulatoryNotification: RegulatoryNotification[];
  customerNotification: CustomerNotification;
  dealerNotification: DealerNotification;
  mediaStrategy: MediaStrategy;
}

export interface RegulatoryNotification {
  authority: string;
  notificationDate: Date;
  reportNumber: string;
  acknowledgmentReceived: boolean;
  followUpRequired: boolean;
  followUpDate?: Date;
}

export interface CustomerNotification {
  method: 'mail' | 'email' | 'phone' | 'multiple';
  notificationDate: Date;
  messageContent: string;
  responseRate: number;
  customerQuestions: string[];
  customerConcerns: string[];
}

export interface DealerNotification {
  notificationDate: Date;
  trainingProvided: boolean;
  trainingDate?: Date;
  partsAvailability: Date;
  serviceCapacity: number;
  dealerFeedback: string[];
}

export interface MediaStrategy {
  pressRelease: boolean;
  pressReleaseDate?: Date;
  mediaContacts: string[];
  keyMessages: string[];
  spokespersonAssigned: string;
  mediaQueries: MediaQuery[];
}

export interface MediaQuery {
  date: Date;
  outlet: string;
  query: string;
  response: string;
  followUpRequired: boolean;
}

export interface RemedyPlan {
  remedyType: 'repair' | 'replace' | 'refund' | 'software_update';
  remedyDescription: string;
  partsRequired: string[];
  laborTime: number;
  serviceInstructions: string;
  dealerTraining: boolean;
  specialTools: string[];
  safetyPrecautions: string[];
  qualityChecks: string[];
}

export interface RecallTimeline {
  investigationStart: Date;
  decisionDate: Date;
  regulatoryNotification: Date;
  customerNotification: Date;
  remedyAvailable: Date;
  targetCompletion: Date;
  actualCompletion?: Date;
  milestones: RecallMilestone[];
}

export interface RecallMilestone {
  milestone: string;
  plannedDate: Date;
  actualDate?: Date;
  status: 'planned' | 'in_progress' | 'completed' | 'delayed';
  delayReason?: string;
  impact: string;
}

export interface RecallCosts {
  investigationCosts: number;
  notificationCosts: number;
  partsCosts: number;
  laborCosts: number;
  logisticsCosts: number;
  regulatoryCosts: number;
  legalCosts: number;
  totalEstimatedCost: number;
  actualCosts: number;
  costBreakdown: CostBreakdown[];
}

export interface CostBreakdown {
  category: string;
  estimated: number;
  actual: number;
  variance: number;
  explanation: string;
}

export interface EnvironmentalCompliance {
  id: string;
  emissionsStandards: EmissionsStandard[];
  fuelEconomyStandards: FuelEconomyStandard[];
  recyclingRequirements: RecyclingRequirement[];
  hazardousSubstances: HazardousSubstanceCompliance[];
  lifecycleAssessment: LifecycleAssessment;
  carbonFootprint: CarbonFootprint;
  environmentalManagementSystem: EnvironmentalManagementSystem;
}

export interface EmissionsStandard {
  standard: 'Euro_6' | 'EPA_Tier_3' | 'CARB_LEV_III' | 'China_6' | 'BS_VI';
  pollutant: 'NOx' | 'CO' | 'HC' | 'PM' | 'CO2';
  limit: number;
  unit: string;
  testProcedure: string;
  testResults: EmissionsTestResult[];
  complianceStatus: 'compliant' | 'non_compliant' | 'pending_test';
  certificationDate?: Date;
  expiryDate?: Date;
}

export interface EmissionsTestResult {
  testDate: Date;
  testFacility: string;
  testConditions: string;
  measuredValue: number;
  limit: number;
  result: 'pass' | 'fail';
  deteriorationFactor: number;
  notes: string;
}

export interface FuelEconomyStandard {
  standard: 'CAFE' | 'EU_CO2' | 'China_CAFC' | 'Japan_Top_Runner';
  targetValue: number;
  unit: string;
  modelYear: number;
  actualValue?: number;
  complianceStatus: 'compliant' | 'non_compliant' | 'pending_test';
  testProcedure: string;
  credits: number;
  penalties: number;
}

export interface RecyclingRequirement {
  regulation: 'EU_ELV' | 'China_ELV' | 'Japan_ELV' | 'Korea_ELV';
  material: string;
  recyclingRate: number;
  targetRate: number;
  complianceStatus: 'compliant' | 'non_compliant' | 'monitoring';
  certificationRequired: boolean;
  certificationDate?: Date;
  recyclingPartners: string[];
}

export interface HazardousSubstanceCompliance {
  regulation: 'RoHS' | 'REACH' | 'GADSL' | 'IMDS';
  substance: string;
  threshold: number;
  actualContent: number;
  unit: string;
  complianceStatus: 'compliant' | 'non_compliant' | 'exemption_applied';
  exemptionNumber?: string;
  alternativeSubstance?: string;
  phaseOutPlan?: string;
}

export interface LifecycleAssessment {
  id: string;
  product: string;
  assessmentDate: Date;
  methodology: string;
  systemBoundaries: string;
  functionalUnit: string;
  impactCategories: ImpactCategory[];
  hotspots: string[];
  improvementOpportunities: string[];
  nextAssessment: Date;
}

export interface ImpactCategory {
  category: string;
  value: number;
  unit: string;
  contributingFactors: ContributingFactor[];
}

export interface ContributingFactor {
  factor: string;
  contribution: number;
  percentage: number;
}

export interface CarbonFootprint {
  id: string;
  scope1Emissions: number;
  scope2Emissions: number;
  scope3Emissions: number;
  totalEmissions: number;
  unit: string;
  reportingPeriod: string;
  verificationStatus: 'verified' | 'unverified' | 'pending_verification';
  reductionTargets: ReductionTarget[];
  offsetPrograms: OffsetProgram[];
}

export interface ReductionTarget {
  target: string;
  baselineYear: number;
  targetYear: number;
  reductionPercentage: number;
  currentProgress: number;
  status: 'on_track' | 'behind_schedule' | 'ahead_of_schedule' | 'achieved';
}

export interface OffsetProgram {
  programName: string;
  offsetType: string;
  offsetAmount: number;
  cost: number;
  verificationStandard: string;
  retirementDate: Date;
}

export interface EnvironmentalManagementSystem {
  standard: 'ISO_14001' | 'EMAS' | 'Other';
  certificationDate: Date;
  expiryDate: Date;
  certificationBody: string;
  scope: string;
  environmentalPolicy: string;
  objectives: EnvironmentalObjective[];
  auditResults: EnvironmentalAuditResult[];
  nonConformances: EnvironmentalNonConformance[];
}

export interface EnvironmentalObjective {
  objective: string;
  target: string;
  timeline: string;
  responsible: string;
  progress: number;
  status: 'planned' | 'in_progress' | 'achieved' | 'not_achieved';
}

export interface EnvironmentalAuditResult {
  auditDate: Date;
  auditType: 'internal' | 'external' | 'surveillance' | 'recertification';
  auditor: string;
  findings: string[];
  opportunities: string[];
  nonConformances: string[];
  overallRating: 'satisfactory' | 'minor_nonconformity' | 'major_nonconformity';
}

export interface EnvironmentalNonConformance {
  id: string;
  description: string;
  severity: 'minor' | 'major' | 'critical';
  rootCause: string;
  correctiveAction: string;
  preventiveAction: string;
  dueDate: Date;
  status: 'open' | 'in_progress' | 'closed' | 'verified';
}

export interface CybersecurityCompliance {
  id: string;
  standards: CybersecurityStandard[];
  threatAssessment: ThreatAssessment;
  vulnerabilityManagement: VulnerabilityManagement;
  incidentResponse: IncidentResponse;
  securityTesting: SecurityTesting[];
  certifications: CybersecurityCertification[];
  monitoringSystem: SecurityMonitoringSystem;
}

export interface CybersecurityStandard {
  standard: 'ISO_21434' | 'SAE_J3061' | 'UNECE_WP29' | 'NIST_CSF' | 'IEC_62443';
  applicablePhases: string[];
  requirements: SecurityRequirement[];
  complianceLevel: string;
  assessmentDate: Date;
  nextAssessment: Date;
  complianceStatus: 'compliant' | 'non_compliant' | 'partial_compliance';
}

export interface SecurityRequirement {
  id: string;
  requirement: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  implementationStatus: 'not_started' | 'in_progress' | 'implemented' | 'verified';
  evidence: string[];
  gaps: string[];
  mitigationPlan: string;
}

export interface ThreatAssessment {
  id: string;
  assessmentDate: Date;
  methodology: string;
  assets: SecurityAsset[];
  threats: SecurityThreat[];
  riskMatrix: RiskMatrix[];
  mitigationStrategies: MitigationStrategy[];
  nextAssessment: Date;
}

export interface SecurityAsset {
  id: string;
  name: string;
  type: 'hardware' | 'software' | 'data' | 'communication' | 'physical';
  criticality: 'low' | 'medium' | 'high' | 'critical';
  securityProperties: SecurityProperty[];
  threats: string[];
  vulnerabilities: string[];
}

export interface SecurityProperty {
  property: 'confidentiality' | 'integrity' | 'availability' | 'authenticity' | 'authorization' | 'non_repudiation';
  level: 'low' | 'medium' | 'high' | 'very_high';
  rationale: string;
}

export interface SecurityThreat {
  id: string;
  name: string;
  description: string;
  category: string;
  likelihood: 'very_low' | 'low' | 'medium' | 'high' | 'very_high';
  impact: 'negligible' | 'minor' | 'moderate' | 'major' | 'severe';
  riskLevel: 'very_low' | 'low' | 'medium' | 'high' | 'very_high';
  affectedAssets: string[];
  attackVectors: string[];
  mitigations: string[];
}

export interface RiskMatrix {
  assetId: string;
  threatId: string;
  likelihood: string;
  impact: string;
  riskLevel: string;
  acceptableRisk: boolean;
  mitigationRequired: boolean;
  mitigationPlan: string;
}

export interface MitigationStrategy {
  id: string;
  strategy: string;
  type: 'preventive' | 'detective' | 'corrective' | 'recovery';
  applicableThreats: string[];
  implementationCost: number;
  effectiveness: 'low' | 'medium' | 'high' | 'very_high';
  implementationStatus: 'planned' | 'in_progress' | 'implemented' | 'verified';
  verificationMethod: string;
}

export interface VulnerabilityManagement {
  id: string;
  vulnerabilityDatabase: Vulnerability[];
  scanningSchedule: ScanningSchedule;
  patchManagement: PatchManagement;
  disclosurePolicy: DisclosurePolicy;
  monitoringSources: string[];
}

export interface Vulnerability {
  id: string;
  cveId?: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  cvssScore: number;
  affectedComponents: string[];
  discoveryDate: Date;
  disclosureDate?: Date;
  patchAvailable: boolean;
  patchDate?: Date;
  workaround?: string;
  status: 'open' | 'patched' | 'mitigated' | 'accepted_risk';
  mitigationPlan: string;
}

export interface ScanningSchedule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  scanTypes: string[];
  coverage: string[];
  lastScan: Date;
  nextScan: Date;
  scanResults: ScanResult[];
}

export interface ScanResult {
  scanDate: Date;
  scanType: string;
  vulnerabilitiesFound: number;
  criticalVulnerabilities: number;
  highVulnerabilities: number;
  mediumVulnerabilities: number;
  lowVulnerabilities: number;
  falsePositives: number;
  remediated: number;
}

export interface PatchManagement {
  patchingPolicy: string;
  testingProcedure: string;
  rollbackProcedure: string;
  emergencyPatchingProcess: string;
  patchSchedule: PatchSchedule[];
  patchHistory: PatchHistory[];
}

export interface PatchSchedule {
  severity: string;
  maxTimeToTest: number;
  maxTimeToDeploy: number;
  approvalRequired: boolean;
  testingEnvironment: string;
  rollbackPlan: string;
}

export interface PatchHistory {
  patchId: string;
  vulnerabilityId: string;
  patchDate: Date;
  testingCompleted: boolean;
  deploymentSuccessful: boolean;
  rollbackRequired: boolean;
  issues: string[];
  lessons: string[];
}

export interface DisclosurePolicy {
  coordinatedDisclosure: boolean;
  disclosureTimeline: number; // days
  contactInformation: string;
  bugBountyProgram: boolean;
  publicDisclosurePolicy: string;
  legalRequirements: string[];
}

export interface IncidentResponse {
  id: string;
  responseTeam: ResponseTeam;
  incidentCategories: IncidentCategory[];
  responsePlaybooks: ResponsePlaybook[];
  incidentHistory: SecurityIncident[];
  communicationPlan: CommunicationPlan;
  lessonsLearned: LessonsLearned[];
}

export interface ResponseTeam {
  teamLead: string;
  members: TeamMember[];
  externalContacts: ExternalContact[];
  escalationMatrix: EscalationLevel[];
  trainingSchedule: TrainingSchedule;
}

export interface TeamMember {
  name: string;
  role: string;
  responsibilities: string[];
  contactInfo: string;
  backupPerson: string;
  availability: string;
}

export interface ExternalContact {
  organization: string;
  contactPerson: string;
  contactInfo: string;
  serviceType: string;
  escalationCriteria: string;
}

export interface EscalationLevel {
  level: number;
  criteria: string;
  timeframe: number; // minutes
  notificationList: string[];
  actions: string[];
}

export interface TrainingSchedule {
  frequency: string;
  lastTraining: Date;
  nextTraining: Date;
  trainingTopics: string[];
  simulationExercises: boolean;
  externalTraining: boolean;
}

export interface IncidentCategory {
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  responseTime: number; // minutes
  escalationCriteria: string;
  containmentActions: string[];
  investigationProcedure: string;
  recoveryProcedure: string;
}

export interface ResponsePlaybook {
  incidentType: string;
  severity: string;
  responseSteps: ResponseStep[];
  roles: string[];
  tools: string[];
  communicationTemplates: string[];
  legalRequirements: string[];
}

export interface ResponseStep {
  step: number;
  action: string;
  responsible: string;
  timeframe: number; // minutes
  dependencies: string[];
  successCriteria: string;
  escalationTrigger: string;
}

export interface SecurityIncident {
  id: string;
  incidentDate: Date;
  detectionDate: Date;
  incidentType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  affectedSystems: string[];
  impactAssessment: ImpactAssessment;
  responseActions: ResponseAction[];
  containmentDate?: Date;
  resolutionDate?: Date;
  status: 'detected' | 'contained' | 'investigating' | 'resolved' | 'closed';
  lessonsLearned: string[];
  preventiveMeasures: string[];
}

export interface ImpactAssessment {
  confidentialityImpact: 'none' | 'low' | 'medium' | 'high';
  integrityImpact: 'none' | 'low' | 'medium' | 'high';
  availabilityImpact: 'none' | 'low' | 'medium' | 'high';
  businessImpact: string;
  financialImpact: number;
  reputationalImpact: string;
  legalImpact: string;
  customersAffected: number;
  dataCompromised: boolean;
  dataTypes: string[];
}

export interface ResponseAction {
  action: string;
  performedBy: string;
  timestamp: Date;
  result: string;
  effectiveness: 'effective' | 'partially_effective' | 'ineffective';
  followUpRequired: boolean;
}

export interface CommunicationPlan {
  internalCommunication: InternalCommunication;
  externalCommunication: ExternalCommunication;
  regulatoryCommunication: RegulatoryCommunication;
  customerCommunication: CustomerCommunication;
  mediaCommunication: MediaCommunication;
}

export interface InternalCommunication {
  notificationList: string[];
  communicationChannels: string[];
  updateFrequency: string;
  escalationCriteria: string;
  messageTemplates: string[];
}

export interface ExternalCommunication {
  stakeholders: string[];
  communicationTriggers: string[];
  approvalProcess: string;
  legalReview: boolean;
  messageTemplates: string[];
}

export interface RegulatoryCommunication {
  applicableRegulations: string[];
  reportingRequirements: string[];
  reportingTimelines: number[];
  contactInformation: string[];
  reportingTemplates: string[];
}

export interface CustomerCommunication {
  notificationCriteria: string;
  communicationChannels: string[];
  messageContent: string;
  supportResources: string[];
  compensationPolicy: string;
}

export interface MediaCommunication {
  mediaPolicy: string;
  spokespersonAssigned: string;
  keyMessages: string[];
  pressReleaseTemplate: string;
  mediaTraining: boolean;
}

export interface LessonsLearned {
  incidentId: string;
  lessonsIdentified: string[];
  improvementActions: string[];
  processChanges: string[];
  trainingNeeds: string[];
  technologyChanges: string[];
  implementationStatus: 'planned' | 'in_progress' | 'implemented' | 'verified';
}

export interface SecurityTesting {
  id: string;
  testType: 'penetration_test' | 'vulnerability_scan' | 'code_review' | 'red_team' | 'social_engineering';
  testDate: Date;
  testScope: string[];
  testMethodology: string;
  testTeam: string[];
  findings: SecurityFinding[];
  recommendations: string[];
  retestRequired: boolean;
  retestDate?: Date;
  complianceMapping: string[];
}

export interface SecurityFinding {
  id: string;
  title: string;
  description: string;
  severity: 'informational' | 'low' | 'medium' | 'high' | 'critical';
  cvssScore?: number;
  affectedSystems: string[];
  exploitability: 'none' | 'low' | 'medium' | 'high';
  businessImpact: string;
  recommendation: string;
  remediation: string;
  status: 'open' | 'in_progress' | 'resolved' | 'accepted_risk';
  dueDate: Date;
  assignedTo: string;
}

export interface CybersecurityCertification {
  id: string;
  certificationType: 'ISO_27001' | 'SOC_2' | 'Common_Criteria' | 'FIPS_140' | 'Custom';
  certificationBody: string;
  certificateNumber: string;
  issuedDate: Date;
  expiryDate: Date;
  scope: string[];
  conditions: string[];
  status: 'active' | 'expired' | 'suspended' | 'pending_renewal';
  auditResults: CertificationAuditResult[];
  surveillanceSchedule: SurveillanceSchedule;
}

export interface CertificationAuditResult {
  auditDate: Date;
  auditType: 'initial' | 'surveillance' | 'recertification' | 'special';
  auditor: string;
  findings: string[];
  nonConformities: string[];
  opportunities: string[];
  overallResult: 'certified' | 'conditional' | 'not_certified';
  correctiveActions: string[];
  nextAuditDate: Date;
}

export interface SurveillanceSchedule {
  frequency: string;
  lastSurveillance: Date;
  nextSurveillance: Date;
  surveillanceScope: string[];
  surveillanceResults: SurveillanceResult[];
}

export interface SurveillanceResult {
  surveillanceDate: Date;
  scope: string[];
  findings: string[];
  nonConformities: string[];
  correctiveActions: string[];
  certificateStatus: 'maintained' | 'suspended' | 'withdrawn';
}

export interface SecurityMonitoringSystem {
  id: string;
  monitoringTools: MonitoringTool[];
  alertRules: AlertRule[];
  dashboards: SecurityDashboard[];
  reportingSchedule: SecurityReportingSchedule;
  metricsTracking: SecurityMetric[];
  threatIntelligence: ThreatIntelligence;
}

export interface MonitoringTool {
  toolName: string;
  toolType: 'SIEM' | 'IDS' | 'IPS' | 'DLP' | 'EDR' | 'Network_Monitor' | 'Log_Analyzer';
  vendor: string;
  version: string;
  deploymentDate: Date;
  coverage: string[];
  configuration: string;
  integrations: string[];
  status: 'active' | 'inactive' | 'maintenance';
}

export interface AlertRule {
  ruleId: string;
  ruleName: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  conditions: string[];
  actions: string[];
  escalationRules: string[];
  falsePositiveRate: number;
  tuningHistory: TuningHistory[];
}

export interface TuningHistory {
  tuningDate: Date;
  changes: string[];
  reason: string;
  impact: string;
  performedBy: string;
}

export interface SecurityDashboard {
  dashboardName: string;
  audience: string[];
  metrics: string[];
  refreshFrequency: string;
  alertThresholds: AlertThreshold[];
  customizations: string[];
}

export interface AlertThreshold {
  metric: string;
  warningThreshold: number;
  criticalThreshold: number;
  escalationActions: string[];
}

export interface SecurityReportingSchedule {
  reportType: string;
  frequency: string;
  recipients: string[];
  content: string[];
  deliveryMethod: string;
  lastReport: Date;
  nextReport: Date;
}

export interface SecurityMetric {
  metricName: string;
  description: string;
  calculationMethod: string;
  target: number;
  actual: number;
  trend: 'improving' | 'stable' | 'declining';
  reportingFrequency: string;
  dataSource: string[];
}

export interface ThreatIntelligence {
  sources: ThreatIntelligenceSource[];
  feeds: ThreatFeed[];
  indicators: ThreatIndicator[];
  campaigns: ThreatCampaign[];
  analysis: ThreatAnalysis[];
}

export interface ThreatIntelligenceSource {
  sourceName: string;
  sourceType: 'commercial' | 'open_source' | 'government' | 'industry' | 'internal';
  reliability: 'A' | 'B' | 'C' | 'D' | 'E' | 'F';
  credibility: 'confirmed' | 'probably_true' | 'possibly_true' | 'doubtful' | 'improbable' | 'cannot_judge';
  coverage: string[];
  updateFrequency: string;
  cost: number;
}

export interface ThreatFeed {
  feedName: string;
  feedType: 'IOC' | 'TTPs' | 'vulnerabilities' | 'malware' | 'campaigns';
  format: 'STIX' | 'TAXII' | 'JSON' | 'XML' | 'CSV';
  updateFrequency: string;
  lastUpdate: Date;
  recordCount: number;
  qualityScore: number;
}

export interface ThreatIndicator {
  indicatorId: string;
  indicatorType: 'IP' | 'domain' | 'URL' | 'hash' | 'email' | 'file' | 'registry';
  value: string;
  confidence: 'high' | 'medium' | 'low';
  severity: 'low' | 'medium' | 'high' | 'critical';
  firstSeen: Date;
  lastSeen: Date;
  source: string;
  context: string;
  relatedIndicators: string[];
}

export interface ThreatCampaign {
  campaignId: string;
  campaignName: string;
  description: string;
  threatActor: string;
  targets: string[];
  ttps: string[];
  indicators: string[];
  timeline: CampaignTimeline[];
  attribution: Attribution;
}

export interface CampaignTimeline {
  date: Date;
  event: string;
  description: string;
  evidence: string[];
}

export interface Attribution {
  confidence: 'high' | 'medium' | 'low';
  threatActor: string;
  motivation: string[];
  capabilities: string[];
  infrastructure: string[];
  evidence: string[];
}

export interface ThreatAnalysis {
  analysisId: string;
  analysisDate: Date;
  analyst: string;
  subject: string;
  keyFindings: string[];
  implications: string[];
  recommendations: string[];
  confidence: 'high' | 'medium' | 'low';
  sources: string[];
  distribution: string[];
}

// Technology Industry Module
export interface TechnologyComplianceWorkflow {
  id: string;
  tenantId: string;
  productType: 'software' | 'hardware' | 'cloud_service' | 'mobile_app' | 'iot_device' | 'ai_system';
  productName: string;
  vendor: string;
  version: string;
  intellectualPropertyProtection: IPProtection;
  securityCompliance: TechSecurityCompliance;
  dataPrivacyCompliance: DataPrivacyCompliance;
  accessibilityCompliance: AccessibilityCompliance;
  softwareLicensing: SoftwareLicensing;
  cloudCompliance: CloudCompliance;
  aiEthicsCompliance: AIEthicsCompliance;
  createdAt: Date;
  updatedAt: Date;
}

export interface IPProtection {
  id: string;
  patents: Patent[];
  trademarks: Trademark[];
  copyrights: Copyright[];
  tradeSecrets: TradeSecret[];
  licensingAgreements: LicensingAgreement[];
  infringementMonitoring: InfringementMonitoring;
  ipStrategy: IPStrategy;
}

export interface Patent {
  id: string;
  patentNumber: string;
  title: string;
  description: string;
  inventors: string[];
  filingDate: Date;
  grantDate?: Date;
  expiryDate: Date;
  jurisdiction: string[];
  status: 'pending' | 'granted' | 'expired' | 'abandoned' | 'rejected';
  claims: string[];
  priorArt: string[];
  maintenanceFees: MaintenanceFee[];
  licensingOpportunities: string[];
}

export interface MaintenanceFee {
  dueDate: Date;
  amount: number;
  currency: string;
  paidDate?: Date;
  status: 'due' | 'paid' | 'overdue' | 'waived';
}

export interface Trademark {
  id: string;
  trademarkNumber: string;
  mark: string;
  description: string;
  classes: string[];
  filingDate: Date;
  registrationDate?: Date;
  expiryDate: Date;
  jurisdiction: string[];
  status: 'pending' | 'registered' | 'expired' | 'abandoned' | 'opposed';
  renewalSchedule: RenewalSchedule[];
  usageGuidelines: string[];
}

export interface RenewalSchedule {
  renewalDate: Date;
  renewalPeriod: string;
  fees: number;
  currency: string;
  status: 'upcoming' | 'completed' | 'overdue';
}

export interface Copyright {
  id: string;
  workTitle: string;
  workType: 'software' | 'documentation' | 'design' | 'content' | 'multimedia';
  authors: string[];
  creationDate: Date;
  registrationDate?: Date;
  registrationNumber?: string;
  jurisdiction: string[];
  duration: string;
  licensingTerms: string[];
  usageRestrictions: string[];
}

export interface TradeSecret {
  id: string;
  secretName: string;
  description: string;
  category: 'algorithm' | 'formula' | 'process' | 'design' | 'customer_list' | 'business_method';
  confidentialityLevel: 'restricted' | 'confidential' | 'highly_confidential' | 'top_secret';
  accessControls: AccessControl[];
  protectionMeasures: ProtectionMeasure[];
  disclosureAgreements: DisclosureAgreement[];
  valuationDate: Date;
  estimatedValue: number;
}

export interface AccessControl {
  userId: string;
  userName: string;
  accessLevel: 'read' | 'write' | 'admin';
  accessReason: string;
  grantedDate: Date;
  expiryDate?: Date;
  accessHistory: AccessHistory[];
}

export interface AccessHistory {
  accessDate: Date;
  accessType: 'view' | 'download' | 'modify' | 'share';
  ipAddress: string;
  device: string;
  duration: number; // minutes
}

export interface ProtectionMeasure {
  measureType: 'technical' | 'physical' | 'administrative' | 'legal';
  description: string;
  implementationDate: Date;
  effectiveness: 'high' | 'medium' | 'low';
  cost: number;
  reviewDate: Date;
  status: 'active' | 'inactive' | 'under_review';
}

export interface DisclosureAgreement {
  agreementId: string;
  agreementType: 'NDA' | 'employee_agreement' | 'contractor_agreement' | 'partner_agreement';
  parties: string[];
  effectiveDate: Date;
  expiryDate?: Date;
  scope: string[];
  obligations: string[];
  penalties: string[];
  status: 'active' | 'expired' | 'terminated' | 'breached';
}

export interface LicensingAgreement {
  id: string;
  agreementType: 'inbound' | 'outbound' | 'cross_license';
  licensor: string;
  licensee: string;
  licensedIP: string[];
  licenseScope: string[];
  territory: string[];
  duration: string;
  royaltyTerms: RoyaltyTerms;
  restrictions: string[];
  terminationConditions: string[];
  status: 'active' | 'expired' | 'terminated' | 'under_negotiation';
}

export interface RoyaltyTerms {
  royaltyType: 'fixed' | 'percentage' | 'per_unit' | 'milestone' | 'hybrid';
  rate: number;
  minimumRoyalty: number;
  maximumRoyalty?: number;
  paymentSchedule: string;
  reportingRequirements: string[];
  auditRights: boolean;
}

export interface InfringementMonitoring {
  id: string;
  monitoringScope: string[];
  monitoringTools: string[];
  monitoringFrequency: string;
  alertCriteria: string[];
  infringementCases: InfringementCase[];
  enforcementActions: EnforcementAction[];
  budgetAllocated: number;
  lastReview: Date;
}

export interface InfringementCase {
  caseId: string;
  infringerName: string;
  infringedIP: string[];
  discoveryDate: Date;
  infringementType: 'direct' | 'indirect' | 'contributory' | 'willful';
  evidenceCollected: string[];
  damageAssessment: DamageAssessment;
  legalStrategy: string;
  status: 'investigating' | 'cease_desist_sent' | 'negotiating' | 'litigation' | 'settled' | 'closed';
  resolution: string;
}

export interface DamageAssessment {
  lostRevenue: number;
  infringerProfits: number;
  reasonableRoyalty: number;
  additionalDamages: number;
  totalDamages: number;
  calculationMethod: string;
  expertOpinion: string;
}

export interface EnforcementAction {
  actionId: string;
  actionType: 'cease_desist' | 'licensing_negotiation' | 'litigation' | 'customs_enforcement' | 'takedown_notice';
  targetParty: string;
  actionDate: Date;
  description: string;
  outcome: string;
  cost: number;
  effectiveness: 'successful' | 'partially_successful' | 'unsuccessful' | 'pending';
  followUpRequired: boolean;
}

export interface IPStrategy {
  strategyDocument: string;
  objectives: string[];
  budgetAllocation: BudgetAllocation[];
  portfolioAnalysis: PortfolioAnalysis;
  competitorAnalysis: CompetitorAnalysis[];
  riskAssessment: IPRiskAssessment;
  performanceMetrics: IPPerformanceMetric[];
  reviewSchedule: string;
}

export interface BudgetAllocation {
  category: 'filing' | 'prosecution' | 'maintenance' | 'enforcement' | 'licensing' | 'monitoring';
  allocatedAmount: number;
  spentAmount: number;
  remainingAmount: number;
  utilizationRate: number;
}

export interface PortfolioAnalysis {
  totalAssets: number;
  assetsByType: AssetCount[];
  assetsByJurisdiction: AssetCount[];
  assetsByStatus: AssetCount[];
  portfolioValue: number;
  maintenanceCosts: number;
  roi: number;
  strengthAssessment: string;
}

export interface AssetCount {
  category: string;
  count: number;
  percentage: number;
  value: number;
}

export interface CompetitorAnalysis {
  competitorName: string;
  portfolioSize: number;
  keyPatents: string[];
  filingTrends: FilingTrend[];
  litigationHistory: string[];
  licensingActivity: string[];
  threatLevel: 'low' | 'medium' | 'high' | 'critical';
  opportunities: string[];
  threats: string[];
}

export interface FilingTrend {
  year: number;
  filings: number;
  technologyAreas: string[];
  trend: 'increasing' | 'stable' | 'decreasing';
}

export interface IPRiskAssessment {
  riskCategories: IPRiskCategory[];
  overallRiskLevel: 'low' | 'medium' | 'high' | 'critical';
  mitigationStrategies: string[];
  contingencyPlans: string[];
  insuranceCoverage: InsuranceCoverage[];
  lastAssessment: Date;
  nextAssessment: Date;
}

export interface IPRiskCategory {
  category: 'infringement' | 'invalidity' | 'freedom_to_operate' | 'trade_secret_theft' | 'counterfeiting';
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  probability: number;
  impact: number;
  mitigationMeasures: string[];
  monitoringActivities: string[];
}

export interface InsuranceCoverage {
  policyType: 'IP_liability' | 'IP_enforcement' | 'cyber_liability' | 'errors_omissions';
  insurer: string;
  policyNumber: string;
  coverageAmount: number;
  deductible: number;
  effectiveDate: Date;
  expiryDate: Date;
  exclusions: string[];
  claims: InsuranceClaim[];
}

export interface InsuranceClaim {
  claimId: string;
  claimDate: Date;
  claimType: string;
  claimAmount: number;
  status: 'submitted' | 'investigating' | 'approved' | 'denied' | 'settled';
  resolution: string;
  payout: number;
}

export interface IPPerformanceMetric {
  metric: string;
  target: number;
  actual: number;
  period: string;
  trend: 'improving' | 'stable' | 'declining';
  benchmarkData: number;
  actionItems: string[];
}

export interface TechSecurityCompliance {
  id: string;
  securityFrameworks: TechSecurityFramework[];
  vulnerabilityManagement: TechVulnerabilityManagement;
  secureCodePractices: SecureCodePractices;
  penetrationTesting: PenetrationTesting[];
  securityTraining: SecurityTraining[];
  incidentResponse: TechIncidentResponse;
  complianceAudits: SecurityComplianceAudit[];
}

export interface TechSecurityFramework {
  framework: 'NIST_CSF' | 'ISO_27001' | 'SOC_2' | 'OWASP' | 'CIS_Controls' | 'SANS_Top_20';
  implementationLevel: 'basic' | 'intermediate' | 'advanced' | 'expert';
  controls: SecurityControl[];
  assessmentDate: Date;
  nextAssessment: Date;
  complianceScore: number;
  gaps: string[];
  improvementPlan: string[];
}

export interface SecurityControl {
  controlId: string;
  controlName: string;
  description: string;
  category: string;
  implementationStatus: 'not_implemented' | 'partially_implemented' | 'implemented' | 'optimized';
  effectiveness: 'low' | 'medium' | 'high' | 'very_high';
  evidence: string[];
  lastReview: Date;
  nextReview: Date;
  responsiblePerson: string;
}

export interface TechVulnerabilityManagement {
  id: string;
  scanningTools: VulnerabilityScanner[];
  vulnerabilityDatabase: TechVulnerability[];
  patchManagementProcess: TechPatchManagement;
  riskAssessmentProcess: VulnerabilityRiskAssessment;
  reportingSchedule: VulnerabilityReporting;
  metrics: VulnerabilityMetric[];
}

export interface VulnerabilityScanner {
  scannerName: string;
  scannerType: 'SAST' | 'DAST' | 'IAST' | 'SCA' | 'Infrastructure' | 'Container';
  vendor: string;
  version: string;
  scanFrequency: string;
  coverage: string[];
  integrations: string[];
  lastScan: Date;
  nextScan: Date;
}

export interface TechVulnerability {
  vulnerabilityId: string;
  title: string;
  description: string;
  severity: 'informational' | 'low' | 'medium' | 'high' | 'critical';
  cvssScore: number;
  cweId?: string;
  affectedComponents: string[];
  discoveryDate: Date;
  reportedBy: string;
  status: 'open' | 'in_progress' | 'resolved' | 'accepted_risk' | 'false_positive';
  assignedTo: string;
  dueDate: Date;
  resolutionDate?: Date;
  resolutionMethod: string;
  testingRequired: boolean;
  retestDate?: Date;
}

export interface TechPatchManagement {
  patchingPolicy: string;
  testingEnvironments: TestingEnvironment[];
  deploymentProcess: DeploymentProcess;
  rollbackProcedure: string;
  emergencyPatchProcess: string;
  patchSchedule: TechPatchSchedule[];
  approvalWorkflow: ApprovalWorkflow;
}

export interface TestingEnvironment {
  environmentName: string;
  environmentType: 'development' | 'staging' | 'pre_production' | 'production';
  testingScope: string[];
  testingDuration: number; // hours
  approvalCriteria: string[];
  rollbackCapability: boolean;
}

export interface DeploymentProcess {
  deploymentStages: DeploymentStage[];
  automationLevel: 'manual' | 'semi_automated' | 'fully_automated';
  monitoringDuring: boolean;
  rollbackTriggers: string[];
  communicationPlan: string;
}

export interface DeploymentStage {
  stageName: string;
  stageOrder: number;
  duration: number; // hours
  prerequisites: string[];
  activities: string[];
  successCriteria: string[];
  rollbackProcedure: string;
}

export interface TechPatchSchedule {
  severity: 'low' | 'medium' | 'high' | 'critical';
  maxTestingTime: number; // hours
  maxDeploymentTime: number; // hours
  approvalRequired: boolean;
  communicationRequired: boolean;
  maintenanceWindow: string;
}

export interface ApprovalWorkflow {
  approvalLevels: ApprovalLevel[];
  escalationRules: string[];
  timeoutActions: string[];
  emergencyBypass: boolean;
  auditTrail: boolean;
}

export interface ApprovalLevel {
  level: number;
  approvers: string[];
  criteria: string[];
  timeoutPeriod: number; // hours
  escalationAction: string;
}

export interface VulnerabilityRiskAssessment {
  riskMatrix: VulnerabilityRiskMatrix[];
  businessImpactAssessment: BusinessImpactAssessment;
  threatLandscape: ThreatLandscape;
  riskAcceptanceCriteria: string[];
  riskTreatmentOptions: string[];
}

export interface VulnerabilityRiskMatrix {
  severity: string;
  exploitability: string;
  businessImpact: string;
  riskLevel: 'very_low' | 'low' | 'medium' | 'high' | 'very_high';
  treatmentRequired: boolean;
  maxAcceptableTime: number; // days
}

export interface BusinessImpactAssessment {
  impactCategories: ImpactCategory[];
  criticalAssets: string[];
  businessProcesses: string[];
  complianceRequirements: string[];
  reputationalImpact: string;
}

export interface ThreatLandscape {
  threatActors: string[];
  attackVectors: string[];
  currentThreats: string[];
  emergingThreats: string[];
  industryTrends: string[];
  threatIntelligenceSources: string[];
}

export interface VulnerabilityReporting {
  reportTypes: VulnerabilityReportType[];
  reportingFrequency: string;
  recipients: string[];
  escalationCriteria: string[];
  metricsIncluded: string[];
}

export interface VulnerabilityReportType {
  reportName: string;
  audience: string;
  content: string[];
  format: string;
  deliveryMethod: string;
  frequency: string;
}

export interface VulnerabilityMetric {
  metricName: string;
  description: string;
  target: number;
  actual: number;
  trend: 'improving' | 'stable' | 'worsening';
  reportingPeriod: string;
  benchmarkData?: number;
}

export interface SecureCodePractices {
  id: string;
  codingStandards: CodingStandard[];
  securityGuidelines: SecurityGuideline[];
  codeReviewProcess: CodeReviewProcess;
  staticAnalysis: StaticAnalysisTools[];
  dynamicAnalysis: DynamicAnalysisTools[];
  dependencyManagement: DependencyManagement;
  securityTesting: SecurityTestingPractices;
}

export interface CodingStandard {
  standardName: string;
  version: string;
  applicableLanguages: string[];
  securityRules: SecurityRule[];
  complianceLevel: 'basic' | 'intermediate' | 'advanced';
  enforcementMethods: string[];
  trainingRequired: boolean;
}

export interface SecurityRule {
  ruleId: string;
  ruleName: string;
  description: string;
  category: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  examples: string[];
  remediation: string;
  automatedCheck: boolean;
}

export interface SecurityGuideline {
  guidelineName: string;
  scope: string[];
  securityPrinciples: string[];
  implementationGuidance: string[];
  bestPractices: string[];
  commonPitfalls: string[];
  lastUpdated: Date;
  nextReview: Date;
}

export interface CodeReviewProcess {
  reviewPolicy: string;
  reviewCriteria: string[];
  securityFocus: string[];
  reviewerQualifications: string[];
  toolsUsed: string[];
  reviewMetrics: CodeReviewMetric[];
  improvementActions: string[];
}

export interface CodeReviewMetric {
  metric: string;
  target: number;
  actual: number;
  trend: 'improving' | 'stable' | 'declining';
  period: string;
}

export interface StaticAnalysisTools {
  toolName: string;
  vendor: string;
  version: string;
  languages: string[];
  rulesets: string[];
  integrations: string[];
  scanFrequency: string;
  falsePositiveRate: number;
  coverage: number;
}

export interface DynamicAnalysisTools {
  toolName: string;
  vendor: string;
  version: string;
  testTypes: string[];
  environments: string[];
  automationLevel: string;
  scanFrequency: string;
  coverage: number;
  integrations: string[];
}

export interface DependencyManagement {
  dependencyInventory: Dependency[];
  licenseCompliance: LicenseCompliance;
  vulnerabilityScanning: DependencyVulnerabilityScanning;
  updatePolicy: DependencyUpdatePolicy;
  approvalProcess: DependencyApprovalProcess;
  riskAssessment: DependencyRiskAssessment;
}

export interface Dependency {
  name: string;
  version: string;
  type: 'direct' | 'transitive';
  license: string;
  source: string;
  lastUpdated: Date;
  vulnerabilities: string[];
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  approvalStatus: 'approved' | 'pending' | 'rejected' | 'conditional';
  alternatives: string[];
}

export interface LicenseCompliance {
  approvedLicenses: string[];
  restrictedLicenses: string[];
  licenseCompatibility: LicenseCompatibilityRule[];
  complianceChecks: LicenseComplianceCheck[];
  violationHandling: string;
}

export interface LicenseCompatibilityRule {
  license1: string;
  license2: string;
  compatible: boolean;
  conditions: string[];
  restrictions: string[];
}

export interface LicenseComplianceCheck {
  checkDate: Date;
  violations: LicenseViolation[];
  warnings: string[];
  recommendations: string[];
  overallStatus: 'compliant' | 'violations_found' | 'warnings_only';
}

export interface LicenseViolation {
  dependency: string;
  license: string;
  violationType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  remediation: string;
  dueDate: Date;
  status: 'open' | 'in_progress' | 'resolved';
}

export interface DependencyVulnerabilityScanning {
  scanningTools: string[];
  scanFrequency: string;
  alertThresholds: VulnerabilityAlertThreshold[];
  automatedActions: string[];
  reportingSchedule: string;
}

export interface VulnerabilityAlertThreshold {
  severity: string;
  alertLevel: 'info' | 'warning' | 'critical';
  escalationTime: number; // hours
  automatedResponse: string;
}

export interface DependencyUpdatePolicy {
  updateStrategy: 'conservative' | 'moderate' | 'aggressive';
  testingRequirements: string[];
  approvalRequirements: string[];
  rollbackProcedure: string;
  maintenanceWindows: string[];
}

export interface DependencyApprovalProcess {
  approvalCriteria: string[];
  approvers: string[];
  evaluationProcess: string;
  documentationRequirements: string[];
  appealProcess: string;
}

export interface DependencyRiskAssessment {
  riskFactors: DependencyRiskFactor[];
  riskMatrix: DependencyRiskMatrix[];
  mitigationStrategies: string[];
  monitoringActivities: string[];
}

export interface DependencyRiskFactor {
  factor: string;
  weight: number;
  assessment: 'low' | 'medium' | 'high' | 'critical';
  rationale: string;
}

export interface DependencyRiskMatrix {
  riskCategory: string;
  likelihood: string;
  impact: string;
  riskLevel: string;
  mitigationRequired: boolean;
}

export interface SecurityTestingPractices {
  testingTypes: SecurityTestType[];
  testingSchedule: SecurityTestingSchedule;
  testingEnvironments: SecurityTestingEnvironment[];
  testingMetrics: SecurityTestingMetric[];
  continuousIntegration: SecurityCIIntegration;
}

export interface SecurityTestType {
  testType: 'unit_security' | 'integration_security' | 'api_security' | 'ui_security' | 'infrastructure_security';
  description: string;
  tools: string[];
  frequency: string;
  coverage: number;
  automationLevel: string;
}

export interface SecurityTestingSchedule {
  testPhases: SecurityTestPhase[];
  regressionTesting: boolean;
  performanceTesting: boolean;
  loadTesting: boolean;
  stressTesting: boolean;
}

export interface SecurityTestPhase {
  phase: string;
  testTypes: string[];
  duration: number; // hours
  prerequisites: string[];
  exitCriteria: string[];
}

export interface SecurityTestingEnvironment {
  environmentName: string;
  purpose: string;
  configuration: string;
  dataManagement: string;
  accessControls: string[];
  monitoring: boolean;
}

export interface SecurityTestingMetric {
  metric: string;
  target: number;
  actual: number;
  trend: 'improving' | 'stable' | 'declining';
  reportingFrequency: string;
}

export interface SecurityCIIntegration {
  pipelineIntegration: boolean;
  automatedScanning: boolean;
  qualityGates: SecurityQualityGate[];
  failureCriteria: string[];
  reportingIntegration: boolean;
}

export interface SecurityQualityGate {
  gateName: string;
  criteria: string[];
  thresholds: GateThreshold[];
  actions: string[];
  bypassAllowed: boolean;
}

export interface GateThreshold {
  metric: string;
  operator: 'less_than' | 'greater_than' | 'equals' | 'not_equals';
  value: number;
  severity: 'info' | 'warning' | 'error' | 'critical';
}

// Industry-Specific Service Implementation
export class IndustrySpecificService {
  private static instance: IndustrySpecificService;

  public static getInstance(): IndustrySpecificService {
    if (!IndustrySpecificService.instance) {
      IndustrySpecificService.instance = new IndustrySpecificService();
    }
    return IndustrySpecificService.instance;
  }

  // Healthcare Industry Methods
  async createHealthcareComplianceWorkflow(
    tenantId: string,
    workflowData: Omit<HealthcareComplianceWorkflow, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<HealthcareComplianceWorkflow> {
    try {
      const workflow: HealthcareComplianceWorkflow = {
        id: `healthcare_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...workflowData,
        tenantId,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const docRef = doc(db, 'healthcareCompliance', workflow.id);
      await setDoc(docRef, {
        ...workflow,
        createdAt: Timestamp.fromDate(workflow.createdAt),
        updatedAt: Timestamp.fromDate(workflow.updatedAt)
      });

      return workflow;
    } catch (error) {
      console.error('Error creating healthcare compliance workflow:', error);
      throw error;
    }
  }

  async getHealthcareComplianceWorkflows(tenantId: string): Promise<HealthcareComplianceWorkflow[]> {
    try {
      const q = query(
        collection(db, 'healthcareCompliance'),
        where('tenantId', '==', tenantId),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
          createdAt: data.createdAt.toDate(),
          updatedAt: data.updatedAt.toDate()
        } as HealthcareComplianceWorkflow;
      });
    } catch (error) {
      console.error('Error fetching healthcare compliance workflows:', error);
      throw error;
    }
  }

  async updateHealthcareComplianceWorkflow(
    workflowId: string,
    updates: Partial<HealthcareComplianceWorkflow>
  ): Promise<void> {
    try {
      const docRef = doc(db, 'healthcareCompliance', workflowId);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: Timestamp.fromDate(new Date())
      });
    } catch (error) {
      console.error('Error updating healthcare compliance workflow:', error);
      throw error;
    }
  }

  // Automotive Industry Methods
  async createAutomotiveComplianceWorkflow(
    tenantId: string,
    workflowData: Omit<AutomotiveComplianceWorkflow, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<AutomotiveComplianceWorkflow> {
    try {
      const workflow: AutomotiveComplianceWorkflow = {
        id: `automotive_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...workflowData,
        tenantId,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const docRef = doc(db, 'automotiveCompliance', workflow.id);
      await setDoc(docRef, {
        ...workflow,
        createdAt: Timestamp.fromDate(workflow.createdAt),
        updatedAt: Timestamp.fromDate(workflow.updatedAt)
      });

      return workflow;
    } catch (error) {
      console.error('Error creating automotive compliance workflow:', error);
      throw error;
    }
  }

  async getAutomotiveComplianceWorkflows(tenantId: string): Promise<AutomotiveComplianceWorkflow[]> {
    try {
      const q = query(
        collection(db, 'automotiveCompliance'),
        where('tenantId', '==', tenantId),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
          createdAt: data.createdAt.toDate(),
          updatedAt: data.updatedAt.toDate()
        } as AutomotiveComplianceWorkflow;
      });
    } catch (error) {
      console.error('Error fetching automotive compliance workflows:', error);
      throw error;
    }
  }

  async updateAutomotiveComplianceWorkflow(
    workflowId: string,
    updates: Partial<AutomotiveComplianceWorkflow>
  ): Promise<void> {
    try {
      const docRef = doc(db, 'automotiveCompliance', workflowId);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: Timestamp.fromDate(new Date())
      });
    } catch (error) {
      console.error('Error updating automotive compliance workflow:', error);
      throw error;
    }
  }

  // Technology Industry Methods
  async createTechnologyComplianceWorkflow(
    tenantId: string,
    workflowData: Omit<TechnologyComplianceWorkflow, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<TechnologyComplianceWorkflow> {
    try {
      const workflow: TechnologyComplianceWorkflow = {
        id: `technology_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...workflowData,
        tenantId,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const docRef = doc(db, 'technologyCompliance', workflow.id);
      await setDoc(docRef, {
        ...workflow,
        createdAt: Timestamp.fromDate(workflow.createdAt),
        updatedAt: Timestamp.fromDate(workflow.updatedAt)
      });

      return workflow;
    } catch (error) {
      console.error('Error creating technology compliance workflow:', error);
      throw error;
    }
  }

  async getTechnologyComplianceWorkflows(tenantId: string): Promise<TechnologyComplianceWorkflow[]> {
    try {
      const q = query(
        collection(db, 'technologyCompliance'),
        where('tenantId', '==', tenantId),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
          createdAt: data.createdAt.toDate(),
          updatedAt: data.updatedAt.toDate()
        } as TechnologyComplianceWorkflow;
      });
    } catch (error) {
      console.error('Error fetching technology compliance workflows:', error);
      throw error;
    }
  }

  async updateTechnologyComplianceWorkflow(
    workflowId: string,
    updates: Partial<TechnologyComplianceWorkflow>
  ): Promise<void> {
    try {
      const docRef = doc(db, 'technologyCompliance', workflowId);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: Timestamp.fromDate(new Date())
      });
    } catch (error) {
      console.error('Error updating technology compliance workflow:', error);
      throw error;
    }
  }

  // Industry-Specific Automation Workflows
  async generateIndustrySpecificWorkflow(
    industry: 'healthcare' | 'automotive' | 'technology',
    workflowType: string,
    parameters: Record<string, any>
  ): Promise<any> {
    try {
      const response = await fetch('/api/ai/industry-automation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generate_workflow',
          industry,
          workflowType,
          parameters
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate industry-specific workflow');
      }

      return await response.json();
    } catch (error) {
      console.error('Error generating industry-specific workflow:', error);
      throw error;
    }
  }

  // Industry-Specific Reporting
  async generateIndustryComplianceReport(
    industry: 'healthcare' | 'automotive' | 'technology',
    tenantId: string,
    reportType: string,
    dateRange: { startDate: Date; endDate: Date }
  ): Promise<any> {
    try {
      let workflows: any[] = [];

      switch (industry) {
        case 'healthcare':
          workflows = await this.getHealthcareComplianceWorkflows(tenantId);
          break;
        case 'automotive':
          workflows = await this.getAutomotiveComplianceWorkflows(tenantId);
          break;
        case 'technology':
          workflows = await this.getTechnologyComplianceWorkflows(tenantId);
          break;
      }

      // Filter workflows by date range
      const filteredWorkflows = workflows.filter(workflow =>
        workflow.createdAt >= dateRange.startDate &&
        workflow.createdAt <= dateRange.endDate
      );

      // Generate industry-specific compliance report
      const response = await fetch('/api/ai/industry-reporting', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generate_compliance_report',
          industry,
          reportType,
          workflows: filteredWorkflows,
          dateRange
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate industry compliance report');
      }

      return await response.json();
    } catch (error) {
      console.error('Error generating industry compliance report:', error);
      throw error;
    }
  }

  // Industry-Specific Analytics
  async getIndustryAnalytics(
    industry: 'healthcare' | 'automotive' | 'technology',
    tenantId: string,
    analyticsType: string
  ): Promise<any> {
    try {
      let workflows: any[] = [];

      switch (industry) {
        case 'healthcare':
          workflows = await this.getHealthcareComplianceWorkflows(tenantId);
          break;
        case 'automotive':
          workflows = await this.getAutomotiveComplianceWorkflows(tenantId);
          break;
        case 'technology':
          workflows = await this.getTechnologyComplianceWorkflows(tenantId);
          break;
      }

      // Calculate industry-specific analytics
      const analytics = this.calculateIndustryAnalytics(industry, workflows, analyticsType);

      return analytics;
    } catch (error) {
      console.error('Error getting industry analytics:', error);
      throw error;
    }
  }

  private calculateIndustryAnalytics(
    industry: string,
    workflows: any[],
    analyticsType: string
  ): any {
    // Industry-specific analytics calculations
    const baseAnalytics = {
      totalWorkflows: workflows.length,
      complianceRate: 0,
      riskLevel: 'medium',
      trends: [],
      recommendations: []
    };

    switch (industry) {
      case 'healthcare':
        return this.calculateHealthcareAnalytics(workflows, analyticsType, baseAnalytics);
      case 'automotive':
        return this.calculateAutomotiveAnalytics(workflows, analyticsType, baseAnalytics);
      case 'technology':
        return this.calculateTechnologyAnalytics(workflows, analyticsType, baseAnalytics);
      default:
        return baseAnalytics;
    }
  }

  private calculateHealthcareAnalytics(workflows: HealthcareComplianceWorkflow[], analyticsType: string, baseAnalytics: any): any {
    const compliantWorkflows = workflows.filter(w => w.complianceStatus === 'compliant');
    const complianceRate = workflows.length > 0 ? (compliantWorkflows.length / workflows.length) * 100 : 0;

    return {
      ...baseAnalytics,
      complianceRate,
      fdaApprovals: workflows.filter(w => w.fdaNumber).length,
      ceMarkings: workflows.filter(w => w.ceMarking).length,
      riskAssessments: workflows.filter(w => w.riskAssessment).length,
      postMarketSurveillance: workflows.filter(w => w.postMarketSurveillance).length,
      recommendations: [
        'Ensure all medical devices have current FDA approvals',
        'Maintain up-to-date risk assessments for all devices',
        'Implement robust post-market surveillance programs'
      ]
    };
  }

  private calculateAutomotiveAnalytics(workflows: AutomotiveComplianceWorkflow[], analyticsType: string, baseAnalytics: any): any {
    const safetyCompliantWorkflows = workflows.filter(w =>
      w.safetyStandards.every(s => s.complianceStatus === 'compliant')
    );
    const complianceRate = workflows.length > 0 ? (safetyCompliantWorkflows.length / workflows.length) * 100 : 0;

    return {
      ...baseAnalytics,
      complianceRate,
      safetyStandards: workflows.reduce((acc, w) => acc + w.safetyStandards.length, 0),
      certifications: workflows.reduce((acc, w) => acc + w.certifications.length, 0),
      testingResults: workflows.reduce((acc, w) => acc + w.testingResults.length, 0),
      recalls: workflows.filter(w => w.recallManagement).length,
      recommendations: [
        'Ensure all safety standards are up to date',
        'Maintain comprehensive testing documentation',
        'Implement proactive recall management processes'
      ]
    };
  }

  private calculateTechnologyAnalytics(workflows: TechnologyComplianceWorkflow[], analyticsType: string, baseAnalytics: any): any {
    const secureWorkflows = workflows.filter(w =>
      w.securityCompliance && w.intellectualPropertyProtection
    );
    const complianceRate = workflows.length > 0 ? (secureWorkflows.length / workflows.length) * 100 : 0;

    return {
      ...baseAnalytics,
      complianceRate,
      ipProtections: workflows.filter(w => w.intellectualPropertyProtection).length,
      securityCompliance: workflows.filter(w => w.securityCompliance).length,
      dataPrivacyCompliance: workflows.filter(w => w.dataPrivacyCompliance).length,
      recommendations: [
        'Strengthen intellectual property protection measures',
        'Implement comprehensive security compliance frameworks',
        'Ensure data privacy compliance across all products'
      ]
    };
  }
}
