/**
 * Stripe Payment Service for EVEXA
 * Handles payment processing, subscription management, and billing automation
 */

import Stripe from 'stripe';
import { subscriptionService, type TenantSubscription, type SubscriptionPlan } from './subscriptionService';
import { db } from '@/lib/firebase';
import { doc, updateDoc, addDoc, collection, Timestamp } from 'firebase/firestore';

// Initialize Stripe with secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: string;
  clientSecret: string;
  metadata?: Record<string, string>;
}

export interface StripeCustomer {
  id: string;
  email: string;
  name?: string;
  metadata?: Record<string, string>;
}

export interface StripeSubscription {
  id: string;
  customerId: string;
  status: string;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  priceId: string;
  quantity: number;
  metadata?: Record<string, string>;
}

export interface BillingPortalSession {
  url: string;
}

class StripePaymentService {
  /**
   * Create a Stripe customer
   */
  async createCustomer(
    email: string,
    name?: string,
    tenantId?: string,
    metadata?: Record<string, string>
  ): Promise<StripeCustomer> {
    try {
      const customer = await stripe.customers.create({
        email,
        name,
        metadata: {
          tenantId: tenantId || '',
          ...metadata
        }
      });

      return {
        id: customer.id,
        email: customer.email!,
        name: customer.name || undefined,
        metadata: customer.metadata
      };
    } catch (error) {
      console.error('Error creating Stripe customer:', error);
      throw new Error('Failed to create customer');
    }
  }

  /**
   * Create a payment intent for one-time payments
   */
  async createPaymentIntent(
    amount: number,
    currency: string = 'usd',
    customerId?: string,
    metadata?: Record<string, string>
  ): Promise<PaymentIntent> {
    try {
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency,
        customer: customerId,
        metadata,
        automatic_payment_methods: {
          enabled: true,
        },
      });

      return {
        id: paymentIntent.id,
        amount: paymentIntent.amount / 100,
        currency: paymentIntent.currency,
        status: paymentIntent.status,
        clientSecret: paymentIntent.client_secret!,
        metadata: paymentIntent.metadata
      };
    } catch (error) {
      console.error('Error creating payment intent:', error);
      throw new Error('Failed to create payment intent');
    }
  }

  /**
   * Create a subscription
   */
  async createSubscription(
    customerId: string,
    priceId: string,
    tenantId: string,
    trialPeriodDays?: number,
    metadata?: Record<string, string>
  ): Promise<StripeSubscription> {
    try {
      const subscription = await stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: priceId }],
        trial_period_days: trialPeriodDays,
        metadata: {
          tenantId,
          ...metadata
        },
        expand: ['latest_invoice.payment_intent'],
      });

      const subData = subscription as any; // Type assertion for Stripe API compatibility
      return {
        id: subData.id,
        customerId: subData.customer as string,
        status: subData.status,
        currentPeriodStart: new Date(subData.current_period_start * 1000),
        currentPeriodEnd: new Date(subData.current_period_end * 1000),
        priceId: subData.items.data[0].price.id,
        quantity: subData.items.data[0].quantity || 1,
        metadata: subData.metadata
      };
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw new Error('Failed to create subscription');
    }
  }

  /**
   * Update subscription
   */
  async updateSubscription(
    subscriptionId: string,
    priceId?: string,
    quantity?: number,
    metadata?: Record<string, string>
  ): Promise<StripeSubscription> {
    try {
      const updateData: any = {};
      
      if (priceId) {
        // Get current subscription to update items
        const currentSub = await stripe.subscriptions.retrieve(subscriptionId);
        updateData.items = [{
          id: currentSub.items.data[0].id,
          price: priceId,
          quantity: quantity || 1
        }];
      }
      
      if (metadata) {
        updateData.metadata = metadata;
      }

      const subscription = await stripe.subscriptions.update(subscriptionId, updateData);

      const subData = subscription as any; // Type assertion for Stripe API compatibility
      return {
        id: subData.id,
        customerId: subData.customer as string,
        status: subData.status,
        currentPeriodStart: new Date(subData.current_period_start * 1000),
        currentPeriodEnd: new Date(subData.current_period_end * 1000),
        priceId: subData.items.data[0].price.id,
        quantity: subData.items.data[0].quantity || 1,
        metadata: subData.metadata
      };
    } catch (error) {
      console.error('Error updating subscription:', error);
      throw new Error('Failed to update subscription');
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true): Promise<void> {
    try {
      if (cancelAtPeriodEnd) {
        await stripe.subscriptions.update(subscriptionId, {
          cancel_at_period_end: true
        });
      } else {
        await stripe.subscriptions.cancel(subscriptionId);
      }
    } catch (error) {
      console.error('Error canceling subscription:', error);
      throw new Error('Failed to cancel subscription');
    }
  }

  /**
   * Create billing portal session
   */
  async createBillingPortalSession(
    customerId: string,
    returnUrl: string
  ): Promise<BillingPortalSession> {
    try {
      const session = await stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: returnUrl,
      });

      return {
        url: session.url
      };
    } catch (error) {
      console.error('Error creating billing portal session:', error);
      throw new Error('Failed to create billing portal session');
    }
  }

  /**
   * Create an invoice
   */
  async createInvoice(
    customerId: string,
    lineItems: Array<{
      description: string;
      amount: number;
      quantity: number;
    }>,
    metadata?: Record<string, string>
  ): Promise<any> {
    try {
      // Create invoice items first
      for (const item of lineItems) {
        await stripe.invoiceItems.create({
          customer: customerId,
          amount: Math.round(item.amount * 100), // Convert to cents
          currency: 'usd',
          description: item.description,
          quantity: item.quantity,
        });
      }

      // Create the invoice
      const invoice = await stripe.invoices.create({
        customer: customerId,
        metadata: metadata || {},
        auto_advance: false, // Don't automatically finalize
      });

      return invoice;
    } catch (error) {
      console.error('Error creating invoice:', error);
      throw error;
    }
  }

  /**
   * Create checkout session for subscription
   */
  async createCheckoutSession(
    priceId: string,
    tenantId: string,
    customerId?: string,
    successUrl?: string,
    cancelUrl?: string,
    trialPeriodDays?: number
  ): Promise<{ sessionId: string; url: string }> {
    try {
      const sessionData: any = {
        mode: 'subscription',
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        success_url: successUrl || `${process.env.NEXT_PUBLIC_APP_URL}/billing/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: cancelUrl || `${process.env.NEXT_PUBLIC_APP_URL}/billing/cancel`,
        metadata: {
          tenantId
        },
        subscription_data: {
          metadata: {
            tenantId
          }
        }
      };

      if (customerId) {
        sessionData.customer = customerId;
      } else {
        sessionData.customer_creation = 'always';
      }

      if (trialPeriodDays) {
        sessionData.subscription_data.trial_period_days = trialPeriodDays;
      }

      const session = await stripe.checkout.sessions.create(sessionData);

      return {
        sessionId: session.id,
        url: session.url!
      };
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw new Error('Failed to create checkout session');
    }
  }

  /**
   * Retrieve subscription
   */
  async getSubscription(subscriptionId: string): Promise<StripeSubscription> {
    try {
      const subscription = await stripe.subscriptions.retrieve(subscriptionId);

      const subData = subscription as any; // Type assertion for Stripe API compatibility
      return {
        id: subData.id,
        customerId: subData.customer as string,
        status: subData.status,
        currentPeriodStart: new Date(subData.current_period_start * 1000),
        currentPeriodEnd: new Date(subData.current_period_end * 1000),
        priceId: subData.items.data[0].price.id,
        quantity: subData.items.data[0].quantity || 1,
        metadata: subscription.metadata
      };
    } catch (error) {
      console.error('Error retrieving subscription:', error);
      throw new Error('Failed to retrieve subscription');
    }
  }

  /**
   * Handle webhook events
   */
  async handleWebhook(event: Stripe.Event): Promise<void> {
    try {
      switch (event.type) {
        case 'customer.subscription.created':
          await this.handleSubscriptionCreated(event.data.object as Stripe.Subscription);
          break;
        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
          break;
        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
          break;
        case 'invoice.payment_succeeded':
          await this.handlePaymentSucceeded(event.data.object as Stripe.Invoice);
          break;
        case 'invoice.payment_failed':
          await this.handlePaymentFailed(event.data.object as Stripe.Invoice);
          break;
        default:
          console.log(`Unhandled event type: ${event.type}`);
      }
    } catch (error) {
      console.error('Error handling webhook:', error);
      throw error;
    }
  }

  /**
   * Handle subscription created webhook
   */
  private async handleSubscriptionCreated(subscription: Stripe.Subscription): Promise<void> {
    const tenantId = subscription.metadata.tenantId;
    if (!tenantId) return;

    // Update tenant subscription in Firebase
    const tenantSubscription = await subscriptionService.getTenantSubscription(tenantId);
    if (tenantSubscription) {
      await subscriptionService.updateSubscription(tenantSubscription.id!, {
        status: subscription.status as any,
        'billing.subscriptionId': subscription.id,
        'billing.customerId': subscription.customer as string,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      });
    }
  }

  /**
   * Handle subscription updated webhook
   */
  private async handleSubscriptionUpdated(subscription: Stripe.Subscription): Promise<void> {
    const tenantId = subscription.metadata.tenantId;
    if (!tenantId) return;

    const tenantSubscription = await subscriptionService.getTenantSubscription(tenantId);
    if (tenantSubscription) {
      await subscriptionService.updateSubscription(tenantSubscription.id!, {
        status: subscription.status as any,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        canceledAt: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : undefined,
      });
    }
  }

  /**
   * Handle subscription deleted webhook
   */
  private async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<void> {
    const tenantId = subscription.metadata.tenantId;
    if (!tenantId) return;

    const tenantSubscription = await subscriptionService.getTenantSubscription(tenantId);
    if (tenantSubscription) {
      await subscriptionService.updateSubscription(tenantSubscription.id!, {
        status: 'canceled',
        canceledAt: new Date(),
      });
    }
  }

  /**
   * Handle payment succeeded webhook
   */
  private async handlePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    const subscriptionId = invoice.subscription as string;
    if (!subscriptionId) return;

    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    const tenantId = subscription.metadata.tenantId;
    if (!tenantId) return;

    const tenantSubscription = await subscriptionService.getTenantSubscription(tenantId);
    if (tenantSubscription) {
      await subscriptionService.updateSubscription(tenantSubscription.id!, {
        'billing.lastPaymentDate': new Date(),
        'billing.nextPaymentDate': new Date(subscription.current_period_end * 1000),
        'billing.amountDue': 0,
      });
    }
  }

  /**
   * Handle payment failed webhook
   */
  private async handlePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    const subscriptionId = invoice.subscription as string;
    if (!subscriptionId) return;

    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    const tenantId = subscription.metadata.tenantId;
    if (!tenantId) return;

    const tenantSubscription = await subscriptionService.getTenantSubscription(tenantId);
    if (tenantSubscription) {
      await subscriptionService.updateSubscription(tenantSubscription.id!, {
        status: 'past_due',
        'billing.amountDue': invoice.amount_due / 100,
      });
    }
  }
}

export const stripePaymentService = new StripePaymentService();
