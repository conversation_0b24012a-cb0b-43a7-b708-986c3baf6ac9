/**
 * Invitation Management Service
 * Comprehensive invitation system with token generation, expiration handling, and status tracking
 */

import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit,
  serverTimestamp,
  Timestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { validateTenantId } from './tenantIdHelperService';
import { EnhancedEmailService } from './enhancedEmailService';
import crypto from 'crypto';

// ===== TYPES =====

export interface UserInvitation {
  id: string;
  tenantId: string;
  email: string;
  firstName: string;
  lastName: string;
  personaId: string;
  roleName: string;
  status: 'pending' | 'accepted' | 'expired' | 'cancelled' | 'resent';
  token: string;
  invitationUrl: string;
  invitedBy: string;
  invitedByName: string;
  sentAt: Date;
  expiresAt: Date;
  acceptedAt?: Date;
  cancelledAt?: Date;
  lastReminderSent?: Date;
  reminderCount: number;
  messageId?: string;
  customMessage?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface InvitationCreateRequest {
  tenantId: string;
  email: string;
  firstName: string;
  lastName: string;
  personaId: string;
  invitedBy: string;
  invitedByName: string;
  customMessage?: string;
  expirationDays?: number;
}

export interface InvitationStats {
  total: number;
  pending: number;
  accepted: number;
  expired: number;
  cancelled: number;
  acceptanceRate: number;
}

// ===== INVITATION MANAGEMENT SERVICE =====

export class InvitationManagementService {
  private emailService: EnhancedEmailService;

  constructor(private tenantId: string) {
    validateTenantId(tenantId);
    this.emailService = new EnhancedEmailService(tenantId);
  }

  /**
   * Create and send a new invitation
   */
  async createInvitation(request: InvitationCreateRequest): Promise<UserInvitation> {
    try {
      // Validate request
      if (request.tenantId !== this.tenantId) {
        throw new Error('Tenant ID mismatch');
      }

      // Check if user is already invited or exists
      const existingInvitation = await this.getInvitationByEmail(request.email);
      if (existingInvitation && existingInvitation.status === 'pending') {
        throw new Error('User already has a pending invitation');
      }

      // Generate secure token
      const token = this.generateSecureToken();
      const invitationUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'https://app.evexa.com'}/invitation/${token}`;
      const expirationDays = request.expirationDays || 7;
      const expiresAt = new Date(Date.now() + expirationDays * 24 * 60 * 60 * 1000);

      // Create invitation record
      const invitation: Omit<UserInvitation, 'id'> = {
        tenantId: this.tenantId,
        email: request.email,
        firstName: request.firstName,
        lastName: request.lastName,
        personaId: request.personaId,
        roleName: await this.getPersonaName(request.personaId),
        status: 'pending',
        token,
        invitationUrl,
        invitedBy: request.invitedBy,
        invitedByName: request.invitedByName,
        sentAt: new Date(),
        expiresAt,
        reminderCount: 0,
        customMessage: request.customMessage,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Save to database
      const docRef = await addDoc(collection(db, COLLECTIONS.USER_INVITATIONS), {
        ...invitation,
        sentAt: serverTimestamp(),
        expiresAt: Timestamp.fromDate(expiresAt),
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // Send invitation email
      const emailResult = await this.emailService.sendInvitationEmail(
        request.email,
        `${request.firstName} ${request.lastName}`,
        request.invitedByName,
        invitation.roleName,
        invitationUrl,
        expiresAt.toLocaleDateString()
      );

      if (!emailResult.success) {
        // Update invitation status to failed
        await updateDoc(docRef, {
          status: 'cancelled',
          cancelledAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          metadata: { emailError: emailResult.error }
        });
        throw new Error(`Failed to send invitation email: ${emailResult.error}`);
      }

      // Update with email message ID
      await updateDoc(docRef, {
        messageId: emailResult.messageId,
        updatedAt: serverTimestamp()
      });

      return {
        ...invitation,
        id: docRef.id,
        messageId: emailResult.messageId
      };

    } catch (error) {
      console.error('Error creating invitation:', error);
      throw error;
    }
  }

  /**
   * Get invitation by token
   */
  async getInvitationByToken(token: string): Promise<UserInvitation | null> {
    try {
      const q = query(
        collection(db, COLLECTIONS.USER_INVITATIONS),
        where('token', '==', token),
        where('tenantId', '==', this.tenantId),
        limit(1)
      );

      const snapshot = await getDocs(q);
      if (snapshot.empty) {
        return null;
      }

      const doc = snapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data(),
        sentAt: doc.data().sentAt?.toDate() || new Date(),
        expiresAt: doc.data().expiresAt?.toDate() || new Date(),
        acceptedAt: doc.data().acceptedAt?.toDate(),
        cancelledAt: doc.data().cancelledAt?.toDate(),
        lastReminderSent: doc.data().lastReminderSent?.toDate(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      } as UserInvitation;

    } catch (error) {
      console.error('Error getting invitation by token:', error);
      return null;
    }
  }

  /**
   * Get invitation by email
   */
  async getInvitationByEmail(email: string): Promise<UserInvitation | null> {
    try {
      const q = query(
        collection(db, COLLECTIONS.USER_INVITATIONS),
        where('email', '==', email),
        where('tenantId', '==', this.tenantId),
        orderBy('createdAt', 'desc'),
        limit(1)
      );

      const snapshot = await getDocs(q);
      if (snapshot.empty) {
        return null;
      }

      const doc = snapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data(),
        sentAt: doc.data().sentAt?.toDate() || new Date(),
        expiresAt: doc.data().expiresAt?.toDate() || new Date(),
        acceptedAt: doc.data().acceptedAt?.toDate(),
        cancelledAt: doc.data().cancelledAt?.toDate(),
        lastReminderSent: doc.data().lastReminderSent?.toDate(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      } as UserInvitation;

    } catch (error) {
      console.error('Error getting invitation by email:', error);
      return null;
    }
  }

  /**
   * Accept an invitation
   */
  async acceptInvitation(token: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const invitation = await this.getInvitationByToken(token);
      
      if (!invitation) {
        return { success: false, error: 'Invitation not found' };
      }

      if (invitation.status !== 'pending') {
        return { success: false, error: 'Invitation is no longer valid' };
      }

      if (invitation.expiresAt < new Date()) {
        // Mark as expired
        await this.updateInvitationStatus(invitation.id, 'expired');
        return { success: false, error: 'Invitation has expired' };
      }

      // Update invitation status
      await updateDoc(doc(db, COLLECTIONS.USER_INVITATIONS, invitation.id), {
        status: 'accepted',
        acceptedAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        metadata: {
          ...invitation.metadata,
          acceptedByUserId: userId
        }
      });

      return { success: true };

    } catch (error) {
      console.error('Error accepting invitation:', error);
      return { success: false, error: 'Failed to accept invitation' };
    }
  }

  /**
   * Generate secure invitation token
   */
  private generateSecureToken(): string {
    const timestamp = Date.now().toString();
    const randomBytes = crypto.randomBytes(16).toString('hex');
    const hash = crypto.createHash('sha256').update(`${timestamp}_${randomBytes}_${this.tenantId}`).digest('hex');
    return `inv_${timestamp}_${hash.substring(0, 16)}`;
  }

  /**
   * Get persona name by ID
   */
  private async getPersonaName(personaId: string): Promise<string> {
    try {
      const personaDoc = await getDoc(doc(db, COLLECTIONS.USER_PERSONAS, personaId));
      if (personaDoc.exists()) {
        return personaDoc.data().name || 'Team Member';
      }
      return 'Team Member';
    } catch (error) {
      console.error('Error getting persona name:', error);
      return 'Team Member';
    }
  }

  /**
   * Update invitation status
   */
  private async updateInvitationStatus(invitationId: string, status: UserInvitation['status']): Promise<void> {
    const updates: any = {
      status,
      updatedAt: serverTimestamp()
    };

    if (status === 'expired') {
      updates.expiredAt = serverTimestamp();
    } else if (status === 'cancelled') {
      updates.cancelledAt = serverTimestamp();
    }

    await updateDoc(doc(db, COLLECTIONS.USER_INVITATIONS, invitationId), updates);
  }

  /**
   * Cancel an invitation
   */
  async cancelInvitation(invitationId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const invitationDoc = await getDoc(doc(db, COLLECTIONS.USER_INVITATIONS, invitationId));

      if (!invitationDoc.exists()) {
        return { success: false, error: 'Invitation not found' };
      }

      const invitation = invitationDoc.data() as UserInvitation;

      if (invitation.tenantId !== this.tenantId) {
        return { success: false, error: 'Unauthorized' };
      }

      if (invitation.status !== 'pending') {
        return { success: false, error: 'Can only cancel pending invitations' };
      }

      await this.updateInvitationStatus(invitationId, 'cancelled');
      return { success: true };

    } catch (error) {
      console.error('Error cancelling invitation:', error);
      return { success: false, error: 'Failed to cancel invitation' };
    }
  }

  /**
   * Resend an invitation
   */
  async resendInvitation(invitationId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const invitationDoc = await getDoc(doc(db, COLLECTIONS.USER_INVITATIONS, invitationId));

      if (!invitationDoc.exists()) {
        return { success: false, error: 'Invitation not found' };
      }

      const invitation = {
        id: invitationDoc.id,
        ...invitationDoc.data(),
        sentAt: invitationDoc.data()?.sentAt?.toDate() || new Date(),
        expiresAt: invitationDoc.data()?.expiresAt?.toDate() || new Date(),
        createdAt: invitationDoc.data()?.createdAt?.toDate() || new Date(),
        updatedAt: invitationDoc.data()?.updatedAt?.toDate() || new Date()
      } as UserInvitation;

      if (invitation.tenantId !== this.tenantId) {
        return { success: false, error: 'Unauthorized' };
      }

      if (invitation.status !== 'pending') {
        return { success: false, error: 'Can only resend pending invitations' };
      }

      // Extend expiration date
      const newExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

      // Send email
      const emailResult = await this.emailService.sendInvitationEmail(
        invitation.email,
        `${invitation.firstName} ${invitation.lastName}`,
        invitation.invitedByName,
        invitation.roleName,
        invitation.invitationUrl,
        newExpiresAt.toLocaleDateString()
      );

      if (!emailResult.success) {
        return { success: false, error: `Failed to resend invitation: ${emailResult.error}` };
      }

      // Update invitation
      await updateDoc(doc(db, COLLECTIONS.USER_INVITATIONS, invitationId), {
        status: 'resent',
        expiresAt: Timestamp.fromDate(newExpiresAt),
        lastReminderSent: serverTimestamp(),
        reminderCount: invitation.reminderCount + 1,
        messageId: emailResult.messageId,
        updatedAt: serverTimestamp()
      });

      return { success: true };

    } catch (error) {
      console.error('Error resending invitation:', error);
      return { success: false, error: 'Failed to resend invitation' };
    }
  }

  /**
   * Get all invitations for tenant
   */
  async getInvitations(
    status?: UserInvitation['status'],
    limitCount: number = 50
  ): Promise<UserInvitation[]> {
    try {
      let q = query(
        collection(db, COLLECTIONS.USER_INVITATIONS),
        where('tenantId', '==', this.tenantId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      if (status) {
        q = query(
          collection(db, COLLECTIONS.USER_INVITATIONS),
          where('tenantId', '==', this.tenantId),
          where('status', '==', status),
          orderBy('createdAt', 'desc'),
          limit(limitCount)
        );
      }

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        sentAt: doc.data().sentAt?.toDate() || new Date(),
        expiresAt: doc.data().expiresAt?.toDate() || new Date(),
        acceptedAt: doc.data().acceptedAt?.toDate(),
        cancelledAt: doc.data().cancelledAt?.toDate(),
        lastReminderSent: doc.data().lastReminderSent?.toDate(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      })) as UserInvitation[];

    } catch (error) {
      console.error('Error getting invitations:', error);
      return [];
    }
  }

  /**
   * Get invitation statistics
   */
  async getInvitationStats(): Promise<InvitationStats> {
    try {
      const invitations = await this.getInvitations(undefined, 1000);

      const stats = {
        total: invitations.length,
        pending: invitations.filter(inv => inv.status === 'pending').length,
        accepted: invitations.filter(inv => inv.status === 'accepted').length,
        expired: invitations.filter(inv => inv.status === 'expired').length,
        cancelled: invitations.filter(inv => inv.status === 'cancelled').length,
        acceptanceRate: 0
      };

      const totalSent = stats.accepted + stats.expired + stats.cancelled;
      if (totalSent > 0) {
        stats.acceptanceRate = (stats.accepted / totalSent) * 100;
      }

      return stats;

    } catch (error) {
      console.error('Error getting invitation stats:', error);
      return {
        total: 0,
        pending: 0,
        accepted: 0,
        expired: 0,
        cancelled: 0,
        acceptanceRate: 0
      };
    }
  }

  /**
   * Process expired invitations (should be run periodically)
   */
  async processExpiredInvitations(): Promise<number> {
    try {
      const now = new Date();
      const q = query(
        collection(db, COLLECTIONS.USER_INVITATIONS),
        where('tenantId', '==', this.tenantId),
        where('status', '==', 'pending'),
        where('expiresAt', '<', Timestamp.fromDate(now))
      );

      const snapshot = await getDocs(q);
      let expiredCount = 0;

      for (const docSnapshot of snapshot.docs) {
        await this.updateInvitationStatus(docSnapshot.id, 'expired');
        expiredCount++;
      }

      return expiredCount;

    } catch (error) {
      console.error('Error processing expired invitations:', error);
      return 0;
    }
  }
}
