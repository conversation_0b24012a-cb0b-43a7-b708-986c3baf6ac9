/**
 * Design Project Service
 * Manages design projects for marketing floorplans and booth designs
 */

import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  Timestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import type { DesignProject } from '@/types/firestore';

/**
 * Get all design projects for a tenant
 */
export async function getDesignProjects(tenantId: string): Promise<DesignProject[]> {
  try {
    const q = query(
      collection(db, COLLECTIONS.DESIGN_PROJECTS),
      where('tenantId', '==', tenantId),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as DesignProject[];
  } catch (error) {
    console.error('Error fetching design projects:', error);
    return [];
  }
}

/**
 * Get a single design project by ID
 */
export async function getDesignProject(projectId: string): Promise<DesignProject | null> {
  try {
    const docRef = doc(db, COLLECTIONS.DESIGN_PROJECTS, projectId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data()
      } as DesignProject;
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching design project:', error);
    return null;
  }
}

/**
 * Create a new design project
 */
export async function createDesignProject(
  projectData: Omit<DesignProject, 'id' | 'createdAt' | 'updatedAt'>
): Promise<string | null> {
  try {
    const now = Timestamp.now();
    const docRef = await addDoc(collection(db, COLLECTIONS.DESIGN_PROJECTS), {
      ...projectData,
      createdAt: now,
      updatedAt: now
    });
    
    return docRef.id;
  } catch (error) {
    console.error('Error creating design project:', error);
    return null;
  }
}

/**
 * Update an existing design project
 */
export async function updateDesignProject(
  projectId: string,
  updates: Partial<Omit<DesignProject, 'id' | 'createdAt'>>
): Promise<boolean> {
  try {
    const docRef = doc(db, COLLECTIONS.DESIGN_PROJECTS, projectId);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: Timestamp.now()
    });
    
    return true;
  } catch (error) {
    console.error('Error updating design project:', error);
    return false;
  }
}

/**
 * Delete a design project
 */
export async function deleteDesignProject(projectId: string): Promise<boolean> {
  try {
    const docRef = doc(db, COLLECTIONS.DESIGN_PROJECTS, projectId);
    await deleteDoc(docRef);
    
    return true;
  } catch (error) {
    console.error('Error deleting design project:', error);
    return false;
  }
}

/**
 * Get design projects by status
 */
export async function getDesignProjectsByStatus(
  tenantId: string, 
  status: DesignProject['status']
): Promise<DesignProject[]> {
  try {
    const q = query(
      collection(db, COLLECTIONS.DESIGN_PROJECTS),
      where('tenantId', '==', tenantId),
      where('status', '==', status),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as DesignProject[];
  } catch (error) {
    console.error('Error fetching design projects by status:', error);
    return [];
  }
}

/**
 * Get design projects for a specific exhibition
 */
export async function getDesignProjectsForExhibition(
  tenantId: string,
  exhibitionId: string
): Promise<DesignProject[]> {
  try {
    const q = query(
      collection(db, COLLECTIONS.DESIGN_PROJECTS),
      where('tenantId', '==', tenantId),
      where('exhibitionId', '==', exhibitionId),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as DesignProject[];
  } catch (error) {
    console.error('Error fetching design projects for exhibition:', error);
    return [];
  }
}

/**
 * Calculate design project metrics
 */
export function calculateDesignMetrics(projects: DesignProject[]) {
  const totalProjects = projects.length;
  const totalBudget = projects.reduce((sum, p) => sum + p.budget, 0);
  const totalSpent = projects.reduce((sum, p) => sum + p.spent, 0);
  const completedProjects = projects.filter(p => p.status === 'completed').length;
  const activeProjects = projects.filter(p => p.status === 'in_progress').length;
  const averageProgress = totalProjects > 0 
    ? Math.round(projects.reduce((sum, p) => sum + p.progress, 0) / totalProjects)
    : 0;

  // Calculate top contractors
  const contractorStats = projects.reduce((acc, project) => {
    if (!acc[project.contractor]) {
      acc[project.contractor] = {
        name: project.contractor,
        projectCount: 0,
        totalValue: 0,
        rating: 4.5 // Default rating - could be enhanced with real ratings
      };
    }
    acc[project.contractor].projectCount++;
    acc[project.contractor].totalValue += project.spent;
    return acc;
  }, {} as Record<string, { name: string; projectCount: number; totalValue: number; rating: number }>);

  const topContractors = Object.values(contractorStats)
    .sort((a, b) => b.totalValue - a.totalValue)
    .slice(0, 5);

  return {
    totalProjects,
    totalBudget,
    totalSpent,
    completedProjects,
    activeProjects,
    averageProgress,
    topContractors
  };
}

/**
 * Seed sample design projects for testing (development only)
 */
export async function seedSampleDesignProjects(tenantId: string): Promise<boolean> {
  try {
    const sampleProjects: Omit<DesignProject, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        name: 'Tech Summit 2024 Booth Design',
        status: 'completed',
        budget: 25000,
        spent: 23500,
        contractor: 'Creative Designs Inc',
        startDate: new Date('2024-01-15'),
        endDate: new Date('2024-02-28'),
        progress: 100,
        description: 'Modern tech booth with interactive displays',
        tenantId
      },
      {
        name: 'Healthcare Expo Stand',
        status: 'in_progress',
        budget: 18000,
        spent: 12000,
        contractor: 'Modern Exhibits LLC',
        startDate: new Date('2024-02-01'),
        endDate: new Date('2024-03-15'),
        progress: 65,
        description: 'Clean, professional healthcare exhibition stand',
        tenantId
      },
      {
        name: 'Auto Show Display',
        status: 'planning',
        budget: 30000,
        spent: 0,
        contractor: 'Elite Booth Builders',
        startDate: new Date('2024-03-01'),
        endDate: new Date('2024-04-30'),
        progress: 15,
        description: 'High-impact automotive display with vehicle showcase',
        tenantId
      }
    ];

    for (const project of sampleProjects) {
      await createDesignProject(project);
    }

    return true;
  } catch (error) {
    console.error('Error seeding sample design projects:', error);
    return false;
  }
}
