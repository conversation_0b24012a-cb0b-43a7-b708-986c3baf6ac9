/**
 * TenantId Helper Service
 * Provides utility functions that automatically add tenantId filters to all queries
 * and validate tenantId on all write operations for the flat collection architecture
 */

import {
  collection,
  doc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  endBefore,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  writeBatch,
  serverTimestamp,
  type QueryConstraint,
  type DocumentData,
  type Query,
  type DocumentReference,
  type WriteBatch,
  type WhereFilterOp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { TenantAwareEntity } from '@/types/firestore';
import {
  validateDocumentSchema,
  sanitizeDocument,
  type ValidationResult
} from './dataValidationLayer';
import { getValidationSchema } from './collectionValidationSchemas';
import { syncDocumentDuplication } from './dataDuplicationService';

// Safe wrapper for Firebase where() queries
function safeWhere(field: string, operator: WhereFilterOp, value: any) {
  if (value === undefined || value === null) {
    console.error(`🚨 PREVENTED FIREBASE ERROR: where() called with undefined/null value`, {
      field,
      operator,
      value,
      stack: new Error().stack
    });
    throw new Error(`Invalid query: field '${field}' has undefined/null value`);
  }
  return where(field, operator, value);
}

// ===== TENANT VALIDATION =====

/**
 * Validate that tenantId is provided and valid
 */
export function validateTenantId(tenantId: string | null | undefined): asserts tenantId is string {
  if (!tenantId || typeof tenantId !== 'string' || tenantId.trim() === '') {
    throw new Error('TenantId is required for all database operations');
  }
}

/**
 * Validate that document data includes tenantId
 */
export function validateDocumentTenantId<T extends TenantAwareEntity>(
  data: Partial<T>, 
  expectedTenantId: string
): void {
  if (!data.tenantId) {
    throw new Error('Document must include tenantId');
  }
  if (data.tenantId !== expectedTenantId) {
    throw new Error(`Document tenantId (${data.tenantId}) does not match expected tenantId (${expectedTenantId})`);
  }
}

// ===== QUERY HELPERS =====

/**
 * Create a tenant-scoped query with automatic tenantId filtering
 */
export function createTenantQuery(
  tenantId: string,
  collectionName: string,
  ...constraints: QueryConstraint[]
): Query<DocumentData> {
  validateTenantId(tenantId);
  
  const collectionRef = collection(db, collectionName);
  const tenantConstraint = where('tenantId', '==', tenantId);
  
  return query(collectionRef, tenantConstraint, ...constraints);
}

/**
 * Create tenant-scoped query with common filters
 */
export function createTenantQueryWithFilters(
  tenantId: string,
  collectionName: string,
  filters: Array<{ field: string; operator: WhereFilterOp; value: any }> = [],
  sortField?: string,
  sortDirection: 'asc' | 'desc' = 'desc',
  limitCount?: number
): Query<DocumentData> {
  validateTenantId(tenantId);
  
  const constraints: QueryConstraint[] = [where('tenantId', '==', tenantId)];
  
  // Add custom filters (validate to prevent undefined values)
  filters.forEach(filter => {
    if (filter.value === undefined || filter.value === null) {
      console.error(`🚨 FIREBASE ERROR PREVENTION: Skipping filter with undefined/null value:`, {
        collection: collectionName,
        field: filter.field,
        operator: filter.operator,
        value: filter.value,
        tenantId,
        stack: new Error().stack
      });
      return;
    }
    constraints.push(where(filter.field, filter.operator, filter.value));
  });
  
  // Add sorting
  if (sortField) {
    constraints.push(orderBy(sortField, sortDirection));
  }
  
  // Add limit
  if (limitCount) {
    constraints.push(limit(limitCount));
  }
  
  return query(collection(db, collectionName), ...constraints);
}

// ===== READ OPERATIONS =====

/**
 * Get all documents from a tenant-scoped collection
 */
export async function getTenantDocuments<T extends TenantAwareEntity>(
  tenantId: string,
  collectionName: string,
  sortField?: string,
  sortDirection: 'asc' | 'desc' = 'desc',
  limitCount?: number
): Promise<T[]> {
  validateTenantId(tenantId);
  
  const q = createTenantQueryWithFilters(
    tenantId,
    collectionName,
    [],
    sortField,
    sortDirection,
    limitCount
  );
  
  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data()
  } as T));
}

/**
 * Get documents with custom filters
 */
export async function getTenantDocumentsWithFilters<T extends TenantAwareEntity>(
  tenantId: string,
  collectionName: string,
  filters: Array<{ field: string; operator: WhereFilterOp; value: any }>,
  sortField?: string,
  sortDirection: 'asc' | 'desc' = 'desc',
  limitCount?: number
): Promise<T[]> {
  validateTenantId(tenantId);
  
  const q = createTenantQueryWithFilters(
    tenantId,
    collectionName,
    filters,
    sortField,
    sortDirection,
    limitCount
  );
  
  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data()
  } as T));
}

/**
 * Get a single document by ID with tenant validation
 */
export async function getTenantDocumentById<T extends TenantAwareEntity>(
  tenantId: string,
  collectionName: string,
  documentId: string
): Promise<T | null> {
  validateTenantId(tenantId);
  
  const docRef = doc(db, collectionName, documentId);
  const docSnap = await getDoc(docRef);
  
  if (!docSnap.exists()) {
    return null;
  }
  
  const data = { id: docSnap.id, ...docSnap.data() } as T;
  
  // Validate tenant ownership
  if (data.tenantId !== tenantId) {
    throw new Error(`Access denied: Document belongs to different tenant`);
  }
  
  return data;
}

// ===== WRITE OPERATIONS =====

/**
 * Add a document with automatic tenantId stamping, validation, and duplication sync
 */
export async function addTenantDocument<T extends TenantAwareEntity>(
  tenantId: string,
  collectionName: string,
  data: Omit<T, 'id' | 'tenantId' | 'createdAt' | 'updatedAt'>
): Promise<T> {
  validateTenantId(tenantId);

  const documentData = {
    ...data,
    tenantId,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  };

  // Validate document before saving
  const schema = getValidationSchema(collectionName);
  if (schema) {
    const validation = validateDocumentSchema(documentData, schema);
    if (!validation.valid) {
      const errorMessages = validation.errors.map(e => `${e.field}: ${e.message}`).join(', ');
      throw new Error(`Document validation failed: ${errorMessages}`);
    }
  }

  // Sanitize document
  const sanitizedData = sanitizeDocument(documentData);

  const docRef = await addDoc(collection(db, collectionName), sanitizedData);

  const result = {
    id: docRef.id,
    ...sanitizedData,
    createdAt: new Date(),
    updatedAt: new Date()
  } as T;

  // Sync duplicated data asynchronously (don't wait for it to complete)
  syncDocumentDuplication(tenantId, collectionName, docRef.id, result as Record<string, any>)
    .catch(error => {
      console.error(`Error syncing duplication for new document ${docRef.id}:`, error);
    });

  return result;
}

/**
 * Update a document with tenant validation, data validation, and duplication sync
 */
export async function updateTenantDocument<T extends TenantAwareEntity>(
  tenantId: string,
  collectionName: string,
  documentId: string,
  updates: Partial<Omit<T, 'id' | 'tenantId' | 'createdAt'>>
): Promise<void> {
  validateTenantId(tenantId);

  // First verify the document belongs to the tenant
  const existingDoc = await getTenantDocumentById<T>(tenantId, collectionName, documentId);
  if (!existingDoc) {
    throw new Error('Document not found or access denied');
  }

  const updatesWithTimestamp = {
    ...updates,
    updatedAt: serverTimestamp()
  };

  // Validate the merged document
  const schema = getValidationSchema(collectionName);
  if (schema) {
    const mergedDocument = { ...existingDoc, ...updatesWithTimestamp };
    const validation = validateDocumentSchema(mergedDocument, schema);
    if (!validation.valid) {
      const errorMessages = validation.errors.map(e => `${e.field}: ${e.message}`).join(', ');
      throw new Error(`Document validation failed: ${errorMessages}`);
    }
  }

  // Sanitize updates
  const sanitizedUpdates = sanitizeDocument(updatesWithTimestamp);

  const docRef = doc(db, collectionName, documentId);
  await updateDoc(docRef, sanitizedUpdates);

  // Sync duplicated data asynchronously (don't wait for it to complete)
  syncDocumentDuplication(tenantId, collectionName, documentId, updates as Record<string, any>)
    .catch(error => {
      console.error(`Error syncing duplication for updated document ${documentId}:`, error);
    });
}

/**
 * Delete a document with tenant validation
 */
export async function deleteTenantDocument(
  tenantId: string,
  collectionName: string,
  documentId: string
): Promise<void> {
  validateTenantId(tenantId);
  
  // First verify the document belongs to the tenant
  const existingDoc = await getTenantDocumentById(tenantId, collectionName, documentId);
  if (!existingDoc) {
    throw new Error('Document not found or access denied');
  }
  
  const docRef = doc(db, collectionName, documentId);
  await deleteDoc(docRef);
}

// ===== BATCH OPERATIONS =====

/**
 * Create a batch with tenant validation helpers
 */
export class TenantAwareBatch {
  private batch: WriteBatch;
  private tenantId: string;
  
  constructor(tenantId: string) {
    validateTenantId(tenantId);
    this.tenantId = tenantId;
    this.batch = writeBatch(db);
  }
  
  /**
   * Add document to batch with automatic tenantId stamping and validation
   */
  add<T extends TenantAwareEntity>(
    collectionName: string,
    data: Omit<T, 'id' | 'tenantId' | 'createdAt' | 'updatedAt'>
  ): DocumentReference {
    const docRef = doc(collection(db, collectionName));

    const documentData = {
      ...data,
      tenantId: this.tenantId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    // Validate document before adding to batch
    const schema = getValidationSchema(collectionName);
    if (schema) {
      const validation = validateDocumentSchema(documentData, schema);
      if (!validation.valid) {
        const errorMessages = validation.errors.map(e => `${e.field}: ${e.message}`).join(', ');
        throw new Error(`Batch document validation failed: ${errorMessages}`);
      }
    }

    // Sanitize document
    const sanitizedData = sanitizeDocument(documentData);

    this.batch.set(docRef, sanitizedData);
    return docRef;
  }
  
  /**
   * Update document in batch (tenant validation should be done before calling this)
   */
  update<T extends TenantAwareEntity>(
    collectionName: string,
    documentId: string,
    updates: Partial<Omit<T, 'id' | 'tenantId' | 'createdAt'>>
  ): void {
    const docRef = doc(db, collectionName, documentId);
    this.batch.update(docRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
  }
  
  /**
   * Delete document in batch (tenant validation should be done before calling this)
   */
  delete(collectionName: string, documentId: string): void {
    const docRef = doc(db, collectionName, documentId);
    this.batch.delete(docRef);
  }
  
  /**
   * Commit the batch
   */
  async commit(): Promise<void> {
    await this.batch.commit();
  }
}

// ===== UTILITY FUNCTIONS =====

/**
 * Get tenant-scoped document reference
 */
export function getTenantDocumentRef(
  tenantId: string,
  collectionName: string,
  documentId?: string
): DocumentReference {
  validateTenantId(tenantId);
  
  if (documentId) {
    return doc(db, collectionName, documentId);
  } else {
    return doc(collection(db, collectionName));
  }
}

/**
 * Count documents in tenant-scoped collection
 */
export async function countTenantDocuments(
  tenantId: string,
  collectionName: string,
  filters: Array<{ field: string; operator: WhereFilterOp; value: any }> = []
): Promise<number> {
  validateTenantId(tenantId);

  const q = createTenantQueryWithFilters(tenantId, collectionName, filters);
  const querySnapshot = await getDocs(q);
  return querySnapshot.size;
}

/**
 * Query tenant collection with constraints and options
 */
export async function queryTenantCollection<T extends TenantAwareEntity>(
  collectionName: string,
  constraints: QueryConstraint[] = [],
  tenantId?: string
): Promise<T[]> {
  if (!tenantId) {
    throw new Error('TenantId is required for queryTenantCollection');
  }

  validateTenantId(tenantId);

  const q = createTenantQuery(tenantId, collectionName, ...constraints);
  const querySnapshot = await getDocs(q);

  return querySnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data()
  } as T));
}

/**
 * Get tenant document by ID (alias for getTenantDocumentById)
 */
export async function getTenantDocument<T extends TenantAwareEntity>(
  tenantId: string,
  collectionName: string,
  documentId: string
): Promise<T | null> {
  return getTenantDocumentById<T>(tenantId, collectionName, documentId);
}
