/**
 * Advanced Usage Analytics & Billing Service for EVEXA
 * Handles detailed usage tracking, overage billing, usage-based pricing, and predictive analytics
 */

import { db } from '@/lib/firebase';
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  startAfter,
  Timestamp,
  writeBatch,
  increment
} from 'firebase/firestore';
import { subscriptionService } from './subscriptionService';
import { stripePaymentService } from './stripePaymentService';

export interface UsageMetric {
  id: string;
  tenantId: string;
  userId?: string;
  category: 'exhibitions' | 'events' | 'users' | 'tasks' | 'leads' | 'vendors' | 'storage' | 'api_calls' | 'emails' | 'ai_requests' | 'custom';
  subcategory?: string;
  action: string;
  quantity: number;
  unit: string;
  cost?: number;
  metadata: {
    resourceId?: string;
    resourceType?: string;
    feature?: string;
    location?: string;
    userAgent?: string;
    ipAddress?: string;
    sessionId?: string;
    [key: string]: any;
  };
  timestamp: Date;
  billingPeriod: string; // YYYY-MM format
  processed: boolean;
  tags?: string[];
}

export interface UsageTier {
  id: string;
  name: string;
  category: UsageMetric['category'];
  minQuantity: number;
  maxQuantity?: number;
  unitPrice: number;
  currency: string;
  billingModel: 'per_unit' | 'flat_rate' | 'tiered' | 'volume';
  overage: {
    enabled: boolean;
    unitPrice?: number;
    threshold?: number;
    warningThreshold?: number;
  };
}

export interface UsageAnalytics {
  tenantId: string;
  period: {
    start: Date;
    end: Date;
    type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  };
  totalUsage: {
    [category: string]: {
      quantity: number;
      cost: number;
      unit: string;
      growth: number; // percentage change from previous period
      trend: 'increasing' | 'decreasing' | 'stable';
    };
  };
  topFeatures: Array<{
    feature: string;
    category: string;
    usage: number;
    cost: number;
    percentage: number;
  }>;
  userActivity: {
    activeUsers: number;
    totalSessions: number;
    averageSessionDuration: number;
    topUsers: Array<{
      userId: string;
      usage: number;
      cost: number;
    }>;
  };
  predictions: {
    nextPeriodUsage: {
      [category: string]: {
        predicted: number;
        confidence: number;
        estimatedCost: number;
      };
    };
    overageRisk: {
      [category: string]: {
        risk: 'low' | 'medium' | 'high';
        currentUsage: number;
        limit: number;
        daysRemaining: number;
        projectedOverage: number;
      };
    };
  };
  recommendations: Array<{
    type: 'cost_optimization' | 'usage_optimization' | 'plan_upgrade' | 'feature_adoption';
    title: string;
    description: string;
    impact: 'low' | 'medium' | 'high';
    estimatedSavings?: number;
    actionRequired: boolean;
  }>;
}

export interface OverageBilling {
  id: string;
  tenantId: string;
  billingPeriod: string;
  category: string;
  baseLimit: number;
  actualUsage: number;
  overageQuantity: number;
  overageRate: number;
  overageAmount: number;
  currency: string;
  status: 'pending' | 'billed' | 'paid' | 'disputed';
  invoiceId?: string;
  createdAt: Date;
  processedAt?: Date;
}

class AdvancedUsageAnalyticsService {
  private usageMetricsCollection = 'usage_metrics';
  private usageTiersCollection = 'usage_tiers';
  private overageBillingCollection = 'overage_billing';
  private usageAnalyticsCollection = 'usage_analytics';

  // Usage Tracking
  async trackUsage(
    tenantId: string,
    category: UsageMetric['category'],
    action: string,
    quantity: number = 1,
    options: {
      userId?: string;
      subcategory?: string;
      unit?: string;
      cost?: number;
      metadata?: Record<string, any>;
      tags?: string[];
    } = {}
  ): Promise<void> {
    try {
      const now = new Date();
      const billingPeriod = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;

      const usageMetric: Omit<UsageMetric, 'id'> = {
        tenantId,
        userId: options.userId,
        category,
        subcategory: options.subcategory,
        action,
        quantity,
        unit: options.unit || 'count',
        cost: options.cost,
        metadata: options.metadata || {},
        timestamp: now,
        billingPeriod,
        processed: false,
        tags: options.tags
      };

      await addDoc(collection(db, this.usageMetricsCollection), {
        ...usageMetric,
        timestamp: Timestamp.fromDate(now)
      });

      // Update real-time usage counters
      await this.updateUsageCounters(tenantId, category, quantity, billingPeriod);

      // Check for overage warnings
      await this.checkOverageThresholds(tenantId, category, billingPeriod);

    } catch (error) {
      console.error('Error tracking usage:', error);
      throw error;
    }
  }

  private async updateUsageCounters(
    tenantId: string,
    category: string,
    quantity: number,
    billingPeriod: string
  ): Promise<void> {
    const counterDoc = doc(db, 'usage_counters', `${tenantId}_${billingPeriod}_${category}`);
    
    try {
      await updateDoc(counterDoc, {
        quantity: increment(quantity),
        lastUpdated: Timestamp.fromDate(new Date())
      });
    } catch (error) {
      // Document doesn't exist, create it
      await updateDoc(counterDoc, {
        tenantId,
        category,
        billingPeriod,
        quantity,
        lastUpdated: Timestamp.fromDate(new Date())
      });
    }
  }

  // Usage Analytics
  async generateUsageAnalytics(
    tenantId: string,
    period: { start: Date; end: Date; type: UsageAnalytics['period']['type'] }
  ): Promise<UsageAnalytics> {
    const startTimestamp = Timestamp.fromDate(period.start);
    const endTimestamp = Timestamp.fromDate(period.end);

    // Get usage metrics for the period
    const q = query(
      collection(db, this.usageMetricsCollection),
      where('tenantId', '==', tenantId),
      where('timestamp', '>=', startTimestamp),
      where('timestamp', '<=', endTimestamp),
      orderBy('timestamp', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const metrics = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      timestamp: doc.data().timestamp.toDate()
    })) as UsageMetric[];

    // Calculate total usage by category
    const totalUsage: UsageAnalytics['totalUsage'] = {};
    const categoryTotals: Record<string, { quantity: number; cost: number }> = {};

    metrics.forEach(metric => {
      if (!categoryTotals[metric.category]) {
        categoryTotals[metric.category] = { quantity: 0, cost: 0 };
      }
      categoryTotals[metric.category].quantity += metric.quantity;
      categoryTotals[metric.category].cost += metric.cost || 0;
    });

    // Get previous period for growth calculation
    const previousPeriod = this.getPreviousPeriod(period);
    const previousMetrics = await this.getUsageForPeriod(tenantId, previousPeriod);

    Object.entries(categoryTotals).forEach(([category, totals]) => {
      const previousTotal = previousMetrics[category]?.quantity || 0;
      const growth = previousTotal > 0 ? ((totals.quantity - previousTotal) / previousTotal) * 100 : 0;
      
      totalUsage[category] = {
        quantity: totals.quantity,
        cost: totals.cost,
        unit: this.getUnitForCategory(category),
        growth,
        trend: growth > 5 ? 'increasing' : growth < -5 ? 'decreasing' : 'stable'
      };
    });

    // Calculate top features
    const featureUsage: Record<string, { usage: number; cost: number; category: string }> = {};
    metrics.forEach(metric => {
      const feature = metric.metadata.feature || metric.action;
      if (!featureUsage[feature]) {
        featureUsage[feature] = { usage: 0, cost: 0, category: metric.category };
      }
      featureUsage[feature].usage += metric.quantity;
      featureUsage[feature].cost += metric.cost || 0;
    });

    const totalFeatureUsage = Object.values(featureUsage).reduce((sum, f) => sum + f.usage, 0);
    const topFeatures = Object.entries(featureUsage)
      .map(([feature, data]) => ({
        feature,
        category: data.category,
        usage: data.usage,
        cost: data.cost,
        percentage: (data.usage / totalFeatureUsage) * 100
      }))
      .sort((a, b) => b.usage - a.usage)
      .slice(0, 10);

    // Calculate user activity
    const userSessions: Record<string, { sessions: number; totalDuration: number; usage: number; cost: number }> = {};
    metrics.forEach(metric => {
      if (metric.userId) {
        if (!userSessions[metric.userId]) {
          userSessions[metric.userId] = { sessions: 0, totalDuration: 0, usage: 0, cost: 0 };
        }
        userSessions[metric.userId].usage += metric.quantity;
        userSessions[metric.userId].cost += metric.cost || 0;
        
        if (metric.metadata.sessionId) {
          userSessions[metric.userId].sessions++;
        }
      }
    });

    const userActivity = {
      activeUsers: Object.keys(userSessions).length,
      totalSessions: Object.values(userSessions).reduce((sum, u) => sum + u.sessions, 0),
      averageSessionDuration: 0, // Would need session duration tracking
      topUsers: Object.entries(userSessions)
        .map(([userId, data]) => ({
          userId,
          usage: data.usage,
          cost: data.cost
        }))
        .sort((a, b) => b.usage - a.usage)
        .slice(0, 10)
    };

    // Generate predictions and recommendations
    const predictions = await this.generatePredictions(tenantId, totalUsage, period);
    const recommendations = await this.generateRecommendations(tenantId, totalUsage, predictions);

    const analytics: UsageAnalytics = {
      tenantId,
      period,
      totalUsage,
      topFeatures,
      userActivity,
      predictions,
      recommendations
    };

    // Cache the analytics
    await this.cacheAnalytics(analytics);

    return analytics;
  }

  private async generatePredictions(
    tenantId: string,
    currentUsage: UsageAnalytics['totalUsage'],
    period: UsageAnalytics['period']
  ): Promise<UsageAnalytics['predictions']> {
    const predictions: UsageAnalytics['predictions'] = {
      nextPeriodUsage: {},
      overageRisk: {}
    };

    // Get subscription limits
    const subscription = await subscriptionService.getTenantSubscription(tenantId);
    const plan = subscription ? await subscriptionService.getPlan(subscription.planId) : null;

    for (const [category, usage] of Object.entries(currentUsage)) {
      // Simple linear prediction based on growth trend
      const growthFactor = 1 + (usage.growth / 100);
      const predicted = Math.round(usage.quantity * growthFactor);
      const confidence = Math.max(0.6, 1 - Math.abs(usage.growth) / 100); // Lower confidence for high volatility

      predictions.nextPeriodUsage[category] = {
        predicted,
        confidence,
        estimatedCost: predicted * (usage.cost / usage.quantity || 0)
      };

      // Calculate overage risk
      if (plan?.features) {
        const limit = this.getCategoryLimit(plan.features, category);
        if (limit > 0) {
          const currentUsageInPeriod = usage.quantity;
          const daysInPeriod = this.getDaysInPeriod(period);
          const daysElapsed = this.getDaysElapsed(period);
          const daysRemaining = daysInPeriod - daysElapsed;

          const dailyAverage = currentUsageInPeriod / daysElapsed;
          const projectedTotal = currentUsageInPeriod + (dailyAverage * daysRemaining);
          const projectedOverage = Math.max(0, projectedTotal - limit);

          let risk: 'low' | 'medium' | 'high' = 'low';
          if (projectedOverage > limit * 0.2) risk = 'high';
          else if (projectedOverage > limit * 0.1) risk = 'medium';

          predictions.overageRisk[category] = {
            risk,
            currentUsage: currentUsageInPeriod,
            limit,
            daysRemaining,
            projectedOverage
          };
        }
      }
    }

    return predictions;
  }

  private async generateRecommendations(
    tenantId: string,
    currentUsage: UsageAnalytics['totalUsage'],
    predictions: UsageAnalytics['predictions']
  ): Promise<UsageAnalytics['recommendations']> {
    const recommendations: UsageAnalytics['recommendations'] = [];

    // Check for high overage risk
    Object.entries(predictions.overageRisk).forEach(([category, risk]) => {
      if (risk.risk === 'high') {
        recommendations.push({
          type: 'plan_upgrade',
          title: `High Overage Risk for ${category}`,
          description: `You're projected to exceed your ${category} limit by ${risk.projectedOverage} units. Consider upgrading your plan.`,
          impact: 'high',
          estimatedSavings: risk.projectedOverage * 0.5, // Assume overage costs 2x normal rate
          actionRequired: true
        });
      }
    });

    // Check for cost optimization opportunities
    Object.entries(currentUsage).forEach(([category, usage]) => {
      if (usage.growth < -20) {
        recommendations.push({
          type: 'cost_optimization',
          title: `Underutilized ${category} Capacity`,
          description: `Your ${category} usage has decreased by ${Math.abs(usage.growth).toFixed(1)}%. Consider downgrading to save costs.`,
          impact: 'medium',
          estimatedSavings: usage.cost * 0.3,
          actionRequired: false
        });
      }
    });

    // Feature adoption recommendations
    const lowUsageFeatures = Object.entries(currentUsage)
      .filter(([_, usage]) => usage.quantity < 10)
      .map(([category]) => category);

    if (lowUsageFeatures.length > 0) {
      recommendations.push({
        type: 'feature_adoption',
        title: 'Underutilized Features',
        description: `You have low usage in: ${lowUsageFeatures.join(', ')}. Explore these features to maximize your subscription value.`,
        impact: 'low',
        actionRequired: false
      });
    }

    return recommendations;
  }

  // Overage Billing
  async processOverageBilling(tenantId: string, billingPeriod: string): Promise<OverageBilling[]> {
    const subscription = await subscriptionService.getTenantSubscription(tenantId);
    if (!subscription) {
      throw new Error('No subscription found for tenant');
    }

    const plan = await subscriptionService.getPlan(subscription.planId);
    if (!plan) {
      throw new Error('Plan not found');
    }

    const overages: OverageBilling[] = [];
    const usageTiers = await this.getUsageTiers();

    // Get usage for the billing period
    const usage = await this.getUsageForBillingPeriod(tenantId, billingPeriod);

    for (const [category, usageData] of Object.entries(usage)) {
      const limit = this.getCategoryLimit(plan.features, category);
      const tier = usageTiers.find(t => t.category === category);

      if (limit > 0 && usageData.quantity > limit && tier?.overage.enabled) {
        const overageQuantity = usageData.quantity - limit;
        const overageRate = tier.overage.unitPrice || tier.unitPrice * 2; // Default to 2x rate
        const overageAmount = overageQuantity * overageRate;

        const overage: Omit<OverageBilling, 'id'> = {
          tenantId,
          billingPeriod,
          category,
          baseLimit: limit,
          actualUsage: usageData.quantity,
          overageQuantity,
          overageRate,
          overageAmount,
          currency: tier.currency,
          status: 'pending',
          createdAt: new Date()
        };

        const docRef = await addDoc(collection(db, this.overageBillingCollection), {
          ...overage,
          createdAt: Timestamp.fromDate(overage.createdAt)
        });

        overages.push({ ...overage, id: docRef.id });
      }
    }

    // Create Stripe invoice for overages if any
    if (overages.length > 0) {
      await this.createOverageInvoice(tenantId, overages);
    }

    return overages;
  }

  private async createOverageInvoice(tenantId: string, overages: OverageBilling[]): Promise<void> {
    const subscription = await subscriptionService.getTenantSubscription(tenantId);
    if (!subscription?.billing.customerId) {
      throw new Error('No customer ID found for billing');
    }

    const lineItems = overages.map(overage => ({
      description: `${overage.category} overage (${overage.overageQuantity} units over limit)`,
      amount: overage.overageAmount,
      quantity: 1
    }));

    const invoice = await stripePaymentService.createInvoice(
      subscription.billing.customerId,
      lineItems,
      {
        currency: overages[0].currency,
        metadata: {
          type: 'overage_billing',
          tenantId,
          billingPeriod: overages[0].billingPeriod
        }
      }
    );

    // Update overage records with invoice ID
    const batch = writeBatch(db);
    overages.forEach(overage => {
      const overageRef = doc(db, this.overageBillingCollection, overage.id);
      batch.update(overageRef, {
        invoiceId: invoice.id,
        status: 'billed',
        processedAt: Timestamp.fromDate(new Date())
      });
    });

    await batch.commit();
  }

  // Helper Methods
  private async getUsageForPeriod(
    tenantId: string,
    period: { start: Date; end: Date }
  ): Promise<Record<string, { quantity: number; cost: number }>> {
    const q = query(
      collection(db, this.usageMetricsCollection),
      where('tenantId', '==', tenantId),
      where('timestamp', '>=', Timestamp.fromDate(period.start)),
      where('timestamp', '<=', Timestamp.fromDate(period.end))
    );

    const querySnapshot = await getDocs(q);
    const usage: Record<string, { quantity: number; cost: number }> = {};

    querySnapshot.docs.forEach(doc => {
      const metric = doc.data() as UsageMetric;
      if (!usage[metric.category]) {
        usage[metric.category] = { quantity: 0, cost: 0 };
      }
      usage[metric.category].quantity += metric.quantity;
      usage[metric.category].cost += metric.cost || 0;
    });

    return usage;
  }

  private async getUsageForBillingPeriod(
    tenantId: string,
    billingPeriod: string
  ): Promise<Record<string, { quantity: number; cost: number }>> {
    const q = query(
      collection(db, this.usageMetricsCollection),
      where('tenantId', '==', tenantId),
      where('billingPeriod', '==', billingPeriod)
    );

    const querySnapshot = await getDocs(q);
    const usage: Record<string, { quantity: number; cost: number }> = {};

    querySnapshot.docs.forEach(doc => {
      const metric = doc.data() as UsageMetric;
      if (!usage[metric.category]) {
        usage[metric.category] = { quantity: 0, cost: 0 };
      }
      usage[metric.category].quantity += metric.quantity;
      usage[metric.category].cost += metric.cost || 0;
    });

    return usage;
  }

  private async getUsageTiers(): Promise<UsageTier[]> {
    const q = query(collection(db, this.usageTiersCollection));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as UsageTier[];
  }

  private getCategoryLimit(features: any, category: string): number {
    const categoryMap: Record<string, string> = {
      'exhibitions': 'maxExhibitions',
      'events': 'maxEvents',
      'users': 'maxUsers',
      'tasks': 'maxTasks',
      'leads': 'maxLeads',
      'vendors': 'maxVendors',
      'storage': 'storageGB',
      'api_calls': 'apiCallsPerMonth',
      'emails': 'emailsPerMonth'
    };

    const featureKey = categoryMap[category];
    return featureKey ? features[featureKey] || 0 : 0;
  }

  private getUnitForCategory(category: string): string {
    const unitMap: Record<string, string> = {
      'exhibitions': 'exhibitions',
      'events': 'events',
      'users': 'users',
      'tasks': 'tasks',
      'leads': 'leads',
      'vendors': 'vendors',
      'storage': 'GB',
      'api_calls': 'calls',
      'emails': 'emails',
      'ai_requests': 'requests'
    };

    return unitMap[category] || 'units';
  }

  private getPreviousPeriod(period: UsageAnalytics['period']): { start: Date; end: Date } {
    const { start, end, type } = period;
    const duration = end.getTime() - start.getTime();

    return {
      start: new Date(start.getTime() - duration),
      end: new Date(start.getTime())
    };
  }

  private getDaysInPeriod(period: UsageAnalytics['period']): number {
    return Math.ceil((period.end.getTime() - period.start.getTime()) / (1000 * 60 * 60 * 24));
  }

  private getDaysElapsed(period: UsageAnalytics['period']): number {
    const now = new Date();
    const elapsed = Math.min(now.getTime(), period.end.getTime()) - period.start.getTime();
    return Math.ceil(elapsed / (1000 * 60 * 60 * 24));
  }

  private async cacheAnalytics(analytics: UsageAnalytics): Promise<void> {
    const cacheKey = `${analytics.tenantId}_${analytics.period.type}_${analytics.period.start.getTime()}`;

    await updateDoc(doc(db, this.usageAnalyticsCollection, cacheKey), {
      ...analytics,
      'period.start': Timestamp.fromDate(analytics.period.start),
      'period.end': Timestamp.fromDate(analytics.period.end),
      cachedAt: Timestamp.fromDate(new Date())
    });
  }

  private async checkOverageThresholds(
    tenantId: string,
    category: string,
    billingPeriod: string
  ): Promise<void> {
    const subscription = await subscriptionService.getTenantSubscription(tenantId);
    if (!subscription) return;

    const plan = await subscriptionService.getPlan(subscription.planId);
    if (!plan) return;

    const limit = this.getCategoryLimit(plan.features, category);
    if (limit <= 0) return;

    // Get current usage for the billing period
    const usage = await this.getUsageForBillingPeriod(tenantId, billingPeriod);
    const currentUsage = usage[category]?.quantity || 0;

    const usagePercentage = (currentUsage / limit) * 100;

    // Send warnings at 80% and 95% thresholds
    if (usagePercentage >= 80 && usagePercentage < 95) {
      await this.sendUsageWarning(tenantId, category, currentUsage, limit, '80%');
    } else if (usagePercentage >= 95) {
      await this.sendUsageWarning(tenantId, category, currentUsage, limit, '95%');
    }
  }

  private async sendUsageWarning(
    tenantId: string,
    category: string,
    currentUsage: number,
    limit: number,
    threshold: string
  ): Promise<void> {
    // This would integrate with your notification system
    console.log(`Usage warning for ${tenantId}: ${category} at ${threshold} (${currentUsage}/${limit})`);

    // You could send email notifications, in-app notifications, etc.
    // await notificationService.sendUsageWarning(tenantId, category, currentUsage, limit, threshold);
  }

  // Public API Methods
  async getUsageAnalytics(
    tenantId: string,
    period: { start: Date; end: Date; type: UsageAnalytics['period']['type'] }
  ): Promise<UsageAnalytics> {
    return this.generateUsageAnalytics(tenantId, period);
  }

  async getCurrentUsage(tenantId: string): Promise<Record<string, { quantity: number; cost: number }>> {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    return this.getUsageForPeriod(tenantId, { start: startOfMonth, end: now });
  }

  async getOverageBilling(tenantId: string, billingPeriod?: string): Promise<OverageBilling[]> {
    let q = query(
      collection(db, this.overageBillingCollection),
      where('tenantId', '==', tenantId),
      orderBy('createdAt', 'desc')
    );

    if (billingPeriod) {
      q = query(q, where('billingPeriod', '==', billingPeriod));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt.toDate(),
      processedAt: doc.data().processedAt?.toDate()
    })) as OverageBilling[];
  }
}

export const advancedUsageAnalyticsService = new AdvancedUsageAnalyticsService();
