/**
 * Financial Migration Service
 * Migrates existing budget, expense, purchase request/order data to consolidated 'financials' collection
 */

import {
  collection,
  doc,
  getDocs,
  addDoc,
  query,
  where,
  writeBatch,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import type { Budget, Expense, PurchaseRequest, PurchaseOrder } from '@/types/firestore';
import type { FinancialDocument } from './refactoredFinancialService';

export interface MigrationResult {
  success: boolean;
  migratedCounts: {
    budgets: number;
    expenses: number;
    purchaseRequests: number;
    purchaseOrders: number;
  };
  errors: string[];
  totalMigrated: number;
}

class FinancialMigrationService {
  /**
   * Migrate all financial data for a specific tenant
   */
  async migrateTenantFinancialData(tenantId: string): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: false,
      migratedCounts: {
        budgets: 0,
        expenses: 0,
        purchaseRequests: 0,
        purchaseOrders: 0
      },
      errors: [],
      totalMigrated: 0
    };

    try {
      console.log(`🔄 Starting financial data migration for tenant: ${tenantId}`);

      // Migrate budgets
      const budgetCount = await this.migrateBudgets(tenantId);
      result.migratedCounts.budgets = budgetCount;

      // Migrate expenses
      const expenseCount = await this.migrateExpenses(tenantId);
      result.migratedCounts.expenses = expenseCount;

      // Migrate purchase requests
      const prCount = await this.migratePurchaseRequests(tenantId);
      result.migratedCounts.purchaseRequests = prCount;

      // Migrate purchase orders
      const poCount = await this.migratePurchaseOrders(tenantId);
      result.migratedCounts.purchaseOrders = poCount;

      result.totalMigrated = budgetCount + expenseCount + prCount + poCount;
      result.success = true;

      console.log(`✅ Migration completed for tenant ${tenantId}:`, result.migratedCounts);
      
    } catch (error) {
      console.error(`❌ Migration failed for tenant ${tenantId}:`, error);
      result.errors.push(error instanceof Error ? error.message : 'Unknown error');
    }

    return result;
  }

  /**
   * Migrate budgets to financials collection
   */
  private async migrateBudgets(tenantId: string): Promise<number> {
    const budgetsQuery = query(
      collection(db, COLLECTIONS.BUDGET_ALLOCATIONS),
      where('tenantId', '==', tenantId)
    );

    const budgetsSnapshot = await getDocs(budgetsQuery);
    const batch = writeBatch(db);
    let count = 0;

    budgetsSnapshot.docs.forEach((budgetDoc) => {
      const budget = budgetDoc.data() as Budget;
      
      const financialDoc: Omit<FinancialDocument, 'id'> = {
        type: 'budget',
        tenantId,
        activityId: budget.activityId,
        activityType: budget.activityType,
        activityName: budget.activityName,
        amount: budget.totalBudget,
        currency: budget.currency,
        status: budget.status || 'draft',
        description: budget.notes,
        
        // Budget-specific fields
        totalBudget: budget.totalBudget,
        categories: budget.categories,
        totalSpent: budget.totalSpent,
        budgetHolder: budget.budgetHolder,
        fiscalYear: budget.fiscalYear,
        
        // Audit fields
        createdByUserId: budget.createdByUserId,
        createdByName: budget.createdByName,
        updatedByUserId: budget.updatedByUserId,
        updatedByName: budget.updatedByName,
        createdAt: budget.createdAt || Timestamp.now(),
        updatedAt: budget.updatedAt || Timestamp.now()
      };

      const newDocRef = doc(collection(db, COLLECTIONS.FINANCIALS));
      batch.set(newDocRef, financialDoc);
      count++;
    });

    if (count > 0) {
      await batch.commit();
      console.log(`✅ Migrated ${count} budgets for tenant ${tenantId}`);
    }

    return count;
  }

  /**
   * Migrate expenses to financials collection
   */
  private async migrateExpenses(tenantId: string): Promise<number> {
    const expensesQuery = query(
      collection(db, COLLECTIONS.EXPENSE_RECORDS),
      where('tenantId', '==', tenantId)
    );

    const expensesSnapshot = await getDocs(expensesQuery);
    const batch = writeBatch(db);
    let count = 0;

    expensesSnapshot.docs.forEach((expenseDoc) => {
      const expense = expenseDoc.data() as Expense;
      
      const financialDoc: Omit<FinancialDocument, 'id'> = {
        type: 'expense',
        tenantId,
        activityId: expense.activityId,
        activityType: expense.activityType || 'Exhibition',
        activityName: expense.activityId, // Will be updated by data duplication sync
        amount: expense.amount,
        currency: expense.currency,
        status: expense.status || 'Pending',
        description: expense.description,
        
        // Expense-specific fields
        expenseName: expense.expenseName,
        budgetCategory: expense.budgetCategory,
        taxAmount: expense.taxAmount,
        vendorId: expense.vendorId,
        vendorName: expense.vendorName,
        transactionDate: expense.transactionDate,
        receiptUrl: expense.receiptUrl,
        
        // Audit fields
        createdByUserId: expense.createdByUserId,
        createdByName: expense.createdByName,
        updatedByUserId: expense.updatedByUserId,
        updatedByName: expense.updatedByName,
        createdAt: expense.createdAt || Timestamp.now(),
        updatedAt: expense.updatedAt || Timestamp.now()
      };

      const newDocRef = doc(collection(db, COLLECTIONS.FINANCIALS));
      batch.set(newDocRef, financialDoc);
      count++;
    });

    if (count > 0) {
      await batch.commit();
      console.log(`✅ Migrated ${count} expenses for tenant ${tenantId}`);
    }

    return count;
  }

  /**
   * Migrate purchase requests to financials collection
   */
  private async migratePurchaseRequests(tenantId: string): Promise<number> {
    const prQuery = query(
      collection(db, COLLECTIONS.PURCHASE_REQUESTS),
      where('tenantId', '==', tenantId)
    );

    const prSnapshot = await getDocs(prQuery);
    const batch = writeBatch(db);
    let count = 0;

    prSnapshot.docs.forEach((prDoc) => {
      const pr = prDoc.data() as PurchaseRequest;
      
      const financialDoc: Omit<FinancialDocument, 'id'> = {
        type: 'purchase_request',
        tenantId,
        activityId: pr.activityId,
        activityType: pr.activityType || 'Exhibition',
        activityName: pr.activityName || pr.activityId,
        amount: pr.totalAmount || pr.estimatedCost || 0,
        currency: pr.currency || 'USD',
        status: pr.status || 'Draft',
        description: pr.description,
        
        // Purchase request specific fields
        requestedBy: pr.requestedByName,
        items: pr.items,
        
        // Audit fields
        createdByUserId: pr.createdByUserId,
        createdByName: pr.createdByName,
        updatedByUserId: pr.updatedByUserId,
        updatedByName: pr.updatedByName,
        createdAt: pr.createdAt || Timestamp.now(),
        updatedAt: pr.updatedAt || Timestamp.now()
      };

      const newDocRef = doc(collection(db, COLLECTIONS.FINANCIALS));
      batch.set(newDocRef, financialDoc);
      count++;
    });

    if (count > 0) {
      await batch.commit();
      console.log(`✅ Migrated ${count} purchase requests for tenant ${tenantId}`);
    }

    return count;
  }

  /**
   * Migrate purchase orders to financials collection
   */
  private async migratePurchaseOrders(tenantId: string): Promise<number> {
    const poQuery = query(
      collection(db, COLLECTIONS.PURCHASE_ORDERS),
      where('tenantId', '==', tenantId)
    );

    const poSnapshot = await getDocs(poQuery);
    const batch = writeBatch(db);
    let count = 0;

    poSnapshot.docs.forEach((poDoc) => {
      const po = poDoc.data() as PurchaseOrder;
      
      const financialDoc: Omit<FinancialDocument, 'id'> = {
        type: 'purchase_order',
        tenantId,
        activityId: po.activityId,
        activityType: po.activityType || 'Exhibition',
        activityName: po.activityName || po.activityId,
        amount: po.totalAmount,
        currency: po.currency,
        status: po.status || 'Draft',
        description: po.notes,
        
        // Purchase order specific fields
        vendorName: po.vendorName,
        items: po.items,
        subtotal: po.subtotal,
        totalAmount: po.totalAmount,
        orderDate: po.orderDate,
        expectedDeliveryDate: po.expectedDeliveryDate,
        
        // Audit fields
        createdByUserId: po.createdByUserId,
        createdByName: po.createdByName,
        updatedByUserId: po.updatedByUserId,
        updatedByName: po.updatedByName,
        createdAt: po.createdAt || Timestamp.now(),
        updatedAt: po.updatedAt || Timestamp.now()
      };

      const newDocRef = doc(collection(db, COLLECTIONS.FINANCIALS));
      batch.set(newDocRef, financialDoc);
      count++;
    });

    if (count > 0) {
      await batch.commit();
      console.log(`✅ Migrated ${count} purchase orders for tenant ${tenantId}`);
    }

    return count;
  }

  /**
   * Check if migration is needed for a tenant
   */
  async checkMigrationStatus(tenantId: string): Promise<{
    needsMigration: boolean;
    legacyDataCounts: {
      budgets: number;
      expenses: number;
      purchaseRequests: number;
      purchaseOrders: number;
    };
    newDataCount: number;
  }> {
    // Check legacy collections
    const [budgetsSnapshot, expensesSnapshot, prSnapshot, poSnapshot, financialsSnapshot] = await Promise.all([
      getDocs(query(collection(db, COLLECTIONS.BUDGET_ALLOCATIONS), where('tenantId', '==', tenantId))),
      getDocs(query(collection(db, COLLECTIONS.EXPENSE_RECORDS), where('tenantId', '==', tenantId))),
      getDocs(query(collection(db, COLLECTIONS.PURCHASE_REQUESTS), where('tenantId', '==', tenantId))),
      getDocs(query(collection(db, COLLECTIONS.PURCHASE_ORDERS), where('tenantId', '==', tenantId))),
      getDocs(query(collection(db, COLLECTIONS.FINANCIALS), where('tenantId', '==', tenantId)))
    ]);

    const legacyDataCounts = {
      budgets: budgetsSnapshot.size,
      expenses: expensesSnapshot.size,
      purchaseRequests: prSnapshot.size,
      purchaseOrders: poSnapshot.size
    };

    const totalLegacyData = Object.values(legacyDataCounts).reduce((sum, count) => sum + count, 0);
    const needsMigration = totalLegacyData > 0 && financialsSnapshot.size === 0;

    return {
      needsMigration,
      legacyDataCounts,
      newDataCount: financialsSnapshot.size
    };
  }
}

// Export singleton instance
export const financialMigrationService = new FinancialMigrationService();
