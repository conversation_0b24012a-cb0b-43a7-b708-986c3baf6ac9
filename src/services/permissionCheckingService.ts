/**
 * Permission Checking Service
 * Comprehensive permission checking system with caching and performance optimization
 */

import { 
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { validateTenantId } from './tenantIdHelperService';
import { createTenantPersonaService } from './tenantPersonaService';
import { 
  EvexaModule,
  PermissionAction,
  TenantPersona,
  ModuleAccess,
  MODULE_METADATA
} from '@/types/personas';

// ===== PERMISSION CHECKING TYPES =====

export interface PermissionCheckResult {
  hasPermission: boolean;
  reason: 'granted' | 'denied' | 'no_persona' | 'invalid_user' | 'invalid_module';
  details?: string;
  persona?: TenantPersona;
  checkedAt: Date;
}

export interface BulkPermissionCheckResult {
  userId: string;
  checks: Array<{
    module: EvexaModule;
    action: PermissionAction;
    result: PermissionCheckResult;
  }>;
  summary: {
    totalChecks: number;
    granted: number;
    denied: number;
    successRate: number;
  };
}

export interface PermissionOverride {
  id: string;
  userId: string;
  tenantId: string;
  module: EvexaModule;
  action: PermissionAction;
  granted: boolean;
  reason: string;
  createdBy: string;
  expiresAt?: Date;
  createdAt: Date;
  isActive: boolean;
}

// ===== PERMISSION CACHE =====

interface CacheEntry {
  result: PermissionCheckResult;
  expiresAt: number;
}

class PermissionCache {
  private cache = new Map<string, CacheEntry>();
  private readonly TTL = 5 * 60 * 1000; // 5 minutes

  private getCacheKey(userId: string, module: EvexaModule, action: PermissionAction): string {
    return `${userId}:${module}:${action}`;
  }

  get(userId: string, module: EvexaModule, action: PermissionAction): PermissionCheckResult | null {
    const key = this.getCacheKey(userId, module, action);
    const entry = this.cache.get(key);
    
    if (!entry || Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.result;
  }

  set(userId: string, module: EvexaModule, action: PermissionAction, result: PermissionCheckResult): void {
    const key = this.getCacheKey(userId, module, action);
    this.cache.set(key, {
      result,
      expiresAt: Date.now() + this.TTL
    });
  }

  invalidateUser(userId: string): void {
    for (const [key] of this.cache) {
      if (key.startsWith(`${userId}:`)) {
        this.cache.delete(key);
      }
    }
  }

  clear(): void {
    this.cache.clear();
  }

  getStats(): { size: number; hitRate: number } {
    return {
      size: this.cache.size,
      hitRate: 0 // Would need to track hits/misses for accurate rate
    };
  }
}

// ===== PERMISSION CHECKING SERVICE =====

export class PermissionCheckingService {
  private tenantId: string;
  private personaService: ReturnType<typeof createTenantPersonaService>;
  private cache = new PermissionCache();

  constructor(tenantId: string) {
    validateTenantId(tenantId);
    this.tenantId = tenantId;
    this.personaService = createTenantPersonaService(tenantId);
  }

  // ===== CORE PERMISSION CHECKING =====

  /**
   * Check if user has permission for specific module and action
   */
  async checkPermission(
    userId: string,
    module: EvexaModule,
    action: PermissionAction,
    useCache: boolean = true
  ): Promise<PermissionCheckResult> {
    // Check cache first
    if (useCache) {
      const cached = this.cache.get(userId, module, action);
      if (cached) {
        return cached;
      }
    }

    const result = await this.performPermissionCheck(userId, module, action);
    
    // Cache the result
    if (useCache) {
      this.cache.set(userId, module, action, result);
    }
    
    return result;
  }

  /**
   * Perform the actual permission check
   */
  private async performPermissionCheck(
    userId: string,
    module: EvexaModule,
    action: PermissionAction
  ): Promise<PermissionCheckResult> {
    const baseResult = {
      hasPermission: false,
      checkedAt: new Date()
    };

    try {
      // Validate module exists
      if (!MODULE_METADATA[module]) {
        return {
          ...baseResult,
          reason: 'invalid_module',
          details: `Module ${module} does not exist`
        };
      }

      // Check for permission overrides first
      const override = await this.getPermissionOverride(userId, module, action);
      if (override) {
        return {
          ...baseResult,
          hasPermission: override.granted,
          reason: override.granted ? 'granted' : 'denied',
          details: `Override: ${override.reason}`
        };
      }

      // Get user's persona
      const persona = await this.personaService.getUserPersona(userId);
      if (!persona) {
        return {
          ...baseResult,
          reason: 'no_persona',
          details: 'User has no assigned persona'
        };
      }

      // Check persona permissions
      const modulePermission = persona.permissions.modules.find(m => m.module === module);
      if (!modulePermission) {
        return {
          ...baseResult,
          reason: 'denied',
          details: `Persona ${persona.name} does not have access to module ${module}`,
          persona
        };
      }

      const hasPermission = modulePermission.actions.includes(action);
      return {
        ...baseResult,
        hasPermission,
        reason: hasPermission ? 'granted' : 'denied',
        details: hasPermission 
          ? `Granted by persona ${persona.name}` 
          : `Persona ${persona.name} does not have ${action} permission for ${module}`,
        persona
      };

    } catch (error) {
      console.error('Error checking permission:', error);
      return {
        ...baseResult,
        reason: 'denied',
        details: `Error checking permission: ${error}`
      };
    }
  }

  /**
   * Check multiple permissions at once
   */
  async checkBulkPermissions(
    userId: string,
    checks: Array<{ module: EvexaModule; action: PermissionAction }>
  ): Promise<BulkPermissionCheckResult> {
    const results = await Promise.all(
      checks.map(async ({ module, action }) => ({
        module,
        action,
        result: await this.checkPermission(userId, module, action)
      }))
    );

    const granted = results.filter(r => r.result.hasPermission).length;
    const denied = results.length - granted;

    return {
      userId,
      checks: results,
      summary: {
        totalChecks: results.length,
        granted,
        denied,
        successRate: results.length > 0 ? (granted / results.length) * 100 : 0
      }
    };
  }

  /**
   * Check if user is super admin
   */
  private isSuperAdmin(userId: string): boolean {
    try {
      const { SUPER_ADMIN_USER_IDS } = require('@/services/superAdminService');
      return SUPER_ADMIN_USER_IDS.includes(userId);
    } catch {
      return false;
    }
  }

  /**
   * Get all accessible modules for user
   */
  async getUserAccessibleModules(userId: string): Promise<ModuleAccess[]> {
    // SUPER ADMIN BYPASS: Super admin has access to all modules
    if (this.isSuperAdmin(userId)) {
      return Object.values(MODULE_METADATA).map(moduleInfo => ({
        module: moduleInfo.id,
        hasAccess: true,
        permissions: ['create', 'read', 'update', 'delete', 'manage'] as PermissionAction[],
        reason: 'super_admin'
      }));
    }

    const persona = await this.personaService.getUserPersona(userId);
    if (!persona) {
      return [];
    }

    const moduleAccess: ModuleAccess[] = [];

    // Check all modules
    for (const moduleInfo of Object.values(MODULE_METADATA)) {
      const permissions = persona.permissions.modules
        .find(m => m.module === moduleInfo.id)?.actions || [];

      // Check for overrides
      const overrides = await this.getUserModuleOverrides(userId, moduleInfo.id);
      const hasOverrideAccess = overrides.some(o => o.granted && o.isActive);
      const hasPersonaAccess = permissions.length > 0;

      moduleAccess.push({
        module: moduleInfo.id,
        hasAccess: hasOverrideAccess || hasPersonaAccess,
        permissions,
        reason: hasOverrideAccess ? 'custom' : hasPersonaAccess ? 'persona' : 'subscription'
      });
    }

    return moduleAccess;
  }

  // ===== PERMISSION OVERRIDES =====

  /**
   * Get permission override for specific user, module, and action
   */
  private async getPermissionOverride(
    userId: string,
    module: EvexaModule,
    action: PermissionAction
  ): Promise<PermissionOverride | null> {
    try {
      const overrideQuery = query(
        collection(db, COLLECTIONS.PERMISSION_OVERRIDES),
        where('tenantId', '==', this.tenantId),
        where('userId', '==', userId),
        where('module', '==', module),
        where('action', '==', action),
        where('isActive', '==', true),
        orderBy('createdAt', 'desc'),
        limit(1)
      );

      const snapshot = await getDocs(overrideQuery);
      if (snapshot.empty) {
        return null;
      }

      const override = { id: snapshot.docs[0].id, ...snapshot.docs[0].data() } as PermissionOverride;
      
      // Check if override has expired
      if (override.expiresAt && new Date() > override.expiresAt.toDate()) {
        return null;
      }

      return override;
    } catch (error) {
      console.error('Error getting permission override:', error);
      return null;
    }
  }

  /**
   * Get all permission overrides for user and module
   */
  private async getUserModuleOverrides(
    userId: string,
    module: EvexaModule
  ): Promise<PermissionOverride[]> {
    try {
      const overrideQuery = query(
        collection(db, COLLECTIONS.PERMISSION_OVERRIDES),
        where('tenantId', '==', this.tenantId),
        where('userId', '==', userId),
        where('module', '==', module),
        where('isActive', '==', true)
      );

      const snapshot = await getDocs(overrideQuery);
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as PermissionOverride));
    } catch (error) {
      console.error('Error getting user module overrides:', error);
      return [];
    }
  }

  /**
   * Create permission override
   */
  async createPermissionOverride(
    userId: string,
    module: EvexaModule,
    action: PermissionAction,
    granted: boolean,
    reason: string,
    createdBy: string,
    expiresAt?: Date
  ): Promise<PermissionOverride> {
    const override: Omit<PermissionOverride, 'id'> = {
      userId,
      tenantId: this.tenantId,
      module,
      action,
      granted,
      reason,
      createdBy,
      expiresAt,
      createdAt: new Date(),
      isActive: true
    };

    const docRef = await addDoc(collection(db, COLLECTIONS.PERMISSION_OVERRIDES), override);
    
    // Invalidate cache for this user
    this.cache.invalidateUser(userId);
    
    return { id: docRef.id, ...override };
  }

  /**
   * Revoke permission override
   */
  async revokePermissionOverride(overrideId: string): Promise<void> {
    const overrideRef = doc(db, COLLECTIONS.PERMISSION_OVERRIDES, overrideId);
    const overrideDoc = await getDoc(overrideRef);

    if (!overrideDoc.exists()) {
      throw new Error('Permission override not found');
    }

    const override = overrideDoc.data() as PermissionOverride;
    if (override.tenantId !== this.tenantId) {
      throw new Error('Access denied');
    }

    await updateDoc(overrideRef, {
      isActive: false,
      revokedAt: serverTimestamp()
    });

    // Invalidate cache for this user
    this.cache.invalidateUser(override.userId);
  }

  // ===== SYSTEM PERMISSIONS =====

  /**
   * Check system permission
   */
  async checkSystemPermission(
    userId: string,
    permission: keyof TenantPersona['permissions']['systemPermissions']
  ): Promise<boolean> {
    try {
      const persona = await this.personaService.getUserPersona(userId);
      if (!persona) {
        return false;
      }

      return persona.permissions.systemPermissions[permission];
    } catch (error) {
      console.error('Error checking system permission:', error);
      return false;
    }
  }

  // ===== UTILITY FUNCTIONS =====

  /**
   * Invalidate cache for user
   */
  invalidateUserCache(userId: string): void {
    this.cache.invalidateUser(userId);
  }

  /**
   * Clear all cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; hitRate: number } {
    return this.cache.getStats();
  }

  /**
   * Validate user has required permissions for operation
   */
  async requirePermissions(
    userId: string,
    requiredPermissions: Array<{ module: EvexaModule; action: PermissionAction }>
  ): Promise<void> {
    const bulkResult = await this.checkBulkPermissions(userId, requiredPermissions);
    const deniedPermissions = bulkResult.checks.filter(c => !c.result.hasPermission);

    if (deniedPermissions.length > 0) {
      const deniedList = deniedPermissions
        .map(p => `${p.module}:${p.action}`)
        .join(', ');
      throw new Error(`Access denied. Missing permissions: ${deniedList}`);
    }
  }
}

// ===== CONVENIENCE FUNCTIONS =====

/**
 * Create permission checking service instance
 */
export function createPermissionCheckingService(tenantId: string): PermissionCheckingService {
  return new PermissionCheckingService(tenantId);
}

/**
 * Quick permission check
 */
export async function checkUserPermission(
  tenantId: string,
  userId: string,
  module: EvexaModule,
  action: PermissionAction
): Promise<boolean> {
  const service = createPermissionCheckingService(tenantId);
  const result = await service.checkPermission(userId, module, action);
  return result.hasPermission;
}
