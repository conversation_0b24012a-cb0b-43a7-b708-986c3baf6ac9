/**
 * Enterprise Billing Service for EVEXA
 * Handles multi-currency support, tax calculation automation, purchase order processing, and enterprise contract billing
 */

import { db } from '@/lib/firebase';
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { stripePaymentService } from './stripePaymentService';
import { invoiceManagementService } from './invoiceManagementService';

export interface CurrencyConfig {
  id: string;
  code: string; // ISO 4217 currency code
  name: string;
  symbol: string;
  decimalPlaces: number;
  isActive: boolean;
  exchangeRates: {
    [baseCurrency: string]: {
      rate: number;
      lastUpdated: Date;
      source: string;
    };
  };
  formatting: {
    locale: string;
    position: 'before' | 'after';
    spaceBetween: boolean;
  };
  metadata: {
    createdAt: Date;
    updatedAt: Date;
  };
}

export interface TaxConfiguration {
  id: string;
  tenantId: string;
  name: string;
  type: 'vat' | 'gst' | 'sales_tax' | 'custom';
  isActive: boolean;
  
  // Tax Rules
  rules: Array<{
    id: string;
    name: string;
    rate: number;
    applicableRegions: string[];
    applicableProducts: string[];
    exemptions: string[];
    effectiveDate: Date;
    expiryDate?: Date;
  }>;
  
  // Regional Settings
  regions: Array<{
    country: string;
    state?: string;
    taxId: string;
    registrationNumber?: string;
    isReverseChargeApplicable: boolean;
  }>;
  
  // Automation Settings
  automation: {
    enabled: boolean;
    provider: 'avalara' | 'taxjar' | 'vertex' | 'manual';
    apiKey?: string;
    webhookUrl?: string;
    autoCalculate: boolean;
    autoFile: boolean;
  };
  
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    lastCalculation?: Date;
  };
}

export interface PurchaseOrder {
  id: string;
  tenantId: string;
  poNumber: string;
  status: 'draft' | 'submitted' | 'approved' | 'rejected' | 'invoiced' | 'paid' | 'cancelled';
  
  // Vendor Information
  vendor: {
    id: string;
    name: string;
    email: string;
    address: string;
    taxId?: string;
    paymentTerms: string;
  };
  
  // Buyer Information
  buyer: {
    department: string;
    approver: string;
    costCenter: string;
    budgetCode: string;
    requestedBy: string;
  };
  
  // Financial Details
  financial: {
    currency: string;
    subtotal: number;
    taxAmount: number;
    totalAmount: number;
    budgetAllocated: number;
    budgetRemaining: number;
  };
  
  // Line Items
  items: Array<{
    id: string;
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    category: string;
    accountCode: string;
    taxRate?: number;
    deliveryDate?: Date;
  }>;
  
  // Approval Workflow
  approvals: Array<{
    level: number;
    approver: string;
    status: 'pending' | 'approved' | 'rejected';
    approvedAt?: Date;
    comments?: string;
    delegatedTo?: string;
  }>;
  
  // Delivery & Terms
  delivery: {
    address: string;
    expectedDate: Date;
    method: string;
    instructions?: string;
  };
  
  terms: {
    paymentTerms: string;
    deliveryTerms: string;
    warrantyPeriod?: string;
    penalties?: string;
    specialConditions?: string;
  };
  
  // Document Management
  attachments: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
    uploadedBy: string;
    uploadedAt: Date;
  }>;
  
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    lastModifiedBy: string;
  };
}

export interface EnterpriseContract {
  id: string;
  tenantId: string;
  contractNumber: string;
  status: 'draft' | 'negotiation' | 'approved' | 'active' | 'expired' | 'terminated';
  
  // Contract Parties
  parties: {
    client: {
      companyName: string;
      contactPerson: string;
      email: string;
      address: string;
      taxId: string;
    };
    vendor: {
      companyName: string;
      contactPerson: string;
      email: string;
      address: string;
      taxId: string;
    };
  };
  
  // Contract Terms
  terms: {
    startDate: Date;
    endDate: Date;
    autoRenewal: boolean;
    renewalPeriod?: number; // months
    terminationNotice: number; // days
    currency: string;
    paymentTerms: string;
  };
  
  // Pricing Structure
  pricing: {
    model: 'fixed' | 'usage_based' | 'tiered' | 'hybrid';
    baseAmount?: number;
    usageRates?: Array<{
      category: string;
      rate: number;
      unit: string;
      minimumCommitment?: number;
    }>;
    tiers?: Array<{
      name: string;
      minQuantity: number;
      maxQuantity?: number;
      rate: number;
    }>;
    discounts?: Array<{
      type: 'volume' | 'early_payment' | 'loyalty';
      threshold: number;
      discount: number;
    }>;
  };
  
  // Service Level Agreements
  sla: {
    uptime: number;
    responseTime: number;
    resolutionTime: number;
    penalties: Array<{
      metric: string;
      threshold: number;
      penalty: number;
    }>;
  };
  
  // Billing Configuration
  billing: {
    frequency: 'monthly' | 'quarterly' | 'annually';
    invoiceDay: number;
    paymentDue: number; // days
    currency: string;
    taxInclusive: boolean;
    customFields: Record<string, any>;
  };
  
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    lastModifiedBy: string;
    version: number;
  };
}

export interface TaxCalculationResult {
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  currency: string;
  breakdown: Array<{
    taxType: string;
    rate: number;
    amount: number;
    jurisdiction: string;
  }>;
  exemptions: Array<{
    type: string;
    amount: number;
    reason: string;
  }>;
  metadata: {
    calculatedAt: Date;
    provider: string;
    transactionId: string;
  };
}

class EnterpriseBillingService {
  private currenciesCollection = 'currencies';
  private taxConfigCollection = 'tax_configurations';
  private purchaseOrdersCollection = 'purchase_orders';
  private contractsCollection = 'enterprise_contracts';
  private exchangeRatesCollection = 'exchange_rates';

  // Multi-Currency Support
  async getSupportedCurrencies(): Promise<CurrencyConfig[]> {
    const q = query(
      collection(db, this.currenciesCollection),
      where('isActive', '==', true),
      orderBy('code', 'asc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        metadata: {
          createdAt: data.metadata.createdAt.toDate(),
          updatedAt: data.metadata.updatedAt.toDate()
        },
        exchangeRates: Object.entries(data.exchangeRates || {}).reduce((acc, [currency, rateData]: [string, any]) => ({
          ...acc,
          [currency]: {
            ...rateData,
            lastUpdated: rateData.lastUpdated.toDate()
          }
        }), {})
      } as CurrencyConfig;
    });
  }

  async updateExchangeRates(): Promise<void> {
    try {
      // This would integrate with a real exchange rate API like exchangerate-api.com or fixer.io
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
      const data = await response.json();
      
      const batch = writeBatch(db);
      const now = new Date();
      
      // Update exchange rates for all supported currencies
      const currencies = await this.getSupportedCurrencies();
      
      for (const currency of currencies) {
        if (data.rates[currency.code]) {
          const currencyRef = doc(db, this.currenciesCollection, currency.id);
          batch.update(currencyRef, {
            [`exchangeRates.USD.rate`]: data.rates[currency.code],
            [`exchangeRates.USD.lastUpdated`]: Timestamp.fromDate(now),
            [`exchangeRates.USD.source`]: 'exchangerate-api.com',
            'metadata.updatedAt': Timestamp.fromDate(now)
          });
        }
      }
      
      await batch.commit();
      console.log('Exchange rates updated successfully');
    } catch (error) {
      console.error('Error updating exchange rates:', error);
      throw error;
    }
  }

  async convertCurrency(
    amount: number,
    fromCurrency: string,
    toCurrency: string
  ): Promise<{ convertedAmount: number; rate: number; lastUpdated: Date }> {
    if (fromCurrency === toCurrency) {
      return { convertedAmount: amount, rate: 1, lastUpdated: new Date() };
    }

    const currencies = await this.getSupportedCurrencies();
    const fromCurrencyConfig = currencies.find(c => c.code === fromCurrency);
    const toCurrencyConfig = currencies.find(c => c.code === toCurrency);

    if (!fromCurrencyConfig || !toCurrencyConfig) {
      throw new Error(`Unsupported currency conversion: ${fromCurrency} to ${toCurrency}`);
    }

    // Use USD as base currency for conversion
    const fromRate = fromCurrencyConfig.exchangeRates.USD?.rate || 1;
    const toRate = toCurrencyConfig.exchangeRates.USD?.rate || 1;
    
    const rate = toRate / fromRate;
    const convertedAmount = amount * rate;
    
    return {
      convertedAmount: Math.round(convertedAmount * 100) / 100,
      rate,
      lastUpdated: fromCurrencyConfig.exchangeRates.USD?.lastUpdated || new Date()
    };
  }

  // Tax Calculation Automation
  async calculateTax(
    tenantId: string,
    amount: number,
    currency: string,
    customerAddress: {
      country: string;
      state?: string;
      city?: string;
      postalCode?: string;
    },
    productCategories: string[] = []
  ): Promise<TaxCalculationResult> {
    const taxConfig = await this.getTaxConfiguration(tenantId);

    if (!taxConfig || !taxConfig.automation.enabled) {
      // Manual tax calculation
      return this.calculateManualTax(amount, currency, customerAddress, taxConfig);
    }

    // Automated tax calculation based on provider
    switch (taxConfig.automation.provider) {
      case 'avalara':
        return this.calculateAvalaraTax(amount, currency, customerAddress, productCategories, taxConfig);
      case 'taxjar':
        return this.calculateTaxJarTax(amount, currency, customerAddress, productCategories, taxConfig);
      case 'vertex':
        return this.calculateVertexTax(amount, currency, customerAddress, productCategories, taxConfig);
      default:
        return this.calculateManualTax(amount, currency, customerAddress, taxConfig);
    }
  }

  private async calculateManualTax(
    amount: number,
    currency: string,
    customerAddress: { country: string; state?: string },
    taxConfig: TaxConfiguration | null
  ): Promise<TaxCalculationResult> {
    let taxAmount = 0;
    const breakdown: TaxCalculationResult['breakdown'] = [];

    if (taxConfig) {
      // Find applicable tax rules
      const applicableRules = taxConfig.rules.filter(rule => {
        const now = new Date();
        const isEffective = rule.effectiveDate <= now && (!rule.expiryDate || rule.expiryDate >= now);
        const isRegionApplicable = rule.applicableRegions.length === 0 ||
          rule.applicableRegions.some(region =>
            region === customerAddress.country ||
            region === `${customerAddress.country}-${customerAddress.state}`
          );

        return isEffective && isRegionApplicable;
      });

      // Calculate tax for each applicable rule
      for (const rule of applicableRules) {
        const ruleAmount = amount * (rule.rate / 100);
        taxAmount += ruleAmount;

        breakdown.push({
          taxType: rule.name,
          rate: rule.rate,
          amount: ruleAmount,
          jurisdiction: rule.applicableRegions[0] || 'Default'
        });
      }
    }

    return {
      subtotal: amount,
      taxAmount: Math.round(taxAmount * 100) / 100,
      totalAmount: Math.round((amount + taxAmount) * 100) / 100,
      currency,
      breakdown,
      exemptions: [],
      metadata: {
        calculatedAt: new Date(),
        provider: 'manual',
        transactionId: `manual_${Date.now()}`
      }
    };
  }

  private async calculateAvalaraTax(
    amount: number,
    currency: string,
    customerAddress: any,
    productCategories: string[],
    taxConfig: TaxConfiguration
  ): Promise<TaxCalculationResult> {
    // This would integrate with Avalara's AvaTax API
    // For now, we'll simulate the calculation
    const baseRate = 0.08; // 8% default rate
    const taxAmount = amount * baseRate;

    return {
      subtotal: amount,
      taxAmount: Math.round(taxAmount * 100) / 100,
      totalAmount: Math.round((amount + taxAmount) * 100) / 100,
      currency,
      breakdown: [{
        taxType: 'Sales Tax',
        rate: baseRate * 100,
        amount: taxAmount,
        jurisdiction: `${customerAddress.state}, ${customerAddress.country}`
      }],
      exemptions: [],
      metadata: {
        calculatedAt: new Date(),
        provider: 'avalara',
        transactionId: `avalara_${Date.now()}`
      }
    };
  }

  private async calculateTaxJarTax(
    amount: number,
    currency: string,
    customerAddress: any,
    productCategories: string[],
    taxConfig: TaxConfiguration
  ): Promise<TaxCalculationResult> {
    // This would integrate with TaxJar's API
    // Simulation for now
    const baseRate = 0.075; // 7.5% default rate
    const taxAmount = amount * baseRate;

    return {
      subtotal: amount,
      taxAmount: Math.round(taxAmount * 100) / 100,
      totalAmount: Math.round((amount + taxAmount) * 100) / 100,
      currency,
      breakdown: [{
        taxType: 'Sales Tax',
        rate: baseRate * 100,
        amount: taxAmount,
        jurisdiction: `${customerAddress.state}, ${customerAddress.country}`
      }],
      exemptions: [],
      metadata: {
        calculatedAt: new Date(),
        provider: 'taxjar',
        transactionId: `taxjar_${Date.now()}`
      }
    };
  }

  private async calculateVertexTax(
    amount: number,
    currency: string,
    customerAddress: any,
    productCategories: string[],
    taxConfig: TaxConfiguration
  ): Promise<TaxCalculationResult> {
    // This would integrate with Vertex's API
    // Simulation for now
    const baseRate = 0.085; // 8.5% default rate
    const taxAmount = amount * baseRate;

    return {
      subtotal: amount,
      taxAmount: Math.round(taxAmount * 100) / 100,
      totalAmount: Math.round((amount + taxAmount) * 100) / 100,
      currency,
      breakdown: [{
        taxType: 'Sales Tax',
        rate: baseRate * 100,
        amount: taxAmount,
        jurisdiction: `${customerAddress.state}, ${customerAddress.country}`
      }],
      exemptions: [],
      metadata: {
        calculatedAt: new Date(),
        provider: 'vertex',
        transactionId: `vertex_${Date.now()}`
      }
    };
  }

  // Purchase Order Processing
  async createPurchaseOrder(po: Omit<PurchaseOrder, 'id' | 'metadata'>): Promise<PurchaseOrder> {
    const now = new Date();
    const poData: Omit<PurchaseOrder, 'id'> = {
      ...po,
      metadata: {
        createdAt: now,
        updatedAt: now,
        createdBy: 'current-user',
        lastModifiedBy: 'current-user'
      }
    };

    const docRef = await addDoc(collection(db, this.purchaseOrdersCollection), {
      ...poData,
      'delivery.expectedDate': Timestamp.fromDate(po.delivery.expectedDate),
      'metadata.createdAt': Timestamp.fromDate(now),
      'metadata.updatedAt': Timestamp.fromDate(now),
      'approvals': po.approvals.map(approval => ({
        ...approval,
        approvedAt: approval.approvedAt ? Timestamp.fromDate(approval.approvedAt) : null
      })),
      'attachments': po.attachments.map(attachment => ({
        ...attachment,
        uploadedAt: Timestamp.fromDate(attachment.uploadedAt)
      })),
      'items': po.items.map(item => ({
        ...item,
        deliveryDate: item.deliveryDate ? Timestamp.fromDate(item.deliveryDate) : null
      }))
    });

    return { ...poData, id: docRef.id };
  }

  async approvePurchaseOrder(
    poId: string,
    approverLevel: number,
    approver: string,
    approved: boolean,
    comments?: string
  ): Promise<void> {
    const poRef = doc(db, this.purchaseOrdersCollection, poId);
    const poDoc = await getDoc(poRef);

    if (!poDoc.exists()) {
      throw new Error('Purchase order not found');
    }

    const po = poDoc.data() as PurchaseOrder;
    const approvals = [...po.approvals];

    // Update the specific approval level
    const approvalIndex = approvals.findIndex(a => a.level === approverLevel);
    if (approvalIndex !== -1) {
      approvals[approvalIndex] = {
        ...approvals[approvalIndex],
        status: approved ? 'approved' : 'rejected',
        approvedAt: new Date(),
        comments
      };
    }

    // Determine overall status
    let newStatus = po.status;
    if (!approved) {
      newStatus = 'rejected';
    } else {
      const allApproved = approvals.every(a => a.status === 'approved');
      if (allApproved) {
        newStatus = 'approved';
      }
    }

    await updateDoc(poRef, {
      approvals: approvals.map(approval => ({
        ...approval,
        approvedAt: approval.approvedAt ? Timestamp.fromDate(approval.approvedAt) : null
      })),
      status: newStatus,
      'metadata.updatedAt': Timestamp.fromDate(new Date()),
      'metadata.lastModifiedBy': approver
    });

    // If fully approved, trigger invoice generation
    if (newStatus === 'approved') {
      await this.generateInvoiceFromPO(poId);
    }
  }

  private async generateInvoiceFromPO(poId: string): Promise<void> {
    const poDoc = await getDoc(doc(db, this.purchaseOrdersCollection, poId));
    if (!poDoc.exists()) return;

    const po = poDoc.data() as PurchaseOrder;

    // Calculate tax for the PO
    const taxResult = await this.calculateTax(
      po.tenantId,
      po.financial.subtotal,
      po.financial.currency,
      { country: 'US', state: 'CA' }, // This would come from vendor address
      po.items.map(item => item.category)
    );

    // Create invoice using the invoice management service
    const invoice = await invoiceManagementService.createInvoice({
      tenantId: po.tenantId,
      templateId: 'default',
      status: 'draft',
      type: 'one_time',
      customer: {
        id: po.vendor.id,
        name: po.vendor.name,
        email: po.vendor.email,
        address: po.vendor.address,
        taxId: po.vendor.taxId
      },
      details: {
        issueDate: new Date(),
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        currency: po.financial.currency,
        subtotal: po.financial.subtotal,
        taxAmount: taxResult.taxAmount,
        discountAmount: 0,
        totalAmount: taxResult.totalAmount,
        paidAmount: 0,
        balanceAmount: taxResult.totalAmount
      },
      lineItems: po.items.map(item => ({
        id: item.id,
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
        taxRate: item.taxRate,
        category: item.category
      })),
      payment: {},
      approval: {
        required: false,
        status: 'approved'
      },
      notes: `Generated from Purchase Order: ${po.poNumber}`,
      internalNotes: `PO ID: ${poId}, Department: ${po.buyer.department}`
    });

    // Update PO status
    await updateDoc(doc(db, this.purchaseOrdersCollection, poId), {
      status: 'invoiced',
      'metadata.updatedAt': Timestamp.fromDate(new Date())
    });
  }

  // Enterprise Contract Billing
  async createEnterpriseContract(contract: Omit<EnterpriseContract, 'id' | 'metadata'>): Promise<EnterpriseContract> {
    const now = new Date();
    const contractData: Omit<EnterpriseContract, 'id'> = {
      ...contract,
      metadata: {
        createdAt: now,
        updatedAt: now,
        createdBy: 'current-user',
        lastModifiedBy: 'current-user',
        version: 1
      }
    };

    const docRef = await addDoc(collection(db, this.contractsCollection), {
      ...contractData,
      'terms.startDate': Timestamp.fromDate(contract.terms.startDate),
      'terms.endDate': Timestamp.fromDate(contract.terms.endDate),
      'metadata.createdAt': Timestamp.fromDate(now),
      'metadata.updatedAt': Timestamp.fromDate(now)
    });

    return { ...contractData, id: docRef.id };
  }

  async generateContractInvoice(
    contractId: string,
    billingPeriod: { start: Date; end: Date },
    usageData?: Record<string, number>
  ): Promise<string> {
    const contractDoc = await getDoc(doc(db, this.contractsCollection, contractId));
    if (!contractDoc.exists()) {
      throw new Error('Contract not found');
    }

    const contract = contractDoc.data() as EnterpriseContract;

    // Calculate billing amount based on pricing model
    let subtotal = 0;
    const lineItems: any[] = [];

    switch (contract.pricing.model) {
      case 'fixed':
        subtotal = contract.pricing.baseAmount || 0;
        lineItems.push({
          id: 'fixed_fee',
          description: `Fixed fee for ${billingPeriod.start.toLocaleDateString()} - ${billingPeriod.end.toLocaleDateString()}`,
          quantity: 1,
          unitPrice: subtotal,
          totalPrice: subtotal,
          category: 'subscription'
        });
        break;

      case 'usage_based':
        if (usageData && contract.pricing.usageRates) {
          for (const rate of contract.pricing.usageRates) {
            const usage = usageData[rate.category] || 0;
            const amount = Math.max(usage, rate.minimumCommitment || 0) * rate.rate;
            subtotal += amount;

            lineItems.push({
              id: `usage_${rate.category}`,
              description: `${rate.category} usage: ${usage} ${rate.unit}`,
              quantity: usage,
              unitPrice: rate.rate,
              totalPrice: amount,
              category: rate.category
            });
          }
        }
        break;

      case 'tiered':
        if (usageData && contract.pricing.tiers) {
          const totalUsage = Object.values(usageData).reduce((sum, val) => sum + val, 0);
          const applicableTier = contract.pricing.tiers.find(tier =>
            totalUsage >= tier.minQuantity && (!tier.maxQuantity || totalUsage <= tier.maxQuantity)
          );

          if (applicableTier) {
            subtotal = totalUsage * applicableTier.rate;
            lineItems.push({
              id: 'tiered_pricing',
              description: `${applicableTier.name} tier: ${totalUsage} units`,
              quantity: totalUsage,
              unitPrice: applicableTier.rate,
              totalPrice: subtotal,
              category: 'usage'
            });
          }
        }
        break;

      case 'hybrid':
        // Combination of fixed and usage-based
        if (contract.pricing.baseAmount) {
          subtotal += contract.pricing.baseAmount;
          lineItems.push({
            id: 'base_fee',
            description: 'Base subscription fee',
            quantity: 1,
            unitPrice: contract.pricing.baseAmount,
            totalPrice: contract.pricing.baseAmount,
            category: 'subscription'
          });
        }

        // Add usage-based charges
        if (usageData && contract.pricing.usageRates) {
          for (const rate of contract.pricing.usageRates) {
            const usage = usageData[rate.category] || 0;
            const amount = usage * rate.rate;
            subtotal += amount;

            lineItems.push({
              id: `usage_${rate.category}`,
              description: `${rate.category} usage: ${usage} ${rate.unit}`,
              quantity: usage,
              unitPrice: rate.rate,
              totalPrice: amount,
              category: rate.category
            });
          }
        }
        break;
    }

    // Apply discounts
    if (contract.pricing.discounts) {
      for (const discount of contract.pricing.discounts) {
        if (discount.type === 'volume' && subtotal >= discount.threshold) {
          const discountAmount = subtotal * (discount.discount / 100);
          subtotal -= discountAmount;

          lineItems.push({
            id: `discount_${discount.type}`,
            description: `Volume discount (${discount.discount}%)`,
            quantity: 1,
            unitPrice: -discountAmount,
            totalPrice: -discountAmount,
            category: 'discount'
          });
        }
      }
    }

    // Calculate tax
    const taxResult = await this.calculateTax(
      contract.tenantId,
      subtotal,
      contract.terms.currency,
      { country: 'US', state: 'CA' }, // This would come from client address
      lineItems.map(item => item.category)
    );

    // Create invoice
    const invoice = await invoiceManagementService.createInvoice({
      tenantId: contract.tenantId,
      templateId: 'enterprise',
      status: 'approved',
      type: 'subscription',
      customer: {
        id: contract.parties.client.companyName,
        name: contract.parties.client.companyName,
        email: contract.parties.client.email,
        address: contract.parties.client.address,
        taxId: contract.parties.client.taxId
      },
      details: {
        issueDate: new Date(),
        dueDate: new Date(Date.now() + (contract.billing.paymentDue * 24 * 60 * 60 * 1000)),
        currency: contract.terms.currency,
        subtotal,
        taxAmount: taxResult.taxAmount,
        discountAmount: 0,
        totalAmount: taxResult.totalAmount,
        paidAmount: 0,
        balanceAmount: taxResult.totalAmount
      },
      lineItems,
      payment: {},
      approval: {
        required: false,
        status: 'approved'
      },
      notes: `Contract billing for ${contract.contractNumber}`,
      internalNotes: `Contract ID: ${contractId}, Billing Period: ${billingPeriod.start.toLocaleDateString()} - ${billingPeriod.end.toLocaleDateString()}`
    });

    return invoice.id;
  }

  // Helper Methods
  async getTaxConfiguration(tenantId: string): Promise<TaxConfiguration | null> {
    const q = query(
      collection(db, this.taxConfigCollection),
      where('tenantId', '==', tenantId),
      where('isActive', '==', true),
      limit(1)
    );

    const querySnapshot = await getDocs(q);
    if (querySnapshot.empty) return null;

    const doc = querySnapshot.docs[0];
    const data = doc.data();

    return {
      id: doc.id,
      ...data,
      rules: data.rules.map((rule: any) => ({
        ...rule,
        effectiveDate: rule.effectiveDate.toDate(),
        expiryDate: rule.expiryDate?.toDate()
      })),
      metadata: {
        createdAt: data.metadata.createdAt.toDate(),
        updatedAt: data.metadata.updatedAt.toDate(),
        lastCalculation: data.metadata.lastCalculation?.toDate()
      }
    } as TaxConfiguration;
  }

  async getPurchaseOrders(tenantId: string, status?: PurchaseOrder['status']): Promise<PurchaseOrder[]> {
    let q = query(
      collection(db, this.purchaseOrdersCollection),
      where('tenantId', '==', tenantId),
      orderBy('metadata.createdAt', 'desc')
    );

    if (status) {
      q = query(q, where('status', '==', status));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        delivery: {
          ...data.delivery,
          expectedDate: data.delivery.expectedDate.toDate()
        },
        approvals: data.approvals.map((approval: any) => ({
          ...approval,
          approvedAt: approval.approvedAt?.toDate()
        })),
        attachments: data.attachments.map((attachment: any) => ({
          ...attachment,
          uploadedAt: attachment.uploadedAt.toDate()
        })),
        items: data.items.map((item: any) => ({
          ...item,
          deliveryDate: item.deliveryDate?.toDate()
        })),
        metadata: {
          createdAt: data.metadata.createdAt.toDate(),
          updatedAt: data.metadata.updatedAt.toDate(),
          createdBy: data.metadata.createdBy,
          lastModifiedBy: data.metadata.lastModifiedBy
        }
      } as PurchaseOrder;
    });
  }

  async getEnterpriseContracts(tenantId: string, status?: EnterpriseContract['status']): Promise<EnterpriseContract[]> {
    let q = query(
      collection(db, this.contractsCollection),
      where('tenantId', '==', tenantId),
      orderBy('metadata.createdAt', 'desc')
    );

    if (status) {
      q = query(q, where('status', '==', status));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        terms: {
          ...data.terms,
          startDate: data.terms.startDate.toDate(),
          endDate: data.terms.endDate.toDate()
        },
        metadata: {
          createdAt: data.metadata.createdAt.toDate(),
          updatedAt: data.metadata.updatedAt.toDate(),
          createdBy: data.metadata.createdBy,
          lastModifiedBy: data.metadata.lastModifiedBy,
          version: data.metadata.version
        }
      } as EnterpriseContract;
    });
  }
}

export const enterpriseBillingService = new EnterpriseBillingService();
