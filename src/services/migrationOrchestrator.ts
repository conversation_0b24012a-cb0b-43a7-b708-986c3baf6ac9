/**
 * Migration Orchestrator
 * Coordinates all data migration activities with proper sequencing and error handling
 */

import { 
  migrateAllCollections, 
  type MigrationResult, 
  type MigrationProgress 
} from './dataMigrationService';
import { 
  migrateAllNestedCollections, 
  type NestedMigrationResult,
  getMigrationSummary 
} from './nestedCollectionMigrationService';
import { validateTenantId } from './tenantIdHelperService';

// ===== ORCHESTRATION TYPES =====

export interface MigrationPlan {
  tenantId: string;
  phases: MigrationPhase[];
  dryRun: boolean;
  continueOnError: boolean;
  backupBeforeMigration: boolean;
}

export interface MigrationPhase {
  name: string;
  description: string;
  type: 'flat' | 'nested' | 'custom';
  dependencies?: string[];
  critical: boolean;
}

export interface OrchestrationResult {
  success: boolean;
  tenantId: string;
  totalDuration: number;
  startTime: Date;
  endTime: Date;
  phases: PhaseResult[];
  summary: MigrationSummary;
  errors: string[];
}

export interface PhaseResult {
  phase: string;
  success: boolean;
  duration: number;
  flatMigrations?: MigrationResult[];
  nestedMigrations?: NestedMigrationResult[];
  errors: string[];
}

export interface MigrationSummary {
  totalDocumentsProcessed: number;
  totalDocumentsSkipped: number;
  totalCollectionsMigrated: number;
  totalErrors: number;
  successRate: number;
}

// ===== MIGRATION PHASES =====

export const MIGRATION_PHASES: MigrationPhase[] = [
  {
    name: 'user_management',
    description: 'Migrate user-related collections',
    type: 'flat',
    critical: true
  },
  {
    name: 'core_entities',
    description: 'Migrate core business entities (exhibitions, events)',
    type: 'flat',
    dependencies: ['user_management'],
    critical: true
  },
  {
    name: 'nested_collections',
    description: 'Migrate nested subcollections to flat structure',
    type: 'nested',
    dependencies: ['core_entities'],
    critical: true
  },
  {
    name: 'financial_data',
    description: 'Migrate financial and vendor data',
    type: 'flat',
    dependencies: ['core_entities'],
    critical: false
  },
  {
    name: 'communication_data',
    description: 'Migrate communication and marketing data',
    type: 'flat',
    dependencies: ['core_entities'],
    critical: false
  }
];

// ===== ORCHESTRATION FUNCTIONS =====

/**
 * Execute complete migration plan
 */
export async function executeMigrationPlan(
  plan: MigrationPlan,
  onProgress?: (progress: {
    phase: string;
    phaseIndex: number;
    totalPhases: number;
    details: string;
  }) => void
): Promise<OrchestrationResult> {
  const startTime = new Date();
  validateTenantId(plan.tenantId);

  const result: OrchestrationResult = {
    success: false,
    tenantId: plan.tenantId,
    totalDuration: 0,
    startTime,
    endTime: new Date(),
    phases: [],
    summary: {
      totalDocumentsProcessed: 0,
      totalDocumentsSkipped: 0,
      totalCollectionsMigrated: 0,
      totalErrors: 0,
      successRate: 0
    },
    errors: []
  };

  try {
    console.log(`${plan.dryRun ? '[DRY RUN] ' : ''}Starting migration orchestration for tenant: ${plan.tenantId}`);

    // Execute phases in order
    for (let i = 0; i < plan.phases.length; i++) {
      const phase = plan.phases[i];
      
      if (onProgress) {
        onProgress({
          phase: phase.name,
          phaseIndex: i,
          totalPhases: plan.phases.length,
          details: `Starting ${phase.description}`
        });
      }

      const phaseResult = await executePhase(phase, plan.tenantId, plan.dryRun, (details) => {
        if (onProgress) {
          onProgress({
            phase: phase.name,
            phaseIndex: i,
            totalPhases: plan.phases.length,
            details
          });
        }
      });

      result.phases.push(phaseResult);

      // Check if phase failed and if we should continue
      if (!phaseResult.success) {
        result.errors.push(`Phase ${phase.name} failed: ${phaseResult.errors.join(', ')}`);
        
        if (phase.critical && !plan.continueOnError) {
          console.error(`Critical phase ${phase.name} failed. Stopping migration.`);
          break;
        }
      }
    }

    // Calculate summary
    result.summary = calculateMigrationSummary(result.phases);
    result.success = result.phases.some(p => p.success) && result.summary.totalErrors === 0;

  } catch (error) {
    result.errors.push(`Migration orchestration failed: ${error}`);
    console.error('Migration orchestration error:', error);
  }

  result.endTime = new Date();
  result.totalDuration = result.endTime.getTime() - result.startTime.getTime();

  return result;
}

/**
 * Execute a single migration phase
 */
async function executePhase(
  phase: MigrationPhase,
  tenantId: string,
  dryRun: boolean,
  onProgress?: (details: string) => void
): Promise<PhaseResult> {
  const phaseStartTime = new Date();
  
  const result: PhaseResult = {
    phase: phase.name,
    success: false,
    duration: 0,
    errors: []
  };

  try {
    console.log(`Executing phase: ${phase.name} - ${phase.description}`);

    switch (phase.type) {
      case 'flat':
        result.flatMigrations = await migrateAllCollections(
          tenantId,
          dryRun,
          (progress) => {
            if (onProgress) {
              onProgress(`${progress.currentCollection}: ${progress.documentsProcessed} documents processed`);
            }
          }
        );
        result.success = result.flatMigrations.every(m => m.success);
        result.errors = result.flatMigrations.flatMap(m => m.errors);
        break;

      case 'nested':
        result.nestedMigrations = await migrateAllNestedCollections(
          tenantId,
          dryRun,
          (progress) => {
            if (onProgress) {
              onProgress(`${progress.current}: ${progress.processed} documents processed`);
            }
          }
        );
        result.success = result.nestedMigrations.every(m => m.success);
        result.errors = result.nestedMigrations.flatMap(m => m.errors);
        break;

      case 'custom':
        // Custom migration logic would go here
        result.success = true;
        break;

      default:
        throw new Error(`Unknown phase type: ${phase.type}`);
    }

  } catch (error) {
    result.errors.push(`Phase execution failed: ${error}`);
    console.error(`Phase ${phase.name} error:`, error);
  }

  const phaseEndTime = new Date();
  result.duration = phaseEndTime.getTime() - phaseStartTime.getTime();

  return result;
}

/**
 * Calculate migration summary from phase results
 */
function calculateMigrationSummary(phases: PhaseResult[]): MigrationSummary {
  let totalDocumentsProcessed = 0;
  let totalDocumentsSkipped = 0;
  let totalCollectionsMigrated = 0;
  let totalErrors = 0;

  for (const phase of phases) {
    // Count flat migrations
    if (phase.flatMigrations) {
      totalDocumentsProcessed += phase.flatMigrations.reduce((sum, m) => sum + m.documentsProcessed, 0);
      totalDocumentsSkipped += phase.flatMigrations.reduce((sum, m) => sum + m.documentsSkipped, 0);
      totalCollectionsMigrated += phase.flatMigrations.length;
      totalErrors += phase.flatMigrations.reduce((sum, m) => sum + m.errors.length, 0);
    }

    // Count nested migrations
    if (phase.nestedMigrations) {
      totalDocumentsProcessed += phase.nestedMigrations.reduce((sum, m) => sum + m.documentsProcessed, 0);
      totalDocumentsSkipped += phase.nestedMigrations.reduce((sum, m) => sum + m.documentsSkipped, 0);
      totalCollectionsMigrated += phase.nestedMigrations.length;
      totalErrors += phase.nestedMigrations.reduce((sum, m) => sum + m.errors.length, 0);
    }

    // Count phase-level errors
    totalErrors += phase.errors.length;
  }

  const successRate = totalDocumentsProcessed > 0 
    ? (totalDocumentsProcessed / (totalDocumentsProcessed + totalDocumentsSkipped)) * 100 
    : 0;

  return {
    totalDocumentsProcessed,
    totalDocumentsSkipped,
    totalCollectionsMigrated,
    totalErrors,
    successRate
  };
}

/**
 * Create a standard migration plan for a tenant
 */
export function createStandardMigrationPlan(
  tenantId: string,
  options: {
    dryRun?: boolean;
    continueOnError?: boolean;
    backupBeforeMigration?: boolean;
  } = {}
): MigrationPlan {
  return {
    tenantId,
    phases: MIGRATION_PHASES,
    dryRun: options.dryRun ?? false,
    continueOnError: options.continueOnError ?? false,
    backupBeforeMigration: options.backupBeforeMigration ?? true
  };
}

/**
 * Validate migration plan
 */
export function validateMigrationPlan(plan: MigrationPlan): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  try {
    validateTenantId(plan.tenantId);
  } catch (error) {
    errors.push(`Invalid tenant ID: ${error}`);
  }

  if (!plan.phases || plan.phases.length === 0) {
    errors.push('Migration plan must include at least one phase');
  }

  // Check phase dependencies
  const phaseNames = new Set(plan.phases.map(p => p.name));
  for (const phase of plan.phases) {
    if (phase.dependencies) {
      for (const dependency of phase.dependencies) {
        if (!phaseNames.has(dependency)) {
          errors.push(`Phase ${phase.name} depends on missing phase: ${dependency}`);
        }
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Generate migration report
 */
export function generateMigrationReport(result: OrchestrationResult): string {
  const lines: string[] = [];
  
  lines.push('='.repeat(60));
  lines.push('EVEXA DATA MIGRATION REPORT');
  lines.push('='.repeat(60));
  lines.push('');
  
  lines.push(`Tenant ID: ${result.tenantId}`);
  lines.push(`Migration Status: ${result.success ? 'SUCCESS' : 'FAILED'}`);
  lines.push(`Start Time: ${result.startTime.toISOString()}`);
  lines.push(`End Time: ${result.endTime.toISOString()}`);
  lines.push(`Total Duration: ${(result.totalDuration / 1000).toFixed(2)} seconds`);
  lines.push('');
  
  lines.push('SUMMARY:');
  lines.push(`- Documents Processed: ${result.summary.totalDocumentsProcessed}`);
  lines.push(`- Documents Skipped: ${result.summary.totalDocumentsSkipped}`);
  lines.push(`- Collections Migrated: ${result.summary.totalCollectionsMigrated}`);
  lines.push(`- Total Errors: ${result.summary.totalErrors}`);
  lines.push(`- Success Rate: ${result.summary.successRate.toFixed(2)}%`);
  lines.push('');
  
  lines.push('PHASE RESULTS:');
  for (const phase of result.phases) {
    lines.push(`- ${phase.phase}: ${phase.success ? 'SUCCESS' : 'FAILED'} (${(phase.duration / 1000).toFixed(2)}s)`);
    if (phase.errors.length > 0) {
      lines.push(`  Errors: ${phase.errors.length}`);
    }
  }
  
  if (result.errors.length > 0) {
    lines.push('');
    lines.push('ERRORS:');
    result.errors.forEach(error => lines.push(`- ${error}`));
  }
  
  lines.push('');
  lines.push('='.repeat(60));
  
  return lines.join('\n');
}
