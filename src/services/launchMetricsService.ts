import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  Timestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { marketingCampaignService } from './marketingCampaignService';
import { partnershipService } from './partnershipService';
import { clientAcquisitionService } from './clientAcquisitionService';

export interface LaunchMetrics {
  readinessScore: number;
  partnershipsActive: number;
  clientsOnboarded: number;
  revenueTarget: number;
  currentRevenue: number;
  marketingCampaigns: number;
  leadConversion: number;
  systemUptime: number;
  supportTickets: number;
}

export interface LaunchTask {
  id?: string;
  title: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'blocked';
  priority: 'low' | 'medium' | 'high' | 'critical';
  assignee: string;
  dueDate: Date;
  category: 'technical' | 'marketing' | 'partnerships' | 'legal' | 'operations';
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

class LaunchMetricsService {
  private launchTasksCollection = 'launch_tasks';
  private launchMetricsCollection = 'launch_metrics';

  // Launch Tasks Management
  async createLaunchTask(task: Omit<LaunchTask, 'id' | 'createdAt' | 'updatedAt'>): Promise<LaunchTask> {
    const now = new Date();
    const taskData: Omit<LaunchTask, 'id'> = {
      ...task,
      createdAt: now,
      updatedAt: now
    };

    const docRef = await addDoc(collection(db, this.launchTasksCollection), {
      ...taskData,
      dueDate: Timestamp.fromDate(taskData.dueDate),
      createdAt: Timestamp.fromDate(now),
      updatedAt: Timestamp.fromDate(now),
      completedAt: taskData.completedAt ? Timestamp.fromDate(taskData.completedAt) : null
    });

    return { ...taskData, id: docRef.id };
  }

  async getAllLaunchTasks(): Promise<LaunchTask[]> {
    const q = query(
      collection(db, this.launchTasksCollection),
      orderBy('dueDate', 'asc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        dueDate: data.dueDate.toDate(),
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
        completedAt: data.completedAt?.toDate()
      } as LaunchTask;
    });
  }

  async updateLaunchTask(id: string, updates: Partial<LaunchTask>): Promise<void> {
    const docRef = doc(db, this.launchTasksCollection, id);
    const updateData = {
      ...updates,
      updatedAt: Timestamp.fromDate(new Date())
    };

    // Handle date conversions
    if (updates.dueDate) {
      updateData.dueDate = Timestamp.fromDate(updates.dueDate);
    }
    if (updates.completedAt) {
      updateData.completedAt = Timestamp.fromDate(updates.completedAt);
    }

    await updateDoc(docRef, updateData);
  }

  async completeLaunchTask(id: string): Promise<void> {
    await this.updateLaunchTask(id, {
      status: 'completed',
      completedAt: new Date()
    });
  }

  // Launch Metrics Calculation
  async calculateLaunchMetrics(): Promise<LaunchMetrics> {
    try {
      // Get data from various services
      const [
        partnerships,
        leads,
        campaigns,
        launchTasks
      ] = await Promise.all([
        partnershipService.getAllPartnerships(),
        clientAcquisitionService.getAllLeads(),
        marketingCampaignService.getAllCampaigns(),
        this.getAllLaunchTasks()
      ]);

      // Calculate partnerships
      const partnershipsActive = partnerships.filter(p => p.status === 'active').length;

      // Calculate clients onboarded (converted leads)
      const clientsOnboarded = leads.filter(l => l.status === 'closed_won').length;

      // Calculate revenue
      const currentRevenue = leads
        .filter(l => l.status === 'closed_won')
        .reduce((sum, l) => sum + l.estimatedValue, 0);

      // Set revenue target (this could be configurable)
      const revenueTarget = 500000;

      // Calculate marketing campaigns
      const marketingCampaigns = campaigns.length;

      // Calculate lead conversion rate
      const totalLeads = leads.length;
      const convertedLeads = leads.filter(l => l.status === 'closed_won').length;
      const leadConversion = totalLeads > 0 ? (convertedLeads / totalLeads) * 100 : 0;

      // Calculate system uptime (placeholder - would need real monitoring)
      const systemUptime = 99.9;

      // Calculate support tickets (placeholder - would need real support system)
      const supportTickets = 3;

      // Calculate readiness score based on various factors
      const readinessScore = this.calculateReadinessScore({
        partnershipsActive,
        clientsOnboarded,
        currentRevenue,
        revenueTarget,
        marketingCampaigns,
        leadConversion,
        systemUptime,
        launchTasks
      });

      return {
        readinessScore,
        partnershipsActive,
        clientsOnboarded,
        revenueTarget,
        currentRevenue,
        marketingCampaigns,
        leadConversion,
        systemUptime,
        supportTickets
      };
    } catch (error) {
      console.error('Failed to calculate launch metrics:', error);
      // Return default metrics if calculation fails
      return {
        readinessScore: 0,
        partnershipsActive: 0,
        clientsOnboarded: 0,
        revenueTarget: 500000,
        currentRevenue: 0,
        marketingCampaigns: 0,
        leadConversion: 0,
        systemUptime: 0,
        supportTickets: 0
      };
    }
  }

  private calculateReadinessScore(data: {
    partnershipsActive: number;
    clientsOnboarded: number;
    currentRevenue: number;
    revenueTarget: number;
    marketingCampaigns: number;
    leadConversion: number;
    systemUptime: number;
    launchTasks: LaunchTask[];
  }): number {
    let score = 0;

    // Partnerships (20 points max)
    score += Math.min(data.partnershipsActive * 2, 20);

    // Clients onboarded (20 points max)
    score += Math.min(data.clientsOnboarded * 0.5, 20);

    // Revenue progress (20 points max)
    const revenueProgress = (data.currentRevenue / data.revenueTarget) * 100;
    score += Math.min(revenueProgress * 0.2, 20);

    // Marketing campaigns (15 points max)
    score += Math.min(data.marketingCampaigns * 2, 15);

    // Lead conversion (15 points max)
    score += Math.min(data.leadConversion * 0.75, 15);

    // System uptime (10 points max)
    score += Math.min(data.systemUptime * 0.1, 10);

    // Task completion (bonus points)
    const completedTasks = data.launchTasks.filter(t => t.status === 'completed').length;
    const totalTasks = data.launchTasks.length;
    if (totalTasks > 0) {
      const taskCompletionRate = (completedTasks / totalTasks) * 100;
      score += Math.min(taskCompletionRate * 0.1, 10);
    }

    return Math.min(Math.round(score), 100);
  }

  // Launch readiness assessment
  async getLaunchReadinessAssessment(): Promise<{
    isReady: boolean;
    blockers: string[];
    recommendations: string[];
    score: number;
  }> {
    const metrics = await this.calculateLaunchMetrics();
    const tasks = await this.getAllLaunchTasks();

    const blockers: string[] = [];
    const recommendations: string[] = [];

    // Check for critical blockers
    const criticalTasks = tasks.filter(t => t.priority === 'critical' && t.status !== 'completed');
    if (criticalTasks.length > 0) {
      blockers.push(`${criticalTasks.length} critical tasks pending completion`);
    }

    if (metrics.systemUptime < 99.0) {
      blockers.push('System uptime below acceptable threshold');
    }

    // Generate recommendations
    if (metrics.partnershipsActive < 5) {
      recommendations.push('Establish more strategic partnerships before launch');
    }

    if (metrics.leadConversion < 10) {
      recommendations.push('Improve lead conversion rate through better qualification');
    }

    if (metrics.marketingCampaigns < 3) {
      recommendations.push('Launch additional marketing campaigns to build awareness');
    }

    const isReady = metrics.readinessScore >= 85 && blockers.length === 0;

    return {
      isReady,
      blockers,
      recommendations,
      score: metrics.readinessScore
    };
  }
}

export const launchMetricsService = new LaunchMetricsService();
