/**
 * Enhanced Invoice Management Service for EVEXA
 * Handles custom invoice templates, automated generation, approval workflows, and bulk processing
 */

import { db } from '@/lib/firebase';
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { stripePaymentService } from './stripePaymentService';
import { subscriptionService } from './subscriptionService';
import { defaultInvoiceTemplates } from '@/lib/defaultInvoiceTemplates';

export interface InvoiceTemplate {
  id: string;
  tenantId: string;
  name: string;
  description: string;
  isDefault: boolean;
  branding: {
    logo?: string;
    companyName: string;
    companyAddress: string;
    companyEmail: string;
    companyPhone?: string;
    website?: string;
    primaryColor: string;
    secondaryColor: string;
  };
  layout: {
    headerStyle: 'minimal' | 'standard' | 'detailed';
    footerStyle: 'minimal' | 'standard' | 'detailed';
    itemsTableStyle: 'simple' | 'detailed' | 'modern';
    showTaxBreakdown: boolean;
    showPaymentTerms: boolean;
    showNotes: boolean;
  };
  customFields: Array<{
    id: string;
    name: string;
    type: 'text' | 'number' | 'date' | 'boolean';
    required: boolean;
    defaultValue?: any;
  }>;
  paymentTerms: {
    dueDays: number;
    lateFeesEnabled: boolean;
    lateFeePercentage?: number;
    lateFeeFlat?: number;
    discountTerms?: {
      days: number;
      percentage: number;
    };
  };
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    lastModifiedBy: string;
  };
}

export interface CustomInvoice {
  id: string;
  tenantId: string;
  templateId: string;
  invoiceNumber: string;
  status: 'draft' | 'pending_approval' | 'approved' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  type: 'subscription' | 'one_time' | 'usage_based' | 'custom';
  
  // Customer Information
  customer: {
    id: string;
    name: string;
    email: string;
    address?: string;
    taxId?: string;
    customFields?: Record<string, any>;
  };
  
  // Invoice Details
  details: {
    issueDate: Date;
    dueDate: Date;
    currency: string;
    subtotal: number;
    taxAmount: number;
    discountAmount: number;
    totalAmount: number;
    paidAmount: number;
    balanceAmount: number;
  };
  
  // Line Items
  lineItems: Array<{
    id: string;
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    taxRate?: number;
    category?: string;
    metadata?: Record<string, any>;
  }>;
  
  // Payment Information
  payment: {
    stripeInvoiceId?: string;
    paymentIntentId?: string;
    paymentMethod?: string;
    paidAt?: Date;
    failureReason?: string;
  };
  
  // Approval Workflow
  approval: {
    required: boolean;
    status: 'pending' | 'approved' | 'rejected';
    approvedBy?: string;
    approvedAt?: Date;
    rejectedBy?: string;
    rejectedAt?: Date;
    rejectionReason?: string;
    approvalNotes?: string;
  };
  
  // Additional Information
  notes?: string;
  internalNotes?: string;
  attachments?: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
    size: number;
  }>;
  
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    lastModifiedBy: string;
  };
}

export interface BulkInvoiceOperation {
  id: string;
  tenantId: string;
  operation: 'create' | 'send' | 'approve' | 'cancel';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'partial';
  totalCount: number;
  processedCount: number;
  successCount: number;
  failureCount: number;
  invoiceIds: string[];
  errors: Array<{
    invoiceId: string;
    error: string;
  }>;
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
  };
}

class InvoiceManagementService {
  private templatesCollection = 'invoice_templates';
  private invoicesCollection = 'custom_invoices';
  private bulkOperationsCollection = 'bulk_invoice_operations';

  // Template Management
  async createTemplate(template: Omit<InvoiceTemplate, 'id' | 'metadata'>): Promise<InvoiceTemplate> {
    const now = new Date();
    const templateData: Omit<InvoiceTemplate, 'id'> = {
      ...template,
      metadata: {
        createdAt: now,
        updatedAt: now,
        createdBy: 'current-user', // Replace with actual user ID
        lastModifiedBy: 'current-user'
      }
    };

    const docRef = await addDoc(collection(db, this.templatesCollection), {
      ...templateData,
      'metadata.createdAt': Timestamp.fromDate(now),
      'metadata.updatedAt': Timestamp.fromDate(now)
    });

    return { ...templateData, id: docRef.id };
  }

  async getTemplates(tenantId: string): Promise<InvoiceTemplate[]> {
    const q = query(
      collection(db, this.templatesCollection),
      where('tenantId', '==', tenantId),
      orderBy('metadata.createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);

    // If no templates exist, initialize default templates
    if (querySnapshot.empty) {
      await this.initializeDefaultTemplates(tenantId);
      return this.getTemplates(tenantId); // Recursive call to get the newly created templates
    }

    return querySnapshot.docs.map(docSnapshot => {
      const data = docSnapshot.data();
      return {
        ...data,
        id: docSnapshot.id,
        metadata: {
          createdAt: data.metadata.createdAt.toDate(),
          updatedAt: data.metadata.updatedAt.toDate(),
          createdBy: data.metadata.createdBy,
          lastModifiedBy: data.metadata.lastModifiedBy
        }
      } as InvoiceTemplate;
    });
  }

  async initializeDefaultTemplates(tenantId: string): Promise<void> {
    const batch = writeBatch(db);
    const now = new Date();

    for (const template of defaultInvoiceTemplates) {
      const templateData = {
        ...template,
        tenantId,
        metadata: {
          createdAt: Timestamp.fromDate(now),
          updatedAt: Timestamp.fromDate(now),
          createdBy: 'system',
          lastModifiedBy: 'system'
        }
      };

      const docRef = doc(collection(db, this.templatesCollection));
      batch.set(docRef, templateData);
    }

    await batch.commit();
  }

  async updateTemplate(templateId: string, updates: Partial<InvoiceTemplate>): Promise<void> {
    const now = new Date();
    const updateData = {
      ...updates,
      'metadata.updatedAt': Timestamp.fromDate(now),
      'metadata.lastModifiedBy': 'current-user'
    };

    await updateDoc(doc(db, this.templatesCollection, templateId), updateData);
  }

  async deleteTemplate(templateId: string): Promise<void> {
    await deleteDoc(doc(db, this.templatesCollection, templateId));
  }

  async setDefaultTemplate(tenantId: string, templateId: string): Promise<void> {
    const batch = writeBatch(db);

    // Remove default from all templates
    const q = query(
      collection(db, this.templatesCollection),
      where('tenantId', '==', tenantId),
      where('isDefault', '==', true)
    );
    
    const querySnapshot = await getDocs(q);
    querySnapshot.docs.forEach(docSnapshot => {
      batch.update(docSnapshot.ref, { isDefault: false });
    });

    // Set new default
    batch.update(doc(db, this.templatesCollection, templateId), { isDefault: true });

    await batch.commit();
  }

  // Invoice Generation
  async generateInvoiceNumber(tenantId: string): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');

    // Get count of invoices for this month
    const q = query(
      collection(db, this.invoicesCollection),
      where('tenantId', '==', tenantId),
      where('invoiceNumber', '>=', `INV-${year}${month}-`),
      where('invoiceNumber', '<', `INV-${year}${month}-ZZZZ`)
    );

    const querySnapshot = await getDocs(q);
    const count = querySnapshot.size + 1;

    return `INV-${year}${month}-${String(count).padStart(4, '0')}`;
  }

  async createInvoice(invoice: Omit<CustomInvoice, 'id' | 'invoiceNumber' | 'metadata'>): Promise<CustomInvoice> {
    const now = new Date();
    const invoiceNumber = await this.generateInvoiceNumber(invoice.tenantId);

    const invoiceData: Omit<CustomInvoice, 'id'> = {
      ...invoice,
      invoiceNumber,
      metadata: {
        createdAt: now,
        updatedAt: now,
        createdBy: 'current-user',
        lastModifiedBy: 'current-user'
      }
    };

    const docRef = await addDoc(collection(db, this.invoicesCollection), {
      ...invoiceData,
      'details.issueDate': Timestamp.fromDate(invoice.details.issueDate),
      'details.dueDate': Timestamp.fromDate(invoice.details.dueDate),
      'payment.paidAt': invoice.payment.paidAt ? Timestamp.fromDate(invoice.payment.paidAt) : null,
      'approval.approvedAt': invoice.approval.approvedAt ? Timestamp.fromDate(invoice.approval.approvedAt) : null,
      'approval.rejectedAt': invoice.approval.rejectedAt ? Timestamp.fromDate(invoice.approval.rejectedAt) : null,
      'metadata.createdAt': Timestamp.fromDate(now),
      'metadata.updatedAt': Timestamp.fromDate(now)
    });

    return { ...invoiceData, id: docRef.id };
  }

  async getInvoices(tenantId: string, filters?: {
    status?: CustomInvoice['status'];
    type?: CustomInvoice['type'];
    customerId?: string;
    dateRange?: { start: Date; end: Date };
  }): Promise<CustomInvoice[]> {
    let q = query(
      collection(db, this.invoicesCollection),
      where('tenantId', '==', tenantId),
      orderBy('metadata.createdAt', 'desc')
    );

    if (filters?.status) {
      q = query(q, where('status', '==', filters.status));
    }

    if (filters?.type) {
      q = query(q, where('type', '==', filters.type));
    }

    if (filters?.customerId) {
      q = query(q, where('customer.id', '==', filters.customerId));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(docSnapshot => {
      const data = docSnapshot.data();
      return {
        ...data,
        id: docSnapshot.id,
        details: {
          ...data.details,
          issueDate: data.details.issueDate.toDate(),
          dueDate: data.details.dueDate.toDate()
        },
        payment: {
          ...data.payment,
          paidAt: data.payment.paidAt?.toDate()
        },
        approval: {
          ...data.approval,
          approvedAt: data.approval.approvedAt?.toDate(),
          rejectedAt: data.approval.rejectedAt?.toDate()
        },
        metadata: {
          createdAt: data.metadata.createdAt.toDate(),
          updatedAt: data.metadata.updatedAt.toDate(),
          createdBy: data.metadata.createdBy,
          lastModifiedBy: data.metadata.lastModifiedBy
        }
      } as CustomInvoice;
    });
  }

  async updateInvoice(invoiceId: string, updates: Partial<CustomInvoice>): Promise<void> {
    const now = new Date();
    const updateData: any = {
      ...updates,
      'metadata.updatedAt': Timestamp.fromDate(now),
      'metadata.lastModifiedBy': 'current-user'
    };

    // Handle date conversions
    if (updates.details?.issueDate) {
      updateData['details.issueDate'] = Timestamp.fromDate(updates.details.issueDate);
    }
    if (updates.details?.dueDate) {
      updateData['details.dueDate'] = Timestamp.fromDate(updates.details.dueDate);
    }
    if (updates.payment?.paidAt) {
      updateData['payment.paidAt'] = Timestamp.fromDate(updates.payment.paidAt);
    }
    if (updates.approval?.approvedAt) {
      updateData['approval.approvedAt'] = Timestamp.fromDate(updates.approval.approvedAt);
    }
    if (updates.approval?.rejectedAt) {
      updateData['approval.rejectedAt'] = Timestamp.fromDate(updates.approval.rejectedAt);
    }

    await updateDoc(doc(db, this.invoicesCollection, invoiceId), updateData);
  }

  async deleteInvoice(invoiceId: string): Promise<void> {
    await deleteDoc(doc(db, this.invoicesCollection, invoiceId));
  }

  // Approval Workflow
  async submitForApproval(invoiceId: string): Promise<void> {
    await this.updateInvoice(invoiceId, {
      status: 'pending_approval',
      approval: {
        required: true,
        status: 'pending'
      }
    });
  }

  async approveInvoice(invoiceId: string, approvedBy: string, notes?: string): Promise<void> {
    const now = new Date();
    await this.updateInvoice(invoiceId, {
      status: 'approved',
      approval: {
        required: true,
        status: 'approved',
        approvedBy,
        approvedAt: now,
        approvalNotes: notes
      }
    });
  }

  async rejectInvoice(invoiceId: string, rejectedBy: string, reason: string): Promise<void> {
    const now = new Date();
    await this.updateInvoice(invoiceId, {
      status: 'draft',
      approval: {
        required: true,
        status: 'rejected',
        rejectedBy,
        rejectedAt: now,
        rejectionReason: reason
      }
    });
  }

  // Stripe Integration
  async sendInvoiceViaStripe(invoiceId: string): Promise<void> {
    const invoiceDoc = await getDoc(doc(db, this.invoicesCollection, invoiceId));
    if (!invoiceDoc.exists()) {
      throw new Error('Invoice not found');
    }

    const invoice = invoiceDoc.data() as CustomInvoice;

    // Create Stripe invoice
    const stripeInvoice = await stripePaymentService.createInvoice(
      invoice.customer.id,
      invoice.lineItems.map(item => ({
        description: item.description,
        amount: item.totalPrice,
        quantity: item.quantity
      })),
      {
        dueDate: invoice.details.dueDate,
        currency: invoice.details.currency,
        metadata: {
          customInvoiceId: invoiceId,
          tenantId: invoice.tenantId
        }
      }
    );

    // Update invoice with Stripe ID
    await this.updateInvoice(invoiceId, {
      status: 'sent',
      payment: {
        ...invoice.payment,
        stripeInvoiceId: stripeInvoice.id
      }
    });
  }

  async processPayment(invoiceId: string, paymentIntentId: string): Promise<void> {
    const now = new Date();
    const invoiceDoc = await getDoc(doc(db, this.invoicesCollection, invoiceId));

    if (!invoiceDoc.exists()) {
      throw new Error('Invoice not found');
    }

    const invoice = invoiceDoc.data() as CustomInvoice;

    await this.updateInvoice(invoiceId, {
      status: 'paid',
      details: {
        ...invoice.details,
        paidAmount: invoice.details.totalAmount,
        balanceAmount: 0
      },
      payment: {
        ...invoice.payment,
        paymentIntentId,
        paidAt: now
      }
    });
  }

  // Bulk Operations
  async createBulkOperation(
    tenantId: string,
    operation: BulkInvoiceOperation['operation'],
    invoiceIds: string[]
  ): Promise<BulkInvoiceOperation> {
    const now = new Date();
    const bulkOp: Omit<BulkInvoiceOperation, 'id'> = {
      tenantId,
      operation,
      status: 'pending',
      totalCount: invoiceIds.length,
      processedCount: 0,
      successCount: 0,
      failureCount: 0,
      invoiceIds,
      errors: [],
      metadata: {
        createdAt: now,
        updatedAt: now,
        createdBy: 'current-user'
      }
    };

    const docRef = await addDoc(collection(db, this.bulkOperationsCollection), {
      ...bulkOp,
      'metadata.createdAt': Timestamp.fromDate(now),
      'metadata.updatedAt': Timestamp.fromDate(now)
    });

    return { ...bulkOp, id: docRef.id };
  }

  async processBulkOperation(operationId: string): Promise<void> {
    const operationDoc = await getDoc(doc(db, this.bulkOperationsCollection, operationId));
    if (!operationDoc.exists()) {
      throw new Error('Bulk operation not found');
    }

    const operation = operationDoc.data() as BulkInvoiceOperation;

    // Update status to processing
    await updateDoc(doc(db, this.bulkOperationsCollection, operationId), {
      status: 'processing',
      'metadata.updatedAt': Timestamp.fromDate(new Date())
    });

    let successCount = 0;
    let failureCount = 0;
    const errors: Array<{ invoiceId: string; error: string }> = [];

    for (const invoiceId of operation.invoiceIds) {
      try {
        switch (operation.operation) {
          case 'send':
            await this.sendInvoiceViaStripe(invoiceId);
            break;
          case 'approve':
            await this.approveInvoice(invoiceId, 'bulk-operation');
            break;
          case 'cancel':
            await this.updateInvoice(invoiceId, { status: 'cancelled' });
            break;
        }
        successCount++;
      } catch (error) {
        failureCount++;
        errors.push({
          invoiceId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Update operation status
    const finalStatus = failureCount === 0 ? 'completed' :
                       successCount === 0 ? 'failed' : 'partial';

    await updateDoc(doc(db, this.bulkOperationsCollection, operationId), {
      status: finalStatus,
      processedCount: operation.invoiceIds.length,
      successCount,
      failureCount,
      errors,
      'metadata.updatedAt': Timestamp.fromDate(new Date())
    });
  }

  async getBulkOperations(tenantId: string): Promise<BulkInvoiceOperation[]> {
    const q = query(
      collection(db, this.bulkOperationsCollection),
      where('tenantId', '==', tenantId),
      orderBy('metadata.createdAt', 'desc'),
      limit(50)
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(docSnapshot => {
      const data = docSnapshot.data();
      return {
        ...data,
        id: docSnapshot.id,
        metadata: {
          createdAt: data.metadata.createdAt.toDate(),
          updatedAt: data.metadata.updatedAt.toDate(),
          createdBy: data.metadata.createdBy
        }
      } as BulkInvoiceOperation;
    });
  }
}

export const invoiceManagementService = new InvoiceManagementService();
