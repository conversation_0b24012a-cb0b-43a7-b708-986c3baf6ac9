/**
 * Refactored Financial Service
 * Consolidates budgets, expenses, and purchase_orders into single 'financials' collection
 * Optimized for flat collection architecture with tenantId filtering
 */

import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  Timestamp,
  writeBatch,
  runTransaction,
  type DocumentSnapshot,
  type QueryConstraint
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { 
  validateTenantId, 
  addTenantDocument, 
  getTenantDocument, 
  updateTenantDocument,
  deleteTenantDocument,
  queryTenantCollection
} from './tenantIdHelperService';
import { syncDocumentDuplication } from './dataDuplicationService';
import type { Budget, Expense, PurchaseRequest, PurchaseOrder, TenantAwareEntity } from '@/types/firestore';

// ===== TYPES =====

export type FinancialDocumentType = 'budget' | 'expense' | 'purchase_request' | 'purchase_order';

export interface FinancialDocument extends TenantAwareEntity {
  id?: string;
  type: FinancialDocumentType;
  activityId: string; // Exhibition or Event ID
  activityType: 'Exhibition' | 'Event';
  activityName: string; // Duplicated for performance
  
  // Common fields
  amount: number;
  currency: string;
  status: string;
  description?: string;
  
  // Budget-specific fields (when type === 'budget')
  totalBudget?: number;
  categories?: any[];
  totalSpent?: number;
  budgetHolder?: string;
  fiscalYear?: string;
  
  // Expense-specific fields (when type === 'expense')
  expenseName?: string;
  budgetCategory?: string;
  taxAmount?: number;
  vendorId?: string;
  vendorName?: string;
  transactionDate?: Timestamp | Date | string;
  receiptUrl?: string;
  
  // Purchase Request/Order specific fields
  requestedBy?: string;
  vendorName?: string;
  items?: any[];
  subtotal?: number;
  totalAmount?: number;
  orderDate?: Timestamp | Date | string;
  expectedDeliveryDate?: Timestamp | Date | string;
  
  // Audit fields
  createdByUserId?: string;
  createdByName?: string;
  updatedByUserId?: string;
  updatedByName?: string;
  createdAt?: Timestamp | Date | string;
  updatedAt?: Timestamp | Date | string;
}

export interface FinancialQueryOptions {
  tenantId: string;
  type?: FinancialDocumentType;
  activityId?: string;
  status?: string;
  limit?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  startAfter?: DocumentSnapshot;
}

export interface FinancialSummary {
  totalBudgets: number;
  totalExpenses: number;
  totalPurchaseRequests: number;
  totalPurchaseOrders: number;
  budgetUtilization: number;
  pendingApprovals: number;
  overBudgetActivities: string[];
}

// ===== CORE CRUD OPERATIONS =====

class RefactoredFinancialService {
  private readonly collectionName = 'financials';

  /**
   * Create a new financial document
   */
  async create(tenantId: string, data: Omit<FinancialDocument, 'id' | 'tenantId' | 'createdAt' | 'updatedAt'>): Promise<string> {
    validateTenantId(tenantId);
    
    const financialDoc: Omit<FinancialDocument, 'id'> = {
      ...data,
      tenantId,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };

    const docRef = await addTenantDocument(this.collectionName, financialDoc, tenantId);
    
    // Sync data duplication for activity names
    await syncDocumentDuplication(this.collectionName, docRef.id, {
      activityName: data.activityName
    });

    return docRef.id;
  }

  /**
   * Get financial document by ID
   */
  async getById(tenantId: string, id: string): Promise<FinancialDocument | null> {
    validateTenantId(tenantId);
    return getTenantDocument<FinancialDocument>(this.collectionName, id, tenantId);
  }

  /**
   * Update financial document
   */
  async update(tenantId: string, id: string, updates: Partial<FinancialDocument>): Promise<void> {
    validateTenantId(tenantId);
    
    const updateData = {
      ...updates,
      updatedAt: Timestamp.now()
    };

    await updateTenantDocument(this.collectionName, id, updateData, tenantId);
    
    // Sync data duplication if activity name changed
    if (updates.activityName) {
      await syncDocumentDuplication(this.collectionName, id, {
        activityName: updates.activityName
      });
    }
  }

  /**
   * Delete financial document
   */
  async delete(tenantId: string, id: string): Promise<void> {
    validateTenantId(tenantId);
    await deleteTenantDocument(this.collectionName, id, tenantId);
  }

  // ===== QUERY METHODS =====

  /**
   * Get all financial documents with filtering
   */
  async getAll(options: FinancialQueryOptions): Promise<FinancialDocument[]> {
    validateTenantId(options.tenantId);
    
    const constraints: QueryConstraint[] = [
      where('tenantId', '==', options.tenantId)
    ];

    if (options.type) {
      constraints.push(where('type', '==', options.type));
    }

    if (options.activityId) {
      constraints.push(where('activityId', '==', options.activityId));
    }

    if (options.status) {
      constraints.push(where('status', '==', options.status));
    }

    if (options.orderBy) {
      constraints.push(orderBy(options.orderBy, options.orderDirection || 'desc'));
    }

    if (options.limit) {
      constraints.push(limit(options.limit));
    }

    if (options.startAfter) {
      constraints.push(startAfter(options.startAfter));
    }

    return queryTenantCollection<FinancialDocument>(this.collectionName, constraints, options.tenantId);
  }

  /**
   * Get budgets for activity
   */
  async getBudgetsByActivity(tenantId: string, activityId: string): Promise<FinancialDocument[]> {
    return this.getAll({
      tenantId,
      type: 'budget',
      activityId,
      orderBy: 'createdAt',
      orderDirection: 'desc'
    });
  }

  /**
   * Get expenses for activity
   */
  async getExpensesByActivity(tenantId: string, activityId: string): Promise<FinancialDocument[]> {
    return this.getAll({
      tenantId,
      type: 'expense',
      activityId,
      orderBy: 'transactionDate',
      orderDirection: 'desc'
    });
  }

  /**
   * Get purchase requests for activity
   */
  async getPurchaseRequestsByActivity(tenantId: string, activityId: string): Promise<FinancialDocument[]> {
    return this.getAll({
      tenantId,
      type: 'purchase_request',
      activityId,
      orderBy: 'createdAt',
      orderDirection: 'desc'
    });
  }

  /**
   * Get purchase orders for activity
   */
  async getPurchaseOrdersByActivity(tenantId: string, activityId: string): Promise<FinancialDocument[]> {
    return this.getAll({
      tenantId,
      type: 'purchase_order',
      activityId,
      orderBy: 'orderDate',
      orderDirection: 'desc'
    });
  }

  // ===== ANALYTICS & REPORTING =====

  /**
   * Get financial summary for tenant
   */
  async getFinancialSummary(tenantId: string): Promise<FinancialSummary> {
    validateTenantId(tenantId);
    
    const allFinancials = await this.getAll({ tenantId });
    
    const budgets = allFinancials.filter(f => f.type === 'budget');
    const expenses = allFinancials.filter(f => f.type === 'expense');
    const purchaseRequests = allFinancials.filter(f => f.type === 'purchase_request');
    const purchaseOrders = allFinancials.filter(f => f.type === 'purchase_order');
    
    const totalBudgetAmount = budgets.reduce((sum, b) => sum + (b.totalBudget || 0), 0);
    const totalExpenseAmount = expenses.reduce((sum, e) => sum + e.amount, 0);
    const budgetUtilization = totalBudgetAmount > 0 ? (totalExpenseAmount / totalBudgetAmount) * 100 : 0;
    
    const pendingApprovals = allFinancials.filter(f => 
      f.status === 'pending' || f.status === 'Pending'
    ).length;
    
    // Find over-budget activities
    const overBudgetActivities: string[] = [];
    const activityBudgets = new Map<string, number>();
    const activityExpenses = new Map<string, number>();
    
    budgets.forEach(b => {
      activityBudgets.set(b.activityId, (b.totalBudget || 0));
    });
    
    expenses.forEach(e => {
      const current = activityExpenses.get(e.activityId) || 0;
      activityExpenses.set(e.activityId, current + e.amount);
    });
    
    activityBudgets.forEach((budget, activityId) => {
      const spent = activityExpenses.get(activityId) || 0;
      if (spent > budget) {
        overBudgetActivities.push(activityId);
      }
    });

    return {
      totalBudgets: budgets.length,
      totalExpenses: expenses.length,
      totalPurchaseRequests: purchaseRequests.length,
      totalPurchaseOrders: purchaseOrders.length,
      budgetUtilization,
      pendingApprovals,
      overBudgetActivities
    };
  }
}

// Export singleton instance
export const refactoredFinancialService = new RefactoredFinancialService();
