/**
 * Real License Service Implementation
 * Provides actual license validation and management for EVEXA
 */

import {
  collection,
  doc,
  getDoc,
  addDoc,
  updateDoc,
  query,
  where,
  getDocs,
  Timestamp,
  runTransaction
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { auditService } from './auditService';

export interface RealLicenseInfo {
  id: string;
  licenseKey: string;
  licenseType: 'trial' | 'basic' | 'professional' | 'enterprise' | 'developer';
  tenantId: string;
  tenantName: string;
  isActive: boolean;
  issueDate: Date;
  expiryDate: Date;
  maxUsers: number;
  maxTenants: number;
  features: string[];
  domainRestrictions?: string[];
  ipRestrictions?: string[];
  hardwareFingerprint?: string;
  validationCount: number;
  lastValidated?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface LicenseCreationRequest {
  tenantId: string;
  tenantName: string;
  licenseType: 'trial' | 'basic' | 'professional' | 'enterprise' | 'developer';
  durationDays: number;
  maxUsers: number;
  maxTenants: number;
  features: string[];
  domainRestrictions?: string[];
  ipRestrictions?: string[];
}

export interface LicenseValidationResult {
  isValid: boolean;
  license?: RealLicenseInfo;
  reason?: string;
  remainingDays?: number;
  warnings?: string[];
}

class RealLicenseService {
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private licenseCache = new Map<string, { license: RealLicenseInfo; timestamp: number }>();

  /**
   * Create a new license for a tenant
   */
  async createLicense(request: LicenseCreationRequest): Promise<{ success: boolean; licenseKey?: string; error?: string }> {
    try {
      const licenseKey = this.generateLicenseKey(request.licenseType);
      const now = new Date();
      const expiryDate = new Date(now.getTime() + (request.durationDays * 24 * 60 * 60 * 1000));

      const licenseData: Omit<RealLicenseInfo, 'id'> = {
        licenseKey,
        licenseType: request.licenseType,
        tenantId: request.tenantId,
        tenantName: request.tenantName,
        isActive: true,
        issueDate: now,
        expiryDate,
        maxUsers: request.maxUsers,
        maxTenants: request.maxTenants,
        features: request.features,
        domainRestrictions: request.domainRestrictions,
        ipRestrictions: request.ipRestrictions,
        validationCount: 0,
        createdAt: now,
        updatedAt: now
      };

      const docRef = await addDoc(collection(db, 'licenses'), {
        ...licenseData,
        issueDate: Timestamp.fromDate(licenseData.issueDate),
        expiryDate: Timestamp.fromDate(licenseData.expiryDate),
        createdAt: Timestamp.fromDate(licenseData.createdAt),
        updatedAt: Timestamp.fromDate(licenseData.updatedAt)
      });

      // Log license creation
      await auditService.logEvent({
        tenantId: request.tenantId,
        eventType: 'license_created',
        category: 'security',
        action: 'create',
        resource: 'license',
        resourceId: docRef.id,
        details: {
          licenseType: request.licenseType,
          durationDays: request.durationDays,
          maxUsers: request.maxUsers
        },
        severity: 'high'
      });

      return { success: true, licenseKey };
    } catch (error: any) {
      console.error('Error creating license:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Validate a license key
   */
  async validateLicense(licenseKey: string): Promise<LicenseValidationResult> {
    try {
      // Check cache first
      const cached = this.licenseCache.get(licenseKey);
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
        return this.performValidationChecks(cached.license);
      }

      // Fetch from database
      const q = query(collection(db, 'licenses'), where('licenseKey', '==', licenseKey));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        return {
          isValid: false,
          reason: 'License key not found'
        };
      }

      const licenseDoc = querySnapshot.docs[0];
      const licenseData = licenseDoc.data();
      
      const license: RealLicenseInfo = {
        id: licenseDoc.id,
        licenseKey: licenseData.licenseKey,
        licenseType: licenseData.licenseType,
        tenantId: licenseData.tenantId,
        tenantName: licenseData.tenantName,
        isActive: licenseData.isActive,
        issueDate: licenseData.issueDate.toDate(),
        expiryDate: licenseData.expiryDate.toDate(),
        maxUsers: licenseData.maxUsers,
        maxTenants: licenseData.maxTenants,
        features: licenseData.features,
        domainRestrictions: licenseData.domainRestrictions,
        ipRestrictions: licenseData.ipRestrictions,
        hardwareFingerprint: licenseData.hardwareFingerprint,
        validationCount: licenseData.validationCount || 0,
        lastValidated: licenseData.lastValidated?.toDate(),
        createdAt: licenseData.createdAt.toDate(),
        updatedAt: licenseData.updatedAt.toDate()
      };

      // Cache the license
      this.licenseCache.set(licenseKey, { license, timestamp: Date.now() });

      // Update validation stats
      await this.updateValidationStats(licenseDoc.id);

      return this.performValidationChecks(license);
    } catch (error: any) {
      console.error('License validation error:', error);
      return {
        isValid: false,
        reason: 'License validation failed'
      };
    }
  }

  /**
   * Perform validation checks on a license
   */
  private performValidationChecks(license: RealLicenseInfo): LicenseValidationResult {
    const now = new Date();
    const warnings: string[] = [];

    // Check if license is active
    if (!license.isActive) {
      return {
        isValid: false,
        reason: 'License is inactive'
      };
    }

    // Check expiry date
    if (license.expiryDate < now) {
      return {
        isValid: false,
        reason: 'License has expired'
      };
    }

    // Calculate remaining days
    const remainingDays = Math.ceil((license.expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    // Add warnings for upcoming expiry
    if (remainingDays <= 30) {
      warnings.push(`License expires in ${remainingDays} days`);
    }
    if (remainingDays <= 7) {
      warnings.push('License expiring soon - please renew');
    }

    // Check domain restrictions (if in browser)
    if (typeof window !== 'undefined' && license.domainRestrictions && license.domainRestrictions.length > 0) {
      const currentDomain = window.location.hostname;
      if (!license.domainRestrictions.includes(currentDomain)) {
        return {
          isValid: false,
          reason: `License not valid for domain: ${currentDomain}`
        };
      }
    }

    return {
      isValid: true,
      license,
      remainingDays,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  /**
   * Generate a license key
   */
  private generateLicenseKey(licenseType: string): string {
    const prefix = this.getLicensePrefix(licenseType);
    const timestamp = Date.now().toString(36).toUpperCase();
    const random = Math.random().toString(36).substr(2, 8).toUpperCase();
    const checksum = this.generateChecksum(`${prefix}${timestamp}${random}`);
    
    return `${prefix}-${timestamp}-${random}-${checksum}`;
  }

  /**
   * Get license prefix based on type
   */
  private getLicensePrefix(licenseType: string): string {
    const prefixes = {
      trial: 'EVX-TRL',
      basic: 'EVX-BSC',
      professional: 'EVX-PRO',
      enterprise: 'EVX-ENT',
      developer: 'EVX-DEV'
    };
    return prefixes[licenseType as keyof typeof prefixes] || 'EVX-UNK';
  }

  /**
   * Generate checksum for license key
   */
  private generateChecksum(input: string): string {
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36).substr(0, 4).toUpperCase();
  }

  /**
   * Update validation statistics
   */
  private async updateValidationStats(licenseId: string): Promise<void> {
    try {
      const licenseRef = doc(db, 'licenses', licenseId);
      await updateDoc(licenseRef, {
        validationCount: (await getDoc(licenseRef)).data()?.validationCount + 1 || 1,
        lastValidated: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.warn('Failed to update validation stats:', error);
    }
  }

  /**
   * Deactivate a license
   */
  async deactivateLicense(licenseKey: string): Promise<{ success: boolean; error?: string }> {
    try {
      const q = query(collection(db, 'licenses'), where('licenseKey', '==', licenseKey));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        return { success: false, error: 'License not found' };
      }

      const licenseDoc = querySnapshot.docs[0];
      await updateDoc(licenseDoc.ref, {
        isActive: false,
        updatedAt: Timestamp.now()
      });

      // Clear from cache
      this.licenseCache.delete(licenseKey);

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Get license features for a given license type
   */
  static getLicenseFeatures(licenseType: string): string[] {
    const features = {
      trial: ['basic_dashboard', 'task_management', 'basic_analytics'],
      basic: ['basic_dashboard', 'task_management', 'basic_analytics', 'email_campaigns', 'contact_management'],
      professional: ['all_basic', 'advanced_analytics', 'workflow_automation', 'social_media_hub', 'ai_features'],
      enterprise: ['all_professional', 'multi_tenant', 'advanced_security', 'custom_branding', 'api_access', 'priority_support'],
      developer: ['all_enterprise', 'development_tools', 'testing_features', 'debug_mode']
    };
    return features[licenseType as keyof typeof features] || [];
  }
}

export const realLicenseService = new RealLicenseService();
