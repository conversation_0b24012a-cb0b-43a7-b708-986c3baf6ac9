/**
 * Module Permission Engine Tests
 * Comprehensive tests for the enhanced permission system
 */

import { 
  ModulePermissionEngine, 
  createModulePermissionEngine, 
  createPermissionContext,
  validatePermissions
} from '../modulePermissionEngine';
import { TenantPersona, EvexaModule, PermissionAction } from '@/types/personas';

// Mock Firebase
jest.mock('@/lib/firebase', () => ({
  db: {}
}));

jest.mock('firebase/firestore', () => ({
  collection: jest.fn(),
  doc: jest.fn(),
  addDoc: jest.fn(),
  updateDoc: jest.fn(),
  deleteDoc: jest.fn(),
  getDocs: jest.fn(),
  getDoc: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  orderBy: jest.fn(),
  serverTimestamp: jest.fn(() => new Date()),
  writeBatch: jest.fn()
}));

describe('ModulePermissionEngine', () => {
  const testTenantId = 'test-tenant-123';
  let engine: ModulePermissionEngine;

  beforeEach(() => {
    engine = createModulePermissionEngine(testTenantId);
    jest.clearAllMocks();
  });

  describe('Permission Context Creation', () => {
    test('should create valid permission context', () => {
      const context = createPermissionContext(
        'user-123',
        'tenant-456',
        undefined,
        'professional'
      );

      expect(context.userId).toBe('user-123');
      expect(context.tenantId).toBe('tenant-456');
      expect(context.subscriptionTier).toBe('professional');
      expect(context.customOverrides).toEqual([]);
    });

    test('should include persona in context', () => {
      const mockPersona: TenantPersona = {
        id: 'test-persona',
        name: 'Test Persona',
        description: 'Test description',
        category: 'custom',
        isActive: true,
        permissions: {
          modules: [
            {
              module: 'exhibitions',
              actions: ['read', 'write'],
              description: 'Exhibition access'
            }
          ],
          systemPermissions: {
            canManageUsers: false,
            canManageSettings: false,
            canViewAnalytics: true,
            canExportData: true,
            canManageIntegrations: false,
            canAccessSupport: true
          }
        },
        targetUsers: ['Test users'],
        tenantId: 'tenant-456',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const context = createPermissionContext(
        'user-123',
        'tenant-456',
        mockPersona,
        'professional'
      );

      expect(context.persona).toEqual(mockPersona);
    });
  });

  describe('Permission Checking', () => {
    test('should deny access when no persona is provided', async () => {
      const context = createPermissionContext('user-123', 'tenant-456');
      
      const hasAccess = await engine.checkPermission(
        context,
        'exhibitions' as EvexaModule,
        'read',
        { useCache: false }
      );

      expect(hasAccess).toBe(false);
    });

    test('should grant access when persona has required permission', async () => {
      const mockPersona: TenantPersona = {
        id: 'test-persona',
        name: 'Test Persona',
        description: 'Test description',
        category: 'custom',
        isActive: true,
        permissions: {
          modules: [
            {
              module: 'exhibitions',
              actions: ['read', 'write'],
              description: 'Exhibition access'
            }
          ],
          systemPermissions: {
            canManageUsers: false,
            canManageSettings: false,
            canViewAnalytics: true,
            canExportData: true,
            canManageIntegrations: false,
            canAccessSupport: true
          }
        },
        targetUsers: ['Test users'],
        tenantId: 'tenant-456',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const context = createPermissionContext(
        'user-123',
        'tenant-456',
        mockPersona,
        'professional'
      );
      
      const hasAccess = await engine.checkPermission(
        context,
        'exhibitions' as EvexaModule,
        'read',
        { useCache: false }
      );

      expect(hasAccess).toBe(true);
    });

    test('should deny access when persona lacks required permission', async () => {
      const mockPersona: TenantPersona = {
        id: 'test-persona',
        name: 'Test Persona',
        description: 'Test description',
        category: 'custom',
        isActive: true,
        permissions: {
          modules: [
            {
              module: 'exhibitions',
              actions: ['read'],
              description: 'Exhibition read access only'
            }
          ],
          systemPermissions: {
            canManageUsers: false,
            canManageSettings: false,
            canViewAnalytics: true,
            canExportData: true,
            canManageIntegrations: false,
            canAccessSupport: true
          }
        },
        targetUsers: ['Test users'],
        tenantId: 'tenant-456',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const context = createPermissionContext(
        'user-123',
        'tenant-456',
        mockPersona,
        'professional'
      );
      
      const hasAccess = await engine.checkPermission(
        context,
        'exhibitions' as EvexaModule,
        'write',
        { useCache: false }
      );

      expect(hasAccess).toBe(false);
    });
  });

  describe('Bulk Permission Checking', () => {
    test('should check multiple permissions correctly', async () => {
      const mockPersona: TenantPersona = {
        id: 'test-persona',
        name: 'Test Persona',
        description: 'Test description',
        category: 'custom',
        isActive: true,
        permissions: {
          modules: [
            {
              module: 'exhibitions',
              actions: ['read', 'write'],
              description: 'Exhibition access'
            },
            {
              module: 'tasks',
              actions: ['read'],
              description: 'Task read access'
            }
          ],
          systemPermissions: {
            canManageUsers: false,
            canManageSettings: false,
            canViewAnalytics: true,
            canExportData: true,
            canManageIntegrations: false,
            canAccessSupport: true
          }
        },
        targetUsers: ['Test users'],
        tenantId: 'tenant-456',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const context = createPermissionContext(
        'user-123',
        'tenant-456',
        mockPersona,
        'professional'
      );

      const permissions = [
        { module: 'exhibitions' as EvexaModule, action: 'read' as PermissionAction },
        { module: 'exhibitions' as EvexaModule, action: 'write' as PermissionAction },
        { module: 'tasks' as EvexaModule, action: 'read' as PermissionAction },
        { module: 'tasks' as EvexaModule, action: 'write' as PermissionAction }
      ];

      const result = await engine.checkBulkPermissions(context, permissions);

      expect(result.userId).toBe('user-123');
      expect(result.results).toHaveLength(4);
      expect(result.results[0].hasPermission).toBe(true); // exhibitions:read
      expect(result.results[1].hasPermission).toBe(true); // exhibitions:write
      expect(result.results[2].hasPermission).toBe(true); // tasks:read
      expect(result.results[3].hasPermission).toBe(false); // tasks:write
      expect(result.overallAccess).toBe(false); // Not all permissions granted
    });
  });

  describe('Permission Rules', () => {
    test('should add and evaluate custom rules', async () => {
      const context = createPermissionContext('user-123', 'tenant-456');

      // Add a custom rule that grants access
      engine.addRule({
        id: 'test-rule',
        name: 'Test Rule',
        description: 'Test rule for specific user',
        condition: (ctx) => ctx.userId === 'user-123',
        modules: ['exhibitions' as EvexaModule],
        actions: ['read'],
        priority: 100
      });

      const hasAccess = await engine.checkPermission(
        context,
        'exhibitions' as EvexaModule,
        'read',
        { useCache: false }
      );

      expect(hasAccess).toBe(true);
    });

    test('should remove custom rules', async () => {
      const context = createPermissionContext('user-123', 'tenant-456');

      // Add a rule
      engine.addRule({
        id: 'test-rule',
        name: 'Test Rule',
        description: 'Test rule for specific user',
        condition: (ctx) => ctx.userId === 'user-123',
        modules: ['exhibitions' as EvexaModule],
        actions: ['read'],
        priority: 100
      });

      // Verify rule works
      let hasAccess = await engine.checkPermission(
        context,
        'exhibitions' as EvexaModule,
        'read',
        { useCache: false }
      );
      expect(hasAccess).toBe(true);

      // Remove rule
      engine.removeRule('test-rule');

      // Verify rule no longer works
      hasAccess = await engine.checkPermission(
        context,
        'exhibitions' as EvexaModule,
        'read',
        { useCache: false }
      );
      expect(hasAccess).toBe(false);
    });
  });

  describe('Permission Validation', () => {
    test('should validate required permissions successfully', async () => {
      const mockPersona: TenantPersona = {
        id: 'test-persona',
        name: 'Test Persona',
        description: 'Test description',
        category: 'custom',
        isActive: true,
        permissions: {
          modules: [
            {
              module: 'exhibitions',
              actions: ['read', 'write'],
              description: 'Exhibition access'
            }
          ],
          systemPermissions: {
            canManageUsers: false,
            canManageSettings: false,
            canViewAnalytics: true,
            canExportData: true,
            canManageIntegrations: false,
            canAccessSupport: true
          }
        },
        targetUsers: ['Test users'],
        tenantId: 'tenant-456',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const context = createPermissionContext(
        'user-123',
        'tenant-456',
        mockPersona,
        'professional'
      );

      const requirements = [
        { module: 'exhibitions' as EvexaModule, action: 'read' as PermissionAction }
      ];

      await expect(validatePermissions(engine, context, requirements)).resolves.not.toThrow();
    });

    test('should throw error when permissions are missing', async () => {
      const context = createPermissionContext('user-123', 'tenant-456');

      const requirements = [
        { module: 'exhibitions' as EvexaModule, action: 'read' as PermissionAction }
      ];

      await expect(validatePermissions(engine, context, requirements))
        .rejects.toThrow('Access denied. Missing permissions: exhibitions:read');
    });
  });

  describe('Cache Management', () => {
    test('should provide cache statistics', () => {
      const stats = engine.getCacheStats();
      
      expect(stats).toHaveProperty('size');
      expect(stats).toHaveProperty('hitRate');
      expect(typeof stats.size).toBe('number');
      expect(typeof stats.hitRate).toBe('number');
    });

    test('should invalidate cache', () => {
      // This is a basic test - in a real implementation we'd verify cache behavior
      expect(() => engine.invalidateCache()).not.toThrow();
      expect(() => engine.invalidateCache('user-123')).not.toThrow();
    });
  });
});
