/**
 * EVEXA Business Service
 * Manages EVEXA's own marketing, sales, and customer operations
 */

import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  arrayUnion
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { 
  EvexaLead, 
  LandingPage, 
  OnboardingFlow, 
  SalesEnablementMaterial,
  EvexaBusinessMetrics
} from '@/types/firestore';

export class EvexaBusinessService {
  private leadsCollection = 'evexa_leads';
  private landingPagesCollection = 'landing_pages';
  private onboardingFlowsCollection = 'onboarding_flows';
  private salesMaterialsCollection = 'sales_materials';
  private businessMetricsCollection = 'evexa_business_metrics';

  // Lead Management for EVEXA's own business
  async createLead(leadData: Omit<EvexaLead, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, this.leadsCollection), {
        ...leadData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating EVEXA lead:', error);
      throw error;
    }
  }

  async updateLead(leadId: string, updates: Partial<EvexaLead>): Promise<void> {
    try {
      await updateDoc(doc(db, this.leadsCollection, leadId), {
        ...updates,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating EVEXA lead:', error);
      throw error;
    }
  }

  async getLeads(filters?: {
    status?: string;
    source?: string;
    assignedTo?: string;
    limit?: number;
  }): Promise<EvexaLead[]> {
    try {
      let q = query(collection(db, this.leadsCollection), orderBy('createdAt', 'desc'));

      if (filters?.status) {
        q = query(q, where('status', '==', filters.status));
      }
      if (filters?.source) {
        q = query(q, where('source', '==', filters.source));
      }
      if (filters?.assignedTo) {
        q = query(q, where('assignedTo', '==', filters.assignedTo));
      }
      if (filters?.limit) {
        q = query(q, limit(filters.limit));
      }

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as EvexaLead[];
    } catch (error) {
      console.error('Error getting EVEXA leads:', error);
      throw error;
    }
  }

  // Landing Page Management
  async createLandingPage(pageData: Omit<LandingPage, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, this.landingPagesCollection), {
        ...pageData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating landing page:', error);
      throw error;
    }
  }

  async updateLandingPage(pageId: string, updates: Partial<LandingPage>): Promise<void> {
    try {
      await updateDoc(doc(db, this.landingPagesCollection, pageId), {
        ...updates,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating landing page:', error);
      throw error;
    }
  }

  async getLandingPages(publishedOnly: boolean = false): Promise<LandingPage[]> {
    try {
      let q = query(collection(db, this.landingPagesCollection), orderBy('createdAt', 'desc'));
      
      if (publishedOnly) {
        q = query(q, where('isPublished', '==', true));
      }

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as LandingPage[];
    } catch (error) {
      console.error('Error getting landing pages:', error);
      throw error;
    }
  }

  async getLandingPageBySlug(slug: string): Promise<LandingPage | null> {
    try {
      const q = query(
        collection(db, this.landingPagesCollection), 
        where('slug', '==', slug),
        where('isPublished', '==', true),
        limit(1)
      );
      
      const querySnapshot = await getDocs(q);
      if (querySnapshot.empty) return null;
      
      const doc = querySnapshot.docs[0];
      return { id: doc.id, ...doc.data() } as LandingPage;
    } catch (error) {
      console.error('Error getting landing page by slug:', error);
      throw error;
    }
  }

  async trackLandingPageView(pageId: string): Promise<void> {
    try {
      const pageRef = doc(db, this.landingPagesCollection, pageId);
      const pageDoc = await getDoc(pageRef);
      
      if (pageDoc.exists()) {
        const currentViews = pageDoc.data().analytics?.views || 0;
        await updateDoc(pageRef, {
          'analytics.views': currentViews + 1,
          'analytics.lastUpdated': Timestamp.now()
        });
      }
    } catch (error) {
      console.error('Error tracking landing page view:', error);
    }
  }

  async trackLandingPageConversion(pageId: string): Promise<void> {
    try {
      const pageRef = doc(db, this.landingPagesCollection, pageId);
      const pageDoc = await getDoc(pageRef);
      
      if (pageDoc.exists()) {
        const data = pageDoc.data();
        const currentConversions = data.analytics?.conversions || 0;
        const currentViews = data.analytics?.views || 1;
        const newConversions = currentConversions + 1;
        const conversionRate = (newConversions / currentViews) * 100;
        
        await updateDoc(pageRef, {
          'analytics.conversions': newConversions,
          'analytics.conversionRate': conversionRate,
          'analytics.lastUpdated': Timestamp.now()
        });
      }
    } catch (error) {
      console.error('Error tracking landing page conversion:', error);
    }
  }

  // Onboarding Flow Management
  async createOnboardingFlow(flowData: Omit<OnboardingFlow, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, this.onboardingFlowsCollection), {
        ...flowData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating onboarding flow:', error);
      throw error;
    }
  }

  async getOnboardingFlows(targetAudience?: string): Promise<OnboardingFlow[]> {
    try {
      let q = query(collection(db, this.onboardingFlowsCollection), orderBy('createdAt', 'desc'));
      
      if (targetAudience) {
        q = query(q, where('targetAudience', '==', targetAudience));
      }

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as OnboardingFlow[];
    } catch (error) {
      console.error('Error getting onboarding flows:', error);
      throw error;
    }
  }

  // Sales Enablement Materials
  async createSalesMaterial(materialData: Omit<SalesEnablementMaterial, 'id'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, this.salesMaterialsCollection), {
        ...materialData,
        lastUpdated: Timestamp.now(),
        downloadCount: 0
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating sales material:', error);
      throw error;
    }
  }

  async getSalesMaterials(type?: string): Promise<SalesEnablementMaterial[]> {
    try {
      let q = query(collection(db, this.salesMaterialsCollection), orderBy('lastUpdated', 'desc'));
      
      if (type) {
        q = query(q, where('type', '==', type));
      }

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as SalesEnablementMaterial[];
    } catch (error) {
      console.error('Error getting sales materials:', error);
      throw error;
    }
  }

  async trackMaterialDownload(materialId: string): Promise<void> {
    try {
      const materialRef = doc(db, this.salesMaterialsCollection, materialId);
      const materialDoc = await getDoc(materialRef);
      
      if (materialDoc.exists()) {
        const currentDownloads = materialDoc.data().downloadCount || 0;
        await updateDoc(materialRef, {
          downloadCount: currentDownloads + 1
        });
      }
    } catch (error) {
      console.error('Error tracking material download:', error);
    }
  }

  // Business Metrics for EVEXA
  async calculateBusinessMetrics(
    period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly',
    startDate: Date,
    endDate: Date
  ): Promise<EvexaBusinessMetrics> {
    try {
      // Get leads in period
      const leads = await this.getLeadsInPeriod(startDate, endDate);
      
      // Calculate lead metrics
      const totalLeads = leads.length;
      const qualifiedLeads = leads.filter(lead => lead.status === 'qualified').length;
      const leadConversionRate = totalLeads > 0 ? (qualifiedLeads / totalLeads) * 100 : 0;
      
      // Group leads by source
      const leadsBySource: Record<string, number> = {};
      leads.forEach(lead => {
        leadsBySource[lead.source] = (leadsBySource[lead.source] || 0) + 1;
      });

      // Calculate customer metrics (mock data for now)
      const newCustomers = leads.filter(lead => lead.status === 'closed_won').length;
      const customerAcquisitionCost = newCustomers > 0 ? 5000 : 0; // Mock CAC
      const customerLifetimeValue = 50000; // Mock LTV
      
      // Calculate revenue metrics (mock data)
      const monthlyRecurringRevenue = newCustomers * 2000; // Mock MRR
      const annualRecurringRevenue = monthlyRecurringRevenue * 12;

      const metrics: EvexaBusinessMetrics = {
        period,
        startDate: Timestamp.fromDate(startDate),
        endDate: Timestamp.fromDate(endDate),
        totalLeads,
        qualifiedLeads,
        leadConversionRate,
        leadsBySource,
        newCustomers,
        totalCustomers: 150, // Mock total customers
        customerAcquisitionCost,
        customerLifetimeValue,
        churnRate: 5, // Mock churn rate
        monthlyRecurringRevenue,
        annualRecurringRevenue,
        totalRevenue: monthlyRecurringRevenue,
        revenueGrowthRate: 15, // Mock growth rate
        activeUsers: 1200, // Mock active users
        featureAdoption: {
          'task_management': 95,
          'contact_management': 87,
          'marketing_automation': 65,
          'analytics': 78
        },
        supportTickets: 25, // Mock support tickets
        npsScore: 8.5, // Mock NPS score
        createdAt: Timestamp.now()
      };

      // Save metrics
      await addDoc(collection(db, this.businessMetricsCollection), metrics);
      
      return metrics;
    } catch (error) {
      console.error('Error calculating business metrics:', error);
      throw error;
    }
  }

  private async getLeadsInPeriod(startDate: Date, endDate: Date): Promise<EvexaLead[]> {
    try {
      const q = query(
        collection(db, this.leadsCollection),
        where('createdAt', '>=', Timestamp.fromDate(startDate)),
        where('createdAt', '<=', Timestamp.fromDate(endDate))
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as EvexaLead[];
    } catch (error) {
      console.error('Error getting leads in period:', error);
      return [];
    }
  }

  // Lead scoring for EVEXA prospects
  calculateLeadScore(lead: EvexaLead): number {
    let score = 0;
    
    // Company size scoring
    const companySizeScores = {
      'startup': 20,
      'small': 40,
      'medium': 70,
      'enterprise': 100
    };
    score += companySizeScores[lead.companySize || 'small'];
    
    // Source scoring
    const sourceScores = {
      'demo_request': 80,
      'contact_form': 60,
      'landing_page': 40,
      'referral': 90,
      'event': 70,
      'social_media': 30
    };
    score += sourceScores[lead.source] || 20;
    
    // Interest scoring
    score += lead.interests.length * 10;
    
    // Budget indication
    if (lead.budget) score += 30;
    if (lead.timeline) score += 20;
    
    return Math.min(score, 100);
  }
  // Customer Lead Management
  async createCustomerLead(leadData: {
    personalInfo: {
      firstName: string;
      lastName: string;
      email: string;
      phone?: string;
      jobTitle?: string;
    };
    companyInfo: {
      name: string;
      size: string;
      industry: string;
      website?: string;
      country?: string;
    };
    exhibitionInfo: {
      annualExhibitions: string;
      budget?: string;
      currentTools?: string;
      painPoints?: string;
    };
    preferences: {
      selectedPlan: string;
      billingCycle: string;
      marketingConsent: boolean;
    };
    source: string;
    status: string;
  }): Promise<string> {
    try {
      const leadDoc = {
        ...leadData,
        id: `lead_${Date.now()}`,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        lastContactDate: null,
        assignedTo: null,
        score: this.calculateLeadScore(leadData),
        tags: this.generateLeadTags(leadData),
        notes: []
      };

      const docRef = await addDoc(collection(db, 'customer_leads'), leadDoc);

      // Send welcome email (TODO: implement email service)
      await this.sendWelcomeEmail(leadData.personalInfo.email, leadData.personalInfo.firstName);

      return docRef.id;
    } catch (error) {
      console.error('Error creating customer lead:', error);
      throw error;
    }
  }

  private calculateLeadScore(leadData: any): number {
    let score = 0;

    // Company size scoring
    const sizeScores: Record<string, number> = {
      '1-10': 10,
      '11-50': 20,
      '51-200': 30,
      '201-1000': 40,
      '1000+': 50
    };
    score += sizeScores[leadData.companyInfo.size] || 0;

    // Exhibition volume scoring
    const exhibitionScores: Record<string, number> = {
      '1-5': 10,
      '6-15': 25,
      '16-30': 40,
      '30+': 50
    };
    score += exhibitionScores[leadData.exhibitionInfo.annualExhibitions] || 0;

    // Budget scoring
    const budgetScores: Record<string, number> = {
      'under-100k': 5,
      '100k-500k': 15,
      '500k-1m': 25,
      '1m-5m': 35,
      '5m+': 50
    };
    score += budgetScores[leadData.exhibitionInfo.budget] || 0;

    // Plan selection scoring
    const planScores: Record<string, number> = {
      'starter': 10,
      'professional': 25,
      'enterprise': 40
    };
    score += planScores[leadData.preferences.selectedPlan] || 0;

    return Math.min(score, 100); // Cap at 100
  }

  private generateLeadTags(leadData: any): string[] {
    const tags: string[] = [];

    tags.push(leadData.companyInfo.industry);
    tags.push(leadData.companyInfo.size);
    tags.push(leadData.exhibitionInfo.annualExhibitions);
    tags.push(leadData.preferences.selectedPlan);

    if (leadData.exhibitionInfo.budget) {
      tags.push(leadData.exhibitionInfo.budget);
    }

    if (leadData.companyInfo.country) {
      tags.push(leadData.companyInfo.country);
    }

    return tags;
  }

  private async sendWelcomeEmail(email: string, firstName: string): Promise<void> {
    // TODO: Implement email service integration
    console.log(`Sending welcome email to ${email} for ${firstName}`);
  }

  async getCustomerLeads(filters?: {
    status?: string;
    industry?: string;
    minScore?: number;
    assignedTo?: string;
  }): Promise<any[]> {
    try {
      let q = query(collection(db, 'customer_leads'), orderBy('createdAt', 'desc'));

      if (filters?.status) {
        q = query(q, where('status', '==', filters.status));
      }

      if (filters?.industry) {
        q = query(q, where('companyInfo.industry', '==', filters.industry));
      }

      if (filters?.assignedTo) {
        q = query(q, where('assignedTo', '==', filters.assignedTo));
      }

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        lastContactDate: doc.data().lastContactDate?.toDate() || null
      }));
    } catch (error) {
      console.error('Error getting customer leads:', error);
      return [];
    }
  }

  async updateLeadStatus(leadId: string, status: string, notes?: string): Promise<void> {
    try {
      const updateData: any = {
        status,
        updatedAt: Timestamp.now()
      };

      if (notes) {
        updateData.notes = arrayUnion({
          text: notes,
          createdAt: Timestamp.now(),
          createdBy: 'current-user' // TODO: get from auth context
        });
      }

      await updateDoc(doc(db, 'customer_leads', leadId), updateData);
    } catch (error) {
      console.error('Error updating lead status:', error);
      throw error;
    }
  }
}

export const evexaBusinessService = new EvexaBusinessService();
