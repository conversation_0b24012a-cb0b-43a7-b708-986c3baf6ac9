/**
 * Tenant Collection Initialization Service
 * Handles proper collection setup when new tenants are created
 */

import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { collection, doc, setDoc, writeBatch, serverTimestamp } from 'firebase/firestore';

// Core collections that should be initialized at tenant creation
export const CORE_TENANT_COLLECTIONS = [
  // User Management
  COLLECTIONS.USER_PROFILES,
  COLLECTIONS.USER_GROUPS,
  COLLECTIONS.USER_SETTINGS,
  
  // Exhibition Management
  COLLECTIONS.EXHIBITIONS,
  COLLECTIONS.EXHIBITION_EVENTS,
  COLLECTIONS.EXHIBITION_TASKS,
  
  // Lead Management
  COLLECTIONS.LEAD_CONTACTS,
  COLLECTIONS.LEAD_SEGMENTS,
  COLLECTIONS.LEAD_COMMUNICATIONS,
  
  // Financial Management
  COLLECTIONS.FINANCIALS, // NEW: Consolidated financial collection
  COLLECTIONS.VENDOR_PROFILES,

  // DEPRECATED: Legacy financial collections (for migration compatibility)
  COLLECTIONS.BUDGET_ALLOCATIONS,
  COLLECTIONS.EXPENSE_RECORDS,
  COLLECTIONS.PURCHASE_REQUESTS,
  COLLECTIONS.PURCHASE_ORDERS,
  
  // Communication
  COLLECTIONS.EMAIL_CAMPAIGNS,
  COLLECTIONS.EMAIL_TEMPLATES,
  COLLECTIONS.SOCIAL_POSTS,
  COLLECTIONS.NOTIFICATIONS,
  
  // System
  COLLECTIONS.AUDIT_LOGS,
  COLLECTIONS.SECURITY_EVENTS,
  COLLECTIONS.SUPPORT_TICKETS
];

// Collections created on-demand (when first document is added)
export const ON_DEMAND_COLLECTIONS = [
  // Logistics (created when first shipment/travel is added)
  COLLECTIONS.TRAVEL_BOOKINGS,
  COLLECTIONS.SHIPMENT_TRACKING,
  COLLECTIONS.BOOTH_MEETINGS,
  
  // Advanced features (created when features are enabled)
  COLLECTIONS.MEDIA_CONTACTS,
  COLLECTIONS.PRESS_KITS,
  COLLECTIONS.COMPETITOR_PROFILES,
  COLLECTIONS.VIP_VISITS,
  COLLECTIONS.GIFT_ITEMS,
  
  // Analytics (created when analytics are first generated)
  COLLECTIONS.BOOTH_ANALYTICS,
  COLLECTIONS.SUCCESS_SCORECARDS,
  COLLECTIONS.INSIGHTS
];

// System-wide collections (not tenant-specific)
export const SYSTEM_COLLECTIONS = [
  COLLECTIONS.TENANTS,
  'tenant-users',
  'tenant-isolation-configs',
  'system-metrics',
  'global-settings'
];

/**
 * Initialize core collections for a new tenant
 */
export async function initializeTenantCollections(tenantId: string): Promise<{
  success: boolean;
  initializedCollections: string[];
  errors: string[];
}> {
  console.log(`🏗️ Initializing collections for tenant: ${tenantId}`);
  
  const initializedCollections: string[] = [];
  const errors: string[] = [];
  const batch = writeBatch(db);
  
  try {
    for (const collectionName of CORE_TENANT_COLLECTIONS) {
      try {
        // Create tenant-scoped collection path
        const tenantCollectionRef = collection(db, 'tenant-data', tenantId, collectionName);
        const initDocRef = doc(tenantCollectionRef, `${collectionName}_init`);
        
        // Create initialization document
        const initDoc = {
          id: `${collectionName}_init`,
          _collection_info: {
            name: collectionName,
            tenant_id: tenantId,
            initialized_at: serverTimestamp(),
            is_initialization_document: true,
            collection_type: 'core',
            description: getCollectionDescription(collectionName)
          },
          created_at: serverTimestamp(),
          updated_at: serverTimestamp(),
          created_by: 'system'
        };
        
        batch.set(initDocRef, initDoc);
        initializedCollections.push(collectionName);
        
      } catch (error) {
        console.error(`Failed to initialize collection ${collectionName}:`, error);
        errors.push(`${collectionName}: ${error}`);
      }
    }
    
    // Commit all initialization documents
    await batch.commit();
    
    console.log(`✅ Initialized ${initializedCollections.length} collections for tenant ${tenantId}`);
    
    return {
      success: errors.length === 0,
      initializedCollections,
      errors
    };
    
  } catch (error) {
    console.error('Error during tenant collection initialization:', error);
    return {
      success: false,
      initializedCollections,
      errors: [`Batch commit failed: ${error}`]
    };
  }
}

/**
 * Create on-demand collection when first document is added
 */
export async function initializeOnDemandCollection(
  tenantId: string, 
  collectionName: string
): Promise<boolean> {
  try {
    const tenantCollectionRef = collection(db, 'tenant-data', tenantId, collectionName);
    const initDocRef = doc(tenantCollectionRef, `${collectionName}_init`);
    
    const initDoc = {
      id: `${collectionName}_init`,
      _collection_info: {
        name: collectionName,
        tenant_id: tenantId,
        initialized_at: serverTimestamp(),
        is_initialization_document: true,
        collection_type: 'on_demand',
        description: getCollectionDescription(collectionName)
      },
      created_at: serverTimestamp(),
      updated_at: serverTimestamp(),
      created_by: 'system'
    };
    
    await setDoc(initDocRef, initDoc);
    console.log(`✅ Initialized on-demand collection: ${collectionName} for tenant ${tenantId}`);
    return true;
    
  } catch (error) {
    console.error(`Failed to initialize on-demand collection ${collectionName}:`, error);
    return false;
  }
}

/**
 * Get collection description for documentation
 */
function getCollectionDescription(collectionName: string): string {
  const descriptions: Record<string, string> = {
    [COLLECTIONS.USER_PROFILES]: 'User profiles and authentication data',
    [COLLECTIONS.USER_GROUPS]: 'User groups and permissions',
    [COLLECTIONS.USER_SETTINGS]: 'User preferences and settings',
    [COLLECTIONS.EXHIBITIONS]: 'Exhibition events and details',
    [COLLECTIONS.EXHIBITION_EVENTS]: 'Sub-events within exhibitions',
    [COLLECTIONS.EXHIBITION_TASKS]: 'Tasks related to exhibitions',
    [COLLECTIONS.LEAD_CONTACTS]: 'Lead contact information',
    [COLLECTIONS.LEAD_SEGMENTS]: 'Lead segmentation data',
    [COLLECTIONS.LEAD_COMMUNICATIONS]: 'Communication history with leads',
    [COLLECTIONS.FINANCIALS]: 'Consolidated financial documents (budgets, expenses, purchase requests/orders)',
    [COLLECTIONS.VENDOR_PROFILES]: 'Vendor information and contracts',

    // DEPRECATED: Legacy financial collections
    [COLLECTIONS.BUDGET_ALLOCATIONS]: 'Budget planning and allocations (DEPRECATED - use financials)',
    [COLLECTIONS.EXPENSE_RECORDS]: 'Expense tracking and records (DEPRECATED - use financials)',
    [COLLECTIONS.PURCHASE_REQUESTS]: 'Purchase request submissions (DEPRECATED - use financials)',
    [COLLECTIONS.PURCHASE_ORDERS]: 'Purchase orders and approvals (DEPRECATED - use financials)',
    [COLLECTIONS.EMAIL_CAMPAIGNS]: 'Email marketing campaigns',
    [COLLECTIONS.EMAIL_TEMPLATES]: 'Email templates and designs',
    [COLLECTIONS.SOCIAL_POSTS]: 'Social media posts and campaigns',
    [COLLECTIONS.NOTIFICATIONS]: 'System notifications and alerts',
    [COLLECTIONS.AUDIT_LOGS]: 'System audit and activity logs',
    [COLLECTIONS.SECURITY_EVENTS]: 'Security events and monitoring',
    [COLLECTIONS.SUPPORT_TICKETS]: 'Customer support tickets'
  };
  
  return descriptions[collectionName] || `${collectionName} collection`;
}

/**
 * Check if tenant collections are properly initialized
 */
export async function validateTenantCollections(tenantId: string): Promise<{
  isValid: boolean;
  missingCollections: string[];
  totalCollections: number;
}> {
  const missingCollections: string[] = [];
  
  for (const collectionName of CORE_TENANT_COLLECTIONS) {
    try {
      const initDocRef = doc(db, 'tenant-data', tenantId, collectionName, `${collectionName}_init`);
      const initDoc = await initDocRef.get();
      
      if (!initDoc.exists()) {
        missingCollections.push(collectionName);
      }
    } catch (error) {
      console.error(`Error checking collection ${collectionName}:`, error);
      missingCollections.push(collectionName);
    }
  }
  
  return {
    isValid: missingCollections.length === 0,
    missingCollections,
    totalCollections: CORE_TENANT_COLLECTIONS.length
  };
}

/**
 * Repair missing tenant collections
 */
export async function repairTenantCollections(tenantId: string): Promise<{
  success: boolean;
  repairedCollections: string[];
  errors: string[];
}> {
  console.log(`🔧 Repairing collections for tenant: ${tenantId}`);
  
  const validation = await validateTenantCollections(tenantId);
  
  if (validation.isValid) {
    return {
      success: true,
      repairedCollections: [],
      errors: []
    };
  }
  
  const repairedCollections: string[] = [];
  const errors: string[] = [];
  
  for (const collectionName of validation.missingCollections) {
    try {
      await initializeOnDemandCollection(tenantId, collectionName);
      repairedCollections.push(collectionName);
    } catch (error) {
      console.error(`Failed to repair collection ${collectionName}:`, error);
      errors.push(`${collectionName}: ${error}`);
    }
  }
  
  console.log(`✅ Repaired ${repairedCollections.length} collections for tenant ${tenantId}`);
  
  return {
    success: errors.length === 0,
    repairedCollections,
    errors
  };
}
