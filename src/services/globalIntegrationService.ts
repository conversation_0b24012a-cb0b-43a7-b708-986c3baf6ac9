import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';

export interface VenueIntegration {
  id?: string;
  venueName: string;
  venueType: 'convention_center' | 'hotel' | 'exhibition_hall' | 'outdoor_venue' | 'virtual_venue';
  location: {
    country: string;
    city: string;
    address: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  contactInfo: {
    primaryContact: string;
    email: string;
    phone: string;
    website?: string;
  };
  integrationDetails: {
    apiEndpoint?: string;
    authMethod: 'api_key' | 'oauth' | 'basic_auth' | 'webhook';
    apiKey?: string;
    webhookUrl?: string;
    supportedFeatures: string[];
    dataMapping: Record<string, string>;
  };
  capabilities: {
    bookingManagement: boolean;
    floorPlanIntegration: boolean;
    capacityManagement: boolean;
    equipmentBooking: boolean;
    cateringIntegration: boolean;
    securityIntegration: boolean;
    parkingManagement: boolean;
    realTimeAvailability: boolean;
  };
  complianceInfo: {
    certifications: string[];
    safetyStandards: string[];
    accessibilityCompliance: string[];
    environmentalCertifications: string[];
  };
  status: 'active' | 'pending' | 'testing' | 'inactive';
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    lastSyncAt?: Date;
    integrationVersion: string;
  };
}

export interface APIPartnership {
  id?: string;
  partnerName: string;
  partnerType: 'exhibition_organizer' | 'venue_management' | 'payment_processor' | 'logistics' | 'marketing' | 'analytics';
  region: string;
  apiDetails: {
    baseUrl: string;
    version: string;
    authMethod: 'api_key' | 'oauth2' | 'jwt' | 'basic_auth';
    rateLimits: {
      requestsPerMinute: number;
      requestsPerHour: number;
      requestsPerDay: number;
    };
    endpoints: Array<{
      path: string;
      method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
      description: string;
      requiredPermissions: string[];
    }>;
  };
  dataExchange: {
    inboundData: string[];
    outboundData: string[];
    syncFrequency: 'real_time' | 'hourly' | 'daily' | 'weekly';
    dataFormat: 'json' | 'xml' | 'csv' | 'custom';
  };
  complianceRequirements: {
    dataProtection: string[];
    industryStandards: string[];
    regionalCompliance: string[];
  };
  status: 'active' | 'pending' | 'testing' | 'suspended';
  performance: {
    uptime: number;
    averageResponseTime: number;
    errorRate: number;
    lastHealthCheck: Date;
  };
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    lastSyncAt?: Date;
  };
}



export interface GlobalMarket {
  id?: string;
  region: string;
  countries: string[];
  marketInfo: {
    marketSize: number;
    currency: string;
    growthRate: number;
    competitorCount: number;
    marketMaturity: 'emerging' | 'developing' | 'mature';
  };
  localization: {
    languages: string[];
    currencies: string[];
    dateFormats: string[];
    timeZones: string[];
    culturalConsiderations: string[];
  };
  businessRequirements: {
    legalEntity: boolean;
    localPartnership: boolean;
    dataResidency: boolean;
    localSupport: boolean;
    complianceFrameworks: string[];
  };
  opportunities: Array<{
    opportunityType: string;
    description: string;
    estimatedValue: number;
    timeline: string;
    requirements: string[];
  }>;
  challenges: Array<{
    challengeType: string;
    description: string;
    impact: 'low' | 'medium' | 'high';
    mitigationStrategy: string;
  }>;
  status: 'researching' | 'planning' | 'entering' | 'active' | 'paused';
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    lastAnalysisDate: Date;
  };
}

export interface ComplianceFramework {
  id?: string;
  name: string;
  region: string;
  description: string;
  requirements: string[];
  complianceScore: number;
  lastAuditDate: Date;
  nextAuditDate: Date;
  certificationBody: string;
  status: 'active' | 'pending' | 'expired' | 'suspended';
  metadata: {
    createdAt: Date;
    updatedAt: Date;
  };
}

class GlobalIntegrationService {
  private venueIntegrationsCollection = 'venue_integrations';
  private apiPartnershipsCollection = 'api_partnerships';
  private complianceFrameworksCollection = 'compliance_frameworks';
  private globalMarketsCollection = 'global_markets';

  // Venue Integration Management
  async createVenueIntegration(integration: Omit<VenueIntegration, 'id' | 'metadata'>): Promise<VenueIntegration> {
    const now = new Date();
    const integrationData: Omit<VenueIntegration, 'id'> = {
      ...integration,
      metadata: {
        createdAt: now,
        updatedAt: now,
        integrationVersion: '1.0'
      }
    };

    const docRef = await addDoc(collection(db, this.venueIntegrationsCollection), {
      ...integrationData,
      'metadata.createdAt': Timestamp.fromDate(now),
      'metadata.updatedAt': Timestamp.fromDate(now),
      'metadata.lastSyncAt': integrationData.metadata.lastSyncAt ? Timestamp.fromDate(integrationData.metadata.lastSyncAt) : null
    });

    return { ...integrationData, id: docRef.id };
  }

  async getAllVenueIntegrations(): Promise<VenueIntegration[]> {
    try {
      // First try to get from Firebase
      const q = query(
        collection(db, this.venueIntegrationsCollection),
        orderBy('metadata.createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const firebaseData = querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          metadata: {
            ...data.metadata,
            createdAt: data.metadata.createdAt.toDate(),
            updatedAt: data.metadata.updatedAt.toDate(),
            lastSyncAt: data.metadata.lastSyncAt?.toDate()
          }
        } as VenueIntegration;
      });

      return firebaseData;
    } catch (error) {
      console.error('Failed to fetch venue integrations from Firebase:', error);
      return [];
    }
  }

  // API Partnership Management
  async createAPIPartnership(partnership: Omit<APIPartnership, 'id' | 'metadata'>): Promise<APIPartnership> {
    const now = new Date();
    const partnershipData: Omit<APIPartnership, 'id'> = {
      ...partnership,
      metadata: {
        createdAt: now,
        updatedAt: now
      }
    };

    const docRef = await addDoc(collection(db, this.apiPartnershipsCollection), {
      ...partnershipData,
      'performance.lastHealthCheck': Timestamp.fromDate(partnershipData.performance.lastHealthCheck),
      'metadata.createdAt': Timestamp.fromDate(now),
      'metadata.updatedAt': Timestamp.fromDate(now),
      'metadata.lastSyncAt': partnershipData.metadata.lastSyncAt ? Timestamp.fromDate(partnershipData.metadata.lastSyncAt) : null
    });

    return { ...partnershipData, id: docRef.id };
  }

  async getAllAPIPartnerships(): Promise<APIPartnership[]> {
    try {
      // First try to get from Firebase
      const q = query(
        collection(db, this.apiPartnershipsCollection),
        orderBy('metadata.createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const firebaseData = querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          performance: {
            ...data.performance,
            lastHealthCheck: data.performance.lastHealthCheck.toDate()
          },
          metadata: {
            ...data.metadata,
            createdAt: data.metadata.createdAt.toDate(),
            updatedAt: data.metadata.updatedAt.toDate(),
            lastSyncAt: data.metadata.lastSyncAt?.toDate()
          }
        } as APIPartnership;
      });

      return firebaseData;
    } catch (error) {
      console.error('Failed to fetch API partnerships from Firebase:', error);
      return [];
    }
  }

  // Compliance Framework Management
  async createComplianceFramework(framework: Omit<ComplianceFramework, 'id' | 'metadata'>): Promise<ComplianceFramework> {
    const now = new Date();
    const frameworkData: Omit<ComplianceFramework, 'id'> = {
      ...framework,
      metadata: {
        createdAt: now,
        updatedAt: now
      }
    };

    const docRef = await addDoc(collection(db, this.complianceFrameworksCollection), {
      ...frameworkData,
      lastAssessmentDate: Timestamp.fromDate(frameworkData.lastAssessmentDate),
      nextAssessmentDate: Timestamp.fromDate(frameworkData.nextAssessmentDate),
      requirements: frameworkData.requirements.map(req => ({
        ...req,
        lastAuditDate: req.lastAuditDate ? Timestamp.fromDate(req.lastAuditDate) : null,
        nextAuditDate: req.nextAuditDate ? Timestamp.fromDate(req.nextAuditDate) : null
      })),
      certifications: frameworkData.certifications.map(cert => ({
        ...cert,
        validFrom: Timestamp.fromDate(cert.validFrom),
        validUntil: Timestamp.fromDate(cert.validUntil)
      })),
      'metadata.createdAt': Timestamp.fromDate(now),
      'metadata.updatedAt': Timestamp.fromDate(now)
    });

    return { ...frameworkData, id: docRef.id };
  }

  async getComplianceFrameworksByRegion(region: string): Promise<ComplianceFramework[]> {
    const q = query(
      collection(db, this.complianceFrameworksCollection),
      where('region', '==', region),
      orderBy('metadata.createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        lastAssessmentDate: data.lastAssessmentDate.toDate(),
        nextAssessmentDate: data.nextAssessmentDate.toDate(),
        requirements: data.requirements.map((req: any) => ({
          ...req,
          lastAuditDate: req.lastAuditDate?.toDate(),
          nextAuditDate: req.nextAuditDate?.toDate()
        })),
        certifications: data.certifications.map((cert: any) => ({
          ...cert,
          validFrom: cert.validFrom.toDate(),
          validUntil: cert.validUntil.toDate()
        })),
        metadata: {
          ...data.metadata,
          createdAt: data.metadata.createdAt.toDate(),
          updatedAt: data.metadata.updatedAt.toDate()
        }
      } as ComplianceFramework;
    });
  }

  // Global Market Management
  async createGlobalMarket(market: Omit<GlobalMarket, 'id' | 'metadata'>): Promise<GlobalMarket> {
    const now = new Date();
    const marketData: Omit<GlobalMarket, 'id'> = {
      ...market,
      metadata: {
        createdAt: now,
        updatedAt: now,
        lastAnalysisDate: now
      }
    };

    const docRef = await addDoc(collection(db, this.globalMarketsCollection), {
      ...marketData,
      'metadata.createdAt': Timestamp.fromDate(now),
      'metadata.updatedAt': Timestamp.fromDate(now),
      'metadata.lastAnalysisDate': Timestamp.fromDate(now)
    });

    return { ...marketData, id: docRef.id };
  }

  async getAllGlobalMarkets(): Promise<GlobalMarket[]> {
    try {
      // First try to get from Firebase
      const q = query(
        collection(db, this.globalMarketsCollection),
        orderBy('metadata.createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const firebaseData = querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          metadata: {
            ...data.metadata,
            createdAt: data.metadata.createdAt.toDate(),
            updatedAt: data.metadata.updatedAt.toDate(),
            lastAnalysisDate: data.metadata.lastAnalysisDate.toDate()
          }
        } as GlobalMarket;
      });

      return firebaseData;
    } catch (error) {
      console.error('Failed to fetch global markets from Firebase:', error);
      return [];
    }
  }

  // Compliance Framework Management
  async getComplianceFrameworksByRegion(region: string): Promise<ComplianceFramework[]> {
    try {
      // First try to get from Firebase
      const q = query(
        collection(db, COLLECTIONS.COMPLIANCE_FRAMEWORKS),
        where('region', '==', region),
        orderBy('metadata.createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const firebaseData = querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          lastAuditDate: data.lastAuditDate.toDate(),
          nextAuditDate: data.nextAuditDate.toDate(),
          metadata: {
            ...data.metadata,
            createdAt: data.metadata.createdAt.toDate(),
            updatedAt: data.metadata.updatedAt.toDate()
          }
        } as ComplianceFramework;
      });

      return firebaseData;
    } catch (error) {
      console.error('Failed to fetch compliance frameworks from Firebase:', error);
      return [];
    }
  }

  async getAllComplianceFrameworks(): Promise<ComplianceFramework[]> {
    return this.getComplianceFrameworksByRegion('global');
  }

  // Integration Health Monitoring
  async performHealthCheck(partnershipId: string): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    responseTime: number;
    uptime: number;
    lastError?: string;
  }> {
    // Simulate health check - in real implementation, this would make actual API calls
    const mockHealthCheck = {
      status: 'healthy' as const,
      responseTime: Math.random() * 500 + 100, // 100-600ms
      uptime: 99.5 + Math.random() * 0.5, // 99.5-100%
    };

    // Update partnership performance metrics
    await this.updateAPIPartnership(partnershipId, {
      performance: {
        ...mockHealthCheck,
        errorRate: Math.random() * 0.01, // 0-1% error rate
        lastHealthCheck: new Date()
      }
    });

    return mockHealthCheck;
  }

  async updateAPIPartnership(id: string, updates: Partial<APIPartnership>): Promise<void> {
    const docRef = doc(db, this.apiPartnershipsCollection, id);
    const updateData = {
      ...updates,
      'metadata.updatedAt': Timestamp.fromDate(new Date())
    };

    if (updates.performance?.lastHealthCheck) {
      updateData['performance.lastHealthCheck'] = Timestamp.fromDate(updates.performance.lastHealthCheck);
    }

    await updateDoc(docRef, updateData);
  }

  // Analytics and Reporting
  async getGlobalIntegrationStats(): Promise<{
    totalVenueIntegrations: number;
    activeAPIPartnerships: number;
    complianceScore: number;
    globalMarketsCovered: number;
    regionBreakdown: Record<string, number>;
  }> {
    const [venues, partnerships, frameworks, markets] = await Promise.all([
      this.getAllVenueIntegrations(),
      this.getAllAPIPartnerships(),
      this.getComplianceFrameworksByRegion('global'),
      this.getAllGlobalMarkets()
    ]);

    const regionBreakdown = markets.reduce((acc, market) => {
      acc[market.region] = (acc[market.region] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const averageComplianceScore = frameworks.length > 0 
      ? frameworks.reduce((sum, f) => sum + f.complianceScore, 0) / frameworks.length 
      : 0;

    return {
      totalVenueIntegrations: venues.length,
      activeAPIPartnerships: partnerships.filter(p => p.status === 'active').length,
      complianceScore: Math.round(averageComplianceScore),
      globalMarketsCovered: markets.filter(m => m.status === 'active').length,
      regionBreakdown
    };
  }

  // Note: Mock data seeding removed as per user request - no fake data allowed

  // Data Reset for Development
  async resetAllData(): Promise<void> {
    try {
      console.log('Resetting all global integration data...');

      const collections = [
        this.venueIntegrationsCollection,
        this.apiPartnershipsCollection,
        this.globalMarketsCollection,
        'compliance_frameworks'
      ];

      for (const collectionName of collections) {
        const querySnapshot = await getDocs(collection(db, collectionName));
        const deletePromises = querySnapshot.docs.map(doc => deleteDoc(doc.ref));
        await Promise.all(deletePromises);
      }

      console.log('All global integration data reset successfully');
    } catch (error) {
      console.error('Error resetting global integration data:', error);
      throw error;
    }
  }
}

export const globalIntegrationService = new GlobalIntegrationService();
export default globalIntegrationService;
