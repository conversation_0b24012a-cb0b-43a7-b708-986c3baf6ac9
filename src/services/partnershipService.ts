import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

export interface Partnership {
  id?: string;
  name: string;
  type: 'exhibition_organizer' | 'technology_partner' | 'vendor' | 'strategic_alliance' | 'reseller';
  status: 'active' | 'pending' | 'inactive' | 'terminated';
  tier: 'platinum' | 'gold' | 'silver' | 'bronze';
  contactInfo: {
    primaryContact: string;
    email: string;
    phone?: string;
    company: string;
    website?: string;
  };
  agreement: {
    startDate: Date;
    endDate?: Date;
    renewalDate?: Date;
    contractValue?: number;
    currency: string;
    terms: string;
  };
  services: {
    provided: string[];
    received: string[];
    integrations: string[];
  };
  performance: {
    leadsGenerated: number;
    revenueGenerated: number;
    clientsReferred: number;
    satisfactionScore: number;
  };
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    lastModifiedBy: string;
  };
}

export interface PartnershipOpportunity {
  id?: string;
  partnerName: string;
  type: Partnership['type'];
  status: 'identified' | 'contacted' | 'negotiating' | 'approved' | 'rejected';
  priority: 'high' | 'medium' | 'low';
  potentialValue: number;
  description: string;
  nextAction: string;
  assignedTo: string;
  dueDate: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface PartnerIntegration {
  id?: string;
  partnershipId: string;
  integrationType: 'api' | 'webhook' | 'data_sync' | 'sso' | 'custom';
  status: 'planned' | 'in_development' | 'testing' | 'live' | 'deprecated';
  configuration: {
    apiEndpoint?: string;
    authMethod: 'api_key' | 'oauth' | 'basic_auth' | 'custom';
    dataMapping: Record<string, string>;
    syncFrequency?: 'real_time' | 'hourly' | 'daily' | 'weekly';
  };
  metrics: {
    uptime: number;
    errorRate: number;
    avgResponseTime: number;
    lastSync?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

class PartnershipService {
  private partnershipsCollection = 'partnerships';
  private opportunitiesCollection = 'partnership_opportunities';
  private integrationsCollection = 'partner_integrations';

  // Partnership CRUD operations
  async createPartnership(partnership: Omit<Partnership, 'id' | 'metadata'>): Promise<Partnership> {
    const now = new Date();
    const partnershipData: Omit<Partnership, 'id'> = {
      ...partnership,
      metadata: {
        createdAt: now,
        updatedAt: now,
        createdBy: 'system', // TODO: Get from auth context
        lastModifiedBy: 'system'
      }
    };

    const docRef = await addDoc(collection(db, this.partnershipsCollection), {
      ...partnershipData,
      'agreement.startDate': Timestamp.fromDate(partnershipData.agreement.startDate),
      'agreement.endDate': partnershipData.agreement.endDate ? Timestamp.fromDate(partnershipData.agreement.endDate) : null,
      'agreement.renewalDate': partnershipData.agreement.renewalDate ? Timestamp.fromDate(partnershipData.agreement.renewalDate) : null,
      'metadata.createdAt': Timestamp.fromDate(now),
      'metadata.updatedAt': Timestamp.fromDate(now)
    });

    return { ...partnershipData, id: docRef.id };
  }

  async getPartnership(id: string): Promise<Partnership | null> {
    const docRef = doc(db, this.partnershipsCollection, id);
    const docSnap = await getDoc(docRef);
    
    if (!docSnap.exists()) return null;
    
    const data = docSnap.data();
    return {
      id: docSnap.id,
      ...data,
      agreement: {
        ...data.agreement,
        startDate: data.agreement.startDate.toDate(),
        endDate: data.agreement.endDate?.toDate(),
        renewalDate: data.agreement.renewalDate?.toDate()
      },
      metadata: {
        ...data.metadata,
        createdAt: data.metadata.createdAt.toDate(),
        updatedAt: data.metadata.updatedAt.toDate()
      }
    } as Partnership;
  }

  async getAllPartnerships(): Promise<Partnership[]> {
    const q = query(
      collection(db, this.partnershipsCollection),
      orderBy('metadata.createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        agreement: {
          ...data.agreement,
          startDate: data.agreement.startDate.toDate(),
          endDate: data.agreement.endDate?.toDate(),
          renewalDate: data.agreement.renewalDate?.toDate()
        },
        metadata: {
          ...data.metadata,
          createdAt: data.metadata.createdAt.toDate(),
          updatedAt: data.metadata.updatedAt.toDate()
        }
      } as Partnership;
    });
  }

  async getPartnershipsByType(type: Partnership['type']): Promise<Partnership[]> {
    const q = query(
      collection(db, this.partnershipsCollection),
      where('type', '==', type),
      orderBy('metadata.createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        agreement: {
          ...data.agreement,
          startDate: data.agreement.startDate.toDate(),
          endDate: data.agreement.endDate?.toDate(),
          renewalDate: data.agreement.renewalDate?.toDate()
        },
        metadata: {
          ...data.metadata,
          createdAt: data.metadata.createdAt.toDate(),
          updatedAt: data.metadata.updatedAt.toDate()
        }
      } as Partnership;
    });
  }

  async updatePartnership(id: string, updates: Partial<Partnership>): Promise<void> {
    const docRef = doc(db, this.partnershipsCollection, id);
    const updateData = {
      ...updates,
      'metadata.updatedAt': Timestamp.fromDate(new Date()),
      'metadata.lastModifiedBy': 'system' // TODO: Get from auth context
    };

    // Handle date conversions
    if (updates.agreement?.startDate) {
      updateData['agreement.startDate'] = Timestamp.fromDate(updates.agreement.startDate);
    }
    if (updates.agreement?.endDate) {
      updateData['agreement.endDate'] = Timestamp.fromDate(updates.agreement.endDate);
    }
    if (updates.agreement?.renewalDate) {
      updateData['agreement.renewalDate'] = Timestamp.fromDate(updates.agreement.renewalDate);
    }

    await updateDoc(docRef, updateData);
  }

  async deletePartnership(id: string): Promise<void> {
    const docRef = doc(db, this.partnershipsCollection, id);
    await deleteDoc(docRef);
  }

  // Partnership Opportunities
  async createOpportunity(opportunity: Omit<PartnershipOpportunity, 'id' | 'createdAt' | 'updatedAt'>): Promise<PartnershipOpportunity> {
    const now = new Date();
    const opportunityData: Omit<PartnershipOpportunity, 'id'> = {
      ...opportunity,
      createdAt: now,
      updatedAt: now
    };

    const docRef = await addDoc(collection(db, this.opportunitiesCollection), {
      ...opportunityData,
      dueDate: Timestamp.fromDate(opportunityData.dueDate),
      createdAt: Timestamp.fromDate(now),
      updatedAt: Timestamp.fromDate(now)
    });

    return { ...opportunityData, id: docRef.id };
  }

  async getOpportunities(): Promise<PartnershipOpportunity[]> {
    const q = query(
      collection(db, this.opportunitiesCollection),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        dueDate: data.dueDate.toDate(),
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate()
      } as PartnershipOpportunity;
    });
  }

  // Partner Integrations
  async createIntegration(integration: Omit<PartnerIntegration, 'id' | 'createdAt' | 'updatedAt'>): Promise<PartnerIntegration> {
    const now = new Date();
    const integrationData: Omit<PartnerIntegration, 'id'> = {
      ...integration,
      createdAt: now,
      updatedAt: now
    };

    const docRef = await addDoc(collection(db, this.integrationsCollection), {
      ...integrationData,
      createdAt: Timestamp.fromDate(now),
      updatedAt: Timestamp.fromDate(now),
      'metrics.lastSync': integration.metrics.lastSync ? Timestamp.fromDate(integration.metrics.lastSync) : null
    });

    return { ...integrationData, id: docRef.id };
  }

  async getIntegrationsByPartnership(partnershipId: string): Promise<PartnerIntegration[]> {
    const q = query(
      collection(db, this.integrationsCollection),
      where('partnershipId', '==', partnershipId)
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
        metrics: {
          ...data.metrics,
          lastSync: data.metrics.lastSync?.toDate()
        }
      } as PartnerIntegration;
    });
  }

  // Analytics and Reporting
  async getPartnershipStats(): Promise<{
    totalPartnerships: number;
    activePartnerships: number;
    totalRevenue: number;
    topPerformers: Partnership[];
  }> {
    const partnerships = await this.getAllPartnerships();
    
    const activePartnerships = partnerships.filter(p => p.status === 'active');
    const totalRevenue = partnerships.reduce((sum, p) => sum + (p.performance.revenueGenerated || 0), 0);
    const topPerformers = partnerships
      .sort((a, b) => b.performance.revenueGenerated - a.performance.revenueGenerated)
      .slice(0, 5);

    return {
      totalPartnerships: partnerships.length,
      activePartnerships: activePartnerships.length,
      totalRevenue,
      topPerformers
    };
  }
}

export const partnershipService = new PartnershipService();
export default partnershipService;
