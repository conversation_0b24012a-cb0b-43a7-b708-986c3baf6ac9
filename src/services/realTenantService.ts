/**
 * EVEXA Real Tenant Management Service
 * 
 * Production-ready multi-tenant architecture with proper isolation
 * Handles tenant creation, user assignment, and data segregation
 */

import { 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  collection, 
  query, 
  where, 
  getDocs,
  writeBatch,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { initializeTenantCollections } from '@/services/tenantCollectionInitializer';
import type { EvexUser } from '@/types/firestore';

export interface Tenant {
  id: string;
  name: string;
  domain: string;
  status: 'active' | 'suspended' | 'trial' | 'expired';
  plan: 'free' | 'professional' | 'enterprise' | 'custom';
  settings: {
    maxUsers: number;
    maxExhibitions: number;
    features: string[];
    customBranding: boolean;
    apiAccess: boolean;
  };
  billing: {
    subscriptionId?: string;
    currentPeriodStart: Date;
    currentPeriodEnd: Date;
    trialEndsAt?: Date;
  };
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    industry?: string;
    companySize?: string;
    country?: string;
  };
}

export interface TenantUser {
  userId: string;
  tenantId: string;
  role: 'owner' | 'admin' | 'manager' | 'user';
  status: 'active' | 'invited' | 'suspended';
  permissions: string[];
  joinedAt: Date;
  invitedBy?: string;
}

/**
 * Create a new tenant with proper setup
 */
export async function createTenant(tenantData: {
  name: string;
  domain: string;
  ownerEmail: string;
  plan?: 'free' | 'professional' | 'enterprise';
  industry?: string;
  companySize?: string;
  country?: string;
}): Promise<{ success: boolean; tenant?: Tenant; error?: string }> {
  try {
    const tenantId = `tenant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Check if domain is already taken
    const domainQuery = query(
      collection(db, COLLECTIONS.TENANTS || 'tenants'),
      where('domain', '==', tenantData.domain)
    );
    const existingDomain = await getDocs(domainQuery);
    
    if (!existingDomain.empty) {
      return {
        success: false,
        error: 'Domain already exists'
      };
    }

    const tenant: Tenant = {
      id: tenantId,
      name: tenantData.name,
      domain: tenantData.domain,
      status: tenantData.plan === 'free' ? 'trial' : 'active',
      plan: tenantData.plan || 'free',
      settings: {
        maxUsers: tenantData.plan === 'enterprise' ? 1000 : tenantData.plan === 'professional' ? 100 : 10,
        maxExhibitions: tenantData.plan === 'enterprise' ? 500 : tenantData.plan === 'professional' ? 50 : 5,
        features: getTenantFeatures(tenantData.plan || 'free'),
        customBranding: tenantData.plan !== 'free',
        apiAccess: tenantData.plan === 'enterprise'
      },
      billing: {
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        trialEndsAt: tenantData.plan === 'free' ? new Date(Date.now() + 14 * 24 * 60 * 60 * 1000) : undefined
      },
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: tenantData.ownerEmail,
        industry: tenantData.industry,
        companySize: tenantData.companySize,
        country: tenantData.country
      }
    };

    // Create tenant document
    await setDoc(doc(db, COLLECTIONS.TENANTS || 'tenants', tenantId), tenant);

    // Create default tenant settings
    await createDefaultTenantSettings(tenantId);

    // Initialize tenant collections
    console.log(`🏗️ Initializing collections for tenant: ${tenantId}`);
    const collectionResult = await initializeTenantCollections(tenantId);

    if (!collectionResult.success) {
      console.warn(`⚠️ Some collections failed to initialize:`, collectionResult.errors);
    } else {
      console.log(`✅ Initialized ${collectionResult.initializedCollections.length} collections`);
    }

    console.log(`✅ Created tenant: ${tenant.name} (${tenantId})`);

    return {
      success: true,
      tenant
    };

  } catch (error: any) {
    console.error('❌ Tenant creation failed:', error);
    return {
      success: false,
      error: error.message || 'Failed to create tenant'
    };
  }
}

/**
 * Add user to tenant with specific role
 */
export async function addUserToTenant(
  userId: string,
  tenantId: string,
  role: 'owner' | 'admin' | 'manager' | 'user',
  invitedBy?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const tenantUser: TenantUser = {
      userId,
      tenantId,
      role,
      status: 'active',
      permissions: getTenantUserPermissions(role),
      joinedAt: new Date(),
      invitedBy
    };

    // Add to tenant_users collection
    await setDoc(
      doc(db, 'tenant_users', `${tenantId}_${userId}`),
      tenantUser
    );

    // Update user profile with tenant info
    await updateDoc(doc(db, COLLECTIONS.USER_PROFILES, userId), {
      tenantId,
      updatedAt: new Date()
    });

    console.log(`✅ Added user ${userId} to tenant ${tenantId} as ${role}`);
    
    return { success: true };

  } catch (error: any) {
    console.error('❌ Failed to add user to tenant:', error);
    return {
      success: false,
      error: error.message || 'Failed to add user to tenant'
    };
  }
}

/**
 * Get tenant by ID
 */
export async function getTenant(tenantId: string): Promise<Tenant | null> {
  try {
    const tenantDoc = await getDoc(doc(db, COLLECTIONS.TENANTS || 'tenants', tenantId));
    
    if (tenantDoc.exists()) {
      return tenantDoc.data() as Tenant;
    }
    
    return null;
  } catch (error) {
    console.error('❌ Failed to get tenant:', error);
    return null;
  }
}

/**
 * Get user's tenant role and permissions
 */
export async function getUserTenantRole(userId: string, tenantId: string): Promise<TenantUser | null> {
  try {
    const tenantUserDoc = await getDoc(doc(db, 'tenant_users', `${tenantId}_${userId}`));
    
    if (tenantUserDoc.exists()) {
      return tenantUserDoc.data() as TenantUser;
    }
    
    return null;
  } catch (error) {
    console.error('❌ Failed to get user tenant role:', error);
    return null;
  }
}

/**
 * Check if user has permission in tenant
 */
export async function hasPermission(
  userId: string,
  tenantId: string,
  permission: string
): Promise<boolean> {
  try {
    const tenantUser = await getUserTenantRole(userId, tenantId);
    
    if (!tenantUser || tenantUser.status !== 'active') {
      return false;
    }
    
    return tenantUser.permissions.includes(permission) || tenantUser.role === 'owner';
  } catch (error) {
    console.error('❌ Permission check failed:', error);
    return false;
  }
}

/**
 * Get features for tenant plan
 */
function getTenantFeatures(plan: string): string[] {
  const baseFeatures = ['exhibitions', 'tasks', 'basic_analytics'];
  
  switch (plan) {
    case 'professional':
      return [...baseFeatures, 'advanced_analytics', 'integrations', 'custom_reports'];
    case 'enterprise':
      return [...baseFeatures, 'advanced_analytics', 'integrations', 'custom_reports', 'api_access', 'sso', 'advanced_security'];
    default:
      return baseFeatures;
  }
}

/**
 * Get permissions for tenant user role
 */
function getTenantUserPermissions(role: string): string[] {
  const basePermissions = ['read_exhibitions', 'read_tasks'];
  
  switch (role) {
    case 'owner':
      return ['*']; // All permissions
    case 'admin':
      return [...basePermissions, 'write_exhibitions', 'write_tasks', 'manage_users', 'view_analytics'];
    case 'manager':
      return [...basePermissions, 'write_exhibitions', 'write_tasks', 'view_analytics'];
    default:
      return basePermissions;
  }
}

/**
 * Create default settings for new tenant
 */
async function createDefaultTenantSettings(tenantId: string): Promise<void> {
  const defaultSettings = {
    tenantId,
    branding: {
      primaryColor: '#3B82F6',
      secondaryColor: '#1E40AF',
      logo: null,
      companyName: ''
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: true,
      weeklyReports: true
    },
    security: {
      passwordPolicy: 'standard',
      sessionTimeout: 480, // 8 hours
      twoFactorRequired: false
    },
    integrations: {
      enabled: [],
      apiKeys: {}
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };

  await setDoc(
    doc(db, 'tenant_settings', tenantId),
    defaultSettings
  );
}

/**
 * EVEXA Development Company - Default Super Admin Tenant
 */
export const EVEXA_SUPER_ADMIN_TENANT = 'evexa-development-company';

/**
 * Create EVEXA super admin tenant (for development)
 */
export async function createEvexaSuperAdminTenant(): Promise<void> {
  const evexaTenant: Tenant = {
    id: EVEXA_SUPER_ADMIN_TENANT,
    name: 'EVEXA Development Company',
    domain: 'evexa.ai',
    status: 'active',
    plan: 'custom',
    settings: {
      maxUsers: 10000,
      maxExhibitions: 10000,
      features: ['*'], // All features
      customBranding: true,
      apiAccess: true
    },
    billing: {
      currentPeriodStart: new Date(),
      currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
    },
    metadata: {
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'system',
      industry: 'Software Development',
      companySize: '1-10',
      country: 'Global'
    }
  };

  await setDoc(doc(db, COLLECTIONS.TENANTS || 'tenants', EVEXA_SUPER_ADMIN_TENANT), evexaTenant);
  await createDefaultTenantSettings(EVEXA_SUPER_ADMIN_TENANT);
  
  console.log('✅ Created EVEXA super admin tenant');
}
