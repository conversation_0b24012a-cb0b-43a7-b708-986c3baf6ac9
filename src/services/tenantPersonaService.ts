/**
 * Tenant Persona Service
 * Manages persona templates, assignments, and permission checking for multi-tenant EVEXA
 */

import { 
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { validateTenantId } from './tenantIdHelperService';
import { 
  PersonaTemplate, 
  TenantPersona, 
  PersonaAssignment, 
  ModuleAccess,
  EvexaModule,
  PermissionAction,
  MODULE_METADATA
} from '@/types/personas';
import { 
  DEFAULT_PERSONAS, 
  getAllDefaultPersonas,
  getPersonaById,
  personaHasModuleAccess,
  getPersonaModulePermissions
} from '@/config/defaultPersonas';

// ===== TENANT PERSONA SERVICE =====

export class TenantPersonaService {
  private tenantId: string;

  constructor(tenantId: string) {
    validateTenantId(tenantId);
    this.tenantId = tenantId;
  }

  // ===== PERSONA TEMPLATE OPERATIONS =====

  /**
   * Get all personas for the tenant (default + custom)
   */
  async getAllPersonas(): Promise<TenantPersona[]> {
    const personas: TenantPersona[] = [];

    // Add default personas
    const defaultPersonas = getAllDefaultPersonas();
    personas.push(...defaultPersonas.map(persona => ({
      ...persona,
      tenantId: this.tenantId
    })));

    // Add custom personas
    const customPersonasQuery = query(
      collection(db, COLLECTIONS.TENANT_PERSONAS),
      where('tenantId', '==', this.tenantId),
      where('category', '==', 'custom'),
      orderBy('name')
    );

    const customSnapshot = await getDocs(customPersonasQuery);
    customSnapshot.docs.forEach(doc => {
      personas.push({ id: doc.id, ...doc.data() } as TenantPersona);
    });

    return personas;
  }

  /**
   * Get persona by ID
   */
  async getPersonaById(personaId: string): Promise<TenantPersona | null> {
    // Check if it's a default persona
    const defaultPersona = getPersonaById(personaId);
    if (defaultPersona) {
      return {
        ...defaultPersona,
        tenantId: this.tenantId
      };
    }

    // Check custom personas
    const personaDoc = await getDoc(doc(db, COLLECTIONS.TENANT_PERSONAS, personaId));
    if (personaDoc.exists()) {
      const data = personaDoc.data();
      if (data.tenantId === this.tenantId) {
        return { id: personaDoc.id, ...data } as TenantPersona;
      }
    }

    return null;
  }

  /**
   * Create custom persona
   */
  async createCustomPersona(
    personaData: Omit<PersonaTemplate, 'id' | 'category' | 'createdAt' | 'updatedAt'>
  ): Promise<TenantPersona> {
    const persona: Omit<TenantPersona, 'id'> = {
      ...personaData,
      tenantId: this.tenantId,
      category: 'custom',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const docRef = await addDoc(collection(db, COLLECTIONS.TENANT_PERSONAS), {
      ...persona,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return { id: docRef.id, ...persona };
  }

  /**
   * Update custom persona
   */
  async updateCustomPersona(
    personaId: string,
    updates: Partial<Omit<PersonaTemplate, 'id' | 'category' | 'tenantId' | 'createdAt'>>
  ): Promise<void> {
    const personaRef = doc(db, COLLECTIONS.TENANT_PERSONAS, personaId);
    const personaDoc = await getDoc(personaRef);

    if (!personaDoc.exists()) {
      throw new Error('Persona not found');
    }

    const data = personaDoc.data();
    if (data.tenantId !== this.tenantId) {
      throw new Error('Access denied');
    }

    if (data.category !== 'custom') {
      throw new Error('Cannot update default persona');
    }

    await updateDoc(personaRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
  }

  /**
   * Delete custom persona
   */
  async deleteCustomPersona(personaId: string): Promise<void> {
    const personaRef = doc(db, COLLECTIONS.TENANT_PERSONAS, personaId);
    const personaDoc = await getDoc(personaRef);

    if (!personaDoc.exists()) {
      throw new Error('Persona not found');
    }

    const data = personaDoc.data();
    if (data.tenantId !== this.tenantId) {
      throw new Error('Access denied');
    }

    if (data.category !== 'custom') {
      throw new Error('Cannot delete default persona');
    }

    // Check if persona is assigned to any users
    const assignmentsQuery = query(
      collection(db, COLLECTIONS.PERSONA_ASSIGNMENTS),
      where('tenantId', '==', this.tenantId),
      where('personaId', '==', personaId),
      where('isActive', '==', true)
    );

    const assignmentsSnapshot = await getDocs(assignmentsQuery);
    if (!assignmentsSnapshot.empty) {
      throw new Error('Cannot delete persona that is assigned to users');
    }

    await deleteDoc(personaRef);
  }

  // ===== PERSONA ASSIGNMENTS =====

  /**
   * Assign persona to user
   */
  async assignPersonaToUser(
    userId: string,
    personaId: string,
    assignedBy: string
  ): Promise<PersonaAssignment> {
    // Verify persona exists
    const persona = await this.getPersonaById(personaId);
    if (!persona) {
      throw new Error('Persona not found');
    }

    // Deactivate existing assignments for this user
    await this.deactivateUserPersonas(userId);

    // Create new assignment
    const assignment: Omit<PersonaAssignment, 'id'> = {
      userId,
      personaId,
      tenantId: this.tenantId,
      assignedBy,
      assignedAt: new Date(),
      isActive: true
    };

    const docRef = await addDoc(collection(db, COLLECTIONS.PERSONA_ASSIGNMENTS), {
      ...assignment,
      assignedAt: serverTimestamp()
    });

    return { id: docRef.id, ...assignment };
  }

  /**
   * Deactivate user's persona assignments
   */
  private async deactivateUserPersonas(userId: string): Promise<void> {
    // Validate inputs to prevent Firebase errors
    if (!userId || userId.trim() === '') {
      console.error('🚨 PREVENTED FIREBASE ERROR: deactivateUserPersonas called with invalid userId:', userId);
      return;
    }

    if (!this.tenantId || this.tenantId.trim() === '') {
      console.error('🚨 PREVENTED FIREBASE ERROR: deactivateUserPersonas called with invalid tenantId:', this.tenantId);
      return;
    }

    try {
      const assignmentsQuery = query(
        collection(db, COLLECTIONS.PERSONA_ASSIGNMENTS),
        where('tenantId', '==', this.tenantId),
        where('userId', '==', userId.trim()),
        where('isActive', '==', true)
      );

      const snapshot = await getDocs(assignmentsQuery);
      const batch = writeBatch(db);

      snapshot.docs.forEach(doc => {
        batch.update(doc.ref, {
          isActive: false,
          deactivatedAt: serverTimestamp()
        });
      });

      if (!snapshot.empty) {
        await batch.commit();
      }
    } catch (error) {
      console.error('🚨 Firebase error in deactivateUserPersonas:', error, { userId, tenantId: this.tenantId });
    }
  }

  /**
   * Get user's active persona
   */
  async getUserPersona(userId: string): Promise<TenantPersona | null> {
    // Validate inputs to prevent Firebase errors
    if (!userId || userId.trim() === '') {
      console.error('🚨 PREVENTED FIREBASE ERROR: getUserPersona called with invalid userId:', userId);
      return null;
    }

    if (!this.tenantId || this.tenantId.trim() === '') {
      console.error('🚨 PREVENTED FIREBASE ERROR: getUserPersona called with invalid tenantId:', this.tenantId);
      return null;
    }

    try {
      const assignmentQuery = query(
        collection(db, COLLECTIONS.PERSONA_ASSIGNMENTS),
        where('tenantId', '==', this.tenantId),
        where('userId', '==', userId.trim()),
        where('isActive', '==', true)
      );

      const assignmentSnapshot = await getDocs(assignmentQuery);
      if (assignmentSnapshot.empty) {
        return null;
      }

      const assignment = assignmentSnapshot.docs[0].data() as PersonaAssignment;
      return this.getPersonaById(assignment.personaId);
    } catch (error) {
      console.error('🚨 Firebase error in getUserPersona:', error, { userId, tenantId: this.tenantId });
      return null;
    }
  }

  /**
   * Get users assigned to persona
   */
  async getPersonaUsers(personaId: string): Promise<PersonaAssignment[]> {
    const assignmentsQuery = query(
      collection(db, COLLECTIONS.PERSONA_ASSIGNMENTS),
      where('tenantId', '==', this.tenantId),
      where('personaId', '==', personaId),
      where('isActive', '==', true),
      orderBy('assignedAt', 'desc')
    );

    const snapshot = await getDocs(assignmentsQuery);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as PersonaAssignment));
  }

  // ===== PERMISSION CHECKING =====

  /**
   * Check if user has module access
   */
  async userHasModuleAccess(
    userId: string,
    module: EvexaModule,
    action: PermissionAction
  ): Promise<boolean> {
    const persona = await this.getUserPersona(userId);
    if (!persona) {
      return false;
    }

    return personaHasModuleAccess(persona, module, action);
  }

  /**
   * Get user's module permissions
   */
  async getUserModulePermissions(
    userId: string,
    module: EvexaModule
  ): Promise<PermissionAction[]> {
    const persona = await this.getUserPersona(userId);
    if (!persona) {
      return [];
    }

    return getPersonaModulePermissions(persona, module);
  }

  /**
   * Get user's accessible modules
   */
  async getUserAccessibleModules(userId: string): Promise<ModuleAccess[]> {
    const persona = await this.getUserPersona(userId);
    if (!persona) {
      return [];
    }

    const moduleAccess: ModuleAccess[] = [];

    // Check all modules
    Object.values(MODULE_METADATA).forEach(moduleInfo => {
      const permissions = getPersonaModulePermissions(persona, moduleInfo.id);
      const hasAccess = permissions.length > 0;

      moduleAccess.push({
        module: moduleInfo.id,
        hasAccess,
        permissions,
        reason: 'persona'
      });
    });

    return moduleAccess;
  }

  /**
   * Check system permission
   */
  async userHasSystemPermission(
    userId: string,
    permission: keyof PersonaTemplate['permissions']['systemPermissions']
  ): Promise<boolean> {
    const persona = await this.getUserPersona(userId);
    if (!persona) {
      return false;
    }

    return persona.permissions.systemPermissions[permission];
  }

  // ===== BULK OPERATIONS =====

  /**
   * Initialize default personas for tenant
   */
  async initializeDefaultPersonas(): Promise<void> {
    // Default personas are handled dynamically, no need to store them
    console.log(`Default personas initialized for tenant: ${this.tenantId}`);
  }

  /**
   * Bulk assign personas to users
   */
  async bulkAssignPersonas(
    assignments: Array<{ userId: string; personaId: string }>,
    assignedBy: string
  ): Promise<PersonaAssignment[]> {
    const results: PersonaAssignment[] = [];

    for (const assignment of assignments) {
      try {
        const result = await this.assignPersonaToUser(
          assignment.userId,
          assignment.personaId,
          assignedBy
        );
        results.push(result);
      } catch (error) {
        console.error(`Failed to assign persona ${assignment.personaId} to user ${assignment.userId}:`, error);
      }
    }

    return results;
  }
}

// ===== CONVENIENCE FUNCTIONS =====

/**
 * Create tenant persona service instance
 */
export function createTenantPersonaService(tenantId: string): TenantPersonaService {
  return new TenantPersonaService(tenantId);
}

/**
 * Initialize default personas for a tenant
 */
export async function initializeDefaultPersonas(tenantId: string): Promise<void> {
  const service = createTenantPersonaService(tenantId);
  await service.initializeDefaultPersonas();
}

/**
 * Get module metadata
 */
export function getModuleMetadata(module: EvexaModule) {
  return MODULE_METADATA[module];
}

/**
 * Get all module metadata
 */
export function getAllModuleMetadata() {
  return MODULE_METADATA;
}
