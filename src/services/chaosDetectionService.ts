/**
 * EVEXA Chaos Detection Service
 * 
 * Automated monitoring system that prevents collection chaos, naming violations,
 * and unauthorized data operations. Integrates with security monitoring and
 * provides real-time alerts for data integrity violations.
 */

import { db } from '@/lib/firebase';
import { collection, getDocs, addDoc, query, where, orderBy, limit, Timestamp } from 'firebase/firestore';
import { DataIntegrityValidator, type ChaosViolation } from '@/lib/dataIntegrityValidator';
import { securityMonitoringService } from '@/services/securityMonitoringService';
import { getAllCollectionNames, getCollectionMetadata } from '@/lib/collectionSchemas';

export interface ChaosDetectionMetrics {
  totalScans: number;
  violationsDetected: number;
  criticalViolations: number;
  lastScanTime: Date;
  systemHealth: 'healthy' | 'warning' | 'critical';
  preventedChaosEvents: number;
  monitoringUptime: number; // percentage
}

export interface ChaosPreventionRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
  conditions: {
    collectionPattern?: string;
    documentPattern?: string;
    operationType?: 'create' | 'update' | 'delete';
    tenantValidation?: boolean;
    metadataValidation?: boolean;
  };
  actions: {
    block?: boolean;
    alert?: boolean;
    log?: boolean;
    notify?: string[]; // user IDs to notify
  };
}

export class ChaosDetectionService {
  private static instance: ChaosDetectionService;
  private isMonitoring = false;
  private metrics: ChaosDetectionMetrics;
  private preventionRules: ChaosPreventionRule[] = [];
  private violationHistory: ChaosViolation[] = [];
  private monitoringStartTime: Date | null = null;

  private constructor() {
    this.metrics = {
      totalScans: 0,
      violationsDetected: 0,
      criticalViolations: 0,
      lastScanTime: new Date(),
      systemHealth: 'healthy',
      preventedChaosEvents: 0,
      monitoringUptime: 0
    };

    this.initializeDefaultRules();
    this.setupChaosDetectionCallbacks();
  }

  public static getInstance(): ChaosDetectionService {
    if (!ChaosDetectionService.instance) {
      ChaosDetectionService.instance = new ChaosDetectionService();
    }
    return ChaosDetectionService.instance;
  }

  /**
   * Start comprehensive chaos detection monitoring
   */
  public async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 Chaos detection already running');
      }
      return;
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('🚨 Starting EVEXA Chaos Detection System...');
    }
    
    this.isMonitoring = true;
    this.monitoringStartTime = new Date();
    
    // Start the data integrity validator monitoring
    DataIntegrityValidator.startChaosDetection();
    
    // Log monitoring start event
    await this.logChaosEvent({
      type: 'monitoring_started',
      severity: 'low',
      description: 'Chaos detection monitoring started',
      timestamp: new Date(),
      collectionName: 'system',
      details: { startTime: this.monitoringStartTime.toISOString() }
    });

    if (process.env.NODE_ENV === 'development') {
      console.log('✅ Chaos Detection System is now active');
    }
  }

  /**
   * Stop chaos detection monitoring
   */
  public async stopMonitoring(): Promise<void> {
    if (!this.isMonitoring) {
      console.log('🔍 Chaos detection not running');
      return;
    }

    console.log('🛑 Stopping EVEXA Chaos Detection System...');
    
    this.isMonitoring = false;
    DataIntegrityValidator.stopChaosDetection();
    
    // Calculate uptime
    if (this.monitoringStartTime) {
      const uptime = Date.now() - this.monitoringStartTime.getTime();
      this.metrics.monitoringUptime = 100; // Assume 100% uptime for now
    }
    
    // Log monitoring stop event
    await this.logChaosEvent({
      type: 'monitoring_stopped',
      severity: 'low',
      description: 'Chaos detection monitoring stopped',
      timestamp: new Date(),
      collectionName: 'system',
      details: { 
        stopTime: new Date().toISOString(),
        totalScans: this.metrics.totalScans,
        violationsDetected: this.metrics.violationsDetected
      }
    });

    console.log('✅ Chaos Detection System stopped');
  }

  /**
   * Get current chaos detection metrics
   */
  public getMetrics(): ChaosDetectionMetrics {
    return { ...this.metrics };
  }

  /**
   * Get violation history
   */
  public getViolationHistory(limit = 50): ChaosViolation[] {
    return this.violationHistory
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Add custom prevention rule
   */
  public addPreventionRule(rule: Omit<ChaosPreventionRule, 'id'>): string {
    const id = `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newRule: ChaosPreventionRule = { ...rule, id };
    
    this.preventionRules.push(newRule);
    console.log(`✅ Added chaos prevention rule: ${rule.name}`);
    
    return id;
  }

  /**
   * Remove prevention rule
   */
  public removePreventionRule(ruleId: string): boolean {
    const index = this.preventionRules.findIndex(rule => rule.id === ruleId);
    if (index !== -1) {
      const rule = this.preventionRules[index];
      this.preventionRules.splice(index, 1);
      console.log(`✅ Removed chaos prevention rule: ${rule.name}`);
      return true;
    }
    return false;
  }

  /**
   * Get all prevention rules
   */
  public getPreventionRules(): ChaosPreventionRule[] {
    return [...this.preventionRules];
  }

  /**
   * Perform manual chaos detection scan
   */
  public async performManualScan(): Promise<{
    violations: ChaosViolation[];
    summary: {
      totalViolations: number;
      criticalViolations: number;
      systemHealth: 'healthy' | 'warning' | 'critical';
    };
  }> {
    console.log('🔍 Performing manual chaos detection scan...');
    
    const scanStartTime = Date.now();
    const violationsBefore = this.violationHistory.length;
    
    // Trigger a comprehensive scan
    await this.performComprehensiveScan();
    
    const violationsAfter = this.violationHistory.length;
    const newViolations = this.violationHistory.slice(violationsBefore);
    
    const criticalViolations = newViolations.filter(v => v.severity === 'critical').length;
    
    let systemHealth: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (criticalViolations > 0) {
      systemHealth = 'critical';
    } else if (newViolations.length > 0) {
      systemHealth = 'warning';
    }
    
    this.metrics.totalScans++;
    this.metrics.lastScanTime = new Date();
    this.metrics.systemHealth = systemHealth;
    
    const scanDuration = Date.now() - scanStartTime;
    console.log(`✅ Manual scan completed in ${scanDuration}ms - Found ${newViolations.length} violations`);
    
    return {
      violations: newViolations,
      summary: {
        totalViolations: newViolations.length,
        criticalViolations,
        systemHealth
      }
    };
  }

  /**
   * Initialize default chaos prevention rules
   */
  private initializeDefaultRules(): void {
    const defaultRules: Omit<ChaosPreventionRule, 'id'>[] = [
      {
        name: 'Unauthorized Collection Prevention',
        description: 'Prevents creation of collections not in standardized collection schemas',
        enabled: true,
        severity: 'critical',
        conditions: {
          operationType: 'create',
          collectionPattern: 'SCHEMA_VALIDATION' // Will be validated against schema registry
        },
        actions: {
          block: true,
          alert: true,
          log: true
        }
      },
      {
        name: 'Snake Case Naming Enforcement',
        description: 'Enforces snake_case naming convention for all collections',
        enabled: true,
        severity: 'high',
        conditions: {
          collectionPattern: '^(?![a-z][a-z0-9_]*[a-z0-9]$).*'
        },
        actions: {
          block: true,
          alert: true,
          log: true
        }
      },
      {
        name: 'Tenant ID Validation',
        description: 'Ensures all documents have proper tenant ID',
        enabled: true,
        severity: 'high',
        conditions: {
          tenantValidation: true
        },
        actions: {
          block: false,
          alert: true,
          log: true
        }
      },
      {
        name: 'Metadata Validation',
        description: 'Ensures all documents have required metadata fields',
        enabled: true,
        severity: 'medium',
        conditions: {
          metadataValidation: true
        },
        actions: {
          block: false,
          alert: true,
          log: true
        }
      }
    ];

    defaultRules.forEach(rule => {
      this.addPreventionRule(rule);
    });

    console.log(`✅ Initialized ${defaultRules.length} default chaos prevention rules`);
  }

  /**
   * Setup callbacks for chaos detection
   */
  private setupChaosDetectionCallbacks(): void {
    DataIntegrityValidator.onChaosDetected((violation: ChaosViolation) => {
      this.handleChaosViolation(violation);
    });
  }

  /**
   * Handle detected chaos violation
   */
  private async handleChaosViolation(violation: ChaosViolation): Promise<void> {
    // Add to violation history
    this.violationHistory.push(violation);
    
    // Update metrics
    this.metrics.violationsDetected++;
    if (violation.severity === 'critical') {
      this.metrics.criticalViolations++;
      this.metrics.systemHealth = 'critical';
    } else if (this.metrics.systemHealth === 'healthy') {
      this.metrics.systemHealth = 'warning';
    }
    
    // Log the violation
    await this.logChaosEvent(violation);
    
    // Apply prevention rules
    await this.applyPreventionRules(violation);
    
    console.error(`🚨 CHAOS VIOLATION: ${violation.type} - ${violation.description}`);
  }

  /**
   * Apply prevention rules to violation
   */
  private async applyPreventionRules(violation: ChaosViolation): Promise<void> {
    for (const rule of this.preventionRules) {
      if (!rule.enabled) continue;
      
      if (this.ruleMatches(rule, violation)) {
        if (rule.actions.block) {
          this.metrics.preventedChaosEvents++;
          console.log(`🛡️ BLOCKED: ${violation.description} (Rule: ${rule.name})`);
        }
        
        if (rule.actions.alert) {
          await this.sendChaosAlert(violation, rule);
        }
        
        if (rule.actions.log) {
          await this.logSecurityEvent(violation, rule);
        }
      }
    }
  }

  /**
   * Check if rule matches violation
   */
  private ruleMatches(rule: ChaosPreventionRule, violation: ChaosViolation): boolean {
    // Simple pattern matching for now
    if (rule.conditions.collectionPattern) {
      const pattern = new RegExp(rule.conditions.collectionPattern);
      return pattern.test(violation.collectionName);
    }
    
    return true; // Default to match if no specific conditions
  }

  /**
   * Send chaos alert
   */
  private async sendChaosAlert(violation: ChaosViolation, rule: ChaosPreventionRule): Promise<void> {
    console.error(`🚨 CHAOS ALERT (${rule.name}): ${violation.description}`);
    
    // In production, this would send notifications to administrators
    // For now, we'll log to security monitoring
  }

  /**
   * Log security event for chaos violation
   */
  private async logSecurityEvent(violation: ChaosViolation, rule: ChaosPreventionRule): Promise<void> {
    try {
      await securityMonitoringService.logSecurityEvent({
        id: `chaos_${Date.now()}`,
        type: 'data_integrity_violation',
        severity: violation.severity === 'critical' ? 'critical' : 'high',
        userId: 'system',
        tenantId: 'evexa-development-company',
        ipAddress: 'localhost',
        userAgent: 'EVEXA-ChaosDetection',
        details: {
          chaosViolationType: violation.type,
          description: violation.description,
          collectionName: violation.collectionName,
          preventionRule: rule.name,
          ...violation.details
        }
      });
    } catch (error) {
      console.error('Failed to log chaos violation to security:', error);
    }
  }

  /**
   * Log chaos event to Firebase
   */
  private async logChaosEvent(violation: ChaosViolation): Promise<void> {
    try {
      await addDoc(collection(db, 'chaos_detection_logs'), {
        ...violation,
        timestamp: Timestamp.fromDate(violation.timestamp)
      });
    } catch (error) {
      // Log errors only in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to log chaos event to Firebase:', error);
      }
    }
  }

  /**
   * Perform comprehensive chaos detection scan
   */
  private async performComprehensiveScan(): Promise<void> {
    // This will trigger the DataIntegrityValidator scan
    // which will call our callbacks with any violations found
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Triggering comprehensive chaos detection scan...');
    }
    
    // The actual scanning is done by DataIntegrityValidator.performChaosDetectionScan()
    // which is called automatically by the monitoring interval
  }

  /**
   * Validate collection against standardized schema
   */
  public async validateCollectionAgainstSchema(collectionName: string): Promise<{
    isValid: boolean;
    errors: string[];
    metadata?: any;
  }> {
    try {
      const authorizedCollections = getAllCollectionNames();

      if (!authorizedCollections.includes(collectionName)) {
        return {
          isValid: false,
          errors: [`Collection "${collectionName}" is not in standardized schema. Authorized collections: ${authorizedCollections.length} total.`]
        };
      }

      const metadata = getCollectionMetadata(collectionName);

      return {
        isValid: true,
        errors: [],
        metadata
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [`Schema validation failed: ${error instanceof Error ? error.message : String(error)}`]
      };
    }
  }

  /**
   * Get all authorized collection names from schema
   */
  public getAuthorizedCollections(): string[] {
    return getAllCollectionNames();
  }

  /**
   * Check if collection is authorized
   */
  public isCollectionAuthorized(collectionName: string): boolean {
    const authorizedCollections = getAllCollectionNames();
    return authorizedCollections.includes(collectionName);
  }
}

// Export singleton instance
export const chaosDetectionService = ChaosDetectionService.getInstance();
