/**
 * Data Duplication Service
 * Implements strategic data duplication for performance optimization
 * Manages duplicated fields across collections and keeps them in sync
 */

import { 
  doc,
  updateDoc,
  writeBatch,
  query,
  where,
  getDocs,
  collection,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { validateTenantId } from './tenantIdHelperService';
import type { TenantAwareEntity } from '@/types/firestore';

// ===== DUPLICATION STRATEGY CONFIGURATION =====

export interface DuplicationRule {
  sourceCollection: string;
  sourceField: string;
  targetCollections: Array<{
    collection: string;
    targetField: string;
    relationshipField: string; // Field that links to source document
    conditions?: Array<{ field: string; operator: any; value: any }>; // Additional conditions
  }>;
  description: string;
  enabled: boolean;
}

export interface DuplicationSyncResult {
  sourceCollection: string;
  sourceDocumentId: string;
  sourceField: string;
  updatedCollections: Array<{
    collection: string;
    documentsUpdated: number;
    errors: string[];
  }>;
  totalUpdated: number;
  totalErrors: number;
  duration: number;
  timestamp: Date;
}

// ===== DUPLICATION RULES CONFIGURATION =====

export const DUPLICATION_RULES: Record<string, DuplicationRule> = {
  // Exhibition data duplication
  exhibitionName: {
    sourceCollection: COLLECTIONS.EXHIBITIONS,
    sourceField: 'name',
    targetCollections: [
      {
        collection: COLLECTIONS.EXHIBITION_EVENTS,
        targetField: 'exhibitionName',
        relationshipField: 'exhibitionId'
      },
      {
        collection: COLLECTIONS.EXHIBITION_TASKS,
        targetField: 'exhibitionName',
        relationshipField: 'exhibitionId'
      },
      {
        collection: COLLECTIONS.LEAD_CONTACTS,
        targetField: 'exhibitionName',
        relationshipField: 'exhibitionId'
      },
      {
        collection: COLLECTIONS.BUDGET_ALLOCATIONS,
        targetField: 'exhibitionName',
        relationshipField: 'exhibitionId'
      },
      {
        collection: COLLECTIONS.EXPENSE_RECORDS,
        targetField: 'exhibitionName',
        relationshipField: 'exhibitionId'
      },
      // NEW: Include unified financials collection
      {
        collection: COLLECTIONS.FINANCIALS,
        targetField: 'activityName',
        relationshipField: 'activityId'
      }
    ],
    description: 'Duplicate exhibition name for faster list views and filtering',
    enabled: true
  },

  exhibitionDates: {
    sourceCollection: COLLECTIONS.EXHIBITIONS,
    sourceField: 'startDate,endDate',
    targetCollections: [
      {
        collection: COLLECTIONS.EXHIBITION_EVENTS,
        targetField: 'exhibitionStartDate,exhibitionEndDate',
        relationshipField: 'exhibitionId'
      },
      {
        collection: COLLECTIONS.EXHIBITION_TASKS,
        targetField: 'exhibitionStartDate,exhibitionEndDate',
        relationshipField: 'exhibitionId'
      }
    ],
    description: 'Duplicate exhibition dates for timeline views and deadline calculations',
    enabled: true
  },

  exhibitionLocation: {
    sourceCollection: COLLECTIONS.EXHIBITIONS,
    sourceField: 'venue,city,country',
    targetCollections: [
      {
        collection: COLLECTIONS.EXHIBITION_EVENTS,
        targetField: 'exhibitionVenue,exhibitionCity,exhibitionCountry',
        relationshipField: 'exhibitionId'
      },
      {
        collection: COLLECTIONS.LEAD_CONTACTS,
        targetField: 'exhibitionVenue,exhibitionCity,exhibitionCountry',
        relationshipField: 'exhibitionId'
      }
    ],
    description: 'Duplicate exhibition location for geographical filtering and reporting',
    enabled: true
  },

  // User data duplication
  userDisplayName: {
    sourceCollection: COLLECTIONS.USER_PROFILES,
    sourceField: 'displayName',
    targetCollections: [
      {
        collection: COLLECTIONS.EXHIBITION_TASKS,
        targetField: 'assignedToName',
        relationshipField: 'assignedTo'
      },
      {
        collection: COLLECTIONS.EXHIBITION_EVENTS,
        targetField: 'organizerName',
        relationshipField: 'organizer'
      },
      {
        collection: COLLECTIONS.EXPENSE_RECORDS,
        targetField: 'submittedByName',
        relationshipField: 'submittedBy'
      },
      {
        collection: COLLECTIONS.LEAD_CONTACTS,
        targetField: 'assignedToName',
        relationshipField: 'assignedTo'
      }
    ],
    description: 'Duplicate user display names for better UI without additional lookups',
    enabled: true
  },

  // Vendor data duplication
  vendorName: {
    sourceCollection: COLLECTIONS.VENDOR_PROFILES,
    sourceField: 'name',
    targetCollections: [
      {
        collection: COLLECTIONS.EXPENSE_RECORDS,
        targetField: 'vendorName',
        relationshipField: 'vendorId'
      },
      {
        collection: 'purchase_orders',
        targetField: 'vendorName',
        relationshipField: 'vendorId'
      }
    ],
    description: 'Duplicate vendor names for financial reporting without joins',
    enabled: true
  },

  // Lead company duplication
  leadCompany: {
    sourceCollection: COLLECTIONS.LEAD_CONTACTS,
    sourceField: 'company',
    targetCollections: [
      {
        collection: 'lead_interactions',
        targetField: 'leadCompany',
        relationshipField: 'leadId'
      },
      {
        collection: 'lead_notes',
        targetField: 'leadCompany',
        relationshipField: 'leadId'
      }
    ],
    description: 'Duplicate lead company for interaction tracking and reporting',
    enabled: true
  }
};

// ===== DATA DUPLICATION SERVICE CLASS =====

export class DataDuplicationService {
  private tenantId: string;

  constructor(tenantId: string) {
    validateTenantId(tenantId);
    this.tenantId = tenantId;
  }

  // ===== SYNC OPERATIONS =====

  /**
   * Sync duplicated data when source document is updated
   */
  async syncDuplicatedData(
    sourceCollection: string,
    sourceDocumentId: string,
    updatedFields: Record<string, any>
  ): Promise<DuplicationSyncResult[]> {
    const results: DuplicationSyncResult[] = [];

    // Find applicable duplication rules
    const applicableRules = Object.values(DUPLICATION_RULES).filter(rule => 
      rule.sourceCollection === sourceCollection && 
      rule.enabled &&
      this.hasUpdatedFields(rule.sourceField, updatedFields)
    );

    for (const rule of applicableRules) {
      const result = await this.syncRule(rule, sourceDocumentId, updatedFields);
      results.push(result);
    }

    return results;
  }

  /**
   * Sync a specific duplication rule
   */
  private async syncRule(
    rule: DuplicationRule,
    sourceDocumentId: string,
    updatedFields: Record<string, any>
  ): Promise<DuplicationSyncResult> {
    const startTime = new Date();
    
    const result: DuplicationSyncResult = {
      sourceCollection: rule.sourceCollection,
      sourceDocumentId,
      sourceField: rule.sourceField,
      updatedCollections: [],
      totalUpdated: 0,
      totalErrors: 0,
      duration: 0,
      timestamp: startTime
    };

    try {
      for (const target of rule.targetCollections) {
        const collectionResult = await this.syncTargetCollection(
          rule,
          target,
          sourceDocumentId,
          updatedFields
        );
        
        result.updatedCollections.push(collectionResult);
        result.totalUpdated += collectionResult.documentsUpdated;
        result.totalErrors += collectionResult.errors.length;
      }

    } catch (error) {
      console.error(`Error syncing rule ${rule.sourceField}:`, error);
      result.totalErrors++;
    }

    const endTime = new Date();
    result.duration = endTime.getTime() - startTime.getTime();

    return result;
  }

  /**
   * Sync data to a specific target collection
   */
  private async syncTargetCollection(
    rule: DuplicationRule,
    target: { collection: string; targetField: string; relationshipField: string; conditions?: any[] },
    sourceDocumentId: string,
    updatedFields: Record<string, any>
  ): Promise<{ collection: string; documentsUpdated: number; errors: string[] }> {
    const result = {
      collection: target.collection,
      documentsUpdated: 0,
      errors: []
    };

    try {
      // Build query to find documents to update
      let q = query(
        collection(db, target.collection),
        where('tenantId', '==', this.tenantId),
        where(target.relationshipField, '==', sourceDocumentId)
      );

      // Add additional conditions if specified
      if (target.conditions) {
        for (const condition of target.conditions) {
          q = query(q, where(condition.field, condition.operator, condition.value));
        }
      }

      const snapshot = await getDocs(q);

      if (snapshot.empty) {
        return result;
      }

      // Prepare update data
      const updateData = this.buildUpdateData(rule.sourceField, target.targetField, updatedFields);
      updateData.updatedAt = serverTimestamp();
      updateData.lastDuplicationSync = serverTimestamp();

      // Update documents in batches
      const batches = [];
      let currentBatch = writeBatch(db);
      let batchCount = 0;

      for (const docSnapshot of snapshot.docs) {
        currentBatch.update(docSnapshot.ref, updateData);
        batchCount++;
        result.documentsUpdated++;

        if (batchCount >= 500) {
          batches.push(currentBatch);
          currentBatch = writeBatch(db);
          batchCount = 0;
        }
      }

      if (batchCount > 0) {
        batches.push(currentBatch);
      }

      // Execute all batches
      for (const batch of batches) {
        await batch.commit();
      }

      console.log(`Synced ${result.documentsUpdated} documents in ${target.collection}`);

    } catch (error) {
      const errorMessage = `Error syncing ${target.collection}: ${error}`;
      console.error(errorMessage);
      result.errors.push(errorMessage);
    }

    return result;
  }

  // ===== UTILITY FUNCTIONS =====

  /**
   * Check if any of the rule's source fields were updated
   */
  private hasUpdatedFields(sourceField: string, updatedFields: Record<string, any>): boolean {
    const fields = sourceField.split(',').map(f => f.trim());
    return fields.some(field => updatedFields.hasOwnProperty(field));
  }

  /**
   * Build update data object from source fields to target fields
   */
  private buildUpdateData(
    sourceField: string,
    targetField: string,
    updatedFields: Record<string, any>
  ): Record<string, any> {
    const sourceFields = sourceField.split(',').map(f => f.trim());
    const targetFields = targetField.split(',').map(f => f.trim());
    const updateData: Record<string, any> = {};

    for (let i = 0; i < sourceFields.length && i < targetFields.length; i++) {
      const source = sourceFields[i];
      const target = targetFields[i];
      
      if (updatedFields.hasOwnProperty(source)) {
        updateData[target] = updatedFields[source];
      }
    }

    return updateData;
  }

  // ===== BULK OPERATIONS =====

  /**
   * Initialize duplicated data for existing documents
   */
  async initializeDuplicatedData(
    ruleKey: string,
    batchSize: number = 100
  ): Promise<DuplicationSyncResult> {
    const rule = DUPLICATION_RULES[ruleKey];
    if (!rule || !rule.enabled) {
      throw new Error(`Rule ${ruleKey} not found or disabled`);
    }

    const startTime = new Date();
    
    const result: DuplicationSyncResult = {
      sourceCollection: rule.sourceCollection,
      sourceDocumentId: 'bulk_initialization',
      sourceField: rule.sourceField,
      updatedCollections: [],
      totalUpdated: 0,
      totalErrors: 0,
      duration: 0,
      timestamp: startTime
    };

    try {
      // Get all source documents
      const sourceQuery = query(
        collection(db, rule.sourceCollection),
        where('tenantId', '==', this.tenantId)
      );
      
      const sourceSnapshot = await getDocs(sourceQuery);

      console.log(`Initializing duplicated data for ${sourceSnapshot.size} source documents`);

      // Process each source document
      for (const sourceDoc of sourceSnapshot.docs) {
        const sourceData = sourceDoc.data();
        const sourceFields = rule.sourceField.split(',').map(f => f.trim());
        const updatedFields: Record<string, any> = {};

        // Extract source field values
        sourceFields.forEach(field => {
          if (sourceData[field] !== undefined) {
            updatedFields[field] = sourceData[field];
          }
        });

        if (Object.keys(updatedFields).length > 0) {
          const syncResults = await this.syncDuplicatedData(
            rule.sourceCollection,
            sourceDoc.id,
            updatedFields
          );

          // Aggregate results
          syncResults.forEach(syncResult => {
            result.totalUpdated += syncResult.totalUpdated;
            result.totalErrors += syncResult.totalErrors;
          });
        }
      }

    } catch (error) {
      console.error(`Error initializing duplicated data for rule ${ruleKey}:`, error);
      result.totalErrors++;
    }

    const endTime = new Date();
    result.duration = endTime.getTime() - startTime.getTime();

    return result;
  }

  /**
   * Validate duplicated data consistency
   */
  async validateDuplicatedData(ruleKey: string): Promise<{
    consistent: boolean;
    inconsistencies: Array<{
      sourceId: string;
      targetCollection: string;
      targetId: string;
      field: string;
      sourceValue: any;
      targetValue: any;
    }>;
  }> {
    const rule = DUPLICATION_RULES[ruleKey];
    if (!rule || !rule.enabled) {
      throw new Error(`Rule ${ruleKey} not found or disabled`);
    }

    const inconsistencies: any[] = [];

    try {
      // Get all source documents
      const sourceQuery = query(
        collection(db, rule.sourceCollection),
        where('tenantId', '==', this.tenantId)
      );
      
      const sourceSnapshot = await getDocs(sourceQuery);

      for (const sourceDoc of sourceSnapshot.docs) {
        const sourceData = sourceDoc.data();
        
        // Check each target collection
        for (const target of rule.targetCollections) {
          const targetQuery = query(
            collection(db, target.collection),
            where('tenantId', '==', this.tenantId),
            where(target.relationshipField, '==', sourceDoc.id)
          );
          
          const targetSnapshot = await getDocs(targetQuery);
          
          for (const targetDoc of targetSnapshot.docs) {
            const targetData = targetDoc.data();
            
            // Compare field values
            const sourceFields = rule.sourceField.split(',').map(f => f.trim());
            const targetFields = target.targetField.split(',').map(f => f.trim());
            
            for (let i = 0; i < sourceFields.length && i < targetFields.length; i++) {
              const sourceField = sourceFields[i];
              const targetField = targetFields[i];
              
              if (sourceData[sourceField] !== targetData[targetField]) {
                inconsistencies.push({
                  sourceId: sourceDoc.id,
                  targetCollection: target.collection,
                  targetId: targetDoc.id,
                  field: targetField,
                  sourceValue: sourceData[sourceField],
                  targetValue: targetData[targetField]
                });
              }
            }
          }
        }
      }

    } catch (error) {
      console.error(`Error validating duplicated data for rule ${ruleKey}:`, error);
    }

    return {
      consistent: inconsistencies.length === 0,
      inconsistencies
    };
  }
}

// ===== CONVENIENCE FUNCTIONS =====

/**
 * Create a data duplication service instance
 */
export function createDataDuplicationService(tenantId: string): DataDuplicationService {
  return new DataDuplicationService(tenantId);
}

/**
 * Sync duplicated data for a document update
 */
export async function syncDocumentDuplication(
  tenantId: string,
  sourceCollection: string,
  sourceDocumentId: string,
  updatedFields: Record<string, any>
): Promise<DuplicationSyncResult[]> {
  const service = createDataDuplicationService(tenantId);
  return service.syncDuplicatedData(sourceCollection, sourceDocumentId, updatedFields);
}

/**
 * Get all enabled duplication rules
 */
export function getEnabledDuplicationRules(): Record<string, DuplicationRule> {
  return Object.fromEntries(
    Object.entries(DUPLICATION_RULES).filter(([_, rule]) => rule.enabled)
  );
}
