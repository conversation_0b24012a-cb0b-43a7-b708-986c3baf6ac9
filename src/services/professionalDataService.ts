/**
 * Professional Data Service
 * Secure data access with proper tenant isolation
 */

import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { validateTenantId } from './tenantIdHelperService';
import type {
  EvexUser,
  Exhibition,
  Event as EvexEvent,
  Task,
  Lead,
  Budget
} from '@/types/firestore';

/**
 * Base service class with tenant isolation
 */
abstract class BaseTenantService<T> {
  protected collectionName: string;

  constructor(collectionName: string) {
    this.collectionName = collectionName;
  }

  /**
   * Get all documents for a tenant
   */
  async getByTenant(tenantId: string): Promise<T[]> {
    try {
      const q = query(
        collection(db, this.collectionName),
        where('tenantId', '==', tenantId)
      );
      
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as T));
    } catch (error) {
      console.error(`Error getting ${this.collectionName} by tenant:`, error);
      return [];
    }
  }

  /**
   * Get document by ID (with tenant validation)
   */
  async getById(id: string, tenantId: string): Promise<T | null> {
    try {
      const docRef = doc(db, this.collectionName, id);
      const docSnap = await getDoc(docRef);
      
      if (!docSnap.exists()) {
        return null;
      }

      const data = docSnap.data() as T & { tenantId: string };
      
      // Validate tenant access
      if (data.tenantId !== tenantId) {
        console.warn(`Tenant ${tenantId} attempted to access ${this.collectionName} ${id} from tenant ${data.tenantId}`);
        return null;
      }

      return {
        id: docSnap.id,
        ...data
      } as T;
    } catch (error) {
      console.error(`Error getting ${this.collectionName} by ID:`, error);
      return null;
    }
  }

  /**
   * Create new document
   */
  async create(data: Omit<T, 'id'> & { tenantId: string }): Promise<string | null> {
    try {
      const docRef = await addDoc(collection(db, this.collectionName), {
        ...data,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      
      return docRef.id;
    } catch (error) {
      console.error(`Error creating ${this.collectionName}:`, error);
      return null;
    }
  }

  /**
   * Update document (with tenant validation)
   */
  async update(id: string, updates: Partial<T>, tenantId: string): Promise<boolean> {
    try {
      // First validate tenant access
      const existing = await this.getById(id, tenantId);
      if (!existing) {
        return false;
      }

      const docRef = doc(db, this.collectionName, id);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: Timestamp.now()
      });
      
      return true;
    } catch (error) {
      console.error(`Error updating ${this.collectionName}:`, error);
      return false;
    }
  }

  /**
   * Delete document (with tenant validation)
   */
  async delete(id: string, tenantId: string): Promise<boolean> {
    try {
      // First validate tenant access
      const existing = await this.getById(id, tenantId);
      if (!existing) {
        return false;
      }

      const docRef = doc(db, this.collectionName, id);
      await deleteDoc(docRef);
      
      return true;
    } catch (error) {
      console.error(`Error deleting ${this.collectionName}:`, error);
      return false;
    }
  }
}

/**
 * User Service
 */
class UserService extends BaseTenantService<EvexUser> {
  constructor() {
    super('user_profiles');
  }
}

/**
 * Exhibition Service - Refactored for Flat Collection Architecture
 * Uses root-level exhibitions collection with tenantId filtering
 */
class ExhibitionService extends BaseTenantService<Exhibition> {
  constructor() {
    super('exhibitions');
  }

  /**
   * Get upcoming exhibitions for tenant with optimized query
   */
  async getUpcoming(tenantId: string, limit_count: number = 10): Promise<Exhibition[]> {
    try {
      validateTenantId(tenantId);

      const q = query(
        collection(db, this.collectionName),
        where('tenantId', '==', tenantId),
        where('startDate', '>', Timestamp.now()),
        orderBy('startDate', 'asc'),
        limit(limit_count)
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Exhibition));
    } catch (error) {
      console.error('Error getting upcoming exhibitions:', error);
      return [];
    }
  }

  /**
   * Get exhibitions by status for tenant
   */
  async getByStatus(tenantId: string, status: string): Promise<Exhibition[]> {
    try {
      validateTenantId(tenantId);

      const q = query(
        collection(db, this.collectionName),
        where('tenantId', '==', tenantId),
        where('status', '==', status),
        orderBy('startDate', 'desc')
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Exhibition));
    } catch (error) {
      console.error('Error getting exhibitions by status:', error);
      return [];
    }
  }

  /**
   * Get exhibitions for date range with tenant filtering
   */
  async getByDateRange(
    tenantId: string,
    startDate: Date,
    endDate: Date
  ): Promise<Exhibition[]> {
    try {
      validateTenantId(tenantId);

      const q = query(
        collection(db, this.collectionName),
        where('tenantId', '==', tenantId),
        where('startDate', '>=', Timestamp.fromDate(startDate)),
        where('startDate', '<=', Timestamp.fromDate(endDate)),
        orderBy('startDate', 'asc')
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Exhibition));
    } catch (error) {
      console.error('Error getting exhibitions by date range:', error);
      return [];
    }
  }

  /**
   * Get exhibitions with strategic data duplication for performance
   * Includes embedded data to avoid additional queries
   */
  async getWithEmbeddedData(tenantId: string): Promise<Exhibition[]> {
    try {
      validateTenantId(tenantId);

      const exhibitions = await this.getAll(tenantId);

      // Strategic data duplication: exhibitions already contain embedded data
      // like exhibitionName in related tasks, tenantName in user records, etc.
      // This reduces the need for joins and improves query performance

      return exhibitions;
    } catch (error) {
      console.error('Error getting exhibitions with embedded data:', error);
      return [];
    }
  }
}

/**
 * Event Service
 */
class EventService extends BaseTenantService<EvexEvent> {
  constructor() {
    super('events');
  }

  async getByExhibition(exhibitionId: string, tenantId: string): Promise<EvexEvent[]> {
    try {
      const q = query(
        collection(db, this.collectionName),
        where('tenantId', '==', tenantId),
        where('exhibitionId', '==', exhibitionId),
        orderBy('startTime', 'asc')
      );
      
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as EvexEvent));
    } catch (error) {
      console.error('Error getting events by exhibition:', error);
      return [];
    }
  }
}

/**
 * Task Service - Refactored for Flat Collection Architecture
 * Uses root-level exhibition_tasks collection with tenantId filtering
 */
class TaskService extends BaseTenantService<Task> {
  constructor() {
    super('exhibition_tasks');
  }

  /**
   * Get tasks by assignee with optimized query
   */
  async getByAssignee(assigneeId: string, tenantId: string): Promise<Task[]> {
    try {
      validateTenantId(tenantId);

      const q = query(
        collection(db, this.collectionName),
        where('tenantId', '==', tenantId),
        where('assigneeId', '==', assigneeId),
        orderBy('dueDate', 'asc')
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Task));
    } catch (error) {
      console.error('Error getting tasks by assignee:', error);
      return [];
    }
  }

  /**
   * Get tasks by exhibition with tenant filtering
   */
  async getByExhibition(exhibitionId: string, tenantId: string): Promise<Task[]> {
    try {
      validateTenantId(tenantId);

      const q = query(
        collection(db, this.collectionName),
        where('tenantId', '==', tenantId),
        where('parentActivityId', '==', exhibitionId),
        where('parentActivityType', '==', 'Exhibition'),
        orderBy('dueDate', 'asc')
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Task));
    } catch (error) {
      console.error('Error getting tasks by exhibition:', error);
      return [];
    }
  }

  /**
   * Get tasks by status with tenant filtering
   */
  async getByStatus(status: string, tenantId: string): Promise<Task[]> {
    try {
      validateTenantId(tenantId);

      const q = query(
        collection(db, this.collectionName),
        where('tenantId', '==', tenantId),
        where('status', '==', status),
        orderBy('dueDate', 'asc')
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Task));
    } catch (error) {
      console.error('Error getting tasks by status:', error);
      return [];
    }
  }
}

/**
 * Lead Service
 */
class LeadService extends BaseTenantService<Lead> {
  constructor() {
    super('leads');
  }

  async getByStatus(status: string, tenantId: string): Promise<Lead[]> {
    try {
      const q = query(
        collection(db, this.collectionName),
        where('tenantId', '==', tenantId),
        where('status', '==', status),
        orderBy('createdAt', 'desc')
      );
      
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Lead));
    } catch (error) {
      console.error('Error getting leads by status:', error);
      return [];
    }
  }
}

/**
 * Budget Service
 */
class BudgetService extends BaseTenantService<Budget> {
  constructor() {
    super('budgets');
  }

  async getByExhibition(exhibitionId: string, tenantId: string): Promise<Budget[]> {
    try {
      const q = query(
        collection(db, this.collectionName),
        where('tenantId', '==', tenantId),
        where('exhibitionId', '==', exhibitionId)
      );
      
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Budget));
    } catch (error) {
      console.error('Error getting budgets by exhibition:', error);
      return [];
    }
  }
}

// Export service instances
export const professionalDataService = {
  users: new UserService(),
  exhibitions: new ExhibitionService(),
  events: new EventService(),
  tasks: new TaskService(),
  leads: new LeadService(),
  budgets: new BudgetService()
};
