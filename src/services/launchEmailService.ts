import { sendTransactionalEmail } from '@/ai/flows/send-transactional-email-flow';

export interface LaunchEmailTemplate {
  id: string;
  name: string;
  subject: string;
  content: string;
  type: 'welcome' | 'onboarding' | 'partnership' | 'product_launch' | 'webinar' | 'follow_up';
  variables: string[];
}

export interface EmailCampaignRecipient {
  email: string;
  name: string;
  company?: string;
  role?: string;
  customFields?: Record<string, any>;
}

export interface LaunchEmailCampaign {
  id: string;
  name: string;
  templateId: string;
  recipients: EmailCampaignRecipient[];
  scheduledDate?: Date;
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'paused';
  metrics: {
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
    bounced: number;
    unsubscribed: number;
  };
  createdAt: Date;
  sentAt?: Date;
}

class LaunchEmailService {
  private templates: LaunchEmailTemplate[] = [
    {
      id: 'welcome_new_client',
      name: 'Welcome New Client',
      subject: 'Welcome to EVEXA - Your Exhibition Management Journey Begins!',
      type: 'welcome',
      variables: ['clientName', 'companyName', 'loginUrl', 'supportEmail'],
      content: `
        <h1>Welcome to EVEXA, {{clientName}}!</h1>
        
        <p>We're thrilled to have {{companyName}} join the EVEXA family. You're now part of a community of forward-thinking exhibition professionals who are transforming how they manage their events.</p>
        
        <h2>What's Next?</h2>
        <ul>
          <li><strong>Access Your Dashboard:</strong> <a href="{{loginUrl}}">Login to your EVEXA account</a></li>
          <li><strong>Complete Your Setup:</strong> Follow our guided onboarding process</li>
          <li><strong>Schedule Training:</strong> Book a personalized training session with our team</li>
        </ul>
        
        <h2>Key Features to Explore:</h2>
        <ul>
          <li>📊 <strong>Analytics Dashboard:</strong> Real-time insights into your exhibition performance</li>
          <li>🤝 <strong>Lead Management:</strong> Capture, track, and convert leads efficiently</li>
          <li>📅 <strong>Event Planning:</strong> Comprehensive exhibition planning tools</li>
          <li>💰 <strong>Budget Tracking:</strong> Keep your finances on track</li>
          <li>🎯 <strong>Task Management:</strong> Never miss a deadline again</li>
        </ul>
        
        <p>Need help? Our support team is here for you at <a href="mailto:{{supportEmail}}">{{supportEmail}}</a></p>
        
        <p>Welcome aboard!</p>
        <p>The EVEXA Team</p>
      `
    },
    {
      id: 'partnership_announcement',
      name: 'Partnership Announcement',
      subject: 'Exciting Partnership News: EVEXA & {{partnerName}}',
      type: 'partnership',
      variables: ['recipientName', 'partnerName', 'partnershipDetails', 'benefitsUrl'],
      content: `
        <h1>Exciting Partnership News!</h1>
        
        <p>Dear {{recipientName}},</p>
        
        <p>We're excited to announce our strategic partnership with {{partnerName}}! This collaboration brings you enhanced capabilities and exclusive benefits.</p>
        
        <h2>What This Means for You:</h2>
        <p>{{partnershipDetails}}</p>
        
        <h2>Exclusive Benefits:</h2>
        <ul>
          <li>🎯 Enhanced integration capabilities</li>
          <li>💰 Special pricing on partner services</li>
          <li>🚀 Early access to new features</li>
          <li>📞 Priority support</li>
        </ul>
        
        <p><a href="{{benefitsUrl}}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">Learn More About Benefits</a></p>
        
        <p>Questions? Reply to this email or contact our partnership team.</p>
        
        <p>Best regards,<br>The EVEXA Team</p>
      `
    },
    {
      id: 'product_launch',
      name: 'Product Launch Announcement',
      subject: '🚀 Introducing {{featureName}} - Transform Your Exhibition Management',
      type: 'product_launch',
      variables: ['recipientName', 'featureName', 'featureDescription', 'demoUrl', 'launchDate'],
      content: `
        <h1>🚀 Introducing {{featureName}}</h1>
        
        <p>Hi {{recipientName}},</p>
        
        <p>We're thrilled to announce the launch of {{featureName}}, our latest innovation designed to revolutionize your exhibition management experience!</p>
        
        <h2>What's New:</h2>
        <p>{{featureDescription}}</p>
        
        <h2>Key Benefits:</h2>
        <ul>
          <li>⚡ Increased efficiency and productivity</li>
          <li>📊 Better insights and analytics</li>
          <li>🎯 Improved lead conversion rates</li>
          <li>💰 Cost savings and ROI optimization</li>
        </ul>
        
        <h2>Available Now:</h2>
        <p>{{featureName}} is now live in your EVEXA dashboard. Available from {{launchDate}}.</p>
        
        <p><a href="{{demoUrl}}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">Watch Demo</a></p>
        
        <p>Ready to get started? The feature is already enabled in your account!</p>
        
        <p>Happy exhibiting!<br>The EVEXA Team</p>
      `
    },
    {
      id: 'webinar_invitation',
      name: 'Webinar Invitation',
      subject: '📅 Join Our Exclusive Webinar: {{webinarTitle}}',
      type: 'webinar',
      variables: ['recipientName', 'webinarTitle', 'webinarDate', 'webinarTime', 'registrationUrl', 'agenda'],
      content: `
        <h1>You're Invited to Our Exclusive Webinar!</h1>
        
        <p>Dear {{recipientName}},</p>
        
        <p>Join us for an exclusive webinar: <strong>{{webinarTitle}}</strong></p>
        
        <h2>Event Details:</h2>
        <ul>
          <li>📅 <strong>Date:</strong> {{webinarDate}}</li>
          <li>🕐 <strong>Time:</strong> {{webinarTime}}</li>
          <li>💻 <strong>Format:</strong> Live Online Webinar</li>
          <li>🎯 <strong>Duration:</strong> 60 minutes + Q&A</li>
        </ul>
        
        <h2>What You'll Learn:</h2>
        <p>{{agenda}}</p>
        
        <h2>Who Should Attend:</h2>
        <ul>
          <li>Exhibition managers and coordinators</li>
          <li>Event marketing professionals</li>
          <li>Business development teams</li>
          <li>C-level executives in exhibition companies</li>
        </ul>
        
        <p><a href="{{registrationUrl}}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">Register Now - FREE</a></p>
        
        <p>Spaces are limited, so secure your spot today!</p>
        
        <p>See you there!<br>The EVEXA Team</p>
      `
    },
    {
      id: 'follow_up_demo',
      name: 'Demo Follow-up',
      subject: 'Thanks for Your Interest in EVEXA - Next Steps',
      type: 'follow_up',
      variables: ['recipientName', 'companyName', 'demoDate', 'salesRepName', 'proposalUrl', 'calendlyUrl'],
      content: `
        <h1>Thank You for Your Interest in EVEXA!</h1>
        
        <p>Hi {{recipientName}},</p>
        
        <p>Thank you for taking the time to explore EVEXA with us on {{demoDate}}. It was great learning about {{companyName}}'s exhibition management needs.</p>
        
        <h2>What We Discussed:</h2>
        <ul>
          <li>Your current exhibition management challenges</li>
          <li>How EVEXA can streamline your processes</li>
          <li>Potential ROI and efficiency gains</li>
          <li>Implementation timeline and support</li>
        </ul>
        
        <h2>Next Steps:</h2>
        <ol>
          <li><strong>Review Your Proposal:</strong> <a href="{{proposalUrl}}">Access your customized proposal</a></li>
          <li><strong>Schedule Follow-up:</strong> <a href="{{calendlyUrl}}">Book a call with {{salesRepName}}</a></li>
          <li><strong>Start Your Trial:</strong> Begin with our 30-day free trial</li>
        </ol>
        
        <h2>Questions?</h2>
        <p>Don't hesitate to reach out to {{salesRepName}} directly or reply to this email.</p>
        
        <p>We're excited about the possibility of partnering with {{companyName}} to transform your exhibition management!</p>
        
        <p>Best regards,<br>{{salesRepName}}<br>EVEXA Sales Team</p>
      `
    }
  ];

  async getTemplates(): Promise<LaunchEmailTemplate[]> {
    return this.templates;
  }

  async getTemplate(id: string): Promise<LaunchEmailTemplate | null> {
    return this.templates.find(t => t.id === id) || null;
  }

  async sendWelcomeEmail(recipient: EmailCampaignRecipient, variables: Record<string, string>): Promise<boolean> {
    const template = await this.getTemplate('welcome_new_client');
    if (!template) return false;

    try {
      const result = await sendTransactionalEmail({
        recipientEmail: recipient.email,
        subject: this.replaceVariables(template.subject, variables),
        body: this.replaceVariables(template.content, variables),
        templateId: 'welcome_new_client',
        metadata: {
          recipientId: recipient.id,
          templateType: 'welcome'
        }
      });

      return result.success;
    } catch (error) {
      console.error('Failed to send welcome email:', error);
      return false;
    }
  }

  async sendPartnershipAnnouncement(recipients: EmailCampaignRecipient[], variables: Record<string, string>): Promise<{ sent: number; failed: number }> {
    const template = await this.getTemplate('partnership_announcement');
    if (!template) return { sent: 0, failed: recipients.length };

    let sent = 0;
    let failed = 0;

    for (const recipient of recipients) {
      try {
        const personalizedVariables = {
          ...variables,
          recipientName: recipient.name
        };

        const result = await sendTransactionalEmail({
          to: recipient.email,
          subject: this.replaceVariables(template.subject, personalizedVariables),
          htmlContent: this.replaceVariables(template.content, personalizedVariables),
          templateType: 'partnership'
        });

        if (result.success) {
          sent++;
        } else {
          failed++;
        }
      } catch (error) {
        console.error(`Failed to send partnership email to ${recipient.email}:`, error);
        failed++;
      }
    }

    return { sent, failed };
  }

  async sendProductLaunchEmail(recipients: EmailCampaignRecipient[], variables: Record<string, string>): Promise<{ sent: number; failed: number }> {
    const template = await this.getTemplate('product_launch');
    if (!template) return { sent: 0, failed: recipients.length };

    let sent = 0;
    let failed = 0;

    for (const recipient of recipients) {
      try {
        const personalizedVariables = {
          ...variables,
          recipientName: recipient.name
        };

        const result = await sendTransactionalEmail({
          to: recipient.email,
          subject: this.replaceVariables(template.subject, personalizedVariables),
          htmlContent: this.replaceVariables(template.content, personalizedVariables),
          templateType: 'product_launch'
        });

        if (result.success) {
          sent++;
        } else {
          failed++;
        }
      } catch (error) {
        console.error(`Failed to send product launch email to ${recipient.email}:`, error);
        failed++;
      }
    }

    return { sent, failed };
  }

  async sendWebinarInvitation(recipients: EmailCampaignRecipient[], variables: Record<string, string>): Promise<{ sent: number; failed: number }> {
    const template = await this.getTemplate('webinar_invitation');
    if (!template) return { sent: 0, failed: recipients.length };

    let sent = 0;
    let failed = 0;

    for (const recipient of recipients) {
      try {
        const personalizedVariables = {
          ...variables,
          recipientName: recipient.name
        };

        const result = await sendTransactionalEmail({
          to: recipient.email,
          subject: this.replaceVariables(template.subject, personalizedVariables),
          htmlContent: this.replaceVariables(template.content, personalizedVariables),
          templateType: 'webinar'
        });

        if (result.success) {
          sent++;
        } else {
          failed++;
        }
      } catch (error) {
        console.error(`Failed to send webinar invitation to ${recipient.email}:`, error);
        failed++;
      }
    }

    return { sent, failed };
  }

  async sendFollowUpEmail(recipient: EmailCampaignRecipient, variables: Record<string, string>): Promise<boolean> {
    const template = await this.getTemplate('follow_up_demo');
    if (!template) return false;

    try {
      const personalizedVariables = {
        ...variables,
        recipientName: recipient.name,
        companyName: recipient.company || 'your company'
      };

      const result = await sendTransactionalEmail({
        to: recipient.email,
        subject: this.replaceVariables(template.subject, personalizedVariables),
        htmlContent: this.replaceVariables(template.content, personalizedVariables),
        templateType: 'follow_up'
      });

      return result.success;
    } catch (error) {
      console.error('Failed to send follow-up email:', error);
      return false;
    }
  }

  private replaceVariables(content: string, variables: Record<string, string>): string {
    let result = content;
    
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value);
    });
    
    return result;
  }

  // Automated email sequences
  async startOnboardingSequence(recipient: EmailCampaignRecipient, companyInfo: Record<string, string>): Promise<void> {
    // Day 0: Welcome email
    await this.sendWelcomeEmail(recipient, {
      clientName: recipient.name,
      companyName: recipient.company || 'your company',
      loginUrl: `${process.env.NEXT_PUBLIC_APP_URL}/login`,
      supportEmail: '<EMAIL>'
    });

    // Schedule follow-up emails (in a real implementation, this would use a job queue)
    console.log('Onboarding sequence started for:', recipient.email);
  }

  async startLeadNurturingSequence(recipient: EmailCampaignRecipient, leadInfo: Record<string, string>): Promise<void> {
    // This would typically be handled by a marketing automation platform
    // For now, we'll just send a follow-up email
    await this.sendFollowUpEmail(recipient, {
      ...leadInfo,
      salesRepName: 'Sarah Johnson',
      demoDate: new Date().toLocaleDateString(),
      proposalUrl: `${process.env.NEXT_PUBLIC_APP_URL}/proposal/${recipient.email}`,
      calendlyUrl: 'https://calendly.com/evexa-sales'
    });

    console.log('Lead nurturing sequence started for:', recipient.email);
  }
}

export const launchEmailService = new LaunchEmailService();
export default launchEmailService;
