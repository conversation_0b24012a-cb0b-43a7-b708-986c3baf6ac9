/**
 * Professional Authentication Service
 * Clean, secure authentication with proper tenant isolation
 */

import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser,
  updateProfile
} from 'firebase/auth';
import { 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  Timestamp,
  collection,
  query,
  where,
  getDocs
} from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import type { EvexUser, UserRoleType } from '@/types/firestore';

// Professional authentication service - uses real Firebase Auth
// No hardcoded user data - all users are managed through Firebase Auth and Firestore

export interface AuthResult {
  success: boolean;
  user?: EvexUser;
  error?: string;
}

/**
 * Get or create user profile from Firebase user
 */
async function getOrCreateUserProfile(firebaseUser: FirebaseUser): Promise<EvexUser | null> {
  try {
    const email = firebaseUser.email!;
    const userDocRef = doc(db, 'user_profiles', firebaseUser.uid);
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists()) {
      // Update last login
      await updateDoc(userDocRef, {
        lastLoginAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });

      return {
        id: firebaseUser.uid,
        ...userDoc.data()
      } as EvexUser;
    }

    // Create new user profile with default values
    const newUserProfile: Omit<EvexUser, 'id'> = {
      email,
      displayName: firebaseUser.displayName || email.split('@')[0],
      role: 'user', // Default role - will be updated by admin
      tenantId: 'default-tenant', // Will be updated during tenant assignment
      status: 'active',
      department: 'General',
      jobTitle: 'Team Member',
      profileImageUrl: firebaseUser.photoURL || '',
      phone: '',
      isEmailVerified: firebaseUser.emailVerified,
      groupIds: [],
      workloadCapacityType: 'taskCount',
      workloadCapacityValue: 20,
      preferences: {
        theme: 'system',
        notifications: true,
        language: 'en'
      },
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      lastLoginAt: Timestamp.now()
    };

    // Use Firebase user ID
    const userId = firebaseUser.uid;
    const finalUserDocRef = doc(db, 'user_profiles', userId);
    
    await setDoc(finalUserDocRef, newUserProfile);

    return {
      id: userId,
      ...newUserProfile
    };

  } catch (error) {
    console.error('Error getting/creating user profile:', error);
    return null;
  }
}

/**
 * Sign in with email and password
 */
export async function signInWithEmail(email: string, password: string): Promise<AuthResult> {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const userProfile = await getOrCreateUserProfile(userCredential.user);
    
    if (!userProfile) {
      return { success: false, error: 'Failed to load user profile' };
    }
    
    return { success: true, user: userProfile };
  } catch (error: any) {
    console.error('Sign in error:', error);
    return { 
      success: false, 
      error: getAuthErrorMessage(error.code) 
    };
  }
}

/**
 * Sign up with email and password
 */
export async function signUpWithEmail(
  email: string, 
  password: string, 
  displayName: string
): Promise<AuthResult> {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    
    // Update display name
    await updateProfile(userCredential.user, { displayName });
    
    const userProfile = await getOrCreateUserProfile(userCredential.user);
    
    if (!userProfile) {
      return { success: false, error: 'Failed to create user profile' };
    }
    
    return { success: true, user: userProfile };
  } catch (error: any) {
    console.error('Sign up error:', error);
    return { 
      success: false, 
      error: getAuthErrorMessage(error.code) 
    };
  }
}

/**
 * Sign out user
 */
export async function signOutUser(): Promise<void> {
  await signOut(auth);
}

/**
 * Auth state observer
 */
export function onAuthStateChange(callback: (user: EvexUser | null) => void) {
  return onAuthStateChanged(auth, async (firebaseUser) => {
    if (firebaseUser) {
      const userProfile = await getOrCreateUserProfile(firebaseUser);
      callback(userProfile);
    } else {
      callback(null);
    }
  });
}

/**
 * Get user by tenant (for tenant isolation)
 */
export async function getUsersByTenant(tenantId: string): Promise<EvexUser[]> {
  try {
    const usersQuery = query(
      collection(db, 'user_profiles'),
      where('tenantId', '==', tenantId)
    );
    
    const snapshot = await getDocs(usersQuery);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as EvexUser));
  } catch (error) {
    console.error('Error getting users by tenant:', error);
    return [];
  }
}

/**
 * Convert Firebase auth error codes to user-friendly messages
 */
function getAuthErrorMessage(errorCode: string): string {
  switch (errorCode) {
    case 'auth/user-not-found':
      return 'No account found with this email address.';
    case 'auth/wrong-password':
      return 'Incorrect password.';
    case 'auth/email-already-in-use':
      return 'An account with this email already exists.';
    case 'auth/weak-password':
      return 'Password should be at least 6 characters.';
    case 'auth/invalid-email':
      return 'Invalid email address.';
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Please try again later.';
    default:
      return 'Authentication failed. Please try again.';
  }
}
