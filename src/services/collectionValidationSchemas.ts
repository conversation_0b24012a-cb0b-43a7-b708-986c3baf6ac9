/**
 * Collection Validation Schemas
 * Defines validation rules for all EVEXA collections
 */

import { COLLECTIONS } from '@/lib/collections';
import type { 
  CollectionValidationSchema, 
  ValidationRule, 
  ValidationResult 
} from './dataValidationLayer';

// ===== COMMON VALIDATION RULES =====

const COMMON_RULES = {
  tenantId: {
    field: 'tenantId',
    required: true,
    type: 'string' as const,
    minLength: 3,
    maxLength: 100
  },
  id: {
    field: 'id',
    required: false,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100
  },
  createdAt: {
    field: 'createdAt',
    required: false,
    type: 'date' as const
  },
  updatedAt: {
    field: 'updatedAt',
    required: false,
    type: 'date' as const
  }
};

// ===== CUSTOM VALIDATORS =====

const CUSTOM_VALIDATORS = {
  emailFormat: (value: any): ValidationResult => {
    const result = { valid: true, errors: [], warnings: [] };
    
    if (typeof value === 'string') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        result.valid = false;
        result.errors.push({
          field: 'email',
          code: 'INVALID_EMAIL_FORMAT',
          message: 'Invalid email format',
          severity: 'error' as const
        });
      }
    }
    
    return result;
  },

  dateRange: (startDate: any, endDate: any): ValidationResult => {
    const result = { valid: true, errors: [], warnings: [] };
    
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (start >= end) {
        result.valid = false;
        result.errors.push({
          field: 'dateRange',
          code: 'INVALID_DATE_RANGE',
          message: 'Start date must be before end date',
          severity: 'error' as const
        });
      }
    }
    
    return result;
  },

  positiveAmount: (value: any): ValidationResult => {
    const result = { valid: true, errors: [], warnings: [] };
    
    if (typeof value === 'number' && value <= 0) {
      result.valid = false;
      result.errors.push({
        field: 'amount',
        code: 'INVALID_AMOUNT',
        message: 'Amount must be positive',
        severity: 'error' as const
      });
    }
    
    return result;
  }
};

// ===== COLLECTION SCHEMAS =====

export const USER_PROFILES_SCHEMA: CollectionValidationSchema = {
  collectionName: COLLECTIONS.USER_PROFILES,
  requireTenantId: true,
  rules: [
    COMMON_RULES.tenantId,
    COMMON_RULES.id,
    {
      field: 'email',
      required: true,
      type: 'string',
      minLength: 5,
      maxLength: 255,
      customValidator: CUSTOM_VALIDATORS.emailFormat
    },
    {
      field: 'displayName',
      required: true,
      type: 'string',
      minLength: 1,
      maxLength: 100
    },
    {
      field: 'role',
      required: true,
      type: 'string',
      customValidator: (value: any) => {
        const validRoles = ['super_admin', 'admin', 'management', 'user'];
        const result = { valid: true, errors: [], warnings: [] };
        
        if (!validRoles.includes(value)) {
          result.valid = false;
          result.errors.push({
            field: 'role',
            code: 'INVALID_ROLE',
            message: `Role must be one of: ${validRoles.join(', ')}`,
            severity: 'error' as const
          });
        }
        
        return result;
      }
    },
    {
      field: 'status',
      required: true,
      type: 'string',
      customValidator: (value: any) => {
        const validStatuses = ['active', 'inactive', 'suspended'];
        const result = { valid: true, errors: [], warnings: [] };
        
        if (!validStatuses.includes(value)) {
          result.valid = false;
          result.errors.push({
            field: 'status',
            code: 'INVALID_STATUS',
            message: `Status must be one of: ${validStatuses.join(', ')}`,
            severity: 'error' as const
          });
        }
        
        return result;
      }
    },
    COMMON_RULES.createdAt,
    COMMON_RULES.updatedAt
  ]
};

export const EXHIBITIONS_SCHEMA: CollectionValidationSchema = {
  collectionName: COLLECTIONS.EXHIBITIONS,
  requireTenantId: true,
  rules: [
    COMMON_RULES.tenantId,
    COMMON_RULES.id,
    {
      field: 'name',
      required: true,
      type: 'string',
      minLength: 1,
      maxLength: 200
    },
    {
      field: 'venue',
      required: true,
      type: 'string',
      minLength: 1,
      maxLength: 200
    },
    {
      field: 'city',
      required: true,
      type: 'string',
      minLength: 1,
      maxLength: 100
    },
    {
      field: 'country',
      required: true,
      type: 'string',
      minLength: 2,
      maxLength: 100
    },
    {
      field: 'startDate',
      required: true,
      type: 'date'
    },
    {
      field: 'endDate',
      required: true,
      type: 'date'
    },
    {
      field: 'status',
      required: true,
      type: 'string',
      customValidator: (value: any) => {
        const validStatuses = ['planning', 'confirmed', 'active', 'completed', 'cancelled'];
        const result = { valid: true, errors: [], warnings: [] };
        
        if (!validStatuses.includes(value)) {
          result.valid = false;
          result.errors.push({
            field: 'status',
            code: 'INVALID_STATUS',
            message: `Status must be one of: ${validStatuses.join(', ')}`,
            severity: 'error' as const
          });
        }
        
        return result;
      }
    },
    COMMON_RULES.createdAt,
    COMMON_RULES.updatedAt
  ],
  customValidators: [
    (document: any) => CUSTOM_VALIDATORS.dateRange(document.startDate, document.endDate)
  ]
};

export const EXHIBITION_EVENTS_SCHEMA: CollectionValidationSchema = {
  collectionName: COLLECTIONS.EXHIBITION_EVENTS,
  requireTenantId: true,
  rules: [
    COMMON_RULES.tenantId,
    COMMON_RULES.id,
    {
      field: 'name',
      required: true,
      type: 'string',
      minLength: 1,
      maxLength: 200
    },
    {
      field: 'exhibitionId',
      required: true,
      type: 'string',
      minLength: 1,
      maxLength: 100
    },
    {
      field: 'startDate',
      required: true,
      type: 'date'
    },
    {
      field: 'endDate',
      required: false,
      type: 'date'
    },
    {
      field: 'status',
      required: true,
      type: 'string',
      customValidator: (value: any) => {
        const validStatuses = ['planned', 'confirmed', 'active', 'completed', 'cancelled'];
        const result = { valid: true, errors: [], warnings: [] };
        
        if (!validStatuses.includes(value)) {
          result.valid = false;
          result.errors.push({
            field: 'status',
            code: 'INVALID_STATUS',
            message: `Status must be one of: ${validStatuses.join(', ')}`,
            severity: 'error' as const
          });
        }
        
        return result;
      }
    },
    COMMON_RULES.createdAt,
    COMMON_RULES.updatedAt
  ]
};

export const EXHIBITION_TASKS_SCHEMA: CollectionValidationSchema = {
  collectionName: COLLECTIONS.EXHIBITION_TASKS,
  requireTenantId: true,
  rules: [
    COMMON_RULES.tenantId,
    COMMON_RULES.id,
    {
      field: 'title',
      required: true,
      type: 'string',
      minLength: 1,
      maxLength: 200
    },
    {
      field: 'status',
      required: true,
      type: 'string',
      customValidator: (value: any) => {
        const validStatuses = ['pending', 'in_progress', 'completed', 'cancelled'];
        const result = { valid: true, errors: [], warnings: [] };
        
        if (!validStatuses.includes(value)) {
          result.valid = false;
          result.errors.push({
            field: 'status',
            code: 'INVALID_STATUS',
            message: `Status must be one of: ${validStatuses.join(', ')}`,
            severity: 'error' as const
          });
        }
        
        return result;
      }
    },
    {
      field: 'priority',
      required: false,
      type: 'string',
      customValidator: (value: any) => {
        if (!value) return { valid: true, errors: [], warnings: [] };
        
        const validPriorities = ['low', 'medium', 'high', 'urgent'];
        const result = { valid: true, errors: [], warnings: [] };
        
        if (!validPriorities.includes(value)) {
          result.valid = false;
          result.errors.push({
            field: 'priority',
            code: 'INVALID_PRIORITY',
            message: `Priority must be one of: ${validPriorities.join(', ')}`,
            severity: 'error' as const
          });
        }
        
        return result;
      }
    },
    COMMON_RULES.createdAt,
    COMMON_RULES.updatedAt
  ]
};

export const LEAD_CONTACTS_SCHEMA: CollectionValidationSchema = {
  collectionName: COLLECTIONS.LEAD_CONTACTS,
  requireTenantId: true,
  rules: [
    COMMON_RULES.tenantId,
    COMMON_RULES.id,
    {
      field: 'email',
      required: true,
      type: 'string',
      minLength: 5,
      maxLength: 255,
      customValidator: CUSTOM_VALIDATORS.emailFormat
    },
    {
      field: 'firstName',
      required: false,
      type: 'string',
      maxLength: 100
    },
    {
      field: 'lastName',
      required: false,
      type: 'string',
      maxLength: 100
    },
    {
      field: 'company',
      required: false,
      type: 'string',
      maxLength: 200
    },
    {
      field: 'status',
      required: true,
      type: 'string',
      customValidator: (value: any) => {
        const validStatuses = ['new', 'contacted', 'qualified', 'converted', 'lost'];
        const result = { valid: true, errors: [], warnings: [] };
        
        if (!validStatuses.includes(value)) {
          result.valid = false;
          result.errors.push({
            field: 'status',
            code: 'INVALID_STATUS',
            message: `Status must be one of: ${validStatuses.join(', ')}`,
            severity: 'error' as const
          });
        }
        
        return result;
      }
    },
    {
      field: 'leadScore',
      required: false,
      type: 'number',
      customValidator: (value: any) => {
        const result = { valid: true, errors: [], warnings: [] };
        
        if (typeof value === 'number' && (value < 0 || value > 100)) {
          result.valid = false;
          result.errors.push({
            field: 'leadScore',
            code: 'INVALID_LEAD_SCORE',
            message: 'Lead score must be between 0 and 100',
            severity: 'error' as const
          });
        }
        
        return result;
      }
    },
    COMMON_RULES.createdAt,
    COMMON_RULES.updatedAt
  ]
};

export const BUDGET_ALLOCATIONS_SCHEMA: CollectionValidationSchema = {
  collectionName: COLLECTIONS.BUDGET_ALLOCATIONS,
  requireTenantId: true,
  rules: [
    COMMON_RULES.tenantId,
    COMMON_RULES.id,
    {
      field: 'activityId',
      required: true,
      type: 'string',
      minLength: 1,
      maxLength: 100
    },
    {
      field: 'amount',
      required: true,
      type: 'number',
      customValidator: CUSTOM_VALIDATORS.positiveAmount
    },
    {
      field: 'currency',
      required: true,
      type: 'string',
      minLength: 3,
      maxLength: 3,
      customValidator: (value: any) => {
        const validCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'JPY', 'AUD'];
        const result = { valid: true, errors: [], warnings: [] };
        
        if (!validCurrencies.includes(value)) {
          result.warnings.push({
            field: 'currency',
            code: 'UNCOMMON_CURRENCY',
            message: `Currency ${value} is not commonly used`,
            suggestion: `Consider using one of: ${validCurrencies.join(', ')}`
          });
        }
        
        return result;
      }
    },
    {
      field: 'category',
      required: true,
      type: 'string',
      minLength: 1,
      maxLength: 100
    },
    COMMON_RULES.createdAt,
    COMMON_RULES.updatedAt
  ]
};

export const EXPENSE_RECORDS_SCHEMA: CollectionValidationSchema = {
  collectionName: COLLECTIONS.EXPENSE_RECORDS,
  requireTenantId: true,
  rules: [
    COMMON_RULES.tenantId,
    COMMON_RULES.id,
    {
      field: 'activityId',
      required: true,
      type: 'string',
      minLength: 1,
      maxLength: 100
    },
    {
      field: 'amount',
      required: true,
      type: 'number',
      customValidator: CUSTOM_VALIDATORS.positiveAmount
    },
    {
      field: 'currency',
      required: true,
      type: 'string',
      minLength: 3,
      maxLength: 3
    },
    {
      field: 'description',
      required: true,
      type: 'string',
      minLength: 1,
      maxLength: 500
    },
    {
      field: 'status',
      required: true,
      type: 'string',
      customValidator: (value: any) => {
        const validStatuses = ['pending', 'approved', 'rejected', 'paid'];
        const result = { valid: true, errors: [], warnings: [] };
        
        if (!validStatuses.includes(value)) {
          result.valid = false;
          result.errors.push({
            field: 'status',
            code: 'INVALID_STATUS',
            message: `Status must be one of: ${validStatuses.join(', ')}`,
            severity: 'error' as const
          });
        }
        
        return result;
      }
    },
    COMMON_RULES.createdAt,
    COMMON_RULES.updatedAt
  ]
};

export const VENDOR_PROFILES_SCHEMA: CollectionValidationSchema = {
  collectionName: COLLECTIONS.VENDOR_PROFILES,
  requireTenantId: true,
  rules: [
    COMMON_RULES.tenantId,
    COMMON_RULES.id,
    {
      field: 'name',
      required: true,
      type: 'string',
      minLength: 1,
      maxLength: 200
    },
    {
      field: 'email',
      required: false,
      type: 'string',
      customValidator: CUSTOM_VALIDATORS.emailFormat
    },
    {
      field: 'phone',
      required: false,
      type: 'string',
      maxLength: 50
    },
    {
      field: 'status',
      required: true,
      type: 'string',
      customValidator: (value: any) => {
        const validStatuses = ['active', 'inactive', 'blacklisted'];
        const result = { valid: true, errors: [], warnings: [] };
        
        if (!validStatuses.includes(value)) {
          result.valid = false;
          result.errors.push({
            field: 'status',
            code: 'INVALID_STATUS',
            message: `Status must be one of: ${validStatuses.join(', ')}`,
            severity: 'error' as const
          });
        }
        
        return result;
      }
    },
    COMMON_RULES.createdAt,
    COMMON_RULES.updatedAt
  ]
};

// ===== SCHEMA REGISTRY =====

export const VALIDATION_SCHEMAS: Record<string, CollectionValidationSchema> = {
  [COLLECTIONS.USER_PROFILES]: USER_PROFILES_SCHEMA,
  [COLLECTIONS.EXHIBITIONS]: EXHIBITIONS_SCHEMA,
  [COLLECTIONS.EXHIBITION_EVENTS]: EXHIBITION_EVENTS_SCHEMA,
  [COLLECTIONS.EXHIBITION_TASKS]: EXHIBITION_TASKS_SCHEMA,
  [COLLECTIONS.LEAD_CONTACTS]: LEAD_CONTACTS_SCHEMA,
  [COLLECTIONS.BUDGET_ALLOCATIONS]: BUDGET_ALLOCATIONS_SCHEMA,
  [COLLECTIONS.EXPENSE_RECORDS]: EXPENSE_RECORDS_SCHEMA,
  [COLLECTIONS.VENDOR_PROFILES]: VENDOR_PROFILES_SCHEMA
};

/**
 * Get validation schema for a collection
 */
export function getValidationSchema(collectionName: string): CollectionValidationSchema | null {
  return VALIDATION_SCHEMAS[collectionName] || null;
}
