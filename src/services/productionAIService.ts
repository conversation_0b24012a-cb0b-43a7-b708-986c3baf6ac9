/**
 * Production AI Service for EVEXA
 * Simple, reliable AI service using Groq API for development and production
 */

export interface AIRequest {
  service: string;
  prompt: string;
  context?: any;
  maxTokens?: number;
}

export interface AIResponse {
  response: string;
  tokensUsed: number;
  cost: number;
  model: string;
  provider: string;
}

class ProductionAIService {
  private readonly GROQ_BASE_URL = 'https://api.groq.com/openai/v1';

  private getApiKey(): string {
    // Server-side: check environment variables
    if (typeof window === 'undefined') {
      return process.env.GROQ_API_KEY || process.env.NEXT_PUBLIC_GROQ_API_KEY || '';
    }
    // Client-side: check localStorage
    return localStorage.getItem('groq_api_key') || '';
  }
  
  // Reliable models that work consistently
  private readonly models = {
    fast: 'llama3-8b-8192',           // Fast, efficient for most tasks
    smart: 'llama3-70b-8192',         // Smarter for complex tasks
    creative: 'mixtral-8x7b-32768',   // Creative tasks, social media
    code: 'llama3-8b-8192'            // Code generation
  };

  /**
   * Execute AI request with automatic model selection
   */
  async executeRequest(request: AIRequest): Promise<AIResponse> {
    const model = this.selectModel(request.service);
    const maxTokens = request.maxTokens || 500;

    try {
      console.log(`🤖 AI Request: ${request.service} using ${model}`);

      const apiKey = this.getApiKey();
      if (!apiKey) {
        throw new Error('Groq API key not configured. Please visit /ai-setup to configure.');
      }

      const response = await fetch(`${this.GROQ_BASE_URL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model,
          max_tokens: maxTokens,
          temperature: this.getTemperature(request.service),
          messages: this.buildMessages(request.service, request.prompt, request.context)
        })
      });

      if (!response.ok) {
        throw new Error(`Groq API error: ${response.status}`);
      }

      const data = await response.json();
      const responseText = data.choices[0]?.message?.content || '';
      const tokensUsed = data.usage?.total_tokens || 0;
      const cost = tokensUsed * 0.0000001; // Groq is very cheap

      // Log AI usage for monitoring (production-safe)
      if (process.env.NODE_ENV === 'development') {
        console.log(`✅ AI Response: ${tokensUsed} tokens, $${cost.toFixed(6)}`);
      }

      return {
        response: responseText,
        tokensUsed,
        cost,
        model,
        provider: 'Groq'
      };

    } catch (error) {
      // Log errors for debugging (production-safe)
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ AI request failed:', error);
      }
      
      // Return error response - no mock data allowed
      throw new Error(this.getErrorResponse(request.service));
    }
  }

  /**
   * Select appropriate model based on service type
   */
  private selectModel(service: string): string {
    switch (service) {
      case 'socialMedia':
      case 'contentGeneration':
      case 'marketing':
        return this.models.creative; // Mixtral for creative content
      
      case 'budgetPrediction':
      case 'performanceInsights':
      case 'analytics':
        return this.models.smart; // Llama 70B for complex analysis
      
      case 'leadScoring':
      case 'workflowAutomation':
      case 'virtualAssistant':
        return this.models.fast; // Llama 8B for quick responses
      
      default:
        return this.models.fast;
    }
  }

  /**
   * Get temperature based on service type
   */
  private getTemperature(service: string): number {
    switch (service) {
      case 'socialMedia':
      case 'contentGeneration':
      case 'marketing':
        return 0.8; // More creative
      
      case 'budgetPrediction':
      case 'performanceInsights':
      case 'analytics':
        return 0.3; // More factual
      
      default:
        return 0.7; // Balanced
    }
  }

  /**
   * Build messages with service-specific system prompts
   */
  private buildMessages(service: string, prompt: string, context?: any): any[] {
    const systemPrompt = this.getSystemPrompt(service);
    
    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: prompt }
    ];

    return messages;
  }

  /**
   * Get system prompt based on service
   */
  private getSystemPrompt(service: string): string {
    const basePrompt = 'You are EVEXANA, an AI assistant specialized in exhibition and event management.';
    
    switch (service) {
      case 'socialMedia':
        return `${basePrompt} You create engaging social media content for exhibitions and events. Be creative, use emojis, and focus on driving engagement and attendance.`;
      
      case 'contentGeneration':
        return `${basePrompt} You generate high-quality marketing content for exhibitions. Create compelling, professional content that drives results.`;
      
      case 'budgetPrediction':
        return `${basePrompt} You analyze exhibition budgets and provide accurate cost predictions. Be detailed, realistic, and provide actionable insights.`;
      
      case 'leadScoring':
        return `${basePrompt} You analyze leads and prospects for exhibitions. Provide scoring based on engagement, company profile, and conversion potential.`;
      
      case 'performanceInsights':
        return `${basePrompt} You analyze exhibition performance data and provide actionable insights for improvement.`;
      
      case 'workflowAutomation':
        return `${basePrompt} You optimize exhibition workflows and suggest automation opportunities for better efficiency.`;
      
      case 'virtualAssistant':
        return `${basePrompt} You are a helpful assistant for exhibition planning and management. Provide clear, actionable advice.`;
      
      default:
        return basePrompt;
    }
  }

  /**
   * Error response when AI service is unavailable
   */
  private getErrorResponse(service: string): string {
    return `AI service temporarily unavailable. Please try again later or contact support if the issue persists.`;
  }

  /**
   * Test connection to Groq API
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.executeRequest({
        service: 'virtualAssistant',
        prompt: 'Hello, please respond with "Connection successful"',
        maxTokens: 50
      });
      
      return response.response.toLowerCase().includes('connection') || response.provider === 'Groq';
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }

  /**
   * Get available models
   */
  getAvailableModels(): string[] {
    return Object.values(this.models);
  }
}

// Export singleton instance
export const productionAIService = new ProductionAIService();
