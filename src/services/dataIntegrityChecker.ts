/**
 * Data Integrity Checker
 * Utility to scan database for missing or incorrect tenantId stamps and fix data consistency issues
 */

import { 
  collection,
  doc,
  getDocs,
  updateDoc,
  deleteDoc,
  writeBatch,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { validateTenantId } from './tenantIdHelperService';
import { validationService } from './validationService';
import type { TenantAwareEntity } from '@/types/firestore';

// ===== INTEGRITY CHECK TYPES =====

export interface IntegrityCheckResult {
  collectionName: string;
  totalDocuments: number;
  validDocuments: number;
  invalidDocuments: number;
  missingTenantId: number;
  invalidTenantId: number;
  orphanedDocuments: number;
  duplicateDocuments: number;
  issues: IntegrityIssue[];
  fixedIssues: number;
  unfixedIssues: number;
  duration: number;
  timestamp: Date;
}

export interface IntegrityIssue {
  documentId: string;
  type: 'missing_tenant_id' | 'invalid_tenant_id' | 'orphaned_document' | 'duplicate_document' | 'validation_error';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  suggestedFix?: string;
  autoFixable: boolean;
  fixed: boolean;
  details?: any;
}

export interface IntegrityCheckOptions {
  collections?: string[];
  autoFix?: boolean;
  dryRun?: boolean;
  batchSize?: number;
  expectedTenantId?: string;
  skipValidation?: boolean;
  includeOrphanCheck?: boolean;
  includeDuplicateCheck?: boolean;
}

export interface IntegrityReport {
  summary: {
    totalCollections: number;
    totalDocuments: number;
    totalIssues: number;
    criticalIssues: number;
    fixedIssues: number;
    successRate: number;
  };
  collectionResults: IntegrityCheckResult[];
  recommendations: string[];
  timestamp: Date;
  duration: number;
}

// ===== DATA INTEGRITY CHECKER CLASS =====

export class DataIntegrityChecker {
  private defaultTenantId: string;
  private batchSize: number;

  constructor(defaultTenantId: string = 'evexa-super-admin-tenant', batchSize: number = 100) {
    this.defaultTenantId = defaultTenantId;
    this.batchSize = batchSize;
  }

  // ===== MAIN INTEGRITY CHECK FUNCTIONS =====

  /**
   * Run comprehensive integrity check on specified collections
   */
  async runIntegrityCheck(options: IntegrityCheckOptions = {}): Promise<IntegrityReport> {
    const startTime = new Date();
    const {
      collections = Object.values(COLLECTIONS),
      autoFix = false,
      dryRun = false,
      expectedTenantId = this.defaultTenantId
    } = options;

    console.log(`${dryRun ? '[DRY RUN] ' : ''}Starting data integrity check...`);
    console.log(`Collections to check: ${collections.length}`);
    console.log(`Auto-fix enabled: ${autoFix}`);

    const collectionResults: IntegrityCheckResult[] = [];
    
    for (const collectionName of collections) {
      try {
        console.log(`\nChecking collection: ${collectionName}`);
        const result = await this.checkCollectionIntegrity(collectionName, {
          ...options,
          expectedTenantId
        });
        collectionResults.push(result);
        
        console.log(`  - Total documents: ${result.totalDocuments}`);
        console.log(`  - Issues found: ${result.issues.length}`);
        console.log(`  - Fixed issues: ${result.fixedIssues}`);
        
      } catch (error) {
        console.error(`Error checking collection ${collectionName}:`, error);
        
        // Create error result
        collectionResults.push({
          collectionName,
          totalDocuments: 0,
          validDocuments: 0,
          invalidDocuments: 0,
          missingTenantId: 0,
          invalidTenantId: 0,
          orphanedDocuments: 0,
          duplicateDocuments: 0,
          issues: [{
            documentId: 'N/A',
            type: 'validation_error',
            severity: 'critical',
            description: `Failed to check collection: ${error}`,
            autoFixable: false,
            fixed: false
          }],
          fixedIssues: 0,
          unfixedIssues: 1,
          duration: 0,
          timestamp: new Date()
        });
      }
    }

    const endTime = new Date();
    const duration = endTime.getTime() - startTime.getTime();

    // Generate summary and recommendations
    const summary = this.generateSummary(collectionResults);
    const recommendations = this.generateRecommendations(collectionResults);

    return {
      summary,
      collectionResults,
      recommendations,
      timestamp: startTime,
      duration
    };
  }

  /**
   * Check integrity of a single collection
   */
  async checkCollectionIntegrity(
    collectionName: string,
    options: IntegrityCheckOptions = {}
  ): Promise<IntegrityCheckResult> {
    const startTime = new Date();
    const {
      autoFix = false,
      dryRun = false,
      batchSize = this.batchSize,
      expectedTenantId = this.defaultTenantId,
      skipValidation = false,
      includeOrphanCheck = true,
      includeDuplicateCheck = true
    } = options;

    const result: IntegrityCheckResult = {
      collectionName,
      totalDocuments: 0,
      validDocuments: 0,
      invalidDocuments: 0,
      missingTenantId: 0,
      invalidTenantId: 0,
      orphanedDocuments: 0,
      duplicateDocuments: 0,
      issues: [],
      fixedIssues: 0,
      unfixedIssues: 0,
      duration: 0,
      timestamp: startTime
    };

    try {
      // Get all documents in collection
      const collectionRef = collection(db, collectionName);
      const snapshot = await getDocs(collectionRef);
      
      result.totalDocuments = snapshot.size;
      
      if (snapshot.empty) {
        console.log(`  Collection ${collectionName} is empty`);
        return result;
      }

      // Process documents in batches
      const documents = snapshot.docs;
      const batches = [];
      
      for (let i = 0; i < documents.length; i += batchSize) {
        batches.push(documents.slice(i, i + batchSize));
      }

      for (const batch of batches) {
        for (const docSnapshot of batch) {
          const docData = docSnapshot.data();
          const docId = docSnapshot.id;

          // Check tenant ID issues
          const tenantIssues = this.checkTenantIdIssues(docId, docData, expectedTenantId);
          result.issues.push(...tenantIssues);

          // Update counters
          if (tenantIssues.length === 0) {
            result.validDocuments++;
          } else {
            result.invalidDocuments++;
            
            tenantIssues.forEach(issue => {
              switch (issue.type) {
                case 'missing_tenant_id':
                  result.missingTenantId++;
                  break;
                case 'invalid_tenant_id':
                  result.invalidTenantId++;
                  break;
              }
            });
          }

          // Run validation checks if not skipped
          if (!skipValidation) {
            const validationIssues = this.checkValidationIssues(docId, docData, collectionName);
            result.issues.push(...validationIssues);
          }

          // Auto-fix issues if enabled
          if (autoFix && !dryRun) {
            const fixableIssues = result.issues.filter(issue => 
              issue.documentId === docId && issue.autoFixable && !issue.fixed
            );
            
            for (const issue of fixableIssues) {
              try {
                await this.fixIssue(collectionName, docId, docData, issue);
                issue.fixed = true;
                result.fixedIssues++;
              } catch (error) {
                console.error(`Failed to fix issue for document ${docId}:`, error);
                result.unfixedIssues++;
              }
            }
          }
        }
      }

      // Check for orphaned documents if enabled
      if (includeOrphanCheck) {
        const orphanIssues = await this.checkOrphanedDocuments(collectionName, documents);
        result.issues.push(...orphanIssues);
        result.orphanedDocuments = orphanIssues.length;
      }

      // Check for duplicates if enabled
      if (includeDuplicateCheck) {
        const duplicateIssues = this.checkDuplicateDocuments(documents);
        result.issues.push(...duplicateIssues);
        result.duplicateDocuments = duplicateIssues.length;
      }

      // Count unfixed issues
      result.unfixedIssues = result.issues.filter(issue => !issue.fixed).length;

    } catch (error) {
      console.error(`Error checking collection ${collectionName}:`, error);
      result.issues.push({
        documentId: 'N/A',
        type: 'validation_error',
        severity: 'critical',
        description: `Collection check failed: ${error}`,
        autoFixable: false,
        fixed: false
      });
    }

    const endTime = new Date();
    result.duration = endTime.getTime() - startTime.getTime();

    return result;
  }

  // ===== ISSUE DETECTION FUNCTIONS =====

  /**
   * Check for tenant ID related issues
   */
  private checkTenantIdIssues(
    docId: string,
    docData: any,
    expectedTenantId: string
  ): IntegrityIssue[] {
    const issues: IntegrityIssue[] = [];

    // Check for missing tenant ID
    if (!docData.tenantId) {
      issues.push({
        documentId: docId,
        type: 'missing_tenant_id',
        severity: 'critical',
        description: 'Document is missing tenantId field',
        suggestedFix: `Add tenantId: "${expectedTenantId}"`,
        autoFixable: true,
        fixed: false,
        details: { expectedTenantId }
      });
    } else {
      // Check for invalid tenant ID format
      try {
        validateTenantId(docData.tenantId);
      } catch (error) {
        issues.push({
          documentId: docId,
          type: 'invalid_tenant_id',
          severity: 'high',
          description: `Invalid tenantId format: ${error}`,
          suggestedFix: `Fix tenantId format or set to "${expectedTenantId}"`,
          autoFixable: true,
          fixed: false,
          details: { currentTenantId: docData.tenantId, expectedTenantId }
        });
      }

      // Check if tenant ID matches expected value
      if (docData.tenantId !== expectedTenantId) {
        issues.push({
          documentId: docId,
          type: 'invalid_tenant_id',
          severity: 'medium',
          description: `Document belongs to different tenant: ${docData.tenantId}`,
          suggestedFix: `Verify tenant ownership or update to "${expectedTenantId}"`,
          autoFixable: false, // Requires manual verification
          fixed: false,
          details: { currentTenantId: docData.tenantId, expectedTenantId }
        });
      }
    }

    return issues;
  }

  /**
   * Check for validation issues
   */
  private checkValidationIssues(
    docId: string,
    docData: any,
    collectionName: string
  ): IntegrityIssue[] {
    const issues: IntegrityIssue[] = [];

    try {
      const validation = validationService.validateDocument(docData, collectionName);
      
      if (!validation.valid) {
        validation.errors.forEach(error => {
          issues.push({
            documentId: docId,
            type: 'validation_error',
            severity: error.severity === 'critical' ? 'critical' : 'medium',
            description: `Validation error in ${error.field}: ${error.message}`,
            suggestedFix: 'Fix validation error according to schema requirements',
            autoFixable: false, // Most validation errors require manual review
            fixed: false,
            details: { field: error.field, code: error.code }
          });
        });
      }
    } catch (error) {
      issues.push({
        documentId: docId,
        type: 'validation_error',
        severity: 'medium',
        description: `Validation check failed: ${error}`,
        autoFixable: false,
        fixed: false
      });
    }

    return issues;
  }

  /**
   * Check for orphaned documents (documents referencing non-existent parents)
   */
  private async checkOrphanedDocuments(
    collectionName: string,
    documents: any[]
  ): Promise<IntegrityIssue[]> {
    const issues: IntegrityIssue[] = [];

    // Define parent-child relationships
    const parentRelationships: Record<string, string> = {
      [COLLECTIONS.EXHIBITION_EVENTS]: 'exhibitionId',
      [COLLECTIONS.EXHIBITION_TASKS]: 'exhibitionId',
      [COLLECTIONS.LEAD_CONTACTS]: 'exhibitionId',
      [COLLECTIONS.BUDGET_ALLOCATIONS]: 'activityId',
      [COLLECTIONS.EXPENSE_RECORDS]: 'activityId'
    };

    const parentField = parentRelationships[collectionName];
    if (!parentField) {
      return issues; // No parent relationship defined
    }

    // Check each document for valid parent reference
    for (const docSnapshot of documents) {
      const docData = docSnapshot.data();
      const docId = docSnapshot.id;
      const parentId = docData[parentField];

      if (parentId) {
        try {
          // Check if parent exists (simplified check - would need more sophisticated logic)
          const parentCollectionName = parentField === 'exhibitionId' ? COLLECTIONS.EXHIBITIONS : COLLECTIONS.EXHIBITIONS;
          const parentDoc = await doc(db, parentCollectionName, parentId);
          const parentSnapshot = await parentDoc.get();

          if (!parentSnapshot.exists()) {
            issues.push({
              documentId: docId,
              type: 'orphaned_document',
              severity: 'high',
              description: `Document references non-existent parent: ${parentId}`,
              suggestedFix: 'Remove document or fix parent reference',
              autoFixable: false,
              fixed: false,
              details: { parentField, parentId }
            });
          }
        } catch (error) {
          // Skip orphan check if parent lookup fails
        }
      }
    }

    return issues;
  }

  /**
   * Check for duplicate documents
   */
  private checkDuplicateDocuments(documents: any[]): IntegrityIssue[] {
    const issues: IntegrityIssue[] = [];
    const seen = new Map<string, string>();

    for (const docSnapshot of documents) {
      const docData = docSnapshot.data();
      const docId = docSnapshot.id;

      // Create a simple hash based on key fields (email, name, etc.)
      const keyFields = ['email', 'name', 'title'];
      const keyValues = keyFields
        .filter(field => docData[field])
        .map(field => `${field}:${docData[field]}`)
        .join('|');

      if (keyValues && seen.has(keyValues)) {
        issues.push({
          documentId: docId,
          type: 'duplicate_document',
          severity: 'medium',
          description: `Potential duplicate of document: ${seen.get(keyValues)}`,
          suggestedFix: 'Review and merge or remove duplicate',
          autoFixable: false,
          fixed: false,
          details: { duplicateOf: seen.get(keyValues), keyFields: keyValues }
        });
      } else if (keyValues) {
        seen.set(keyValues, docId);
      }
    }

    return issues;
  }

  // ===== ISSUE FIXING FUNCTIONS =====

  /**
   * Fix a specific issue
   */
  private async fixIssue(
    collectionName: string,
    docId: string,
    docData: any,
    issue: IntegrityIssue
  ): Promise<void> {
    const docRef = doc(db, collectionName, docId);

    switch (issue.type) {
      case 'missing_tenant_id':
        await updateDoc(docRef, {
          tenantId: issue.details?.expectedTenantId || this.defaultTenantId,
          updatedAt: serverTimestamp(),
          integrityFixed: true,
          integrityFixedAt: serverTimestamp()
        });
        break;

      case 'invalid_tenant_id':
        if (issue.autoFixable) {
          await updateDoc(docRef, {
            tenantId: issue.details?.expectedTenantId || this.defaultTenantId,
            updatedAt: serverTimestamp(),
            integrityFixed: true,
            integrityFixedAt: serverTimestamp()
          });
        }
        break;

      default:
        throw new Error(`Cannot auto-fix issue type: ${issue.type}`);
    }
  }

  // ===== REPORTING FUNCTIONS =====

  /**
   * Generate summary from collection results
   */
  private generateSummary(results: IntegrityCheckResult[]): IntegrityReport['summary'] {
    const totalCollections = results.length;
    const totalDocuments = results.reduce((sum, r) => sum + r.totalDocuments, 0);
    const totalIssues = results.reduce((sum, r) => sum + r.issues.length, 0);
    const criticalIssues = results.reduce((sum, r) => 
      sum + r.issues.filter(i => i.severity === 'critical').length, 0
    );
    const fixedIssues = results.reduce((sum, r) => sum + r.fixedIssues, 0);
    const successRate = totalIssues > 0 ? ((totalIssues - totalIssues + fixedIssues) / totalIssues) * 100 : 100;

    return {
      totalCollections,
      totalDocuments,
      totalIssues,
      criticalIssues,
      fixedIssues,
      successRate
    };
  }

  /**
   * Generate recommendations based on results
   */
  private generateRecommendations(results: IntegrityCheckResult[]): string[] {
    const recommendations: string[] = [];
    
    const totalIssues = results.reduce((sum, r) => sum + r.issues.length, 0);
    const criticalIssues = results.reduce((sum, r) => 
      sum + r.issues.filter(i => i.severity === 'critical').length, 0
    );

    if (criticalIssues > 0) {
      recommendations.push(`🚨 URGENT: Fix ${criticalIssues} critical issues immediately`);
    }

    if (totalIssues > 0) {
      recommendations.push(`📋 Run integrity check with autoFix=true to fix ${results.reduce((sum, r) => sum + r.issues.filter(i => i.autoFixable).length, 0)} auto-fixable issues`);
    }

    const collectionsWithIssues = results.filter(r => r.issues.length > 0);
    if (collectionsWithIssues.length > 0) {
      recommendations.push(`🔍 Focus on collections: ${collectionsWithIssues.map(r => r.collectionName).join(', ')}`);
    }

    if (totalIssues === 0) {
      recommendations.push('✅ Database integrity is good! No issues found.');
    }

    return recommendations;
  }
}

// ===== CONVENIENCE FUNCTIONS =====

/**
 * Create a data integrity checker instance
 */
export function createIntegrityChecker(
  defaultTenantId: string = 'evexa-super-admin-tenant',
  batchSize: number = 100
): DataIntegrityChecker {
  return new DataIntegrityChecker(defaultTenantId, batchSize);
}

/**
 * Quick integrity check for all collections
 */
export async function quickIntegrityCheck(
  options: IntegrityCheckOptions = {}
): Promise<IntegrityReport> {
  const checker = createIntegrityChecker();
  return checker.runIntegrityCheck(options);
}
