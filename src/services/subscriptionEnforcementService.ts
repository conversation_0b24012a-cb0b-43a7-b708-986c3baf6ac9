/**
 * Subscription Enforcement Service
 * Enhanced subscription limit enforcement with real-time validation and user management
 */

import { 
  collection,
  doc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { SubscriptionService, TenantSubscription, SubscriptionPlan } from './subscriptionService';
import { createTenantPersonaService } from './tenantPersonaService';
import { validateTenantId } from './tenantIdHelperService';

// ===== TYPES =====

export interface UserLimitCheck {
  allowed: boolean;
  currentUsers: number;
  maxUsers: number;
  availableSlots: number;
  reason?: string;
  upgradeRequired?: boolean;
  suggestedPlan?: string;
}

export interface InvitationLimitCheck {
  allowed: boolean;
  currentInvitations: number;
  maxInvitations: number;
  pendingInvitations: number;
  reason?: string;
}

export interface SubscriptionEnforcementOptions {
  includeInactive?: boolean;
  includePending?: boolean;
  gracePeriodDays?: number;
  allowOverage?: boolean;
  checkTrialStatus?: boolean;
}

export interface UserUsageBreakdown {
  activeUsers: number;
  inactiveUsers: number;
  pendingInvitations: number;
  totalSlots: number;
  availableSlots: number;
  usagePercentage: number;
  isNearLimit: boolean;
  isAtLimit: boolean;
}

export interface EnforcementAction {
  type: 'block' | 'warn' | 'allow';
  message: string;
  upgradeUrl?: string;
  contactSupport?: boolean;
}

// ===== SUBSCRIPTION ENFORCEMENT SERVICE =====

export class SubscriptionEnforcementService {
  private subscriptionService: SubscriptionService;
  private personaService: any;

  constructor(private tenantId: string) {
    validateTenantId(tenantId);
    this.subscriptionService = new SubscriptionService(tenantId);
    this.personaService = createTenantPersonaService(tenantId);
  }

  // ===== USER LIMIT ENFORCEMENT =====

  /**
   * Check if current user is super admin
   */
  private isSuperAdmin(): boolean {
    try {
      const { SUPER_ADMIN_USER_IDS } = require('@/services/superAdminService');
      const { auth } = require('@/lib/firebase');
      return auth.currentUser && SUPER_ADMIN_USER_IDS.includes(auth.currentUser.uid);
    } catch {
      return false;
    }
  }

  /**
   * Check if new users can be added to the tenant
   */
  async checkUserLimit(
    requestedUsers: number = 1,
    options: SubscriptionEnforcementOptions = {}
  ): Promise<UserLimitCheck> {
    try {
      // SUPER ADMIN BYPASS: Super admin has unlimited users
      if (this.isSuperAdmin()) {
        return {
          allowed: true,
          currentUsers: 0,
          maxUsers: -1, // Unlimited
          availableSlots: -1, // Unlimited
          reason: 'Super admin - unlimited access'
        };
      }

      // Get current subscription
      const subscription = await this.subscriptionService.getTenantSubscription(this.tenantId);
      if (!subscription) {
        return {
          allowed: false,
          currentUsers: 0,
          maxUsers: 0,
          availableSlots: 0,
          reason: 'No active subscription found',
          upgradeRequired: true,
          suggestedPlan: 'starter'
        };
      }

      // Get subscription plan
      const plan = await this.subscriptionService.getPlan(subscription.planId);
      if (!plan) {
        return {
          allowed: false,
          currentUsers: 0,
          maxUsers: 0,
          availableSlots: 0,
          reason: 'Invalid subscription plan',
          upgradeRequired: true
        };
      }

      // Get current user count
      const userBreakdown = await this.getUserUsageBreakdown(options);
      const currentUsers = userBreakdown.activeUsers + (options.includePending ? userBreakdown.pendingInvitations : 0);
      const maxUsers = plan.features.maxUsers;

      // Check if unlimited
      if (maxUsers === -1) {
        return {
          allowed: true,
          currentUsers,
          maxUsers: -1,
          availableSlots: -1
        };
      }

      // Calculate available slots
      const availableSlots = Math.max(0, maxUsers - currentUsers);
      const wouldExceedLimit = currentUsers + requestedUsers > maxUsers;

      // Check grace period for trial/new subscriptions
      if (wouldExceedLimit && options.gracePeriodDays && subscription.status === 'trialing') {
        const trialEnd = subscription.billing.trialEnd;
        if (trialEnd && new Date() < trialEnd) {
          const gracePeriodEnd = new Date(trialEnd.getTime() + (options.gracePeriodDays * 24 * 60 * 60 * 1000));
          if (new Date() < gracePeriodEnd) {
            return {
              allowed: true,
              currentUsers,
              maxUsers,
              availableSlots: 0,
              reason: 'Grace period active for trial subscription'
            };
          }
        }
      }

      // Check overage allowance
      if (wouldExceedLimit && options.allowOverage && plan.features.allowUserOverage) {
        const overageLimit = plan.features.maxUserOverage || 2;
        if (currentUsers + requestedUsers <= maxUsers + overageLimit) {
          return {
            allowed: true,
            currentUsers,
            maxUsers,
            availableSlots: 0,
            reason: `Overage allowed (${requestedUsers} users over limit)`
          };
        }
      }

      if (wouldExceedLimit) {
        return {
          allowed: false,
          currentUsers,
          maxUsers,
          availableSlots,
          reason: `User limit would be exceeded. Current: ${currentUsers}, Max: ${maxUsers}, Requested: ${requestedUsers}`,
          upgradeRequired: true,
          suggestedPlan: this.getSuggestedUpgradePlan(plan.tier)
        };
      }

      return {
        allowed: true,
        currentUsers,
        maxUsers,
        availableSlots
      };

    } catch (error) {
      console.error('Error checking user limit:', error);
      return {
        allowed: false,
        currentUsers: 0,
        maxUsers: 0,
        availableSlots: 0,
        reason: 'Error checking subscription limits',
        upgradeRequired: true
      };
    }
  }

  /**
   * Check invitation limits
   */
  async checkInvitationLimit(
    requestedInvitations: number = 1
  ): Promise<InvitationLimitCheck> {
    try {
      const userBreakdown = await this.getUserUsageBreakdown();
      const userLimitCheck = await this.checkUserLimit(requestedInvitations);

      // Calculate invitation-specific limits
      const maxInvitations = userLimitCheck.maxUsers === -1 ? -1 : userLimitCheck.availableSlots;
      const currentInvitations = userBreakdown.pendingInvitations;

      if (maxInvitations !== -1 && requestedInvitations > maxInvitations) {
        return {
          allowed: false,
          currentInvitations,
          maxInvitations,
          pendingInvitations: userBreakdown.pendingInvitations,
          reason: `Cannot send ${requestedInvitations} invitations. Only ${maxInvitations} slots available.`
        };
      }

      return {
        allowed: true,
        currentInvitations,
        maxInvitations,
        pendingInvitations: userBreakdown.pendingInvitations
      };

    } catch (error) {
      console.error('Error checking invitation limit:', error);
      return {
        allowed: false,
        currentInvitations: 0,
        maxInvitations: 0,
        pendingInvitations: 0,
        reason: 'Error checking invitation limits'
      };
    }
  }

  /**
   * Get detailed user usage breakdown
   */
  async getUserUsageBreakdown(
    options: SubscriptionEnforcementOptions = {}
  ): Promise<UserUsageBreakdown> {
    try {
      // Get all users for the tenant
      const usersQuery = query(
        collection(db, COLLECTIONS.USERS),
        where('tenantId', '==', this.tenantId)
      );
      const usersSnapshot = await getDocs(usersQuery);
      
      let activeUsers = 0;
      let inactiveUsers = 0;

      usersSnapshot.docs.forEach(doc => {
        const userData = doc.data();
        if (userData.status === 'active' || (!userData.status && userData.lastLogin)) {
          activeUsers++;
        } else {
          inactiveUsers++;
        }
      });

      // Get pending invitations
      const invitationsQuery = query(
        collection(db, COLLECTIONS.INVITATIONS),
        where('tenantId', '==', this.tenantId),
        where('status', '==', 'pending')
      );
      const invitationsSnapshot = await getDocs(invitationsQuery);
      const pendingInvitations = invitationsSnapshot.size;

      // Get subscription limits
      const userLimitCheck = await this.checkUserLimit(0);
      const totalSlots = userLimitCheck.maxUsers;
      const currentUsage = activeUsers + (options.includePending ? pendingInvitations : 0);
      const availableSlots = totalSlots === -1 ? -1 : Math.max(0, totalSlots - currentUsage);
      const usagePercentage = totalSlots === -1 ? 0 : (currentUsage / totalSlots) * 100;

      return {
        activeUsers,
        inactiveUsers,
        pendingInvitations,
        totalSlots,
        availableSlots,
        usagePercentage,
        isNearLimit: usagePercentage >= 80,
        isAtLimit: usagePercentage >= 100
      };

    } catch (error) {
      console.error('Error getting user usage breakdown:', error);
      return {
        activeUsers: 0,
        inactiveUsers: 0,
        pendingInvitations: 0,
        totalSlots: 0,
        availableSlots: 0,
        usagePercentage: 0,
        isNearLimit: false,
        isAtLimit: false
      };
    }
  }

  /**
   * Enforce user limit before adding users
   */
  async enforceUserLimit(
    requestedUsers: number = 1,
    options: SubscriptionEnforcementOptions = {}
  ): Promise<EnforcementAction> {
    const limitCheck = await this.checkUserLimit(requestedUsers, options);

    if (!limitCheck.allowed) {
      if (limitCheck.upgradeRequired) {
        return {
          type: 'block',
          message: limitCheck.reason || 'User limit exceeded',
          upgradeUrl: `/billing/upgrade?plan=${limitCheck.suggestedPlan}`,
          contactSupport: false
        };
      } else {
        return {
          type: 'block',
          message: limitCheck.reason || 'Cannot add users at this time',
          contactSupport: true
        };
      }
    }

    // Check if near limit (80% or more)
    const usageBreakdown = await this.getUserUsageBreakdown();
    if (usageBreakdown.isNearLimit && !usageBreakdown.isAtLimit) {
      return {
        type: 'warn',
        message: `You're using ${usageBreakdown.usagePercentage.toFixed(0)}% of your user limit. Consider upgrading soon.`,
        upgradeUrl: `/billing/upgrade`
      };
    }

    return {
      type: 'allow',
      message: 'User addition allowed'
    };
  }

  // ===== HELPER METHODS =====

  private getSuggestedUpgradePlan(currentTier: string): string {
    const tierUpgrades: Record<string, string> = {
      'free': 'starter',
      'starter': 'professional',
      'professional': 'enterprise',
      'enterprise': 'custom'
    };
    return tierUpgrades[currentTier] || 'professional';
  }

  /**
   * Update user count in subscription usage
   */
  async updateUserCount(): Promise<void> {
    try {
      const userBreakdown = await this.getUserUsageBreakdown();
      await this.subscriptionService.trackUsage(
        this.tenantId,
        'user_count_update',
        'system',
        userBreakdown.activeUsers,
        { breakdown: userBreakdown }
      );
    } catch (error) {
      console.error('Error updating user count:', error);
    }
  }

  /**
   * Clean up inactive users (admin function)
   */
  async cleanupInactiveUsers(inactiveDays: number = 90): Promise<{
    cleaned: number;
    errors: string[];
  }> {
    const results = { cleaned: 0, errors: [] };
    
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - inactiveDays);

      const usersQuery = query(
        collection(db, COLLECTIONS.USERS),
        where('tenantId', '==', this.tenantId),
        where('status', '==', 'inactive'),
        where('lastLogin', '<', Timestamp.fromDate(cutoffDate))
      );

      const usersSnapshot = await getDocs(usersQuery);
      const batch = writeBatch(db);

      usersSnapshot.docs.forEach(userDoc => {
        batch.delete(userDoc.ref);
        results.cleaned++;
      });

      if (results.cleaned > 0) {
        await batch.commit();
        await this.updateUserCount();
      }

    } catch (error) {
      console.error('Error cleaning up inactive users:', error);
      results.errors.push(error instanceof Error ? error.message : 'Unknown error');
    }

    return results;
  }
}

// ===== FACTORY FUNCTION =====

/**
 * Create subscription enforcement service instance
 */
export function createSubscriptionEnforcementService(tenantId: string): SubscriptionEnforcementService {
  return new SubscriptionEnforcementService(tenantId);
}
