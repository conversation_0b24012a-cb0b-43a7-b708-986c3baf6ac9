/**
 * AI Cost Prediction Service for EVEXA
 * Provides predictive analytics for AI cost management and optimization
 */

import { aiCostOptimizationService, AIUsageRecord } from './aiCostOptimizationService';

export interface CostPrediction {
  predictedDailyCost: number;
  predictedMonthlyCost: number;
  confidence: number;
  factors: string[];
  recommendations: string[];
  riskLevel: 'low' | 'medium' | 'high';
}

export interface UsagePattern {
  service: string;
  averageRequestsPerHour: number;
  averageCostPerRequest: number;
  peakHours: number[];
  seasonality: 'increasing' | 'decreasing' | 'stable';
}

export interface CostOptimizationRecommendation {
  type: 'caching' | 'rate_limiting' | 'service_optimization' | 'model_selection';
  title: string;
  description: string;
  potentialSavings: number;
  implementationEffort: 'low' | 'medium' | 'high';
  priority: number;
}

class AICostPredictionService {
  private usagePatterns: Map<string, UsagePattern> = new Map();
  private lastAnalysisTime: Date | null = null;
  private readonly ANALYSIS_INTERVAL = 60 * 60 * 1000; // 1 hour

  /**
   * Predict future costs based on usage patterns
   */
  async predictCosts(): Promise<CostPrediction> {
    const usageHistory = aiCostOptimizationService.getUsageHistory(1000);
    const analytics = await aiCostOptimizationService.getCostAnalytics();
    
    if (usageHistory.length < 10) {
      return {
        predictedDailyCost: analytics.dailyCost,
        predictedMonthlyCost: analytics.monthlyCost,
        confidence: 0.3,
        factors: ['Insufficient historical data'],
        recommendations: ['Continue using AI services to build prediction accuracy'],
        riskLevel: 'low'
      };
    }

    // Analyze usage patterns
    this.analyzeUsagePatterns(usageHistory);
    
    // Calculate predictions
    const dailyPrediction = this.predictDailyCost(usageHistory);
    const monthlyPrediction = this.predictMonthlyCost(usageHistory);
    
    // Determine risk level
    const config = aiCostOptimizationService.getConfiguration();
    const riskLevel = this.calculateRiskLevel(dailyPrediction, monthlyPrediction, config);
    
    // Generate recommendations
    const recommendations = this.generateRecommendations(usageHistory, analytics);
    
    return {
      predictedDailyCost: dailyPrediction.amount,
      predictedMonthlyCost: monthlyPrediction.amount,
      confidence: Math.min(dailyPrediction.confidence, monthlyPrediction.confidence),
      factors: [...dailyPrediction.factors, ...monthlyPrediction.factors],
      recommendations: recommendations.map(r => r.title),
      riskLevel
    };
  }

  /**
   * Analyze usage patterns from historical data
   */
  private analyzeUsagePatterns(usageHistory: AIUsageRecord[]): void {
    const serviceGroups = this.groupByService(usageHistory);
    
    for (const [service, records] of serviceGroups.entries()) {
      const pattern = this.calculateServicePattern(service, records);
      this.usagePatterns.set(service, pattern);
    }
    
    this.lastAnalysisTime = new Date();
  }

  /**
   * Calculate usage pattern for a specific service
   */
  private calculateServicePattern(service: string, records: AIUsageRecord[]): UsagePattern {
    const hourlyUsage = new Array(24).fill(0);
    let totalCost = 0;
    
    records.forEach(record => {
      const hour = record.timestamp.getHours();
      hourlyUsage[hour]++;
      totalCost += record.cost;
    });
    
    const averageRequestsPerHour = records.length / 24;
    const averageCostPerRequest = totalCost / records.length;
    const peakHours = this.findPeakHours(hourlyUsage);
    const seasonality = this.calculateSeasonality(records);
    
    return {
      service,
      averageRequestsPerHour,
      averageCostPerRequest,
      peakHours,
      seasonality
    };
  }

  /**
   * Predict daily cost based on patterns
   */
  private predictDailyCost(usageHistory: AIUsageRecord[]): {
    amount: number;
    confidence: number;
    factors: string[];
  } {
    const recentRecords = usageHistory.slice(0, 100);
    const averageDailyCost = this.calculateAverageDailyCost(recentRecords);
    const trend = this.calculateTrend(recentRecords);
    
    let predictedCost = averageDailyCost * (1 + trend);
    let confidence = 0.7;
    const factors: string[] = [];
    
    // Adjust for day of week patterns
    const dayOfWeek = new Date().getDay();
    const weekdayMultiplier = this.getWeekdayMultiplier(dayOfWeek, recentRecords);
    predictedCost *= weekdayMultiplier;
    
    if (weekdayMultiplier !== 1) {
      factors.push(`${dayOfWeek === 0 || dayOfWeek === 6 ? 'Weekend' : 'Weekday'} usage pattern`);
    }
    
    // Adjust for cache hit rate
    const cacheHitRate = this.calculateCacheHitRate(recentRecords);
    if (cacheHitRate < 0.5) {
      factors.push('Low cache hit rate increasing costs');
      confidence -= 0.1;
    }
    
    // Adjust for recent usage spikes
    const recentSpike = this.detectUsageSpike(recentRecords);
    if (recentSpike) {
      predictedCost *= 1.2;
      factors.push('Recent usage spike detected');
      confidence -= 0.2;
    }
    
    return {
      amount: Math.max(0, predictedCost),
      confidence: Math.max(0.1, confidence),
      factors
    };
  }

  /**
   * Predict monthly cost based on patterns
   */
  private predictMonthlyCost(usageHistory: AIUsageRecord[]): {
    amount: number;
    confidence: number;
    factors: string[];
  } {
    const dailyPrediction = this.predictDailyCost(usageHistory);
    const daysInMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate();
    
    let monthlyPrediction = dailyPrediction.amount * daysInMonth;
    let confidence = dailyPrediction.confidence * 0.8; // Lower confidence for longer predictions
    const factors = [...dailyPrediction.factors];
    
    // Adjust for monthly growth trends
    const monthlyGrowth = this.calculateMonthlyGrowth(usageHistory);
    if (monthlyGrowth !== 0) {
      monthlyPrediction *= (1 + monthlyGrowth);
      factors.push(`Monthly growth trend: ${(monthlyGrowth * 100).toFixed(1)}%`);
    }
    
    return {
      amount: Math.max(0, monthlyPrediction),
      confidence: Math.max(0.1, confidence),
      factors
    };
  }

  /**
   * Calculate risk level based on predictions and limits
   */
  private calculateRiskLevel(
    dailyPrediction: number,
    monthlyPrediction: number,
    config: any
  ): 'low' | 'medium' | 'high' {
    const dailyRisk = dailyPrediction / config.maxDailyCost;
    const monthlyRisk = monthlyPrediction / config.maxMonthlyCost;
    
    const maxRisk = Math.max(dailyRisk, monthlyRisk);
    
    if (maxRisk > 0.9) return 'high';
    if (maxRisk > 0.7) return 'medium';
    return 'low';
  }

  /**
   * Generate cost optimization recommendations
   */
  generateRecommendations(
    usageHistory: AIUsageRecord[],
    analytics: any
  ): CostOptimizationRecommendation[] {
    const recommendations: CostOptimizationRecommendation[] = [];
    
    // Cache optimization
    if (analytics.cacheHitRate < 0.6) {
      recommendations.push({
        type: 'caching',
        title: 'Improve Cache Hit Rate',
        description: 'Current cache hit rate is low. Consider increasing cache TTL or optimizing cache keys.',
        potentialSavings: analytics.dailyCost * 0.3,
        implementationEffort: 'low',
        priority: 1
      });
    }
    
    // Rate limiting optimization
    const highVolumeServices = this.findHighVolumeServices(usageHistory);
    if (highVolumeServices.length > 0) {
      recommendations.push({
        type: 'rate_limiting',
        title: 'Optimize High-Volume Services',
        description: `Services ${highVolumeServices.join(', ')} have high request volumes. Consider rate limiting or batching.`,
        potentialSavings: analytics.dailyCost * 0.2,
        implementationEffort: 'medium',
        priority: 2
      });
    }
    
    // Model selection optimization
    const expensiveQueries = analytics.topExpensiveQueries || [];
    if (expensiveQueries.length > 0) {
      recommendations.push({
        type: 'model_selection',
        title: 'Optimize Model Selection',
        description: 'Some queries are using expensive models. Consider using cheaper models for simpler tasks.',
        potentialSavings: analytics.dailyCost * 0.25,
        implementationEffort: 'high',
        priority: 3
      });
    }
    
    return recommendations.sort((a, b) => a.priority - b.priority);
  }

  // Helper methods
  private groupByService(records: AIUsageRecord[]): Map<string, AIUsageRecord[]> {
    const groups = new Map<string, AIUsageRecord[]>();
    records.forEach(record => {
      if (!groups.has(record.service)) {
        groups.set(record.service, []);
      }
      groups.get(record.service)!.push(record);
    });
    return groups;
  }

  private findPeakHours(hourlyUsage: number[]): number[] {
    const average = hourlyUsage.reduce((sum, count) => sum + count, 0) / 24;
    return hourlyUsage
      .map((count, hour) => ({ hour, count }))
      .filter(({ count }) => count > average * 1.5)
      .map(({ hour }) => hour);
  }

  private calculateSeasonality(records: AIUsageRecord[]): 'increasing' | 'decreasing' | 'stable' {
    if (records.length < 7) return 'stable';
    
    const recent = records.slice(0, Math.floor(records.length / 2));
    const older = records.slice(Math.floor(records.length / 2));
    
    const recentAvg = recent.reduce((sum, r) => sum + r.cost, 0) / recent.length;
    const olderAvg = older.reduce((sum, r) => sum + r.cost, 0) / older.length;
    
    const change = (recentAvg - olderAvg) / olderAvg;
    
    if (change > 0.1) return 'increasing';
    if (change < -0.1) return 'decreasing';
    return 'stable';
  }

  private calculateAverageDailyCost(records: AIUsageRecord[]): number {
    const dailyCosts = new Map<string, number>();
    
    records.forEach(record => {
      const date = record.timestamp.toDateString();
      dailyCosts.set(date, (dailyCosts.get(date) || 0) + record.cost);
    });
    
    const costs = Array.from(dailyCosts.values());
    return costs.length > 0 ? costs.reduce((sum, cost) => sum + cost, 0) / costs.length : 0;
  }

  private calculateTrend(records: AIUsageRecord[]): number {
    if (records.length < 10) return 0;
    
    const recent = records.slice(0, 5);
    const older = records.slice(-5);
    
    const recentAvg = recent.reduce((sum, r) => sum + r.cost, 0) / recent.length;
    const olderAvg = older.reduce((sum, r) => sum + r.cost, 0) / older.length;
    
    return olderAvg > 0 ? (recentAvg - olderAvg) / olderAvg : 0;
  }

  private getWeekdayMultiplier(dayOfWeek: number, records: AIUsageRecord[]): number {
    const weekdayUsage = new Array(7).fill(0);
    
    records.forEach(record => {
      weekdayUsage[record.timestamp.getDay()]++;
    });
    
    const average = weekdayUsage.reduce((sum, count) => sum + count, 0) / 7;
    return average > 0 ? weekdayUsage[dayOfWeek] / average : 1;
  }

  private calculateCacheHitRate(records: AIUsageRecord[]): number {
    const cacheHits = records.filter(r => r.cacheHit).length;
    return records.length > 0 ? cacheHits / records.length : 0;
  }

  private detectUsageSpike(records: AIUsageRecord[]): boolean {
    if (records.length < 20) return false;
    
    const recent = records.slice(0, 10);
    const baseline = records.slice(10, 20);
    
    const recentAvg = recent.reduce((sum, r) => sum + r.cost, 0) / recent.length;
    const baselineAvg = baseline.reduce((sum, r) => sum + r.cost, 0) / baseline.length;
    
    return recentAvg > baselineAvg * 2;
  }

  private calculateMonthlyGrowth(records: AIUsageRecord[]): number {
    // Simplified monthly growth calculation
    if (records.length < 30) return 0;
    
    const thisMonth = records.slice(0, 15);
    const lastMonth = records.slice(15, 30);
    
    const thisMonthAvg = thisMonth.reduce((sum, r) => sum + r.cost, 0) / thisMonth.length;
    const lastMonthAvg = lastMonth.reduce((sum, r) => sum + r.cost, 0) / lastMonth.length;
    
    return lastMonthAvg > 0 ? (thisMonthAvg - lastMonthAvg) / lastMonthAvg : 0;
  }

  private findHighVolumeServices(records: AIUsageRecord[]): string[] {
    const serviceCounts = new Map<string, number>();
    
    records.forEach(record => {
      serviceCounts.set(record.service, (serviceCounts.get(record.service) || 0) + 1);
    });
    
    const average = records.length / serviceCounts.size;
    
    return Array.from(serviceCounts.entries())
      .filter(([, count]) => count > average * 2)
      .map(([service]) => service);
  }
}

export const aiCostPredictionService = new AICostPredictionService();
