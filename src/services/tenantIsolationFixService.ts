/**
 * Tenant Isolation Fix Service
 * CRITICAL: Fixes tenant isolation violations by adding missing tenantId fields
 */

import {
  collection,
  doc,
  getDocs,
  updateDoc,
  writeBatch,
  query,
  where,
  limit,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';

export interface TenantIsolationViolation {
  collectionName: string;
  documentId: string;
  documentData: any;
  suggestedTenantId?: string;
}

export interface TenantIsolationFixResult {
  success: boolean;
  totalViolations: number;
  fixedViolations: number;
  failedFixes: number;
  errors: string[];
  fixedCollections: string[];
}

/**
 * Detect tenant isolation violations across all collections
 */
export async function detectTenantIsolationViolations(): Promise<{
  violations: TenantIsolationViolation[];
  totalDocuments: number;
  violatingCollections: string[];
}> {
  const violations: TenantIsolationViolation[] = [];
  const violatingCollections: string[] = [];
  let totalDocuments = 0;

  console.log('🔍 Scanning for tenant isolation violations...');

  for (const collectionName of Object.values(COLLECTIONS)) {
    try {
      const collectionRef = collection(db, collectionName);
      const snapshot = await getDocs(collectionRef);
      
      let collectionHasViolations = false;
      
      snapshot.docs.forEach(docSnapshot => {
        totalDocuments++;
        const data = docSnapshot.data();
        
        // Check if document has proper tenantId
        if (!data.tenantId || typeof data.tenantId !== 'string') {
          violations.push({
            collectionName,
            documentId: docSnapshot.id,
            documentData: data,
            suggestedTenantId: inferTenantId(data, collectionName)
          });
          collectionHasViolations = true;
        }
      });
      
      if (collectionHasViolations) {
        violatingCollections.push(collectionName);
      }
      
      console.log(`✅ Scanned ${collectionName}: ${snapshot.docs.length} documents`);
    } catch (error) {
      console.error(`❌ Error scanning ${collectionName}:`, error);
    }
  }

  console.log(`🚨 Found ${violations.length} tenant isolation violations across ${violatingCollections.length} collections`);
  
  return {
    violations,
    totalDocuments,
    violatingCollections
  };
}

/**
 * Attempt to infer tenantId from document data
 */
function inferTenantId(data: any, collectionName: string): string | undefined {
  // Strategy 1: Look for common tenant-related fields
  if (data.tenant_id) return data.tenant_id;
  if (data.tenantSlug) return data.tenantSlug;
  if (data.organizationId) return data.organizationId;
  
  // Strategy 2: Look for user-related fields that might indicate tenant
  if (data.createdBy && typeof data.createdBy === 'string') {
    // Could query user_profiles to find tenant, but for now return undefined
    return undefined;
  }
  
  // Strategy 3: Collection-specific inference
  switch (collectionName) {
    case COLLECTIONS.EXHIBITIONS:
      // Exhibitions might have organizer info
      if (data.organizerId) return data.organizerId;
      break;
    case COLLECTIONS.EXHIBITION_TASKS:
      // Tasks might have assignee info
      if (data.assignedTo) return data.assignedTo;
      break;
    default:
      break;
  }
  
  // Strategy 4: Default to super admin tenant for system data
  return 'evexa-super-admin-tenant';
}

/**
 * Fix tenant isolation violations by adding missing tenantId fields
 */
export async function fixTenantIsolationViolations(
  violations: TenantIsolationViolation[],
  defaultTenantId: string = 'evexa-super-admin-tenant'
): Promise<TenantIsolationFixResult> {
  const result: TenantIsolationFixResult = {
    success: false,
    totalViolations: violations.length,
    fixedViolations: 0,
    failedFixes: 0,
    errors: [],
    fixedCollections: []
  };

  console.log(`🔧 Starting to fix ${violations.length} tenant isolation violations...`);

  // Group violations by collection for batch processing
  const violationsByCollection = violations.reduce((acc, violation) => {
    if (!acc[violation.collectionName]) {
      acc[violation.collectionName] = [];
    }
    acc[violation.collectionName].push(violation);
    return acc;
  }, {} as Record<string, TenantIsolationViolation[]>);

  // Process each collection
  for (const [collectionName, collectionViolations] of Object.entries(violationsByCollection)) {
    try {
      console.log(`🔧 Fixing ${collectionViolations.length} violations in ${collectionName}...`);
      
      // Process in batches of 500 (Firestore batch limit)
      const batchSize = 500;
      const batches = [];
      
      for (let i = 0; i < collectionViolations.length; i += batchSize) {
        const batch = writeBatch(db);
        const batchViolations = collectionViolations.slice(i, i + batchSize);
        
        batchViolations.forEach(violation => {
          const docRef = doc(db, collectionName, violation.documentId);
          const tenantId = violation.suggestedTenantId || defaultTenantId;

          // Use set with merge to avoid "document already exists" errors
          batch.set(docRef, {
            ...violation.documentData, // Preserve existing data
            tenantId,
            updatedAt: serverTimestamp(),
            tenantIsolationFixed: true,
            tenantIsolationFixedAt: serverTimestamp()
          }, { merge: true }); // Merge with existing document
        });
        
        batches.push({ batch, violations: batchViolations });
      }
      
      // Execute all batches for this collection
      for (const { batch, violations: batchViolations } of batches) {
        try {
          await batch.commit();
          result.fixedViolations += batchViolations.length;
          console.log(`✅ Fixed ${batchViolations.length} violations in ${collectionName}`);
        } catch (error) {
          result.failedFixes += batchViolations.length;
          result.errors.push(`Failed to fix violations in ${collectionName}: ${error}`);
          console.error(`❌ Failed to fix violations in ${collectionName}:`, error);
        }
      }
      
      if (result.fixedViolations > 0) {
        result.fixedCollections.push(collectionName);
      }
      
    } catch (error) {
      result.failedFixes += collectionViolations.length;
      result.errors.push(`Error processing ${collectionName}: ${error}`);
      console.error(`❌ Error processing ${collectionName}:`, error);
    }
  }

  result.success = result.fixedViolations > 0 && result.failedFixes === 0;
  
  console.log(`🎯 Fix complete: ${result.fixedViolations} fixed, ${result.failedFixes} failed`);
  
  return result;
}

/**
 * Complete tenant isolation fix workflow
 */
export async function fixAllTenantIsolationViolations(
  defaultTenantId: string = 'evexa-super-admin-tenant'
): Promise<TenantIsolationFixResult> {
  console.log('🚨 Starting complete tenant isolation fix...');
  
  // Step 1: Detect violations
  const { violations } = await detectTenantIsolationViolations();
  
  if (violations.length === 0) {
    console.log('✅ No tenant isolation violations found!');
    return {
      success: true,
      totalViolations: 0,
      fixedViolations: 0,
      failedFixes: 0,
      errors: [],
      fixedCollections: []
    };
  }
  
  // Step 2: Fix violations
  const result = await fixTenantIsolationViolations(violations, defaultTenantId);
  
  console.log('🎯 Tenant isolation fix complete:', result);
  
  return result;
}

/**
 * Verify tenant isolation after fixes
 */
export async function verifyTenantIsolation(): Promise<{
  isValid: boolean;
  totalDocuments: number;
  violationsRemaining: number;
  collections: Record<string, { total: number; violations: number }>;
}> {
  console.log('🔍 Verifying tenant isolation...');
  
  const collections: Record<string, { total: number; violations: number }> = {};
  let totalDocuments = 0;
  let violationsRemaining = 0;

  for (const collectionName of Object.values(COLLECTIONS)) {
    try {
      const collectionRef = collection(db, collectionName);
      const snapshot = await getDocs(collectionRef);
      
      let violations = 0;
      
      snapshot.docs.forEach(docSnapshot => {
        const data = docSnapshot.data();
        if (!data.tenantId || typeof data.tenantId !== 'string') {
          violations++;
        }
      });
      
      collections[collectionName] = {
        total: snapshot.docs.length,
        violations
      };
      
      totalDocuments += snapshot.docs.length;
      violationsRemaining += violations;
      
    } catch (error) {
      console.error(`Error verifying ${collectionName}:`, error);
    }
  }

  const isValid = violationsRemaining === 0;
  
  console.log(`🎯 Verification complete: ${isValid ? 'VALID' : 'INVALID'} (${violationsRemaining} violations remaining)`);
  
  return {
    isValid,
    totalDocuments,
    violationsRemaining,
    collections
  };
}
