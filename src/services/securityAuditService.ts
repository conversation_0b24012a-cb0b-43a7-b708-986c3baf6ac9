/**
 * Security Audit Service
 * Comprehensive security auditing and vulnerability assessment
 */

import {
  collection,
  doc,
  query,
  where,
  getDocs,
  getDoc,
  limit,
  orderBy,
  collectionGroup
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { getCurrentUser, getCurrentTenant } from '@/lib/auth-utils';

export interface SecurityVulnerability {
  id: string;
  title: string;
  description: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  category: 'authentication' | 'authorization' | 'data_exposure' | 'injection' | 'configuration';
  affected: string[];
  recommendations: string[];
  cveReference?: string;
}

export interface SecurityAuditResult {
  auditId: string;
  timestamp: Date;
  tenantId: string;
  overallScore: number;
  vulnerabilities: SecurityVulnerability[];
  summary: {
    critical: number;
    high: number;
    medium: number;
    low: number;
    total: number;
  };
  recommendations: string[];
}

export class SecurityAuditService {
  private auditId: string;
  private vulnerabilities: SecurityVulnerability[] = [];

  constructor() {
    this.auditId = `audit-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Run comprehensive security audit
   */
  async runSecurityAudit(): Promise<SecurityAuditResult> {
    const startTime = Date.now();
    this.vulnerabilities = [];

    const currentUser = await getCurrentUser();
    const currentTenant = await getCurrentTenant();

    if (!currentUser || !currentTenant) {
      throw new Error('Authentication required for security audit');
    }

    // Run all security checks
    await Promise.all([
      this.checkDataExposure(),
      this.checkAuthenticationWeaknesses(),
      this.checkAuthorizationFlaws(),
      this.checkConfigurationIssues(),
      this.checkInjectionVulnerabilities(),
      this.checkTenantIsolationBreaches()
    ]);

    // Calculate security score
    const overallScore = this.calculateSecurityScore();

    // Generate summary
    const summary = {
      critical: this.vulnerabilities.filter(v => v.severity === 'critical').length,
      high: this.vulnerabilities.filter(v => v.severity === 'high').length,
      medium: this.vulnerabilities.filter(v => v.severity === 'medium').length,
      low: this.vulnerabilities.filter(v => v.severity === 'low').length,
      total: this.vulnerabilities.length
    };

    // Generate recommendations
    const recommendations = this.generateRecommendations();

    return {
      auditId: this.auditId,
      timestamp: new Date(),
      tenantId: currentTenant.id,
      overallScore,
      vulnerabilities: this.vulnerabilities,
      summary,
      recommendations
    };
  }

  /**
   * Check for data exposure vulnerabilities
   */
  private async checkDataExposure(): Promise<void> {
    try {
      // Check for documents without proper tenantId
      const collections = [
        COLLECTIONS.EXHIBITIONS,
        COLLECTIONS.USER_PROFILES,
        COLLECTIONS.LEAD_CONTACTS,
        COLLECTIONS.VENDOR_PROFILES
      ];

      for (const collectionName of collections) {
        const q = query(collection(db, collectionName), limit(100));
        const snapshot = await getDocs(q);

        let documentsWithoutTenantId = 0;
        let documentsWithSensitiveData = 0;

        snapshot.docs.forEach(doc => {
          const data = doc.data();
          
          // Check for missing tenantId
          if (!data.tenantId) {
            documentsWithoutTenantId++;
          }

          // Check for exposed sensitive data
          const sensitiveFields = ['password', 'ssn', 'creditCard', 'bankAccount', 'apiKey'];
          sensitiveFields.forEach(field => {
            if (data[field] && typeof data[field] === 'string' && data[field].length > 0) {
              // Check if field appears to be plain text
              if (!data[field].startsWith('$') && !data[field].includes('encrypted:')) {
                documentsWithSensitiveData++;
              }
            }
          });
        });

        if (documentsWithoutTenantId > 0) {
          this.vulnerabilities.push({
            id: `data-exposure-${collectionName}-tenantid`,
            title: 'Documents Missing Tenant ID',
            description: `${documentsWithoutTenantId} documents in ${collectionName} collection lack proper tenant isolation`,
            severity: 'critical',
            category: 'data_exposure',
            affected: [collectionName],
            recommendations: [
              'Add tenantId field to all documents',
              'Implement data migration script',
              'Add validation rules to prevent documents without tenantId'
            ]
          });
        }

        if (documentsWithSensitiveData > 0) {
          this.vulnerabilities.push({
            id: `data-exposure-${collectionName}-sensitive`,
            title: 'Sensitive Data Exposure',
            description: `${documentsWithSensitiveData} documents contain unencrypted sensitive data`,
            severity: 'high',
            category: 'data_exposure',
            affected: [collectionName],
            recommendations: [
              'Encrypt sensitive fields before storage',
              'Implement field-level encryption',
              'Review data classification policies'
            ]
          });
        }
      }
    } catch (error) {
      console.error('Error checking data exposure:', error);
    }
  }

  /**
   * Check for authentication weaknesses
   */
  private async checkAuthenticationWeaknesses(): Promise<void> {
    try {
      const currentUser = await getCurrentUser();
      
      if (!currentUser) {
        this.vulnerabilities.push({
          id: 'auth-no-user',
          title: 'No Authenticated User',
          description: 'Security audit running without proper authentication',
          severity: 'critical',
          category: 'authentication',
          affected: ['authentication_system'],
          recommendations: [
            'Ensure proper authentication before accessing sensitive operations',
            'Implement session validation',
            'Add authentication middleware'
          ]
        });
        return;
      }

      // Check for weak session management
      if (!currentUser.lastLoginAt) {
        this.vulnerabilities.push({
          id: 'auth-session-tracking',
          title: 'Weak Session Tracking',
          description: 'User sessions are not properly tracked',
          severity: 'medium',
          category: 'authentication',
          affected: ['session_management'],
          recommendations: [
            'Implement proper session tracking',
            'Add last login timestamp',
            'Monitor session activity'
          ]
        });
      }

      // Check for missing email verification
      if (!currentUser.isEmailVerified) {
        this.vulnerabilities.push({
          id: 'auth-email-verification',
          title: 'Unverified Email Address',
          description: 'User email address is not verified',
          severity: 'medium',
          category: 'authentication',
          affected: ['email_verification'],
          recommendations: [
            'Implement email verification flow',
            'Require email verification for sensitive operations',
            'Send verification reminders'
          ]
        });
      }
    } catch (error) {
      console.error('Error checking authentication:', error);
    }
  }

  /**
   * Check for authorization flaws
   */
  private async checkAuthorizationFlaws(): Promise<void> {
    try {
      const currentUser = await getCurrentUser();
      
      if (!currentUser) return;

      // Check for missing role assignment
      if (!currentUser.role || currentUser.role === '') {
        this.vulnerabilities.push({
          id: 'authz-missing-role',
          title: 'Missing User Role',
          description: 'User has no role assigned, potentially allowing unauthorized access',
          severity: 'high',
          category: 'authorization',
          affected: ['role_management'],
          recommendations: [
            'Assign appropriate role to user',
            'Implement default role assignment',
            'Add role validation middleware'
          ]
        });
      }

      // Check for overprivileged users
      if (currentUser.role === 'super_admin') {
        // Count super admin users
        const superAdminQuery = query(
          collection(db, COLLECTIONS.USER_PROFILES),
          where('role', '==', 'super_admin'),
          limit(10)
        );
        const superAdminSnapshot = await getDocs(superAdminQuery);

        if (superAdminSnapshot.size > 3) {
          this.vulnerabilities.push({
            id: 'authz-excessive-admins',
            title: 'Excessive Super Admin Users',
            description: `${superAdminSnapshot.size} super admin users found - potential security risk`,
            severity: 'medium',
            category: 'authorization',
            affected: ['user_management'],
            recommendations: [
              'Review super admin user list',
              'Implement principle of least privilege',
              'Regular access reviews'
            ]
          });
        }
      }
    } catch (error) {
      console.error('Error checking authorization:', error);
    }
  }

  /**
   * Check for configuration issues
   */
  private async checkConfigurationIssues(): Promise<void> {
    try {
      // Check for development configurations in production
      if (typeof window !== 'undefined') {
        // Check for debug flags
        if ((window as any).__EVEXA_DEBUG__ === true) {
          this.vulnerabilities.push({
            id: 'config-debug-enabled',
            title: 'Debug Mode Enabled',
            description: 'Debug mode is enabled in production environment',
            severity: 'medium',
            category: 'configuration',
            affected: ['application_configuration'],
            recommendations: [
              'Disable debug mode in production',
              'Review environment configuration',
              'Implement proper build configurations'
            ]
          });
        }

        // Check for exposed API keys
        const scripts = document.getElementsByTagName('script');
        for (let i = 0; i < scripts.length; i++) {
          const scriptContent = scripts[i].innerHTML;
          if (scriptContent.includes('apiKey') || scriptContent.includes('API_KEY')) {
            this.vulnerabilities.push({
              id: 'config-exposed-api-keys',
              title: 'Exposed API Keys',
              description: 'API keys found in client-side code',
              severity: 'high',
              category: 'configuration',
              affected: ['api_security'],
              recommendations: [
                'Move API keys to server-side',
                'Use environment variables',
                'Implement proper secret management'
              ]
            });
            break;
          }
        }
      }
    } catch (error) {
      console.error('Error checking configuration:', error);
    }
  }

  /**
   * Check for injection vulnerabilities
   */
  private async checkInjectionVulnerabilities(): Promise<void> {
    // This would typically involve testing input validation
    // For now, we'll check for potential NoSQL injection patterns
    
    try {
      // Check for documents with suspicious query patterns
      const suspiciousPatterns = ['$where', '$regex', 'javascript:', 'eval('];
      
      const collections = [COLLECTIONS.EXHIBITIONS, COLLECTIONS.LEAD_CONTACTS];
      
      for (const collectionName of collections) {
        const q = query(collection(db, collectionName), limit(50));
        const snapshot = await getDocs(q);

        let suspiciousDocuments = 0;

        snapshot.docs.forEach(doc => {
          const data = JSON.stringify(doc.data());
          suspiciousPatterns.forEach(pattern => {
            if (data.includes(pattern)) {
              suspiciousDocuments++;
            }
          });
        });

        if (suspiciousDocuments > 0) {
          this.vulnerabilities.push({
            id: `injection-${collectionName}`,
            title: 'Potential Injection Vulnerability',
            description: `${suspiciousDocuments} documents contain suspicious patterns that may indicate injection attempts`,
            severity: 'high',
            category: 'injection',
            affected: [collectionName],
            recommendations: [
              'Implement input validation',
              'Use parameterized queries',
              'Sanitize user input',
              'Review data for malicious content'
            ]
          });
        }
      }
    } catch (error) {
      console.error('Error checking injection vulnerabilities:', error);
    }
  }

  /**
   * Check for tenant isolation breaches
   */
  private async checkTenantIsolationBreaches(): Promise<void> {
    try {
      const currentTenant = await getCurrentTenant();
      if (!currentTenant) return;

      // Check for cross-tenant data leakage
      const collections = [COLLECTIONS.EXHIBITIONS, COLLECTIONS.USER_PROFILES];
      
      for (const collectionName of collections) {
        const q = query(collection(db, collectionName), limit(100));
        const snapshot = await getDocs(q);

        let crossTenantDocuments = 0;

        snapshot.docs.forEach(doc => {
          const data = doc.data();
          if (data.tenantId && data.tenantId !== currentTenant.id) {
            crossTenantDocuments++;
          }
        });

        if (crossTenantDocuments > 0) {
          this.vulnerabilities.push({
            id: `tenant-isolation-${collectionName}`,
            title: 'Tenant Isolation Breach',
            description: `${crossTenantDocuments} documents from other tenants are accessible`,
            severity: 'critical',
            category: 'authorization',
            affected: [collectionName],
            recommendations: [
              'Implement proper tenant filtering',
              'Review Firestore security rules',
              'Add tenant validation middleware',
              'Audit all database queries'
            ]
          });
        }
      }
    } catch (error) {
      console.error('Error checking tenant isolation:', error);
    }
  }

  /**
   * Calculate overall security score
   */
  private calculateSecurityScore(): number {
    if (this.vulnerabilities.length === 0) {
      return 100;
    }

    const weights = {
      critical: 25,
      high: 15,
      medium: 8,
      low: 3
    };

    const totalDeductions = this.vulnerabilities.reduce((total, vuln) => {
      return total + weights[vuln.severity];
    }, 0);

    const score = Math.max(0, 100 - totalDeductions);
    return Math.round(score);
  }

  /**
   * Generate security recommendations
   */
  private generateRecommendations(): string[] {
    const recommendations = new Set<string>();

    // Add general recommendations based on vulnerabilities found
    const criticalVulns = this.vulnerabilities.filter(v => v.severity === 'critical');
    const highVulns = this.vulnerabilities.filter(v => v.severity === 'high');

    if (criticalVulns.length > 0) {
      recommendations.add('Address all critical vulnerabilities immediately');
      recommendations.add('Implement emergency security patches');
    }

    if (highVulns.length > 0) {
      recommendations.add('Prioritize high-severity vulnerabilities');
      recommendations.add('Conduct security code review');
    }

    // Add specific recommendations from vulnerabilities
    this.vulnerabilities.forEach(vuln => {
      vuln.recommendations.forEach(rec => recommendations.add(rec));
    });

    // Add general security best practices
    recommendations.add('Implement regular security audits');
    recommendations.add('Keep all dependencies up to date');
    recommendations.add('Enable security monitoring and alerting');
    recommendations.add('Conduct penetration testing');
    recommendations.add('Implement security training for development team');

    return Array.from(recommendations);
  }
}

export const securityAuditService = new SecurityAuditService();
