import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  getDocs, 
  query, 
  where, 
  orderBy,
  Timestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

export interface ComplianceRequirement {
  id?: string;
  country: string;
  countryCode: string;
  region: string;
  category: 'data_protection' | 'financial' | 'employment' | 'tax' | 'business_registration' | 'industry_specific';
  title: string;
  description: string;
  requirements: string[];
  penalties: string[];
  implementationSteps: string[];
  documentationRequired: string[];
  renewalFrequency: 'annual' | 'biannual' | 'quarterly' | 'monthly' | 'one_time';
  complianceDeadline?: Date;
  lastUpdated: Date;
  source: string;
  authorityContact: {
    name: string;
    email?: string;
    phone?: string;
    website?: string;
  };
  status: 'active' | 'pending' | 'deprecated';
}

export interface ComplianceAssessment {
  id?: string;
  tenantId: string;
  country: string;
  assessmentDate: Date;
  overallScore: number; // 0-100
  categoryScores: {
    dataProtection: number;
    financial: number;
    employment: number;
    tax: number;
    businessRegistration: number;
    industrySpecific: number;
  };
  findings: Array<{
    requirementId: string;
    status: 'compliant' | 'non_compliant' | 'partially_compliant' | 'not_applicable';
    notes: string;
    evidence?: string[];
    actionRequired?: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
  }>;
  recommendations: string[];
  nextAssessmentDate: Date;
  assessedBy: string;
  approvedBy?: string;
}

export interface ComplianceAction {
  id?: string;
  tenantId: string;
  requirementId: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  assignedTo: string;
  dueDate: Date;
  completedDate?: Date;
  estimatedCost?: number;
  actualCost?: number;
  currency: string;
  progress: number; // 0-100
  notes: string[];
  attachments: string[];
  createdAt: Date;
  updatedAt: Date;
}

class InternationalComplianceService {
  private requirementsCollection = 'compliance_requirements';
  private assessmentsCollection = 'compliance_assessments';
  private actionsCollection = 'compliance_actions';

  // Compliance Requirements Management
  async getComplianceRequirementsByCountry(countryCode: string): Promise<ComplianceRequirement[]> {
    try {
      const q = query(
        collection(db, this.requirementsCollection),
        where('countryCode', '==', countryCode),
        where('status', '==', 'active'),
        orderBy('category')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          complianceDeadline: data.complianceDeadline?.toDate(),
          lastUpdated: data.lastUpdated.toDate()
        } as ComplianceRequirement;
      });
    } catch (error) {
      console.error('Error fetching compliance requirements:', error);
      return this.getMockComplianceRequirements(countryCode);
    }
  }

  async getComplianceRequirementsByRegion(region: string): Promise<ComplianceRequirement[]> {
    try {
      const q = query(
        collection(db, this.requirementsCollection),
        where('region', '==', region),
        where('status', '==', 'active'),
        orderBy('country')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          complianceDeadline: data.complianceDeadline?.toDate(),
          lastUpdated: data.lastUpdated.toDate()
        } as ComplianceRequirement;
      });
    } catch (error) {
      console.error('Error fetching regional compliance requirements:', error);
      return this.getMockRegionalRequirements(region);
    }
  }

  // Compliance Assessment Management
  async createComplianceAssessment(assessment: Omit<ComplianceAssessment, 'id'>): Promise<ComplianceAssessment> {
    try {
      const docRef = await addDoc(collection(db, this.assessmentsCollection), {
        ...assessment,
        assessmentDate: Timestamp.fromDate(assessment.assessmentDate),
        nextAssessmentDate: Timestamp.fromDate(assessment.nextAssessmentDate)
      });

      return { ...assessment, id: docRef.id };
    } catch (error) {
      console.error('Error creating compliance assessment:', error);
      throw error;
    }
  }

  async getComplianceAssessmentsByTenant(tenantId: string): Promise<ComplianceAssessment[]> {
    try {
      const q = query(
        collection(db, this.assessmentsCollection),
        where('tenantId', '==', tenantId),
        orderBy('assessmentDate', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          assessmentDate: data.assessmentDate.toDate(),
          nextAssessmentDate: data.nextAssessmentDate.toDate()
        } as ComplianceAssessment;
      });
    } catch (error) {
      console.error('Error fetching compliance assessments:', error);
      return [];
    }
  }

  // Compliance Action Management
  async createComplianceAction(action: Omit<ComplianceAction, 'id' | 'createdAt' | 'updatedAt'>): Promise<ComplianceAction> {
    try {
      const now = new Date();
      const actionData: Omit<ComplianceAction, 'id'> = {
        ...action,
        createdAt: now,
        updatedAt: now
      };

      const docRef = await addDoc(collection(db, this.actionsCollection), {
        ...actionData,
        dueDate: Timestamp.fromDate(action.dueDate),
        completedDate: action.completedDate ? Timestamp.fromDate(action.completedDate) : null,
        createdAt: Timestamp.fromDate(now),
        updatedAt: Timestamp.fromDate(now)
      });

      return { ...actionData, id: docRef.id };
    } catch (error) {
      console.error('Error creating compliance action:', error);
      throw error;
    }
  }

  async getComplianceActionsByTenant(tenantId: string): Promise<ComplianceAction[]> {
    try {
      const q = query(
        collection(db, this.actionsCollection),
        where('tenantId', '==', tenantId),
        orderBy('dueDate', 'asc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          dueDate: data.dueDate.toDate(),
          completedDate: data.completedDate?.toDate(),
          createdAt: data.createdAt.toDate(),
          updatedAt: data.updatedAt.toDate()
        } as ComplianceAction;
      });
    } catch (error) {
      console.error('Error fetching compliance actions:', error);
      return [];
    }
  }

  // Mock Data Methods (fallback when Firebase is not available)
  private getMockComplianceRequirements(countryCode: string): ComplianceRequirement[] {
    const mockRequirements: ComplianceRequirement[] = [
      {
        id: `req_${countryCode}_001`,
        country: countryCode === 'US' ? 'United States' : countryCode === 'DE' ? 'Germany' : 'United Kingdom',
        countryCode,
        region: countryCode === 'US' ? 'North America' : 'Europe',
        category: 'data_protection',
        title: countryCode === 'US' ? 'CCPA Compliance' : countryCode === 'DE' ? 'GDPR Compliance' : 'UK GDPR Compliance',
        description: 'Data protection and privacy compliance requirements',
        requirements: ['Privacy policy implementation', 'Data subject rights', 'Consent management'],
        penalties: ['Fines up to 4% of annual revenue', 'Legal action', 'Reputation damage'],
        implementationSteps: ['Conduct privacy audit', 'Update privacy policies', 'Implement consent system'],
        documentationRequired: ['Privacy policy', 'Data processing agreements', 'Consent records'],
        renewalFrequency: 'annual',
        lastUpdated: new Date(),
        source: 'Official government website',
        authorityContact: {
          name: countryCode === 'US' ? 'California Attorney General' : 'Data Protection Authority',
          website: countryCode === 'US' ? 'https://oag.ca.gov' : 'https://gdpr.eu'
        },
        status: 'active'
      }
    ];

    return mockRequirements;
  }

  private getMockRegionalRequirements(region: string): ComplianceRequirement[] {
    // Return mock requirements for the specified region
    return [];
  }

  // Utility Methods
  async generateComplianceReport(tenantId: string, country: string): Promise<{
    overallScore: number;
    totalRequirements: number;
    compliantRequirements: number;
    pendingActions: number;
    criticalIssues: number;
    recommendations: string[];
  }> {
    const [requirements, assessments, actions] = await Promise.all([
      this.getComplianceRequirementsByCountry(country),
      this.getComplianceAssessmentsByTenant(tenantId),
      this.getComplianceActionsByTenant(tenantId)
    ]);

    const latestAssessment = assessments[0];
    const pendingActions = actions.filter(a => a.status === 'pending' || a.status === 'in_progress').length;
    const criticalIssues = actions.filter(a => a.priority === 'critical' && a.status !== 'completed').length;

    return {
      overallScore: latestAssessment?.overallScore || 0,
      totalRequirements: requirements.length,
      compliantRequirements: latestAssessment?.findings.filter(f => f.status === 'compliant').length || 0,
      pendingActions,
      criticalIssues,
      recommendations: latestAssessment?.recommendations || []
    };
  }
}

export const internationalComplianceService = new InternationalComplianceService();
