/**
 * Invitation Registration Service
 * Handles user registration flow for invited users with persona assignment and tenant association
 */

import { 
  createUserWithEmailAndPassword,
  updateProfile,
  sendEmailVerification,
  User as FirebaseUser
} from 'firebase/auth';
import { 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  serverTimestamp,
  runTransaction
} from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { InvitationManagementService } from './invitationManagementService';
import { validateTenantId } from './tenantIdHelperService';
import type { EvexUser, UserPersona } from '@/types/firestore';

// ===== TYPES =====

export interface InvitationRegistrationRequest {
  invitationToken: string;
  password: string;
  confirmPassword: string;
  acceptTerms: boolean;
  acceptPrivacy: boolean;
  profileData?: {
    phone?: string;
    timezone?: string;
    language?: string;
    profileImageUrl?: string;
  };
}

export interface InvitationRegistrationResult {
  success: boolean;
  user?: EvexUser;
  firebaseUser?: FirebaseUser;
  error?: string;
  requiresEmailVerification?: boolean;
}

export interface InvitationValidationResult {
  isValid: boolean;
  invitation?: any;
  error?: string;
  expired?: boolean;
}

// ===== INVITATION REGISTRATION SERVICE =====

export class InvitationRegistrationService {
  
  /**
   * Validate invitation token before registration
   */
  static async validateInvitationToken(token: string): Promise<InvitationValidationResult> {
    try {
      // Get invitation details from API (public endpoint)
      const response = await fetch(`/api/invitations/${token}`);
      
      if (!response.ok) {
        return {
          isValid: false,
          error: 'Invitation not found or invalid'
        };
      }

      const invitation = await response.json();

      // Check invitation status
      if (invitation.status !== 'pending') {
        return {
          isValid: false,
          error: 'This invitation is no longer valid',
          invitation
        };
      }

      // Check expiration
      const expiresAt = new Date(invitation.expiresAt);
      if (expiresAt < new Date()) {
        return {
          isValid: false,
          error: 'This invitation has expired',
          expired: true,
          invitation
        };
      }

      return {
        isValid: true,
        invitation
      };

    } catch (error) {
      console.error('Error validating invitation token:', error);
      return {
        isValid: false,
        error: 'Failed to validate invitation'
      };
    }
  }

  /**
   * Register user from invitation
   */
  static async registerFromInvitation(
    request: InvitationRegistrationRequest
  ): Promise<InvitationRegistrationResult> {
    try {
      // Validate input
      const validationError = this.validateRegistrationRequest(request);
      if (validationError) {
        return {
          success: false,
          error: validationError
        };
      }

      // Validate invitation token
      const tokenValidation = await this.validateInvitationToken(request.invitationToken);
      if (!tokenValidation.isValid) {
        return {
          success: false,
          error: tokenValidation.error
        };
      }

      const invitation = tokenValidation.invitation!;

      // Create Firebase Auth user
      const firebaseUser = await this.createFirebaseUser(
        invitation.email,
        request.password,
        `${invitation.firstName} ${invitation.lastName}`
      );

      // Create user profile in Firestore
      const userProfile = await this.createUserProfile(
        firebaseUser,
        invitation,
        request.profileData
      );

      // Accept the invitation
      const invitationService = new InvitationManagementService(invitation.tenantId);
      const acceptResult = await invitationService.acceptInvitation(
        request.invitationToken,
        firebaseUser.uid
      );

      if (!acceptResult.success) {
        // If invitation acceptance fails, we should clean up the created user
        console.error('Failed to accept invitation after user creation:', acceptResult.error);
        // Note: In production, you might want to implement cleanup logic here
      }

      // Send email verification if required
      let requiresEmailVerification = false;
      if (!firebaseUser.emailVerified) {
        try {
          await sendEmailVerification(firebaseUser);
          requiresEmailVerification = true;
        } catch (emailError) {
          console.warn('Failed to send email verification:', emailError);
        }
      }

      return {
        success: true,
        user: userProfile,
        firebaseUser,
        requiresEmailVerification
      };

    } catch (error) {
      console.error('Error registering user from invitation:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Registration failed'
      };
    }
  }

  /**
   * Create Firebase Auth user
   */
  private static async createFirebaseUser(
    email: string,
    password: string,
    displayName: string
  ): Promise<FirebaseUser> {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Update profile with display name
      await updateProfile(user, {
        displayName
      });

      return user;

    } catch (error: any) {
      console.error('Error creating Firebase user:', error);
      
      // Provide user-friendly error messages
      switch (error.code) {
        case 'auth/email-already-in-use':
          throw new Error('An account with this email already exists');
        case 'auth/weak-password':
          throw new Error('Password is too weak. Please choose a stronger password');
        case 'auth/invalid-email':
          throw new Error('Invalid email address');
        default:
          throw new Error('Failed to create account. Please try again');
      }
    }
  }

  /**
   * Create user profile in Firestore
   */
  private static async createUserProfile(
    firebaseUser: FirebaseUser,
    invitation: any,
    profileData?: InvitationRegistrationRequest['profileData']
  ): Promise<EvexUser> {
    try {
      validateTenantId(invitation.tenantId);

      // Get persona information
      const persona = await this.getPersonaById(invitation.personaId);

      const userProfile: EvexUser = {
        id: firebaseUser.uid,
        email: firebaseUser.email!,
        displayName: firebaseUser.displayName!,
        firstName: invitation.firstName,
        lastName: invitation.lastName,
        role: this.mapPersonaToRole(persona),
        tenantId: invitation.tenantId,
        personaId: invitation.personaId,
        status: 'active',
        department: persona?.name || 'General',
        jobTitle: persona?.name || 'Team Member',
        profileImageUrl: profileData?.profileImageUrl || firebaseUser.photoURL || '',
        phone: profileData?.phone || '',
        isEmailVerified: firebaseUser.emailVerified,
        groupIds: [],
        workloadCapacityType: 'taskCount',
        workloadCapacityValue: 20,
        preferences: {
          theme: 'system',
          notifications: true,
          language: profileData?.language || 'en',
          timezone: profileData?.timezone || 'UTC'
        },
        invitationAcceptedAt: new Date(),
        invitedBy: invitation.invitedBy,
        createdAt: new Date(),
        updatedAt: new Date(),
        lastLoginAt: new Date()
      };

      // Save user profile to Firestore
      await setDoc(doc(db, COLLECTIONS.USER_PROFILES, firebaseUser.uid), {
        ...userProfile,
        invitationAcceptedAt: serverTimestamp(),
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        lastLoginAt: serverTimestamp()
      });

      return userProfile;

    } catch (error) {
      console.error('Error creating user profile:', error);
      throw new Error('Failed to create user profile');
    }
  }

  /**
   * Get persona by ID
   */
  private static async getPersonaById(personaId: string): Promise<UserPersona | null> {
    try {
      const personaDoc = await getDoc(doc(db, COLLECTIONS.USER_PERSONAS, personaId));
      if (personaDoc.exists()) {
        return { id: personaDoc.id, ...personaDoc.data() } as UserPersona;
      }
      return null;
    } catch (error) {
      console.error('Error getting persona:', error);
      return null;
    }
  }

  /**
   * Map persona to user role
   */
  private static mapPersonaToRole(persona: UserPersona | null): string {
    if (!persona) return 'user';
    
    // Map persona names to roles
    const roleMapping: Record<string, string> = {
      'Administrator': 'admin',
      'Manager': 'management',
      'Exhibition Manager': 'management',
      'Marketing Manager': 'management',
      'Team Lead': 'management',
      'Senior': 'senior',
      'Specialist': 'specialist',
      'Coordinator': 'coordinator'
    };

    return roleMapping[persona.name] || 'user';
  }

  /**
   * Validate registration request
   */
  private static validateRegistrationRequest(request: InvitationRegistrationRequest): string | null {
    if (!request.invitationToken) {
      return 'Invitation token is required';
    }

    if (!request.password || request.password.length < 8) {
      return 'Password must be at least 8 characters long';
    }

    if (request.password !== request.confirmPassword) {
      return 'Passwords do not match';
    }

    if (!request.acceptTerms) {
      return 'You must accept the Terms of Service';
    }

    if (!request.acceptPrivacy) {
      return 'You must accept the Privacy Policy';
    }

    // Password strength validation
    const hasUpperCase = /[A-Z]/.test(request.password);
    const hasLowerCase = /[a-z]/.test(request.password);
    const hasNumbers = /\d/.test(request.password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(request.password);

    if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
      return 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character';
    }

    return null;
  }

  /**
   * Update user profile after registration
   */
  static async updateUserProfile(
    userId: string,
    updates: Partial<EvexUser>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const userRef = doc(db, COLLECTIONS.USER_PROFILES, userId);
      
      await updateDoc(userRef, {
        ...updates,
        updatedAt: serverTimestamp()
      });

      return { success: true };

    } catch (error) {
      console.error('Error updating user profile:', error);
      return {
        success: false,
        error: 'Failed to update user profile'
      };
    }
  }

  /**
   * Get user profile by ID
   */
  static async getUserProfile(userId: string): Promise<EvexUser | null> {
    try {
      const userDoc = await getDoc(doc(db, COLLECTIONS.USER_PROFILES, userId));
      if (userDoc.exists()) {
        return { id: userDoc.id, ...userDoc.data() } as EvexUser;
      }
      return null;
    } catch (error) {
      console.error('Error getting user profile:', error);
      return null;
    }
  }
}
