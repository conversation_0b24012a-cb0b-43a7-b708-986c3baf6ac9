/**
 * Nested Collection Migration Service
 * Migrates data from nested subcollections to flat root-level collections
 */

import { 
  collection,
  doc,
  getDocs,
  writeBatch,
  query,
  orderBy,
  collectionGroup,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { validateTenantId } from './tenantIdHelperService';
import type { TenantAwareEntity } from '@/types/firestore';

// ===== NESTED MIGRATION CONFIGURATION =====

export interface NestedMigrationConfig {
  sourcePattern: string; // e.g., "exhibitions/{exhibitionId}/tasks"
  targetCollection: string;
  description: string;
  batchSize: number;
  preserveOriginal: boolean;
  parentIdField: string; // Field to store the parent document ID
  requiredFields: string[];
  dataTransformations?: Array<{
    field: string;
    transform: (value: any, parentId: string) => any;
  }>;
}

export interface NestedMigrationResult {
  success: boolean;
  sourcePattern: string;
  targetCollection: string;
  documentsProcessed: number;
  documentsSkipped: number;
  parentDocuments: number;
  errors: string[];
  duration: number;
  startTime: Date;
  endTime: Date;
}

// ===== NESTED MIGRATION CONFIGURATIONS =====

export const NESTED_MIGRATION_CONFIGS: Record<string, NestedMigrationConfig> = {
  exhibitionTasks: {
    sourcePattern: 'exhibitions/{exhibitionId}/tasks',
    targetCollection: COLLECTIONS.EXHIBITION_TASKS,
    description: 'Migrate nested exhibition tasks to flat exhibition_tasks collection',
    batchSize: 100,
    preserveOriginal: false,
    parentIdField: 'exhibitionId',
    requiredFields: ['title'],
    dataTransformations: [
      {
        field: 'status',
        transform: (value: any) => value || 'pending'
      }
    ]
  },

  exhibitionEvents: {
    sourcePattern: 'exhibitions/{exhibitionId}/events',
    targetCollection: COLLECTIONS.EXHIBITION_EVENTS,
    description: 'Migrate nested exhibition events to flat exhibition_events collection',
    batchSize: 50,
    preserveOriginal: false,
    parentIdField: 'exhibitionId',
    requiredFields: ['name', 'startDate'],
    dataTransformations: [
      {
        field: 'status',
        transform: (value: any) => value || 'planned'
      }
    ]
  },

  exhibitionLeads: {
    sourcePattern: 'exhibitions/{exhibitionId}/leads',
    targetCollection: COLLECTIONS.LEAD_CONTACTS,
    description: 'Migrate nested exhibition leads to flat lead_contacts collection',
    batchSize: 200,
    preserveOriginal: false,
    parentIdField: 'exhibitionId',
    requiredFields: ['email'],
    dataTransformations: [
      {
        field: 'status',
        transform: (value: any) => value || 'new'
      },
      {
        field: 'leadScore',
        transform: (value: any) => typeof value === 'number' ? value : 0
      }
    ]
  },

  exhibitionBudgets: {
    sourcePattern: 'exhibitions/{exhibitionId}/budgets',
    targetCollection: COLLECTIONS.BUDGET_ALLOCATIONS,
    description: 'Migrate nested exhibition budgets to flat budget_allocations collection',
    batchSize: 100,
    preserveOriginal: false,
    parentIdField: 'exhibitionId',
    requiredFields: ['amount'],
    dataTransformations: [
      {
        field: 'activityId',
        transform: (value: any, parentId: string) => value || parentId
      }
    ]
  },

  exhibitionExpenses: {
    sourcePattern: 'exhibitions/{exhibitionId}/expenses',
    targetCollection: COLLECTIONS.EXPENSE_RECORDS,
    description: 'Migrate nested exhibition expenses to flat expense_records collection',
    batchSize: 200,
    preserveOriginal: false,
    parentIdField: 'exhibitionId',
    requiredFields: ['amount'],
    dataTransformations: [
      {
        field: 'activityId',
        transform: (value: any, parentId: string) => value || parentId
      },
      {
        field: 'status',
        transform: (value: any) => value || 'pending'
      }
    ]
  },

  exhibitionVendors: {
    sourcePattern: 'exhibitions/{exhibitionId}/vendors',
    targetCollection: COLLECTIONS.VENDOR_PROFILES,
    description: 'Migrate nested exhibition vendors to flat vendor_profiles collection',
    batchSize: 100,
    preserveOriginal: false,
    parentIdField: 'exhibitionId',
    requiredFields: ['name'],
    dataTransformations: [
      {
        field: 'status',
        transform: (value: any) => value || 'active'
      }
    ]
  }
};

// ===== NESTED MIGRATION FUNCTIONS =====

/**
 * Migrate a nested collection to flat structure
 */
export async function migrateNestedCollection(
  configKey: string,
  tenantId: string,
  dryRun: boolean = false,
  onProgress?: (progress: { processed: number; total: number; current: string }) => void
): Promise<NestedMigrationResult> {
  const startTime = new Date();
  const config = NESTED_MIGRATION_CONFIGS[configKey];
  
  if (!config) {
    throw new Error(`Nested migration configuration not found for: ${configKey}`);
  }

  validateTenantId(tenantId);

  const result: NestedMigrationResult = {
    success: false,
    sourcePattern: config.sourcePattern,
    targetCollection: config.targetCollection,
    documentsProcessed: 0,
    documentsSkipped: 0,
    parentDocuments: 0,
    errors: [],
    duration: 0,
    startTime,
    endTime: new Date()
  };

  try {
    console.log(`${dryRun ? '[DRY RUN] ' : ''}Starting nested migration: ${config.description}`);

    // Extract collection name from pattern
    const collectionName = config.sourcePattern.split('/').pop()!;
    
    // Use collection group query to get all documents from nested collections
    const collectionGroupRef = collectionGroup(db, collectionName);
    const nestedQuery = query(collectionGroupRef, orderBy('__name__'));
    const nestedSnapshot = await getDocs(nestedQuery);

    if (nestedSnapshot.empty) {
      console.log(`No documents found in nested collection: ${collectionName}`);
      result.success = true;
      return result;
    }

    const totalDocuments = nestedSnapshot.size;
    console.log(`Found ${totalDocuments} nested documents to migrate`);

    // Group documents by parent
    const documentsByParent = new Map<string, any[]>();
    
    for (const nestedDoc of nestedSnapshot.docs) {
      try {
        // Extract parent ID from document path
        const pathParts = nestedDoc.ref.path.split('/');
        const parentId = pathParts[pathParts.length - 3]; // Get parent document ID
        
        if (!documentsByParent.has(parentId)) {
          documentsByParent.set(parentId, []);
        }
        
        documentsByParent.get(parentId)!.push({
          id: nestedDoc.id,
          data: nestedDoc.data(),
          parentId: parentId
        });
        
      } catch (error) {
        result.errors.push(`Error processing nested document ${nestedDoc.id}: ${error}`);
        result.documentsSkipped++;
      }
    }

    result.parentDocuments = documentsByParent.size;
    console.log(`Documents grouped under ${result.parentDocuments} parent documents`);

    // Process documents in batches
    const batches = [];
    let currentBatch = [];
    let processedCount = 0;

    for (const [parentId, documents] of documentsByParent) {
      for (const docInfo of documents) {
        try {
          // Validate required fields
          const missingFields = config.requiredFields.filter(field => !docInfo.data[field]);
          if (missingFields.length > 0) {
            result.errors.push(`Document ${docInfo.id} missing required fields: ${missingFields.join(', ')}`);
            result.documentsSkipped++;
            continue;
          }

          // Transform data
          const transformedData = await transformNestedDocumentData(
            docInfo.data, 
            docInfo.id, 
            docInfo.parentId, 
            config, 
            tenantId
          );
          
          currentBatch.push({
            id: docInfo.id,
            data: transformedData
          });

          if (currentBatch.length >= config.batchSize) {
            batches.push([...currentBatch]);
            currentBatch = [];
          }

          processedCount++;
          
          if (onProgress) {
            onProgress({
              processed: processedCount,
              total: totalDocuments,
              current: `${parentId}/${docInfo.id}`
            });
          }

        } catch (error) {
          result.errors.push(`Error processing nested document ${docInfo.id}: ${error}`);
          result.documentsSkipped++;
        }
      }
    }

    // Add remaining documents to final batch
    if (currentBatch.length > 0) {
      batches.push(currentBatch);
    }

    // Execute batches
    if (!dryRun) {
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        await executeNestedMigrationBatch(batch, config.targetCollection);
        console.log(`Completed batch ${i + 1}/${batches.length} (${batch.length} documents)`);
      }
    } else {
      console.log(`[DRY RUN] Would migrate ${processedCount} documents in ${batches.length} batches`);
    }

    result.documentsProcessed = processedCount;
    result.success = true;

  } catch (error) {
    result.errors.push(`Nested migration failed: ${error}`);
    console.error(`Nested migration error for ${config.sourcePattern}:`, error);
  }

  result.endTime = new Date();
  result.duration = result.endTime.getTime() - result.startTime.getTime();

  return result;
}

/**
 * Transform nested document data according to migration config
 */
async function transformNestedDocumentData(
  sourceData: any,
  documentId: string,
  parentId: string,
  config: NestedMigrationConfig,
  tenantId: string
): Promise<TenantAwareEntity> {
  const transformedData: any = {
    id: documentId,
    tenantId: tenantId,
    [config.parentIdField]: parentId
  };

  // Copy all source fields
  Object.assign(transformedData, sourceData);

  // Apply data transformations
  if (config.dataTransformations) {
    for (const transformation of config.dataTransformations) {
      if (transformedData[transformation.field] !== undefined) {
        transformedData[transformation.field] = transformation.transform(
          transformedData[transformation.field], 
          parentId
        );
      }
    }
  }

  // Ensure timestamps are properly formatted
  if (sourceData.createdAt) {
    transformedData.createdAt = sourceData.createdAt instanceof Timestamp 
      ? sourceData.createdAt 
      : new Date(sourceData.createdAt);
  } else {
    transformedData.createdAt = serverTimestamp();
  }

  if (sourceData.updatedAt) {
    transformedData.updatedAt = sourceData.updatedAt instanceof Timestamp 
      ? sourceData.updatedAt 
      : new Date(sourceData.updatedAt);
  } else {
    transformedData.updatedAt = serverTimestamp();
  }

  return transformedData as TenantAwareEntity;
}

/**
 * Execute a batch of nested document migrations
 */
async function executeNestedMigrationBatch(
  batch: Array<{ id: string; data: TenantAwareEntity }>,
  targetCollection: string
): Promise<void> {
  const writeBatchRef = writeBatch(db);
  
  for (const item of batch) {
    const docRef = doc(db, targetCollection, item.id);
    writeBatchRef.set(docRef, item.data);
  }
  
  await writeBatchRef.commit();
}

/**
 * Migrate all configured nested collections
 */
export async function migrateAllNestedCollections(
  tenantId: string,
  dryRun: boolean = false,
  onProgress?: (progress: { current: string; processed: number; total: number }) => void
): Promise<NestedMigrationResult[]> {
  validateTenantId(tenantId);
  
  const configKeys = Object.keys(NESTED_MIGRATION_CONFIGS);
  const results: NestedMigrationResult[] = [];
  
  for (let i = 0; i < configKeys.length; i++) {
    const configKey = configKeys[i];
    
    const result = await migrateNestedCollection(configKey, tenantId, dryRun, (progress) => {
      if (onProgress) {
        onProgress({
          current: `${configKey} (${i + 1}/${configKeys.length})`,
          processed: progress.processed,
          total: progress.total
        });
      }
    });
    
    results.push(result);
  }
  
  return results;
}

/**
 * Get migration summary
 */
export function getMigrationSummary(results: NestedMigrationResult[]): {
  totalProcessed: number;
  totalSkipped: number;
  totalErrors: number;
  totalDuration: number;
  successfulMigrations: number;
} {
  return {
    totalProcessed: results.reduce((sum, r) => sum + r.documentsProcessed, 0),
    totalSkipped: results.reduce((sum, r) => sum + r.documentsSkipped, 0),
    totalErrors: results.reduce((sum, r) => sum + r.errors.length, 0),
    totalDuration: results.reduce((sum, r) => sum + r.duration, 0),
    successfulMigrations: results.filter(r => r.success).length
  };
}
