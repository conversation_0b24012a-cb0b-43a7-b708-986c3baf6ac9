import { doc, getDoc, setDoc, collection, query, where, getDocs, addDoc, Timestamp, orderBy, limit, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// Customer Success Interfaces
export interface CustomerSuccessProfile {
  id: string;
  tenantId: string;
  userId: string;
  healthScore: number; // 0-100
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  onboardingStatus: 'not_started' | 'in_progress' | 'completed' | 'stalled';
  lastActivity: Date;
  totalLogins: number;
  featureAdoption: FeatureAdoption[];
  engagementMetrics: EngagementMetrics;
  supportInteractions: SupportInteraction[];
  milestones: CustomerMilestone[];
  riskFactors: RiskFactor[];
  successPlan: SuccessPlan;
  createdAt: Date;
  updatedAt: Date;
}

export interface FeatureAdoption {
  featureName: string;
  firstUsed?: Date;
  lastUsed?: Date;
  usageCount: number;
  adoptionStatus: 'not_adopted' | 'trial' | 'adopted' | 'power_user';
  timeToAdoption?: number; // days
}

export interface EngagementMetrics {
  dailyActiveUse: boolean;
  weeklyActiveUse: boolean;
  monthlyActiveUse: boolean;
  averageSessionDuration: number; // minutes
  pagesPerSession: number;
  bounceRate: number;
  featureUsageDepth: number; // 1-5 scale
  collaborationLevel: number; // team usage indicator
}

export interface SupportInteraction {
  id: string;
  type: 'ticket' | 'chat' | 'email' | 'call' | 'training';
  subject: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  category: string;
  createdAt: Date;
  resolvedAt?: Date;
  resolutionTime?: number; // hours
  satisfactionRating?: number; // 1-5
  escalated: boolean;
  tags: string[];
}

export interface CustomerMilestone {
  id: string;
  name: string;
  description: string;
  category: 'onboarding' | 'adoption' | 'expansion' | 'renewal';
  targetDate: Date;
  completedDate?: Date;
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  importance: 'low' | 'medium' | 'high' | 'critical';
  successCriteria: string[];
  blockers: string[];
  assignedTo: string;
}

export interface RiskFactor {
  id: string;
  type: 'usage_decline' | 'support_volume' | 'feature_abandonment' | 'payment_issues' | 'team_turnover' | 'competitor_interest';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  detectedAt: Date;
  mitigationPlan: string;
  status: 'active' | 'monitoring' | 'resolved';
  impact: number; // 1-10 scale
  likelihood: number; // 1-10 scale
}

export interface SuccessPlan {
  id: string;
  objectives: SuccessObjective[];
  timeline: string;
  keyActivities: SuccessActivity[];
  successMetrics: SuccessMetric[];
  checkpoints: SuccessCheckpoint[];
  assignedCSM: string; // Customer Success Manager
  lastReviewed: Date;
  nextReview: Date;
}

export interface SuccessObjective {
  id: string;
  title: string;
  description: string;
  category: 'onboarding' | 'adoption' | 'expansion' | 'retention';
  targetDate: Date;
  status: 'not_started' | 'in_progress' | 'completed' | 'at_risk';
  progress: number; // 0-100
  successCriteria: string[];
  dependencies: string[];
}

export interface SuccessActivity {
  id: string;
  title: string;
  description: string;
  type: 'training' | 'check_in' | 'review' | 'optimization' | 'expansion_discussion';
  scheduledDate: Date;
  completedDate?: Date;
  status: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled';
  participants: string[];
  outcomes: string[];
  followUpRequired: boolean;
  followUpDate?: Date;
}

export interface SuccessMetric {
  id: string;
  name: string;
  description: string;
  target: number;
  current: number;
  unit: string;
  trend: 'improving' | 'stable' | 'declining';
  lastUpdated: Date;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
}

export interface SuccessCheckpoint {
  id: string;
  name: string;
  scheduledDate: Date;
  completedDate?: Date;
  status: 'upcoming' | 'completed' | 'overdue';
  agenda: string[];
  outcomes: string[];
  actionItems: ActionItem[];
  nextCheckpoint?: Date;
}

export interface ActionItem {
  id: string;
  description: string;
  assignedTo: string;
  dueDate: Date;
  status: 'open' | 'in_progress' | 'completed' | 'overdue';
  priority: 'low' | 'medium' | 'high' | 'critical';
}

// Knowledge Base Interfaces
export interface KnowledgeBaseArticle {
  id: string;
  title: string;
  content: string;
  summary: string;
  category: string;
  subcategory?: string;
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedReadTime: number; // minutes
  author: string;
  lastUpdated: Date;
  version: string;
  status: 'draft' | 'published' | 'archived';
  views: number;
  likes: number;
  helpfulVotes: number;
  notHelpfulVotes: number;
  relatedArticles: string[];
  attachments: KBAttachment[];
  searchKeywords: string[];
}

export interface KBAttachment {
  id: string;
  name: string;
  type: 'image' | 'video' | 'document' | 'link';
  url: string;
  description?: string;
  size?: number;
}

export interface KnowledgeBaseCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  parentId?: string;
  order: number;
  articleCount: number;
  subcategories: KnowledgeBaseCategory[];
}

// Training Program Interfaces
export interface TrainingProgram {
  id: string;
  title: string;
  description: string;
  category: 'onboarding' | 'feature_training' | 'best_practices' | 'advanced_usage';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedDuration: number; // minutes
  modules: TrainingModule[];
  prerequisites: string[];
  learningObjectives: string[];
  completionCriteria: string[];
  certificateAwarded: boolean;
  tags: string[];
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

export interface TrainingModule {
  id: string;
  title: string;
  description: string;
  type: 'video' | 'interactive' | 'reading' | 'quiz' | 'hands_on';
  content: TrainingContent;
  estimatedDuration: number; // minutes
  order: number;
  isRequired: boolean;
  completionCriteria: string[];
}

export interface TrainingContent {
  type: 'video' | 'text' | 'interactive' | 'quiz';
  data: any; // Flexible content structure
  resources: TrainingResource[];
}

export interface TrainingResource {
  id: string;
  name: string;
  type: 'document' | 'link' | 'video' | 'template';
  url: string;
  description?: string;
}

export interface UserTrainingProgress {
  id: string;
  userId: string;
  tenantId: string;
  programId: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'paused';
  startedAt: Date;
  completedAt?: Date;
  currentModuleId?: string;
  moduleProgress: ModuleProgress[];
  overallProgress: number; // 0-100
  timeSpent: number; // minutes
  certificateEarned: boolean;
  certificateIssuedAt?: Date;
  lastAccessedAt: Date;
}

export interface ModuleProgress {
  moduleId: string;
  status: 'not_started' | 'in_progress' | 'completed';
  startedAt?: Date;
  completedAt?: Date;
  timeSpent: number; // minutes
  attempts: number;
  score?: number; // for quizzes
  notes?: string;
}

// Customer Success Service Implementation
export class CustomerSuccessService {
  private static instance: CustomerSuccessService;

  public static getInstance(): CustomerSuccessService {
    if (!CustomerSuccessService.instance) {
      CustomerSuccessService.instance = new CustomerSuccessService();
    }
    return CustomerSuccessService.instance;
  }

  // Customer Success Profile Methods
  async createCustomerSuccessProfile(
    tenantId: string,
    userId: string,
    initialData?: Partial<CustomerSuccessProfile>
  ): Promise<CustomerSuccessProfile> {
    try {
      const profile: CustomerSuccessProfile = {
        id: `cs_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        tenantId,
        userId,
        healthScore: 50, // Start with neutral score
        riskLevel: 'low',
        onboardingStatus: 'not_started',
        lastActivity: new Date(),
        totalLogins: 0,
        featureAdoption: [],
        engagementMetrics: {
          dailyActiveUse: false,
          weeklyActiveUse: false,
          monthlyActiveUse: false,
          averageSessionDuration: 0,
          pagesPerSession: 0,
          bounceRate: 0,
          featureUsageDepth: 1,
          collaborationLevel: 1
        },
        supportInteractions: [],
        milestones: [],
        riskFactors: [],
        successPlan: {
          id: `plan_${Date.now()}`,
          objectives: [],
          timeline: '90 days',
          keyActivities: [],
          successMetrics: [],
          checkpoints: [],
          assignedCSM: '',
          lastReviewed: new Date(),
          nextReview: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
        },
        createdAt: new Date(),
        updatedAt: new Date(),
        ...initialData
      };

      const docRef = doc(db, 'customerSuccessProfiles', profile.id);
      await setDoc(docRef, {
        ...profile,
        createdAt: Timestamp.fromDate(profile.createdAt),
        updatedAt: Timestamp.fromDate(profile.updatedAt),
        lastActivity: Timestamp.fromDate(profile.lastActivity)
      });

      return profile;
    } catch (error) {
      console.error('Error creating customer success profile:', error);
      throw error;
    }
  }

  async getCustomerSuccessProfile(userId: string, tenantId: string): Promise<CustomerSuccessProfile | null> {
    try {
      const q = query(
        collection(db, 'customerSuccessProfiles'),
        where('userId', '==', userId),
        where('tenantId', '==', tenantId),
        limit(1)
      );
      
      const querySnapshot = await getDocs(q);
      if (querySnapshot.empty) {
        return null;
      }

      const doc = querySnapshot.docs[0];
      const data = doc.data();
      return {
        ...data,
        id: doc.id,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
        lastActivity: data.lastActivity.toDate()
      } as CustomerSuccessProfile;
    } catch (error) {
      console.error('Error fetching customer success profile:', error);
      throw error;
    }
  }

  async updateHealthScore(profileId: string, newScore: number, factors: string[]): Promise<void> {
    try {
      const docRef = doc(db, 'customerSuccessProfiles', profileId);
      await updateDoc(docRef, {
        healthScore: newScore,
        riskLevel: this.calculateRiskLevel(newScore),
        updatedAt: Timestamp.fromDate(new Date())
      });
    } catch (error) {
      console.error('Error updating health score:', error);
      throw error;
    }
  }

  private calculateRiskLevel(healthScore: number): 'low' | 'medium' | 'high' | 'critical' {
    if (healthScore >= 80) return 'low';
    if (healthScore >= 60) return 'medium';
    if (healthScore >= 40) return 'high';
    return 'critical';
  }

  // Knowledge Base Methods
  async createKnowledgeBaseArticle(articleData: Omit<KnowledgeBaseArticle, 'id' | 'views' | 'likes' | 'helpfulVotes' | 'notHelpfulVotes'>): Promise<KnowledgeBaseArticle> {
    try {
      const article: KnowledgeBaseArticle = {
        id: `kb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        views: 0,
        likes: 0,
        helpfulVotes: 0,
        notHelpfulVotes: 0,
        ...articleData
      };

      const docRef = doc(db, 'knowledgeBase', article.id);
      await setDoc(docRef, {
        ...article,
        lastUpdated: Timestamp.fromDate(article.lastUpdated)
      });

      return article;
    } catch (error) {
      console.error('Error creating knowledge base article:', error);
      throw error;
    }
  }

  async searchKnowledgeBase(query: string, category?: string): Promise<KnowledgeBaseArticle[]> {
    try {
      // In a real implementation, this would use full-text search
      // For now, we'll simulate with a basic query
      let q = collection(db, 'knowledgeBase');
      
      if (category) {
        q = query(q, where('category', '==', category));
      }

      const querySnapshot = await getDocs(q);
      const articles = querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
          lastUpdated: data.lastUpdated.toDate()
        } as KnowledgeBaseArticle;
      });

      // Simple text search simulation
      const searchTerms = query.toLowerCase().split(' ');
      return articles.filter(article => 
        searchTerms.some(term => 
          article.title.toLowerCase().includes(term) ||
          article.content.toLowerCase().includes(term) ||
          article.tags.some(tag => tag.toLowerCase().includes(term)) ||
          article.searchKeywords.some(keyword => keyword.toLowerCase().includes(term))
        )
      );
    } catch (error) {
      console.error('Error searching knowledge base:', error);
      throw error;
    }
  }

  // Training Program Methods
  async createTrainingProgram(programData: Omit<TrainingProgram, 'id' | 'createdAt' | 'updatedAt'>): Promise<TrainingProgram> {
    try {
      const program: TrainingProgram = {
        id: `training_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date(),
        updatedAt: new Date(),
        ...programData
      };

      const docRef = doc(db, 'trainingPrograms', program.id);
      await setDoc(docRef, {
        ...program,
        createdAt: Timestamp.fromDate(program.createdAt),
        updatedAt: Timestamp.fromDate(program.updatedAt)
      });

      return program;
    } catch (error) {
      console.error('Error creating training program:', error);
      throw error;
    }
  }

  async enrollUserInTraining(userId: string, tenantId: string, programId: string): Promise<UserTrainingProgress> {
    try {
      const progress: UserTrainingProgress = {
        id: `progress_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId,
        tenantId,
        programId,
        status: 'not_started',
        startedAt: new Date(),
        moduleProgress: [],
        overallProgress: 0,
        timeSpent: 0,
        certificateEarned: false,
        lastAccessedAt: new Date()
      };

      const docRef = doc(db, 'userTrainingProgress', progress.id);
      await setDoc(docRef, {
        ...progress,
        startedAt: Timestamp.fromDate(progress.startedAt),
        lastAccessedAt: Timestamp.fromDate(progress.lastAccessedAt)
      });

      return progress;
    } catch (error) {
      console.error('Error enrolling user in training:', error);
      throw error;
    }
  }

  // Analytics and Reporting
  async generateCustomerHealthReport(tenantId: string): Promise<any> {
    try {
      const q = query(
        collection(db, 'customerSuccessProfiles'),
        where('tenantId', '==', tenantId)
      );
      
      const querySnapshot = await getDocs(q);
      const profiles = querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
          createdAt: data.createdAt.toDate(),
          updatedAt: data.updatedAt.toDate(),
          lastActivity: data.lastActivity.toDate()
        } as CustomerSuccessProfile;
      });

      // Calculate aggregate metrics
      const totalUsers = profiles.length;
      const averageHealthScore = profiles.reduce((sum, p) => sum + p.healthScore, 0) / totalUsers;
      const riskDistribution = profiles.reduce((acc, p) => {
        acc[p.riskLevel] = (acc[p.riskLevel] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const onboardingStatus = profiles.reduce((acc, p) => {
        acc[p.onboardingStatus] = (acc[p.onboardingStatus] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return {
        totalUsers,
        averageHealthScore,
        riskDistribution,
        onboardingStatus,
        profiles: profiles.slice(0, 10) // Top 10 for detailed view
      };
    } catch (error) {
      console.error('Error generating customer health report:', error);
      throw error;
    }
  }
}
