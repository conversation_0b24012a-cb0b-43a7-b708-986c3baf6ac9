import {
  addDocument,
  updateDocument,
  getDocumentById,
  uploadFileToStorage
} from '@/services/firestoreService';
import { Timestamp } from 'firebase/firestore';
import type { SignatureStatus } from '@/types/firestore';

export interface DocumentSignature {
  id?: string;
  documentId: string;
  documentType: 'approval' | 'contract' | 'vendor_document' | 'purchase_order' | 'other';
  signerId: string;
  signerName: string;
  signerEmail: string;
  signatureData: string; // Base64 encoded signature image
  signatureMethod: 'digital' | 'electronic' | 'biometric' | 'manual';
  signedAt: Timestamp | Date | string;
  ipAddress?: string;
  userAgent?: string;
  location?: {
    latitude: number;
    longitude: number;
    accuracy?: number;
  };
  verificationData?: {
    hash: string;
    timestamp: string;
    nonce: string;
  };
  status: 'pending' | 'completed' | 'rejected' | 'expired';
  expiresAt?: Timestamp | Date | string;
  createdAt?: Timestamp | Date | string;
  updatedAt?: Timestamp | Date | string;
}

export interface SigningRequest {
  id?: string;
  documentId: string;
  documentName: string;
  documentUrl: string;
  documentType: 'approval' | 'contract' | 'vendor_document' | 'purchase_order' | 'other';
  requesterId: string;
  requesterName: string;
  signers: SigningRequestSigner[];
  status: 'draft' | 'sent' | 'in_progress' | 'completed' | 'cancelled' | 'expired';
  message?: string;
  dueDate?: Timestamp | Date | string;
  reminderSettings?: {
    enabled: boolean;
    intervalDays: number;
    maxReminders: number;
  };
  completedAt?: Timestamp | Date | string;
  createdAt?: Timestamp | Date | string;
  updatedAt?: Timestamp | Date | string;
}

export interface SigningRequestSigner {
  id: string;
  email: string;
  name: string;
  role?: string;
  order: number;
  status: 'pending' | 'signed' | 'declined' | 'expired';
  signedAt?: Timestamp | Date | string;
  signatureId?: string;
  accessToken?: string;
  remindersSent?: number;
}

// Convert class methods to standalone functions for server actions compatibility
export async function createSigningRequest(data: Omit<SigningRequest, 'id' | 'status' | 'createdAt' | 'updatedAt'>): Promise<{ success: boolean; requestId?: string; error?: string }> {
  try {
    const signingRequest: Omit<SigningRequest, 'id'> = {
      ...data,
      status: 'draft',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };

    const requestId = await addDocument('signing_requests', signingRequest);

    return { success: true, requestId };
  } catch (error) {
    console.error('Error creating signing request:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Send signing request to signers
 */
export async function sendSigningRequest(requestId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const request = await getDocumentById<SigningRequest>('signing_requests', requestId);
    if (!request) {
      return { success: false, error: 'Signing request not found' };
    }

    // Generate access tokens for signers
    const updatedSigners = request.signers.map(signer => ({
      ...signer,
      accessToken: generateAccessToken(),
      status: 'pending' as const
    }));

    await updateDocument('signing_requests', requestId, {
      status: 'sent',
      signers: updatedSigners,
      updatedAt: Timestamp.now()
    });

    // TODO: Send email notifications to signers
    // This would integrate with your email service
    await sendSigningNotifications(request, updatedSigners);

    return { success: true };
  } catch (error) {
    console.error('Error sending signing request:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Process a signature for a document
 */
export async function processSignature(data: {
    requestId: string;
    signerId: string;
    signatureData: string;
    signatureMethod: DocumentSignature['signatureMethod'];
    ipAddress?: string;
    userAgent?: string;
    location?: DocumentSignature['location'];
  }): Promise<{ success: boolean; signatureId?: string; error?: string }> {
  try {
    const request = await getDocumentById<SigningRequest>('signing_requests', requestId);
    if (!request) {
      return { success: false, error: 'Signing request not found' };
    }

    const signer = request.signers.find(s => s.id === data.signerId);
    if (!signer) {
      return { success: false, error: 'Signer not found' };
    }

    if (signer.status !== 'pending') {
      return { success: false, error: 'Signature already processed or expired' };
    }

    // Create signature record
    const signature: Omit<DocumentSignature, 'id'> = {
      documentId: request.documentId,
      documentType: request.documentType,
      signerId: data.signerId,
      signerName: signer.name,
      signerEmail: signer.email,
      signatureData: data.signatureData,
      signatureMethod: data.signatureMethod,
      signedAt: Timestamp.now(),
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      location: data.location,
      verificationData: generateVerificationData(data.signatureData),
      status: 'completed',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };

    const signatureId = await addDocument('document_signatures', signature);

    // Update signer status
    const updatedSigners = request.signers.map(s =>
      s.id === data.signerId
        ? { ...s, status: 'signed' as const, signedAt: Timestamp.now(), signatureId }
        : s
    );

    // Check if all signers have signed
    const allSigned = updatedSigners.every(s => s.status === 'signed');
    const requestStatus = allSigned ? 'completed' : 'in_progress';

    await updateDocument('signing_requests', requestId, {
      signers: updatedSigners,
      status: requestStatus,
      completedAt: allSigned ? Timestamp.now() : undefined,
      updatedAt: Timestamp.now()
    });

    // If all signed, generate final signed document
    if (allSigned) {
      await generateSignedDocument(requestId);
    }

    return { success: true, signatureId };
  } catch (error) {
    console.error('Error processing signature:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Get signing request by ID
 */
export async function getSigningRequest(requestId: string): Promise<SigningRequest | null> {
  try {
    return await getDocumentById<SigningRequest>('signing_requests', requestId);
  } catch (error) {
    console.error('Error getting signing request:', error);
    return null;
  }
}

/**
 * Get signatures for a document
 */
export async function getDocumentSignatures(documentId: string): Promise<DocumentSignature[]> {
  try {
    // This would need a query implementation in firestoreService
    // For now, return empty array
    return [];
  } catch (error) {
    console.error('Error getting document signatures:', error);
    return [];
  }
}

/**
 * Update document signature status
 */
export async function updateDocumentSignatureStatus(
    documentId: string,
    documentType: string,
    status: SignatureStatus,
    signedDocumentUrl?: string
  ): Promise<{ success: boolean; error?: string }> {
  try {
    let collectionName = '';

    switch (documentType) {
      case 'vendor_document':
        collectionName = 'vendor_documents';
        break;
      case 'purchase_order':
        collectionName = 'purchase_orders';
        break;
      default:
        return { success: false, error: 'Unsupported document type' };
    }

    const updateData: any = {
      signatureStatus: status,
      updatedAt: Timestamp.now()
    };

    if (signedDocumentUrl) {
      updateData.signedDocumentUrl = signedDocumentUrl;
    }

    await updateDocument(collectionName, documentId, updateData);

    return { success: true };
  } catch (error) {
    console.error('Error updating document signature status:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Generate access token for signer
 */
function generateAccessToken(): string {
  return `sign_${Math.random().toString(36).substr(2, 16)}_${Date.now()}`;
}

/**
 * Generate verification data for signature
 */
function generateVerificationData(signatureData: string): DocumentSignature['verificationData'] {
  const timestamp = Date.now().toString();
  const nonce = Math.random().toString(36).substr(2, 16);
  const hash = generateHash(`${signatureData}${timestamp}${nonce}`);

  return { hash, timestamp, nonce };
}

/**
 * Generate hash for verification
 */
function generateHash(data: string): string {
  // Simple hash implementation - in production, use crypto.subtle
  let hash = 0;
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(16);
}

/**
 * Send signing notifications to signers
 */
async function sendSigningNotifications(request: SigningRequest, signers: SigningRequestSigner[]): Promise<void> {
  // TODO: Implement email notifications
  // This would integrate with your email service
  console.log('Sending signing notifications for request:', request.id);
}

/**
 * Generate final signed document with all signatures
 */
async function generateSignedDocument(requestId: string): Promise<void> {
  // TODO: Implement signed document generation
  // This would combine the original document with signature overlays
  console.log('Generating signed document for request:', requestId);
}

// Export a service object for backward compatibility
export const documentSigningService = {
  createSigningRequest,
  sendSigningRequest,
  processSignature,
  getSigningRequest,
  getDocumentSignatures,
  updateDocumentSignatureStatus
};
