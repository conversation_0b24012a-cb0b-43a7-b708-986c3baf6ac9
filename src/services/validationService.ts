/**
 * Validation Service
 * Centralized validation service for all EVEXA data operations
 */

import { 
  validateDocumentSchema,
  validateDocumentTenantId,
  validateDocumentBatch,
  createValidationMiddleware,
  formatValidationErrors,
  formatValidationWarnings,
  hasCriticalErrors,
  sanitizeDocument,
  type ValidationResult,
  type ValidationError,
  type ValidationWarning
} from './dataValidationLayer';
import { 
  getValidationSchema,
  VALIDATION_SCHEMAS 
} from './collectionValidationSchemas';
import { validateTenantId } from './tenantIdHelperService';
import type { TenantAwareEntity } from '@/types/firestore';

// ===== VALIDATION SERVICE CLASS =====

export class ValidationService {
  private static instance: ValidationService;
  
  private constructor() {}
  
  public static getInstance(): ValidationService {
    if (!ValidationService.instance) {
      ValidationService.instance = new ValidationService();
    }
    return ValidationService.instance;
  }

  // ===== DOCUMENT VALIDATION =====

  /**
   * Validate a single document for a specific collection
   */
  validateDocument(
    document: any,
    collectionName: string,
    expectedTenantId?: string
  ): ValidationResult {
    const schema = getValidationSchema(collectionName);
    
    if (!schema) {
      return {
        valid: true,
        errors: [],
        warnings: [{
          field: 'schema',
          code: 'NO_SCHEMA_FOUND',
          message: `No validation schema found for collection: ${collectionName}`,
          suggestion: 'Consider adding a validation schema for this collection'
        }]
      };
    }

    // Validate against schema
    const schemaValidation = validateDocumentSchema(document, schema);
    
    // Additional tenant validation if expected tenant is provided
    if (expectedTenantId) {
      const tenantValidation = validateDocumentTenantId(document, expectedTenantId);
      schemaValidation.errors.push(...tenantValidation.errors);
      schemaValidation.warnings.push(...tenantValidation.warnings);
      
      if (!tenantValidation.valid) {
        schemaValidation.valid = false;
      }
    }

    return schemaValidation;
  }

  /**
   * Validate multiple documents
   */
  validateDocuments(
    documents: any[],
    collectionName: string,
    expectedTenantId?: string
  ): {
    valid: boolean;
    results: Array<{ documentIndex: number; validation: ValidationResult }>;
    summary: {
      totalDocuments: number;
      validDocuments: number;
      invalidDocuments: number;
      totalErrors: number;
      totalWarnings: number;
      criticalErrors: number;
    };
  } {
    const results: Array<{ documentIndex: number; validation: ValidationResult }> = [];
    let validCount = 0;
    let totalErrors = 0;
    let totalWarnings = 0;
    let criticalErrors = 0;

    for (let i = 0; i < documents.length; i++) {
      const validation = this.validateDocument(documents[i], collectionName, expectedTenantId);
      results.push({ documentIndex: i, validation });
      
      if (validation.valid) {
        validCount++;
      }
      
      totalErrors += validation.errors.length;
      totalWarnings += validation.warnings.length;
      criticalErrors += validation.errors.filter(e => e.severity === 'critical').length;
    }

    return {
      valid: validCount === documents.length,
      results,
      summary: {
        totalDocuments: documents.length,
        validDocuments: validCount,
        invalidDocuments: documents.length - validCount,
        totalErrors,
        totalWarnings,
        criticalErrors
      }
    };
  }

  // ===== TENANT VALIDATION =====

  /**
   * Validate tenant ID format and existence
   */
  validateTenant(tenantId: string): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    try {
      validateTenantId(tenantId);
    } catch (error) {
      result.valid = false;
      result.errors.push({
        field: 'tenantId',
        code: 'INVALID_TENANT_ID',
        message: `Invalid tenant ID: ${error}`,
        severity: 'critical'
      });
    }

    return result;
  }

  /**
   * Validate document belongs to specific tenant
   */
  validateDocumentTenant(
    document: TenantAwareEntity,
    expectedTenantId: string
  ): ValidationResult {
    return validateDocumentTenantId(document, expectedTenantId);
  }

  // ===== COLLECTION VALIDATION =====

  /**
   * Get available validation schemas
   */
  getAvailableSchemas(): string[] {
    return Object.keys(VALIDATION_SCHEMAS);
  }

  /**
   * Check if collection has validation schema
   */
  hasValidationSchema(collectionName: string): boolean {
    return !!getValidationSchema(collectionName);
  }

  /**
   * Get validation schema for collection
   */
  getCollectionSchema(collectionName: string) {
    return getValidationSchema(collectionName);
  }

  // ===== VALIDATION MIDDLEWARE =====

  /**
   * Create validation middleware for a collection
   */
  createMiddleware(collectionName: string) {
    const schema = getValidationSchema(collectionName);
    
    if (!schema) {
      throw new Error(`No validation schema found for collection: ${collectionName}`);
    }

    return createValidationMiddleware(schema);
  }

  // ===== UTILITY FUNCTIONS =====

  /**
   * Sanitize document for safe storage
   */
  sanitizeDocument(document: any): any {
    return sanitizeDocument(document);
  }

  /**
   * Format validation errors for display
   */
  formatErrors(errors: ValidationError[]): string[] {
    return formatValidationErrors(errors);
  }

  /**
   * Format validation warnings for display
   */
  formatWarnings(warnings: ValidationWarning[]): string[] {
    return formatValidationWarnings(warnings);
  }

  /**
   * Check if validation result has critical errors
   */
  hasCriticalErrors(result: ValidationResult): boolean {
    return hasCriticalErrors(result);
  }

  /**
   * Get validation summary for multiple results
   */
  getValidationSummary(results: ValidationResult[]): {
    totalValidations: number;
    successfulValidations: number;
    failedValidations: number;
    totalErrors: number;
    totalWarnings: number;
    criticalErrors: number;
    successRate: number;
  } {
    const totalValidations = results.length;
    const successfulValidations = results.filter(r => r.valid).length;
    const failedValidations = totalValidations - successfulValidations;
    const totalErrors = results.reduce((sum, r) => sum + r.errors.length, 0);
    const totalWarnings = results.reduce((sum, r) => sum + r.warnings.length, 0);
    const criticalErrors = results.reduce((sum, r) => sum + r.errors.filter(e => e.severity === 'critical').length, 0);
    const successRate = totalValidations > 0 ? (successfulValidations / totalValidations) * 100 : 0;

    return {
      totalValidations,
      successfulValidations,
      failedValidations,
      totalErrors,
      totalWarnings,
      criticalErrors,
      successRate
    };
  }

  // ===== VALIDATION REPORTING =====

  /**
   * Generate validation report
   */
  generateValidationReport(
    collectionName: string,
    results: ValidationResult[],
    options: {
      includeWarnings?: boolean;
      includeSuccessful?: boolean;
      groupByErrorType?: boolean;
    } = {}
  ): string {
    const { includeWarnings = true, includeSuccessful = false, groupByErrorType = false } = options;
    const summary = this.getValidationSummary(results);
    
    const lines: string[] = [];
    
    lines.push('='.repeat(60));
    lines.push(`VALIDATION REPORT: ${collectionName.toUpperCase()}`);
    lines.push('='.repeat(60));
    lines.push('');
    
    lines.push('SUMMARY:');
    lines.push(`- Total Validations: ${summary.totalValidations}`);
    lines.push(`- Successful: ${summary.successfulValidations}`);
    lines.push(`- Failed: ${summary.failedValidations}`);
    lines.push(`- Success Rate: ${summary.successRate.toFixed(2)}%`);
    lines.push(`- Total Errors: ${summary.totalErrors}`);
    lines.push(`- Critical Errors: ${summary.criticalErrors}`);
    
    if (includeWarnings) {
      lines.push(`- Total Warnings: ${summary.totalWarnings}`);
    }
    
    lines.push('');

    if (groupByErrorType) {
      // Group errors by type
      const errorsByType = new Map<string, ValidationError[]>();
      
      results.forEach(result => {
        result.errors.forEach(error => {
          if (!errorsByType.has(error.code)) {
            errorsByType.set(error.code, []);
          }
          errorsByType.get(error.code)!.push(error);
        });
      });

      if (errorsByType.size > 0) {
        lines.push('ERRORS BY TYPE:');
        for (const [errorCode, errors] of errorsByType) {
          lines.push(`- ${errorCode}: ${errors.length} occurrences`);
          lines.push(`  ${errors[0].message}`);
        }
        lines.push('');
      }
    }

    // Individual validation results
    results.forEach((result, index) => {
      if (!result.valid || includeSuccessful) {
        lines.push(`VALIDATION ${index + 1}: ${result.valid ? 'PASSED' : 'FAILED'}`);
        
        if (result.errors.length > 0) {
          lines.push('  Errors:');
          result.errors.forEach(error => {
            lines.push(`    - [${error.severity.toUpperCase()}] ${error.field}: ${error.message}`);
          });
        }
        
        if (includeWarnings && result.warnings.length > 0) {
          lines.push('  Warnings:');
          result.warnings.forEach(warning => {
            lines.push(`    - ${warning.field}: ${warning.message}`);
          });
        }
        
        lines.push('');
      }
    });
    
    lines.push('='.repeat(60));
    
    return lines.join('\n');
  }
}

// ===== SINGLETON INSTANCE =====

export const validationService = ValidationService.getInstance();

// ===== CONVENIENCE EXPORTS =====

export {
  ValidationResult,
  ValidationError,
  ValidationWarning
} from './dataValidationLayer';

export {
  getValidationSchema,
  VALIDATION_SCHEMAS
} from './collectionValidationSchemas';
