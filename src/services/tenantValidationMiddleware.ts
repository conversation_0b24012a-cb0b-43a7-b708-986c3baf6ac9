/**
 * Tenant Validation Middleware
 * Provides middleware functions for API routes to validate tenant access
 */

import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { validateTenantId } from './tenantIdHelperService';
import type { TenantAwareEntity } from '@/types/firestore';

// ===== TYPES =====

export interface TenantValidationResult {
  success: boolean;
  tenantId?: string;
  error?: string;
  statusCode?: number;
}

export interface TenantMiddlewareOptions {
  requireTenantId?: boolean;
  allowSuperAdmin?: boolean;
  validateTenantAccess?: boolean;
}

// ===== TENANT EXTRACTION =====

/**
 * Extract tenant ID from various sources in the request
 */
export function extractTenantIdFromRequest(request: NextRequest): string | null {
  // 1. Check Authorization header for tenant info
  const authHeader = request.headers.get('authorization');
  if (authHeader) {
    // Extract tenant from JWT token if present
    // This would need to be implemented based on your JWT structure
    const tenantFromAuth = extractTenantFromAuthHeader(authHeader);
    if (tenantFromAuth) return tenantFromAuth;
  }

  // 2. Check custom tenant header
  const tenantHeader = request.headers.get('x-tenant-id');
  if (tenantHeader) return tenantHeader;

  // 3. Check query parameters
  const url = new URL(request.url);
  const tenantFromQuery = url.searchParams.get('tenantId');
  if (tenantFromQuery) return tenantFromQuery;

  // 4. Check subdomain
  const host = request.headers.get('host');
  if (host) {
    const tenantFromSubdomain = extractTenantFromSubdomain(host);
    if (tenantFromSubdomain) return tenantFromSubdomain;
  }

  // 5. Check path-based tenant (e.g., /t/tenant-slug)
  const pathname = url.pathname;
  const pathMatch = pathname.match(/^\/t\/([^\/]+)/);
  if (pathMatch) return pathMatch[1];

  return null;
}

/**
 * Extract tenant from Authorization header (JWT token)
 */
function extractTenantFromAuthHeader(authHeader: string): string | null {
  try {
    // Remove 'Bearer ' prefix
    const token = authHeader.replace('Bearer ', '');
    
    // For now, return null - this would need JWT decoding implementation
    // const decoded = jwt.decode(token);
    // return decoded?.tenantId || null;
    
    return null;
  } catch (error) {
    return null;
  }
}

/**
 * Extract tenant from subdomain
 */
function extractTenantFromSubdomain(host: string): string | null {
  try {
    const parts = host.split('.');
    if (parts.length >= 3) {
      const subdomain = parts[0];
      // Skip common subdomains
      if (!['www', 'api', 'app', 'admin'].includes(subdomain)) {
        return subdomain;
      }
    }
    return null;
  } catch (error) {
    return null;
  }
}

// ===== VALIDATION FUNCTIONS =====

/**
 * Validate tenant access for API routes
 */
export async function validateTenantAccess(
  request: NextRequest,
  options: TenantMiddlewareOptions = {}
): Promise<TenantValidationResult> {
  const {
    requireTenantId = true,
    allowSuperAdmin = true,
    validateTenantAccess = true
  } = options;

  try {
    // Extract tenant ID from request
    const tenantId = extractTenantIdFromRequest(request);

    // Check if tenant ID is required
    if (requireTenantId && !tenantId) {
      return {
        success: false,
        error: 'Tenant ID is required',
        statusCode: 400
      };
    }

    // Validate tenant ID format if present
    if (tenantId) {
      try {
        validateTenantId(tenantId);
      } catch (error) {
        return {
          success: false,
          error: 'Invalid tenant ID format',
          statusCode: 400
        };
      }
    }

    // TODO: Add additional tenant access validation
    // - Check if tenant exists
    // - Check if tenant is active
    // - Check user permissions for tenant
    // - Check subscription limits

    return {
      success: true,
      tenantId: tenantId || undefined
    };

  } catch (error) {
    return {
      success: false,
      error: 'Tenant validation failed',
      statusCode: 500
    };
  }
}

/**
 * Middleware wrapper for API routes
 */
export function withTenantValidation(
  handler: (request: NextRequest, context: { tenantId: string }) => Promise<NextResponse>,
  options: TenantMiddlewareOptions = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const validation = await validateTenantAccess(request, options);

    if (!validation.success) {
      return NextResponse.json(
        { error: validation.error },
        { status: validation.statusCode || 400 }
      );
    }

    if (!validation.tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    try {
      return await handler(request, { tenantId: validation.tenantId });
    } catch (error) {
      console.error('API handler error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

// ===== SERVER ACTION HELPERS =====

/**
 * Get tenant ID from server action context
 */
export async function getTenantIdFromServerAction(): Promise<string | null> {
  try {
    const headersList = headers();
    
    // Check custom tenant header
    const tenantHeader = headersList.get('x-tenant-id');
    if (tenantHeader) return tenantHeader;

    // Check host for subdomain
    const host = headersList.get('host');
    if (host) {
      const tenantFromSubdomain = extractTenantFromSubdomain(host);
      if (tenantFromSubdomain) return tenantFromSubdomain;
    }

    return null;
  } catch (error) {
    console.error('Error getting tenant ID from server action:', error);
    return null;
  }
}

/**
 * Validate tenant access in server actions
 */
export async function validateServerActionTenantAccess(
  expectedTenantId?: string
): Promise<TenantValidationResult> {
  try {
    const tenantId = await getTenantIdFromServerAction();

    if (!tenantId) {
      return {
        success: false,
        error: 'Tenant context not available',
        statusCode: 400
      };
    }

    if (expectedTenantId && tenantId !== expectedTenantId) {
      return {
        success: false,
        error: 'Tenant access denied',
        statusCode: 403
      };
    }

    validateTenantId(tenantId);

    return {
      success: true,
      tenantId
    };

  } catch (error) {
    return {
      success: false,
      error: 'Tenant validation failed',
      statusCode: 500
    };
  }
}

// ===== DOCUMENT VALIDATION =====

/**
 * Validate that document data belongs to the correct tenant
 */
export function validateDocumentTenantOwnership<T extends TenantAwareEntity>(
  document: T,
  expectedTenantId: string
): void {
  if (!document.tenantId) {
    throw new Error('Document missing tenant ID');
  }

  if (document.tenantId !== expectedTenantId) {
    throw new Error(`Document belongs to different tenant. Expected: ${expectedTenantId}, Got: ${document.tenantId}`);
  }
}

/**
 * Validate array of documents belong to the correct tenant
 */
export function validateDocumentsTenantOwnership<T extends TenantAwareEntity>(
  documents: T[],
  expectedTenantId: string
): void {
  documents.forEach((doc, index) => {
    try {
      validateDocumentTenantOwnership(doc, expectedTenantId);
    } catch (error) {
      throw new Error(`Document at index ${index}: ${error}`);
    }
  });
}

// ===== UTILITY FUNCTIONS =====

/**
 * Create error response for tenant validation failures
 */
export function createTenantErrorResponse(
  error: string,
  statusCode: number = 400
): NextResponse {
  return NextResponse.json(
    { 
      error,
      code: 'TENANT_VALIDATION_ERROR',
      timestamp: new Date().toISOString()
    },
    { status: statusCode }
  );
}

/**
 * Check if request is from super admin
 */
export function isSuperAdminRequest(request: NextRequest): boolean {
  // This would need to be implemented based on your authentication system
  // For now, return false
  return false;
}

/**
 * Get tenant-safe error message (don't leak tenant information)
 */
export function getTenantSafeErrorMessage(error: string): string {
  // Remove any tenant-specific information from error messages
  return error
    .replace(/tenant[^a-zA-Z0-9]/gi, 'organization ')
    .replace(/tenantId/gi, 'organization ID');
}
