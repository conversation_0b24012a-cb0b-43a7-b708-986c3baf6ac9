/**
 * Automated Billing Service for EVEXA
 * Handles usage tracking, metering, and automated billing processes
 */

import { subscriptionService, type TenantSubscription, type UsageEvent } from './subscriptionService';
import { stripePaymentService } from './stripePaymentService';
import { db } from '@/lib/firebase';
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp 
} from 'firebase/firestore';

export interface UsageMetrics {
  tenantId: string;
  period: {
    start: Date;
    end: Date;
  };
  metrics: {
    exhibitions: number;
    events: number;
    users: number;
    tasks: number;
    leads: number;
    vendors: number;
    storageUsedGB: number;
    apiCallsThisMonth: number;
    emailsSentThisMonth: number;
  };
  overages: {
    exhibitions?: number;
    events?: number;
    users?: number;
    tasks?: number;
    leads?: number;
    vendors?: number;
    storageGB?: number;
    apiCalls?: number;
    emails?: number;
  };
  totalOverageCharges: number;
}

export interface BillingAlert {
  id?: string;
  tenantId: string;
  type: 'usage_warning' | 'usage_limit' | 'payment_failed' | 'trial_ending';
  severity: 'info' | 'warning' | 'critical';
  title: string;
  message: string;
  threshold?: number;
  currentUsage?: number;
  createdAt: Date;
  acknowledged: boolean;
}

class AutomatedBillingService {
  private alertsCollection = 'billing_alerts';
  private usageMetricsCollection = 'usage_metrics';

  /**
   * Track usage for a tenant
   */
  async trackUsage(
    tenantId: string, 
    eventType: UsageEvent['eventType'], 
    resourceId: string, 
    quantity: number = 1,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    try {
      // Track in subscription service
      await subscriptionService.trackUsage(tenantId, eventType, resourceId, quantity, metadata);

      // Check for usage limits and send alerts if necessary
      await this.checkUsageLimits(tenantId);
    } catch (error) {
      console.error('Error tracking usage:', error);
      throw error;
    }
  }

  /**
   * Check usage limits and send alerts
   */
  async checkUsageLimits(tenantId: string): Promise<void> {
    try {
      const subscription = await subscriptionService.getTenantSubscription(tenantId);
      if (!subscription) return;

      const plan = await subscriptionService.getPlanById(subscription.planId);
      if (!plan) return;

      const alerts: BillingAlert[] = [];

      // Check each usage metric
      const usageChecks = [
        {
          current: subscription.usage.exhibitions,
          limit: plan.features.maxExhibitions,
          type: 'exhibitions',
          name: 'Exhibitions'
        },
        {
          current: subscription.usage.events,
          limit: plan.features.maxEvents,
          type: 'events',
          name: 'Events'
        },
        {
          current: subscription.usage.users,
          limit: plan.features.maxUsers,
          type: 'users',
          name: 'Users'
        },
        {
          current: subscription.usage.tasks,
          limit: plan.features.maxTasks,
          type: 'tasks',
          name: 'Tasks'
        },
        {
          current: subscription.usage.leads,
          limit: plan.features.maxLeads,
          type: 'leads',
          name: 'Leads'
        },
        {
          current: subscription.usage.vendors,
          limit: plan.features.maxVendors,
          type: 'vendors',
          name: 'Vendors'
        },
        {
          current: subscription.usage.storageUsedGB,
          limit: plan.features.storageGB,
          type: 'storage',
          name: 'Storage'
        },
        {
          current: subscription.usage.apiCallsThisMonth,
          limit: plan.features.apiCallsPerMonth,
          type: 'api_calls',
          name: 'API Calls'
        },
        {
          current: subscription.usage.emailsSentThisMonth,
          limit: plan.features.emailsPerMonth,
          type: 'emails',
          name: 'Emails'
        }
      ];

      for (const check of usageChecks) {
        if (check.limit === -1) continue; // Unlimited

        const usagePercentage = (check.current / check.limit) * 100;

        // 80% warning
        if (usagePercentage >= 80 && usagePercentage < 100) {
          alerts.push({
            tenantId,
            type: 'usage_warning',
            severity: 'warning',
            title: `${check.name} Usage Warning`,
            message: `You've used ${usagePercentage.toFixed(1)}% of your ${check.name.toLowerCase()} limit (${check.current}/${check.limit})`,
            threshold: 80,
            currentUsage: usagePercentage,
            createdAt: new Date(),
            acknowledged: false
          });
        }

        // 100% limit reached
        if (usagePercentage >= 100) {
          alerts.push({
            tenantId,
            type: 'usage_limit',
            severity: 'critical',
            title: `${check.name} Limit Reached`,
            message: `You've reached your ${check.name.toLowerCase()} limit (${check.current}/${check.limit}). Consider upgrading your plan.`,
            threshold: 100,
            currentUsage: usagePercentage,
            createdAt: new Date(),
            acknowledged: false
          });
        }
      }

      // Save alerts
      for (const alert of alerts) {
        await this.createAlert(alert);
      }
    } catch (error) {
      console.error('Error checking usage limits:', error);
    }
  }

  /**
   * Calculate usage metrics for a billing period
   */
  async calculateUsageMetrics(tenantId: string, startDate: Date, endDate: Date): Promise<UsageMetrics> {
    try {
      const subscription = await subscriptionService.getTenantSubscription(tenantId);
      if (!subscription) {
        throw new Error('No subscription found for tenant');
      }

      const plan = await subscriptionService.getPlanById(subscription.planId);
      if (!plan) {
        throw new Error('No plan found for subscription');
      }

      // Get usage events for the period
      const usageEvents = await this.getUsageEventsForPeriod(tenantId, startDate, endDate);

      // Calculate metrics
      const metrics = {
        exhibitions: subscription.usage.exhibitions,
        events: subscription.usage.events,
        users: subscription.usage.users,
        tasks: subscription.usage.tasks,
        leads: subscription.usage.leads,
        vendors: subscription.usage.vendors,
        storageUsedGB: subscription.usage.storageUsedGB,
        apiCallsThisMonth: subscription.usage.apiCallsThisMonth,
        emailsSentThisMonth: subscription.usage.emailsSentThisMonth
      };

      // Calculate overages
      const overages: any = {};
      let totalOverageCharges = 0;

      const overageRates = {
        exhibitions: 10, // $10 per additional exhibition
        events: 5, // $5 per additional event
        users: 15, // $15 per additional user
        tasks: 0.01, // $0.01 per additional task
        leads: 0.05, // $0.05 per additional lead
        vendors: 2, // $2 per additional vendor
        storageGB: 1, // $1 per additional GB
        apiCalls: 0.001, // $0.001 per additional API call
        emails: 0.01 // $0.01 per additional email
      };

      // Calculate overages for each metric
      if (plan.features.maxExhibitions !== -1 && metrics.exhibitions > plan.features.maxExhibitions) {
        overages.exhibitions = metrics.exhibitions - plan.features.maxExhibitions;
        totalOverageCharges += overages.exhibitions * overageRates.exhibitions;
      }

      if (plan.features.maxEvents !== -1 && metrics.events > plan.features.maxEvents) {
        overages.events = metrics.events - plan.features.maxEvents;
        totalOverageCharges += overages.events * overageRates.events;
      }

      if (plan.features.maxUsers !== -1 && metrics.users > plan.features.maxUsers) {
        overages.users = metrics.users - plan.features.maxUsers;
        totalOverageCharges += overages.users * overageRates.users;
      }

      if (plan.features.maxTasks !== -1 && metrics.tasks > plan.features.maxTasks) {
        overages.tasks = metrics.tasks - plan.features.maxTasks;
        totalOverageCharges += overages.tasks * overageRates.tasks;
      }

      if (plan.features.maxLeads !== -1 && metrics.leads > plan.features.maxLeads) {
        overages.leads = metrics.leads - plan.features.maxLeads;
        totalOverageCharges += overages.leads * overageRates.leads;
      }

      if (plan.features.maxVendors !== -1 && metrics.vendors > plan.features.maxVendors) {
        overages.vendors = metrics.vendors - plan.features.maxVendors;
        totalOverageCharges += overages.vendors * overageRates.vendors;
      }

      if (metrics.storageUsedGB > plan.features.storageGB) {
        overages.storageGB = metrics.storageUsedGB - plan.features.storageGB;
        totalOverageCharges += overages.storageGB * overageRates.storageGB;
      }

      if (metrics.apiCallsThisMonth > plan.features.apiCallsPerMonth) {
        overages.apiCalls = metrics.apiCallsThisMonth - plan.features.apiCallsPerMonth;
        totalOverageCharges += overages.apiCalls * overageRates.apiCalls;
      }

      if (metrics.emailsSentThisMonth > plan.features.emailsPerMonth) {
        overages.emails = metrics.emailsSentThisMonth - plan.features.emailsPerMonth;
        totalOverageCharges += overages.emails * overageRates.emails;
      }

      const usageMetrics: UsageMetrics = {
        tenantId,
        period: { start: startDate, end: endDate },
        metrics,
        overages,
        totalOverageCharges
      };

      // Save metrics
      await this.saveUsageMetrics(usageMetrics);

      return usageMetrics;
    } catch (error) {
      console.error('Error calculating usage metrics:', error);
      throw error;
    }
  }

  /**
   * Process monthly billing
   */
  async processMonthlyBilling(): Promise<void> {
    try {
      console.log('Starting monthly billing process...');

      // Get all active subscriptions
      const subscriptions = await subscriptionService.getAllActiveSubscriptions();

      for (const subscription of subscriptions) {
        try {
          await this.processTenantBilling(subscription);
        } catch (error) {
          console.error(`Error processing billing for tenant ${subscription.tenantId}:`, error);
          
          // Create alert for billing failure
          await this.createAlert({
            tenantId: subscription.tenantId,
            type: 'payment_failed',
            severity: 'critical',
            title: 'Billing Processing Failed',
            message: 'There was an error processing your monthly billing. Please contact support.',
            createdAt: new Date(),
            acknowledged: false
          });
        }
      }

      console.log('Monthly billing process completed');
    } catch (error) {
      console.error('Error in monthly billing process:', error);
      throw error;
    }
  }

  /**
   * Process billing for a specific tenant
   */
  private async processTenantBilling(subscription: TenantSubscription): Promise<void> {
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Calculate usage metrics for last month
    const usageMetrics = await this.calculateUsageMetrics(
      subscription.tenantId,
      lastMonth,
      thisMonth
    );

    // If there are overage charges, create additional invoice
    if (usageMetrics.totalOverageCharges > 0) {
      await this.createOverageInvoice(subscription, usageMetrics);
    }

    // Reset monthly usage counters
    await this.resetMonthlyUsage(subscription.tenantId);
  }

  /**
   * Create overage invoice
   */
  private async createOverageInvoice(
    subscription: TenantSubscription, 
    usageMetrics: UsageMetrics
  ): Promise<void> {
    const lineItems = [];

    // Add line items for each overage
    for (const [key, value] of Object.entries(usageMetrics.overages)) {
      if (value && value > 0) {
        lineItems.push({
          description: `Additional ${key} usage`,
          quantity: value,
          unitPrice: this.getOverageRate(key),
          amount: value * this.getOverageRate(key)
        });
      }
    }

    const invoice = {
      tenantId: subscription.tenantId,
      subscriptionId: subscription.id!,
      invoiceNumber: `OVG-${Date.now()}`,
      status: 'open' as const,
      amount: usageMetrics.totalOverageCharges,
      currency: 'USD',
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      lineItems,
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date()
      }
    };

    await subscriptionService.createInvoice(invoice);
  }

  /**
   * Reset monthly usage counters
   */
  private async resetMonthlyUsage(tenantId: string): Promise<void> {
    const subscription = await subscriptionService.getTenantSubscription(tenantId);
    if (!subscription) return;

    await subscriptionService.updateSubscription(subscription.id!, {
      'usage.apiCallsThisMonth': 0,
      'usage.emailsSentThisMonth': 0,
      'usage.lastUsageUpdate': new Date()
    });
  }

  /**
   * Get usage events for a period
   */
  private async getUsageEventsForPeriod(
    tenantId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<UsageEvent[]> {
    const q = query(
      collection(db, 'usage_events'),
      where('tenantId', '==', tenantId),
      where('timestamp', '>=', Timestamp.fromDate(startDate)),
      where('timestamp', '<=', Timestamp.fromDate(endDate)),
      orderBy('timestamp', 'desc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      timestamp: doc.data().timestamp.toDate()
    })) as UsageEvent[];
  }

  /**
   * Save usage metrics
   */
  private async saveUsageMetrics(metrics: UsageMetrics): Promise<void> {
    await addDoc(collection(db, this.usageMetricsCollection), {
      ...metrics,
      'period.start': Timestamp.fromDate(metrics.period.start),
      'period.end': Timestamp.fromDate(metrics.period.end)
    });
  }

  /**
   * Create billing alert
   */
  private async createAlert(alert: Omit<BillingAlert, 'id'>): Promise<void> {
    // Check if similar alert already exists
    const existingAlerts = await this.getAlertsForTenant(alert.tenantId);
    const similarAlert = existingAlerts.find(a => 
      a.type === alert.type && 
      a.title === alert.title && 
      !a.acknowledged
    );

    if (similarAlert) return; // Don't create duplicate alerts

    await addDoc(collection(db, this.alertsCollection), {
      ...alert,
      createdAt: Timestamp.fromDate(alert.createdAt)
    });
  }

  /**
   * Get alerts for tenant
   */
  async getAlertsForTenant(tenantId: string): Promise<BillingAlert[]> {
    const q = query(
      collection(db, this.alertsCollection),
      where('tenantId', '==', tenantId),
      orderBy('createdAt', 'desc'),
      limit(50)
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt.toDate()
    })) as BillingAlert[];
  }

  /**
   * Get overage rate for a usage type
   */
  private getOverageRate(usageType: string): number {
    const rates: Record<string, number> = {
      exhibitions: 10,
      events: 5,
      users: 15,
      tasks: 0.01,
      leads: 0.05,
      vendors: 2,
      storageGB: 1,
      apiCalls: 0.001,
      emails: 0.01
    };

    return rates[usageType] || 0;
  }
}

export const automatedBillingService = new AutomatedBillingService();
