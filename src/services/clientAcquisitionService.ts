import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

export interface Lead {
  id?: string;
  companyName: string;
  contactPerson: string;
  email: string;
  phone?: string;
  website?: string;
  industry: string;
  companySize: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  source: 'website' | 'referral' | 'partnership' | 'marketing' | 'cold_outreach' | 'event';
  status: 'new' | 'contacted' | 'qualified' | 'proposal_sent' | 'negotiating' | 'closed_won' | 'closed_lost';
  priority: 'high' | 'medium' | 'low';
  estimatedValue: number;
  currency: string;
  notes: string;
  assignedTo?: string;
  nextAction?: string;
  nextActionDate?: Date;
  tags: string[];
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  convertedAt?: Date;
}

export interface ClientOnboardingFlow {
  id?: string;
  leadId: string;
  tenantId?: string;
  status: 'initiated' | 'in_progress' | 'completed' | 'failed';
  currentStep: number;
  totalSteps: number;
  steps: OnboardingStep[];
  assignedTo: string;
  startedAt: Date;
  completedAt?: Date;
  estimatedCompletionDate: Date;
}

export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  type: 'form' | 'document' | 'meeting' | 'setup' | 'training' | 'approval';
  status: 'pending' | 'in_progress' | 'completed' | 'skipped' | 'failed';
  assignee: string;
  dueDate: Date;
  completedAt?: Date;
  notes?: string;
  attachments?: string[];
  dependencies?: string[];
}

export interface ConversionMetrics {
  totalLeads: number;
  qualifiedLeads: number;
  convertedLeads: number;
  conversionRate: number;
  averageTimeToConversion: number;
  totalValue: number;
  averageDealSize: number;
  leadsBySource: Record<string, number>;
  leadsByStatus: Record<string, number>;
  monthlyTrends: Array<{
    month: string;
    leads: number;
    conversions: number;
    value: number;
  }>;
}

class ClientAcquisitionService {
  private leadsCollection = 'leads';
  private onboardingFlowsCollection = 'client_onboarding_flows';

  // Lead Management
  async createLead(lead: Omit<Lead, 'id' | 'createdAt' | 'updatedAt'>): Promise<Lead> {
    const now = new Date();
    const leadData: Omit<Lead, 'id'> = {
      ...lead,
      createdAt: now,
      updatedAt: now
    };

    const docRef = await addDoc(collection(db, this.leadsCollection), {
      ...leadData,
      createdAt: Timestamp.fromDate(now),
      updatedAt: Timestamp.fromDate(now),
      nextActionDate: leadData.nextActionDate ? Timestamp.fromDate(leadData.nextActionDate) : null,
      convertedAt: leadData.convertedAt ? Timestamp.fromDate(leadData.convertedAt) : null
    });

    return { ...leadData, id: docRef.id };
  }

  async getLead(id: string): Promise<Lead | null> {
    const docRef = doc(db, this.leadsCollection, id);
    const docSnap = await getDoc(docRef);
    
    if (!docSnap.exists()) return null;
    
    const data = docSnap.data();
    return {
      id: docSnap.id,
      ...data,
      createdAt: data.createdAt.toDate(),
      updatedAt: data.updatedAt.toDate(),
      nextActionDate: data.nextActionDate?.toDate(),
      convertedAt: data.convertedAt?.toDate()
    } as Lead;
  }

  async getAllLeads(): Promise<Lead[]> {
    const q = query(
      collection(db, this.leadsCollection),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
        nextActionDate: data.nextActionDate?.toDate(),
        convertedAt: data.convertedAt?.toDate()
      } as Lead;
    });
  }

  async getLeadsByStatus(status: Lead['status']): Promise<Lead[]> {
    const q = query(
      collection(db, this.leadsCollection),
      where('status', '==', status),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
        nextActionDate: data.nextActionDate?.toDate(),
        convertedAt: data.convertedAt?.toDate()
      } as Lead;
    });
  }

  async updateLead(id: string, updates: Partial<Lead>): Promise<void> {
    const docRef = doc(db, this.leadsCollection, id);
    const updateData = {
      ...updates,
      updatedAt: Timestamp.fromDate(new Date())
    };

    // Handle date conversions
    if (updates.nextActionDate) {
      updateData.nextActionDate = Timestamp.fromDate(updates.nextActionDate);
    }
    if (updates.convertedAt) {
      updateData.convertedAt = Timestamp.fromDate(updates.convertedAt);
    }

    await updateDoc(docRef, updateData);
  }

  async deleteLead(id: string): Promise<void> {
    const docRef = doc(db, this.leadsCollection, id);
    await deleteDoc(docRef);
  }

  // Onboarding Flow Management
  async createOnboardingFlow(flow: Omit<ClientOnboardingFlow, 'id' | 'startedAt'>): Promise<ClientOnboardingFlow> {
    const now = new Date();
    const flowData: Omit<ClientOnboardingFlow, 'id'> = {
      ...flow,
      startedAt: now
    };

    const docRef = await addDoc(collection(db, this.onboardingFlowsCollection), {
      ...flowData,
      startedAt: Timestamp.fromDate(now),
      completedAt: flowData.completedAt ? Timestamp.fromDate(flowData.completedAt) : null,
      estimatedCompletionDate: Timestamp.fromDate(flowData.estimatedCompletionDate),
      steps: flowData.steps.map(step => ({
        ...step,
        dueDate: Timestamp.fromDate(step.dueDate),
        completedAt: step.completedAt ? Timestamp.fromDate(step.completedAt) : null
      }))
    });

    return { ...flowData, id: docRef.id };
  }

  async getOnboardingFlow(id: string): Promise<ClientOnboardingFlow | null> {
    const docRef = doc(db, this.onboardingFlowsCollection, id);
    const docSnap = await getDoc(docRef);
    
    if (!docSnap.exists()) return null;
    
    const data = docSnap.data();
    return {
      id: docSnap.id,
      ...data,
      startedAt: data.startedAt.toDate(),
      completedAt: data.completedAt?.toDate(),
      estimatedCompletionDate: data.estimatedCompletionDate.toDate(),
      steps: data.steps.map((step: any) => ({
        ...step,
        dueDate: step.dueDate.toDate(),
        completedAt: step.completedAt?.toDate()
      }))
    } as ClientOnboardingFlow;
  }

  async getOnboardingFlowsByStatus(status: ClientOnboardingFlow['status']): Promise<ClientOnboardingFlow[]> {
    const q = query(
      collection(db, this.onboardingFlowsCollection),
      where('status', '==', status),
      orderBy('startedAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        startedAt: data.startedAt.toDate(),
        completedAt: data.completedAt?.toDate(),
        estimatedCompletionDate: data.estimatedCompletionDate.toDate(),
        steps: data.steps.map((step: any) => ({
          ...step,
          dueDate: step.dueDate.toDate(),
          completedAt: step.completedAt?.toDate()
        }))
      } as ClientOnboardingFlow;
    });
  }

  async updateOnboardingFlow(id: string, updates: Partial<ClientOnboardingFlow>): Promise<void> {
    const docRef = doc(db, this.onboardingFlowsCollection, id);
    const updateData = { ...updates };

    // Handle date conversions
    if (updates.completedAt) {
      updateData.completedAt = Timestamp.fromDate(updates.completedAt);
    }
    if (updates.estimatedCompletionDate) {
      updateData.estimatedCompletionDate = Timestamp.fromDate(updates.estimatedCompletionDate);
    }
    if (updates.steps) {
      updateData.steps = updates.steps.map(step => ({
        ...step,
        dueDate: Timestamp.fromDate(step.dueDate),
        completedAt: step.completedAt ? Timestamp.fromDate(step.completedAt) : null
      }));
    }

    await updateDoc(docRef, updateData);
  }

  // Analytics and Metrics
  async getConversionMetrics(): Promise<ConversionMetrics> {
    const leads = await this.getAllLeads();
    
    const totalLeads = leads.length;
    const qualifiedLeads = leads.filter(l => ['qualified', 'proposal_sent', 'negotiating', 'closed_won'].includes(l.status)).length;
    const convertedLeads = leads.filter(l => l.status === 'closed_won').length;
    const conversionRate = totalLeads > 0 ? (convertedLeads / totalLeads) * 100 : 0;
    
    const convertedLeadsData = leads.filter(l => l.status === 'closed_won' && l.convertedAt);
    const totalValue = convertedLeadsData.reduce((sum, l) => sum + l.estimatedValue, 0);
    const averageDealSize = convertedLeads > 0 ? totalValue / convertedLeads : 0;
    
    // Calculate average time to conversion
    const conversionTimes = convertedLeadsData
      .filter(l => l.convertedAt)
      .map(l => l.convertedAt!.getTime() - l.createdAt.getTime());
    const averageTimeToConversion = conversionTimes.length > 0 
      ? conversionTimes.reduce((sum, time) => sum + time, 0) / conversionTimes.length / (1000 * 60 * 60 * 24) // Convert to days
      : 0;

    // Group by source
    const leadsBySource = leads.reduce((acc, lead) => {
      acc[lead.source] = (acc[lead.source] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Group by status
    const leadsByStatus = leads.reduce((acc, lead) => {
      acc[lead.status] = (acc[lead.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Monthly trends (simplified - last 6 months)
    const monthlyTrends = Array.from({ length: 6 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = date.toISOString().slice(0, 7); // YYYY-MM format
      
      const monthLeads = leads.filter(l => l.createdAt.toISOString().slice(0, 7) === monthKey);
      const monthConversions = monthLeads.filter(l => l.status === 'closed_won');
      const monthValue = monthConversions.reduce((sum, l) => sum + l.estimatedValue, 0);
      
      return {
        month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        leads: monthLeads.length,
        conversions: monthConversions.length,
        value: monthValue
      };
    }).reverse();

    return {
      totalLeads,
      qualifiedLeads,
      convertedLeads,
      conversionRate,
      averageTimeToConversion,
      totalValue,
      averageDealSize,
      leadsBySource,
      leadsByStatus,
      monthlyTrends
    };
  }

  // Automated Lead Scoring
  async scoreLeads(): Promise<void> {
    const leads = await this.getAllLeads();

    for (const lead of leads) {
      let score = 0;

      // Company size scoring
      const sizeScores = { startup: 10, small: 20, medium: 30, large: 40, enterprise: 50 };
      score += sizeScores[lead.companySize] || 0;

      // Source scoring
      const sourceScores = { referral: 30, partnership: 25, event: 20, website: 15, marketing: 10, cold_outreach: 5 };
      score += sourceScores[lead.source] || 0;

      // Estimated value scoring
      if (lead.estimatedValue > 100000) score += 30;
      else if (lead.estimatedValue > 50000) score += 20;
      else if (lead.estimatedValue > 10000) score += 10;

      // Engagement scoring (based on status progression)
      const statusScores = { new: 0, contacted: 10, qualified: 20, proposal_sent: 30, negotiating: 40 };
      score += statusScores[lead.status as keyof typeof statusScores] || 0;

      // Update lead priority based on score
      let priority: Lead['priority'] = 'low';
      if (score >= 70) priority = 'high';
      else if (score >= 40) priority = 'medium';

      if (lead.priority !== priority) {
        await this.updateLead(lead.id!, { priority });
      }
    }
  }

  // Generate default onboarding steps
  generateDefaultOnboardingSteps(): OnboardingStep[] {
    const baseDate = new Date();
    return [
      {
        id: '1',
        title: 'Initial Discovery Call',
        description: 'Conduct discovery call to understand client needs',
        type: 'meeting',
        status: 'pending',
        assignee: 'Sales Team',
        dueDate: new Date(baseDate.getTime() + 1 * 24 * 60 * 60 * 1000), // +1 day
      },
      {
        id: '2',
        title: 'Proposal Preparation',
        description: 'Prepare customized proposal based on client requirements',
        type: 'document',
        status: 'pending',
        assignee: 'Sales Team',
        dueDate: new Date(baseDate.getTime() + 3 * 24 * 60 * 60 * 1000), // +3 days
        dependencies: ['1']
      },
      {
        id: '3',
        title: 'Contract Negotiation',
        description: 'Negotiate contract terms and finalize agreement',
        type: 'approval',
        status: 'pending',
        assignee: 'Legal Team',
        dueDate: new Date(baseDate.getTime() + 7 * 24 * 60 * 60 * 1000), // +7 days
        dependencies: ['2']
      },
      {
        id: '4',
        title: 'Tenant Setup',
        description: 'Create tenant account and configure initial settings',
        type: 'setup',
        status: 'pending',
        assignee: 'Technical Team',
        dueDate: new Date(baseDate.getTime() + 10 * 24 * 60 * 60 * 1000), // +10 days
        dependencies: ['3']
      },
      {
        id: '5',
        title: 'Data Migration',
        description: 'Migrate client data from existing systems',
        type: 'setup',
        status: 'pending',
        assignee: 'Technical Team',
        dueDate: new Date(baseDate.getTime() + 14 * 24 * 60 * 60 * 1000), // +14 days
        dependencies: ['4']
      },
      {
        id: '6',
        title: 'User Training',
        description: 'Conduct user training sessions for client team',
        type: 'training',
        status: 'pending',
        assignee: 'Customer Success',
        dueDate: new Date(baseDate.getTime() + 17 * 24 * 60 * 60 * 1000), // +17 days
        dependencies: ['5']
      },
      {
        id: '7',
        title: 'Go-Live Support',
        description: 'Provide go-live support and monitor initial usage',
        type: 'setup',
        status: 'pending',
        assignee: 'Customer Success',
        dueDate: new Date(baseDate.getTime() + 21 * 24 * 60 * 60 * 1000), // +21 days
        dependencies: ['6']
      }
    ];
  }
}

export const clientAcquisitionService = new ClientAcquisitionService();
export default clientAcquisitionService;
