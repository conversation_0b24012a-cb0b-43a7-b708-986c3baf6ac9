import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  Timestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

export interface MarketingCampaign {
  id?: string;
  name: string;
  type: 'email' | 'social' | 'content' | 'webinar' | 'partnership' | 'pr';
  status: 'draft' | 'scheduled' | 'active' | 'paused' | 'completed';
  startDate: Date;
  endDate?: Date;
  budget: number;
  spent: number;
  targetAudience: string;
  channels: string[];
  metrics: {
    impressions: number;
    clicks: number;
    conversions: number;
    leads: number;
    cost_per_lead: number;
  };
  description: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
}

class MarketingCampaignService {
  private campaignsCollection = 'marketing_campaigns';

  // Campaign CRUD operations
  async createCampaign(campaign: Omit<MarketingCampaign, 'id' | 'createdAt' | 'updatedAt'>): Promise<MarketingCampaign> {
    const now = new Date();
    const campaignData: Omit<MarketingCampaign, 'id'> = {
      ...campaign,
      createdAt: now,
      updatedAt: now,
      metrics: {
        impressions: 0,
        clicks: 0,
        conversions: 0,
        leads: 0,
        cost_per_lead: 0,
        ...campaign.metrics
      }
    };

    const docRef = await addDoc(collection(db, this.campaignsCollection), {
      ...campaignData,
      startDate: Timestamp.fromDate(campaignData.startDate),
      endDate: campaignData.endDate ? Timestamp.fromDate(campaignData.endDate) : null,
      createdAt: Timestamp.fromDate(now),
      updatedAt: Timestamp.fromDate(now)
    });

    return { ...campaignData, id: docRef.id };
  }

  async getCampaign(id: string): Promise<MarketingCampaign | null> {
    const docRef = doc(db, this.campaignsCollection, id);
    const docSnap = await getDoc(docRef);
    
    if (!docSnap.exists()) return null;
    
    const data = docSnap.data();
    return {
      id: docSnap.id,
      ...data,
      startDate: data.startDate.toDate(),
      endDate: data.endDate?.toDate(),
      createdAt: data.createdAt.toDate(),
      updatedAt: data.updatedAt.toDate()
    } as MarketingCampaign;
  }

  async getAllCampaigns(): Promise<MarketingCampaign[]> {
    const q = query(
      collection(db, this.campaignsCollection),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        startDate: data.startDate.toDate(),
        endDate: data.endDate?.toDate(),
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate()
      } as MarketingCampaign;
    });
  }

  async getCampaignsByStatus(status: MarketingCampaign['status']): Promise<MarketingCampaign[]> {
    const q = query(
      collection(db, this.campaignsCollection),
      where('status', '==', status),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        startDate: data.startDate.toDate(),
        endDate: data.endDate?.toDate(),
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate()
      } as MarketingCampaign;
    });
  }

  async updateCampaign(id: string, updates: Partial<MarketingCampaign>): Promise<void> {
    const docRef = doc(db, this.campaignsCollection, id);
    const updateData = {
      ...updates,
      updatedAt: Timestamp.fromDate(new Date())
    };

    // Handle date conversions
    if (updates.startDate) {
      updateData.startDate = Timestamp.fromDate(updates.startDate);
    }
    if (updates.endDate) {
      updateData.endDate = Timestamp.fromDate(updates.endDate);
    }

    await updateDoc(docRef, updateData);
  }

  async deleteCampaign(id: string): Promise<void> {
    const docRef = doc(db, this.campaignsCollection, id);
    await deleteDoc(docRef);
  }

  // Campaign metrics and analytics
  async updateCampaignMetrics(id: string, metrics: Partial<MarketingCampaign['metrics']>): Promise<void> {
    const campaign = await this.getCampaign(id);
    if (!campaign) throw new Error('Campaign not found');

    const updatedMetrics = {
      ...campaign.metrics,
      ...metrics
    };

    // Calculate cost per lead
    if (updatedMetrics.leads > 0) {
      updatedMetrics.cost_per_lead = campaign.spent / updatedMetrics.leads;
    }

    await this.updateCampaign(id, { 
      metrics: updatedMetrics,
      updatedAt: new Date()
    });
  }

  async getCampaignMetrics(): Promise<{
    totalCampaigns: number;
    activeCampaigns: number;
    totalBudget: number;
    totalSpent: number;
    totalLeads: number;
    averageCostPerLead: number;
    totalImpressions: number;
    totalClicks: number;
    averageConversionRate: number;
  }> {
    const campaigns = await this.getAllCampaigns();
    
    const totalCampaigns = campaigns.length;
    const activeCampaigns = campaigns.filter(c => c.status === 'active').length;
    const totalBudget = campaigns.reduce((sum, c) => sum + c.budget, 0);
    const totalSpent = campaigns.reduce((sum, c) => sum + c.spent, 0);
    const totalLeads = campaigns.reduce((sum, c) => sum + c.metrics.leads, 0);
    const totalImpressions = campaigns.reduce((sum, c) => sum + c.metrics.impressions, 0);
    const totalClicks = campaigns.reduce((sum, c) => sum + c.metrics.clicks, 0);
    const totalConversions = campaigns.reduce((sum, c) => sum + c.metrics.conversions, 0);
    
    const averageCostPerLead = totalLeads > 0 ? totalSpent / totalLeads : 0;
    const averageConversionRate = totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0;

    return {
      totalCampaigns,
      activeCampaigns,
      totalBudget,
      totalSpent,
      totalLeads,
      averageCostPerLead,
      totalImpressions,
      totalClicks,
      averageConversionRate
    };
  }

  // Campaign status management
  async startCampaign(id: string): Promise<void> {
    await this.updateCampaign(id, { 
      status: 'active',
      updatedAt: new Date()
    });
  }

  async pauseCampaign(id: string): Promise<void> {
    await this.updateCampaign(id, { 
      status: 'paused',
      updatedAt: new Date()
    });
  }

  async completeCampaign(id: string): Promise<void> {
    await this.updateCampaign(id, { 
      status: 'completed',
      endDate: new Date(),
      updatedAt: new Date()
    });
  }
}

export const marketingCampaignService = new MarketingCampaignService();
