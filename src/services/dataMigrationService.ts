/**
 * Data Migration Service
 * Migrates existing nested collection data to flat root-level collections with proper tenantId stamping
 */

import { 
  collection,
  doc,
  getDocs,
  writeBatch,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  serverTimestamp,
  DocumentSnapshot,
  QueryDocumentSnapshot,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { validateTenantId } from './tenantIdHelperService';
import type { TenantAwareEntity } from '@/types/firestore';

// ===== MIGRATION CONFIGURATION =====

export interface MigrationConfig {
  sourceCollection: string;
  targetCollection: string;
  description: string;
  batchSize: number;
  preserveOriginal: boolean;
  fieldMappings?: Record<string, string>;
  dataTransformations?: Array<{
    field: string;
    transform: (value: any) => any;
  }>;
  requiredFields: string[];
  tenantIdSource?: 'field' | 'default' | 'lookup';
  defaultTenantId?: string;
}

export interface MigrationResult {
  success: boolean;
  sourceCollection: string;
  targetCollection: string;
  documentsProcessed: number;
  documentsSkipped: number;
  errors: string[];
  duration: number;
  startTime: Date;
  endTime: Date;
}

export interface MigrationProgress {
  currentCollection: string;
  totalCollections: number;
  currentCollectionIndex: number;
  documentsProcessed: number;
  totalDocuments: number;
  errors: string[];
  isComplete: boolean;
}

// ===== MIGRATION CONFIGURATIONS =====

export const MIGRATION_CONFIGS: Record<string, MigrationConfig> = {
  // User Management
  users: {
    sourceCollection: 'users',
    targetCollection: COLLECTIONS.USER_PROFILES,
    description: 'Migrate users to user_profiles with tenantId stamping',
    batchSize: 100,
    preserveOriginal: false,
    fieldMappings: {
      'displayName': 'displayName',
      'email': 'email',
      'role': 'role',
      'status': 'status'
    },
    requiredFields: ['email'],
    tenantIdSource: 'default',
    defaultTenantId: 'evexa-super-admin-tenant'
  },

  // Exhibition Management
  exhibitions: {
    sourceCollection: 'exhibitions',
    targetCollection: COLLECTIONS.EXHIBITIONS,
    description: 'Migrate exhibitions with tenantId stamping',
    batchSize: 50,
    preserveOriginal: false,
    requiredFields: ['name', 'startDate', 'endDate'],
    tenantIdSource: 'default',
    defaultTenantId: 'evexa-super-admin-tenant'
  },

  events: {
    sourceCollection: 'events',
    targetCollection: COLLECTIONS.EXHIBITION_EVENTS,
    description: 'Migrate events to exhibition_events with tenantId stamping',
    batchSize: 100,
    preserveOriginal: false,
    requiredFields: ['name', 'exhibitionId'],
    tenantIdSource: 'default',
    defaultTenantId: 'evexa-super-admin-tenant'
  },

  tasks: {
    sourceCollection: 'tasks',
    targetCollection: COLLECTIONS.EXHIBITION_TASKS,
    description: 'Migrate tasks to exhibition_tasks with tenantId stamping',
    batchSize: 200,
    preserveOriginal: false,
    requiredFields: ['title'],
    tenantIdSource: 'default',
    defaultTenantId: 'evexa-super-admin-tenant'
  },

  // Lead Management
  leads: {
    sourceCollection: 'leads',
    targetCollection: COLLECTIONS.LEAD_CONTACTS,
    description: 'Migrate leads to lead_contacts with tenantId stamping',
    batchSize: 200,
    preserveOriginal: false,
    requiredFields: ['email'],
    tenantIdSource: 'default',
    defaultTenantId: 'evexa-super-admin-tenant'
  },

  // Financial Management
  budgets: {
    sourceCollection: 'budgets',
    targetCollection: COLLECTIONS.BUDGET_ALLOCATIONS,
    description: 'Migrate budgets to budget_allocations with tenantId stamping',
    batchSize: 100,
    preserveOriginal: false,
    requiredFields: ['activityId', 'amount'],
    tenantIdSource: 'default',
    defaultTenantId: 'evexa-super-admin-tenant'
  },

  expenses: {
    sourceCollection: 'expenses',
    targetCollection: COLLECTIONS.EXPENSE_RECORDS,
    description: 'Migrate expenses to expense_records with tenantId stamping',
    batchSize: 200,
    preserveOriginal: false,
    requiredFields: ['amount', 'activityId'],
    tenantIdSource: 'default',
    defaultTenantId: 'evexa-super-admin-tenant'
  },

  vendors: {
    sourceCollection: 'vendors',
    targetCollection: COLLECTIONS.VENDOR_PROFILES,
    description: 'Migrate vendors to vendor_profiles with tenantId stamping',
    batchSize: 100,
    preserveOriginal: false,
    requiredFields: ['name'],
    tenantIdSource: 'default',
    defaultTenantId: 'evexa-super-admin-tenant'
  }
};

// ===== MIGRATION FUNCTIONS =====

/**
 * Migrate a single collection
 */
export async function migrateCollection(
  configKey: string,
  tenantId: string,
  dryRun: boolean = false,
  onProgress?: (progress: { processed: number; total: number; current: string }) => void
): Promise<MigrationResult> {
  const startTime = new Date();
  const config = MIGRATION_CONFIGS[configKey];
  
  if (!config) {
    throw new Error(`Migration configuration not found for: ${configKey}`);
  }

  validateTenantId(tenantId);

  const result: MigrationResult = {
    success: false,
    sourceCollection: config.sourceCollection,
    targetCollection: config.targetCollection,
    documentsProcessed: 0,
    documentsSkipped: 0,
    errors: [],
    duration: 0,
    startTime,
    endTime: new Date()
  };

  try {
    console.log(`${dryRun ? '[DRY RUN] ' : ''}Starting migration: ${config.description}`);

    // Get source collection documents
    const sourceCollectionRef = collection(db, config.sourceCollection);
    const sourceQuery = query(sourceCollectionRef, orderBy('__name__'));
    const sourceSnapshot = await getDocs(sourceQuery);

    if (sourceSnapshot.empty) {
      console.log(`No documents found in source collection: ${config.sourceCollection}`);
      result.success = true;
      return result;
    }

    const totalDocuments = sourceSnapshot.size;
    console.log(`Found ${totalDocuments} documents to migrate`);

    // Process documents in batches
    const batches = [];
    let currentBatch = [];
    let processedCount = 0;

    for (const sourceDoc of sourceSnapshot.docs) {
      try {
        const sourceData = sourceDoc.data();
        
        // Validate required fields
        const missingFields = config.requiredFields.filter(field => !sourceData[field]);
        if (missingFields.length > 0) {
          result.errors.push(`Document ${sourceDoc.id} missing required fields: ${missingFields.join(', ')}`);
          result.documentsSkipped++;
          continue;
        }

        // Transform data
        const transformedData = await transformDocumentData(sourceData, sourceDoc.id, config, tenantId);
        
        currentBatch.push({
          id: sourceDoc.id,
          data: transformedData
        });

        if (currentBatch.length >= config.batchSize) {
          batches.push([...currentBatch]);
          currentBatch = [];
        }

        processedCount++;
        
        if (onProgress) {
          onProgress({
            processed: processedCount,
            total: totalDocuments,
            current: sourceDoc.id
          });
        }

      } catch (error) {
        result.errors.push(`Error processing document ${sourceDoc.id}: ${error}`);
        result.documentsSkipped++;
      }
    }

    // Add remaining documents to final batch
    if (currentBatch.length > 0) {
      batches.push(currentBatch);
    }

    // Execute batches
    if (!dryRun) {
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        await executeMigrationBatch(batch, config.targetCollection);
        console.log(`Completed batch ${i + 1}/${batches.length} (${batch.length} documents)`);
      }
    } else {
      console.log(`[DRY RUN] Would migrate ${processedCount} documents in ${batches.length} batches`);
    }

    result.documentsProcessed = processedCount;
    result.success = true;

  } catch (error) {
    result.errors.push(`Migration failed: ${error}`);
    console.error(`Migration error for ${config.sourceCollection}:`, error);
  }

  result.endTime = new Date();
  result.duration = result.endTime.getTime() - result.startTime.getTime();

  return result;
}

/**
 * Transform document data according to migration config
 */
async function transformDocumentData(
  sourceData: any,
  documentId: string,
  config: MigrationConfig,
  tenantId: string
): Promise<TenantAwareEntity> {
  const transformedData: any = {
    id: documentId,
    tenantId: tenantId
  };

  // Apply field mappings
  if (config.fieldMappings) {
    for (const [sourceField, targetField] of Object.entries(config.fieldMappings)) {
      if (sourceData[sourceField] !== undefined) {
        transformedData[targetField] = sourceData[sourceField];
      }
    }
  } else {
    // Copy all fields if no mappings specified
    Object.assign(transformedData, sourceData);
  }

  // Apply data transformations
  if (config.dataTransformations) {
    for (const transformation of config.dataTransformations) {
      if (transformedData[transformation.field] !== undefined) {
        transformedData[transformation.field] = transformation.transform(transformedData[transformation.field]);
      }
    }
  }

  // Ensure timestamps are properly formatted
  if (sourceData.createdAt) {
    transformedData.createdAt = sourceData.createdAt instanceof Timestamp 
      ? sourceData.createdAt 
      : new Date(sourceData.createdAt);
  } else {
    transformedData.createdAt = serverTimestamp();
  }

  if (sourceData.updatedAt) {
    transformedData.updatedAt = sourceData.updatedAt instanceof Timestamp 
      ? sourceData.updatedAt 
      : new Date(sourceData.updatedAt);
  } else {
    transformedData.updatedAt = serverTimestamp();
  }

  return transformedData as TenantAwareEntity;
}

/**
 * Execute a batch of document migrations
 */
async function executeMigrationBatch(
  batch: Array<{ id: string; data: TenantAwareEntity }>,
  targetCollection: string
): Promise<void> {
  const writeBatchRef = writeBatch(db);
  
  for (const item of batch) {
    const docRef = doc(db, targetCollection, item.id);
    writeBatchRef.set(docRef, item.data);
  }
  
  await writeBatchRef.commit();
}

/**
 * Migrate all configured collections
 */
export async function migrateAllCollections(
  tenantId: string,
  dryRun: boolean = false,
  onProgress?: (progress: MigrationProgress) => void
): Promise<MigrationResult[]> {
  validateTenantId(tenantId);
  
  const configKeys = Object.keys(MIGRATION_CONFIGS);
  const results: MigrationResult[] = [];
  
  for (let i = 0; i < configKeys.length; i++) {
    const configKey = configKeys[i];
    
    if (onProgress) {
      onProgress({
        currentCollection: MIGRATION_CONFIGS[configKey].sourceCollection,
        totalCollections: configKeys.length,
        currentCollectionIndex: i,
        documentsProcessed: 0,
        totalDocuments: 0,
        errors: [],
        isComplete: false
      });
    }
    
    const result = await migrateCollection(configKey, tenantId, dryRun, (progress) => {
      if (onProgress) {
        onProgress({
          currentCollection: MIGRATION_CONFIGS[configKey].sourceCollection,
          totalCollections: configKeys.length,
          currentCollectionIndex: i,
          documentsProcessed: progress.processed,
          totalDocuments: progress.total,
          errors: [],
          isComplete: false
        });
      }
    });
    
    results.push(result);
  }
  
  if (onProgress) {
    onProgress({
      currentCollection: '',
      totalCollections: configKeys.length,
      currentCollectionIndex: configKeys.length,
      documentsProcessed: 0,
      totalDocuments: 0,
      errors: results.flatMap(r => r.errors),
      isComplete: true
    });
  }
  
  return results;
}
