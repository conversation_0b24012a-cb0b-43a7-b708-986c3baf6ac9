/**
 * Performance Testing Service
 * Comprehensive performance testing for flat collections, indexing strategy, and multi-tenant scalability
 */

import {
  collection,
  doc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  getDocs,
  getDoc,
  addDoc,
  writeBatch,
  serverTimestamp,
  enableNetwork,
  disableNetwork
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { validateTenantId } from '@/services/tenantIdHelperService';
import { performanceOptimizedQueryService } from '@/services/performanceOptimizedQueryService';

export interface PerformanceTestResult {
  testName: string;
  passed: boolean;
  duration: number;
  metrics: {
    queryTime?: number;
    documentsRead?: number;
    cacheHitRate?: number;
    memoryUsage?: number;
    networkRequests?: number;
  };
  details?: string;
  error?: string;
  recommendations?: string[];
}

export interface ScalabilityTestResult {
  testName: string;
  dataSize: number;
  tenantCount: number;
  queryTime: number;
  throughput: number; // operations per second
  memoryUsage: number;
  passed: boolean;
  scalabilityScore: number; // 0-100
}

export interface IndexingTestResult {
  collection: string;
  indexName: string;
  queryType: string;
  withIndex: {
    queryTime: number;
    documentsRead: number;
  };
  withoutIndex: {
    queryTime: number;
    documentsRead: number;
  };
  improvementFactor: number;
  passed: boolean;
}

export class PerformanceTestingService {
  private testTenantIds: string[] = [];
  private testDataCounts = new Map<string, number>();

  /**
   * Initialize performance test environment
   */
  async initializeTestEnvironment(): Promise<void> {
    // Create test tenant IDs for multi-tenant testing
    this.testTenantIds = [
      `perf-test-tenant-1-${Date.now()}`,
      `perf-test-tenant-2-${Date.now()}`,
      `perf-test-tenant-3-${Date.now()}`,
      `perf-test-tenant-4-${Date.now()}`,
      `perf-test-tenant-5-${Date.now()}`
    ];

    // Create test data for each tenant
    await this.createTestData();
  }

  /**
   * Create test data for performance testing
   */
  private async createTestData(): Promise<void> {
    const testCollections = [
      COLLECTIONS.EXHIBITIONS,
      COLLECTIONS.EXHIBITION_TASKS,
      COLLECTIONS.LEAD_CONTACTS,
      COLLECTIONS.USER_PROFILES,
      COLLECTIONS.VENDOR_PROFILES
    ];

    const documentsPerCollection = 100; // Adjust based on testing needs
    const batch = writeBatch(db);
    let batchCount = 0;

    for (const tenantId of this.testTenantIds) {
      for (const collectionName of testCollections) {
        for (let i = 0; i < documentsPerCollection; i++) {
          const testDoc = {
            tenantId,
            name: `Test ${collectionName} ${i} for ${tenantId}`,
            status: i % 3 === 0 ? 'active' : i % 3 === 1 ? 'inactive' : 'pending',
            priority: i % 5,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
            testData: true,
            searchableText: `searchable content ${i} ${tenantId}`,
            numericField: Math.floor(Math.random() * 1000),
            dateField: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000)
          };

          const docRef = doc(collection(db, collectionName));
          batch.set(docRef, testDoc);
          batchCount++;

          // Firestore batch limit is 500 operations
          if (batchCount >= 450) {
            await batch.commit();
            batchCount = 0;
          }
        }

        this.testDataCounts.set(`${tenantId}-${collectionName}`, documentsPerCollection);
      }
    }

    if (batchCount > 0) {
      await batch.commit();
    }
  }

  /**
   * Test flat collection query performance
   */
  async testFlatCollectionPerformance(): Promise<PerformanceTestResult> {
    const startTime = performance.now();
    
    try {
      const testTenantId = this.testTenantIds[0];
      const queryStartTime = performance.now();
      
      // Test optimized flat collection query
      const result = await performanceOptimizedQueryService.getOptimizedList({
        tenantId: testTenantId,
        collection: COLLECTIONS.EXHIBITIONS,
        filters: [
          { field: 'status', operator: '==', value: 'active' }
        ],
        orderByField: 'createdAt',
        orderDirection: 'desc',
        limitCount: 50
      });

      const queryTime = performance.now() - queryStartTime;
      const documentsRead = result.items.length;

      // Performance thresholds
      const QUERY_TIME_THRESHOLD = 1000; // 1 second
      const DOCUMENTS_READ_THRESHOLD = 100;

      const passed = queryTime < QUERY_TIME_THRESHOLD && documentsRead <= DOCUMENTS_READ_THRESHOLD;

      return {
        testName: 'Flat Collection Query Performance',
        passed,
        duration: performance.now() - startTime,
        metrics: {
          queryTime,
          documentsRead,
          cacheHitRate: result.cacheHit ? 1 : 0
        },
        details: `Query completed in ${queryTime.toFixed(2)}ms, read ${documentsRead} documents`,
        recommendations: passed ? [] : [
          'Consider adding composite indexes for complex queries',
          'Implement query result caching',
          'Optimize query filters and ordering'
        ]
      };

    } catch (error) {
      return {
        testName: 'Flat Collection Query Performance',
        passed: false,
        duration: performance.now() - startTime,
        metrics: {},
        error: error instanceof Error ? error.message : 'Unknown error',
        recommendations: [
          'Check network connectivity',
          'Verify Firebase configuration',
          'Review query syntax and parameters'
        ]
      };
    }
  }

  /**
   * Test multi-tenant query isolation performance
   */
  async testMultiTenantQueryPerformance(): Promise<PerformanceTestResult> {
    const startTime = performance.now();
    
    try {
      const queryTimes: number[] = [];
      const documentsRead: number[] = [];

      // Test queries for each tenant
      for (const tenantId of this.testTenantIds) {
        const queryStartTime = performance.now();
        
        const q = query(
          collection(db, COLLECTIONS.EXHIBITIONS),
          where('tenantId', '==', tenantId),
          where('status', '==', 'active'),
          orderBy('createdAt', 'desc'),
          limit(20)
        );

        const snapshot = await getDocs(q);
        const queryTime = performance.now() - queryStartTime;
        
        queryTimes.push(queryTime);
        documentsRead.push(snapshot.docs.length);

        // Verify tenant isolation
        snapshot.docs.forEach(doc => {
          const data = doc.data();
          if (data.tenantId !== tenantId) {
            throw new Error(`Tenant isolation breach: Document ${doc.id} has tenantId ${data.tenantId} but was returned for tenant ${tenantId}`);
          }
        });
      }

      const avgQueryTime = queryTimes.reduce((sum, time) => sum + time, 0) / queryTimes.length;
      const totalDocumentsRead = documentsRead.reduce((sum, count) => sum + count, 0);

      // Performance thresholds
      const AVG_QUERY_TIME_THRESHOLD = 500; // 500ms average
      const MAX_QUERY_TIME_THRESHOLD = 1000; // 1 second max

      const maxQueryTime = Math.max(...queryTimes);
      const passed = avgQueryTime < AVG_QUERY_TIME_THRESHOLD && maxQueryTime < MAX_QUERY_TIME_THRESHOLD;

      return {
        testName: 'Multi-Tenant Query Performance',
        passed,
        duration: performance.now() - startTime,
        metrics: {
          queryTime: avgQueryTime,
          documentsRead: totalDocumentsRead,
          networkRequests: this.testTenantIds.length
        },
        details: `Average query time: ${avgQueryTime.toFixed(2)}ms, Max: ${maxQueryTime.toFixed(2)}ms across ${this.testTenantIds.length} tenants`,
        recommendations: passed ? [] : [
          'Add composite indexes for tenantId + status + createdAt',
          'Consider query result caching for frequently accessed data',
          'Optimize tenant filtering strategy'
        ]
      };

    } catch (error) {
      return {
        testName: 'Multi-Tenant Query Performance',
        passed: false,
        duration: performance.now() - startTime,
        metrics: {},
        error: error instanceof Error ? error.message : 'Unknown error',
        recommendations: [
          'Check tenant isolation configuration',
          'Verify tenantId filtering in queries',
          'Review multi-tenant security rules'
        ]
      };
    }
  }

  /**
   * Test pagination performance
   */
  async testPaginationPerformance(): Promise<PerformanceTestResult> {
    const startTime = performance.now();
    
    try {
      const testTenantId = this.testTenantIds[0];
      const pageSize = 20;
      const pagesToTest = 5;
      const queryTimes: number[] = [];

      let lastDoc = null;

      for (let page = 0; page < pagesToTest; page++) {
        const queryStartTime = performance.now();
        
        const constraints = [
          where('tenantId', '==', testTenantId),
          orderBy('createdAt', 'desc'),
          limit(pageSize)
        ];

        if (lastDoc) {
          constraints.push(startAfter(lastDoc));
        }

        const q = query(collection(db, COLLECTIONS.EXHIBITIONS), ...constraints);
        const snapshot = await getDocs(q);
        
        const queryTime = performance.now() - queryStartTime;
        queryTimes.push(queryTime);

        if (snapshot.docs.length > 0) {
          lastDoc = snapshot.docs[snapshot.docs.length - 1];
        }
      }

      const avgQueryTime = queryTimes.reduce((sum, time) => sum + time, 0) / queryTimes.length;
      const queryTimeVariance = Math.max(...queryTimes) - Math.min(...queryTimes);

      // Performance thresholds
      const AVG_QUERY_TIME_THRESHOLD = 300; // 300ms average
      const VARIANCE_THRESHOLD = 500; // 500ms variance

      const passed = avgQueryTime < AVG_QUERY_TIME_THRESHOLD && queryTimeVariance < VARIANCE_THRESHOLD;

      return {
        testName: 'Pagination Performance',
        passed,
        duration: performance.now() - startTime,
        metrics: {
          queryTime: avgQueryTime,
          networkRequests: pagesToTest
        },
        details: `Average pagination query time: ${avgQueryTime.toFixed(2)}ms, Variance: ${queryTimeVariance.toFixed(2)}ms across ${pagesToTest} pages`,
        recommendations: passed ? [] : [
          'Consider cursor-based pagination optimization',
          'Add indexes for pagination fields',
          'Implement pagination result caching'
        ]
      };

    } catch (error) {
      return {
        testName: 'Pagination Performance',
        passed: false,
        duration: performance.now() - startTime,
        metrics: {},
        error: error instanceof Error ? error.message : 'Unknown error',
        recommendations: [
          'Check pagination cursor implementation',
          'Verify startAfter document references',
          'Review pagination query constraints'
        ]
      };
    }
  }

  /**
   * Test complex query performance
   */
  async testComplexQueryPerformance(): Promise<PerformanceTestResult> {
    const startTime = performance.now();
    
    try {
      const testTenantId = this.testTenantIds[0];
      const queryStartTime = performance.now();

      // Complex query with multiple filters and ordering
      const q = query(
        collection(db, COLLECTIONS.EXHIBITION_TASKS),
        where('tenantId', '==', testTenantId),
        where('status', 'in', ['active', 'pending']),
        where('priority', '>=', 2),
        orderBy('priority', 'desc'),
        orderBy('createdAt', 'desc'),
        limit(50)
      );

      const snapshot = await getDocs(q);
      const queryTime = performance.now() - queryStartTime;
      const documentsRead = snapshot.docs.length;

      // Performance thresholds for complex queries
      const COMPLEX_QUERY_TIME_THRESHOLD = 1500; // 1.5 seconds
      const DOCUMENTS_READ_THRESHOLD = 100;

      const passed = queryTime < COMPLEX_QUERY_TIME_THRESHOLD && documentsRead <= DOCUMENTS_READ_THRESHOLD;

      return {
        testName: 'Complex Query Performance',
        passed,
        duration: performance.now() - startTime,
        metrics: {
          queryTime,
          documentsRead
        },
        details: `Complex query completed in ${queryTime.toFixed(2)}ms, read ${documentsRead} documents`,
        recommendations: passed ? [] : [
          'Add composite index for tenantId + status + priority + createdAt',
          'Consider query optimization or result caching',
          'Review query complexity and necessity'
        ]
      };

    } catch (error) {
      return {
        testName: 'Complex Query Performance',
        passed: false,
        duration: performance.now() - startTime,
        metrics: {},
        error: error instanceof Error ? error.message : 'Unknown error',
        recommendations: [
          'Simplify complex query constraints',
          'Add composite indexes for query fields',
          'Consider query result caching'
        ]
      };
    }
  }

  /**
   * Test scalability with increasing data size
   */
  async testScalability(): Promise<ScalabilityTestResult[]> {
    const results: ScalabilityTestResult[] = [];
    const dataSizes = [50, 100, 200, 500]; // Number of documents per tenant

    for (const dataSize of dataSizes) {
      const startTime = performance.now();
      
      try {
        // Create additional test data for this size
        await this.createScalabilityTestData(dataSize);
        
        const queryStartTime = performance.now();
        
        // Test query performance with this data size
        const testTenantId = this.testTenantIds[0];
        const q = query(
          collection(db, COLLECTIONS.EXHIBITIONS),
          where('tenantId', '==', testTenantId),
          orderBy('createdAt', 'desc'),
          limit(20)
        );

        const snapshot = await getDocs(q);
        const queryTime = performance.now() - queryStartTime;
        const throughput = snapshot.docs.length / (queryTime / 1000); // docs per second

        // Calculate scalability score (lower query time = higher score)
        const baselineTime = 100; // 100ms baseline
        const scalabilityScore = Math.max(0, Math.min(100, 100 - ((queryTime - baselineTime) / 10)));

        const passed = queryTime < 1000 && throughput > 10; // 1 second max, 10 docs/sec min

        results.push({
          testName: `Scalability Test - ${dataSize} documents`,
          dataSize,
          tenantCount: this.testTenantIds.length,
          queryTime,
          throughput,
          memoryUsage: this.getMemoryUsage(),
          passed,
          scalabilityScore
        });

      } catch (error) {
        results.push({
          testName: `Scalability Test - ${dataSize} documents`,
          dataSize,
          tenantCount: this.testTenantIds.length,
          queryTime: -1,
          throughput: 0,
          memoryUsage: 0,
          passed: false,
          scalabilityScore: 0
        });
      }
    }

    return results;
  }

  /**
   * Create test data for scalability testing
   */
  private async createScalabilityTestData(documentsPerTenant: number): Promise<void> {
    const batch = writeBatch(db);
    let batchCount = 0;

    const testTenantId = `scalability-test-${Date.now()}`;
    
    for (let i = 0; i < documentsPerTenant; i++) {
      const testDoc = {
        tenantId: testTenantId,
        name: `Scalability Test Exhibition ${i}`,
        status: i % 3 === 0 ? 'active' : 'inactive',
        priority: i % 5,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        testData: true,
        scalabilityTest: true
      };

      const docRef = doc(collection(db, COLLECTIONS.EXHIBITIONS));
      batch.set(docRef, testDoc);
      batchCount++;

      if (batchCount >= 450) {
        await batch.commit();
        batchCount = 0;
      }
    }

    if (batchCount > 0) {
      await batch.commit();
    }
  }

  /**
   * Get memory usage (simplified for browser environment)
   */
  private getMemoryUsage(): number {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
      return (window.performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
    }
    return 0;
  }

  /**
   * Run all performance tests
   */
  async runAllPerformanceTests(): Promise<PerformanceTestResult[]> {
    await this.initializeTestEnvironment();

    const tests = [
      () => this.testFlatCollectionPerformance(),
      () => this.testMultiTenantQueryPerformance(),
      () => this.testPaginationPerformance(),
      () => this.testComplexQueryPerformance()
    ];

    const results: PerformanceTestResult[] = [];

    for (const test of tests) {
      try {
        const result = await test();
        results.push(result);
      } catch (error) {
        results.push({
          testName: 'Unknown Performance Test',
          passed: false,
          duration: 0,
          metrics: {},
          error: error instanceof Error ? error.message : 'Test execution failed'
        });
      }
    }

    // Clean up test data
    await this.cleanupTestData();

    return results;
  }

  /**
   * Clean up test data
   */
  private async cleanupTestData(): Promise<void> {
    // In a real implementation, you would delete the test documents
    // For now, we'll just clear the local state
    this.testTenantIds = [];
    this.testDataCounts.clear();
  }
}

export const performanceTestingService = new PerformanceTestingService();
