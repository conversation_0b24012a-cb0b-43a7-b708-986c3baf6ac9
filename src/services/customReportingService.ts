/**
 * Custom Reporting Engine Service for EVEXA
 * Provides flexible report builder, industry templates, custom formulas, and export capabilities
 */

import { db } from '@/lib/firebase';
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp,
  writeBatch
} from 'firebase/firestore';

export interface ReportField {
  id: string;
  name: string;
  displayName: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'currency' | 'percentage' | 'calculated';
  source: string; // Collection or table name
  path: string; // Field path in the document
  aggregation?: 'sum' | 'avg' | 'count' | 'min' | 'max' | 'distinct';
  format?: {
    currency?: string;
    decimals?: number;
    dateFormat?: string;
    prefix?: string;
    suffix?: string;
  };
  isFilterable: boolean;
  isSortable: boolean;
  isGroupable: boolean;
}

export interface ReportFilter {
  id: string;
  fieldId: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'between' | 'in' | 'not_in' | 'is_null' | 'is_not_null';
  value: any;
  secondValue?: any; // For 'between' operator
  logicalOperator?: 'AND' | 'OR';
}

export interface ReportSort {
  fieldId: string;
  direction: 'asc' | 'desc';
  priority: number;
}

export interface ReportGrouping {
  fieldId: string;
  level: number;
  showSubtotals: boolean;
  collapseByDefault: boolean;
}

export interface CustomFormula {
  id: string;
  name: string;
  expression: string;
  returnType: 'number' | 'string' | 'date' | 'boolean';
  description: string;
  variables: Array<{
    name: string;
    fieldId: string;
    type: string;
  }>;
}

export interface ReportVisualization {
  type: 'table' | 'chart' | 'pivot' | 'card' | 'gauge' | 'map';
  config: {
    // Chart specific
    chartType?: 'bar' | 'line' | 'pie' | 'area' | 'scatter' | 'funnel' | 'treemap';
    xAxis?: string;
    yAxis?: string[];
    colorBy?: string;
    
    // Table specific
    showRowNumbers?: boolean;
    alternateRowColors?: boolean;
    freezeColumns?: number;
    
    // Pivot specific
    rows?: string[];
    columns?: string[];
    values?: string[];
    
    // Card specific
    metric?: string;
    comparison?: string;
    trend?: string;
    
    // Gauge specific
    value?: string;
    min?: number;
    max?: number;
    target?: number;
    
    // Map specific
    locationField?: string;
    valueField?: string;
    mapType?: 'world' | 'country' | 'region';
  };
}

export interface CustomReport {
  id: string;
  tenantId: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  
  // Data Configuration
  dataSources: Array<{
    id: string;
    name: string;
    collection: string;
    joins?: Array<{
      targetCollection: string;
      localField: string;
      foreignField: string;
      type: 'inner' | 'left' | 'right';
    }>;
  }>;
  
  // Report Structure
  fields: string[]; // Field IDs to include
  filters: ReportFilter[];
  sorting: ReportSort[];
  grouping: ReportGrouping[];
  formulas: CustomFormula[];
  
  // Visualization
  visualization: ReportVisualization;
  
  // Scheduling & Automation
  schedule?: {
    enabled: boolean;
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
    time: string; // HH:MM format
    dayOfWeek?: number; // 0-6 for weekly
    dayOfMonth?: number; // 1-31 for monthly
    recipients: string[];
    format: 'pdf' | 'excel' | 'csv' | 'json';
  };
  
  // Access Control
  permissions: {
    owner: string;
    viewers: string[];
    editors: string[];
    isPublic: boolean;
    shareableLink?: string;
  };
  
  // Performance
  caching: {
    enabled: boolean;
    ttl: number; // Time to live in minutes
    lastCached?: Date;
    cacheKey?: string;
  };
  
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    lastModifiedBy: string;
    version: number;
    executionCount: number;
    lastExecuted?: Date;
    avgExecutionTime?: number;
  };
}

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: 'exhibition' | 'financial' | 'marketing' | 'operations' | 'analytics' | 'compliance';
  industry?: string;
  thumbnail?: string;
  
  // Template Configuration
  template: Omit<CustomReport, 'id' | 'tenantId' | 'metadata' | 'permissions'>;
  
  // Template Metadata
  isBuiltIn: boolean;
  popularity: number;
  rating: number;
  downloadCount: number;
  
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    version: string;
  };
}

export interface ReportExecution {
  id: string;
  reportId: string;
  tenantId: string;
  
  // Execution Details
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  executionTime?: number; // milliseconds
  
  // Results
  data?: any[];
  rowCount?: number;
  error?: string;
  
  // Export Information
  exports: Array<{
    format: 'pdf' | 'excel' | 'csv' | 'json';
    url: string;
    size: number;
    createdAt: Date;
    expiresAt: Date;
  }>;
  
  // Execution Context
  parameters: Record<string, any>;
  executedBy: string;
  executionType: 'manual' | 'scheduled' | 'api';
  
  metadata: {
    createdAt: Date;
    cacheHit: boolean;
    dataSourcesQueried: string[];
    totalRecordsProcessed: number;
  };
}

class CustomReportingService {
  private reportsCollection = 'custom_reports';
  private templatesCollection = 'report_templates';
  private executionsCollection = 'report_executions';
  private fieldsCollection = 'report_fields';
  private cacheCollection = 'report_cache';

  // Report Management
  async createReport(report: Omit<CustomReport, 'id' | 'metadata'>): Promise<CustomReport> {
    const now = new Date();
    const reportData: Omit<CustomReport, 'id'> = {
      ...report,
      metadata: {
        createdAt: now,
        updatedAt: now,
        createdBy: 'current-user',
        lastModifiedBy: 'current-user',
        version: 1,
        executionCount: 0
      }
    };

    const docRef = await addDoc(collection(db, this.reportsCollection), {
      ...reportData,
      'metadata.createdAt': Timestamp.fromDate(now),
      'metadata.updatedAt': Timestamp.fromDate(now),
      'schedule.time': report.schedule?.time,
      'caching.lastCached': report.caching.lastCached ? Timestamp.fromDate(report.caching.lastCached) : null,
      'metadata.lastExecuted': report.metadata?.lastExecuted ? Timestamp.fromDate(report.metadata.lastExecuted) : null
    });

    return { ...reportData, id: docRef.id };
  }

  async executeReport(
    reportId: string,
    parameters: Record<string, any> = {},
    executedBy: string = 'current-user',
    executionType: ReportExecution['executionType'] = 'manual'
  ): Promise<ReportExecution> {
    const report = await this.getReport(reportId);
    if (!report) {
      throw new Error('Report not found');
    }

    // Create execution record
    const execution: Omit<ReportExecution, 'id'> = {
      reportId,
      tenantId: report.tenantId,
      status: 'pending',
      startTime: new Date(),
      parameters,
      executedBy,
      executionType,
      exports: [],
      metadata: {
        createdAt: new Date(),
        cacheHit: false,
        dataSourcesQueried: [],
        totalRecordsProcessed: 0
      }
    };

    const executionRef = await addDoc(collection(db, this.executionsCollection), {
      ...execution,
      startTime: Timestamp.fromDate(execution.startTime),
      'metadata.createdAt': Timestamp.fromDate(execution.metadata.createdAt)
    });

    const executionId = executionRef.id;

    try {
      // Check cache first
      if (report.caching.enabled) {
        const cachedResult = await this.getCachedResult(report, parameters);
        if (cachedResult) {
          await this.updateExecution(executionId, {
            status: 'completed',
            endTime: new Date(),
            executionTime: 50, // Fast cache hit
            data: cachedResult.data,
            rowCount: cachedResult.rowCount,
            'metadata.cacheHit': true
          });

          return { ...execution, id: executionId, ...cachedResult };
        }
      }

      // Update status to running
      await this.updateExecution(executionId, { status: 'running' });

      // Execute the report
      const result = await this.executeReportQuery(report, parameters);

      // Cache the result if caching is enabled
      if (report.caching.enabled) {
        await this.cacheResult(report, parameters, result);
      }

      // Update execution with results
      const endTime = new Date();
      const executionTime = endTime.getTime() - execution.startTime.getTime();

      await this.updateExecution(executionId, {
        status: 'completed',
        endTime,
        executionTime,
        data: result.data,
        rowCount: result.rowCount,
        'metadata.totalRecordsProcessed': result.totalRecordsProcessed,
        'metadata.dataSourcesQueried': result.dataSourcesQueried
      });

      // Update report metadata
      await this.updateReportMetadata(reportId, {
        executionCount: report.metadata.executionCount + 1,
        lastExecuted: endTime,
        avgExecutionTime: report.metadata.avgExecutionTime 
          ? (report.metadata.avgExecutionTime + executionTime) / 2 
          : executionTime
      });

      return {
        ...execution,
        id: executionId,
        status: 'completed',
        endTime,
        executionTime,
        data: result.data,
        rowCount: result.rowCount
      };

    } catch (error) {
      // Update execution with error
      await this.updateExecution(executionId, {
        status: 'failed',
        endTime: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      throw error;
    }
  }

  private async executeReportQuery(
    report: CustomReport,
    parameters: Record<string, any>
  ): Promise<{ data: any[]; rowCount: number; totalRecordsProcessed: number; dataSourcesQueried: string[] }> {
    const dataSourcesQueried: string[] = [];
    let totalRecordsProcessed = 0;
    let data: any[] = [];

    // For each data source, execute the query
    for (const dataSource of report.dataSources) {
      dataSourcesQueried.push(dataSource.collection);

      // Build Firestore query
      let q = collection(db, dataSource.collection);

      // Apply tenant filter
      let firestoreQuery = query(q, where('tenantId', '==', report.tenantId));

      // Apply filters
      for (const filter of report.filters) {
        const field = await this.getReportField(filter.fieldId);
        if (field) {
          firestoreQuery = this.applyFilter(firestoreQuery, field, filter, parameters);
        }
      }

      // Apply sorting
      for (const sort of report.sorting.sort((a, b) => a.priority - b.priority)) {
        const field = await this.getReportField(sort.fieldId);
        if (field) {
          firestoreQuery = query(firestoreQuery, orderBy(field.path, sort.direction));
        }
      }

      // Execute query
      const querySnapshot = await getDocs(firestoreQuery);
      const sourceData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        _source: dataSource.collection
      }));

      totalRecordsProcessed += sourceData.length;

      // If this is the first data source, use it as base
      if (data.length === 0) {
        data = sourceData;
      } else {
        // Handle joins if specified
        if (dataSource.joins) {
          data = this.performJoins(data, sourceData, dataSource.joins);
        }
      }
    }

    // Apply grouping
    if (report.grouping.length > 0) {
      data = await this.applyGrouping(data, report.grouping, report.fields);
    }

    // Apply custom formulas
    if (report.formulas.length > 0) {
      data = await this.applyFormulas(data, report.formulas);
    }

    // Select only requested fields
    data = this.selectFields(data, report.fields);

    return {
      data,
      rowCount: data.length,
      totalRecordsProcessed,
      dataSourcesQueried
    };
  }

  private applyFilter(
    query: any,
    field: ReportField,
    filter: ReportFilter,
    parameters: Record<string, any>
  ): any {
    let value = filter.value;

    // Replace parameter placeholders
    if (typeof value === 'string' && value.startsWith('{{') && value.endsWith('}}')) {
      const paramName = value.slice(2, -2);
      value = parameters[paramName];
    }

    switch (filter.operator) {
      case 'equals':
        return query(query, where(field.path, '==', value));
      case 'not_equals':
        return query(query, where(field.path, '!=', value));
      case 'greater_than':
        return query(query, where(field.path, '>', value));
      case 'less_than':
        return query(query, where(field.path, '<', value));
      case 'in':
        return query(query, where(field.path, 'in', Array.isArray(value) ? value : [value]));
      case 'not_in':
        return query(query, where(field.path, 'not-in', Array.isArray(value) ? value : [value]));
      // Note: Firestore has limitations on complex queries
      // For 'contains', 'between', etc., we'll need to filter in memory
      default:
        return query;
    }
  }

  private performJoins(
    leftData: any[],
    rightData: any[],
    joins: Array<{ targetCollection: string; localField: string; foreignField: string; type: 'inner' | 'left' | 'right' }>
  ): any[] {
    const result: any[] = [];

    for (const join of joins) {
      for (const leftRow of leftData) {
        const matchingRightRows = rightData.filter(rightRow =>
          leftRow[join.localField] === rightRow[join.foreignField]
        );

        if (matchingRightRows.length > 0) {
          for (const rightRow of matchingRightRows) {
            result.push({ ...leftRow, ...rightRow });
          }
        } else if (join.type === 'left') {
          result.push(leftRow);
        }
      }
    }

    return result;
  }

  private async applyGrouping(
    data: any[],
    grouping: ReportGrouping[],
    fieldIds: string[]
  ): Promise<any[]> {
    if (grouping.length === 0) return data;

    // Sort grouping by level
    const sortedGrouping = grouping.sort((a, b) => a.level - b.level);

    // Group data recursively
    return this.groupDataRecursively(data, sortedGrouping, 0, fieldIds);
  }

  private async groupDataRecursively(
    data: any[],
    grouping: ReportGrouping[],
    level: number,
    fieldIds: string[]
  ): Promise<any[]> {
    if (level >= grouping.length) return data;

    const currentGrouping = grouping[level];
    const field = await this.getReportField(currentGrouping.fieldId);

    if (!field) return data;

    // Group by current field
    const groups = new Map<any, any[]>();

    for (const row of data) {
      const groupValue = row[field.path];
      if (!groups.has(groupValue)) {
        groups.set(groupValue, []);
      }
      groups.get(groupValue)!.push(row);
    }

    const result: any[] = [];

    for (const [groupValue, groupData] of groups) {
      // Add group header
      result.push({
        _isGroupHeader: true,
        _groupLevel: level,
        _groupField: field.name,
        _groupValue: groupValue,
        _groupCount: groupData.length,
        _collapsed: currentGrouping.collapseByDefault
      });

      // Recursively group nested levels
      const nestedData = await this.groupDataRecursively(groupData, grouping, level + 1, fieldIds);
      result.push(...nestedData);

      // Add subtotals if requested
      if (currentGrouping.showSubtotals) {
        const subtotals = this.calculateSubtotals(groupData, fieldIds);
        result.push({
          _isSubtotal: true,
          _groupLevel: level,
          _groupField: field.name,
          _groupValue: groupValue,
          ...subtotals
        });
      }
    }

    return result;
  }

  private calculateSubtotals(data: any[], fieldIds: string[]): Record<string, any> {
    const subtotals: Record<string, any> = {};

    for (const fieldId of fieldIds) {
      // This would need to be implemented based on field types and aggregation functions
      // For now, we'll just count
      subtotals[`${fieldId}_count`] = data.length;
    }

    return subtotals;
  }

  private async applyFormulas(data: any[], formulas: CustomFormula[]): Promise<any[]> {
    return data.map(row => {
      const enhancedRow = { ...row };

      for (const formula of formulas) {
        try {
          // Simple formula evaluation - in production, you'd want a proper expression parser
          let expression = formula.expression;

          // Replace variables with actual values
          for (const variable of formula.variables) {
            const value = row[variable.fieldId] || 0;
            expression = expression.replace(new RegExp(`\\b${variable.name}\\b`, 'g'), String(value));
          }

          // Evaluate simple mathematical expressions
          const result = this.evaluateExpression(expression);
          enhancedRow[formula.id] = result;
        } catch (error) {
          enhancedRow[formula.id] = null;
        }
      }

      return enhancedRow;
    });
  }

  private evaluateExpression(expression: string): number {
    // Simple expression evaluator - in production, use a proper math parser
    try {
      // Remove any non-mathematical characters for security
      const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
      return Function(`"use strict"; return (${sanitized})`)();
    } catch {
      return 0;
    }
  }

  private selectFields(data: any[], fieldIds: string[]): any[] {
    if (fieldIds.length === 0) return data;

    return data.map(row => {
      const selectedRow: any = {};

      // Always include metadata fields
      if (row._isGroupHeader) selectedRow._isGroupHeader = row._isGroupHeader;
      if (row._isSubtotal) selectedRow._isSubtotal = row._isSubtotal;
      if (row._groupLevel !== undefined) selectedRow._groupLevel = row._groupLevel;
      if (row._groupField) selectedRow._groupField = row._groupField;
      if (row._groupValue) selectedRow._groupValue = row._groupValue;
      if (row._groupCount) selectedRow._groupCount = row._groupCount;
      if (row._collapsed !== undefined) selectedRow._collapsed = row._collapsed;

      // Include selected fields
      for (const fieldId of fieldIds) {
        if (row[fieldId] !== undefined) {
          selectedRow[fieldId] = row[fieldId];
        }
      }

      return selectedRow;
    });
  }

  // Helper Methods
  private async getReport(reportId: string): Promise<CustomReport | null> {
    const reportDoc = await getDoc(doc(db, this.reportsCollection, reportId));

    if (!reportDoc.exists()) return null;

    const data = reportDoc.data();
    return {
      id: reportDoc.id,
      ...data,
      'metadata.createdAt': data.metadata.createdAt.toDate(),
      'metadata.updatedAt': data.metadata.updatedAt.toDate(),
      'metadata.lastExecuted': data.metadata.lastExecuted?.toDate(),
      'caching.lastCached': data.caching.lastCached?.toDate()
    } as CustomReport;
  }

  private async getReportField(fieldId: string): Promise<ReportField | null> {
    const fieldDoc = await getDoc(doc(db, this.fieldsCollection, fieldId));

    if (!fieldDoc.exists()) return null;

    return {
      id: fieldDoc.id,
      ...fieldDoc.data()
    } as ReportField;
  }

  private async updateExecution(executionId: string, updates: Partial<ReportExecution>): Promise<void> {
    const updateData: any = { ...updates };

    if (updates.endTime) {
      updateData.endTime = Timestamp.fromDate(updates.endTime);
    }

    await updateDoc(doc(db, this.executionsCollection, executionId), updateData);
  }

  private async updateReportMetadata(reportId: string, updates: Partial<CustomReport['metadata']>): Promise<void> {
    const updateData: any = {};

    Object.entries(updates).forEach(([key, value]) => {
      if (value instanceof Date) {
        updateData[`metadata.${key}`] = Timestamp.fromDate(value);
      } else {
        updateData[`metadata.${key}`] = value;
      }
    });

    await updateDoc(doc(db, this.reportsCollection, reportId), updateData);
  }

  private async getCachedResult(report: CustomReport, parameters: Record<string, any>): Promise<any | null> {
    if (!report.caching.enabled || !report.caching.cacheKey) return null;

    const cacheKey = this.generateCacheKey(report.id, parameters);
    const cacheDoc = await getDoc(doc(db, this.cacheCollection, cacheKey));

    if (!cacheDoc.exists()) return null;

    const cacheData = cacheDoc.data();
    const cachedAt = cacheData.cachedAt.toDate();
    const ttlMs = report.caching.ttl * 60 * 1000;

    if (Date.now() - cachedAt.getTime() > ttlMs) {
      return null; // Cache expired
    }

    return cacheData.result;
  }

  private async cacheResult(report: CustomReport, parameters: Record<string, any>, result: any): Promise<void> {
    const cacheKey = this.generateCacheKey(report.id, parameters);

    await updateDoc(doc(db, this.cacheCollection, cacheKey), {
      reportId: report.id,
      parameters,
      result,
      cachedAt: Timestamp.fromDate(new Date()),
      expiresAt: Timestamp.fromDate(new Date(Date.now() + report.caching.ttl * 60 * 1000))
    });
  }

  private generateCacheKey(reportId: string, parameters: Record<string, any>): string {
    const paramString = JSON.stringify(parameters, Object.keys(parameters).sort());
    return `${reportId}_${Buffer.from(paramString).toString('base64')}`;
  }

  // Public API Methods
  async getReports(tenantId: string, category?: string): Promise<CustomReport[]> {
    let q = query(
      collection(db, this.reportsCollection),
      where('tenantId', '==', tenantId),
      orderBy('metadata.updatedAt', 'desc')
    );

    if (category) {
      q = query(q, where('category', '==', category));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        'metadata.createdAt': data.metadata.createdAt.toDate(),
        'metadata.updatedAt': data.metadata.updatedAt.toDate(),
        'metadata.lastExecuted': data.metadata.lastExecuted?.toDate(),
        'caching.lastCached': data.caching.lastCached?.toDate()
      } as CustomReport;
    });
  }

  async getReportTemplates(category?: string, industry?: string): Promise<ReportTemplate[]> {
    let q = query(
      collection(db, this.templatesCollection),
      orderBy('popularity', 'desc')
    );

    if (category) {
      q = query(q, where('category', '==', category));
    }

    if (industry) {
      q = query(q, where('industry', '==', industry));
    }

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        'metadata.createdAt': data.metadata.createdAt.toDate(),
        'metadata.updatedAt': data.metadata.updatedAt.toDate()
      } as ReportTemplate;
    });
  }

  async getAvailableFields(tenantId: string): Promise<ReportField[]> {
    const q = query(collection(db, this.fieldsCollection));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as ReportField[];
  }
}

export const customReportingService = new CustomReportingService();
