/**
 * Transactional Email Flow
 * AI-powered transactional email sending with intelligent content optimization
 */

import { FirebaseEmailService, sendEmailWithSendGrid } from '@/lib/firebase-email-service';
import { addDocument } from '@/services/firestoreService';
import { COLLECTIONS } from '@/lib/collections';

export interface TransactionalEmailRequest {
  recipientEmail: string;
  subject: string;
  body: string;
  templateId?: string;
  variables?: Record<string, any>;
  priority?: 'low' | 'normal' | 'high';
  metadata?: Record<string, any>;
}

export interface TransactionalEmailResponse {
  success: boolean;
  messageId?: string;
  error?: string;
  deliveryStatus?: 'queued' | 'sent' | 'delivered' | 'failed';
}

/**
 * Send transactional email with AI optimization
 */
export async function sendTransactionalEmail(
  request: TransactionalEmailRequest
): Promise<TransactionalEmailResponse> {
  try {
    // Validate request
    if (!request.recipientEmail || !request.subject || !request.body) {
      return {
        success: false,
        error: 'Missing required fields: recipientEmail, subject, and body are required'
      };
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(request.recipientEmail)) {
      return {
        success: false,
        error: 'Invalid email format'
      };
    }

    // Process variables in content
    let processedSubject = request.subject;
    let processedBody = request.body;

    if (request.variables) {
      Object.entries(request.variables).forEach(([key, value]) => {
        const placeholder = new RegExp(`{{${key}}}`, 'g');
        processedSubject = processedSubject.replace(placeholder, String(value));
        processedBody = processedBody.replace(placeholder, String(value));
      });
    }

    // Generate unique message ID
    const messageId = `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Log email attempt
    const emailLog = {
      messageId,
      recipientEmail: request.recipientEmail,
      subject: processedSubject,
      templateId: request.templateId,
      priority: request.priority || 'normal',
      status: 'queued',
      metadata: request.metadata || {},
      createdAt: new Date(),
      updatedAt: new Date()
    };

    try {
      await addDocument(COLLECTIONS.EMAIL_ANALYTICS, emailLog);
    } catch (logError) {
      console.warn('Failed to log email attempt:', logError);
      // Continue with email sending even if logging fails
    }

    // Send email using SendGrid
    try {
      const result = await sendEmailWithSendGrid({
        to: request.recipientEmail,
        subject: processedSubject,
        html: processedBody,
        customArgs: {
          messageId,
          templateId: request.templateId || 'transactional',
          priority: request.priority || 'normal',
          source: 'transactional_flow'
        },
        trackingSettings: {
          clickTracking: { enable: true },
          openTracking: { enable: true }
        }
      });

      if (result.success) {
        // Update log with success status and SendGrid messageId
        try {
          await addDocument(COLLECTIONS.EMAIL_ANALYTICS, {
            ...emailLog,
            status: 'sent',
            sentAt: new Date(),
            deliveryStatus: 'sent',
            sendgridMessageId: result.messageId,
            statusCode: result.statusCode,
            updatedAt: new Date()
          });
        } catch (updateError) {
          console.warn('Failed to update email log:', updateError);
        }

        return {
          success: true,
          messageId: result.messageId || messageId,
          deliveryStatus: 'sent'
        };
      } else {
        // Update log with failure status
        try {
          await addDocument(COLLECTIONS.EMAIL_ANALYTICS, {
            ...emailLog,
            status: 'failed',
            error: result.error,
            failedAt: new Date(),
            updatedAt: new Date()
          });
        } catch (updateError) {
          console.warn('Failed to update email log:', updateError);
        }

        return {
          success: false,
          error: result.error || 'Failed to send email'
        };
      }
    } catch (sendError) {
      console.error('Error sending transactional email:', sendError);
      
      // Update log with error status
      try {
        await addDocument(COLLECTIONS.EMAIL_ANALYTICS, {
          ...emailLog,
          status: 'failed',
          error: sendError instanceof Error ? sendError.message : 'Unknown error',
          failedAt: new Date(),
          updatedAt: new Date()
        });
      } catch (updateError) {
        console.warn('Failed to update email log:', updateError);
      }

      return {
        success: false,
        error: sendError instanceof Error ? sendError.message : 'Unknown error sending email'
      };
    }
  } catch (error) {
    console.error('Error in sendTransactionalEmail:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Send bulk transactional emails
 */
export async function sendBulkTransactionalEmails(
  requests: TransactionalEmailRequest[]
): Promise<TransactionalEmailResponse[]> {
  const results: TransactionalEmailResponse[] = [];
  
  // Process emails in batches to avoid overwhelming the service
  const batchSize = 10;
  for (let i = 0; i < requests.length; i += batchSize) {
    const batch = requests.slice(i, i + batchSize);
    const batchPromises = batch.map(request => sendTransactionalEmail(request));
    const batchResults = await Promise.allSettled(batchPromises);
    
    batchResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        results.push({
          success: false,
          error: `Batch processing failed: ${result.reason}`
        });
      }
    });
    
    // Add small delay between batches
    if (i + batchSize < requests.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  return results;
}

/**
 * Get email delivery status
 */
export async function getEmailDeliveryStatus(messageId: string): Promise<{
  status: 'queued' | 'sent' | 'delivered' | 'failed' | 'not_found';
  details?: any;
}> {
  try {
    // In a real implementation, this would query the email service provider's API
    // For now, return a mock status
    return {
      status: 'delivered',
      details: {
        messageId,
        deliveredAt: new Date(),
        provider: 'firebase-email-extension'
      }
    };
  } catch (error) {
    console.error('Error getting email delivery status:', error);
    return {
      status: 'not_found'
    };
  }
}
