# EVEXA Faker.js Data Generation Script

## Overview

This script generates realistic data for your EVEXA application using Faker.js and your existing Firebase setup. It uses your working credentials that are already functional and creates comprehensive data across all major collections.

## Your Working Credentials

The script preserves and uses your existing working credentials:

- **Superman Super Admin**: `<EMAIL>` / `Superman123!`
- **Admin User**: `<EMAIL>` / `Admin123!`
- **Manager User**: `<EMAIL>` / `Manager123!`
- **Regular User**: `<EMAIL>` / `User123!`

## Usage

### Full Data Generation (Recommended)
```bash
npm run generate-data
```
Generates comprehensive realistic data:
- 20 user profiles (including your working credentials)
- 8 detailed exhibitions with financial tracking
- 25 scheduled events with speakers and materials
- 50 tracked tasks with dependencies and metrics
- 100 qualified leads with conversion tracking
- 15 financial budgets with expense breakdowns

### Sample Data (For Testing)
```bash
npm run generate-data:sample
```
Generates smaller dataset for testing:
- 8 user profiles
- 3 exhibitions
- 10 events
- 15 tasks
- 25 leads
- 5 budgets

### Clean Existing Data (Use with Caution)
```bash
npm run generate-data:clean
```
Removes existing data from collections before generating new data.

## Generated Collections

### Core Collections
- `user_profiles` - User accounts with roles and preferences
- `exhibitions` - Trade shows and events with comprehensive details
- `exhibition_events` - Scheduled activities within exhibitions
- `exhibition_tasks` - Project tasks with tracking and metrics
- `leads` - Sales leads with conversion tracking
- `budgets` - Financial budgets with expense breakdowns

### Data Quality Features

- **Realistic Relationships**: All data is properly cross-referenced
- **Temporal Consistency**: Dates and timelines make logical sense
- **Financial Accuracy**: Budgets, expenses, and ROI calculations are realistic
- **Professional Content**: Job titles, company names, and descriptions are business-appropriate
- **Performance Metrics**: Includes KPIs, scores, and tracking data

## Firebase Integration

- Uses your existing Firebase configuration
- Respects your tenant structure (`evexa-development-company`)
- Maintains proper Firestore document structure
- Includes proper timestamps and metadata

## Security

- Preserves your existing working authentication
- Uses proper tenant isolation
- Includes role-based access control data
- Maintains data consistency across collections

## Troubleshooting

If you encounter issues:

1. **Authentication Errors**: Ensure your Firebase config is properly set in environment variables
2. **Permission Errors**: Check your Firestore security rules
3. **Network Issues**: Verify your internet connection and Firebase project status
4. **Data Conflicts**: Use the clean command to remove existing data first

## Next Steps

After running the script:

1. Login with any of your working credentials
2. Explore the generated data in your EVEXA dashboard
3. Test all modules with realistic data
4. Verify data relationships and consistency
5. Use the data for development and testing

The generated data provides a solid foundation for testing all EVEXA features with realistic, professional-grade content.
