#!/usr/bin/env tsx

/**
 * Initialize Professional Tenant
 * Creates the required tenant for the professional system
 */

// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

import { initializeApp } from 'firebase/app';
import { 
  getFirestore,
  doc,
  setDoc,
  Timestamp
} from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

/**
 * Create professional tenant
 */
async function createProfessionalTenant() {
  console.log('🏢 Creating professional tenant...');

  const tenantData = {
    id: 'evexa-super-admin-tenant',
    name: 'EVEXA Development Company',
    slug: 'evexa-dev',
    domain: 'evexa.ai',
    status: 'active',
    plan: 'enterprise',
    ownerId: 'QRuQpzQJkvgKaC1lACwG3yQBV6g2', // Superman's ID
    adminIds: ['QRuQpzQJkvgKaC1lACwG3yQBV6g2'],
    settings: {
      timezone: 'UTC',
      dateFormat: 'MM/dd/yyyy',
      timeFormat: '12h',
      currency: 'USD',
      language: 'en',
      allowUserRegistration: false,
      requireEmailVerification: true,
      sessionTimeout: 480,
      dataRetentionDays: 365,
      backupEnabled: true,
      auditLogEnabled: true,
      maxUsers: 10000,
      maxExhibitions: 10000,
      features: ['*'], // All features
      customBranding: true,
      apiAccess: true
    },
    branding: {
      primaryColor: '#2563eb',
      secondaryColor: '#64748b',
      accentColor: '#0ea5e9',
      theme: 'default',
      logoUrl: '',
      faviconUrl: '',
      customCss: ''
    },
    billing: {
      currentPeriodStart: Timestamp.now(),
      currentPeriodEnd: Timestamp.fromDate(new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)), // 1 year
      subscriptionId: 'evexa-dev-subscription',
      customerId: 'evexa-dev-customer',
      status: 'active'
    },
    metadata: {
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      createdBy: 'system',
      industry: 'Software Development',
      companySize: '1-10',
      country: 'Global',
      region: 'us-central1',
      deploymentType: 'saas'
    },
    features: {
      exhibitions: true,
      events: true,
      tasks: true,
      leads: true,
      budgets: true,
      analytics: true,
      integrations: true,
      customBranding: true,
      apiAccess: true,
      advancedReporting: true,
      multiTenant: true,
      sso: true,
      auditLogs: true,
      dataExport: true,
      customFields: true
    }
  };

  try {
    // Create tenant document
    const tenantRef = doc(db, 'tenants', tenantData.id);
    await setDoc(tenantRef, tenantData);
    
    console.log('✅ Professional tenant created successfully!');
    console.log(`   Tenant ID: ${tenantData.id}`);
    console.log(`   Tenant Name: ${tenantData.name}`);
    console.log(`   Owner: ${tenantData.ownerId}`);
    
    return tenantData;
  } catch (error) {
    console.error('❌ Error creating professional tenant:', error);
    throw error;
  }
}

/**
 * Create demo tenant as well
 */
async function createDemoTenant() {
  console.log('🎭 Creating demo tenant...');

  const demoTenantData = {
    id: 'evexa-demo-showcase',
    name: 'EVEXA Demo Showcase',
    slug: 'evexa-demo',
    domain: 'demo.evexa.ai',
    status: 'active',
    plan: 'enterprise',
    ownerId: 'QRuQpzQJkvgKaC1lACwG3yQBV6g2', // Superman's ID
    adminIds: ['QRuQpzQJkvgKaC1lACwG3yQBV6g2'],
    settings: {
      timezone: 'UTC',
      dateFormat: 'MM/dd/yyyy',
      timeFormat: '12h',
      currency: 'USD',
      language: 'en',
      allowUserRegistration: false,
      requireEmailVerification: true,
      sessionTimeout: 480,
      dataRetentionDays: 365,
      backupEnabled: true,
      auditLogEnabled: true,
      maxUsers: 1000,
      maxExhibitions: 100,
      features: ['*'], // All features
      customBranding: true,
      apiAccess: true
    },
    branding: {
      primaryColor: '#059669',
      secondaryColor: '#64748b',
      accentColor: '#10b981',
      theme: 'default',
      logoUrl: '',
      faviconUrl: '',
      customCss: ''
    },
    billing: {
      currentPeriodStart: Timestamp.now(),
      currentPeriodEnd: Timestamp.fromDate(new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)), // 1 year
      subscriptionId: 'evexa-demo-subscription',
      customerId: 'evexa-demo-customer',
      status: 'active'
    },
    metadata: {
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      createdBy: 'system',
      industry: 'Demo/Showcase',
      companySize: '1-10',
      country: 'Global',
      region: 'us-central1',
      deploymentType: 'saas'
    },
    features: {
      exhibitions: true,
      events: true,
      tasks: true,
      leads: true,
      budgets: true,
      analytics: true,
      integrations: true,
      customBranding: true,
      apiAccess: true,
      advancedReporting: true,
      multiTenant: true,
      sso: true,
      auditLogs: true,
      dataExport: true,
      customFields: true
    }
  };

  try {
    // Create demo tenant document
    const demoTenantRef = doc(db, 'tenants', demoTenantData.id);
    await setDoc(demoTenantRef, demoTenantData);
    
    console.log('✅ Demo tenant created successfully!');
    console.log(`   Tenant ID: ${demoTenantData.id}`);
    console.log(`   Tenant Name: ${demoTenantData.name}`);
    
    return demoTenantData;
  } catch (error) {
    console.error('❌ Error creating demo tenant:', error);
    throw error;
  }
}

/**
 * Main initialization function
 */
async function initializeProfessionalTenants() {
  try {
    console.log('🚀 Initializing professional tenants...\n');
    
    const devTenant = await createProfessionalTenant();
    const demoTenant = await createDemoTenant();
    
    console.log('\n🎉 Professional tenant initialization complete!');
    console.log('📊 Summary:');
    console.log(`   Development Tenant: ${devTenant.name} (${devTenant.id})`);
    console.log(`   Demo Tenant: ${demoTenant.name} (${demoTenant.id})`);
    console.log('\n✨ Your super admin dashboard should now work properly!');
    
  } catch (error) {
    console.error('❌ Professional tenant initialization failed:', error);
    process.exit(1);
  }
}

// Run initialization
if (require.main === module) {
  initializeProfessionalTenants();
}

export { initializeProfessionalTenants, createProfessionalTenant, createDemoTenant };
