/**
 * Default <PERSON> Templates for EVEXA
 * Pre-configured email, SMS, and in-app notification templates for payment recovery
 */

import { DunningTemplate } from '@/services/automatedDunningService';

export const defaultDunningTemplates: Omit<DunningTemplate, 'id' | 'metadata'>[] = [
  // Initial Payment Failure
  {
    name: 'Initial Payment Failure - Email',
    type: 'email',
    stage: 'initial',
    subject: 'Payment Issue - {{companyName}} Account',
    content: `
Dear {{customerName}},

We hope this message finds you well. We're writing to inform you that we encountered an issue processing your recent payment for your {{companyName}} subscription.

**Payment Details:**
- Invoice: {{invoiceId}}
- Amount: {{amount}}
- Due Date: {{dueDate}}

**What happened:**
Your payment could not be processed due to a temporary issue. This is often due to insufficient funds, an expired card, or a temporary hold by your bank.

**What you need to do:**
Please update your payment method or ensure sufficient funds are available, then visit your billing portal to retry the payment:

{{paymentUrl}}

**Need help?**
If you have any questions or need assistance, please don't hesitate to contact our support team:
- Email: {{supportEmail}}
- Phone: {{supportPhone}}

We'll automatically retry your payment in the next few days, but updating your payment information now will ensure uninterrupted service.

Thank you for your prompt attention to this matter.

Best regards,
The {{companyName}} Team
    `,
    variables: ['customerName', 'companyName', 'invoiceId', 'amount', 'dueDate', 'paymentUrl', 'supportEmail', 'supportPhone'],
    isActive: true
  },

  {
    name: 'Initial Payment Failure - SMS',
    type: 'sms',
    stage: 'initial',
    content: `{{companyName}}: Payment issue for invoice {{invoiceId}} ({{amount}}). Please update payment method: {{paymentUrl}} Questions? {{supportPhone}}`,
    variables: ['companyName', 'invoiceId', 'amount', 'paymentUrl', 'supportPhone'],
    isActive: true
  },

  {
    name: 'Initial Payment Failure - In-App',
    type: 'in_app',
    stage: 'initial',
    content: `Payment processing failed for invoice {{invoiceId}} ({{amount}}). Please update your payment method to continue using {{companyName}} services.`,
    variables: ['invoiceId', 'amount', 'companyName'],
    isActive: true
  },

  // First Reminder
  {
    name: 'First Reminder - Email',
    type: 'email',
    stage: 'reminder_1',
    subject: 'Payment Reminder - {{companyName}} Account',
    content: `
Dear {{customerName}},

This is a friendly reminder that we still haven't received payment for your {{companyName}} subscription.

**Payment Details:**
- Invoice: {{invoiceId}}
- Amount: {{amount}}
- Original Due Date: {{dueDate}}
- Payment Attempts: {{attemptCount}} of {{maxRetries}}

**Action Required:**
To avoid any service interruption, please process your payment as soon as possible:

{{paymentUrl}}

**Why this matters:**
Your {{companyName}} subscription provides valuable tools for your exhibition and event management. We want to ensure you continue to have access to all features without interruption.

**Need assistance?**
Our billing team is here to help:
- Email: {{supportEmail}}
- Phone: {{supportPhone}}

We appreciate your prompt attention to this matter.

Best regards,
The {{companyName}} Billing Team
    `,
    variables: ['customerName', 'companyName', 'invoiceId', 'amount', 'dueDate', 'attemptCount', 'maxRetries', 'paymentUrl', 'supportEmail', 'supportPhone'],
    isActive: true
  },

  {
    name: 'First Reminder - SMS',
    type: 'sms',
    stage: 'reminder_1',
    content: `{{companyName}} Reminder: Payment still pending for {{amount}}. Avoid service interruption: {{paymentUrl}} Support: {{supportPhone}}`,
    variables: ['companyName', 'amount', 'paymentUrl', 'supportPhone'],
    isActive: true
  },

  // Second Reminder
  {
    name: 'Second Reminder - Email',
    type: 'email',
    stage: 'reminder_2',
    subject: 'Urgent: Payment Required - {{companyName}}',
    content: `
Dear {{customerName}},

We've attempted to process your payment multiple times without success. Your {{companyName}} account requires immediate attention.

**Payment Details:**
- Invoice: {{invoiceId}}
- Amount: {{amount}}
- Original Due Date: {{dueDate}}
- Payment Attempts: {{attemptCount}} of {{maxRetries}}

**Urgent Action Required:**
Please process your payment immediately to avoid service suspension:

{{paymentUrl}}

**What happens next:**
If payment is not received within {{gracePeriodDays}} days, your account may be temporarily suspended. This means:
- Loss of access to your exhibition and event data
- Inability to create new events or manage existing ones
- Interruption of automated workflows and notifications

**Get help now:**
If you're experiencing payment difficulties, please contact us immediately:
- Email: {{supportEmail}}
- Phone: {{supportPhone}}

We're here to work with you to resolve this matter quickly.

Sincerely,
The {{companyName}} Billing Team
    `,
    variables: ['customerName', 'companyName', 'invoiceId', 'amount', 'dueDate', 'attemptCount', 'maxRetries', 'gracePeriodDays', 'paymentUrl', 'supportEmail', 'supportPhone'],
    isActive: true
  },

  // Final Notice
  {
    name: 'Final Notice - Email',
    type: 'email',
    stage: 'final_notice',
    subject: 'FINAL NOTICE: Account Suspension Pending - {{companyName}}',
    content: `
Dear {{customerName}},

This is your FINAL NOTICE regarding the outstanding payment on your {{companyName}} account.

**Critical Payment Details:**
- Invoice: {{invoiceId}}
- Amount: {{amount}}
- Original Due Date: {{dueDate}}
- Payment Attempts: {{attemptCount}} of {{maxRetries}}

**IMMEDIATE ACTION REQUIRED:**
Your account will be suspended within 24-48 hours if payment is not received:

{{paymentUrl}}

**Account Suspension Consequences:**
- Complete loss of access to your {{companyName}} dashboard
- All exhibition and event data will become inaccessible
- Ongoing events and workflows will be interrupted
- Customer communications will cease
- Data export capabilities will be disabled

**Last Chance to Avoid Suspension:**
- Process payment immediately using the link above
- Contact our billing team for payment arrangements
- Update your payment method if there are card issues

**Emergency Contact:**
This is urgent. Please contact us immediately:
- Email: {{supportEmail}}
- Phone: {{supportPhone}}

We sincerely hope to resolve this matter and continue serving your exhibition management needs.

The {{companyName}} Billing Team
    `,
    variables: ['customerName', 'companyName', 'invoiceId', 'amount', 'dueDate', 'attemptCount', 'maxRetries', 'paymentUrl', 'supportEmail', 'supportPhone'],
    isActive: true
  },

  // Suspension Notice
  {
    name: 'Account Suspended - Email',
    type: 'email',
    stage: 'suspension_notice',
    subject: 'Account Suspended - {{companyName}}',
    content: `
Dear {{customerName}},

Your {{companyName}} account has been suspended due to non-payment.

**Suspension Details:**
- Invoice: {{invoiceId}}
- Outstanding Amount: {{amount}}
- Suspension Date: Today
- Total Payment Attempts: {{attemptCount}}

**What This Means:**
- Your account access has been temporarily disabled
- All exhibition and event management features are unavailable
- Automated workflows and notifications have been paused
- Your data remains secure but inaccessible

**How to Reactivate:**
Your account can be reactivated immediately upon payment:

{{paymentUrl}}

**Data Security:**
Your exhibition data, events, and configurations are safely stored and will be fully restored upon reactivation.

**Reactivation Process:**
1. Process the outstanding payment
2. Your account will be automatically reactivated within 1 hour
3. All features and data will be immediately available
4. Automated workflows will resume

**Need Assistance:**
Our team is standing by to help you reactivate your account:
- Email: {{supportEmail}}
- Phone: {{supportPhone}}

We look forward to welcoming you back to {{companyName}}.

The {{companyName}} Team
    `,
    variables: ['customerName', 'companyName', 'invoiceId', 'amount', 'attemptCount', 'paymentUrl', 'supportEmail', 'supportPhone'],
    isActive: true
  },

  // Reactivation Welcome
  {
    name: 'Account Reactivated - Email',
    type: 'email',
    stage: 'reactivation',
    subject: 'Welcome Back! Account Reactivated - {{companyName}}',
    content: `
Dear {{customerName}},

Great news! Your {{companyName}} account has been successfully reactivated.

**Reactivation Confirmation:**
- Payment Received: {{amount}}
- Invoice: {{invoiceId}}
- Reactivation Date: Today
- Account Status: Active

**What's Restored:**
✅ Full access to your dashboard and all features
✅ All exhibition and event data
✅ Automated workflows and notifications
✅ Customer communications
✅ Reporting and analytics
✅ Team collaboration tools

**Getting Back on Track:**
Your account is now fully operational. All your exhibitions, events, and configurations are exactly as you left them.

**Prevent Future Interruptions:**
To avoid future payment issues:
- Ensure your payment method is up to date
- Set up automatic payment notifications
- Contact us if you anticipate any payment difficulties

**We're Here to Help:**
If you need any assistance getting back up to speed:
- Email: {{supportEmail}}
- Phone: {{supportPhone}}

Thank you for resolving this matter promptly. We're excited to continue supporting your exhibition management success!

Welcome back,
The {{companyName}} Team
    `,
    variables: ['customerName', 'companyName', 'amount', 'invoiceId', 'supportEmail', 'supportPhone'],
    isActive: true
  }
];

export function getTemplateByStage(stage: DunningTemplate['stage'], type: DunningTemplate['type'] = 'email'): Omit<DunningTemplate, 'id' | 'metadata'> | undefined {
  return defaultDunningTemplates.find(template => template.stage === stage && template.type === type);
}

export function getTemplatesByType(type: DunningTemplate['type']): Omit<DunningTemplate, 'id' | 'metadata'>[] {
  return defaultDunningTemplates.filter(template => template.type === type);
}
