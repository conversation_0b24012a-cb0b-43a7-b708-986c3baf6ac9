/**
 * EVEXA Firebase Collection Validation Utilities
 * Prevents empty collection path errors and provides safe Firebase operations
 */

import { collection, doc, query, where, CollectionReference, DocumentReference, Query } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';

// Type for collection validation result
export interface CollectionValidationResult {
  isValid: boolean;
  collectionName: string;
  error?: string;
}

// Validate collection name
export function validateCollectionName(collectionName: string | undefined | null): CollectionValidationResult {
  if (!collectionName) {
    return {
      isValid: false,
      collectionName: '',
      error: 'Collection name is null or undefined'
    };
  }

  if (typeof collectionName !== 'string') {
    return {
      isValid: false,
      collectionName: String(collectionName),
      error: 'Collection name must be a string'
    };
  }

  if (collectionName.trim() === '') {
    return {
      isValid: false,
      collectionName: collectionName,
      error: 'Collection name cannot be empty'
    };
  }

  if (collectionName.includes('/')) {
    return {
      isValid: false,
      collectionName: collectionName,
      error: 'Collection name cannot contain forward slashes'
    };
  }

  return {
    isValid: true,
    collectionName: collectionName.trim()
  };
}

// Safe collection reference
export function safeCollection(collectionName: string | undefined | null): CollectionReference | null {
  const validation = validateCollectionName(collectionName);
  
  if (!validation.isValid) {
    console.error(`Firebase Collection Error: ${validation.error}`, {
      providedName: collectionName,
      validatedName: validation.collectionName
    });
    return null;
  }

  try {
    return collection(db, validation.collectionName);
  } catch (error) {
    console.error('Error creating collection reference:', error, {
      collectionName: validation.collectionName
    });
    return null;
  }
}

// Safe document reference
export function safeDoc(collectionName: string | undefined | null, docId: string): DocumentReference | null {
  const validation = validateCollectionName(collectionName);
  
  if (!validation.isValid) {
    console.error(`Firebase Document Error: ${validation.error}`, {
      providedName: collectionName,
      docId
    });
    return null;
  }

  if (!docId || docId.trim() === '') {
    console.error('Document ID cannot be empty', {
      collectionName: validation.collectionName,
      docId
    });
    return null;
  }

  try {
    return doc(db, validation.collectionName, docId.trim());
  } catch (error) {
    console.error('Error creating document reference:', error, {
      collectionName: validation.collectionName,
      docId
    });
    return null;
  }
}

// Safe query with tenant filtering
export function safeTenantQuery(
  collectionName: string | undefined | null,
  tenantId: string | undefined | null
): Query | null {
  const collectionRef = safeCollection(collectionName);
  
  if (!collectionRef) {
    return null;
  }

  if (!tenantId || tenantId.trim() === '') {
    console.error('Tenant ID is required for tenant queries', {
      collectionName,
      tenantId
    });
    return null;
  }

  try {
    return query(collectionRef, where('tenantId', '==', tenantId.trim()));
  } catch (error) {
    console.error('Error creating tenant query:', error, {
      collectionName,
      tenantId
    });
    return null;
  }
}

// Get collection name from COLLECTIONS constant with fallback
export function getCollectionName(
  collectionKey: keyof typeof COLLECTIONS,
  fallback?: string
): string | null {
  const collectionName = COLLECTIONS[collectionKey];
  
  if (collectionName && typeof collectionName === 'string' && collectionName.trim() !== '') {
    return collectionName.trim();
  }

  if (fallback && typeof fallback === 'string' && fallback.trim() !== '') {
    console.warn(`Using fallback collection name for ${collectionKey}:`, fallback);
    return fallback.trim();
  }

  console.error(`No valid collection name found for ${collectionKey}`, {
    collectionName,
    fallback
  });
  
  return null;
}

// Validate multiple collection names
export function validateCollectionNames(
  collections: Record<string, string | undefined | null>
): Record<string, CollectionValidationResult> {
  const results: Record<string, CollectionValidationResult> = {};
  
  for (const [key, collectionName] of Object.entries(collections)) {
    results[key] = validateCollectionName(collectionName);
  }
  
  return results;
}

// Safe Firebase operations wrapper
export class SafeFirebaseOperations {
  // Safe collection getter
  static getCollection(collectionName: string | undefined | null): CollectionReference | null {
    return safeCollection(collectionName);
  }

  // Safe document getter
  static getDocument(collectionName: string | undefined | null, docId: string): DocumentReference | null {
    return safeDoc(collectionName, docId);
  }

  // Safe tenant query
  static getTenantQuery(
    collectionName: string | undefined | null,
    tenantId: string | undefined | null
  ): Query | null {
    return safeTenantQuery(collectionName, tenantId);
  }

  // Validate collection before operation
  static validateBeforeOperation(
    collectionName: string | undefined | null,
    operation: string
  ): boolean {
    const validation = validateCollectionName(collectionName);
    
    if (!validation.isValid) {
      console.error(`Cannot perform ${operation}: ${validation.error}`, {
        providedName: collectionName,
        operation
      });
      return false;
    }
    
    return true;
  }
}

// Dashboard-specific collection helpers
export const DashboardCollections = {
  // Get exhibitions collection safely
  getExhibitions: () => getCollectionName('EXHIBITIONS', 'exhibitions'),
  
  // Get events collection safely
  getEvents: () => getCollectionName('EVENTS', 'events'),
  
  // Get leads collection safely
  getLeads: () => getCollectionName('LEADS', 'leads'),
  
  // Get tasks collection safely
  getTasks: () => getCollectionName('TASKS', 'tasks'),
  
  // Get users collection safely
  getUsers: () => getCollectionName('USERS', 'users'),
  
  // Get marketing campaigns collection safely
  getMarketingCampaigns: () => getCollectionName('MARKETING_CAMPAIGNS', 'marketing_campaigns'),
  
  // Get marketing contacts collection safely
  getMarketingContacts: () => getCollectionName('MARKETING_CONTACTS', 'marketing_contacts'),
  
  // Get marketing metrics collection safely
  getMarketingMetrics: () => getCollectionName('MARKETING_METRICS', 'marketing_metrics'),
  
  // Validate all dashboard collections
  validateAll: () => {
    const collections = {
      exhibitions: DashboardCollections.getExhibitions(),
      events: DashboardCollections.getEvents(),
      leads: DashboardCollections.getLeads(),
      tasks: DashboardCollections.getTasks(),
      users: DashboardCollections.getUsers(),
      marketingCampaigns: DashboardCollections.getMarketingCampaigns(),
      marketingContacts: DashboardCollections.getMarketingContacts(),
      marketingMetrics: DashboardCollections.getMarketingMetrics(),
    };
    
    return validateCollectionNames(collections);
  }
};

// Error reporting for collection issues
export function reportCollectionError(
  error: any,
  context: {
    component?: string;
    operation?: string;
    collectionName?: string;
    tenantId?: string;
  }
) {
  const errorInfo = {
    message: error?.message || 'Unknown error',
    code: error?.code || 'unknown',
    context,
    timestamp: new Date().toISOString(),
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server'
  };
  
  console.error('Firebase Collection Error:', errorInfo);
  
  // In production, you might want to send this to an error reporting service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to error reporting service
    // errorReportingService.report(errorInfo);
  }
}

export default SafeFirebaseOperations;
