/**
 * Default Invoice Templates for EVEXA
 * Pre-configured templates for different business needs
 */

import { InvoiceTemplate } from '@/services/invoiceManagementService';

export const defaultInvoiceTemplates: Omit<InvoiceTemplate, 'id' | 'tenantId' | 'metadata'>[] = [
  {
    name: 'Professional Standard',
    description: 'Clean, professional template suitable for most business invoices',
    isDefault: true,
    branding: {
      companyName: 'EVEXA',
      companyAddress: '123 Business Street, Suite 100\nBusiness City, BC 12345',
      companyEmail: '<EMAIL>',
      companyPhone: '+****************',
      website: 'https://evexa.com',
      primaryColor: '#2563eb',
      secondaryColor: '#64748b'
    },
    layout: {
      headerStyle: 'standard',
      footerStyle: 'standard',
      itemsTableStyle: 'detailed',
      showTaxBreakdown: true,
      showPaymentTerms: true,
      showNotes: true
    },
    customFields: [
      {
        id: 'project_reference',
        name: 'Project Reference',
        type: 'text',
        required: false,
        defaultValue: ''
      },
      {
        id: 'purchase_order',
        name: 'Purchase Order #',
        type: 'text',
        required: false,
        defaultValue: ''
      }
    ],
    paymentTerms: {
      dueDays: 30,
      lateFeesEnabled: true,
      lateFeePercentage: 1.5,
      discountTerms: {
        days: 10,
        percentage: 2
      }
    }
  },
  {
    name: 'Minimal Clean',
    description: 'Simple, minimal template for straightforward invoicing',
    isDefault: false,
    branding: {
      companyName: 'EVEXA',
      companyAddress: '123 Business Street\nBusiness City, BC 12345',
      companyEmail: '<EMAIL>',
      primaryColor: '#059669',
      secondaryColor: '#6b7280'
    },
    layout: {
      headerStyle: 'minimal',
      footerStyle: 'minimal',
      itemsTableStyle: 'simple',
      showTaxBreakdown: false,
      showPaymentTerms: true,
      showNotes: false
    },
    customFields: [],
    paymentTerms: {
      dueDays: 15,
      lateFeesEnabled: false
    }
  },
  {
    name: 'Detailed Enterprise',
    description: 'Comprehensive template with detailed breakdown for enterprise clients',
    isDefault: false,
    branding: {
      companyName: 'EVEXA',
      companyAddress: '123 Business Street, Suite 100\nBusiness City, BC 12345\nCountry',
      companyEmail: '<EMAIL>',
      companyPhone: '+****************',
      website: 'https://evexa.com',
      primaryColor: '#7c3aed',
      secondaryColor: '#475569'
    },
    layout: {
      headerStyle: 'detailed',
      footerStyle: 'detailed',
      itemsTableStyle: 'modern',
      showTaxBreakdown: true,
      showPaymentTerms: true,
      showNotes: true
    },
    customFields: [
      {
        id: 'contract_number',
        name: 'Contract Number',
        type: 'text',
        required: true,
        defaultValue: ''
      },
      {
        id: 'department',
        name: 'Department',
        type: 'text',
        required: false,
        defaultValue: ''
      },
      {
        id: 'cost_center',
        name: 'Cost Center',
        type: 'text',
        required: false,
        defaultValue: ''
      },
      {
        id: 'approval_date',
        name: 'Approval Date',
        type: 'date',
        required: false
      }
    ],
    paymentTerms: {
      dueDays: 45,
      lateFeesEnabled: true,
      lateFeePercentage: 2,
      lateFeeFlat: 50,
      discountTerms: {
        days: 15,
        percentage: 3
      }
    }
  },
  {
    name: 'Subscription Billing',
    description: 'Optimized template for recurring subscription invoices',
    isDefault: false,
    branding: {
      companyName: 'EVEXA',
      companyAddress: '123 Business Street\nBusiness City, BC 12345',
      companyEmail: '<EMAIL>',
      companyPhone: '+****************',
      website: 'https://evexa.com',
      primaryColor: '#dc2626',
      secondaryColor: '#64748b'
    },
    layout: {
      headerStyle: 'standard',
      footerStyle: 'standard',
      itemsTableStyle: 'detailed',
      showTaxBreakdown: true,
      showPaymentTerms: false,
      showNotes: true
    },
    customFields: [
      {
        id: 'subscription_period',
        name: 'Subscription Period',
        type: 'text',
        required: true,
        defaultValue: ''
      },
      {
        id: 'next_billing_date',
        name: 'Next Billing Date',
        type: 'date',
        required: false
      },
      {
        id: 'plan_name',
        name: 'Plan Name',
        type: 'text',
        required: true,
        defaultValue: ''
      }
    ],
    paymentTerms: {
      dueDays: 7,
      lateFeesEnabled: true,
      lateFeePercentage: 5
    }
  },
  {
    name: 'Event Services',
    description: 'Specialized template for exhibition and event service invoices',
    isDefault: false,
    branding: {
      companyName: 'EVEXA',
      companyAddress: '123 Business Street, Suite 100\nBusiness City, BC 12345',
      companyEmail: '<EMAIL>',
      companyPhone: '+****************',
      website: 'https://evexa.com',
      primaryColor: '#ea580c',
      secondaryColor: '#64748b'
    },
    layout: {
      headerStyle: 'detailed',
      footerStyle: 'standard',
      itemsTableStyle: 'modern',
      showTaxBreakdown: true,
      showPaymentTerms: true,
      showNotes: true
    },
    customFields: [
      {
        id: 'event_name',
        name: 'Event Name',
        type: 'text',
        required: true,
        defaultValue: ''
      },
      {
        id: 'event_date',
        name: 'Event Date',
        type: 'date',
        required: true
      },
      {
        id: 'venue',
        name: 'Venue',
        type: 'text',
        required: false,
        defaultValue: ''
      },
      {
        id: 'booth_number',
        name: 'Booth Number',
        type: 'text',
        required: false,
        defaultValue: ''
      },
      {
        id: 'event_manager',
        name: 'Event Manager',
        type: 'text',
        required: false,
        defaultValue: ''
      }
    ],
    paymentTerms: {
      dueDays: 30,
      lateFeesEnabled: true,
      lateFeePercentage: 2,
      discountTerms: {
        days: 14,
        percentage: 2.5
      }
    }
  }
];

export function getDefaultTemplate(): Omit<InvoiceTemplate, 'id' | 'tenantId' | 'metadata'> {
  return defaultInvoiceTemplates.find(template => template.isDefault) || defaultInvoiceTemplates[0];
}

export function getTemplateByName(name: string): Omit<InvoiceTemplate, 'id' | 'tenantId' | 'metadata'> | undefined {
  return defaultInvoiceTemplates.find(template => template.name === name);
}
