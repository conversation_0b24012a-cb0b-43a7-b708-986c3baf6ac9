/**
 * Utility to fix tenant IDs for specific documents
 */

import { db } from '@/lib/firebase';
import { collection, getDocs, updateDoc, doc } from 'firebase/firestore';

const CORRECT_TENANT_ID = 'evexa-development-company';

export async function fixUserTenantIds(): Promise<{
  success: boolean;
  fixed: number;
  errors: string[];
}> {
  if (process.env.NODE_ENV === 'production') {
    throw new Error('Tenant ID fixing is only allowed in development');
  }

  console.log('🔧 Fixing user tenant IDs...');
  
  const errors: string[] = [];
  let fixed = 0;

  try {
    // Get all users
    const querySnapshot = await getDocs(collection(db, 'users'));
    
    for (const docSnapshot of querySnapshot.docs) {
      const data = docSnapshot.data();
      
      // Check if tenant ID is missing or incorrect
      if (!data.tenantId || data.tenantId !== CORRECT_TENANT_ID) {
        try {
          await updateDoc(doc(db, 'users', docSnapshot.id), {
            tenantId: CORRECT_TENANT_ID,
            updatedAt: new Date()
          });
          
          console.log(`✅ Fixed tenant ID for user: ${docSnapshot.id}`);
          fixed++;
        } catch (error) {
          const errorMsg = `Failed to fix user ${docSnapshot.id}: ${error}`;
          console.error(`❌ ${errorMsg}`);
          errors.push(errorMsg);
        }
      }
    }

    console.log(`🔧 Tenant ID fix complete: ${fixed} users fixed`);
    
    return {
      success: errors.length === 0,
      fixed,
      errors
    };

  } catch (error) {
    console.error('Tenant ID fix failed:', error);
    return {
      success: false,
      fixed,
      errors: [`Tenant ID fix failed: ${error}`]
    };
  }
}
