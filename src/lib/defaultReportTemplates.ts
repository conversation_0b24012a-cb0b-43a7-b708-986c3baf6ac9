/**
 * Default Report Templates for EVEXA
 * Pre-configured industry-specific report templates for exhibitions, events, and business analytics
 */

import { ReportTemplate, ReportField, CustomFormula } from '@/services/customReportingService';

export const defaultReportFields: Omit<ReportField, 'id'>[] = [
  // Exhibition Fields
  {
    name: 'exhibition_name',
    displayName: 'Exhibition Name',
    type: 'string',
    source: 'exhibitions',
    path: 'name',
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'exhibition_start_date',
    displayName: 'Start Date',
    type: 'date',
    source: 'exhibitions',
    path: 'startDate',
    format: { dateFormat: 'YYYY-MM-DD' },
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'exhibition_end_date',
    displayName: 'End Date',
    type: 'date',
    source: 'exhibitions',
    path: 'endDate',
    format: { dateFormat: 'YYYY-MM-DD' },
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'exhibition_venue',
    displayName: 'Venue',
    type: 'string',
    source: 'exhibitions',
    path: 'venue.name',
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'exhibition_budget',
    displayName: 'Budget',
    type: 'currency',
    source: 'exhibitions',
    path: 'budget.total',
    format: { currency: 'USD', decimals: 2 },
    aggregation: 'sum',
    isFilterable: true,
    isSortable: true,
    isGroupable: false
  },
  {
    name: 'exhibition_actual_cost',
    displayName: 'Actual Cost',
    type: 'currency',
    source: 'exhibitions',
    path: 'budget.actualCost',
    format: { currency: 'USD', decimals: 2 },
    aggregation: 'sum',
    isFilterable: true,
    isSortable: true,
    isGroupable: false
  },
  {
    name: 'exhibition_status',
    displayName: 'Status',
    type: 'string',
    source: 'exhibitions',
    path: 'status',
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },

  // Event Fields
  {
    name: 'event_name',
    displayName: 'Event Name',
    type: 'string',
    source: 'events',
    path: 'name',
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'event_type',
    displayName: 'Event Type',
    type: 'string',
    source: 'events',
    path: 'type',
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'event_date',
    displayName: 'Event Date',
    type: 'date',
    source: 'events',
    path: 'date',
    format: { dateFormat: 'YYYY-MM-DD HH:mm' },
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'event_attendees',
    displayName: 'Attendees',
    type: 'number',
    source: 'events',
    path: 'attendees.count',
    aggregation: 'sum',
    isFilterable: true,
    isSortable: true,
    isGroupable: false
  },
  {
    name: 'event_cost',
    displayName: 'Event Cost',
    type: 'currency',
    source: 'events',
    path: 'cost',
    format: { currency: 'USD', decimals: 2 },
    aggregation: 'sum',
    isFilterable: true,
    isSortable: true,
    isGroupable: false
  },

  // Lead Fields
  {
    name: 'lead_name',
    displayName: 'Lead Name',
    type: 'string',
    source: 'leads',
    path: 'name',
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'lead_company',
    displayName: 'Company',
    type: 'string',
    source: 'leads',
    path: 'company',
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'lead_source',
    displayName: 'Lead Source',
    type: 'string',
    source: 'leads',
    path: 'source',
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'lead_status',
    displayName: 'Lead Status',
    type: 'string',
    source: 'leads',
    path: 'status',
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'lead_value',
    displayName: 'Lead Value',
    type: 'currency',
    source: 'leads',
    path: 'estimatedValue',
    format: { currency: 'USD', decimals: 2 },
    aggregation: 'sum',
    isFilterable: true,
    isSortable: true,
    isGroupable: false
  },
  {
    name: 'lead_created_date',
    displayName: 'Created Date',
    type: 'date',
    source: 'leads',
    path: 'createdAt',
    format: { dateFormat: 'YYYY-MM-DD' },
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },

  // Task Fields
  {
    name: 'task_title',
    displayName: 'Task Title',
    type: 'string',
    source: 'tasks',
    path: 'title',
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'task_status',
    displayName: 'Task Status',
    type: 'string',
    source: 'tasks',
    path: 'status',
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'task_priority',
    displayName: 'Priority',
    type: 'string',
    source: 'tasks',
    path: 'priority',
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'task_assignee',
    displayName: 'Assignee',
    type: 'string',
    source: 'tasks',
    path: 'assignee.name',
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'task_due_date',
    displayName: 'Due Date',
    type: 'date',
    source: 'tasks',
    path: 'dueDate',
    format: { dateFormat: 'YYYY-MM-DD' },
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },

  // Vendor Fields
  {
    name: 'vendor_name',
    displayName: 'Vendor Name',
    type: 'string',
    source: 'vendors',
    path: 'name',
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'vendor_category',
    displayName: 'Category',
    type: 'string',
    source: 'vendors',
    path: 'category',
    isFilterable: true,
    isSortable: true,
    isGroupable: true
  },
  {
    name: 'vendor_rating',
    displayName: 'Rating',
    type: 'number',
    source: 'vendors',
    path: 'rating',
    format: { decimals: 1, suffix: '/5' },
    aggregation: 'avg',
    isFilterable: true,
    isSortable: true,
    isGroupable: false
  },
  {
    name: 'vendor_total_spent',
    displayName: 'Total Spent',
    type: 'currency',
    source: 'vendors',
    path: 'totalSpent',
    format: { currency: 'USD', decimals: 2 },
    aggregation: 'sum',
    isFilterable: true,
    isSortable: true,
    isGroupable: false
  }
];

export const defaultCustomFormulas: Omit<CustomFormula, 'id'>[] = [
  {
    name: 'Budget Variance',
    expression: 'budget - actual_cost',
    returnType: 'number',
    description: 'Difference between budgeted and actual costs',
    variables: [
      { name: 'budget', fieldId: 'exhibition_budget', type: 'number' },
      { name: 'actual_cost', fieldId: 'exhibition_actual_cost', type: 'number' }
    ]
  },
  {
    name: 'Budget Variance Percentage',
    expression: '((budget - actual_cost) / budget) * 100',
    returnType: 'number',
    description: 'Budget variance as a percentage',
    variables: [
      { name: 'budget', fieldId: 'exhibition_budget', type: 'number' },
      { name: 'actual_cost', fieldId: 'exhibition_actual_cost', type: 'number' }
    ]
  },
  {
    name: 'Cost Per Attendee',
    expression: 'event_cost / attendees',
    returnType: 'number',
    description: 'Cost per event attendee',
    variables: [
      { name: 'event_cost', fieldId: 'event_cost', type: 'number' },
      { name: 'attendees', fieldId: 'event_attendees', type: 'number' }
    ]
  },
  {
    name: 'Lead Conversion Rate',
    expression: '(converted_leads / total_leads) * 100',
    returnType: 'number',
    description: 'Percentage of leads that converted',
    variables: [
      { name: 'converted_leads', fieldId: 'lead_status', type: 'number' },
      { name: 'total_leads', fieldId: 'lead_name', type: 'number' }
    ]
  },
  {
    name: 'ROI Percentage',
    expression: '((revenue - cost) / cost) * 100',
    returnType: 'number',
    description: 'Return on Investment percentage',
    variables: [
      { name: 'revenue', fieldId: 'lead_value', type: 'number' },
      { name: 'cost', fieldId: 'exhibition_actual_cost', type: 'number' }
    ]
  }
];

export const defaultReportTemplates: Omit<ReportTemplate, 'id' | 'metadata'>[] = [
  {
    name: 'Exhibition Performance Dashboard',
    description: 'Comprehensive overview of exhibition performance metrics including budget, attendance, and ROI',
    category: 'exhibition',
    industry: 'general',
    thumbnail: '/templates/exhibition-performance.png',
    isBuiltIn: true,
    popularity: 95,
    rating: 4.8,
    downloadCount: 1250,
    template: {
      name: 'Exhibition Performance Dashboard',
      description: 'Track key exhibition metrics and performance indicators',
      category: 'Exhibition Analytics',
      tags: ['exhibition', 'performance', 'budget', 'roi'],
      dataSources: [
        {
          id: 'exhibitions',
          name: 'Exhibitions',
          collection: 'exhibitions'
        },
        {
          id: 'events',
          name: 'Events',
          collection: 'events',
          joins: [
            {
              targetCollection: 'exhibitions',
              localField: 'exhibitionId',
              foreignField: 'id',
              type: 'inner'
            }
          ]
        }
      ],
      fields: [
        'exhibition_name',
        'exhibition_start_date',
        'exhibition_venue',
        'exhibition_budget',
        'exhibition_actual_cost',
        'exhibition_status',
        'event_attendees'
      ],
      filters: [
        {
          id: 'date_filter',
          fieldId: 'exhibition_start_date',
          operator: 'greater_than',
          value: '{{start_date}}',
          logicalOperator: 'AND'
        },
        {
          id: 'status_filter',
          fieldId: 'exhibition_status',
          operator: 'not_equals',
          value: 'cancelled',
          logicalOperator: 'AND'
        }
      ],
      sorting: [
        {
          fieldId: 'exhibition_start_date',
          direction: 'desc',
          priority: 1
        }
      ],
      grouping: [
        {
          fieldId: 'exhibition_status',
          level: 1,
          showSubtotals: true,
          collapseByDefault: false
        }
      ],
      formulas: [
        {
          id: 'budget_variance',
          name: 'Budget Variance',
          expression: 'exhibition_budget - exhibition_actual_cost',
          returnType: 'number',
          description: 'Budget vs actual cost variance',
          variables: [
            { name: 'exhibition_budget', fieldId: 'exhibition_budget', type: 'number' },
            { name: 'exhibition_actual_cost', fieldId: 'exhibition_actual_cost', type: 'number' }
          ]
        }
      ],
      visualization: {
        type: 'chart',
        config: {
          chartType: 'bar',
          xAxis: 'exhibition_name',
          yAxis: ['exhibition_budget', 'exhibition_actual_cost'],
          colorBy: 'exhibition_status'
        }
      },
      caching: {
        enabled: true,
        ttl: 60
      }
    }
  },
  {
    name: 'Lead Generation Report',
    description: 'Track lead generation performance across exhibitions and events',
    category: 'marketing',
    industry: 'general',
    thumbnail: '/templates/lead-generation.png',
    isBuiltIn: true,
    popularity: 88,
    rating: 4.6,
    downloadCount: 980,
    template: {
      name: 'Lead Generation Report',
      description: 'Analyze lead generation effectiveness and conversion rates',
      category: 'Marketing Analytics',
      tags: ['leads', 'conversion', 'marketing', 'sales'],
      dataSources: [
        {
          id: 'leads',
          name: 'Leads',
          collection: 'leads'
        },
        {
          id: 'exhibitions',
          name: 'Exhibitions',
          collection: 'exhibitions',
          joins: [
            {
              targetCollection: 'leads',
              localField: 'id',
              foreignField: 'exhibitionId',
              type: 'left'
            }
          ]
        }
      ],
      fields: [
        'lead_name',
        'lead_company',
        'lead_source',
        'lead_status',
        'lead_value',
        'lead_created_date',
        'exhibition_name'
      ],
      filters: [
        {
          id: 'date_range',
          fieldId: 'lead_created_date',
          operator: 'between',
          value: '{{start_date}}',
          secondValue: '{{end_date}}',
          logicalOperator: 'AND'
        }
      ],
      sorting: [
        {
          fieldId: 'lead_created_date',
          direction: 'desc',
          priority: 1
        },
        {
          fieldId: 'lead_value',
          direction: 'desc',
          priority: 2
        }
      ],
      grouping: [
        {
          fieldId: 'lead_source',
          level: 1,
          showSubtotals: true,
          collapseByDefault: false
        },
        {
          fieldId: 'lead_status',
          level: 2,
          showSubtotals: true,
          collapseByDefault: false
        }
      ],
      formulas: [
        {
          id: 'conversion_rate',
          name: 'Conversion Rate',
          expression: '(qualified_leads / total_leads) * 100',
          returnType: 'number',
          description: 'Lead qualification conversion rate',
          variables: [
            { name: 'qualified_leads', fieldId: 'lead_status', type: 'number' },
            { name: 'total_leads', fieldId: 'lead_name', type: 'number' }
          ]
        }
      ],
      visualization: {
        type: 'pivot',
        config: {
          rows: ['lead_source'],
          columns: ['lead_status'],
          values: ['lead_value']
        }
      },
      caching: {
        enabled: true,
        ttl: 30
      }
    }
  }
];

export function getTemplateByCategory(category: string): Omit<ReportTemplate, 'id' | 'metadata'>[] {
  return defaultReportTemplates.filter(template => template.category === category);
}

export function getFieldsBySource(source: string): Omit<ReportField, 'id'>[] {
  return defaultReportFields.filter(field => field.source === source);
}

export function getFormulasByCategory(category: string): Omit<CustomFormula, 'id'>[] {
  // Return formulas relevant to the category
  const categoryFieldMap: Record<string, string[]> = {
    exhibition: ['exhibition_budget', 'exhibition_actual_cost', 'event_attendees'],
    marketing: ['lead_value', 'lead_status', 'lead_name'],
    financial: ['exhibition_budget', 'exhibition_actual_cost', 'vendor_total_spent']
  };

  const relevantFields = categoryFieldMap[category] || [];
  
  return defaultCustomFormulas.filter(formula =>
    formula.variables.some(variable => relevantFields.includes(variable.fieldId))
  );
}
