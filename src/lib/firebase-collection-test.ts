/**
 * EVEXA Firebase Collection Error Prevention Test
 * Comprehensive test to prevent empty collection path errors
 */

import { COLLECTIONS } from '@/lib/collections';
import { validateCollectionName, SafeFirebaseOperations } from '@/lib/firebase-validation';

// Test all collection names for validity
export function testAllCollectionNames(): { passed: boolean; errors: string[] } {
  const errors: string[] = [];
  let passed = true;

  console.log('🧪 Testing all COLLECTIONS constants for validity...');

  // Test each collection in COLLECTIONS
  Object.entries(COLLECTIONS).forEach(([key, value]) => {
    const validation = validateCollectionName(value);
    
    if (!validation.isValid) {
      errors.push(`❌ ${key}: ${validation.error} (value: "${value}")`);
      passed = false;
    } else {
      console.log(`✅ ${key}: "${validation.collectionName}"`);
    }
  });

  // Test safe collection operations
  console.log('\n🧪 Testing SafeFirebaseOperations...');
  
  const testCollections = [
    'exhibitions',
    'exhibition_events', 
    'lead_contacts',
    'tasks',
    'users',
    '', // Should fail
    null, // Should fail
    undefined // Should fail
  ];

  testCollections.forEach((collectionName, index) => {
    try {
      const collectionRef = SafeFirebaseOperations.getCollection(collectionName);
      
      if (collectionName === '' || collectionName === null || collectionName === undefined) {
        if (collectionRef === null) {
          console.log(`✅ Test ${index + 1}: Correctly rejected invalid collection name: ${collectionName}`);
        } else {
          errors.push(`❌ Test ${index + 1}: Should have rejected invalid collection name: ${collectionName}`);
          passed = false;
        }
      } else {
        if (collectionRef !== null) {
          console.log(`✅ Test ${index + 1}: Successfully created collection reference for: ${collectionName}`);
        } else {
          errors.push(`❌ Test ${index + 1}: Failed to create collection reference for valid name: ${collectionName}`);
          passed = false;
        }
      }
    } catch (error) {
      errors.push(`❌ Test ${index + 1}: Exception thrown for collection "${collectionName}": ${error}`);
      passed = false;
    }
  });

  return { passed, errors };
}

// Test specific dashboard collections
export function testDashboardCollections(): { passed: boolean; errors: string[] } {
  const errors: string[] = [];
  let passed = true;

  console.log('\n🧪 Testing dashboard-specific collections...');

  const dashboardCollections = {
    exhibitions: COLLECTIONS.EXHIBITIONS,
    exhibitionEvents: COLLECTIONS.EXHIBITION_EVENTS,
    leadContacts: COLLECTIONS.LEAD_CONTACTS,
    tasks: COLLECTIONS.TASKS,
    users: COLLECTIONS.USERS,
    budgets: COLLECTIONS.BUDGETS,
    expenses: COLLECTIONS.EXPENSES,
    analyticsData: COLLECTIONS.ANALYTICS_DATA,
    performanceMetrics: COLLECTIONS.PERFORMANCE_METRICS,
  };

  Object.entries(dashboardCollections).forEach(([key, collectionName]) => {
    if (!collectionName) {
      errors.push(`❌ Dashboard collection ${key} is undefined or empty`);
      passed = false;
      return;
    }

    const validation = validateCollectionName(collectionName);
    if (!validation.isValid) {
      errors.push(`❌ Dashboard collection ${key}: ${validation.error}`);
      passed = false;
    } else {
      console.log(`✅ Dashboard collection ${key}: "${validation.collectionName}"`);
    }
  });

  return { passed, errors };
}

// Test performance analytics collections specifically
export function testPerformanceAnalyticsCollections(): { passed: boolean; errors: string[] } {
  const errors: string[] = [];
  let passed = true;

  console.log('\n🧪 Testing performance analytics collections...');

  // These are the collections used in performance analytics
  const performanceCollections = {
    exhibitions: COLLECTIONS.EXHIBITIONS,
    exhibitionEvents: COLLECTIONS.EXHIBITION_EVENTS, // Fixed from COLLECTIONS.EVENTS
    leadContacts: COLLECTIONS.LEAD_CONTACTS,
    tasks: COLLECTIONS.TASKS,
    budgets: COLLECTIONS.BUDGETS,
    performanceMetrics: COLLECTIONS.PERFORMANCE_METRICS,
    analyticsData: COLLECTIONS.ANALYTICS_DATA,
  };

  Object.entries(performanceCollections).forEach(([key, collectionName]) => {
    if (!collectionName) {
      errors.push(`❌ Performance collection ${key} is undefined or empty`);
      passed = false;
      return;
    }

    const validation = validateCollectionName(collectionName);
    if (!validation.isValid) {
      errors.push(`❌ Performance collection ${key}: ${validation.error}`);
      passed = false;
    } else {
      console.log(`✅ Performance collection ${key}: "${validation.collectionName}"`);
      
      // Test safe collection operation
      const collectionRef = SafeFirebaseOperations.getCollection(collectionName);
      if (!collectionRef) {
        errors.push(`❌ Performance collection ${key}: Failed to create safe collection reference`);
        passed = false;
      }
    }
  });

  return { passed, errors };
}

// Run comprehensive collection tests
export function runComprehensiveCollectionTests(): {
  allPassed: boolean;
  results: {
    collectionNames: { passed: boolean; errors: string[] };
    dashboardCollections: { passed: boolean; errors: string[] };
    performanceCollections: { passed: boolean; errors: string[] };
  };
} {
  console.log('🚀 Running comprehensive Firebase collection tests...\n');

  const collectionNames = testAllCollectionNames();
  const dashboardCollections = testDashboardCollections();
  const performanceCollections = testPerformanceAnalyticsCollections();

  const allPassed = collectionNames.passed && dashboardCollections.passed && performanceCollections.passed;

  console.log('\n📊 Test Results Summary:');
  console.log(`Collection Names: ${collectionNames.passed ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Dashboard Collections: ${dashboardCollections.passed ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Performance Collections: ${performanceCollections.passed ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Overall: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

  if (!allPassed) {
    console.log('\n🚨 Errors found:');
    [...collectionNames.errors, ...dashboardCollections.errors, ...performanceCollections.errors]
      .forEach(error => console.log(error));
  }

  return {
    allPassed,
    results: {
      collectionNames,
      dashboardCollections,
      performanceCollections
    }
  };
}

// Quick validation function for runtime use
export function validateFirebaseCollectionsAtRuntime(): boolean {
  try {
    const results = runComprehensiveCollectionTests();
    return results.allPassed;
  } catch (error) {
    console.error('❌ Runtime collection validation failed:', error);
    return false;
  }
}

// Export for use in development/testing
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Make available in browser console for debugging
  (window as any).testFirebaseCollections = runComprehensiveCollectionTests;
  (window as any).validateCollections = validateFirebaseCollectionsAtRuntime;
}

export default {
  testAllCollectionNames,
  testDashboardCollections,
  testPerformanceAnalyticsCollections,
  runComprehensiveCollectionTests,
  validateFirebaseCollectionsAtRuntime
};
