/**
 * Advanced Theming System for EVEXA
 * Supports dark/light mode, custom brand themes, and dynamic color schemes
 */

import { createContext, useContext, useEffect, useState } from 'react';

// Theme Types
export type ThemeMode = 'light' | 'dark' | 'system';
export type ThemeVariant = 'gold' | 'blue' | 'grey';

export interface ThemeColors {
  primary: string;
  primaryForeground: string;
  secondary: string;
  secondaryForeground: string;
  accent: string;
  accentForeground: string;
  background: string;
  foreground: string;
  muted: string;
  mutedForeground: string;
  card: string;
  cardForeground: string;
  border: string;
  input: string;
  ring: string;
  destructive: string;
  destructiveForeground: string;
  success: string;
  successForeground: string;
  warning: string;
  warningForeground: string;
  info: string;
  infoForeground: string;
  // Sidebar colors
  sidebarBackground: string;
  sidebarForeground: string;
  sidebarPrimary: string;
  sidebarPrimaryForeground: string;
  sidebarAccent: string;
  sidebarAccentForeground: string;
  sidebarBorder: string;
  sidebarRing: string;
}

export interface Theme {
  name: string;
  variant: ThemeVariant;
  colors: {
    light: ThemeColors;
    dark: ThemeColors;
  };
  fonts: {
    sans: string[];
    mono: string[];
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}

// Predefined Themes
export const themes: Record<ThemeVariant, Theme> = {
  gold: {
    name: 'Gold & Yellow',
    variant: 'gold',
    colors: {
      light: {
        primary: '45 93% 47%', // Gold #DAA520
        primaryForeground: '0 0% 0%', // Black text on gold
        secondary: '48 100% 88%', // Light yellow #FFFACD
        secondaryForeground: '45 93% 20%', // Dark gold text
        accent: '51 100% 50%', // Bright yellow #FFFF00
        accentForeground: '0 0% 0%', // Black text on yellow
        background: '60 100% 99%', // Cream white #FFFEF7
        foreground: '45 93% 15%', // Dark gold text
        muted: '48 100% 94%', // Very light yellow
        mutedForeground: '45 50% 35%', // Muted gold
        card: '0 0% 100%', // White cards
        cardForeground: '45 93% 15%', // Dark gold text
        border: '48 100% 85%', // Light yellow border
        input: '48 100% 90%', // Light yellow input
        ring: '45 93% 47%', // Gold ring
        destructive: '0 84.2% 60.2%', // Red
        destructiveForeground: '210 40% 98%', // White
        success: '142.1 76.2% 36.3%', // Green
        successForeground: '355.7 100% 97.3%', // White
        warning: '32.5 94.6% 43.7%', // Orange
        warningForeground: '210 40% 98%', // White
        info: '197 37% 44%', // Teal
        infoForeground: '210 40% 98%', // White
        // Sidebar colors - Light Gold
        sidebarBackground: '48 100% 96%', // Very light yellow
        sidebarForeground: '45 93% 15%', // Dark gold text
        sidebarPrimary: '45 93% 47%', // Gold primary
        sidebarPrimaryForeground: '0 0% 0%', // Black text
        sidebarAccent: '51 100% 50%', // Yellow accent
        sidebarAccentForeground: '0 0% 0%', // Black text
        sidebarBorder: '48 100% 85%', // Light yellow border
        sidebarRing: '45 93% 47%', // Gold ring
      },
      dark: {
        primary: '45 93% 55%', // Brighter gold for dark mode
        primaryForeground: '0 0% 0%', // Black text on gold
        secondary: '45 50% 25%', // Dark gold
        secondaryForeground: '48 100% 88%', // Light yellow text
        accent: '51 100% 60%', // Bright yellow for dark mode
        accentForeground: '0 0% 0%', // Black text on yellow
        background: '45 20% 8%', // Very dark gold background
        foreground: '48 100% 88%', // Light yellow text
        muted: '45 30% 15%', // Dark muted gold
        mutedForeground: '48 50% 65%', // Muted yellow
        card: '45 25% 12%', // Dark gold cards
        cardForeground: '48 100% 88%', // Light yellow text
        border: '45 30% 20%', // Dark gold border
        input: '45 30% 18%', // Dark gold input
        ring: '45 93% 55%', // Bright gold ring
        destructive: '0 62.8% 30.6%', // Dark red
        destructiveForeground: '210 40% 98%', // White
        success: '142.1 70.6% 45.3%', // Green
        successForeground: '144.9 80.4% 10%', // Dark green
        warning: '32.5 94.6% 43.7%', // Orange
        warningForeground: '210 40% 98%', // White
        info: '197 37% 44%', // Teal
        infoForeground: '210 40% 98%', // White
        // Sidebar colors - Dark Gold
        sidebarBackground: '45 25% 10%', // Very dark gold
        sidebarForeground: '48 100% 88%', // Light yellow text
        sidebarPrimary: '45 93% 55%', // Bright gold primary
        sidebarPrimaryForeground: '0 0% 0%', // Black text
        sidebarAccent: '51 100% 60%', // Bright yellow accent
        sidebarAccentForeground: '0 0% 0%', // Black text
        sidebarBorder: '45 30% 20%', // Dark gold border
        sidebarRing: '45 93% 55%', // Bright gold ring
      },
    },
    fonts: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'Consolas', 'monospace'],
    },
    borderRadius: {
      sm: '0.375rem',
      md: '0.5rem',
      lg: '0.75rem',
      xl: '1rem',
    },
    shadows: {
      sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
      md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
      lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
      xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    },
  },
  blue: {
    name: 'Blue Shades',
    variant: 'blue',
    colors: {
      light: {
        primary: '217 89% 61%', // Saturated Blue #4285F4
        primaryForeground: '0 0% 100%', // White text on blue
        secondary: '210 40% 96%', // Light blue-grey
        secondaryForeground: '217 89% 25%', // Dark blue text
        accent: '199 89% 48%', // Ocean blue
        accentForeground: '0 0% 100%', // White text on accent
        background: '210 40% 98%', // Very light blue-white
        foreground: '217 89% 15%', // Dark blue text
        muted: '210 40% 94%', // Light blue muted
        mutedForeground: '217 50% 35%', // Muted blue
        card: '0 0% 100%', // White cards
        cardForeground: '217 89% 15%', // Dark blue text
        border: '210 40% 88%', // Light blue border
        input: '210 40% 92%', // Light blue input
        ring: '217 89% 61%', // Blue ring
        destructive: '0 84.2% 60.2%', // Red
        destructiveForeground: '210 40% 98%', // White
        success: '142.1 76.2% 36.3%', // Green
        successForeground: '355.7 100% 97.3%', // White
        warning: '32.5 94.6% 43.7%', // Orange
        warningForeground: '210 40% 98%', // White
        info: '199 89% 48%', // Ocean blue
        infoForeground: '0 0% 100%', // White
        // Sidebar colors - Light Blue
        sidebarBackground: '210 40% 96%', // Very light blue
        sidebarForeground: '217 89% 15%', // Dark blue text
        sidebarPrimary: '217 89% 61%', // Blue primary
        sidebarPrimaryForeground: '0 0% 100%', // White text
        sidebarAccent: '199 89% 48%', // Ocean blue accent
        sidebarAccentForeground: '0 0% 100%', // White text
        sidebarBorder: '210 40% 88%', // Light blue border
        sidebarRing: '217 89% 61%', // Blue ring
      },
      dark: {
        primary: '217 89% 65%', // Brighter blue for dark mode
        primaryForeground: '0 0% 100%', // White text on blue
        secondary: '217 30% 20%', // Dark blue-grey
        secondaryForeground: '210 40% 88%', // Light blue text
        accent: '199 89% 55%', // Brighter ocean blue
        accentForeground: '0 0% 100%', // White text on accent
        background: '217 25% 8%', // Very dark blue background
        foreground: '210 40% 88%', // Light blue text
        muted: '217 30% 15%', // Dark muted blue
        mutedForeground: '210 30% 65%', // Muted light blue
        card: '217 30% 12%', // Dark blue cards
        cardForeground: '210 40% 88%', // Light blue text
        border: '217 30% 20%', // Dark blue border
        input: '217 30% 18%', // Dark blue input
        ring: '217 89% 65%', // Bright blue ring
        destructive: '0 62.8% 30.6%', // Dark red
        destructiveForeground: '210 40% 98%', // White
        success: '142.1 70.6% 45.3%', // Green
        successForeground: '144.9 80.4% 10%', // Dark green
        warning: '32.5 94.6% 43.7%', // Orange
        warningForeground: '210 40% 98%', // White
        info: '199 89% 55%', // Bright ocean blue
        infoForeground: '0 0% 100%', // White
        // Sidebar colors - Dark Blue
        sidebarBackground: '217 30% 10%', // Very dark blue
        sidebarForeground: '210 40% 88%', // Light blue text
        sidebarPrimary: '217 89% 65%', // Bright blue primary
        sidebarPrimaryForeground: '0 0% 100%', // White text
        sidebarAccent: '199 89% 55%', // Bright ocean blue accent
        sidebarAccentForeground: '0 0% 100%', // White text
        sidebarBorder: '217 30% 20%', // Dark blue border
        sidebarRing: '217 89% 65%', // Bright blue ring
      },
    },
    fonts: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'Consolas', 'monospace'],
    },
    borderRadius: {
      sm: '0.25rem',
      md: '0.375rem',
      lg: '0.5rem',
      xl: '0.75rem',
    },
    shadows: {
      sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
      md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
      lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
      xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    },
  },
  grey: {
    name: 'Grey Shades',
    variant: 'grey',
    colors: {
      light: {
        primary: '0 0% 20%', // Dark grey #333333
        primaryForeground: '0 0% 100%', // White text on dark grey
        secondary: '0 0% 96%', // Light grey
        secondaryForeground: '0 0% 20%', // Dark grey text
        accent: '0 0% 40%', // Medium grey
        accentForeground: '0 0% 100%', // White text on accent
        background: '0 0% 100%', // White background
        foreground: '0 0% 15%', // Very dark grey text
        muted: '0 0% 94%', // Light grey muted
        mutedForeground: '0 0% 45%', // Medium grey
        card: '0 0% 100%', // White cards
        cardForeground: '0 0% 15%', // Very dark grey text
        border: '0 0% 88%', // Light grey border
        input: '0 0% 92%', // Light grey input
        ring: '0 0% 20%', // Dark grey ring
        destructive: '0 84.2% 60.2%', // Red
        destructiveForeground: '210 40% 98%', // White
        success: '142.1 76.2% 36.3%', // Green
        successForeground: '355.7 100% 97.3%', // White
        warning: '32.5 94.6% 43.7%', // Orange
        warningForeground: '210 40% 98%', // White
        info: '0 0% 40%', // Medium grey
        infoForeground: '0 0% 100%', // White
        // Sidebar colors - Light Grey
        sidebarBackground: '0 0% 96%', // Very light grey
        sidebarForeground: '0 0% 15%', // Very dark grey text
        sidebarPrimary: '0 0% 20%', // Dark grey primary
        sidebarPrimaryForeground: '0 0% 100%', // White text
        sidebarAccent: '0 0% 40%', // Medium grey accent
        sidebarAccentForeground: '0 0% 100%', // White text
        sidebarBorder: '0 0% 88%', // Light grey border
        sidebarRing: '0 0% 20%', // Dark grey ring
      },
      dark: {
        primary: '0 0% 80%', // Light grey for dark mode
        primaryForeground: '0 0% 10%', // Very dark text on light grey
        secondary: '0 0% 20%', // Dark grey
        secondaryForeground: '0 0% 90%', // Light grey text
        accent: '0 0% 60%', // Medium-light grey
        accentForeground: '0 0% 10%', // Very dark text on accent
        background: '0 0% 8%', // Very dark grey background
        foreground: '0 0% 90%', // Light grey text
        muted: '0 0% 15%', // Dark muted grey
        mutedForeground: '0 0% 65%', // Medium-light grey
        card: '0 0% 12%', // Dark grey cards
        cardForeground: '0 0% 90%', // Light grey text
        border: '0 0% 20%', // Dark grey border
        input: '0 0% 18%', // Dark grey input
        ring: '0 0% 80%', // Light grey ring
        destructive: '0 62.8% 30.6%', // Dark red
        destructiveForeground: '210 40% 98%', // White
        success: '142.1 70.6% 45.3%', // Green
        successForeground: '144.9 80.4% 10%', // Dark green
        warning: '32.5 94.6% 43.7%', // Orange
        warningForeground: '210 40% 98%', // White
        info: '0 0% 60%', // Medium-light grey
        infoForeground: '0 0% 10%', // Very dark text
        // Sidebar colors - Dark Grey
        sidebarBackground: '0 0% 10%', // Very dark grey
        sidebarForeground: '0 0% 90%', // Light grey text
        sidebarPrimary: '0 0% 80%', // Light grey primary
        sidebarPrimaryForeground: '0 0% 10%', // Very dark text
        sidebarAccent: '0 0% 60%', // Medium-light grey accent
        sidebarAccentForeground: '0 0% 10%', // Very dark text
        sidebarBorder: '0 0% 20%', // Dark grey border
        sidebarRing: '0 0% 80%', // Light grey ring
      },
    },
    fonts: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'Consolas', 'monospace'],
    },
    borderRadius: {
      sm: '0.5rem',
      md: '0.75rem',
      lg: '1rem',
      xl: '1.5rem',
    },
    shadows: {
      sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
      md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
      lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
      xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    },
  },
};

// Theme Context
interface ThemeContextValue {
  mode: ThemeMode;
  variant: ThemeVariant;
  theme: Theme;
  setMode: (mode: ThemeMode) => void;
  setVariant: (variant: ThemeVariant) => void;
  toggleMode: () => void;
  systemMode: 'light' | 'dark';
  effectiveMode: 'light' | 'dark';
}

export const ThemeContext = createContext<ThemeContextValue | null>(null);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Theme utilities
export const applyTheme = (theme: Theme, mode: 'light' | 'dark') => {
  if (!theme) {
    console.error('applyTheme called with undefined theme');
    return;
  }

  if (!theme.colors || !theme.colors[mode]) {
    console.error('Theme missing colors for mode:', mode, theme);
    return;
  }

  const root = document.documentElement;
  const colors = theme.colors[mode];

  console.log('Applying theme:', theme.name, mode, colors);

  // Apply CSS custom properties
  Object.entries(colors).forEach(([key, value]) => {
    const cssVar = `--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
    root.style.setProperty(cssVar, value);
    console.log(`Set ${cssVar}: ${value}`);
  });

  // Also apply with higher specificity for theme classes
  const themeClass = `theme-${theme.variant}`;
  const modeClass = mode;

  // Force update by adding and removing a temporary class
  root.classList.add('theme-updating');
  setTimeout(() => {
    root.classList.remove('theme-updating');
  }, 10);

  // Apply font families
  root.style.setProperty('--font-sans', theme.fonts.sans.join(', '));
  root.style.setProperty('--font-mono', theme.fonts.mono.join(', '));

  // Apply border radius
  Object.entries(theme.borderRadius).forEach(([key, value]) => {
    root.style.setProperty(`--radius-${key}`, value);
  });

  // Apply shadows
  Object.entries(theme.shadows).forEach(([key, value]) => {
    root.style.setProperty(`--shadow-${key}`, value);
  });
};

// Utility to clean up invalid theme data from localStorage
export const cleanupInvalidThemeData = (): void => {
  if (typeof window === 'undefined') return;

  try {
    // List of all possible localStorage keys that might contain theme data
    const themeKeys = [
      'theme-mode', 'theme-variant', 'evexa-theme', 'theme', 'currentTheme',
      'EVEXA-theme', 'evexaTheme', 'app-theme', 'ui-theme'
    ];

    // Check and clean specific keys
    themeKeys.forEach(key => {
      const value = localStorage.getItem(key);
      if (value && (
        value.includes(' ') ||
        value === 'EVEXA Dark' ||
        value === 'EVEXA Light' ||
        value === 'EVEXA-Dark' ||
        value === 'EVEXA-Light' ||
        !['gold', 'blue', 'grey', 'system', 'light', 'dark'].includes(value)
      )) {
        console.log('Cleaning invalid theme data:', key, value);
        localStorage.removeItem(key);
      }
    });

    // Scan all localStorage keys for any theme-related data with spaces
    const allKeys = Object.keys(localStorage);
    allKeys.forEach(key => {
      if (key.toLowerCase().includes('theme')) {
        const value = localStorage.getItem(key);
        if (value && value.includes(' ')) {
          console.log('Cleaning theme key with spaces:', key, value);
          localStorage.removeItem(key);
        }
      }
    });
  } catch (error) {
    console.error('Error cleaning theme data:', error);
  }
};

export const getStoredTheme = (): { mode: ThemeMode; variant: ThemeVariant } => {
  if (typeof window === 'undefined') {
    return { mode: 'system', variant: 'gold' };
  }

  // Clean up any invalid data first
  cleanupInvalidThemeData();

  const storedMode = localStorage.getItem('theme-mode') as ThemeMode;
  const storedVariant = localStorage.getItem('theme-variant') as ThemeVariant;

  // Validate stored variant against available themes
  const validVariants: ThemeVariant[] = ['gold', 'blue', 'grey'];
  const isValidVariant = storedVariant && validVariants.includes(storedVariant);

  // Additional safety check: ensure no spaces in variant names
  if (storedVariant && storedVariant.includes(' ')) {
    console.warn('Invalid theme variant with spaces detected:', storedVariant);
    localStorage.removeItem('theme-variant');
    return { mode: storedMode || 'system', variant: 'gold' };
  }

  return {
    mode: storedMode || 'system',
    variant: isValidVariant ? storedVariant : 'gold',
  };
};

export const storeTheme = (mode: ThemeMode, variant: ThemeVariant) => {
  if (typeof window === 'undefined') return;

  localStorage.setItem('theme-mode', mode);
  localStorage.setItem('theme-variant', variant);
};

export const getSystemMode = (): 'light' | 'dark' => {
  if (typeof window === 'undefined') return 'light';
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
};
