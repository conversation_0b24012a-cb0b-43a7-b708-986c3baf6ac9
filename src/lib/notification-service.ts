/**
 * Notification Service
 * Comprehensive notification system for various events
 */

import { emailService, type EmailRecipient } from './email-service';
import { db } from './firebase';
import { collection, addDoc, query, where, getDocs, updateDoc, doc } from 'firebase/firestore';

export interface NotificationEvent {
  id: string;
  type: 'user_invitation' | 'password_reset' | 'welcome' | 'task_assigned' | 'exhibition_reminder' | 'system_alert';
  tenantId: string;
  userId?: string;
  recipientEmail: string;
  recipientName?: string;
  subject: string;
  data: Record<string, any>;
  status: 'pending' | 'sent' | 'failed' | 'bounced';
  sentAt?: Date;
  error?: string;
  retryCount: number;
  maxRetries: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface NotificationTemplate {
  type: string;
  name: string;
  description: string;
  emailTemplateId: string;
  defaultSubject: string;
  requiredVariables: string[];
  optionalVariables: string[];
}

export const NOTIFICATION_TEMPLATES: Record<string, NotificationTemplate> = {
  USER_INVITATION: {
    type: 'user_invitation',
    name: 'User Invitation',
    description: 'Sent when a user is invited to join a tenant',
    emailTemplateId: 'user_invitation',
    defaultSubject: 'You\'re invited to join {{tenantName}} on EVEXA',
    requiredVariables: ['tenantName', 'recipientName', 'inviterName', 'invitationLink'],
    optionalVariables: ['personaName', 'moduleAccess', 'expirationDays', 'customMessage']
  },
  PASSWORD_RESET: {
    type: 'password_reset',
    name: 'Password Reset',
    description: 'Sent when a user requests a password reset',
    emailTemplateId: 'password_reset',
    defaultSubject: 'Reset your EVEXA password',
    requiredVariables: ['userName', 'resetLink'],
    optionalVariables: ['expirationHours']
  },
  WELCOME_EMAIL: {
    type: 'welcome',
    name: 'Welcome Email',
    description: 'Sent when a user successfully joins a tenant',
    emailTemplateId: 'welcome_email',
    defaultSubject: 'Welcome to {{tenantName}} on EVEXA!',
    requiredVariables: ['userName', 'tenantName', 'dashboardLink'],
    optionalVariables: ['personaName', 'moduleAccess', 'tenantLogo']
  },
  TASK_ASSIGNED: {
    type: 'task_assigned',
    name: 'Task Assignment',
    description: 'Sent when a task is assigned to a user',
    emailTemplateId: 'task_assigned',
    defaultSubject: 'New task assigned: {{taskTitle}}',
    requiredVariables: ['userName', 'taskTitle', 'assignerName', 'taskLink'],
    optionalVariables: ['taskDescription', 'dueDate', 'priority']
  },
  EXHIBITION_REMINDER: {
    type: 'exhibition_reminder',
    name: 'Exhibition Reminder',
    description: 'Sent as reminders for upcoming exhibitions',
    emailTemplateId: 'exhibition_reminder',
    defaultSubject: 'Reminder: {{exhibitionName}} is coming up',
    requiredVariables: ['userName', 'exhibitionName', 'exhibitionDate', 'exhibitionLink'],
    optionalVariables: ['daysUntil', 'location', 'checklist']
  }
};

export class NotificationService {
  /**
   * Send a notification
   */
  async sendNotification(
    type: string,
    tenantId: string,
    recipientEmail: string,
    data: Record<string, any>,
    options: {
      recipientName?: string;
      userId?: string;
      customSubject?: string;
      priority?: 'high' | 'normal' | 'low';
      maxRetries?: number;
    } = {}
  ): Promise<string> {
    const template = NOTIFICATION_TEMPLATES[type.toUpperCase()];
    if (!template) {
      throw new Error(`Unknown notification type: ${type}`);
    }

    // Validate required variables
    const missingVariables = template.requiredVariables.filter(
      variable => !data[variable]
    );
    if (missingVariables.length > 0) {
      throw new Error(`Missing required variables: ${missingVariables.join(', ')}`);
    }

    // Create notification record
    const notification: Omit<NotificationEvent, 'id'> = {
      type: type as any,
      tenantId,
      userId: options.userId,
      recipientEmail,
      recipientName: options.recipientName,
      subject: options.customSubject || this.replaceVariables(template.defaultSubject, data),
      data,
      status: 'pending',
      retryCount: 0,
      maxRetries: options.maxRetries || 3,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Save to database
    const docRef = await addDoc(collection(db, 'notifications'), notification);
    const notificationId = docRef.id;

    // Send email immediately
    try {
      await this.processNotification(notificationId, notification);
    } catch (error) {
      console.error('Failed to send notification immediately:', error);
      // The notification will be retried by the background processor
    }

    return notificationId;
  }

  /**
   * Process a notification (send the actual email)
   */
  private async processNotification(
    notificationId: string,
    notification: Omit<NotificationEvent, 'id'>
  ): Promise<void> {
    const template = NOTIFICATION_TEMPLATES[notification.type.toUpperCase()];
    if (!template) {
      throw new Error(`Unknown notification type: ${notification.type}`);
    }

    try {
      // Prepare email recipients
      const recipients: EmailRecipient[] = [{
        email: notification.recipientEmail,
        name: notification.recipientName,
        variables: notification.data
      }];

      // Send email using template
      const result = await emailService.sendTemplateEmail(
        template.emailTemplateId,
        recipients,
        notification.data,
        {
          subject: notification.subject,
          priority: 'normal' // Could be made configurable
        }
      );

      // Update notification status
      const updateData: Partial<NotificationEvent> = {
        updatedAt: new Date()
      };

      if (result.success) {
        updateData.status = 'sent';
        updateData.sentAt = new Date();
      } else {
        updateData.status = 'failed';
        updateData.error = result.error;
        updateData.retryCount = notification.retryCount + 1;
      }

      await updateDoc(doc(db, 'notifications', notificationId), updateData);

    } catch (error) {
      // Update notification with error
      await updateDoc(doc(db, 'notifications', notificationId), {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        retryCount: notification.retryCount + 1,
        updatedAt: new Date()
      });
      throw error;
    }
  }

  /**
   * Send user invitation notification
   */
  async sendUserInvitation(
    tenantId: string,
    recipientEmail: string,
    inviterName: string,
    invitationLink: string,
    options: {
      recipientName?: string;
      tenantName?: string;
      personaName?: string;
      moduleAccess?: string[];
      expirationDays?: number;
      customMessage?: string;
    } = {}
  ): Promise<string> {
    const data = {
      inviterName,
      invitationLink,
      tenantName: options.tenantName || 'Your Organization',
      recipientName: options.recipientName || 'there',
      personaName: options.personaName || 'Team Member',
      moduleAccess: options.moduleAccess || [],
      expirationDays: options.expirationDays || 7,
      customMessage: options.customMessage || '',
      tenantLogo: 'EVEXA', // Could be customized per tenant
      inviterEmail: '<EMAIL>' // Could be the actual inviter's email
    };

    return this.sendNotification(
      'user_invitation',
      tenantId,
      recipientEmail,
      data,
      {
        recipientName: options.recipientName,
        priority: 'high'
      }
    );
  }

  /**
   * Send welcome email notification
   */
  async sendWelcomeEmail(
    tenantId: string,
    userId: string,
    recipientEmail: string,
    userName: string,
    options: {
      tenantName?: string;
      personaName?: string;
      moduleAccess?: string[];
      dashboardLink?: string;
    } = {}
  ): Promise<string> {
    const data = {
      userName,
      tenantName: options.tenantName || 'Your Organization',
      personaName: options.personaName || 'Team Member',
      moduleAccess: options.moduleAccess || [],
      dashboardLink: options.dashboardLink || '/dashboard',
      tenantLogo: 'EVEXA' // Could be customized per tenant
    };

    return this.sendNotification(
      'welcome',
      tenantId,
      recipientEmail,
      data,
      {
        recipientName: userName,
        userId,
        priority: 'normal'
      }
    );
  }

  /**
   * Send password reset notification
   */
  async sendPasswordReset(
    tenantId: string,
    recipientEmail: string,
    userName: string,
    resetLink: string,
    options: {
      expirationHours?: number;
    } = {}
  ): Promise<string> {
    const data = {
      userName,
      resetLink,
      expirationHours: options.expirationHours || 24
    };

    return this.sendNotification(
      'password_reset',
      tenantId,
      recipientEmail,
      data,
      {
        recipientName: userName,
        priority: 'high'
      }
    );
  }

  /**
   * Get notification history for a tenant
   */
  async getNotificationHistory(
    tenantId: string,
    options: {
      limit?: number;
      type?: string;
      status?: string;
    } = {}
  ): Promise<NotificationEvent[]> {
    let q = query(
      collection(db, 'notifications'),
      where('tenantId', '==', tenantId)
    );

    if (options.type) {
      q = query(q, where('type', '==', options.type));
    }

    if (options.status) {
      q = query(q, where('status', '==', options.status));
    }

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as NotificationEvent));
  }

  /**
   * Retry failed notifications
   */
  async retryFailedNotifications(tenantId: string): Promise<number> {
    const failedNotifications = await this.getNotificationHistory(tenantId, {
      status: 'failed'
    });

    let retriedCount = 0;

    for (const notification of failedNotifications) {
      if (notification.retryCount < notification.maxRetries) {
        try {
          await this.processNotification(notification.id, notification);
          retriedCount++;
        } catch (error) {
          console.error(`Failed to retry notification ${notification.id}:`, error);
        }
      }
    }

    return retriedCount;
  }

  /**
   * Replace variables in template strings
   */
  private replaceVariables(template: string, variables: Record<string, any>): string {
    let result = template;
    
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, String(value));
    });

    return result;
  }

  /**
   * Get available notification templates
   */
  getTemplates(): NotificationTemplate[] {
    return Object.values(NOTIFICATION_TEMPLATES);
  }

  /**
   * Get template by type
   */
  getTemplate(type: string): NotificationTemplate | null {
    return NOTIFICATION_TEMPLATES[type.toUpperCase()] || null;
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
