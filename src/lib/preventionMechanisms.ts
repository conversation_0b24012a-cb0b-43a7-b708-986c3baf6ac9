/**
 * EVEXA Prevention Mechanisms v1.0
 * 
 * Comprehensive prevention system to block creation of non-standard 
 * collection names or structures. Integrates with all data operations
 * to maintain schema integrity.
 */

import { 
  runtimeValidator, 
  UnauthorizedCollectionError, 
  DataValidationError, 
  TenantIsolationError 
} from './runtimeValidator';
import { validateCollectionData } from './collectionSchemas';
import { getAllCollectionNames } from './collections';
import { DataIntegrityValidator } from './dataIntegrityValidator';
import { chaosDetectionService } from '@/services/chaosDetectionService';

/**
 * Prevention Configuration
 */
interface PreventionConfig {
  enableCollectionWhitelist: boolean;
  enableSchemaValidation: boolean;
  enableTenantIsolation: boolean;
  enableNamingConventions: boolean;
  enableRealTimeMonitoring: boolean;
  logAllAttempts: boolean;
  blockUnauthorizedOperations: boolean;
  alertOnViolations: boolean;
}

const DEFAULT_PREVENTION_CONFIG: PreventionConfig = {
  enableCollectionWhitelist: true,
  enableSchemaValidation: true,
  enableTenantIsolation: true,
  enableNamingConventions: true,
  enableRealTimeMonitoring: true,
  logAllAttempts: true,
  blockUnauthorizedOperations: true,
  alertOnViolations: true,
};

/**
 * Prevention Violation Types
 */
export interface PreventionViolation {
  type: 'unauthorized_collection' | 'invalid_schema' | 'naming_violation' | 'tenant_violation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  collection: string;
  operation: 'create' | 'update' | 'delete' | 'read';
  description: string;
  timestamp: Date;
  blocked: boolean;
  data?: any;
  stackTrace?: string;
}

/**
 * Prevention Mechanisms Class
 */
export class PreventionMechanisms {
  private config: PreventionConfig;
  private violations: PreventionViolation[] = [];
  private isActive: boolean = false;

  constructor(config: Partial<PreventionConfig> = {}) {
    this.config = { ...DEFAULT_PREVENTION_CONFIG, ...config };
  }

  /**
   * Activate prevention mechanisms
   */
  activate(): void {
    if (this.isActive) {
      console.log('🛡️ Prevention mechanisms already active');
      return;
    }

    console.log('🛡️ Activating EVEXA Prevention Mechanisms...');
    this.isActive = true;

    // Start real-time monitoring if enabled
    if (this.config.enableRealTimeMonitoring) {
      this.startRealTimeMonitoring();
    }

    console.log('✅ Prevention mechanisms activated');
  }

  /**
   * Deactivate prevention mechanisms
   */
  deactivate(): void {
    if (!this.isActive) {
      console.log('🛡️ Prevention mechanisms already inactive');
      return;
    }

    console.log('🛑 Deactivating EVEXA Prevention Mechanisms...');
    this.isActive = false;
    console.log('✅ Prevention mechanisms deactivated');
  }

  /**
   * Validate collection operation before execution
   */
  async validateOperation(
    operation: 'create' | 'update' | 'delete' | 'read',
    collectionName: string,
    data?: any
  ): Promise<{
    allowed: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // 1. Collection whitelist validation
      if (this.config.enableCollectionWhitelist) {
        const whitelistResult = this.validateCollectionWhitelist(collectionName);
        if (!whitelistResult.valid) {
          errors.push(...whitelistResult.errors);
          this.logViolation({
            type: 'unauthorized_collection',
            severity: 'critical',
            collection: collectionName,
            operation,
            description: `Unauthorized collection: ${collectionName}`,
            timestamp: new Date(),
            blocked: this.config.blockUnauthorizedOperations,
            data,
          });
        }
      }

      // 2. Naming convention validation
      if (this.config.enableNamingConventions) {
        const namingResult = this.validateNamingConventions(collectionName);
        if (!namingResult.valid) {
          errors.push(...namingResult.errors);
          this.logViolation({
            type: 'naming_violation',
            severity: 'high',
            collection: collectionName,
            operation,
            description: `Naming convention violation: ${collectionName}`,
            timestamp: new Date(),
            blocked: this.config.blockUnauthorizedOperations,
            data,
          });
        }
      }

      // 3. Schema validation (for create/update operations with data)
      if (this.config.enableSchemaValidation && data && (operation === 'create' || operation === 'update')) {
        const schemaResult = this.validateSchema(collectionName, data);
        if (!schemaResult.valid) {
          errors.push(...schemaResult.errors);
          warnings.push(...schemaResult.warnings);
          this.logViolation({
            type: 'invalid_schema',
            severity: 'medium',
            collection: collectionName,
            operation,
            description: `Schema validation failed: ${schemaResult.errors.join(', ')}`,
            timestamp: new Date(),
            blocked: this.config.blockUnauthorizedOperations,
            data,
          });
        }
      }

      // 4. Tenant isolation validation
      if (this.config.enableTenantIsolation && data) {
        const tenantResult = this.validateTenantIsolation(data, collectionName);
        if (!tenantResult.valid) {
          errors.push(...tenantResult.errors);
          this.logViolation({
            type: 'tenant_violation',
            severity: 'high',
            collection: collectionName,
            operation,
            description: `Tenant isolation violation: ${tenantResult.errors.join(', ')}`,
            timestamp: new Date(),
            blocked: this.config.blockUnauthorizedOperations,
            data,
          });
        }
      }

      const allowed = errors.length === 0 || !this.config.blockUnauthorizedOperations;

      // Log all attempts if configured
      if (this.config.logAllAttempts) {
        console.log(`🛡️ Operation validation: ${operation} on ${collectionName} - ${allowed ? 'ALLOWED' : 'BLOCKED'}`);
        if (errors.length > 0) {
          console.error(`🚨 Validation errors:`, errors);
        }
        if (warnings.length > 0) {
          console.warn(`⚠️ Validation warnings:`, warnings);
        }
      }

      return { allowed, errors, warnings };

    } catch (error) {
      const errorMessage = `Prevention validation failed: ${error instanceof Error ? error.message : String(error)}`;
      errors.push(errorMessage);
      
      this.logViolation({
        type: 'invalid_schema',
        severity: 'critical',
        collection: collectionName,
        operation,
        description: errorMessage,
        timestamp: new Date(),
        blocked: true,
        data,
        stackTrace: error instanceof Error ? error.stack : undefined,
      });

      return { allowed: false, errors, warnings };
    }
  }

  /**
   * Validate collection against whitelist
   */
  private validateCollectionWhitelist(collectionName: string): {
    valid: boolean;
    errors: string[];
  } {
    const authorizedCollections = getAllCollectionNames();
    
    if (!authorizedCollections.includes(collectionName)) {
      return {
        valid: false,
        errors: [`Collection "${collectionName}" is not in authorized schema. Use one of: ${authorizedCollections.slice(0, 5).join(', ')}...`]
      };
    }

    return { valid: true, errors: [] };
  }

  /**
   * Validate naming conventions
   */
  private validateNamingConventions(collectionName: string): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Check snake_case pattern
    if (!/^[a-z][a-z0-9_]*[a-z0-9]$/.test(collectionName)) {
      errors.push(`Collection name "${collectionName}" must use snake_case format`);
    }

    // Check for camelCase
    if (/[A-Z]/.test(collectionName)) {
      errors.push(`Collection name "${collectionName}" contains uppercase letters (camelCase not allowed)`);
    }

    // Check for kebab-case
    if (collectionName.includes('-')) {
      errors.push(`Collection name "${collectionName}" contains hyphens (kebab-case not allowed)`);
    }

    // Check minimum length
    if (collectionName.length < 3) {
      errors.push(`Collection name "${collectionName}" is too short (minimum 3 characters)`);
    }

    // Check maximum length
    if (collectionName.length > 50) {
      errors.push(`Collection name "${collectionName}" is too long (maximum 50 characters)`);
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * Validate data schema
   */
  private validateSchema(collectionName: string, data: any): {
    valid: boolean;
    errors: string[];
    warnings: string[];
  } {
    try {
      const validation = validateCollectionData(collectionName, data);
      
      return {
        valid: validation.isValid,
        errors: validation.isValid ? [] : validation.errors,
        warnings: [] // Add warnings logic if needed
      };
    } catch (error) {
      return {
        valid: false,
        errors: [`Schema validation error: ${error instanceof Error ? error.message : String(error)}`],
        warnings: []
      };
    }
  }

  /**
   * Validate tenant isolation
   */
  private validateTenantIsolation(data: any, collectionName?: string): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // System collections that don't require tenant_id
    const systemCollections = [
      'analytics_configs',
      'analytics_data',
      'performance_metrics',
      'user_activity_logs',
      'business_metrics',
      'dashboard_configs',
      'system_configs',
      'global_configs',
      'release_notes',
      'api_usage_logs',
      'integration_logs',
      'backup_records'
    ];

    // Skip tenant validation for system collections
    if (collectionName && systemCollections.includes(collectionName)) {
      return { valid: true, errors: [] };
    }

    // Check for tenant_id field
    if (!data.tenant_id) {
      errors.push('Missing required tenant_id field');
    } else {
      // Validate tenant_id format (basic validation)
      if (typeof data.tenant_id !== 'string' || data.tenant_id.length < 3) {
        errors.push('Invalid tenant_id format');
      }
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * Log prevention violation
   */
  private logViolation(violation: PreventionViolation): void {
    this.violations.push(violation);

    // Keep only last 1000 violations to prevent memory issues
    if (this.violations.length > 1000) {
      this.violations = this.violations.slice(-1000);
    }

    // Alert if configured
    if (this.config.alertOnViolations) {
      console.error(`🚨 PREVENTION VIOLATION:`, violation);
    }

    // Integrate with chaos detection service
    if (chaosDetectionService) {
      // This would trigger chaos detection alerts
      // chaosDetectionService.reportViolation(violation);
    }
  }

  /**
   * Start real-time monitoring
   */
  private startRealTimeMonitoring(): void {
    console.log('🔍 Starting real-time prevention monitoring...');
    
    // This would integrate with Firebase security rules or middleware
    // For now, we rely on the runtime validator integration
  }

  /**
   * Get violation statistics
   */
  getViolationStats(): {
    total: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
    recent: PreventionViolation[];
  } {
    const byType: Record<string, number> = {};
    const bySeverity: Record<string, number> = {};

    this.violations.forEach(violation => {
      byType[violation.type] = (byType[violation.type] || 0) + 1;
      bySeverity[violation.severity] = (bySeverity[violation.severity] || 0) + 1;
    });

    return {
      total: this.violations.length,
      byType,
      bySeverity,
      recent: this.violations.slice(-10) // Last 10 violations
    };
  }

  /**
   * Clear violation log
   */
  clearViolations(): void {
    this.violations = [];
    console.log('🧹 Prevention violation log cleared');
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<PreventionConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Prevention configuration updated');
  }

  /**
   * Get current configuration
   */
  getConfig(): PreventionConfig {
    return { ...this.config };
  }

  /**
   * Check if prevention is active
   */
  isActivated(): boolean {
    return this.isActive;
  }
}

/**
 * Global prevention mechanisms instance
 */
export const preventionMechanisms = new PreventionMechanisms();

// Auto-activate prevention mechanisms
preventionMechanisms.activate();

/**
 * Convenience function for validating operations
 */
export const validateOperation = (
  operation: 'create' | 'update' | 'delete' | 'read',
  collectionName: string,
  data?: any
) => preventionMechanisms.validateOperation(operation, collectionName, data);

/**
 * Emergency functions
 */
export const emergencyDisablePrevention = () => {
  console.warn('🚨 EMERGENCY: Prevention mechanisms disabled!');
  preventionMechanisms.deactivate();
};

export const emergencyEnablePrevention = () => {
  console.log('✅ Prevention mechanisms re-enabled');
  preventionMechanisms.activate();
};
