/**
 * Default Assets Configuration
 * Centralized configuration for default images and assets used throughout EVEXA
 */

// Default Images from Unsplash (professional, royalty-free)
export const DEFAULT_IMAGES = {
  // Email Builder Images
  email: {
    hero: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=600&h=300&fit=crop&crop=center',
    event: 'https://images.unsplash.com/photo-1511578314322-379afb476865?w=600&h=300&fit=crop&crop=center',
    exhibition: 'https://images.unsplash.com/photo-1505373877841-8d25f7d46678?w=600&h=300&fit=crop&crop=center',
    networking: 'https://images.unsplash.com/photo-1515187029135-18ee286d815b?w=600&h=300&fit=crop&crop=center',
    business: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=300&fit=crop&crop=center'
  },
  
  // Profile and Avatar Images
  profile: {
    default: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    business: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',
    professional: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=150&h=150&fit=crop&crop=face'
  },
  
  // Exhibition and Event Images
  exhibition: {
    booth: 'https://images.unsplash.com/photo-1505373877841-8d25f7d46678?w=800&h=400&fit=crop&crop=center',
    conference: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=800&h=400&fit=crop&crop=center',
    tradeshow: 'https://images.unsplash.com/photo-1511578314322-379afb476865?w=800&h=400&fit=crop&crop=center',
    networking: 'https://images.unsplash.com/photo-1515187029135-18ee286d815b?w=800&h=400&fit=crop&crop=center'
  },
  
  // Company and Brand Images
  company: {
    logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&h=200&fit=crop&crop=center',
    office: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=800&h=400&fit=crop&crop=center',
    team: 'https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=800&h=400&fit=crop&crop=center'
  },
  
  // Product and Service Images
  product: {
    technology: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop&crop=center',
    software: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop&crop=center',
    service: 'https://images.unsplash.com/photo-1556761175-4b46a572b786?w=600&h=400&fit=crop&crop=center'
  }
} as const;

// Default Contact Information
export const DEFAULT_CONTACT = {
  email: {
    support: '<EMAIL>',
    noreply: '<EMAIL>',
    admin: '<EMAIL>',
    sales: '<EMAIL>',
    info: '<EMAIL>'
  },
  
  phone: {
    main: '+****************',
    support: '+****************',
    sales: '+****************'
  },
  
  address: {
    street: '123 Innovation Drive',
    city: 'Tech City',
    state: 'TC',
    zip: '12345',
    country: 'United States',
    full: '123 Innovation Drive, Tech City, TC 12345, United States'
  },
  
  social: {
    website: 'https://evexa.com',
    twitter: '@evexa_platform',
    linkedin: 'company/evexa',
    github: 'evexa-platform',
    facebook: 'evexa.platform'
  }
} as const;

// Default Brand Colors
export const DEFAULT_BRAND = {
  colors: {
    primary: '#2563eb',
    secondary: '#64748b',
    accent: '#0ea5e9',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    background: '#ffffff',
    foreground: '#0f172a'
  },
  
  fonts: {
    primary: 'Inter, sans-serif',
    secondary: 'system-ui, sans-serif',
    mono: 'JetBrains Mono, monospace'
  },
  
  name: 'EVEXA',
  fullName: 'Event Excellence Automated',
  tagline: 'Event Excellence Automated',
  description: 'Comprehensive AI-enhanced platform for exhibition and event management'
} as const;

// Helper functions to get default assets
export const getDefaultImage = (category: keyof typeof DEFAULT_IMAGES, type?: string): string => {
  const categoryImages = DEFAULT_IMAGES[category];
  if (type && type in categoryImages) {
    return (categoryImages as any)[type];
  }
  // Return first image in category as fallback
  return Object.values(categoryImages)[0];
};

export const getDefaultEmail = (type: keyof typeof DEFAULT_CONTACT.email): string => {
  return DEFAULT_CONTACT.email[type];
};

export const getDefaultPhone = (type: keyof typeof DEFAULT_CONTACT.phone): string => {
  return DEFAULT_CONTACT.phone[type];
};

// Image size utilities
export const getImageWithSize = (baseUrl: string, width: number, height: number): string => {
  if (baseUrl.includes('unsplash.com')) {
    // Update Unsplash URL with new dimensions
    const url = new URL(baseUrl);
    url.searchParams.set('w', width.toString());
    url.searchParams.set('h', height.toString());
    return url.toString();
  }
  return baseUrl;
};

// Responsive image utilities
export const getResponsiveImageSizes = (baseUrl: string) => ({
  small: getImageWithSize(baseUrl, 300, 200),
  medium: getImageWithSize(baseUrl, 600, 400),
  large: getImageWithSize(baseUrl, 1200, 800),
  thumbnail: getImageWithSize(baseUrl, 150, 150)
});
