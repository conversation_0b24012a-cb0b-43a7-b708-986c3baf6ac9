/**
 * Default Usage Tiers Configuration for EVEXA
 * Defines pricing tiers and overage billing for different usage categories
 */

import { UsageTier } from '@/services/advancedUsageAnalyticsService';

export const defaultUsageTiers: Omit<UsageTier, 'id'>[] = [
  // Exhibitions
  {
    name: 'Exhibition Basic',
    category: 'exhibitions',
    minQuantity: 0,
    maxQuantity: 5,
    unitPrice: 0,
    currency: 'USD',
    billingModel: 'flat_rate',
    overage: {
      enabled: true,
      unitPrice: 50,
      threshold: 5,
      warningThreshold: 4
    }
  },
  {
    name: 'Exhibition Professional',
    category: 'exhibitions',
    minQuantity: 6,
    maxQuantity: 25,
    unitPrice: 25,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 40,
      threshold: 25,
      warningThreshold: 20
    }
  },
  {
    name: 'Exhibition Enterprise',
    category: 'exhibitions',
    minQuantity: 26,
    unitPrice: 20,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 35,
      warningThreshold: 80 // 80% of limit
    }
  },

  // Events
  {
    name: 'Event Basic',
    category: 'events',
    minQuantity: 0,
    maxQuantity: 10,
    unitPrice: 0,
    currency: 'USD',
    billingModel: 'flat_rate',
    overage: {
      enabled: true,
      unitPrice: 15,
      threshold: 10,
      warningThreshold: 8
    }
  },
  {
    name: 'Event Professional',
    category: 'events',
    minQuantity: 11,
    maxQuantity: 50,
    unitPrice: 10,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 12,
      threshold: 50,
      warningThreshold: 40
    }
  },
  {
    name: 'Event Enterprise',
    category: 'events',
    minQuantity: 51,
    unitPrice: 8,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 10,
      warningThreshold: 80
    }
  },

  // Users
  {
    name: 'User Basic',
    category: 'users',
    minQuantity: 0,
    maxQuantity: 5,
    unitPrice: 0,
    currency: 'USD',
    billingModel: 'flat_rate',
    overage: {
      enabled: true,
      unitPrice: 25,
      threshold: 5,
      warningThreshold: 4
    }
  },
  {
    name: 'User Professional',
    category: 'users',
    minQuantity: 6,
    maxQuantity: 25,
    unitPrice: 20,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 22,
      threshold: 25,
      warningThreshold: 20
    }
  },
  {
    name: 'User Enterprise',
    category: 'users',
    minQuantity: 26,
    unitPrice: 18,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 20,
      warningThreshold: 80
    }
  },

  // Tasks
  {
    name: 'Task Basic',
    category: 'tasks',
    minQuantity: 0,
    maxQuantity: 100,
    unitPrice: 0,
    currency: 'USD',
    billingModel: 'flat_rate',
    overage: {
      enabled: true,
      unitPrice: 0.50,
      threshold: 100,
      warningThreshold: 80
    }
  },
  {
    name: 'Task Professional',
    category: 'tasks',
    minQuantity: 101,
    maxQuantity: 1000,
    unitPrice: 0.25,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 0.30,
      threshold: 1000,
      warningThreshold: 800
    }
  },
  {
    name: 'Task Enterprise',
    category: 'tasks',
    minQuantity: 1001,
    unitPrice: 0.20,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 0.25,
      warningThreshold: 80
    }
  },

  // Leads
  {
    name: 'Lead Basic',
    category: 'leads',
    minQuantity: 0,
    maxQuantity: 500,
    unitPrice: 0,
    currency: 'USD',
    billingModel: 'flat_rate',
    overage: {
      enabled: true,
      unitPrice: 0.10,
      threshold: 500,
      warningThreshold: 400
    }
  },
  {
    name: 'Lead Professional',
    category: 'leads',
    minQuantity: 501,
    maxQuantity: 5000,
    unitPrice: 0.05,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 0.08,
      threshold: 5000,
      warningThreshold: 4000
    }
  },
  {
    name: 'Lead Enterprise',
    category: 'leads',
    minQuantity: 5001,
    unitPrice: 0.03,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 0.05,
      warningThreshold: 80
    }
  },

  // Storage
  {
    name: 'Storage Basic',
    category: 'storage',
    minQuantity: 0,
    maxQuantity: 5, // 5GB
    unitPrice: 0,
    currency: 'USD',
    billingModel: 'flat_rate',
    overage: {
      enabled: true,
      unitPrice: 2, // $2 per GB
      threshold: 5,
      warningThreshold: 4
    }
  },
  {
    name: 'Storage Professional',
    category: 'storage',
    minQuantity: 6,
    maxQuantity: 50,
    unitPrice: 1.50,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 1.75,
      threshold: 50,
      warningThreshold: 40
    }
  },
  {
    name: 'Storage Enterprise',
    category: 'storage',
    minQuantity: 51,
    unitPrice: 1.25,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 1.50,
      warningThreshold: 80
    }
  },

  // API Calls
  {
    name: 'API Basic',
    category: 'api_calls',
    minQuantity: 0,
    maxQuantity: 10000,
    unitPrice: 0,
    currency: 'USD',
    billingModel: 'flat_rate',
    overage: {
      enabled: true,
      unitPrice: 0.001, // $0.001 per call
      threshold: 10000,
      warningThreshold: 8000
    }
  },
  {
    name: 'API Professional',
    category: 'api_calls',
    minQuantity: 10001,
    maxQuantity: 100000,
    unitPrice: 0.0008,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 0.0009,
      threshold: 100000,
      warningThreshold: 80000
    }
  },
  {
    name: 'API Enterprise',
    category: 'api_calls',
    minQuantity: 100001,
    unitPrice: 0.0006,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 0.0008,
      warningThreshold: 80
    }
  },

  // Emails
  {
    name: 'Email Basic',
    category: 'emails',
    minQuantity: 0,
    maxQuantity: 1000,
    unitPrice: 0,
    currency: 'USD',
    billingModel: 'flat_rate',
    overage: {
      enabled: true,
      unitPrice: 0.01, // $0.01 per email
      threshold: 1000,
      warningThreshold: 800
    }
  },
  {
    name: 'Email Professional',
    category: 'emails',
    minQuantity: 1001,
    maxQuantity: 10000,
    unitPrice: 0.008,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 0.009,
      threshold: 10000,
      warningThreshold: 8000
    }
  },
  {
    name: 'Email Enterprise',
    category: 'emails',
    minQuantity: 10001,
    unitPrice: 0.006,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 0.008,
      warningThreshold: 80
    }
  },

  // AI Requests
  {
    name: 'AI Basic',
    category: 'ai_requests',
    minQuantity: 0,
    maxQuantity: 1000,
    unitPrice: 0,
    currency: 'USD',
    billingModel: 'flat_rate',
    overage: {
      enabled: true,
      unitPrice: 0.02, // $0.02 per request
      threshold: 1000,
      warningThreshold: 800
    }
  },
  {
    name: 'AI Professional',
    category: 'ai_requests',
    minQuantity: 1001,
    maxQuantity: 10000,
    unitPrice: 0.015,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 0.018,
      threshold: 10000,
      warningThreshold: 8000
    }
  },
  {
    name: 'AI Enterprise',
    category: 'ai_requests',
    minQuantity: 10001,
    unitPrice: 0.012,
    currency: 'USD',
    billingModel: 'per_unit',
    overage: {
      enabled: true,
      unitPrice: 0.015,
      warningThreshold: 80
    }
  }
];

export function getUsageTierForCategory(category: string, quantity: number): Omit<UsageTier, 'id'> | undefined {
  const categoryTiers = defaultUsageTiers.filter(tier => tier.category === category);
  
  // Find the appropriate tier based on quantity
  return categoryTiers.find(tier => {
    if (tier.maxQuantity) {
      return quantity >= tier.minQuantity && quantity <= tier.maxQuantity;
    } else {
      return quantity >= tier.minQuantity;
    }
  });
}

export function calculateUsageCost(category: string, quantity: number): number {
  const tier = getUsageTierForCategory(category, quantity);
  if (!tier) return 0;

  switch (tier.billingModel) {
    case 'flat_rate':
      return tier.unitPrice;
    case 'per_unit':
      return quantity * tier.unitPrice;
    case 'tiered':
      // Implement tiered pricing logic if needed
      return quantity * tier.unitPrice;
    case 'volume':
      // Implement volume pricing logic if needed
      return quantity * tier.unitPrice;
    default:
      return quantity * tier.unitPrice;
  }
}
