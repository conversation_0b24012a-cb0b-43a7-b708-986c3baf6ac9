/**
 * EVEXA Firebase Health Check Utilities
 * Comprehensive health monitoring for Firebase collections and operations
 */

import React from 'react';
import { collection, getDocs, limit, query } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { COLLECTIONS } from '@/lib/collections';
import { validateCollectionName, SafeFirebaseOperations } from '@/lib/firebase-validation';

export interface CollectionHealthStatus {
  name: string;
  isValid: boolean;
  isAccessible: boolean;
  documentCount?: number;
  error?: string;
  lastChecked: Date;
}

export interface FirebaseHealthReport {
  overall: 'healthy' | 'warning' | 'critical';
  collections: CollectionHealthStatus[];
  totalCollections: number;
  healthyCollections: number;
  warningCollections: number;
  criticalCollections: number;
  generatedAt: Date;
}

// Check individual collection health
export async function checkCollectionHealth(
  collectionName: string | undefined | null
): Promise<CollectionHealthStatus> {
  const status: CollectionHealthStatus = {
    name: collectionName || 'unknown',
    isValid: false,
    isAccessible: false,
    lastChecked: new Date()
  };

  // Validate collection name
  const validation = validateCollectionName(collectionName);
  if (!validation.isValid) {
    status.error = validation.error;
    return status;
  }

  status.isValid = true;
  status.name = validation.collectionName;

  try {
    // Try to access the collection
    const collectionRef = SafeFirebaseOperations.getCollection(validation.collectionName);
    if (!collectionRef) {
      status.error = 'Failed to create collection reference';
      return status;
    }

    // Try to get a small sample of documents to test accessibility
    const testQuery = query(collectionRef, limit(1));
    const snapshot = await getDocs(testQuery);
    
    status.isAccessible = true;
    status.documentCount = snapshot.size;

  } catch (error: any) {
    status.error = error.message || 'Unknown error accessing collection';
    console.error(`Health check failed for collection ${validation.collectionName}:`, error);
  }

  return status;
}

// Check all dashboard collections
export async function checkDashboardCollectionsHealth(): Promise<CollectionHealthStatus[]> {
  const collectionsToCheck = [
    { key: 'EXHIBITIONS', name: COLLECTIONS.EXHIBITIONS },
    { key: 'EXHIBITION_EVENTS', name: COLLECTIONS.EXHIBITION_EVENTS },
    { key: 'LEAD_CONTACTS', name: COLLECTIONS.LEAD_CONTACTS },
    { key: 'TASKS', name: COLLECTIONS.TASKS },
    { key: 'USERS', name: COLLECTIONS.USERS },
    { key: 'BUDGETS', name: COLLECTIONS.BUDGETS },
    { key: 'EXPENSES', name: COLLECTIONS.EXPENSES },
    { key: 'PURCHASE_ORDERS', name: COLLECTIONS.PURCHASE_ORDERS },
    { key: 'MARKETING_CAMPAIGNS', name: COLLECTIONS.MARKETING_CAMPAIGNS },
    { key: 'ANALYTICS_REPORTS', name: COLLECTIONS.ANALYTICS_REPORTS },
  ];

  const healthChecks = await Promise.allSettled(
    collectionsToCheck.map(async ({ key, name }) => {
      try {
        const health = await checkCollectionHealth(name);
        return { ...health, key };
      } catch (error) {
        return {
          name: name || key,
          key,
          isValid: false,
          isAccessible: false,
          error: `Health check failed: ${error}`,
          lastChecked: new Date()
        };
      }
    })
  );

  return healthChecks.map((result, index) => {
    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      const { key, name } = collectionsToCheck[index];
      return {
        name: name || key,
        key,
        isValid: false,
        isAccessible: false,
        error: `Promise rejected: ${result.reason}`,
        lastChecked: new Date()
      };
    }
  });
}

// Generate comprehensive health report
export async function generateFirebaseHealthReport(): Promise<FirebaseHealthReport> {
  const collections = await checkDashboardCollectionsHealth();
  
  const healthyCollections = collections.filter(c => c.isValid && c.isAccessible).length;
  const warningCollections = collections.filter(c => c.isValid && !c.isAccessible).length;
  const criticalCollections = collections.filter(c => !c.isValid).length;

  let overall: 'healthy' | 'warning' | 'critical' = 'healthy';
  
  if (criticalCollections > 0) {
    overall = 'critical';
  } else if (warningCollections > 0) {
    overall = 'warning';
  }

  return {
    overall,
    collections,
    totalCollections: collections.length,
    healthyCollections,
    warningCollections,
    criticalCollections,
    generatedAt: new Date()
  };
}

// Quick health check for critical collections
export async function quickHealthCheck(): Promise<boolean> {
  const criticalCollections = [
    COLLECTIONS.EXHIBITIONS,
    COLLECTIONS.USERS,
    COLLECTIONS.TASKS
  ];

  try {
    const healthChecks = await Promise.all(
      criticalCollections.map(collectionName => checkCollectionHealth(collectionName))
    );

    return healthChecks.every(check => check.isValid && check.isAccessible);
  } catch (error) {
    console.error('Quick health check failed:', error);
    return false;
  }
}

// Monitor collection health in real-time
export class FirebaseHealthMonitor {
  private static instance: FirebaseHealthMonitor;
  private healthStatus: Map<string, CollectionHealthStatus> = new Map();
  private monitoringInterval: NodeJS.Timeout | null = null;
  private listeners: ((report: FirebaseHealthReport) => void)[] = [];

  static getInstance(): FirebaseHealthMonitor {
    if (!FirebaseHealthMonitor.instance) {
      FirebaseHealthMonitor.instance = new FirebaseHealthMonitor();
    }
    return FirebaseHealthMonitor.instance;
  }

  // Start monitoring
  startMonitoring(intervalMs: number = 60000): void {
    if (this.monitoringInterval) {
      this.stopMonitoring();
    }

    this.monitoringInterval = setInterval(async () => {
      try {
        const report = await generateFirebaseHealthReport();
        this.updateHealthStatus(report);
        this.notifyListeners(report);
      } catch (error) {
        console.error('Health monitoring error:', error);
      }
    }, intervalMs);

    // Initial check
    this.performHealthCheck();
  }

  // Stop monitoring
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  // Add health status listener
  addListener(callback: (report: FirebaseHealthReport) => void): void {
    this.listeners.push(callback);
  }

  // Remove health status listener
  removeListener(callback: (report: FirebaseHealthReport) => void): void {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  // Get current health status
  getCurrentHealth(): Map<string, CollectionHealthStatus> {
    return new Map(this.healthStatus);
  }

  // Perform immediate health check
  async performHealthCheck(): Promise<FirebaseHealthReport> {
    const report = await generateFirebaseHealthReport();
    this.updateHealthStatus(report);
    this.notifyListeners(report);
    return report;
  }

  private updateHealthStatus(report: FirebaseHealthReport): void {
    this.healthStatus.clear();
    report.collections.forEach(collection => {
      this.healthStatus.set(collection.name, collection);
    });
  }

  private notifyListeners(report: FirebaseHealthReport): void {
    this.listeners.forEach(listener => {
      try {
        listener(report);
      } catch (error) {
        console.error('Error notifying health status listener:', error);
      }
    });
  }
}

// React hook for Firebase health monitoring
export function useFirebaseHealth() {
  const [healthReport, setHealthReport] = React.useState<FirebaseHealthReport | null>(null);
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    const monitor = FirebaseHealthMonitor.getInstance();
    
    const handleHealthUpdate = (report: FirebaseHealthReport) => {
      setHealthReport(report);
      setIsLoading(false);
    };

    monitor.addListener(handleHealthUpdate);
    
    // Start monitoring
    monitor.startMonitoring(30000); // Check every 30 seconds

    // Cleanup
    return () => {
      monitor.removeListener(handleHealthUpdate);
      monitor.stopMonitoring();
    };
  }, []);

  const performHealthCheck = React.useCallback(async () => {
    setIsLoading(true);
    const monitor = FirebaseHealthMonitor.getInstance();
    const report = await monitor.performHealthCheck();
    setHealthReport(report);
    setIsLoading(false);
    return report;
  }, []);

  return {
    healthReport,
    isLoading,
    performHealthCheck
  };
}

export default FirebaseHealthMonitor;
