/**
 * EVEXA Collection Constants - SINGLE SOURCE OF TRUTH
 *
 * All collection names must be imported from here to ensure consistency
 * and prevent hardcoded collection names throughout the codebase.
 */

/**
 * EVEXA Professional Collection Constants v2.0
 *
 * All collection names must be imported from here to ensure consistency
 * and prevent hardcoded collection names throughout the codebase.
 */
export const COLLECTIONS = {
  // Tier 1: Core Business Collections
  // User Management Module
  USER_PROFILES: 'user_profiles',
  USER_GROUPS: 'user_groups',
  USER_SETTINGS: 'user_settings',
  USER_INVITATIONS: 'user_invitations',

  // Exhibition Management Module
  EXHIBITIONS: 'exhibitions',
  EXHIBITION_EVENTS: 'exhibition_events',
  EXHIBITION_TASKS: 'exhibition_tasks',

  // Lead Management Module
  LEAD_CONTACTS: 'lead_contacts',
  LEAD_SEGMENTS: 'lead_segments',
  LEAD_COMMUNICATIONS: 'lead_communications',

  // Tier 1: Financial Collections
  // REFACTORED: Consolidated financial collection
  FINANCIALS: 'financials', // NEW: Unified collection for budgets, expenses, purchase_requests, purchase_orders

  // Financial Configuration Collections
  FINANCIAL_CATEGORY_DEFINITIONS: 'financial_category_definitions',
  FUNDING_SOURCE_DEFINITIONS: 'funding_source_definitions',
  EVENT_TYPE_DEFINITIONS: 'event_type_definitions',
  CUSTOM_FIELD_DEFINITIONS: 'custom_field_definitions',

  // DEPRECATED: Legacy financial collections (kept for migration compatibility)
  BUDGET_ALLOCATIONS: 'budget_allocations',
  EXPENSE_RECORDS: 'expense_records',
  PURCHASE_REQUESTS: 'purchase_requests',
  PURCHASE_ORDERS: 'purchase_orders',
  PURCHASE_INVOICES: 'purchase_invoices',
  VENDOR_PROFILES: 'vendor_profiles',
  VENDOR_CONTRACTS: 'vendor_contracts',
  VENDOR_REVIEWS: 'vendor_reviews',

  // Tier 2: Communication Collections
  EMAIL_CAMPAIGNS: 'email_campaigns',
  EMAIL_TEMPLATES: 'email_templates',
  EMAIL_SEQUENCES: 'email_sequences',
  EMAIL_RECIPIENTS: 'email_recipients',
  EMAIL_LISTS: 'email_lists',
  EMAIL_ANALYTICS: 'email_analytics',
  EMAIL_QUEUE: 'email_queue',
  EMAIL_QUOTAS: 'email_quotas',
  TENANT_EMAIL_CONFIG: 'tenant_email_config',
  SOCIAL_POSTS: 'social_posts',
  SOCIAL_CAMPAIGNS: 'social_campaigns',
  NOTIFICATION_SETTINGS: 'notification_settings',
  NOTIFICATION_HISTORY: 'notification_history',
  ACKNOWLEDGMENTS: 'acknowledgments',
  MARKETING_MATERIALS: 'marketing_materials',
  DESIGN_PROJECTS: 'design_projects',

  // Tier 2: Logistics Collections
  TRAVEL_BOOKINGS: 'travel_bookings',
  TRAVEL_ITINERARIES: 'travel_itineraries',
  SHIPMENT_TRACKING: 'shipment_tracking',
  SHIPMENT_DOCUMENTS: 'shipment_documents',
  BOOTH_LAYOUTS: 'booth_layouts',
  BOOTH_MEETINGS: 'booth_meetings',
  BOOTH_ANALYTICS: 'booth_analytics',

  // Tier 3: System Collections (GLOBAL - NO tenantId required)
  AUDIT_LOGS: 'audit_logs',
  SECURITY_EVENTS: 'security_events',
  USER_BIOMETRIC_DATA: 'user_biometric_data',
  BLOCKCHAIN_TRANSACTIONS: 'blockchain_transactions',
  THREAT_PATTERNS: 'threat_patterns',
  SYSTEM_METRICS: 'system_metrics',
  PERFORMANCE_METRICS: 'performance_metrics',
  SYSTEM_CONFIGURATIONS: 'system_configurations',
  BACKUP_RECORDS: 'backup_records',
  INTEGRATION_LOGS: 'integration_logs',
  API_USAGE_LOGS: 'api_usage_logs',

  // Tier 3: Analytics Collections
  ANALYTICS_CONFIGS: 'analytics_configs',
  ANALYTICS_DATA: 'analytics_data',
  PERFORMANCE_METRICS: 'performance_metrics',
  USER_ACTIVITY_LOGS: 'user_activity_logs',
  BUSINESS_METRICS: 'business_metrics',
  DASHBOARD_CONFIGS: 'dashboard_configs',

  // Tier 4: Extended Collections
  ATTENDEE_PROFILES: 'attendee_profiles',
  ATTENDEE_INVITATIONS: 'attendee_invitations',
  VIP_VISITS: 'vip_visits',
  GIFT_ALLOCATIONS: 'gift_allocations',
  GIFT_ITEMS: 'gift_items',
  TRAINING_MATERIALS: 'training_materials',
  BRIEFING_PACKS: 'briefing_packs',
  MEDIA_CONTACTS: 'media_contacts',
  PRESS_KITS: 'press_kits',
  PITCHES: 'pitches',
  MEDIA_EVENTS: 'media_events',
  MEDIA_COVERAGE: 'media_coverage',
  COMPETITOR_PROFILES: 'competitor_profiles',
  COMPETITOR_EXHIBITION_PRESENCES: 'competitor_exhibition_presences',
  DEBRIEFS: 'debriefs',
  RELEASE_NOTES: 'release_notes',
  SUPPORT_TICKETS: 'support_tickets',
  BILLING_ALERTS: 'billing_alerts',
  SUCCESS_SCORECARDS: 'success_scorecards',
  INSIGHTS: 'insights',
  NOTIFICATIONS: 'notifications',
  DELEGATIONS: 'delegations',
  SHIPMENTS: 'shipments',
  FUNDING_SOURCE_DEFINITIONS: 'funding_source_definitions',
  FINANCIAL_CATEGORY_DEFINITIONS: 'financial_category_definitions',
  DESIGN_PROJECTS: 'design_projects',
  MEDIA_ALBUMS: 'media_albums',
  MEDIA_ITEMS: 'media_items',
  POST_SHOW_HUBS: 'post_show_hubs',
  SECURE_ASSETS: 'secure_assets',
  RESTRICTED_ZONES: 'restricted_zones',
  ASSET_CUSTODY_LOGS: 'asset_custody_logs',
  REPORT_CONFIGURATIONS: 'report_configurations',
  REPORT_INSTANCES: 'report_instances',
  SMM_SETTINGS: 'smm_settings',
  TEMPORARY_STAFF: 'temporary_staff',
  CONTACT_SEGMENTS: 'contact_segments',
  EXHIBITION_TEMPLATES: 'exhibition_templates',
  LEGACY_COMPLIANCE_FRAMEWORKS: 'legacy_compliance_frameworks',
  SOCIAL_PERFORMANCE_METRICS: 'social_performance_metrics',
  SOCIAL_BENCHMARKS: 'social_benchmarks',
  RESPONSE_TEMPLATES: 'response_templates',
  MONITORING_STREAMS: 'monitoring_streams',
  EXHIBITION_CHAT_CHANNELS: 'exhibition_chat_channels',
  EXHIBITION_CHAT_MESSAGES: 'exhibition_chat_messages',
  EXHIBITION_WHITEBOARDS: 'exhibition_whiteboards',
  EXHIBITION_DOCUMENTS: 'exhibition_documents',
  EXHIBITION_VIDEO_CONFERENCES: 'exhibition_video_conferences',
  USER_PRESENCE: 'user_presence',
  EXHIBITION_COLLABORATION_SESSIONS: 'exhibition_collaboration_sessions',
  RESOURCE_ALLOCATIONS: 'resource_allocations',
  VENDOR_MATCHING_REQUESTS: 'vendor_matching_requests',
  SMART_SCHEDULE_ITEMS: 'smart_schedule_items',
  PREDICTIVE_MAINTENANCE_TASKS: 'predictive_maintenance_tasks',
  PORTFOLIO_ANALYTICS: 'portfolio_analytics',
  STRATEGIC_PLANS: 'strategic_plans',
  EVENT_WORKFLOWS: 'event_workflows',
  EVENT_SCHEDULE_OPTIMIZATIONS: 'event_schedule_optimizations',
  EVENT_RESOURCE_ALLOCATIONS: 'event_resource_allocations',
  EVENT_ATTENDANCE_RECORDS: 'event_attendance_records',
  EVENT_SENTIMENT_DATA: 'event_sentiment_data',
  EVENT_POLLS: 'event_polls',
  EMERGENCY_ALERTS: 'emergency_alerts',
  EVENT_SERIES: 'event_series',
  EVENT_PORTFOLIO_ANALYTICS: 'event_portfolio_analytics',
  STRATEGIC_INSIGHTS: 'strategic_insights',
  BOOTH_ATTENDANCE_RECORDS: 'booth_attendance_records',
  BUSINESS_TRIP_STATUS: 'business_trip_status',
  ATTENDANCE_ALERTS: 'attendance_alerts',
  APPROVAL_DOCUMENTS: 'approval_documents',
  DOCUMENT_SIGNATURES: 'document_signatures',
  SIGNING_REQUESTS: 'signing_requests',
  COMPLIANCE_FRAMEWORKS: 'compliance_frameworks',

  // Tenant Management
  TENANTS: 'tenants',
  TENANT_CONFIGURATIONS: 'tenants',

  // Persona & Permission Management
  TENANT_PERSONAS: 'tenant_personas',
  PERSONA_ASSIGNMENTS: 'persona_assignments',
  USER_PERMISSIONS: 'user_permissions',
  PERMISSION_OVERRIDES: 'permission_overrides',

  // Subscription Management
  TENANT_SUBSCRIPTIONS: 'tenant_subscriptions',
  SUBSCRIPTION_USAGE: 'subscription_usage',
  SUBSCRIPTION_PLANS: 'subscription_plans'
} as const;

/**
 * EVEXA Tenant ID for development
 */
export const EVEXA_TENANT_ID = 'evexa_dev_tenant';

/**
 * Collection name validation
 */
export function isValidCollectionName(name: string): boolean {
  return Object.values(COLLECTIONS).includes(name as any);
}

/**
 * Get all collection names as array
 */
export function getAllCollectionNames(): string[] {
  return Object.values(COLLECTIONS);
}

/**
 * Get collection name by key
 */
export function getCollectionName(key: keyof typeof COLLECTIONS): string {
  return COLLECTIONS[key];
}

/**
 * TENANT ISOLATION CATEGORIES
 * Defines which collections require tenantId and which are global
 */

// Collections that REQUIRE tenantId (tenant-isolated)
export const TENANT_ISOLATED_COLLECTIONS = [
  // Core Business Collections (MUST have tenantId)
  COLLECTIONS.USER_PROFILES,
  COLLECTIONS.USER_GROUPS,
  COLLECTIONS.USER_SETTINGS,
  COLLECTIONS.USER_INVITATIONS,
  COLLECTIONS.EXHIBITIONS,
  COLLECTIONS.EXHIBITION_EVENTS,
  COLLECTIONS.EXHIBITION_TASKS,
  COLLECTIONS.LEAD_CONTACTS,
  COLLECTIONS.LEAD_SEGMENTS,
  COLLECTIONS.LEAD_COMMUNICATIONS,
  COLLECTIONS.FINANCIALS,
  COLLECTIONS.FINANCIAL_CATEGORY_DEFINITIONS,
  COLLECTIONS.FUNDING_SOURCE_DEFINITIONS,
  COLLECTIONS.EVENT_TYPE_DEFINITIONS,
  COLLECTIONS.CUSTOM_FIELD_DEFINITIONS,
  COLLECTIONS.BUDGET_ALLOCATIONS,
  COLLECTIONS.EXPENSE_RECORDS,
  COLLECTIONS.PURCHASE_REQUESTS,
  COLLECTIONS.PURCHASE_ORDERS,
  COLLECTIONS.PURCHASE_INVOICES,
  COLLECTIONS.VENDOR_PROFILES,
  COLLECTIONS.VENDOR_CONTRACTS,
  COLLECTIONS.VENDOR_REVIEWS,
  COLLECTIONS.EMAIL_CAMPAIGNS,
  COLLECTIONS.EMAIL_TEMPLATES,
  COLLECTIONS.EMAIL_SEQUENCES,
  COLLECTIONS.EMAIL_RECIPIENTS,
  COLLECTIONS.SOCIAL_POSTS,
  COLLECTIONS.SOCIAL_CAMPAIGNS,
  COLLECTIONS.SOCIAL_ANALYTICS,
  COLLECTIONS.NOTIFICATIONS,
  COLLECTIONS.NOTIFICATION_PREFERENCES,
  COLLECTIONS.MARKETING_MATERIALS,
  COLLECTIONS.MARKETING_CAMPAIGNS,
  COLLECTIONS.TRAVEL_BOOKINGS,
  COLLECTIONS.TRAVEL_ITINERARIES,
  COLLECTIONS.SHIPMENT_TRACKING,
  COLLECTIONS.SHIPMENT_DOCUMENTS,
  COLLECTIONS.BOOTH_LAYOUTS,
  COLLECTIONS.BOOTH_MEETINGS,
  COLLECTIONS.BOOTH_ANALYTICS,

  // Analytics Collections (tenant-specific)
  COLLECTIONS.ANALYTICS_CONFIGS,
  COLLECTIONS.ANALYTICS_DATA,
  COLLECTIONS.USER_ACTIVITY_LOGS,
  COLLECTIONS.BUSINESS_METRICS,
  COLLECTIONS.DASHBOARD_CONFIGS
];

// Collections that are GLOBAL (no tenantId required)
export const GLOBAL_COLLECTIONS = [
  // System Collections (GLOBAL by design)
  COLLECTIONS.AUDIT_LOGS,
  COLLECTIONS.SECURITY_EVENTS,
  COLLECTIONS.USER_BIOMETRIC_DATA,
  COLLECTIONS.BLOCKCHAIN_TRANSACTIONS,
  COLLECTIONS.THREAT_PATTERNS,
  COLLECTIONS.SYSTEM_METRICS,
  COLLECTIONS.PERFORMANCE_METRICS,
  COLLECTIONS.SYSTEM_CONFIGURATIONS,
  COLLECTIONS.BACKUP_RECORDS,
  COLLECTIONS.INTEGRATION_LOGS,
  COLLECTIONS.API_USAGE_LOGS,

  // Subscription & Billing (GLOBAL by design)
  COLLECTIONS.SUBSCRIPTION_PLANS,
  COLLECTIONS.SUBSCRIPTION_FEATURES,
  COLLECTIONS.BILLING_RECORDS,
  COLLECTIONS.PAYMENT_METHODS,
  COLLECTIONS.INVOICES,
  COLLECTIONS.USAGE_METRICS,

  // System-wide configurations
  COLLECTIONS.RELEASE_NOTES,
  COLLECTIONS.SUPPORT_TICKETS,
  COLLECTIONS.FEATURE_FLAGS,
  COLLECTIONS.SYSTEM_ANNOUNCEMENTS
];

// Validation functions
export function requiresTenantId(collectionName: string): boolean {
  return TENANT_ISOLATED_COLLECTIONS.includes(collectionName);
}

export function isGlobalCollection(collectionName: string): boolean {
  return GLOBAL_COLLECTIONS.includes(collectionName);
}
