/**
 * EVEXA Performance Utilities
 * Comprehensive performance optimization utilities for React components
 */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useDebounce } from 'use-debounce';
import { useInView } from 'react-intersection-observer';

// Performance monitoring utilities
export const performanceUtils = {
  // Measure component render time
  measureRender: (componentName: string) => {
    const start = performance.now();
    return () => {
      const end = performance.now();
      console.log(`${componentName} render time: ${end - start}ms`);
    };
  },

  // Measure async operation time
  measureAsync: async <T>(operation: () => Promise<T>, operationName: string): Promise<T> => {
    const start = performance.now();
    try {
      const result = await operation();
      const end = performance.now();
      console.log(`${operationName} completed in: ${end - start}ms`);
      return result;
    } catch (error) {
      const end = performance.now();
      console.error(`${operationName} failed after: ${end - start}ms`, error);
      throw error;
    }
  },

  // Memory usage monitoring
  getMemoryUsage: () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1048576 * 100) / 100,
        total: Math.round(memory.totalJSHeapSize / 1048576 * 100) / 100,
        limit: Math.round(memory.jsHeapSizeLimit / 1048576 * 100) / 100,
      };
    }
    return null;
  },

  // Bundle size analysis
  analyzeBundleSize: () => {
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
    
    return {
      scriptCount: scripts.length,
      styleCount: styles.length,
      totalResources: scripts.length + styles.length,
    };
  }
};

// Enhanced debounce hook with immediate option
export function useEnhancedDebounce<T>(
  value: T,
  delay: number,
  options: { leading?: boolean; trailing?: boolean; maxWait?: number } = {}
) {
  const [debouncedValue] = useDebounce(value, delay, options);
  return debouncedValue;
}

// Intersection observer hook for lazy loading
export function useLazyLoad(options: IntersectionObserverInit = {}) {
  const { ref, inView, entry } = useInView({
    threshold: 0.1,
    triggerOnce: true,
    ...options,
  });

  return { ref, inView, entry };
}

// Virtual scrolling utilities
export const virtualScrollUtils = {
  // Calculate visible range for virtual scrolling
  calculateVisibleRange: (
    scrollTop: number,
    containerHeight: number,
    itemHeight: number,
    totalItems: number,
    overscan: number = 5
  ) => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      totalItems - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );
    return { startIndex, endIndex };
  },

  // Get item offset for virtual scrolling
  getItemOffset: (index: number, itemHeight: number) => index * itemHeight,

  // Calculate total height for virtual scrolling
  getTotalHeight: (itemCount: number, itemHeight: number) => itemCount * itemHeight,
};

// Memoization utilities
export function useStableMemo<T>(factory: () => T, deps: React.DependencyList): T {
  const ref = useRef<{ deps: React.DependencyList; value: T }>();
  
  if (!ref.current || !areEqual(ref.current.deps, deps)) {
    ref.current = { deps, value: factory() };
  }
  
  return ref.current.value;
}

// Deep equality check for dependencies
function areEqual(a: React.DependencyList, b: React.DependencyList): boolean {
  if (a.length !== b.length) return false;
  return a.every((item, index) => Object.is(item, b[index]));
}

// Performance-optimized event handlers
export function useOptimizedCallback<T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList,
  delay: number = 0
): T {
  const debouncedCallback = useCallback(
    delay > 0 
      ? debounce(callback, delay)
      : callback,
    deps
  );
  
  return debouncedCallback as T;
}

// Simple debounce implementation
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Image lazy loading hook
export function useImageLazyLoad(src: string, placeholder?: string) {
  const [imageSrc, setImageSrc] = useState(placeholder || '');
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const { ref, inView } = useLazyLoad();

  useEffect(() => {
    if (inView && src) {
      const img = new Image();
      img.onload = () => {
        setImageSrc(src);
        setIsLoaded(true);
      };
      img.onerror = () => {
        setIsError(true);
      };
      img.src = src;
    }
  }, [inView, src]);

  return { ref, imageSrc, isLoaded, isError };
}

// Component performance monitoring hook
export function usePerformanceMonitor(componentName: string) {
  const renderCount = useRef(0);
  const startTime = useRef(performance.now());

  useEffect(() => {
    renderCount.current += 1;
    const endTime = performance.now();
    const renderTime = endTime - startTime.current;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`${componentName} - Render #${renderCount.current} - Time: ${renderTime.toFixed(2)}ms`);
    }
    
    startTime.current = performance.now();
  });

  return { renderCount: renderCount.current };
}

// Batch state updates for better performance
export function useBatchedState<T>(initialState: T) {
  const [state, setState] = useState(initialState);
  const batchedUpdates = useRef<Partial<T>[]>([]);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const batchUpdate = useCallback((update: Partial<T>) => {
    batchedUpdates.current.push(update);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      setState(prevState => {
        const newState = { ...prevState };
        batchedUpdates.current.forEach(update => {
          Object.assign(newState, update);
        });
        batchedUpdates.current = [];
        return newState;
      });
    }, 0);
  }, []);

  return [state, batchUpdate] as const;
}

// Resource preloading utilities
export const preloadUtils = {
  // Preload images
  preloadImages: (urls: string[]) => {
    urls.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = url;
      document.head.appendChild(link);
    });
  },

  // Preload scripts
  preloadScripts: (urls: string[]) => {
    urls.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'script';
      link.href = url;
      document.head.appendChild(link);
    });
  },

  // Preload stylesheets
  preloadStyles: (urls: string[]) => {
    urls.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'style';
      link.href = url;
      document.head.appendChild(link);
    });
  },
};

// Web Vitals monitoring
export function useWebVitals() {
  const [vitals, setVitals] = useState<{
    FCP?: number;
    LCP?: number;
    FID?: number;
    CLS?: number;
    TTFB?: number;
  }>({});

  useEffect(() => {
    // This would integrate with web-vitals library if installed
    // For now, we'll use basic performance API
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'largest-contentful-paint') {
          setVitals(prev => ({ ...prev, LCP: entry.startTime }));
        }
        if (entry.entryType === 'first-input') {
          setVitals(prev => ({ ...prev, FID: (entry as any).processingStart - entry.startTime }));
        }
      }
    });

    observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input'] });

    return () => observer.disconnect();
  }, []);

  return vitals;
}

export default performanceUtils;
