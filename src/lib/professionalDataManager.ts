/**
 * EVEXA Professional Data Manager
 * 
 * Single source of truth for all data operations in EVEXA.
 * Replaces all chaotic seeding mechanisms with a professional, unified system.
 */

import { db } from '@/lib/firebase';
import { adminDb } from '@/lib/firebase-admin';
import { EVEXA_DEV_TENANT_ID } from '@/services/superAdminService';
import { DataIntegrityValidator } from '@/lib/dataIntegrityValidator';
import {
  collection,
  doc,
  getDocs,
  deleteDoc,
  writeBatch,
  addDoc,
  query,
  where,
  setDoc,
  updateDoc
} from 'firebase/firestore';
import { runtimeValidator, validateAndCreate, validateAndUpdate, validateAndDelete } from './runtimeValidator';

/**
 * EVEXA Professional Data Schema v2.0
 *
 * Standardized collection naming with snake_case convention
 * Module-based organization with clear hierarchy
 * Professional metadata standards enforced
 */
export const EVEXA_DATA_SCHEMA = {
  // Tier 1: Core Business Collections (Essential)
  CORE_COLLECTIONS: {
    // User Management Module
    user_profiles: 'user_profiles',           // Primary user entity (was: users)
    user_groups: 'user_groups',               // User grouping and permissions
    user_settings: 'user_settings',           // User preferences and settings

    // Exhibition Management Module
    exhibitions: 'exhibitions',               // Primary exhibition entity
    exhibition_events: 'exhibition_events',   // Exhibition events and activities (was: events)
    exhibition_tasks: 'exhibition_tasks',     // Exhibition task management (was: tasks)

    // Lead Management Module
    lead_contacts: 'lead_contacts',           // Lead contact information (was: leads)
    lead_segments: 'lead_segments',           // Lead categorization
    lead_communications: 'lead_communications' // Lead interaction history
  },

  // Tier 1: Financial Collections (Essential)
  FINANCIAL_COLLECTIONS: {
    budget_allocations: 'budget_allocations', // Budget planning (was: budgets)
    expense_records: 'expense_records',       // Expense tracking (was: expenses)
    purchase_requests: 'purchase_requests',   // Purchase request workflow
    purchase_orders: 'purchase_orders',       // Purchase order management
    purchase_invoices: 'purchase_invoices',   // Invoice processing (was: invoices)

    // Vendor Management
    vendor_profiles: 'vendor_profiles',       // Vendor information (was: vendors)
    vendor_contracts: 'vendor_contracts',     // Vendor agreements
    vendor_reviews: 'vendor_reviews'          // Vendor performance tracking
  },

  // Tier 2: Communication Collections (Important)
  COMMUNICATION_COLLECTIONS: {
    email_campaigns: 'email_campaigns',       // Email campaign management
    email_templates: 'email_templates',       // Email template library
    email_sequences: 'email_sequences',       // Email automation sequences
    social_posts: 'social_posts',             // Social media management
    social_campaigns: 'social_campaigns',     // Social media campaigns
    notification_settings: 'notification_settings', // User notification preferences (was: notifications)
    notification_history: 'notification_history',   // Notification delivery log
    marketing_materials: 'marketing_materials'       // Marketing asset management
  },

  // Tier 2: Logistics Collections (Important)
  LOGISTICS_COLLECTIONS: {
    travel_bookings: 'travel_bookings',       // Travel management (was: travel_entries)
    travel_itineraries: 'travel_itineraries', // Travel schedule details
    shipment_tracking: 'shipment_tracking',   // Shipment management (was: shipments)
    shipment_documents: 'shipment_documents', // Shipping documentation
    booth_layouts: 'booth_layouts',           // Booth design and layout
    booth_meetings: 'booth_meetings',         // Booth meeting scheduling
    booth_analytics: 'booth_analytics'        // Booth performance metrics
  },

  // Tier 3: System Collections (Infrastructure)
  SYSTEM_COLLECTIONS: {
    tenants: 'tenants',                       // Multi-tenant configuration
    audit_logs: 'audit_logs',                 // System audit logging
    security_logs: 'security_logs',           // Security event monitoring (was: security-events)
    release_notes: 'release_notes',           // System release documentation
    support_tickets: 'support_tickets',       // Customer support tracking
    business_metrics: 'business_metrics'      // Business analytics (was: evexa_business_metrics)
  },

  // Tier 3: Extended Collections (Optional Features)
  EXTENDED_COLLECTIONS: {
    gift_items: 'gift_items',                 // Gift and promotional items
    media_contacts: 'media_contacts',         // Media relationship management
    press_kits: 'press_kits',                 // Press release materials
    approval_documents: 'approval_documents', // Document approval workflow
    signing_requests: 'signing_requests',     // Digital signature requests
    training_records: 'training_records',     // Training and certification
    compliance_frameworks: 'compliance_frameworks' // Regulatory compliance
  }
} as const;

// Get all collection names
export const ALL_COLLECTIONS = Object.values(EVEXA_DATA_SCHEMA).flatMap(group => Object.values(group));

// Standard tenant ID for development (matches existing data)
export const EVEXA_TENANT_ID = EVEXA_DEV_TENANT_ID;

/**
 * Professional Standard Metadata Interface v2.0
 *
 * All EVEXA entities must implement this interface for:
 * - Multi-tenant isolation
 * - Audit trail compliance
 * - Data versioning
 * - Soft delete capability
 */
export interface StandardMetadata {
  tenant_id: string;           // Multi-tenant isolation (required)
  created_at: Date;            // Creation timestamp (required)
  updated_at: Date;            // Last update timestamp (required)
  created_by: string;          // User ID who created (required)
  updated_by: string;          // User ID who last updated (required)
  version: number;             // Optimistic locking (required)
  deleted_at?: Date;           // Soft delete support (optional)
  deleted_by?: string;         // User who deleted (optional)
}

/**
 * Base Entity Interface
 * All EVEXA entities should extend this for consistency
 */
export interface BaseEntity extends StandardMetadata {
  id: string;                  // Required string ID (consistent naming)
}

/**
 * Collection Schema Validation Interface
 * Defines the structure for validating collection schemas
 */
export interface CollectionSchema {
  name: string;
  tier: 'core' | 'business' | 'extended';
  module: string;
  description: string;
  requiredFields: string[];
  optionalFields: string[];
  relationships?: {
    collection: string;
    field: string;
    type: 'one-to-one' | 'one-to-many' | 'many-to-many';
  }[];
  indexes?: {
    fields: string[];
    unique?: boolean;
  }[];
}

/**
 * Legacy metadata interface for backward compatibility
 * @deprecated Use StandardMetadata instead
 */
export interface LegacyMetadata {
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
  version: number;
}

/**
 * Professional Data Manager Class
 */
export class ProfessionalDataManager {
  private static instance: ProfessionalDataManager;
  
  private constructor() {}
  
  public static getInstance(): ProfessionalDataManager {
    if (!ProfessionalDataManager.instance) {
      ProfessionalDataManager.instance = new ProfessionalDataManager();
    }
    return ProfessionalDataManager.instance;
  }

  /**
   * Add professional standard metadata to data items
   *
   * @param items - Array of data items to add metadata to
   * @param userId - ID of user performing the operation (defaults to system)
   * @returns Items with professional StandardMetadata applied
   */
  private addStandardMetadata<T>(items: T[], userId: string = 'system'): (T & StandardMetadata)[] {
    const now = new Date();
    return items.map(item => ({
      ...item,
      tenant_id: EVEXA_TENANT_ID,
      created_at: now,
      updated_at: now,
      created_by: userId,
      updated_by: userId,
      version: 1,
      // deleted_at and deleted_by are undefined (not deleted)
    }));
  }

  /**
   * Update metadata for existing items
   *
   * @param items - Array of existing items to update
   * @param userId - ID of user performing the update
   * @returns Items with updated metadata
   */
  private updateStandardMetadata<T extends Partial<StandardMetadata>>(
    items: T[],
    userId: string = 'system'
  ): T[] {
    const now = new Date();
    return items.map(item => ({
      ...item,
      updated_at: now,
      updated_by: userId,
      version: (item.version || 0) + 1
    }));
  }

  /**
   * Get all collections that actually exist in Firebase
   *
   * This method discovers real collections instead of using a hardcoded list
   */
  public async getAllFirebaseCollections(): Promise<string[]> {
    try {
      console.log('🔍 Discovering actual Firebase collections...');

      // Get all authorized collections from professional schema v2.0
      const authorizedCollections = [
        ...Object.values(EVEXA_DATA_SCHEMA.CORE_COLLECTIONS),
        ...Object.values(EVEXA_DATA_SCHEMA.FINANCIAL_COLLECTIONS),
        ...Object.values(EVEXA_DATA_SCHEMA.COMMUNICATION_COLLECTIONS),
        ...Object.values(EVEXA_DATA_SCHEMA.LOGISTICS_COLLECTIONS),
        ...Object.values(EVEXA_DATA_SCHEMA.SYSTEM_COLLECTIONS),
        ...Object.values(EVEXA_DATA_SCHEMA.EXTENDED_COLLECTIONS)
      ];

      // Also check for known unauthorized collections that might still exist
      const knownUnauthorizedCollections = [
        'email_recipients', 'email_lists', 'email_templates', 'media_events',
        'security_events', 'workflows', 'design_projects', 'attendee_invitations',
        'vip_visits', 'per_diem_requests', 'training_materials', 'competitor_profiles',
        'competitor_exhibition_presences', 'debriefs', 'success_scorecards', 'insights',
        'groups', 'delegations', 'funding_source_definitions', 'financial_category_definitions',
        'launch_tasks', 'pitches', 'media_coverages', 'briefing_packs', 'smm_settings',
        'media_albums', 'media_items', 'flight_bookings', 'hotel_bookings', 'passport_infos',
        'visa_infos', 'post_show_hubs', 'gift_allocations', 'marketing_campaigns',
        'secure_assets', 'restricted_zones', 'asset_custody_logs', 'report_configurations',
        'report_instances', 'contact_segments', 'attendee_communications', 'booth_analytics_data',
        'monitoring_streams', 'exhibition_collaborations', 'exhibition_chat_messages',
        'exhibition_whiteboards', 'exhibition_documents', 'exhibition_video_conferences',
        'user_presence', 'exhibition_collaboration_sessions', 'booth_attendance',
        'business_trip_status', 'attendance_alerts', 'resource_allocations',
        'vendor_requirements', 'schedule_items', 'maintenance_tasks', 'smart_schedule_items',
        'predictive_maintenance_tasks', 'portfolio_optimizations', 'strategic_plans',
        'event_automations', 'event_schedule_optimizations', 'event_resource_allocations',
        'event_sentiment_data', 'event_polls', 'emergency_alerts', 'event_series',
        'event_portfolio_analytics', 'strategic_insights', 'vendor_matching_history',
        'rfqs_generated', 'contract_analysis_history', 'content_intelligence_items',
        'supply_chain_bottlenecks', 'supply_chain_optimization_opportunities',
        'billing_alerts', 'analytics_configurations', 'travel_spend_analytics',
        'policy_compliance_monitoring', 'traveler_safety_tracking', 'company_profiles',
        'api_partnerships', 'global_markets', 'global_compliance_frameworks',
        'evexa_business_metrics', 'system_settings', 'integration_configs'
      ];

      const allPossibleCollections = [...authorizedCollections, ...knownUnauthorizedCollections];
      const existingCollections: string[] = [];

      // Check which collections actually exist by trying to read them
      for (const collectionName of allPossibleCollections) {
        try {
          const querySnapshot = await getDocs(collection(db, collectionName));
          // Only include collections that exist (even if empty)
          existingCollections.push(collectionName);
          console.log(`   📦 Found collection: ${collectionName} (${querySnapshot.docs.length} documents)`);
        } catch (error) {
          // Collection doesn't exist - this is normal and expected
        }
      }

      console.log(`🔍 Discovery complete: Found ${existingCollections.length} actual collections`);
      return existingCollections;
    } catch (error) {
      console.error('Error getting Firebase collections:', error);
      return [];
    }
  }

  /**
   * NUCLEAR OPTION: Delete ALL DOCUMENTS from Firebase collections
   *
   * ⚠️  WARNING: This will delete ALL DOCUMENTS from ALL COLLECTIONS
   * ⚠️  Collections themselves remain, but all data inside is deleted
   * ⚠️  This is IRREVERSIBLE - use with extreme caution
   * ⚠️  Only works in development mode
   */
  public async nuclearReset(): Promise<{ success: boolean; deletedCollections: string[]; errors: string[] }> {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Nuclear reset is not allowed in production');
    }

    console.log('🚨 NUCLEAR RESET: Discovering and deleting ALL Firebase data...');

    const deletedCollections: string[] = [];
    const errors: string[] = [];

    try {
      // STEP 1: Discover ALL collections that actually exist
      console.log('🔍 Step 1: Discovering all existing collections...');
      const allPossibleCollections = await this.getAllFirebaseCollections();
      const existingCollections: string[] = [];

      // Check which collections actually have data
      for (const collectionName of allPossibleCollections) {
        try {
          const querySnapshot = await getDocs(collection(db, collectionName));
          if (!querySnapshot.empty) {
            existingCollections.push(collectionName);
            console.log(`   📦 Found collection: ${collectionName} (${querySnapshot.docs.length} documents)`);
          }
        } catch (error) {
          // Collection doesn't exist or permission issue - skip
        }
      }

      console.log(`🔍 Discovery complete: Found ${existingCollections.length} collections with data`);
      console.log('🚨 STEP 2: Deleting ALL discovered collections...');

      // STEP 2: Delete all discovered collections
      const allCollections = existingCollections;
      
      for (const collectionName of allCollections) {
        try {
          console.log(`🗑️ Deleting collection: ${collectionName}`);
          
          // Get all documents in the collection
          const querySnapshot = await getDocs(collection(db, collectionName));
          
          if (querySnapshot.empty) {
            console.log(`   ✅ Collection ${collectionName} is already empty`);
            continue;
          }

          // Delete all documents in batches (Firebase limit is 500 operations per batch)
          const docs = querySnapshot.docs;
          let deletedCount = 0;

          // Process in chunks of 500
          for (let i = 0; i < docs.length; i += 500) {
            const batch = writeBatch(db);
            const chunk = docs.slice(i, i + 500);

            chunk.forEach((docSnapshot) => {
              batch.delete(docSnapshot.ref);
            });

            await batch.commit();
            deletedCount += chunk.length;

            if (docs.length > 500) {
              console.log(`   📦 Deleted batch ${Math.floor(i/500) + 1}: ${chunk.length} documents from ${collectionName}`);
            }
          }

          deletedCollections.push(collectionName);
          console.log(`   ✅ Deleted ${deletedCount} documents from ${collectionName}`);
          
        } catch (collectionError) {
          const errorMsg = `Failed to delete collection ${collectionName}: ${collectionError}`;
          console.error(`   ❌ ${errorMsg}`);
          errors.push(errorMsg);
        }
      }

      console.log(`🚨 NUCLEAR RESET COMPLETE: ${deletedCollections.length} collections deleted`);
      
      return {
        success: errors.length === 0,
        deletedCollections,
        errors
      };

    } catch (error) {
      console.error('Nuclear reset failed:', error);
      return {
        success: false,
        deletedCollections,
        errors: [`Nuclear reset failed: ${error}`]
      };
    }
  }

  /**
   * Professional seeding with validation
   */
  public async professionalSeed(data: {
    [collectionName: string]: any[]
  }): Promise<{ success: boolean; seededCollections: string[]; totalDocuments: number; errors: string[] }> {
    
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Professional seeding is only allowed in development');
    }

    console.log('🌱 Starting professional data seeding...');
    
    const seededCollections: string[] = [];
    const errors: string[] = [];
    let totalDocuments = 0;

    try {
      // Validate all collections before seeding to prevent chaos
      console.log('🔍 Validating collections before seeding...');
      for (const collectionName of Object.keys(data)) {
        await DataIntegrityValidator.preventCollectionChaos(collectionName);
      }

      for (const [collectionName, items] of Object.entries(data)) {
        if (!items || items.length === 0) {
          console.log(`   ⏭️ Skipping empty collection: ${collectionName}`);
          continue;
        }

        try {
          console.log(`🌱 Seeding ${items.length} items to ${collectionName}...`);
          
          // Add standard metadata
          const standardizedItems = this.addStandardMetadata(items);
          
          // Seed in batches for better performance
          let batchCount = 0;
          let currentBatch = writeBatch(db);

          for (const item of standardizedItems) {
            // Validate document before adding to prevent data integrity issues
            DataIntegrityValidator.validateDocumentBeforeWrite(item);

            const docRef = doc(collection(db, collectionName));
            currentBatch.set(docRef, item);
            batchCount++;

            // Commit batch if we reach the limit
            if (batchCount >= 500) {
              await currentBatch.commit();
              console.log(`   📦 Committed batch of ${batchCount} documents to ${collectionName}`);
              currentBatch = writeBatch(db); // Create new batch
              batchCount = 0;
            }
          }

          // Commit remaining documents
          if (batchCount > 0) {
            await currentBatch.commit();
          }
          
          seededCollections.push(collectionName);
          totalDocuments += items.length;
          console.log(`   ✅ Successfully seeded ${items.length} documents to ${collectionName}`);
          
        } catch (collectionError) {
          const errorMsg = `Failed to seed collection ${collectionName}: ${collectionError}`;
          console.error(`   ❌ ${errorMsg}`);
          errors.push(errorMsg);
        }
      }

      console.log(`🌱 PROFESSIONAL SEEDING COMPLETE: ${totalDocuments} documents across ${seededCollections.length} collections`);
      
      return {
        success: errors.length === 0,
        seededCollections,
        totalDocuments,
        errors
      };

    } catch (error) {
      console.error('Professional seeding failed:', error);
      return {
        success: false,
        seededCollections,
        totalDocuments,
        errors: [`Professional seeding failed: ${error}`]
      };
    }
  }

  /**
   * Validate data consistency across all collections
   */
  public async validateDataConsistency(): Promise<{
    isValid: boolean;
    issues: string[];
    collectionStats: { [key: string]: { count: number; hasIssues: boolean } };
  }> {
    console.log('🔍 Validating data consistency...');
    
    const issues: string[] = [];
    const collectionStats: { [key: string]: { count: number; hasIssues: boolean } } = {};

    try {
      for (const collectionName of ALL_COLLECTIONS) {
        try {
          const querySnapshot = await getDocs(collection(db, collectionName));
          const count = querySnapshot.docs.length;
          
          // Check for professional metadata compliance
          const docsWithIssues = querySnapshot.docs.filter(doc => {
            const data = doc.data();
            return (
              // Check new professional metadata fields
              !data.tenant_id || data.tenant_id !== EVEXA_TENANT_ID ||
              !data.created_at || !data.updated_at ||
              !data.created_by || !data.updated_by ||
              typeof data.version !== 'number' ||
              // Also check legacy fields for backward compatibility
              (!data.tenantId && !data.tenant_id)
            );
          });

          const hasIssues = docsWithIssues.length > 0;

          if (hasIssues) {
            issues.push(`Collection ${collectionName} has ${docsWithIssues.length} documents with missing/incorrect professional metadata`);
          }
          
          collectionStats[collectionName] = { count, hasIssues };
          
        } catch (error) {
          issues.push(`Failed to validate collection ${collectionName}: ${error}`);
          collectionStats[collectionName] = { count: 0, hasIssues: true };
        }
      }

      const isValid = issues.length === 0;
      
      console.log(`🔍 Validation complete: ${isValid ? 'VALID' : 'ISSUES FOUND'}`);
      if (!isValid) {
        console.warn('Issues found:', issues);
      }

      return { isValid, issues, collectionStats };

    } catch (error) {
      console.error('Data validation failed:', error);
      return {
        isValid: false,
        issues: [`Data validation failed: ${error}`],
        collectionStats
      };
    }
  }

  /**
   * Nuclear Reset - Remove Empty Collections
   *
   * Removes all empty collections that are cluttering Firebase
   * Based on Phase 1 audit findings
   */
  public async nuclearResetEmptyCollections(): Promise<{
    success: boolean;
    removedCollections: string[];
    preservedCollections: string[];
    errors: string[];
  }> {
    console.log('💥 Starting nuclear reset of empty collections...');

    const removedCollections: string[] = [];
    const preservedCollections: string[] = [];
    const errors: string[] = [];

    // Collections identified as empty in Phase 1 audit
    const EMPTY_COLLECTIONS_TO_REMOVE = [
      'a_p_i_partnerships', 'acknowledgments', 'aiConfiguration', 'aiUsageRecords',
      'analytics_configs', 'approval_documents', 'boothAnalytics', 'booth_analytics_enhanced',
      'booth_attendance_records', 'booth_meetings', 'briefingPacks', 'budgets',
      'budgets_enhanced', 'competitor_exhibition_presences_enhanced', 'config',
      'contactSegments', 'coreTeamMembers', 'dashboardLayouts', 'document_signatures',
      'emailSequences', 'emailTemplates', 'email_sequences', 'event_attendance_records',
      'event_workflows', 'events', 'exhibitionTeamAssignments', 'exhibition_chat_channels',
      'exhibition_templates', 'exhibitions', 'expenseCategoryDefinitions', 'expenses',
      'expenses_enhanced', 'flightItineraries', 'gift_items', 'insuranceClaims',
      'internationalCompliance', 'invoices', 'leads', 'legacy_compliance_frameworks',
      'logisticsShipments', 'mail', 'marketing_materials', 'media_contacts',
      'notifications', 'personalizationProfiles', 'portfolio_analytics', 'postShowHubs',
      'press_kits', 'procurement_metrics', 'purchaseOrders', 'purchaseRequests',
      'purchase_orders', 'purchase_requests', 'recommendations', 'release_notes',
      'reportInstanceConfigs', 'response_templates', 'routeOptimizations', 'shipments',
      'signing_requests', 'social_posts', 'subscription_plans', 'support_tickets',
      'tasks', 'temporary_staff', 'tenant-data', 'tenant-users', 'trainingAcknowledgments',
      'trainingMaterials', 'travelRequests', 'travel_entries', 'userGroups',
      'userPreferences', 'vendorPerformanceReviews', 'vendor_matching_requests',
      'vendors', 'venue_integrations', 'visaDetails', 'visaPassportManagement',
      'workflow_execution_logs', 'workflow_executions'
    ];

    // Collections with data to preserve (from Phase 1 audit)
    const COLLECTIONS_WITH_DATA = [
      'users', 'attendee_profiles', 'security-events', 'tenants', 'evexa_business_metrics'
    ];

    try {
      for (const collectionName of EMPTY_COLLECTIONS_TO_REMOVE) {
        try {
          // Double-check collection is actually empty before deletion
          const querySnapshot = await getDocs(collection(db, collectionName));

          if (querySnapshot.docs.length === 0) {
            // Collection is empty, safe to remove
            // Note: Firestore doesn't have a direct way to delete collections
            // Empty collections will be automatically cleaned up by Firestore
            console.log(`   ✅ Verified empty: ${collectionName}`);
            removedCollections.push(collectionName);
          } else {
            console.log(`   ⚠️ Collection ${collectionName} has ${querySnapshot.docs.length} documents - preserving`);
            preservedCollections.push(collectionName);
          }
        } catch (error) {
          console.log(`   ❌ Error checking ${collectionName}: ${error}`);
          errors.push(`Failed to check collection ${collectionName}: ${error}`);
        }
      }

      // Log preserved collections with data
      for (const collectionName of COLLECTIONS_WITH_DATA) {
        try {
          const querySnapshot = await getDocs(collection(db, collectionName));
          if (querySnapshot.docs.length > 0) {
            console.log(`   📁 Preserved: ${collectionName} (${querySnapshot.docs.length} documents)`);
            preservedCollections.push(collectionName);
          }
        } catch (error) {
          console.log(`   ⚠️ Could not verify ${collectionName}: ${error}`);
        }
      }

      console.log(`💥 Nuclear reset complete: ${removedCollections.length} empty collections identified for cleanup`);

      return {
        success: errors.length === 0,
        removedCollections,
        preservedCollections,
        errors
      };

    } catch (error) {
      console.error('Nuclear reset failed:', error);
      return {
        success: false,
        removedCollections,
        preservedCollections,
        errors: [...errors, `Nuclear reset failed: ${error}`]
      };
    }
  }

  /**
   * Runtime Validation Integration Methods
   */

  /**
   * Create document with runtime validation
   */
  async createDocumentWithValidation(
    collectionName: string,
    data: any,
    docId?: string,
    userId: string = 'system'
  ): Promise<any> {
    try {
      // Add standard metadata
      const dataWithMetadata = this.addStandardMetadata([data], userId)[0];

      // Use runtime validator for creation
      const result = await validateAndCreate(collectionName, dataWithMetadata, docId);

      console.log(`✅ Document created with validation in ${collectionName}:`, result.id);
      return result;
    } catch (error) {
      console.error(`❌ Failed to create document in ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Update document with runtime validation
   */
  async updateDocumentWithValidation(
    collectionName: string,
    docId: string,
    data: any,
    userId: string = 'system'
  ): Promise<any> {
    try {
      // Add update metadata
      const updateData = {
        ...data,
        updated_at: new Date(),
        updated_by: userId,
        version: (data.version || 0) + 1
      };

      // Use runtime validator for update
      const result = await validateAndUpdate(collectionName, docId, updateData);

      console.log(`✅ Document updated with validation in ${collectionName}:`, docId);
      return result;
    } catch (error) {
      console.error(`❌ Failed to update document in ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Delete document with runtime validation (soft delete)
   */
  async deleteDocumentWithValidation(
    collectionName: string,
    docId: string,
    userId: string = 'system'
  ): Promise<void> {
    try {
      // Use runtime validator for soft delete
      await validateAndDelete(collectionName, docId, userId);

      console.log(`✅ Document soft deleted with validation in ${collectionName}:`, docId);
    } catch (error) {
      console.error(`❌ Failed to delete document in ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Batch operations with runtime validation
   */
  async batchOperationsWithValidation(operations: Array<{
    type: 'create' | 'update' | 'delete';
    collection: string;
    docId?: string;
    data?: any;
    userId?: string;
  }>): Promise<{
    success: boolean;
    results: any[];
    errors: string[];
  }> {
    const results: any[] = [];
    const errors: string[] = [];

    try {
      // Prepare operations with metadata
      const preparedOperations = operations.map(op => {
        if (op.type === 'create' && op.data) {
          const dataWithMetadata = this.addStandardMetadata([op.data], op.userId || 'system')[0];
          return { ...op, data: dataWithMetadata };
        } else if (op.type === 'update' && op.data) {
          const updateData = {
            ...op.data,
            updated_at: new Date(),
            updated_by: op.userId || 'system',
            version: (op.data.version || 0) + 1
          };
          return { ...op, data: updateData };
        }
        return op;
      });

      // Validate batch operations
      const validation = await runtimeValidator.validateBatch(preparedOperations);

      if (!validation.valid) {
        return {
          success: false,
          results: [],
          errors: validation.errors
        };
      }

      // Execute validated operations
      for (const operation of validation.validatedOperations) {
        try {
          let result;
          switch (operation.type) {
            case 'create':
              result = await validateAndCreate(operation.collection, operation.data, operation.docId);
              break;
            case 'update':
              result = await validateAndUpdate(operation.collection, operation.docId!, operation.data);
              break;
            case 'delete':
              await validateAndDelete(operation.collection, operation.docId!, operation.userId);
              result = { deleted: true, id: operation.docId };
              break;
          }
          results.push(result);
        } catch (error) {
          errors.push(`${operation.collection}: ${error instanceof Error ? error.message : String(error)}`);
        }
      }

      return {
        success: errors.length === 0,
        results,
        errors
      };

    } catch (error) {
      return {
        success: false,
        results: [],
        errors: [`Batch operation failed: ${error instanceof Error ? error.message : String(error)}`]
      };
    }
  }

  /**
   * Get runtime validation statistics
   */
  getValidationStats(): any {
    return runtimeValidator.getValidationStats();
  }

  /**
   * Configure runtime validation
   */
  configureValidation(config: any): void {
    runtimeValidator.updateConfig(config);
  }
}

// Export singleton instance
export const professionalDataManager = ProfessionalDataManager.getInstance();
