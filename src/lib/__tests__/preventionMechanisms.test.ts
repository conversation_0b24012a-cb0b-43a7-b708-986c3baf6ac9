/**
 * EVEXA Prevention Mechanisms Tests
 * 
 * Comprehensive tests to verify that prevention mechanisms
 * properly block unauthorized operations and maintain data integrity
 */

import { preventionMechanisms, validateOperation } from '../preventionMechanisms';
import { getAllCollectionNames } from '../collectionSchemas';

describe('Prevention Mechanisms', () => {
  beforeEach(() => {
    // Ensure prevention mechanisms are active for each test
    preventionMechanisms.activate();
    preventionMechanisms.clearViolations();
  });

  afterEach(() => {
    // Clean up after each test
    preventionMechanisms.clearViolations();
  });

  describe('Collection Whitelist Validation', () => {
    test('should allow authorized collections', async () => {
      const authorizedCollections = getAllCollectionNames();
      
      for (const collectionName of authorizedCollections.slice(0, 5)) { // Test first 5
        const result = await validateOperation('create', collectionName, {
          tenant_id: 'evexa-development-company',
          name: 'Test Document',
          created_at: new Date(),
          updated_at: new Date(),
          created_by: 'test-user',
          updated_by: 'test-user',
          version: 1
        });

        expect(result.allowed).toBe(true);
        expect(result.errors).toHaveLength(0);
      }
    });

    test('should block unauthorized collections', async () => {
      const unauthorizedCollections = [
        'unauthorized_collection',
        'random_data',
        'chaotic_collection',
        'legacy_stuff'
      ];

      for (const collectionName of unauthorizedCollections) {
        const result = await validateOperation('create', collectionName, {
          name: 'Test Document'
        });

        expect(result.allowed).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
        expect(result.errors[0]).toContain('not in authorized schema');
      }
    });
  });

  describe('Naming Convention Validation', () => {
    test('should block camelCase collection names', async () => {
      const camelCaseNames = [
        'userProfiles',
        'emailCampaigns',
        'exhibitionEvents',
        'mediaContacts'
      ];

      for (const collectionName of camelCaseNames) {
        const result = await validateOperation('create', collectionName, {
          name: 'Test Document'
        });

        expect(result.allowed).toBe(false);
        expect(result.errors.some(error => error.includes('camelCase not allowed'))).toBe(true);
      }
    });

    test('should block kebab-case collection names', async () => {
      const kebabCaseNames = [
        'user-profiles',
        'email-campaigns',
        'exhibition-events',
        'media-contacts'
      ];

      for (const collectionName of kebabCaseNames) {
        const result = await validateOperation('create', collectionName, {
          name: 'Test Document'
        });

        expect(result.allowed).toBe(false);
        expect(result.errors.some(error => error.includes('kebab-case not allowed'))).toBe(true);
      }
    });

    test('should allow proper snake_case collection names', async () => {
      // This test uses actual authorized collections, so it should pass naming validation
      const result = await validateOperation('create', 'user_profiles', {
        email: '<EMAIL>',
        name: 'Test User',
        role: 'user',
        status: 'active',
        tenant_id: 'evexa-development-company',
        created_at: new Date(),
        updated_at: new Date(),
        created_by: 'test-user',
        updated_by: 'test-user',
        version: 1
      });

      // Should pass naming validation (but may fail schema validation)
      const namingErrors = result.errors.filter(error => 
        error.includes('camelCase') || error.includes('kebab-case') || error.includes('snake_case')
      );
      expect(namingErrors).toHaveLength(0);
    });
  });

  describe('Schema Validation', () => {
    test('should validate required fields for user_profiles', async () => {
      const invalidData = {
        // Missing required fields: email, name, role, status
        tenant_id: 'evexa-development-company',
        created_at: new Date(),
        updated_at: new Date(),
        created_by: 'test-user',
        updated_by: 'test-user',
        version: 1
      };

      const result = await validateOperation('create', 'user_profiles', invalidData);

      expect(result.allowed).toBe(false);
      expect(result.errors.some(error => error.includes('email'))).toBe(true);
      expect(result.errors.some(error => error.includes('name'))).toBe(true);
      expect(result.errors.some(error => error.includes('role'))).toBe(true);
      expect(result.errors.some(error => error.includes('status'))).toBe(true);
    });

    test('should validate data types for user_profiles', async () => {
      const invalidData = {
        email: 'invalid-email', // Invalid email format
        name: '', // Empty name
        role: 'invalid_role', // Invalid role enum
        status: 'invalid_status', // Invalid status enum
        tenant_id: 'evexa-development-company',
        created_at: new Date(),
        updated_at: new Date(),
        created_by: 'test-user',
        updated_by: 'test-user',
        version: 1
      };

      const result = await validateOperation('create', 'user_profiles', invalidData);

      expect(result.allowed).toBe(false);
      expect(result.errors.some(error => error.includes('email'))).toBe(true);
      expect(result.errors.some(error => error.includes('name'))).toBe(true);
      expect(result.errors.some(error => error.includes('role'))).toBe(true);
      expect(result.errors.some(error => error.includes('status'))).toBe(true);
    });

    test('should accept valid user_profiles data', async () => {
      const validData = {
        email: '<EMAIL>',
        name: 'Test User',
        role: 'user',
        status: 'active',
        tenant_id: 'evexa-development-company',
        created_at: new Date(),
        updated_at: new Date(),
        created_by: 'test-user',
        updated_by: 'test-user',
        version: 1
      };

      const result = await validateOperation('create', 'user_profiles', validData);

      expect(result.allowed).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('Tenant Isolation Validation', () => {
    test('should require tenant_id field', async () => {
      const dataWithoutTenant = {
        email: '<EMAIL>',
        name: 'Test User',
        role: 'user',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date(),
        created_by: 'test-user',
        updated_by: 'test-user',
        version: 1
        // Missing tenant_id
      };

      const result = await validateOperation('create', 'user_profiles', dataWithoutTenant);

      expect(result.allowed).toBe(false);
      expect(result.errors.some(error => error.includes('tenant_id'))).toBe(true);
    });

    test('should validate tenant_id format', async () => {
      const dataWithInvalidTenant = {
        email: '<EMAIL>',
        name: 'Test User',
        role: 'user',
        status: 'active',
        tenant_id: 'x', // Too short
        created_at: new Date(),
        updated_at: new Date(),
        created_by: 'test-user',
        updated_by: 'test-user',
        version: 1
      };

      const result = await validateOperation('create', 'user_profiles', dataWithInvalidTenant);

      expect(result.allowed).toBe(false);
      expect(result.errors.some(error => error.includes('tenant_id'))).toBe(true);
    });
  });

  describe('Operation Types', () => {
    test('should validate create operations', async () => {
      const result = await validateOperation('create', 'unauthorized_collection', {});
      
      expect(result.allowed).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should validate update operations', async () => {
      const result = await validateOperation('update', 'unauthorized_collection', {});
      
      expect(result.allowed).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should validate delete operations', async () => {
      const result = await validateOperation('delete', 'unauthorized_collection');
      
      expect(result.allowed).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should validate read operations', async () => {
      const result = await validateOperation('read', 'unauthorized_collection');
      
      expect(result.allowed).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Violation Logging', () => {
    test('should log violations when operations are blocked', async () => {
      await validateOperation('create', 'unauthorized_collection', {});
      
      const stats = preventionMechanisms.getViolationStats();
      expect(stats.total).toBeGreaterThan(0);
      expect(stats.byType['unauthorized_collection']).toBeGreaterThan(0);
    });

    test('should track violation statistics', async () => {
      // Generate multiple violations
      await validateOperation('create', 'unauthorized_collection_1', {});
      await validateOperation('create', 'unauthorized_collection_2', {});
      await validateOperation('create', 'camelCaseCollection', {});
      
      const stats = preventionMechanisms.getViolationStats();
      expect(stats.total).toBe(3);
      expect(stats.byType['unauthorized_collection']).toBe(2);
      expect(stats.byType['naming_violation']).toBe(1);
    });

    test('should clear violation log', async () => {
      await validateOperation('create', 'unauthorized_collection', {});
      
      let stats = preventionMechanisms.getViolationStats();
      expect(stats.total).toBeGreaterThan(0);
      
      preventionMechanisms.clearViolations();
      
      stats = preventionMechanisms.getViolationStats();
      expect(stats.total).toBe(0);
    });
  });

  describe('Configuration Management', () => {
    test('should allow configuration updates', () => {
      const originalConfig = preventionMechanisms.getConfig();
      
      preventionMechanisms.updateConfig({
        enableCollectionWhitelist: false
      });
      
      const updatedConfig = preventionMechanisms.getConfig();
      expect(updatedConfig.enableCollectionWhitelist).toBe(false);
      expect(updatedConfig.enableSchemaValidation).toBe(originalConfig.enableSchemaValidation);
    });

    test('should respect configuration changes', async () => {
      // Disable collection whitelist
      preventionMechanisms.updateConfig({
        enableCollectionWhitelist: false,
        blockUnauthorizedOperations: false
      });
      
      const result = await validateOperation('create', 'unauthorized_collection', {});
      
      // Should be allowed now since whitelist is disabled
      expect(result.allowed).toBe(true);
    });
  });

  describe('Activation/Deactivation', () => {
    test('should track activation state', () => {
      expect(preventionMechanisms.isActivated()).toBe(true);
      
      preventionMechanisms.deactivate();
      expect(preventionMechanisms.isActivated()).toBe(false);
      
      preventionMechanisms.activate();
      expect(preventionMechanisms.isActivated()).toBe(true);
    });
  });
});
