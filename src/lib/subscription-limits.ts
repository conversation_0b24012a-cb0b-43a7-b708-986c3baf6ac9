/**
 * Subscription Limits Management
 * Handles subscription tiers, limits, and enforcement
 */

export interface SubscriptionLimits {
  users: number;
  exhibitions: number;
  events: number;
  storage: number; // in MB
  apiCalls: number; // per month
  customBranding: boolean;
  advancedAnalytics: boolean;
  whiteLabel: boolean;
  sso: boolean;
  prioritySupport: boolean;
  customIntegrations: boolean;
  bulkOperations: boolean;
  dataExport: boolean;
  auditLogs: boolean;
}

export interface SubscriptionTier {
  id: string;
  name: string;
  price: number; // monthly price in cents
  yearlyPrice?: number; // yearly price in cents
  description: string;
  features: string[];
  limits: SubscriptionLimits;
  popular?: boolean;
  enterprise?: boolean;
}

export const SUBSCRIPTION_TIERS: SubscriptionTier[] = [
  {
    id: 'basic',
    name: 'Basic',
    price: 2900, // $29/month
    yearlyPrice: 29000, // $290/year (2 months free)
    description: 'Perfect for small teams getting started with exhibition management',
    features: [
      '1 admin + unlimited users',
      'Persona-based module access',
      'Basic analytics',
      'Email support',
      'Standard integrations'
    ],
    limits: {
      users: 10,
      exhibitions: 25,
      events: 50,
      storage: 1000, // 1GB
      apiCalls: 1000,
      customBranding: false,
      advancedAnalytics: false,
      whiteLabel: false,
      sso: false,
      prioritySupport: false,
      customIntegrations: false,
      bulkOperations: true,
      dataExport: true,
      auditLogs: false
    }
  },
  {
    id: 'professional',
    name: 'Professional',
    price: 7900, // $79/month
    yearlyPrice: 79000, // $790/year (2 months free)
    description: 'Advanced features for growing exhibition teams',
    features: [
      '1 admin + 9 users',
      'Admin chooses manager/user split',
      'Advanced analytics',
      'Priority support',
      'Custom branding',
      'API access',
      'Bulk operations'
    ],
    limits: {
      users: 10,
      exhibitions: 100,
      events: 200,
      storage: 5000, // 5GB
      apiCalls: 10000,
      customBranding: true,
      advancedAnalytics: true,
      whiteLabel: false,
      sso: false,
      prioritySupport: true,
      customIntegrations: true,
      bulkOperations: true,
      dataExport: true,
      auditLogs: true
    },
    popular: true
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 19900, // $199/month
    yearlyPrice: 199000, // $1990/year (2 months free)
    description: 'Complete solution for large organizations',
    features: [
      'Unlimited users',
      'White label solution',
      'SSO integration',
      'Custom integrations',
      'Dedicated support',
      'On-premise option',
      'Advanced security'
    ],
    limits: {
      users: -1, // unlimited
      exhibitions: -1, // unlimited
      events: -1, // unlimited
      storage: -1, // unlimited
      apiCalls: -1, // unlimited
      customBranding: true,
      advancedAnalytics: true,
      whiteLabel: true,
      sso: true,
      prioritySupport: true,
      customIntegrations: true,
      bulkOperations: true,
      dataExport: true,
      auditLogs: true
    },
    enterprise: true
  }
];

export interface UsageStats {
  users: number;
  exhibitions: number;
  events: number;
  storage: number; // in MB
  apiCalls: number; // current month
}

export interface LimitCheckResult {
  allowed: boolean;
  reason?: string;
  currentUsage?: number;
  limit?: number;
  upgradeRequired?: boolean;
  suggestedTier?: string;
}

/**
 * Get subscription tier by ID
 */
export function getSubscriptionTier(tierId: string): SubscriptionTier | null {
  return SUBSCRIPTION_TIERS.find(tier => tier.id === tierId) || null;
}

/**
 * Check if a specific limit is exceeded
 */
export function checkLimit(
  limitType: keyof SubscriptionLimits,
  currentUsage: number,
  subscriptionTier: SubscriptionTier
): LimitCheckResult {
  const limit = subscriptionTier.limits[limitType];
  
  // -1 means unlimited
  if (limit === -1) {
    return { allowed: true };
  }
  
  // For boolean limits, check if feature is enabled
  if (typeof limit === 'boolean') {
    return {
      allowed: limit,
      reason: limit ? undefined : `${limitType} not available in ${subscriptionTier.name} plan`,
      upgradeRequired: !limit
    };
  }
  
  // For numeric limits, check if usage exceeds limit
  if (typeof limit === 'number') {
    const allowed = currentUsage < limit;
    return {
      allowed,
      reason: allowed ? undefined : `${limitType} limit exceeded (${currentUsage}/${limit})`,
      currentUsage,
      limit,
      upgradeRequired: !allowed,
      suggestedTier: !allowed ? getSuggestedUpgradeTier(subscriptionTier.id) : undefined
    };
  }
  
  return { allowed: true };
}

/**
 * Check multiple limits at once
 */
export function checkLimits(
  usage: Partial<UsageStats>,
  subscriptionTier: SubscriptionTier
): Record<keyof SubscriptionLimits, LimitCheckResult> {
  const results: Partial<Record<keyof SubscriptionLimits, LimitCheckResult>> = {};
  
  // Check numeric limits
  if (usage.users !== undefined) {
    results.users = checkLimit('users', usage.users, subscriptionTier);
  }
  if (usage.exhibitions !== undefined) {
    results.exhibitions = checkLimit('exhibitions', usage.exhibitions, subscriptionTier);
  }
  if (usage.events !== undefined) {
    results.events = checkLimit('events', usage.events, subscriptionTier);
  }
  if (usage.storage !== undefined) {
    results.storage = checkLimit('storage', usage.storage, subscriptionTier);
  }
  if (usage.apiCalls !== undefined) {
    results.apiCalls = checkLimit('apiCalls', usage.apiCalls, subscriptionTier);
  }
  
  // Check boolean features
  results.customBranding = checkLimit('customBranding', 0, subscriptionTier);
  results.advancedAnalytics = checkLimit('advancedAnalytics', 0, subscriptionTier);
  results.whiteLabel = checkLimit('whiteLabel', 0, subscriptionTier);
  results.sso = checkLimit('sso', 0, subscriptionTier);
  results.prioritySupport = checkLimit('prioritySupport', 0, subscriptionTier);
  results.customIntegrations = checkLimit('customIntegrations', 0, subscriptionTier);
  results.bulkOperations = checkLimit('bulkOperations', 0, subscriptionTier);
  results.dataExport = checkLimit('dataExport', 0, subscriptionTier);
  results.auditLogs = checkLimit('auditLogs', 0, subscriptionTier);
  
  return results as Record<keyof SubscriptionLimits, LimitCheckResult>;
}

/**
 * Get suggested upgrade tier
 */
export function getSuggestedUpgradeTier(currentTierId: string): string | undefined {
  const currentIndex = SUBSCRIPTION_TIERS.findIndex(tier => tier.id === currentTierId);
  if (currentIndex === -1 || currentIndex >= SUBSCRIPTION_TIERS.length - 1) {
    return undefined;
  }
  return SUBSCRIPTION_TIERS[currentIndex + 1].id;
}

/**
 * Calculate usage percentage
 */
export function getUsagePercentage(current: number, limit: number): number {
  if (limit === -1) return 0; // unlimited
  return Math.min(Math.round((current / limit) * 100), 100);
}

/**
 * Get usage status color
 */
export function getUsageStatusColor(percentage: number): string {
  if (percentage >= 90) return 'text-red-600';
  if (percentage >= 75) return 'text-orange-600';
  if (percentage >= 50) return 'text-yellow-600';
  return 'text-green-600';
}

/**
 * Check if user can perform an action
 */
export function canPerformAction(
  action: 'create_user' | 'create_exhibition' | 'create_event' | 'use_feature',
  currentUsage: UsageStats,
  subscriptionTier: SubscriptionTier,
  feature?: keyof SubscriptionLimits
): LimitCheckResult {
  switch (action) {
    case 'create_user':
      return checkLimit('users', currentUsage.users + 1, subscriptionTier);
    
    case 'create_exhibition':
      return checkLimit('exhibitions', currentUsage.exhibitions + 1, subscriptionTier);
    
    case 'create_event':
      return checkLimit('events', currentUsage.events + 1, subscriptionTier);
    
    case 'use_feature':
      if (!feature) return { allowed: false, reason: 'Feature not specified' };
      return checkLimit(feature, 0, subscriptionTier);
    
    default:
      return { allowed: false, reason: 'Unknown action' };
  }
}

/**
 * Format price for display
 */
export function formatPrice(priceInCents: number): string {
  return `$${(priceInCents / 100).toFixed(0)}`;
}

/**
 * Calculate savings for yearly billing
 */
export function calculateYearlySavings(tier: SubscriptionTier): number {
  if (!tier.yearlyPrice) return 0;
  const monthlyTotal = tier.price * 12;
  return monthlyTotal - tier.yearlyPrice;
}

/**
 * Get tier comparison data
 */
export function getTierComparison() {
  return SUBSCRIPTION_TIERS.map(tier => ({
    ...tier,
    formattedPrice: formatPrice(tier.price),
    formattedYearlyPrice: tier.yearlyPrice ? formatPrice(tier.yearlyPrice) : null,
    yearlySavings: calculateYearlySavings(tier),
    formattedYearlySavings: formatPrice(calculateYearlySavings(tier))
  }));
}
