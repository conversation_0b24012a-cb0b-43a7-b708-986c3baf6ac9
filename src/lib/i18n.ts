/**
 * Internationalization (i18n) Framework for EVEXA
 * Supports multiple languages, RTL, locale-specific formatting
 */

import { createContext, useContext } from 'react';

// Supported languages
export type SupportedLanguage = 
  | 'en' // English
  | 'es' // Spanish
  | 'fr' // French
  | 'de' // German
  | 'it' // Italian
  | 'pt' // Portuguese
  | 'ru' // Russian
  | 'zh' // Chinese (Simplified)
  | 'ja' // Japanese
  | 'ko' // Korean
  | 'ar' // Arabic
  | 'he' // Hebrew
  | 'hi' // Hindi
  | 'th' // Thai
  | 'vi' // Vietnamese
  | 'tr' // Turkish
  | 'pl' // Polish
  | 'nl' // Dutch
  | 'sv' // Swedish
  | 'da' // Danish
  | 'no' // Norwegian
  | 'fi' // Finnish
  | 'cs' // Czech
  | 'hu' // Hungarian
  | 'ro' // Romanian
  | 'bg' // Bulgarian
  | 'hr' // Croatian
  | 'sk' // Slovak
  | 'sl' // Slovenian
  | 'et' // Estonian
  | 'lv' // Latvian
  | 'lt' // Lithuanian
  | 'mt' // Maltese
  | 'el' // Greek
  | 'cy' // Welsh
  | 'ga' // Irish
  | 'is' // Icelandic
  | 'mk' // Macedonian
  | 'sq' // Albanian
  | 'sr' // Serbian
  | 'bs' // Bosnian
  | 'me' // Montenegrin
  | 'uk' // Ukrainian
  | 'be' // Belarusian
  | 'kk' // Kazakh
  | 'ky' // Kyrgyz
  | 'uz' // Uzbek
  | 'tg' // Tajik
  | 'mn' // Mongolian
  | 'ka' // Georgian
  | 'hy' // Armenian
  | 'az' // Azerbaijani
  | 'fa' // Persian
  | 'ur' // Urdu
  | 'bn' // Bengali
  | 'ta' // Tamil
  | 'te' // Telugu
  | 'ml' // Malayalam
  | 'kn' // Kannada
  | 'gu' // Gujarati
  | 'pa' // Punjabi
  | 'or' // Odia
  | 'as' // Assamese
  | 'ne' // Nepali
  | 'si' // Sinhala
  | 'my' // Myanmar
  | 'km' // Khmer
  | 'lo' // Lao
  | 'ka' // Georgian
  | 'am' // Amharic
  | 'sw' // Swahili
  | 'zu' // Zulu
  | 'af' // Afrikaans
  | 'xh' // Xhosa;

// Language metadata
export interface LanguageInfo {
  code: SupportedLanguage;
  name: string;
  nativeName: string;
  direction: 'ltr' | 'rtl';
  region: string;
  flag: string;
  dateFormat: string;
  timeFormat: string;
  numberFormat: {
    decimal: string;
    thousands: string;
    currency: {
      symbol: string;
      position: 'before' | 'after';
    };
  };
}

// Language definitions
export const languages: Record<SupportedLanguage, LanguageInfo> = {
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    direction: 'ltr',
    region: 'Global',
    flag: '🇺🇸',
    dateFormat: 'MM/dd/yyyy',
    timeFormat: 'h:mm a',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      currency: { symbol: '$', position: 'before' }
    }
  },
  es: {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    direction: 'ltr',
    region: 'Spain & Latin America',
    flag: '🇪🇸',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: ',',
      thousands: '.',
      currency: { symbol: '€', position: 'after' }
    }
  },
  fr: {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    direction: 'ltr',
    region: 'France & Francophone',
    flag: '🇫🇷',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: ',',
      thousands: ' ',
      currency: { symbol: '€', position: 'after' }
    }
  },
  de: {
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    direction: 'ltr',
    region: 'Germany & DACH',
    flag: '🇩🇪',
    dateFormat: 'dd.MM.yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: ',',
      thousands: '.',
      currency: { symbol: '€', position: 'after' }
    }
  },
  ar: {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    direction: 'rtl',
    region: 'Middle East & North Africa',
    flag: '🇸🇦',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'h:mm a',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      currency: { symbol: 'ر.س', position: 'after' }
    }
  },
  zh: {
    code: 'zh',
    name: 'Chinese (Simplified)',
    nativeName: '简体中文',
    direction: 'ltr',
    region: 'China',
    flag: '🇨🇳',
    dateFormat: 'yyyy/MM/dd',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      currency: { symbol: '¥', position: 'before' }
    }
  },
  ja: {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    direction: 'ltr',
    region: 'Japan',
    flag: '🇯🇵',
    dateFormat: 'yyyy/MM/dd',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      currency: { symbol: '¥', position: 'before' }
    }
  },
  ko: {
    code: 'ko',
    name: 'Korean',
    nativeName: '한국어',
    direction: 'ltr',
    region: 'South Korea',
    flag: '🇰🇷',
    dateFormat: 'yyyy. MM. dd.',
    timeFormat: 'a h:mm',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      currency: { symbol: '₩', position: 'before' }
    }
  },
  ru: {
    code: 'ru',
    name: 'Russian',
    nativeName: 'Русский',
    direction: 'ltr',
    region: 'Russia & CIS',
    flag: '🇷🇺',
    dateFormat: 'dd.MM.yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: ',',
      thousands: ' ',
      currency: { symbol: '₽', position: 'after' }
    }
  },
  pt: {
    code: 'pt',
    name: 'Portuguese',
    nativeName: 'Português',
    direction: 'ltr',
    region: 'Brazil & Portugal',
    flag: '🇧🇷',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: ',',
      thousands: '.',
      currency: { symbol: 'R$', position: 'before' }
    }
  },
  it: {
    code: 'it',
    name: 'Italian',
    nativeName: 'Italiano',
    direction: 'ltr',
    region: 'Italy',
    flag: '🇮🇹',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: ',',
      thousands: '.',
      currency: { symbol: '€', position: 'after' }
    }
  },
  he: {
    code: 'he',
    name: 'Hebrew',
    nativeName: 'עברית',
    direction: 'rtl',
    region: 'Israel',
    flag: '🇮🇱',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      currency: { symbol: '₪', position: 'before' }
    }
  },
  hi: {
    code: 'hi',
    name: 'Hindi',
    nativeName: 'हिन्दी',
    direction: 'ltr',
    region: 'India',
    flag: '🇮🇳',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'h:mm a',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      currency: { symbol: '₹', position: 'before' }
    }
  },
  th: {
    code: 'th',
    name: 'Thai',
    nativeName: 'ไทย',
    direction: 'ltr',
    region: 'Thailand',
    flag: '🇹🇭',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      currency: { symbol: '฿', position: 'before' }
    }
  },
  vi: {
    code: 'vi',
    name: 'Vietnamese',
    nativeName: 'Tiếng Việt',
    direction: 'ltr',
    region: 'Vietnam',
    flag: '🇻🇳',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: ',',
      thousands: '.',
      currency: { symbol: '₫', position: 'after' }
    }
  },
  tr: {
    code: 'tr',
    name: 'Turkish',
    nativeName: 'Türkçe',
    direction: 'ltr',
    region: 'Turkey',
    flag: '🇹🇷',
    dateFormat: 'dd.MM.yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: ',',
      thousands: '.',
      currency: { symbol: '₺', position: 'after' }
    }
  },
  // Add more languages as needed...
} as const;

// Translation keys type (will be extended based on actual translations)
export interface TranslationKeys {
  common: {
    save: string;
    cancel: string;
    delete: string;
    edit: string;
    add: string;
    remove: string;
    search: string;
    filter: string;
    sort: string;
    loading: string;
    error: string;
    success: string;
    warning: string;
    info: string;
    yes: string;
    no: string;
    ok: string;
    close: string;
    back: string;
    next: string;
    previous: string;
    submit: string;
    reset: string;
    clear: string;
    select: string;
    selectAll: string;
    deselectAll: string;
    required: string;
    optional: string;
    name: string;
    description: string;
    date: string;
    time: string;
    email: string;
    phone: string;
    address: string;
    country: string;
    currency: string;
    language: string;
    theme: string;
    settings: string;
    profile: string;
    logout: string;
    login: string;
    register: string;
    forgotPassword: string;
    resetPassword: string;
    changePassword: string;
    dashboard: string;
    home: string;
    about: string;
    contact: string;
    help: string;
    support: string;
    documentation: string;
    terms: string;
    privacy: string;
    cookies: string;
  };
  navigation: {
    dashboard: string;
    tasks: string;
    events: string;
    settings: string;
    profile: string;
    logout: string;
  };
  forms: {
    validation: {
      required: string;
      email: string;
      minLength: string;
      maxLength: string;
      pattern: string;
    };
  };
  // Add more sections as needed
}

// I18n Context
interface I18nContextValue {
  language: SupportedLanguage;
  languageInfo: LanguageInfo;
  setLanguage: (language: SupportedLanguage) => void;
  t: (key: string, params?: Record<string, string | number>) => string;
  formatDate: (date: Date, format?: string) => string;
  formatTime: (date: Date, format?: string) => string;
  formatNumber: (number: number, options?: Intl.NumberFormatOptions) => string;
  formatCurrency: (amount: number, currency?: string) => string;
  isRTL: boolean;
}

export const I18nContext = createContext<I18nContextValue | null>(null);

export const useI18n = () => {
  const context = useContext(I18nContext);
  if (!context) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
};

// Translation utilities
export const getStoredLanguage = (): SupportedLanguage => {
  if (typeof window === 'undefined') return 'en';
  
  const stored = localStorage.getItem('language') as SupportedLanguage;
  if (stored && languages[stored]) return stored;
  
  // Try to detect from browser
  const browserLang = navigator.language.split('-')[0] as SupportedLanguage;
  if (languages[browserLang]) return browserLang;
  
  return 'en';
};

export const storeLanguage = (language: SupportedLanguage) => {
  if (typeof window === 'undefined') return;
  localStorage.setItem('language', language);
};

// Formatting utilities
export const formatDateWithLocale = (date: Date, language: SupportedLanguage, format?: string): string => {
  const langInfo = languages[language];
  const locale = language === 'en' ? 'en-US' : language;
  
  if (format) {
    // Custom format implementation would go here
    return date.toLocaleDateString(locale);
  }
  
  return date.toLocaleDateString(locale, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

export const formatTimeWithLocale = (date: Date, language: SupportedLanguage, format?: string): string => {
  const locale = language === 'en' ? 'en-US' : language;
  
  return date.toLocaleTimeString(locale, {
    hour: '2-digit',
    minute: '2-digit',
    hour12: languages[language].timeFormat.includes('a')
  });
};

export const formatNumberWithLocale = (
  number: number, 
  language: SupportedLanguage, 
  options?: Intl.NumberFormatOptions
): string => {
  const locale = language === 'en' ? 'en-US' : language;
  return new Intl.NumberFormat(locale, options).format(number);
};

export const formatCurrencyWithLocale = (
  amount: number, 
  language: SupportedLanguage, 
  currency?: string
): string => {
  const locale = language === 'en' ? 'en-US' : language;
  const currencyCode = currency || (language === 'en' ? 'USD' : 'EUR');
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyCode
  }).format(amount);
};

// RTL languages
export const RTL_LANGUAGES: SupportedLanguage[] = ['ar', 'he', 'fa', 'ur'];

export const isRTLLanguage = (language: SupportedLanguage): boolean => {
  return RTL_LANGUAGES.includes(language);
};
