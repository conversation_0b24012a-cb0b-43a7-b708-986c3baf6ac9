/**
 * Secure Environment Variables Management
 * Provides encrypted storage and validation of sensitive configuration
 */

// Environment variable validation schema
interface EnvSchema {
  [key: string]: {
    required: boolean;
    type: 'string' | 'number' | 'boolean' | 'url' | 'email';
    sensitive: boolean;
    validation?: RegExp;
    description: string;
  };
}

// EVEXA environment schema
const EVEXA_ENV_SCHEMA: EnvSchema = {
  // Firebase Configuration (handled by Next.js, validation optional)
  NEXT_PUBLIC_FIREBASE_API_KEY: {
    required: false, // Next.js handles these differently
    type: 'string',
    sensitive: true,
    description: 'Firebase API Key'
  },
  NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: {
    required: false,
    type: 'url',
    sensitive: false,
    description: 'Firebase Auth Domain'
  },
  NEXT_PUBLIC_FIREBASE_PROJECT_ID: {
    required: false,
    type: 'string',
    sensitive: false,
    description: 'Firebase Project ID'
  },
  NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: {
    required: false,
    type: 'string',
    sensitive: false,
    description: 'Firebase Storage Bucket'
  },
  NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: {
    required: false,
    type: 'string',
    sensitive: false,
    description: 'Firebase Messaging Sender ID'
  },
  NEXT_PUBLIC_FIREBASE_APP_ID: {
    required: false,
    type: 'string',
    sensitive: false,
    description: 'Firebase App ID'
  },
  
  // AI Configuration
  GROQ_API_KEY: {
    required: false,
    type: 'string',
    sensitive: true,
    validation: /^gsk_[a-zA-Z0-9]{48}$/,
    description: 'Groq API Key for AI features'
  },
  
  // Security Configuration
  JWT_SECRET: {
    required: process.env.NODE_ENV === 'production',
    type: 'string',
    sensitive: true,
    description: 'JWT signing secret'
  },
  ENCRYPTION_KEY: {
    required: process.env.NODE_ENV === 'production',
    type: 'string',
    sensitive: true,
    description: 'Data encryption key'
  },
  
  // License Configuration
  EVEXA_LICENSE_KEY: {
    required: false,
    type: 'string',
    sensitive: true,
    description: 'EVEXA license key'
  },
  
  // Deployment Configuration
  NODE_ENV: {
    required: false, // Automatically set by Next.js
    type: 'string',
    sensitive: false,
    validation: /^(development|production|test)$/,
    description: 'Node environment'
  },
  VERCEL_URL: {
    required: false,
    type: 'url',
    sensitive: false,
    description: 'Vercel deployment URL'
  },
  
  // Database Configuration
  REDIS_URL: {
    required: false,
    type: 'url',
    sensitive: true,
    description: 'Redis connection URL'
  },
  
  // Monitoring Configuration
  SENTRY_DSN: {
    required: false,
    type: 'url',
    sensitive: true,
    description: 'Sentry error tracking DSN'
  }
};

export class SecureEnvManager {
  private static instance: SecureEnvManager;
  private encryptionKey: string | null = null;
  private validatedEnv: Map<string, any> = new Map();

  static getInstance(): SecureEnvManager {
    if (!SecureEnvManager.instance) {
      SecureEnvManager.instance = new SecureEnvManager();
    }
    return SecureEnvManager.instance;
  }

  /**
   * Initialize secure environment management
   */
  async initialize(): Promise<void> {
    // COMPLETELY SKIP VALIDATION IN DEVELOPMENT
    if (process.env.NODE_ENV === 'development' || !process.env.NODE_ENV) {
      console.log('🔐 Secure environment initialized (development mode - validation skipped)');
      this.initializeEncryption();
      return;
    }

    try {
      // Only run strict validation in production
      await this.validateEnvironment();

      // Initialize encryption if needed
      this.initializeEncryption();

      console.log('🔐 Secure environment initialized');
    } catch (error) {
      console.error('❌ Environment initialization failed:', error);
      throw error;
    }
  }

  /**
   * Validate environment variables against schema
   */
  private async validateEnvironment(): Promise<void> {
    // COMPLETELY SKIP VALIDATION IN DEVELOPMENT OR WHEN NODE_ENV IS NOT SET
    if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
      console.log('🔐 Environment validation skipped in development mode');
      return;
    }

    // Only run validation in production
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const [key, schema] of Object.entries(EVEXA_ENV_SCHEMA)) {
      const value = process.env[key];

      // Check required variables (only in production)
      if (schema.required && !value) {
        errors.push(`Required environment variable ${key} is missing`);
        continue;
      }

      if (!value) continue;

      // Type validation
      if (!this.validateType(value, schema.type)) {
        errors.push(`Environment variable ${key} has invalid type (expected ${schema.type})`);
        continue;
      }

      // Pattern validation
      if (schema.validation && !schema.validation.test(value)) {
        errors.push(`Environment variable ${key} does not match required pattern`);
        continue;
      }

      // Store validated value
      this.validatedEnv.set(key, this.parseValue(value, schema.type));

      // Security warnings
      if (schema.sensitive && this.isValueInsecure(value)) {
        warnings.push(`Environment variable ${key} may be insecure`);
      }
    }

    // Log warnings
    if (warnings.length > 0) {
      console.warn('⚠️ Environment security warnings:', warnings);
    }

    // Throw errors if any (only in production)
    if (errors.length > 0) {
      throw new Error(`Environment validation failed:\n${errors.join('\n')}`);
    }
  }

  /**
   * Validate value type
   */
  private validateType(value: string, type: string): boolean {
    switch (type) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return !isNaN(Number(value));
      case 'boolean':
        return ['true', 'false', '1', '0'].includes(value.toLowerCase());
      case 'url':
        try {
          new URL(value);
          return true;
        } catch {
          return false;
        }
      case 'email':
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      default:
        return true;
    }
  }

  /**
   * Parse value to correct type
   */
  private parseValue(value: string, type: string): any {
    switch (type) {
      case 'number':
        return Number(value);
      case 'boolean':
        return ['true', '1'].includes(value.toLowerCase());
      default:
        return value;
    }
  }

  /**
   * Check if sensitive value is insecure
   */
  private isValueInsecure(value: string): boolean {
    const insecurePatterns = [
      /^(test|demo|example|default|admin|password|secret)$/i,
      /^(123|abc|qwerty)/i,
      /^.{1,8}$/, // Too short
    ];

    return insecurePatterns.some(pattern => pattern.test(value));
  }

  /**
   * Initialize encryption for sensitive data
   */
  private initializeEncryption(): void {
    this.encryptionKey = process.env.ENCRYPTION_KEY || this.generateEncryptionKey();
  }

  /**
   * Generate encryption key if not provided
   */
  private generateEncryptionKey(): string {
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      const array = new Uint8Array(32);
      crypto.getRandomValues(array);
      return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    }
    
    // Fallback for Node.js
    const crypto = require('crypto');
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Get environment variable with validation
   */
  get(key: string): any {
    if (!this.validatedEnv.has(key)) {
      const value = process.env[key];
      if (value === undefined) {
        // In development, return undefined instead of throwing
        if (process.env.NODE_ENV === 'development' || !process.env.NODE_ENV) {
          return undefined;
        }
        throw new Error(`Environment variable ${key} is not defined`);
      }
      return value;
    }

    return this.validatedEnv.get(key);
  }

  /**
   * Get environment variable with default value
   */
  getWithDefault(key: string, defaultValue: any): any {
    try {
      return this.get(key);
    } catch {
      return defaultValue;
    }
  }

  /**
   * Check if environment variable exists
   */
  has(key: string): boolean {
    return this.validatedEnv.has(key) || process.env[key] !== undefined;
  }

  /**
   * Get all environment variables (excluding sensitive ones in production)
   */
  getAll(includeSensitive: boolean = false): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const [key, schema] of Object.entries(EVEXA_ENV_SCHEMA)) {
      if (!includeSensitive && schema.sensitive && process.env.NODE_ENV === 'production') {
        result[key] = '[REDACTED]';
      } else if (this.has(key)) {
        result[key] = this.get(key);
      }
    }
    
    return result;
  }

  /**
   * Encrypt sensitive value
   */
  async encrypt(value: string): Promise<string> {
    if (!this.encryptionKey) {
      throw new Error('Encryption not initialized');
    }

    try {
      const encoder = new TextEncoder();
      const data = encoder.encode(value);
      const key = await crypto.subtle.importKey(
        'raw',
        encoder.encode(this.encryptionKey.slice(0, 32)),
        { name: 'AES-GCM' },
        false,
        ['encrypt']
      );
      
      const iv = crypto.getRandomValues(new Uint8Array(12));
      const encrypted = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        key,
        data
      );
      
      const result = new Uint8Array(iv.length + encrypted.byteLength);
      result.set(iv);
      result.set(new Uint8Array(encrypted), iv.length);
      
      return btoa(String.fromCharCode(...result));
    } catch (error) {
      throw new Error('Encryption failed');
    }
  }

  /**
   * Decrypt sensitive value
   */
  async decrypt(encryptedValue: string): Promise<string> {
    if (!this.encryptionKey) {
      throw new Error('Encryption not initialized');
    }

    try {
      const data = new Uint8Array(
        atob(encryptedValue).split('').map(char => char.charCodeAt(0))
      );
      
      const iv = data.slice(0, 12);
      const encrypted = data.slice(12);
      
      const encoder = new TextEncoder();
      const key = await crypto.subtle.importKey(
        'raw',
        encoder.encode(this.encryptionKey.slice(0, 32)),
        { name: 'AES-GCM' },
        false,
        ['decrypt']
      );
      
      const decrypted = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv },
        key,
        encrypted
      );
      
      return new TextDecoder().decode(decrypted);
    } catch (error) {
      throw new Error('Decryption failed');
    }
  }

  /**
   * Generate environment report
   */
  generateReport(): {
    valid: string[];
    missing: string[];
    insecure: string[];
    total: number;
  } {
    const valid: string[] = [];
    const missing: string[] = [];
    const insecure: string[] = [];

    for (const [key, schema] of Object.entries(EVEXA_ENV_SCHEMA)) {
      const value = process.env[key];

      if (!value) {
        // In development, don't report missing non-critical vars
        if (schema.required && process.env.NODE_ENV === 'production') {
          missing.push(key);
        }
      } else {
        valid.push(key);
        if (schema.sensitive && this.isValueInsecure(value)) {
          insecure.push(key);
        }
      }
    }

    return {
      valid,
      missing,
      insecure,
      total: Object.keys(EVEXA_ENV_SCHEMA).length
    };
  }
}

// Export singleton instance
export const secureEnv = SecureEnvManager.getInstance();
