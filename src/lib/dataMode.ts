/**
 * EVEXA Data Layer - Industry Standard Implementation
 *
 * This module provides a clean separation between data sources and implements
 * the Repository Pattern for scalable data management.
 *
 * Architecture:
 * - Production: Firebase only
 * - Development: Configurable (Firebase or Mock)
 * - Testing: Mock data with seeding capabilities
 */

// React import moved to client-side components only

// Environment configuration - Safe for both client and server
const NODE_ENV = typeof process !== 'undefined' ? process.env.NODE_ENV : 'development';
const IS_PRODUCTION = NODE_ENV === 'production';
const IS_DEVELOPMENT = NODE_ENV === 'development';
const IS_TEST = NODE_ENV === 'test';

// Safe environment variable access
const getEnvVar = (key: string): string | undefined => {
  if (typeof process !== 'undefined' && process.env) {
    return process.env[key];
  }
  return undefined;
};

// Data source configuration - FIREBASE ONLY MODE
export const DATA_CONFIG = {
  // Always use Firebase - no mock data in production app
  USE_FIREBASE: true,

  // Mock data disabled - only for seeding development data
  USE_MOCK_DATA: false,

  // Testing: Use Firebase with test data seeding
  USE_TEST_DATA: false,

  // Admin controls visibility (for seeding only)
  SHOW_ADMIN_CONTROLS: IS_DEVELOPMENT && getEnvVar('NEXT_PUBLIC_SHOW_ADMIN_CONTROLS') === 'true',

  // Debug logging
  ENABLE_DEBUG_LOGS: IS_DEVELOPMENT && getEnvVar('NEXT_PUBLIC_DEBUG_DATA') === 'true'
} as const;

/**
 * Data Source Types
 */
export type DataSource = 'firebase' | 'mock' | 'test';

/**
 * Get current active data source - Always Firebase
 */
export function getCurrentDataSource(): DataSource {
  // Always return Firebase - mock data only for seeding
  return 'firebase';
}

/**
 * Repository Pattern Implementation
 *
 * This function implements the Repository Pattern, allowing clean switching
 * between data sources without changing business logic.
 */
export async function withDataSource<T>(options: {
  firebase: () => Promise<T>;
  mock?: () => Promise<T> | T;
  test?: () => Promise<T> | T;
  fallback?: () => Promise<T> | T;
}): Promise<T> {
  // Always use Firebase - simplified implementation
  try {
    if (DATA_CONFIG.ENABLE_DEBUG_LOGS) {
      console.log('📊 Data Source: Firebase (Production Mode)');
    }
    return await options.firebase();
  } catch (error) {
    console.error('❌ Firebase data source error:', error);

    // Fallback strategy only
    if (options.fallback) {
      console.log('🔄 Using fallback data source');
      return await Promise.resolve(options.fallback());
    }

    throw error;
  }
}

/**
 * Check if development-only features should be shown
 */
export function shouldShowDevFeatures(adminOnly = false): boolean {
  if (IS_PRODUCTION) return false;
  if (adminOnly && !DATA_CONFIG.SHOW_ADMIN_CONTROLS) return false;
  return true;
}

/**
 * Check if production-only features should be shown
 */
export function shouldShowProdFeatures(): boolean {
  return IS_PRODUCTION;
}

/**
 * Get conditional content based on data source
 */
export function getDataSourceContent<T>(options: {
  firebase?: T;
  mock?: T;
  test?: T;
  fallback?: T;
}): T | null {
  const source = getCurrentDataSource();

  switch (source) {
    case 'firebase':
      return options.firebase || options.fallback || null;
    case 'mock':
      return options.mock || options.fallback || null;
    case 'test':
      return options.test || options.fallback || null;
    default:
      return options.fallback || null;
  }
}

/**
 * Data layer health check
 */
export function getDataLayerStatus() {
  return {
    environment: NODE_ENV,
    dataSource: getCurrentDataSource(),
    config: DATA_CONFIG,
    timestamp: new Date().toISOString()
  };
}

// Export configuration for external use
export { DATA_CONFIG as default };
