/**
 * Data Validation and Monitoring System for EVEXA
 * 
 * This module provides comprehensive data validation, monitoring, and consistency checks
 * to prevent data duplication and ensure data integrity across the application.
 */

import type { Event as EvexEvent, Exhibition, Task, Lead } from '@/types/firestore';

// Development mode flag
const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * Data validation results interface
 */
export interface DataValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  info: string[];
  duplicates: {
    events: string[];
    exhibitions: string[];
    tasks: string[];
    leads: string[];
  };
}

/**
 * Validate array for duplicate IDs
 */
function validateUniqueIds<T extends { id?: string }>(
  data: T[], 
  dataType: string
): { duplicates: string[]; errors: string[] } {
  const ids = data.map(item => item.id).filter(Boolean) as string[];
  const uniqueIds = [...new Set(ids)];
  
  const duplicates: string[] = [];
  const errors: string[] = [];
  
  if (ids.length !== uniqueIds.length) {
    const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index);
    duplicates.push(...new Set(duplicateIds));
    errors.push(`CRITICAL: Duplicate IDs found in ${dataType}: ${duplicates.join(', ')}`);
  }
  
  // Check for missing IDs
  const missingIds = data.filter(item => !item.id);
  if (missingIds.length > 0) {
    errors.push(`WARNING: ${missingIds.length} items in ${dataType} missing IDs`);
  }
  
  return { duplicates, errors };
}

/**
 * Validate events data structure
 */
function validateEvents(events: EvexEvent[]): Partial<DataValidationResult> {
  const result = validateUniqueIds(events, 'events');
  const errors: string[] = [...result.errors];
  const warnings: string[] = [];
  const info: string[] = [];
  
  // Additional event-specific validations
  events.forEach((event, index) => {
    if (!event.eventName) {
      errors.push(`Event at index ${index} missing eventName`);
    }
    
    if (!event.startDate) {
      warnings.push(`Event "${event.eventName || 'Unknown'}" missing startDate`);
    }
    
    if (!event.endDate) {
      warnings.push(`Event "${event.eventName || 'Unknown'}" missing endDate`);
    }
  });
  
  info.push(`✅ Events validated: ${events.length} total, ${events.filter(e => e.id).length} with IDs`);
  
  return {
    errors,
    warnings,
    info,
    duplicates: { events: result.duplicates, exhibitions: [], tasks: [], leads: [] }
  };
}

/**
 * Validate exhibitions data structure
 */
function validateExhibitions(exhibitions: Exhibition[]): Partial<DataValidationResult> {
  const result = validateUniqueIds(exhibitions, 'exhibitions');
  const errors: string[] = [...result.errors];
  const warnings: string[] = [];
  const info: string[] = [];
  
  // Additional exhibition-specific validations
  exhibitions.forEach((exhibition, index) => {
    if (!exhibition.name) {
      errors.push(`Exhibition at index ${index} missing name`);
    }
    
    if (!exhibition.startDate) {
      warnings.push(`Exhibition "${exhibition.name || 'Unknown'}" missing startDate`);
    }
  });
  
  info.push(`✅ Exhibitions validated: ${exhibitions.length} total, ${exhibitions.filter(e => e.id).length} with IDs`);
  
  return {
    errors,
    warnings,
    info,
    duplicates: { events: [], exhibitions: result.duplicates, tasks: [], leads: [] }
  };
}

/**
 * Main data validation function
 */
export function validateDataConsistency(data: {
  events?: EvexEvent[];
  exhibitions?: Exhibition[];
  tasks?: Task[];
  leads?: Lead[];
}): DataValidationResult {
  const result: DataValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    info: [],
    duplicates: { events: [], exhibitions: [], tasks: [], leads: [] }
  };
  
  // Validate events
  if (data.events) {
    const eventValidation = validateEvents(data.events);
    result.errors.push(...(eventValidation.errors || []));
    result.warnings.push(...(eventValidation.warnings || []));
    result.info.push(...(eventValidation.info || []));
    if (eventValidation.duplicates) {
      result.duplicates.events = eventValidation.duplicates.events;
    }
  }
  
  // Validate exhibitions
  if (data.exhibitions) {
    const exhibitionValidation = validateExhibitions(data.exhibitions);
    result.errors.push(...(exhibitionValidation.errors || []));
    result.warnings.push(...(exhibitionValidation.warnings || []));
    result.info.push(...(exhibitionValidation.info || []));
    if (exhibitionValidation.duplicates) {
      result.duplicates.exhibitions = exhibitionValidation.duplicates.exhibitions;
    }
  }
  
  // Validate tasks
  if (data.tasks) {
    const taskValidation = validateUniqueIds(data.tasks, 'tasks');
    result.errors.push(...taskValidation.errors);
    result.duplicates.tasks = taskValidation.duplicates;
    result.info.push(`✅ Tasks validated: ${data.tasks.length} total`);
  }
  
  // Validate leads
  if (data.leads) {
    const leadValidation = validateUniqueIds(data.leads, 'leads');
    result.errors.push(...leadValidation.errors);
    result.duplicates.leads = leadValidation.duplicates;
    result.info.push(`✅ Leads validated: ${data.leads.length} total`);
  }
  
  // Set overall validity
  result.isValid = result.errors.length === 0;
  
  return result;
}

/**
 * Development-time data monitoring
 */
export function monitorDataInDevelopment(
  data: any, 
  dataType: string, 
  context: string = 'unknown'
): void {
  if (!isDevelopment) return;
  
  console.group(`🔍 Data Monitor: ${dataType} in ${context}`);
  
  if (Array.isArray(data)) {
    console.log(`📊 Array length: ${data.length}`);
    
    if (data.length > 0 && data[0].id) {
      const ids = data.map(item => item.id);
      const uniqueIds = [...new Set(ids)];
      
      console.log(`🆔 IDs: ${ids.length} total, ${uniqueIds.length} unique`);
      
      if (ids.length !== uniqueIds.length) {
        const duplicates = ids.filter((id, index) => ids.indexOf(id) !== index);
        console.error(`🚨 DUPLICATES DETECTED:`, [...new Set(duplicates)]);
      }
    }
  } else {
    console.log(`📄 Single item:`, data);
  }
  
  console.groupEnd();
}

/**
 * Runtime data consistency checker
 */
export function ensureDataConsistency<T extends { id?: string }>(
  data: T[], 
  dataType: string
): T[] {
  if (!isDevelopment) return data;
  
  // Remove duplicates by ID, keeping the first occurrence
  const seen = new Set<string>();
  const deduplicatedData = data.filter(item => {
    if (!item.id) return true; // Keep items without IDs
    
    if (seen.has(item.id)) {
      console.warn(`🔧 Removing duplicate ${dataType} with ID: ${item.id}`);
      return false;
    }
    
    seen.add(item.id);
    return true;
  });
  
  if (deduplicatedData.length !== data.length) {
    console.warn(`🔧 Data consistency fix: Removed ${data.length - deduplicatedData.length} duplicates from ${dataType}`);
  }
  
  return deduplicatedData;
}

/**
 * Export validation utilities
 */
export const DataValidator = {
  validateDataConsistency,
  monitorDataInDevelopment,
  ensureDataConsistency,
  validateUniqueIds
};
