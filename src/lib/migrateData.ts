/**
 * EVEXA Critical Data Migration Script
 * 
 * Migrates existing data from legacy collections to professional schema
 * with StandardMetadata and proper naming conventions
 */

import { db } from '@/lib/firebase';
import { 
  collection, 
  doc, 
  getDocs, 
  setDoc, 
  writeBatch,
  query,
  orderBy,
  limit
} from 'firebase/firestore';
import { EVEXA_DATA_SCHEMA, StandardMetadata, EVEXA_TENANT_ID } from '@/lib/professionalDataManager';

/**
 * Create professional metadata for migrated documents
 */
function createMigrationMetadata(
  originalCreatedAt?: any,
  originalUpdatedAt?: any,
  userId: string = 'migration_system'
): StandardMetadata {
  const now = new Date();
  
  // Try to preserve original timestamps if they exist
  let createdAt = now;
  let updatedAt = now;
  
  if (originalCreatedAt) {
    if (originalCreatedAt.toDate) {
      createdAt = originalCreatedAt.toDate();
    } else if (originalCreatedAt instanceof Date) {
      createdAt = originalCreatedAt;
    } else if (typeof originalCreatedAt === 'string') {
      createdAt = new Date(originalCreatedAt);
    }
  }
  
  if (originalUpdatedAt) {
    if (originalUpdatedAt.toDate) {
      updatedAt = originalUpdatedAt.toDate();
    } else if (originalUpdatedAt instanceof Date) {
      updatedAt = originalUpdatedAt;
    } else if (typeof originalUpdatedAt === 'string') {
      updatedAt = new Date(originalUpdatedAt);
    }
  }
  
  return {
    tenant_id: EVEXA_TENANT_ID,
    created_at: createdAt,
    updated_at: updatedAt,
    created_by: userId,
    updated_by: userId,
    version: 1,
    // Migration metadata
    _migrated_from: 'legacy_collection',
    _migration_timestamp: now
  } as StandardMetadata & { _migrated_from: string; _migration_timestamp: Date };
}

/**
 * Migration configuration for each collection
 */
const MIGRATION_CONFIG = {
  users: {
    sourceCollection: 'users',
    targetCollection: EVEXA_DATA_SCHEMA.CORE_COLLECTIONS.user_profiles,
    description: 'Migrate users to user_profiles with professional metadata',
    expectedDocuments: 1,
    fieldMappings: {
      // Keep most fields as-is, but ensure professional naming
      id: 'id',
      displayName: 'display_name',
      email: 'email',
      role: 'role',
      status: 'status',
      profileImageUrl: 'profile_image_url',
      jobTitle: 'job_title',
      department: 'department',
      phone: 'phone'
    }
  },
  
  tenants: {
    sourceCollection: 'tenants',
    targetCollection: EVEXA_DATA_SCHEMA.SYSTEM_COLLECTIONS.tenants,
    description: 'Migrate tenants with professional metadata (keep same collection name)',
    expectedDocuments: 2,
    fieldMappings: {
      // Keep all fields as-is for tenants
      id: 'id',
      name: 'name',
      domain: 'domain',
      status: 'status'
    }
  },
  
  'security-events': {
    sourceCollection: 'security-events',
    targetCollection: EVEXA_DATA_SCHEMA.SYSTEM_COLLECTIONS.security_logs,
    description: 'Migrate security-events to security_logs with professional metadata',
    expectedDocuments: 3168,
    fieldMappings: {
      id: 'id',
      eventType: 'event_type',
      severity: 'severity',
      timestamp: 'timestamp',
      sourceIp: 'source_ip',
      userId: 'user_id',
      description: 'description',
      metadata: 'metadata'
    },
    batchSize: 100 // Process in smaller batches due to large volume
  },
  
  evexa_business_metrics: {
    sourceCollection: 'evexa_business_metrics',
    targetCollection: EVEXA_DATA_SCHEMA.SYSTEM_COLLECTIONS.business_metrics,
    description: 'Migrate evexa_business_metrics to business_metrics with professional metadata',
    expectedDocuments: 2,
    fieldMappings: {
      id: 'id',
      metricType: 'metric_type',
      value: 'value',
      timestamp: 'timestamp',
      category: 'category'
    }
  }
};

/**
 * Migrate a single collection
 */
async function migrateCollection(configKey: keyof typeof MIGRATION_CONFIG): Promise<{
  success: boolean;
  sourceCollection: string;
  targetCollection: string;
  migratedCount: number;
  errors: string[];
}> {
  const config = MIGRATION_CONFIG[configKey];
  console.log(`🔄 Migrating ${config.sourceCollection} → ${config.targetCollection}...`);
  
  const errors: string[] = [];
  let migratedCount = 0;
  
  try {
    // Get source documents
    const sourceRef = collection(db, config.sourceCollection);
    const sourceSnapshot = await getDocs(sourceRef);
    
    console.log(`   📊 Found ${sourceSnapshot.docs.length} documents in ${config.sourceCollection}`);
    
    if (sourceSnapshot.docs.length !== config.expectedDocuments) {
      console.warn(`   ⚠️ Expected ${config.expectedDocuments} documents, found ${sourceSnapshot.docs.length}`);
    }
    
    // Process documents in batches if specified
    const batchSize = config.batchSize || 50;
    const docs = sourceSnapshot.docs;
    
    for (let i = 0; i < docs.length; i += batchSize) {
      const batch = writeBatch(db);
      const batchDocs = docs.slice(i, i + batchSize);
      
      console.log(`   📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(docs.length / batchSize)}`);
      
      for (const sourceDoc of batchDocs) {
        try {
          const sourceData = sourceDoc.data();
          
          // Apply field mappings
          const migratedData: any = {};
          
          for (const [sourceField, targetField] of Object.entries(config.fieldMappings)) {
            if (sourceData[sourceField] !== undefined) {
              migratedData[targetField] = sourceData[sourceField];
            }
          }
          
          // Add any unmapped fields (preserve data)
          for (const [key, value] of Object.entries(sourceData)) {
            if (!config.fieldMappings[key] && !key.startsWith('_') && 
                key !== 'createdAt' && key !== 'updatedAt' && 
                key !== 'tenantId' && key !== 'version') {
              migratedData[key] = value;
            }
          }
          
          // Add professional metadata
          const professionalMetadata = createMigrationMetadata(
            sourceData.createdAt,
            sourceData.updatedAt,
            'migration_system'
          );
          
          const finalDocument = {
            ...migratedData,
            ...professionalMetadata
          };
          
          // Create target document
          const targetRef = doc(collection(db, config.targetCollection), sourceDoc.id);
          batch.set(targetRef, finalDocument);
          
        } catch (error) {
          errors.push(`Failed to migrate document ${sourceDoc.id}: ${error}`);
          console.error(`   ❌ Error migrating ${sourceDoc.id}:`, error);
        }
      }
      
      // Commit batch
      try {
        await batch.commit();
        migratedCount += batchDocs.length;
        console.log(`   ✅ Migrated batch: ${batchDocs.length} documents`);
      } catch (error) {
        errors.push(`Failed to commit batch: ${error}`);
        console.error(`   ❌ Batch commit failed:`, error);
      }
    }
    
    console.log(`🔄 Migration complete: ${config.sourceCollection} → ${config.targetCollection} (${migratedCount} documents)`);
    
    return {
      success: errors.length === 0,
      sourceCollection: config.sourceCollection,
      targetCollection: config.targetCollection,
      migratedCount,
      errors
    };
    
  } catch (error) {
    console.error(`Migration failed for ${config.sourceCollection}:`, error);
    return {
      success: false,
      sourceCollection: config.sourceCollection,
      targetCollection: config.targetCollection,
      migratedCount,
      errors: [...errors, `Migration failed: ${error}`]
    };
  }
}

/**
 * Migrate all critical data collections
 */
export async function migrateCriticalData(): Promise<{
  success: boolean;
  migrations: Array<{
    sourceCollection: string;
    targetCollection: string;
    migratedCount: number;
    success: boolean;
  }>;
  totalMigrated: number;
  errors: string[];
}> {
  console.log('🚀 Starting critical data migration...');
  
  const migrations: Array<{
    sourceCollection: string;
    targetCollection: string;
    migratedCount: number;
    success: boolean;
  }> = [];
  
  const allErrors: string[] = [];
  let totalMigrated = 0;
  
  // Migrate each collection
  for (const configKey of Object.keys(MIGRATION_CONFIG) as Array<keyof typeof MIGRATION_CONFIG>) {
    const result = await migrateCollection(configKey);
    
    migrations.push({
      sourceCollection: result.sourceCollection,
      targetCollection: result.targetCollection,
      migratedCount: result.migratedCount,
      success: result.success
    });
    
    totalMigrated += result.migratedCount;
    allErrors.push(...result.errors);
  }
  
  const overallSuccess = allErrors.length === 0;
  
  console.log(`🚀 Critical data migration ${overallSuccess ? 'COMPLETED' : 'COMPLETED WITH ERRORS'}`);
  console.log(`   📊 Total documents migrated: ${totalMigrated}`);
  console.log(`   ✅ Successful migrations: ${migrations.filter(m => m.success).length}`);
  console.log(`   ❌ Failed migrations: ${migrations.filter(m => !m.success).length}`);
  
  return {
    success: overallSuccess,
    migrations,
    totalMigrated,
    errors: allErrors
  };
}

/**
 * Validate migrated data integrity
 */
export async function validateMigratedData(): Promise<{
  isValid: boolean;
  validations: Array<{
    collection: string;
    documentCount: number;
    hasValidMetadata: boolean;
    issues: string[];
  }>;
  totalIssues: number;
}> {
  console.log('🔍 Validating migrated data...');
  
  const validations: Array<{
    collection: string;
    documentCount: number;
    hasValidMetadata: boolean;
    issues: string[];
  }> = [];
  
  let totalIssues = 0;
  
  // Check each target collection
  const targetCollections = Object.values(MIGRATION_CONFIG).map(config => config.targetCollection);
  
  for (const collectionName of targetCollections) {
    const issues: string[] = [];
    
    try {
      const collectionRef = collection(db, collectionName);
      const snapshot = await getDocs(collectionRef);
      
      let hasValidMetadata = true;
      
      // Check each document for professional metadata
      for (const doc of snapshot.docs) {
        const data = doc.data();
        
        if (!data.tenant_id || data.tenant_id !== EVEXA_TENANT_ID) {
          issues.push(`Document ${doc.id} missing or invalid tenant_id`);
          hasValidMetadata = false;
        }
        
        if (!data.created_at || !data.updated_at) {
          issues.push(`Document ${doc.id} missing timestamps`);
          hasValidMetadata = false;
        }
        
        if (!data.created_by || !data.updated_by) {
          issues.push(`Document ${doc.id} missing user tracking`);
          hasValidMetadata = false;
        }
        
        if (typeof data.version !== 'number') {
          issues.push(`Document ${doc.id} missing version`);
          hasValidMetadata = false;
        }
      }
      
      validations.push({
        collection: collectionName,
        documentCount: snapshot.docs.length,
        hasValidMetadata,
        issues
      });
      
      totalIssues += issues.length;
      
      console.log(`   ${hasValidMetadata ? '✅' : '⚠️'} ${collectionName}: ${snapshot.docs.length} documents, ${issues.length} issues`);
      
    } catch (error) {
      const errorMsg = `Failed to validate ${collectionName}: ${error}`;
      issues.push(errorMsg);
      totalIssues++;
      
      validations.push({
        collection: collectionName,
        documentCount: 0,
        hasValidMetadata: false,
        issues: [errorMsg]
      });
      
      console.error(`   ❌ ${collectionName}: validation failed`);
    }
  }
  
  const isValid = totalIssues === 0;
  console.log(`🔍 Validation ${isValid ? 'PASSED' : 'FAILED'}: ${totalIssues} total issues found`);
  
  return {
    isValid,
    validations,
    totalIssues
  };
}
