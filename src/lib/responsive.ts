/**
 * Responsive Design Utilities for EVEXA
 * Mobile-first design with touch-friendly interactions
 */

import { useEffect, useState } from 'react';

// Breakpoint definitions (mobile-first)
export const breakpoints = {
  xs: 0,      // Extra small devices (phones)
  sm: 640,    // Small devices (large phones)
  md: 768,    // Medium devices (tablets)
  lg: 1024,   // Large devices (desktops)
  xl: 1280,   // Extra large devices (large desktops)
  '2xl': 1536, // 2X large devices (larger desktops)
} as const;

export type Breakpoint = keyof typeof breakpoints;

// Device type detection
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

export const getDeviceType = (width: number): DeviceType => {
  if (width < breakpoints.md) return 'mobile';
  if (width < breakpoints.lg) return 'tablet';
  return 'desktop';
};

// Touch device detection
export const isTouchDevice = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  return (
    'ontouchstart' in window ||
    navigator.maxTouchPoints > 0 ||
    // @ts-ignore
    navigator.msMaxTouchPoints > 0
  );
};

// Screen size hook
export const useScreenSize = () => {
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768,
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      setScreenSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return screenSize;
};

// Breakpoint hook
export const useBreakpoint = () => {
  const { width } = useScreenSize();
  
  const getCurrentBreakpoint = (): Breakpoint => {
    if (width >= breakpoints['2xl']) return '2xl';
    if (width >= breakpoints.xl) return 'xl';
    if (width >= breakpoints.lg) return 'lg';
    if (width >= breakpoints.md) return 'md';
    if (width >= breakpoints.sm) return 'sm';
    return 'xs';
  };

  const isBreakpoint = (breakpoint: Breakpoint): boolean => {
    return width >= breakpoints[breakpoint];
  };

  const isBetween = (min: Breakpoint, max: Breakpoint): boolean => {
    return width >= breakpoints[min] && width < breakpoints[max];
  };

  return {
    current: getCurrentBreakpoint(),
    width,
    isXs: width < breakpoints.sm,
    isSm: isBetween('sm', 'md'),
    isMd: isBetween('md', 'lg'),
    isLg: isBetween('lg', 'xl'),
    isXl: isBetween('xl', '2xl'),
    is2Xl: width >= breakpoints['2xl'],
    isSmAndUp: isBreakpoint('sm'),
    isMdAndUp: isBreakpoint('md'),
    isLgAndUp: isBreakpoint('lg'),
    isXlAndUp: isBreakpoint('xl'),
    is2XlAndUp: isBreakpoint('2xl'),
    isBreakpoint,
    isBetween,
  };
};

// Device type hook
export const useDeviceType = (): DeviceType => {
  const { width } = useScreenSize();
  return getDeviceType(width);
};

// Touch device hook
export const useTouchDevice = (): boolean => {
  const [isTouch, setIsTouch] = useState(false);

  useEffect(() => {
    setIsTouch(isTouchDevice());
  }, []);

  return isTouch;
};

// Orientation hook
export const useOrientation = () => {
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleOrientationChange = () => {
      setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');
    };

    handleOrientationChange(); // Set initial value
    window.addEventListener('resize', handleOrientationChange);
    
    return () => window.removeEventListener('resize', handleOrientationChange);
  }, []);

  return orientation;
};

// Responsive value hook
export const useResponsiveValue = <T>(values: Partial<Record<Breakpoint, T>>): T | undefined => {
  const { current } = useBreakpoint();
  
  // Find the appropriate value for current breakpoint
  const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
  const currentIndex = breakpointOrder.indexOf(current);
  
  // Look for value starting from current breakpoint and going down
  for (let i = currentIndex; i < breakpointOrder.length; i++) {
    const breakpoint = breakpointOrder[i];
    if (values[breakpoint] !== undefined) {
      return values[breakpoint];
    }
  }
  
  return undefined;
};

// Container queries (experimental)
export const useContainerQuery = (containerRef: React.RefObject<HTMLElement>) => {
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    if (!containerRef.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setContainerSize({ width, height });
      }
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [containerRef]);

  return containerSize;
};

// Responsive grid utilities
export const getResponsiveColumns = (
  deviceType: DeviceType,
  options: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  } = {}
): number => {
  const defaults = {
    mobile: 1,
    tablet: 2,
    desktop: 3,
  };

  const columns = { ...defaults, ...options };

  switch (deviceType) {
    case 'mobile':
      return columns.mobile;
    case 'tablet':
      return columns.tablet;
    case 'desktop':
      return columns.desktop;
    default:
      return columns.desktop;
  }
};

// Touch-friendly sizing
export const touchTargetSize = {
  minimum: 44, // WCAG AA minimum
  recommended: 48, // Better for accessibility
  comfortable: 56, // Most comfortable for touch
} as const;

export const getTouchTargetClass = (size: keyof typeof touchTargetSize = 'minimum'): string => {
  const sizeValue = touchTargetSize[size];
  return `min-h-[${sizeValue}px] min-w-[${sizeValue}px]`;
};

// Responsive spacing utilities
export const responsiveSpacing = {
  xs: { padding: 'p-2', margin: 'm-2', gap: 'gap-2' },
  sm: { padding: 'p-3', margin: 'm-3', gap: 'gap-3' },
  md: { padding: 'p-4', margin: 'm-4', gap: 'gap-4' },
  lg: { padding: 'p-6', margin: 'm-6', gap: 'gap-6' },
  xl: { padding: 'p-8', margin: 'm-8', gap: 'gap-8' },
} as const;

export const getResponsiveSpacing = (breakpoint: Breakpoint) => {
  if (breakpoint === 'xs') return responsiveSpacing.xs;
  if (breakpoint === 'sm') return responsiveSpacing.sm;
  if (breakpoint === 'md') return responsiveSpacing.md;
  if (breakpoint === 'lg') return responsiveSpacing.lg;
  return responsiveSpacing.xl;
};

// Safe area utilities (for mobile devices with notches)
export const useSafeArea = () => {
  const [safeArea, setSafeArea] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const updateSafeArea = () => {
      const computedStyle = getComputedStyle(document.documentElement);
      
      setSafeArea({
        top: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-top)') || '0'),
        right: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-right)') || '0'),
        bottom: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
        left: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-left)') || '0'),
      });
    };

    updateSafeArea();
    window.addEventListener('resize', updateSafeArea);
    
    return () => window.removeEventListener('resize', updateSafeArea);
  }, []);

  return safeArea;
};

// Viewport height utilities (handles mobile browser address bar)
export const useViewportHeight = () => {
  const [viewportHeight, setViewportHeight] = useState(
    typeof window !== 'undefined' ? window.innerHeight : 768
  );

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const updateViewportHeight = () => {
      // Use visual viewport if available (better for mobile)
      const height = window.visualViewport?.height || window.innerHeight;
      setViewportHeight(height);
      
      // Update CSS custom property
      document.documentElement.style.setProperty('--vh', `${height * 0.01}px`);
    };

    updateViewportHeight();
    
    window.addEventListener('resize', updateViewportHeight);
    window.visualViewport?.addEventListener('resize', updateViewportHeight);
    
    return () => {
      window.removeEventListener('resize', updateViewportHeight);
      window.visualViewport?.removeEventListener('resize', updateViewportHeight);
    };
  }, []);

  return viewportHeight;
};

// Responsive text utilities
export const responsiveText = {
  xs: 'text-xs',
  sm: 'text-sm',
  base: 'text-base',
  lg: 'text-lg',
  xl: 'text-xl',
  '2xl': 'text-2xl',
  '3xl': 'text-3xl',
} as const;

export const getResponsiveTextSize = (
  deviceType: DeviceType,
  options: {
    mobile?: keyof typeof responsiveText;
    tablet?: keyof typeof responsiveText;
    desktop?: keyof typeof responsiveText;
  } = {}
): string => {
  const defaults = {
    mobile: 'sm' as const,
    tablet: 'base' as const,
    desktop: 'base' as const,
  };

  const sizes = { ...defaults, ...options };
  const size = sizes[deviceType];
  
  return responsiveText[size];
};

// Performance utilities for responsive images
export const getResponsiveImageSizes = (
  breakpoints: Partial<Record<Breakpoint, number>> = {}
): string => {
  const defaultSizes = {
    xs: 100,
    sm: 100,
    md: 50,
    lg: 33,
    xl: 25,
  };

  const sizes = { ...defaultSizes, ...breakpoints };
  
  return Object.entries(sizes)
    .map(([bp, size]) => {
      if (bp === 'xs') return `${size}vw`;
      return `(min-width: ${breakpoints[bp as Breakpoint] || 640}px) ${size}vw`;
    })
    .join(', ');
};
