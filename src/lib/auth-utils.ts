/**
 * Authentication Utilities
 * Utility functions for getting current user and tenant information
 */

import { getAuth } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { EvexUser, Tenant } from '@/types/firestore';

const auth = getAuth();

/**
 * Get current authenticated user
 */
export async function getCurrentUser(): Promise<EvexUser | null> {
  const firebaseUser = auth.currentUser;
  
  if (!firebaseUser) {
    return null;
  }

  try {
    const userDoc = await getDoc(doc(db, 'user_profiles', firebaseUser.uid));
    
    if (!userDoc.exists()) {
      return null;
    }

    return { id: userDoc.id, ...userDoc.data() } as EvexUser;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Get current tenant information
 */
export async function getCurrentTenant(): Promise<Tenant | null> {
  const currentUser = await getCurrentUser();
  
  if (!currentUser?.tenantId) {
    return null;
  }

  try {
    const tenantDoc = await getDoc(doc(db, 'tenants', currentUser.tenantId));
    
    if (!tenantDoc.exists()) {
      return null;
    }

    return { id: tenantDoc.id, ...tenantDoc.data() } as Tenant;
  } catch (error) {
    console.error('Error getting current tenant:', error);
    return null;
  }
}

/**
 * Check if user is authenticated
 */
export function isAuthenticated(): boolean {
  return !!auth.currentUser;
}

/**
 * Get current user ID
 */
export function getCurrentUserId(): string | null {
  return auth.currentUser?.uid || null;
}

/**
 * Get current Firebase Auth user ID token
 * This is the correct way to get the ID token for API calls
 */
export async function getCurrentUserIdToken(): Promise<string> {
  const firebaseUser = auth.currentUser;

  if (!firebaseUser) {
    throw new Error('User not authenticated');
  }

  return await firebaseUser.getIdToken();
}

/**
 * Get current user email
 */
export function getCurrentUserEmail(): string | null {
  return auth.currentUser?.email || null;
}
