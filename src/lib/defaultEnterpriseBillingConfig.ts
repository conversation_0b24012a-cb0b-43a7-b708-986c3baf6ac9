/**
 * Default Enterprise Billing Configuration for EVEXA
 * Pre-configured currencies, tax settings, and enterprise billing templates
 */

import { CurrencyConfig, TaxConfiguration } from '@/services/enterpriseBillingService';

export const defaultCurrencies: Omit<CurrencyConfig, 'id' | 'metadata'>[] = [
  {
    code: 'USD',
    name: 'US Dollar',
    symbol: '$',
    decimalPlaces: 2,
    isActive: true,
    exchangeRates: {
      USD: { rate: 1, lastUpdated: new Date(), source: 'base' }
    },
    formatting: {
      locale: 'en-US',
      position: 'before',
      spaceBetween: false
    }
  },
  {
    code: 'EUR',
    name: 'Euro',
    symbol: '€',
    decimalPlaces: 2,
    isActive: true,
    exchangeRates: {
      USD: { rate: 0.85, lastUpdated: new Date(), source: 'exchangerate-api.com' }
    },
    formatting: {
      locale: 'de-DE',
      position: 'after',
      spaceBetween: true
    }
  },
  {
    code: 'GBP',
    name: 'British Pound',
    symbol: '£',
    decimalPlaces: 2,
    isActive: true,
    exchangeRates: {
      USD: { rate: 0.73, lastUpdated: new Date(), source: 'exchangerate-api.com' }
    },
    formatting: {
      locale: 'en-GB',
      position: 'before',
      spaceBetween: false
    }
  },
  {
    code: 'CAD',
    name: 'Canadian Dollar',
    symbol: 'C$',
    decimalPlaces: 2,
    isActive: true,
    exchangeRates: {
      USD: { rate: 1.35, lastUpdated: new Date(), source: 'exchangerate-api.com' }
    },
    formatting: {
      locale: 'en-CA',
      position: 'before',
      spaceBetween: false
    }
  },
  {
    code: 'AUD',
    name: 'Australian Dollar',
    symbol: 'A$',
    decimalPlaces: 2,
    isActive: true,
    exchangeRates: {
      USD: { rate: 1.52, lastUpdated: new Date(), source: 'exchangerate-api.com' }
    },
    formatting: {
      locale: 'en-AU',
      position: 'before',
      spaceBetween: false
    }
  },
  {
    code: 'JPY',
    name: 'Japanese Yen',
    symbol: '¥',
    decimalPlaces: 0,
    isActive: true,
    exchangeRates: {
      USD: { rate: 149.50, lastUpdated: new Date(), source: 'exchangerate-api.com' }
    },
    formatting: {
      locale: 'ja-JP',
      position: 'before',
      spaceBetween: false
    }
  },
  {
    code: 'CHF',
    name: 'Swiss Franc',
    symbol: 'CHF',
    decimalPlaces: 2,
    isActive: true,
    exchangeRates: {
      USD: { rate: 0.88, lastUpdated: new Date(), source: 'exchangerate-api.com' }
    },
    formatting: {
      locale: 'de-CH',
      position: 'after',
      spaceBetween: true
    }
  },
  {
    code: 'SEK',
    name: 'Swedish Krona',
    symbol: 'kr',
    decimalPlaces: 2,
    isActive: true,
    exchangeRates: {
      USD: { rate: 10.85, lastUpdated: new Date(), source: 'exchangerate-api.com' }
    },
    formatting: {
      locale: 'sv-SE',
      position: 'after',
      spaceBetween: true
    }
  },
  {
    code: 'NOK',
    name: 'Norwegian Krone',
    symbol: 'kr',
    decimalPlaces: 2,
    isActive: true,
    exchangeRates: {
      USD: { rate: 10.95, lastUpdated: new Date(), source: 'exchangerate-api.com' }
    },
    formatting: {
      locale: 'nb-NO',
      position: 'after',
      spaceBetween: true
    }
  },
  {
    code: 'DKK',
    name: 'Danish Krone',
    symbol: 'kr',
    decimalPlaces: 2,
    isActive: true,
    exchangeRates: {
      USD: { rate: 6.85, lastUpdated: new Date(), source: 'exchangerate-api.com' }
    },
    formatting: {
      locale: 'da-DK',
      position: 'after',
      spaceBetween: true
    }
  }
];

export const defaultTaxConfigurations: Omit<TaxConfiguration, 'id' | 'metadata'>[] = [
  {
    tenantId: 'default',
    name: 'US Sales Tax Configuration',
    type: 'sales_tax',
    isActive: true,
    rules: [
      {
        id: 'us_general_sales_tax',
        name: 'General Sales Tax',
        rate: 8.25,
        applicableRegions: ['US', 'US-CA', 'US-NY', 'US-TX'],
        applicableProducts: ['software', 'services', 'subscriptions'],
        exemptions: ['non_profit', 'government'],
        effectiveDate: new Date('2024-01-01'),
        expiryDate: new Date('2024-12-31')
      },
      {
        id: 'us_digital_services_tax',
        name: 'Digital Services Tax',
        rate: 6.0,
        applicableRegions: ['US-WA', 'US-OR'],
        applicableProducts: ['software', 'digital_services'],
        exemptions: [],
        effectiveDate: new Date('2024-01-01')
      }
    ],
    regions: [
      {
        country: 'US',
        state: 'CA',
        taxId: 'CA-TAX-001',
        registrationNumber: 'SR-*********',
        isReverseChargeApplicable: false
      },
      {
        country: 'US',
        state: 'NY',
        taxId: 'NY-TAX-001',
        registrationNumber: 'ST-*********',
        isReverseChargeApplicable: false
      }
    ],
    automation: {
      enabled: true,
      provider: 'avalara',
      autoCalculate: true,
      autoFile: false
    }
  },
  {
    tenantId: 'default',
    name: 'EU VAT Configuration',
    type: 'vat',
    isActive: true,
    rules: [
      {
        id: 'eu_standard_vat',
        name: 'Standard VAT',
        rate: 20.0,
        applicableRegions: ['GB', 'DE', 'FR', 'IT', 'ES'],
        applicableProducts: ['software', 'services', 'subscriptions'],
        exemptions: ['b2b_reverse_charge'],
        effectiveDate: new Date('2024-01-01')
      },
      {
        id: 'eu_reduced_vat',
        name: 'Reduced VAT',
        rate: 5.0,
        applicableRegions: ['GB'],
        applicableProducts: ['educational_services'],
        exemptions: [],
        effectiveDate: new Date('2024-01-01')
      },
      {
        id: 'de_vat',
        name: 'German VAT',
        rate: 19.0,
        applicableRegions: ['DE'],
        applicableProducts: ['software', 'services'],
        exemptions: ['b2b_reverse_charge'],
        effectiveDate: new Date('2024-01-01')
      }
    ],
    regions: [
      {
        country: 'GB',
        taxId: 'GB-VAT-001',
        registrationNumber: 'GB*********',
        isReverseChargeApplicable: true
      },
      {
        country: 'DE',
        taxId: 'DE-VAT-001',
        registrationNumber: 'DE*********',
        isReverseChargeApplicable: true
      }
    ],
    automation: {
      enabled: true,
      provider: 'taxjar',
      autoCalculate: true,
      autoFile: true
    }
  },
  {
    tenantId: 'default',
    name: 'Canada GST/HST Configuration',
    type: 'gst',
    isActive: true,
    rules: [
      {
        id: 'ca_gst',
        name: 'Goods and Services Tax',
        rate: 5.0,
        applicableRegions: ['CA'],
        applicableProducts: ['software', 'services', 'subscriptions'],
        exemptions: ['basic_groceries', 'medical_services'],
        effectiveDate: new Date('2024-01-01')
      },
      {
        id: 'ca_hst_on',
        name: 'Harmonized Sales Tax - Ontario',
        rate: 13.0,
        applicableRegions: ['CA-ON'],
        applicableProducts: ['software', 'services', 'subscriptions'],
        exemptions: ['basic_groceries', 'medical_services'],
        effectiveDate: new Date('2024-01-01')
      },
      {
        id: 'ca_hst_bc',
        name: 'Provincial Sales Tax - British Columbia',
        rate: 7.0,
        applicableRegions: ['CA-BC'],
        applicableProducts: ['software', 'services'],
        exemptions: ['basic_groceries'],
        effectiveDate: new Date('2024-01-01')
      }
    ],
    regions: [
      {
        country: 'CA',
        state: 'ON',
        taxId: 'CA-GST-001',
        registrationNumber: '*********RT0001',
        isReverseChargeApplicable: false
      },
      {
        country: 'CA',
        state: 'BC',
        taxId: 'CA-GST-002',
        registrationNumber: '*********RT0001',
        isReverseChargeApplicable: false
      }
    ],
    automation: {
      enabled: true,
      provider: 'vertex',
      autoCalculate: true,
      autoFile: false
    }
  }
];

export const purchaseOrderTemplates = {
  standard: {
    name: 'Standard Purchase Order',
    description: 'Standard template for general purchases',
    approvalLevels: [
      { level: 1, title: 'Department Manager', amountThreshold: 1000 },
      { level: 2, title: 'Finance Director', amountThreshold: 10000 },
      { level: 3, title: 'CEO', amountThreshold: 50000 }
    ],
    defaultTerms: {
      paymentTerms: 'Net 30',
      deliveryTerms: 'FOB Destination',
      warrantyPeriod: '1 Year',
      penalties: 'Late delivery penalty: 1% per day'
    }
  },
  software: {
    name: 'Software Purchase Order',
    description: 'Template for software and SaaS purchases',
    approvalLevels: [
      { level: 1, title: 'IT Manager', amountThreshold: 5000 },
      { level: 2, title: 'CTO', amountThreshold: 25000 },
      { level: 3, title: 'CEO', amountThreshold: 100000 }
    ],
    defaultTerms: {
      paymentTerms: 'Net 15',
      deliveryTerms: 'Digital Delivery',
      warrantyPeriod: 'Support Period',
      penalties: 'SLA penalties as per contract'
    }
  },
  services: {
    name: 'Professional Services Purchase Order',
    description: 'Template for consulting and professional services',
    approvalLevels: [
      { level: 1, title: 'Project Manager', amountThreshold: 2500 },
      { level: 2, title: 'Department Head', amountThreshold: 15000 },
      { level: 3, title: 'VP', amountThreshold: 75000 }
    ],
    defaultTerms: {
      paymentTerms: 'Net 30',
      deliveryTerms: 'As per SOW',
      warrantyPeriod: '90 Days',
      penalties: 'Performance penalties as per contract'
    }
  }
};

export const contractTemplates = {
  saas: {
    name: 'SaaS Subscription Contract',
    description: 'Standard SaaS subscription agreement',
    defaultTerms: {
      autoRenewal: true,
      renewalPeriod: 12,
      terminationNotice: 30,
      paymentTerms: 'Net 30'
    },
    defaultPricing: {
      model: 'tiered' as const,
      tiers: [
        { name: 'Starter', minQuantity: 1, maxQuantity: 10, rate: 29 },
        { name: 'Professional', minQuantity: 11, maxQuantity: 50, rate: 25 },
        { name: 'Enterprise', minQuantity: 51, rate: 20 }
      ]
    },
    defaultSLA: {
      uptime: 99.9,
      responseTime: 4,
      resolutionTime: 24,
      penalties: [
        { metric: 'uptime', threshold: 99.0, penalty: 10 },
        { metric: 'response_time', threshold: 8, penalty: 5 }
      ]
    }
  },
  enterprise: {
    name: 'Enterprise License Agreement',
    description: 'Comprehensive enterprise software license',
    defaultTerms: {
      autoRenewal: false,
      renewalPeriod: 36,
      terminationNotice: 90,
      paymentTerms: 'Net 45'
    },
    defaultPricing: {
      model: 'hybrid' as const,
      baseAmount: 50000,
      usageRates: [
        { category: 'users', rate: 100, unit: 'user/month', minimumCommitment: 100 },
        { category: 'storage', rate: 5, unit: 'GB/month', minimumCommitment: 1000 }
      ]
    },
    defaultSLA: {
      uptime: 99.95,
      responseTime: 2,
      resolutionTime: 12,
      penalties: [
        { metric: 'uptime', threshold: 99.5, penalty: 25 },
        { metric: 'response_time', threshold: 4, penalty: 15 }
      ]
    }
  }
};

export function getCurrencyByCode(code: string): Omit<CurrencyConfig, 'id' | 'metadata'> | undefined {
  return defaultCurrencies.find(currency => currency.code === code);
}

export function getTaxConfigByRegion(country: string, state?: string): Omit<TaxConfiguration, 'id' | 'metadata'> | undefined {
  return defaultTaxConfigurations.find(config => 
    config.regions.some(region => 
      region.country === country && (!state || region.state === state)
    )
  );
}

export function formatCurrency(amount: number, currencyCode: string): string {
  const currency = getCurrencyByCode(currencyCode);
  if (!currency) return `${amount} ${currencyCode}`;

  const formatter = new Intl.NumberFormat(currency.formatting.locale, {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: currency.decimalPlaces,
    maximumFractionDigits: currency.decimalPlaces
  });

  return formatter.format(amount);
}
