/**
 * Email Service
 * Comprehensive email service for notifications and invitations
 */

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  variables: string[];
}

export interface EmailRecipient {
  email: string;
  name?: string;
  variables?: Record<string, string>;
}

export interface EmailOptions {
  to: EmailRecipient[];
  cc?: EmailRecipient[];
  bcc?: EmailRecipient[];
  subject: string;
  htmlContent?: string;
  textContent?: string;
  templateId?: string;
  templateVariables?: Record<string, string>;
  attachments?: EmailAttachment[];
  replyTo?: string;
  priority?: 'high' | 'normal' | 'low';
  trackOpens?: boolean;
  trackClicks?: boolean;
}

export interface EmailAttachment {
  filename: string;
  content: string; // base64 encoded
  contentType: string;
  disposition?: 'attachment' | 'inline';
  contentId?: string;
}

export interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
  details?: any;
}

export interface BulkEmailResult {
  totalSent: number;
  successful: number;
  failed: number;
  results: EmailResult[];
  errors: string[];
}

// Email Templates
export const EMAIL_TEMPLATES: Record<string, EmailTemplate> = {
  USER_INVITATION: {
    id: 'user_invitation',
    name: 'User Invitation',
    subject: 'You\'re invited to join {{tenantName}} on EVEXA',
    htmlContent: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>EVEXA Invitation</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: white; padding: 30px; border: 1px solid #e1e5e9; }
          .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; }
          .btn { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; font-weight: bold; }
          .btn:hover { background: #5a67d8; }
          .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">{{tenantLogo}}</div>
            <h1>You're Invited!</h1>
          </div>
          <div class="content">
            <p>Hello {{recipientName}},</p>
            <p>{{inviterName}} has invited you to join <strong>{{tenantName}}</strong> on EVEXA, the comprehensive exhibition management platform.</p>
            <p>As a {{personaName}}, you'll have access to:</p>
            <ul>
              {{#each moduleAccess}}
              <li>{{this}}</li>
              {{/each}}
            </ul>
            <p style="text-align: center; margin: 30px 0;">
              <a href="{{invitationLink}}" class="btn">Accept Invitation</a>
            </p>
            <p><strong>Important:</strong> This invitation will expire in {{expirationDays}} days.</p>
            <p>If you have any questions, please contact {{inviterEmail}} or our support team.</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 EVEXA. All rights reserved.</p>
            <p>This invitation was sent by {{tenantName}}.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    textContent: `
      You're invited to join {{tenantName}} on EVEXA!
      
      Hello {{recipientName}},
      
      {{inviterName}} has invited you to join {{tenantName}} on EVEXA, the comprehensive exhibition management platform.
      
      As a {{personaName}}, you'll have access to various modules and features.
      
      To accept this invitation, please visit: {{invitationLink}}
      
      Important: This invitation will expire in {{expirationDays}} days.
      
      If you have any questions, please contact {{inviterEmail}} or our support team.
      
      © 2024 EVEXA. All rights reserved.
      This invitation was sent by {{tenantName}}.
    `,
    variables: ['tenantName', 'tenantLogo', 'recipientName', 'inviterName', 'inviterEmail', 'personaName', 'moduleAccess', 'invitationLink', 'expirationDays']
  },

  PASSWORD_RESET: {
    id: 'password_reset',
    name: 'Password Reset',
    subject: 'Reset your EVEXA password',
    htmlContent: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc3545; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: white; padding: 30px; border: 1px solid #e1e5e9; }
          .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; }
          .btn { display: inline-block; padding: 12px 24px; background: #dc3545; color: white; text-decoration: none; border-radius: 6px; font-weight: bold; }
          .btn:hover { background: #c82333; }
          .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">EVEXA</div>
            <h1>Password Reset</h1>
          </div>
          <div class="content">
            <p>Hello {{userName}},</p>
            <p>We received a request to reset your password for your EVEXA account.</p>
            <p style="text-align: center; margin: 30px 0;">
              <a href="{{resetLink}}" class="btn">Reset Password</a>
            </p>
            <p><strong>Important:</strong> This link will expire in {{expirationHours}} hours.</p>
            <p>If you didn't request this password reset, please ignore this email or contact our support team if you have concerns.</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 EVEXA. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    textContent: `
      Password Reset Request
      
      Hello {{userName}},
      
      We received a request to reset your password for your EVEXA account.
      
      To reset your password, please visit: {{resetLink}}
      
      Important: This link will expire in {{expirationHours}} hours.
      
      If you didn't request this password reset, please ignore this email or contact our support team if you have concerns.
      
      © 2024 EVEXA. All rights reserved.
    `,
    variables: ['userName', 'resetLink', 'expirationHours']
  },

  WELCOME_EMAIL: {
    id: 'welcome_email',
    name: 'Welcome Email',
    subject: 'Welcome to {{tenantName}} on EVEXA!',
    htmlContent: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to EVEXA</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: white; padding: 30px; border: 1px solid #e1e5e9; }
          .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; }
          .btn { display: inline-block; padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 6px; font-weight: bold; }
          .btn:hover { background: #218838; }
          .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">{{tenantLogo}}</div>
            <h1>Welcome to EVEXA!</h1>
          </div>
          <div class="content">
            <p>Hello {{userName}},</p>
            <p>Welcome to <strong>{{tenantName}}</strong> on EVEXA! We're excited to have you on board.</p>
            <p>Your account has been set up with the <strong>{{personaName}}</strong> role, giving you access to:</p>
            <ul>
              {{#each moduleAccess}}
              <li>{{this}}</li>
              {{/each}}
            </ul>
            <p style="text-align: center; margin: 30px 0;">
              <a href="{{dashboardLink}}" class="btn">Go to Dashboard</a>
            </p>
            <p>Here are some quick tips to get started:</p>
            <ul>
              <li>Complete your profile in the settings section</li>
              <li>Explore the dashboard to familiarize yourself with the interface</li>
              <li>Check out our help documentation for detailed guides</li>
            </ul>
            <p>If you need any assistance, don't hesitate to reach out to your team admin or our support team.</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 EVEXA. All rights reserved.</p>
            <p>You're receiving this email because you joined {{tenantName}}.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    textContent: `
      Welcome to EVEXA!
      
      Hello {{userName}},
      
      Welcome to {{tenantName}} on EVEXA! We're excited to have you on board.
      
      Your account has been set up with the {{personaName}} role, giving you access to various modules and features.
      
      To get started, visit your dashboard: {{dashboardLink}}
      
      Here are some quick tips to get started:
      - Complete your profile in the settings section
      - Explore the dashboard to familiarize yourself with the interface
      - Check out our help documentation for detailed guides
      
      If you need any assistance, don't hesitate to reach out to your team admin or our support team.
      
      © 2024 EVEXA. All rights reserved.
      You're receiving this email because you joined {{tenantName}}.
    `,
    variables: ['userName', 'tenantName', 'tenantLogo', 'personaName', 'moduleAccess', 'dashboardLink']
  }
};

/**
 * Email Service Class
 */
export class EmailService {
  private apiKey: string;
  private apiUrl: string;
  private fromEmail: string;
  private fromName: string;

  constructor() {
    // In production, these would come from environment variables
    this.apiKey = process.env.EMAIL_API_KEY || '';
    this.apiUrl = process.env.EMAIL_API_URL || 'https://api.emailservice.com/v1';
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
    this.fromName = process.env.FROM_NAME || 'EVEXA';
  }

  /**
   * Send a single email
   */
  async sendEmail(options: EmailOptions): Promise<EmailResult> {
    try {
      // In a real implementation, this would integrate with services like:
      // - SendGrid
      // - Mailgun
      // - Amazon SES
      // - Postmark
      // - etc.

      // For now, we'll simulate the email sending
      console.log('Sending email:', {
        to: options.to.map(r => r.email),
        subject: options.subject,
        templateId: options.templateId
      });

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Simulate success/failure (90% success rate)
      const success = Math.random() > 0.1;

      if (success) {
        return {
          success: true,
          messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };
      } else {
        return {
          success: false,
          error: 'Failed to send email - simulated failure'
        };
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send bulk emails
   */
  async sendBulkEmails(emails: EmailOptions[]): Promise<BulkEmailResult> {
    const results: EmailResult[] = [];
    const errors: string[] = [];
    let successful = 0;
    let failed = 0;

    for (const email of emails) {
      try {
        const result = await this.sendEmail(email);
        results.push(result);
        
        if (result.success) {
          successful++;
        } else {
          failed++;
          if (result.error) {
            errors.push(result.error);
          }
        }
      } catch (error) {
        failed++;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push(errorMessage);
        results.push({
          success: false,
          error: errorMessage
        });
      }
    }

    return {
      totalSent: emails.length,
      successful,
      failed,
      results,
      errors
    };
  }

  /**
   * Send email using template
   */
  async sendTemplateEmail(
    templateId: string,
    recipients: EmailRecipient[],
    variables: Record<string, string> = {},
    options: Partial<EmailOptions> = {}
  ): Promise<EmailResult> {
    const template = EMAIL_TEMPLATES[templateId];
    if (!template) {
      return {
        success: false,
        error: `Template ${templateId} not found`
      };
    }

    // Replace template variables
    const subject = this.replaceVariables(template.subject, variables);
    const htmlContent = this.replaceVariables(template.htmlContent, variables);
    const textContent = this.replaceVariables(template.textContent, variables);

    return this.sendEmail({
      to: recipients,
      subject,
      htmlContent,
      textContent,
      ...options
    });
  }

  /**
   * Replace template variables
   */
  private replaceVariables(content: string, variables: Record<string, string>): string {
    let result = content;
    
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value);
    });

    return result;
  }

  /**
   * Validate email address
   */
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Get available templates
   */
  getTemplates(): EmailTemplate[] {
    return Object.values(EMAIL_TEMPLATES);
  }

  /**
   * Get template by ID
   */
  getTemplate(templateId: string): EmailTemplate | null {
    return EMAIL_TEMPLATES[templateId] || null;
  }
}

// Export singleton instance
export const emailService = new EmailService();
