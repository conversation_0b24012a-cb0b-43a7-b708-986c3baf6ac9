/**
 * EVEXA Core Collection Creation Script
 * 
 * Creates Tier 1 professional collections with StandardMetadata
 * and proper snake_case naming convention
 */

import { db } from '@/lib/firebase';
import { collection, doc, setDoc, getDoc } from 'firebase/firestore';
import { EVEXA_DATA_SCHEMA, StandardMetadata, EVEXA_TENANT_ID } from '@/lib/professionalDataManager';

/**
 * Create standard metadata for new documents
 */
function createStandardMetadata(userId: string = 'system'): StandardMetadata {
  const now = new Date();
  return {
    tenant_id: EVEXA_TENANT_ID,
    created_at: now,
    updated_at: now,
    created_by: userId,
    updated_by: userId,
    version: 1,
    // deleted_at and deleted_by are undefined (not deleted)
  };
}

/**
 * Core collection initialization data
 */
const CORE_COLLECTION_INITIALIZERS = {
  // User Management Module
  [EVEXA_DATA_SCHEMA.CORE_COLLECTIONS.user_profiles]: {
    description: 'Primary user entity with professional metadata',
    sampleFields: ['id', 'email', 'display_name', 'role', 'status'],
    tier: 1,
    module: 'User Management'
  },
  
  [EVEXA_DATA_SCHEMA.CORE_COLLECTIONS.user_groups]: {
    description: 'User grouping and permissions management',
    sampleFields: ['id', 'name', 'permissions', 'member_ids'],
    tier: 1,
    module: 'User Management'
  },
  
  [EVEXA_DATA_SCHEMA.CORE_COLLECTIONS.user_settings]: {
    description: 'User preferences and configuration settings',
    sampleFields: ['id', 'user_id', 'dashboard_layout', 'notification_preferences'],
    tier: 1,
    module: 'User Management'
  },

  // Exhibition Management Module
  [EVEXA_DATA_SCHEMA.CORE_COLLECTIONS.exhibitions]: {
    description: 'Primary exhibition entity for event management',
    sampleFields: ['id', 'name', 'start_date', 'end_date', 'venue_name', 'status'],
    tier: 1,
    module: 'Exhibition Management'
  },
  
  [EVEXA_DATA_SCHEMA.CORE_COLLECTIONS.exhibition_events]: {
    description: 'Exhibition events and activities management',
    sampleFields: ['id', 'exhibition_id', 'event_name', 'event_type', 'start_date'],
    tier: 1,
    module: 'Exhibition Management'
  },
  
  [EVEXA_DATA_SCHEMA.CORE_COLLECTIONS.exhibition_tasks]: {
    description: 'Exhibition task management and tracking',
    sampleFields: ['id', 'exhibition_id', 'title', 'assigned_to', 'status', 'priority'],
    tier: 1,
    module: 'Exhibition Management'
  },

  // Lead Management Module
  [EVEXA_DATA_SCHEMA.CORE_COLLECTIONS.lead_contacts]: {
    description: 'Lead contact information and management',
    sampleFields: ['id', 'exhibition_id', 'name', 'email', 'company', 'status'],
    tier: 1,
    module: 'Lead Management'
  },
  
  [EVEXA_DATA_SCHEMA.CORE_COLLECTIONS.lead_segments]: {
    description: 'Lead categorization and segmentation',
    sampleFields: ['id', 'name', 'criteria', 'lead_count'],
    tier: 1,
    module: 'Lead Management'
  },
  
  [EVEXA_DATA_SCHEMA.CORE_COLLECTIONS.lead_communications]: {
    description: 'Lead interaction history and communications',
    sampleFields: ['id', 'lead_id', 'communication_type', 'content', 'timestamp'],
    tier: 1,
    module: 'Lead Management'
  }
};

/**
 * Financial collection initialization data
 */
const FINANCIAL_COLLECTION_INITIALIZERS = {
  [EVEXA_DATA_SCHEMA.FINANCIAL_COLLECTIONS.budget_allocations]: {
    description: 'Budget planning and allocation management',
    sampleFields: ['id', 'exhibition_id', 'category', 'allocated_amount', 'spent_amount'],
    tier: 1,
    module: 'Financial Management'
  },
  
  [EVEXA_DATA_SCHEMA.FINANCIAL_COLLECTIONS.expense_records]: {
    description: 'Expense tracking and management',
    sampleFields: ['id', 'exhibition_id', 'amount', 'category', 'receipt_url', 'status'],
    tier: 1,
    module: 'Financial Management'
  },
  
  [EVEXA_DATA_SCHEMA.FINANCIAL_COLLECTIONS.purchase_requests]: {
    description: 'Purchase request workflow management',
    sampleFields: ['id', 'exhibition_id', 'title', 'estimated_cost', 'status'],
    tier: 1,
    module: 'Financial Management'
  },
  
  [EVEXA_DATA_SCHEMA.FINANCIAL_COLLECTIONS.purchase_orders]: {
    description: 'Purchase order processing and tracking',
    sampleFields: ['id', 'purchase_request_id', 'vendor_id', 'total_amount', 'status'],
    tier: 1,
    module: 'Financial Management'
  },
  
  [EVEXA_DATA_SCHEMA.FINANCIAL_COLLECTIONS.purchase_invoices]: {
    description: 'Invoice processing and payment tracking',
    sampleFields: ['id', 'purchase_order_id', 'amount', 'due_date', 'payment_status'],
    tier: 1,
    module: 'Financial Management'
  },
  
  [EVEXA_DATA_SCHEMA.FINANCIAL_COLLECTIONS.vendor_profiles]: {
    description: 'Vendor information and management',
    sampleFields: ['id', 'company_name', 'contact_email', 'services', 'rating'],
    tier: 1,
    module: 'Financial Management'
  },
  
  [EVEXA_DATA_SCHEMA.FINANCIAL_COLLECTIONS.vendor_contracts]: {
    description: 'Vendor agreements and contract management',
    sampleFields: ['id', 'vendor_id', 'contract_type', 'start_date', 'end_date'],
    tier: 1,
    module: 'Financial Management'
  },
  
  [EVEXA_DATA_SCHEMA.FINANCIAL_COLLECTIONS.vendor_reviews]: {
    description: 'Vendor performance tracking and reviews',
    sampleFields: ['id', 'vendor_id', 'exhibition_id', 'rating', 'comments'],
    tier: 1,
    module: 'Financial Management'
  }
};

/**
 * Create core collections with initialization documents
 */
export async function createCoreCollections(): Promise<{
  success: boolean;
  createdCollections: string[];
  errors: string[];
}> {
  console.log('🏗️ Creating Tier 1 professional collections...');
  
  const createdCollections: string[] = [];
  const errors: string[] = [];
  
  const allInitializers = {
    ...CORE_COLLECTION_INITIALIZERS,
    ...FINANCIAL_COLLECTION_INITIALIZERS
  };

  try {
    for (const [collectionName, config] of Object.entries(allInitializers)) {
      try {
        console.log(`   📁 Creating collection: ${collectionName}`);
        
        // Create initialization document with professional metadata
        const initDoc = {
          id: `${collectionName}_init`,
          _collection_info: {
            name: collectionName,
            description: config.description,
            tier: config.tier,
            module: config.module,
            sample_fields: config.sampleFields,
            created_by_system: true,
            is_initialization_document: true
          },
          ...createStandardMetadata('system')
        };
        
        // Create the collection by adding the initialization document
        const collectionRef = collection(db, collectionName);
        const docRef = doc(collectionRef, `${collectionName}_init`);
        
        await setDoc(docRef, initDoc);
        
        console.log(`   ✅ Created: ${collectionName}`);
        createdCollections.push(collectionName);
        
      } catch (error) {
        console.error(`   ❌ Failed to create ${collectionName}:`, error);
        errors.push(`Failed to create ${collectionName}: ${error}`);
      }
    }
    
    console.log(`🏗️ Core collection creation complete: ${createdCollections.length} collections created`);
    
    return {
      success: errors.length === 0,
      createdCollections,
      errors
    };
    
  } catch (error) {
    console.error('Core collection creation failed:', error);
    return {
      success: false,
      createdCollections,
      errors: [...errors, `Core collection creation failed: ${error}`]
    };
  }
}

/**
 * Validate core collections exist and have proper structure
 */
export async function validateCoreCollections(): Promise<{
  isValid: boolean;
  validCollections: string[];
  invalidCollections: string[];
  issues: string[];
}> {
  console.log('🔍 Validating core collections...');
  
  const validCollections: string[] = [];
  const invalidCollections: string[] = [];
  const issues: string[] = [];
  
  const allCollections = [
    ...Object.values(EVEXA_DATA_SCHEMA.CORE_COLLECTIONS),
    ...Object.values(EVEXA_DATA_SCHEMA.FINANCIAL_COLLECTIONS)
  ];
  
  try {
    for (const collectionName of allCollections) {
      try {
        const collectionRef = collection(db, collectionName);
        const initDocRef = doc(collectionRef, `${collectionName}_init`);
        
        // Check if initialization document exists
        const docSnap = await getDoc(initDocRef);
        
        if (docSnap.exists()) {
          const data = docSnap.data();
          
          // Validate professional metadata
          if (data.tenant_id && data.created_at && data.updated_at && 
              data.created_by && data.updated_by && typeof data.version === 'number') {
            validCollections.push(collectionName);
            console.log(`   ✅ Valid: ${collectionName}`);
          } else {
            invalidCollections.push(collectionName);
            issues.push(`Collection ${collectionName} missing professional metadata`);
            console.log(`   ⚠️ Invalid metadata: ${collectionName}`);
          }
        } else {
          invalidCollections.push(collectionName);
          issues.push(`Collection ${collectionName} initialization document not found`);
          console.log(`   ❌ Missing: ${collectionName}`);
        }
        
      } catch (error) {
        invalidCollections.push(collectionName);
        issues.push(`Failed to validate ${collectionName}: ${error}`);
        console.log(`   ❌ Error validating ${collectionName}: ${error}`);
      }
    }
    
    const isValid = invalidCollections.length === 0;
    console.log(`🔍 Validation complete: ${isValid ? 'ALL VALID' : 'ISSUES FOUND'}`);
    
    return {
      isValid,
      validCollections,
      invalidCollections,
      issues
    };
    
  } catch (error) {
    console.error('Core collection validation failed:', error);
    return {
      isValid: false,
      validCollections,
      invalidCollections,
      issues: [...issues, `Validation failed: ${error}`]
    };
  }
}
