/**
 * Standardized Titles and Labels for EVEXA
 * Ensures consistent naming across the entire application
 */

// Main Module Titles
export const MODULE_TITLES = {
  // Core Modules
  DASHBOARD: 'Dashboard',
  TASKS: 'Tasks',
  EXHIBITIONS: 'Exhibitions',
  EVENTS: 'Events',
  LEADS: 'Leads',
  BUDGETS: 'Budgets',
  EXPENSES: 'Expenses',
  VENDORS: 'Vendors',
  LOGISTICS: 'Logistics',
  ANALYTICS: 'Analytics',
  REPORTS: 'Reports',
  
  // Management Modules
  USER_MANAGEMENT: 'User Management',
  ACCESS_CONTROL: 'Access Control',
  SETTINGS: 'Settings',
  
  // Advanced Modules
  AI_ASSISTANT: 'AI Assistant',
  SOCIAL_MEDIA: 'Social Media Hub',
  MARKETING: 'Marketing',
  COMMUNICATIONS: 'Communications',
  TRAVEL: 'Travel Management',
  APPROVALS: 'Approvals',
  PERFORMANCE: 'Performance',
  
  // Admin <PERSON>dules
  SUPER_ADMIN: 'Super Admin',
  TENANT_ADMIN: 'Tenant Administration',
  SYSTEM_ADMIN: 'System Administration',
} as const;

// Page Descriptions
export const MODULE_DESCRIPTIONS = {
  DASHBOARD: 'Overview of your exhibitions, tasks, and key metrics',
  TASKS: 'Manage and track project tasks and to-dos',
  EXHIBITIONS: 'Plan and manage your exhibition participation',
  EVENTS: 'Organize and coordinate exhibition events',
  LEADS: 'Track and nurture potential customers and contacts',
  BUDGETS: 'Plan and monitor exhibition budgets',
  EXPENSES: 'Track and manage exhibition expenses',
  VENDORS: 'Manage vendor relationships and contracts',
  LOGISTICS: 'Coordinate shipping, setup, and logistics',
  ANALYTICS: 'Analyze performance and generate insights',
  REPORTS: 'Generate comprehensive reports and analytics',
  
  USER_MANAGEMENT: 'Manage user accounts, roles, and permissions',
  ACCESS_CONTROL: 'Configure access permissions and security',
  SETTINGS: 'Configure system and user preferences',
  
  AI_ASSISTANT: 'AI-powered assistance and automation',
  SOCIAL_MEDIA: 'Manage social media presence and campaigns',
  MARKETING: 'Plan and execute marketing campaigns',
  COMMUNICATIONS: 'Manage communications and messaging',
  TRAVEL: 'Plan and manage business travel',
  APPROVALS: 'Manage approval workflows and processes',
  PERFORMANCE: 'Monitor and analyze performance metrics',
  
  SUPER_ADMIN: 'System-wide administration and management',
  TENANT_ADMIN: 'Tenant-specific administration',
  SYSTEM_ADMIN: 'System configuration and maintenance',
} as const;

// View Titles (for different views within modules)
export const VIEW_TITLES = {
  // Task Views
  TASK_BOARD: 'Task Board',
  TASK_TABLE: 'Task List',
  TASK_GANTT: 'Gantt Chart',
  TASK_CALENDAR: 'Task Calendar',
  
  // Exhibition Views
  EXHIBITION_GRID: 'Exhibition Grid',
  EXHIBITION_LIST: 'Exhibition List',
  EXHIBITION_CALENDAR: 'Exhibition Calendar',
  EXHIBITION_MAP: 'Exhibition Map',
  
  // Analytics Views
  ANALYTICS_OVERVIEW: 'Analytics Overview',
  ANALYTICS_DETAILED: 'Detailed Analytics',
  ANALYTICS_COMPARISON: 'Comparison View',
  
  // Report Views
  REPORTS_DASHBOARD: 'Reports Dashboard',
  REPORTS_BUILDER: 'Report Builder',
  REPORTS_SCHEDULED: 'Scheduled Reports',
} as const;

// Action Labels
export const ACTION_LABELS = {
  // CRUD Operations
  CREATE: 'Create',
  CREATE_NEW: 'Create New',
  EDIT: 'Edit',
  UPDATE: 'Update',
  DELETE: 'Delete',
  SAVE: 'Save',
  CANCEL: 'Cancel',
  
  // Task Actions
  CREATE_TASK: 'Create Task',
  EDIT_TASK: 'Edit Task',
  COMPLETE_TASK: 'Complete Task',
  ASSIGN_TASK: 'Assign Task',
  
  // Exhibition Actions
  CREATE_EXHIBITION: 'Create Exhibition',
  EDIT_EXHIBITION: 'Edit Exhibition',
  REGISTER_EXHIBITION: 'Register for Exhibition',
  
  // General Actions
  SEARCH: 'Search',
  FILTER: 'Filter',
  SORT: 'Sort',
  EXPORT: 'Export',
  IMPORT: 'Import',
  REFRESH: 'Refresh',
  RESET: 'Reset',
  CLEAR: 'Clear',
  APPLY: 'Apply',
  
  // AI Actions
  AI_CREATE: 'AI Create',
  AI_SUGGEST: 'AI Suggest',
  AI_ANALYZE: 'AI Analyze',
} as const;

// Status Labels
export const STATUS_LABELS = {
  LOADING: 'Loading...',
  SAVING: 'Saving...',
  SAVED: 'Saved',
  ERROR: 'Error',
  SUCCESS: 'Success',
  PENDING: 'Pending',
  COMPLETED: 'Completed',
  IN_PROGRESS: 'In Progress',
  CANCELLED: 'Cancelled',
} as const;

// Form Labels
export const FORM_LABELS = {
  REQUIRED_FIELD: 'Required field',
  OPTIONAL_FIELD: 'Optional',
  SEARCH_PLACEHOLDER: 'Search...',
  SELECT_PLACEHOLDER: 'Select an option...',
  DATE_PLACEHOLDER: 'Select date...',
  TIME_PLACEHOLDER: 'Select time...',
  FILE_UPLOAD_PLACEHOLDER: 'Choose file or drag and drop',
} as const;

// Helper function to get consistent page title
export function getPageTitle(module: keyof typeof MODULE_TITLES, action?: string): string {
  const baseTitle = MODULE_TITLES[module];
  return action ? `${action} ${baseTitle}` : baseTitle;
}

// Helper function to get consistent action label
export function getActionLabel(action: keyof typeof ACTION_LABELS, entity?: string): string {
  const baseAction = ACTION_LABELS[action];
  return entity ? `${baseAction} ${entity}` : baseAction;
}

// Export types for TypeScript
export type ModuleTitle = keyof typeof MODULE_TITLES;
export type ViewTitle = keyof typeof VIEW_TITLES;
export type ActionLabel = keyof typeof ACTION_LABELS;
