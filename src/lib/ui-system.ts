/**
 * EVEXA Centralized UI System
 * 
 * This file provides centralized configuration for all UI components
 * to ensure consistency across the entire application.
 */

import { cva, type VariantProps } from "class-variance-authority";

// ============================================================================
// DESIGN TOKENS
// ============================================================================

export const UI_TOKENS = {
  // Spacing
  spacing: {
    xs: "0.25rem",    // 4px
    sm: "0.5rem",     // 8px
    md: "0.75rem",    // 12px
    lg: "1rem",       // 16px
    xl: "1.5rem",     // 24px
    "2xl": "2rem",    // 32px
    "3xl": "3rem",    // 48px
  },

  // Border radius
  radius: {
    none: "0",
    sm: "0.125rem",   // 2px
    md: "0.375rem",   // 6px
    lg: "0.5rem",     // 8px
    xl: "0.75rem",    // 12px
    "2xl": "1rem",    // 16px
    full: "9999px",
  },

  // Typography
  typography: {
    sizes: {
      xs: "0.75rem",    // 12px
      sm: "0.875rem",   // 14px
      base: "1rem",     // 16px
      lg: "1.125rem",   // 18px
      xl: "1.25rem",    // 20px
      "2xl": "1.5rem",  // 24px
      "3xl": "1.875rem", // 30px
    },
    weights: {
      normal: "400",
      medium: "500",
      semibold: "600",
      bold: "700",
    },
    lineHeights: {
      tight: "1.25",
      normal: "1.5",
      relaxed: "1.75",
    },
  },

  // Shadows
  shadows: {
    sm: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
    md: "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
    lg: "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",
    xl: "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)",
    glow: "0 0 20px hsl(var(--primary) / 0.3)",
    glass: "0 8px 32px 0 rgba(31, 38, 135, 0.37)",
  },

  // Transitions
  transitions: {
    fast: "150ms cubic-bezier(0.4, 0, 0.2, 1)",
    normal: "200ms cubic-bezier(0.4, 0, 0.2, 1)",
    slow: "300ms cubic-bezier(0.4, 0, 0.2, 1)",
  },
} as const;

// ============================================================================
// BUTTON SYSTEM
// ============================================================================

export const buttonVariants = cva(
  // Base styles - consistent across all buttons
  [
    "inline-flex items-center justify-center gap-2",
    "whitespace-nowrap rounded-md text-sm font-medium",
    "ring-offset-background transition-all duration-200",
    "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
    "disabled:pointer-events-none disabled:opacity-50",
    "[&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
    "relative overflow-hidden",
    // Consistent hover and active states
    "hover:scale-[1.02] active:scale-[0.98]",
  ],
  {
    variants: {
      variant: {
        default: [
          "bg-primary text-primary-foreground",
          "hover:bg-primary/90 hover:shadow-glow",
        ],
        destructive: [
          "bg-destructive text-destructive-foreground",
          "hover:bg-destructive/90 hover:shadow-lg",
        ],
        outline: [
          "border border-input bg-background",
          "hover:bg-accent hover:text-accent-foreground hover:border-primary/50",
        ],
        secondary: [
          "bg-secondary text-secondary-foreground",
          "hover:bg-secondary/80 hover:shadow-md",
        ],
        ghost: [
          "hover:bg-accent hover:text-accent-foreground",
        ],
        link: [
          "text-primary underline-offset-4",
          "hover:underline",
        ],
        glass: [
          "glass text-foreground",
          "hover:glass-hover hover:shadow-glass",
        ],
        gradient: [
          "gradient-primary text-primary-foreground",
          "hover:shadow-glow",
        ],
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        xl: "h-12 rounded-md px-10 text-base",
        icon: "h-10 w-10",
        "icon-sm": "h-9 w-9",
        "icon-lg": "h-11 w-11",
      },
      alignment: {
        left: "justify-start",
        center: "justify-center",
        right: "justify-end",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      alignment: "center",
    },
  }
);

// ============================================================================
// INPUT SYSTEM
// ============================================================================

export const inputVariants = cva(
  [
    "flex w-full rounded-md border border-input bg-background",
    "px-3 py-2 text-base text-foreground",
    "ring-offset-background transition-colors",
    "file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground",
    "placeholder:text-muted-foreground",
    "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
    "disabled:cursor-not-allowed disabled:opacity-50",
    "hover:border-ring",
    "md:text-sm",
  ],
  {
    variants: {
      size: {
        default: "h-10",
        sm: "h-9 text-sm",
        lg: "h-11",
      },
      variant: {
        default: "",
        error: "border-destructive focus-visible:ring-destructive",
        success: "border-success focus-visible:ring-success",
        warning: "border-warning focus-visible:ring-warning",
      },
    },
    defaultVariants: {
      size: "default",
      variant: "default",
    },
  }
);

// ============================================================================
// CARD SYSTEM
// ============================================================================

export const cardVariants = cva(
  [
    "rounded-lg border bg-card text-card-foreground shadow-sm",
    "transition-all duration-200",
  ],
  {
    variants: {
      variant: {
        default: "",
        elevated: "shadow-md hover:shadow-lg",
        glass: "glass border-glass-border",
        gradient: "gradient-primary text-primary-foreground border-0",
      },
      padding: {
        none: "",
        sm: "p-4",
        md: "p-6",
        lg: "p-8",
      },
      interactive: {
        true: "hover:shadow-md hover:scale-[1.01] cursor-pointer",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      padding: "md",
      interactive: false,
    },
  }
);

// ============================================================================
// FORM SYSTEM
// ============================================================================

export const formVariants = {
  field: cva([
    "space-y-2",
  ]),
  
  label: cva([
    "text-sm font-medium leading-none",
    "peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
  ]),
  
  description: cva([
    "text-sm text-muted-foreground",
  ]),
  
  message: cva([
    "text-sm font-medium",
  ], {
    variants: {
      variant: {
        default: "text-destructive",
        success: "text-success",
        warning: "text-warning",
        info: "text-info",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }),
};

// ============================================================================
// LAYOUT SYSTEM
// ============================================================================

export const layoutVariants = {
  container: cva([
    "mx-auto w-full",
  ], {
    variants: {
      size: {
        sm: "max-w-2xl",
        md: "max-w-4xl",
        lg: "max-w-6xl",
        xl: "max-w-7xl",
        full: "max-w-full",
      },
      padding: {
        none: "",
        sm: "px-4",
        md: "px-6",
        lg: "px-8",
      },
    },
    defaultVariants: {
      size: "lg",
      padding: "md",
    },
  }),

  stack: cva([
    "flex flex-col",
  ], {
    variants: {
      gap: {
        none: "",
        xs: "gap-1",
        sm: "gap-2",
        md: "gap-4",
        lg: "gap-6",
        xl: "gap-8",
      },
      align: {
        start: "items-start",
        center: "items-center",
        end: "items-end",
        stretch: "items-stretch",
      },
    },
    defaultVariants: {
      gap: "md",
      align: "stretch",
    },
  }),

  grid: cva([
    "grid",
  ], {
    variants: {
      cols: {
        1: "grid-cols-1",
        2: "grid-cols-1 md:grid-cols-2",
        3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
        4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
        6: "grid-cols-2 md:grid-cols-3 lg:grid-cols-6",
        12: "grid-cols-12",
      },
      gap: {
        none: "",
        xs: "gap-1",
        sm: "gap-2",
        md: "gap-4",
        lg: "gap-6",
        xl: "gap-8",
      },
    },
    defaultVariants: {
      cols: 1,
      gap: "md",
    },
  }),
};

// ============================================================================
// ANIMATION SYSTEM
// ============================================================================

export const animationVariants = {
  fadeIn: "animate-fade-in",
  slideUp: "animate-slide-in-up",
  scaleIn: "animate-scale-in",
  bounceIn: "animate-bounce-in",
  
  // Interactive animations
  hoverLift: "hover-lift",
  hoverGlow: "hover-glow",
  hoverScale: "interactive-scale",
  
  // Loading states
  pulse: "animate-pulse",
  spin: "animate-spin",
  bounce: "animate-bounce",
};

// ============================================================================
// TYPE EXPORTS
// ============================================================================

export type ButtonVariants = VariantProps<typeof buttonVariants>;
export type InputVariants = VariantProps<typeof inputVariants>;
export type CardVariants = VariantProps<typeof cardVariants>;
