/**
 * EVEXA Runtime Validation System v1.0
 * 
 * Prevents creation of unauthorized collections and validates data structures
 * in real-time to maintain data integrity and prevent chaos
 */

import { 
  COLLECTION_SCHEMAS, 
  validateCollectionData, 
  getCollectionMetadata,
  getAllCollectionNames 
} from './collectionSchemas';
import { EVEXA_DATA_SCHEMA, ALL_COLLECTIONS, EVEXA_TENANT_ID } from './professionalDataManager';
import { db } from './firebase';
import { collection, doc, setDoc, addDoc, updateDoc, deleteDoc, getDoc } from 'firebase/firestore';

/**
 * Runtime Validation Error Types
 */
export class UnauthorizedCollectionError extends Error {
  constructor(collectionName: string) {
    super(`CHAOS PREVENTION: Unauthorized collection "${collectionName}" blocked. Only standardized collections are allowed.`);
    this.name = 'UnauthorizedCollectionError';
  }
}

export class DataValidationError extends Error {
  constructor(collectionName: string, errors: string[]) {
    super(`DATA VALIDATION FAILED for collection "${collectionName}": ${errors.join(', ')}`);
    this.name = 'DataValidationError';
  }
}

export class TenantIsolationError extends Error {
  constructor(expectedTenant: string, actualTenant: string) {
    super(`TENANT ISOLATION VIOLATION: Expected tenant "${expectedTenant}", got "${actualTenant}"`);
    this.name = 'TenantIsolationError';
  }
}

/**
 * Runtime Validation Configuration
 */
interface ValidationConfig {
  enforceSchemaValidation: boolean;
  enforceCollectionWhitelist: boolean;
  enforceTenantIsolation: boolean;
  logViolations: boolean;
  blockUnauthorizedOperations: boolean;
}

const DEFAULT_VALIDATION_CONFIG: ValidationConfig = {
  enforceSchemaValidation: true,
  enforceCollectionWhitelist: true,
  enforceTenantIsolation: true,
  logViolations: true,
  blockUnauthorizedOperations: true,
};

/**
 * Runtime Validator Class
 */
export class RuntimeValidator {
  private config: ValidationConfig;
  private violationLog: Array<{
    timestamp: Date;
    type: string;
    collection: string;
    error: string;
    data?: any;
  }> = [];

  constructor(config: Partial<ValidationConfig> = {}) {
    this.config = { ...DEFAULT_VALIDATION_CONFIG, ...config };
  }

  /**
   * Validate collection name against whitelist
   */
  private validateCollectionName(collectionName: string): void {
    if (!this.config.enforceCollectionWhitelist) return;

    const authorizedCollections = getAllCollectionNames();
    
    if (!authorizedCollections.includes(collectionName)) {
      const violation = {
        timestamp: new Date(),
        type: 'unauthorized_collection',
        collection: collectionName,
        error: `Collection "${collectionName}" not in authorized schema`,
      };

      this.logViolation(violation);

      if (this.config.blockUnauthorizedOperations) {
        throw new UnauthorizedCollectionError(collectionName);
      }
    }
  }

  /**
   * Validate data structure against schema
   */
  private validateDataStructure(collectionName: string, data: any): any {
    if (!this.config.enforceSchemaValidation) return data;

    const validation = validateCollectionData(collectionName, data);

    if (!validation.isValid) {
      const violation = {
        timestamp: new Date(),
        type: 'schema_validation_failure',
        collection: collectionName,
        error: validation.errors.join(', '),
        data: data,
      };

      this.logViolation(violation);

      if (this.config.blockUnauthorizedOperations) {
        throw new DataValidationError(collectionName, validation.errors);
      }
    }

    return validation.data || data;
  }

  /**
   * Validate tenant isolation
   */
  private validateTenantIsolation(data: any): void {
    if (!this.config.enforceTenantIsolation) return;

    if (data.tenant_id && data.tenant_id !== EVEXA_TENANT_ID) {
      const violation = {
        timestamp: new Date(),
        type: 'tenant_isolation_violation',
        collection: 'unknown',
        error: `Invalid tenant ID: ${data.tenant_id}`,
        data: data,
      };

      this.logViolation(violation);

      if (this.config.blockUnauthorizedOperations) {
        throw new TenantIsolationError(EVEXA_TENANT_ID, data.tenant_id);
      }
    }
  }

  /**
   * Log validation violations
   */
  private logViolation(violation: any): void {
    if (!this.config.logViolations) return;

    this.violationLog.push(violation);
    console.error('🚨 RUNTIME VALIDATION VIOLATION:', violation);

    // Keep only last 1000 violations to prevent memory issues
    if (this.violationLog.length > 1000) {
      this.violationLog = this.violationLog.slice(-1000);
    }
  }

  /**
   * Validated document creation
   */
  async createDocument(collectionName: string, data: any, docId?: string): Promise<any> {
    // Validate collection name
    this.validateCollectionName(collectionName);

    // Validate data structure
    const validatedData = this.validateDataStructure(collectionName, data);

    // Validate tenant isolation
    this.validateTenantIsolation(validatedData);

    // Perform the actual Firestore operation
    const collectionRef = collection(db, collectionName);
    
    if (docId) {
      const docRef = doc(collectionRef, docId);
      await setDoc(docRef, validatedData);
      return { id: docId, ...validatedData };
    } else {
      const docRef = await addDoc(collectionRef, validatedData);
      return { id: docRef.id, ...validatedData };
    }
  }

  /**
   * Validated document update
   */
  async updateDocument(collectionName: string, docId: string, data: any): Promise<any> {
    // Validate collection name
    this.validateCollectionName(collectionName);

    // Get existing document to merge with updates
    const docRef = doc(db, collectionName, docId);
    const existingDoc = await getDoc(docRef);
    
    if (!existingDoc.exists()) {
      throw new Error(`Document ${docId} not found in collection ${collectionName}`);
    }

    const existingData = existingDoc.data();
    const mergedData = { ...existingData, ...data };

    // Validate merged data structure
    const validatedData = this.validateDataStructure(collectionName, mergedData);

    // Validate tenant isolation
    this.validateTenantIsolation(validatedData);

    // Perform the actual Firestore operation
    await updateDoc(docRef, data); // Only update the changed fields
    return { id: docId, ...validatedData };
  }

  /**
   * Validated document deletion (soft delete)
   */
  async deleteDocument(collectionName: string, docId: string, userId: string = 'system'): Promise<void> {
    // Validate collection name
    this.validateCollectionName(collectionName);

    // Perform soft delete by updating metadata
    const docRef = doc(db, collectionName, docId);
    const softDeleteData = {
      deleted_at: new Date(),
      deleted_by: userId,
      updated_at: new Date(),
      updated_by: userId,
    };

    await updateDoc(docRef, softDeleteData);
  }

  /**
   * Hard delete (only for authorized operations)
   */
  async hardDeleteDocument(collectionName: string, docId: string): Promise<void> {
    // Validate collection name
    this.validateCollectionName(collectionName);

    // Log the hard delete operation
    this.logViolation({
      timestamp: new Date(),
      type: 'hard_delete_operation',
      collection: collectionName,
      error: `Hard delete performed on document ${docId}`,
    });

    // Perform the actual hard delete
    const docRef = doc(db, collectionName, docId);
    await deleteDoc(docRef);
  }

  /**
   * Batch validation for multiple documents
   */
  async validateBatch(operations: Array<{
    type: 'create' | 'update' | 'delete';
    collection: string;
    docId?: string;
    data?: any;
  }>): Promise<{
    valid: boolean;
    errors: string[];
    validatedOperations: any[];
  }> {
    const errors: string[] = [];
    const validatedOperations: any[] = [];

    for (const operation of operations) {
      try {
        // Validate collection name
        this.validateCollectionName(operation.collection);

        if (operation.type === 'create' || operation.type === 'update') {
          // Validate data structure
          const validatedData = this.validateDataStructure(operation.collection, operation.data);
          
          // Validate tenant isolation
          this.validateTenantIsolation(validatedData);

          validatedOperations.push({
            ...operation,
            data: validatedData,
          });
        } else {
          validatedOperations.push(operation);
        }
      } catch (error) {
        errors.push(`${operation.collection}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      validatedOperations,
    };
  }

  /**
   * Get validation statistics
   */
  getValidationStats(): {
    totalViolations: number;
    violationsByType: Record<string, number>;
    violationsByCollection: Record<string, number>;
    recentViolations: any[];
  } {
    const violationsByType: Record<string, number> = {};
    const violationsByCollection: Record<string, number> = {};

    this.violationLog.forEach(violation => {
      violationsByType[violation.type] = (violationsByType[violation.type] || 0) + 1;
      violationsByCollection[violation.collection] = (violationsByCollection[violation.collection] || 0) + 1;
    });

    return {
      totalViolations: this.violationLog.length,
      violationsByType,
      violationsByCollection,
      recentViolations: this.violationLog.slice(-10), // Last 10 violations
    };
  }

  /**
   * Clear violation log
   */
  clearViolationLog(): void {
    this.violationLog = [];
  }

  /**
   * Update validation configuration
   */
  updateConfig(newConfig: Partial<ValidationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current configuration
   */
  getConfig(): ValidationConfig {
    return { ...this.config };
  }
}

/**
 * Global runtime validator instance
 */
export const runtimeValidator = new RuntimeValidator();

/**
 * Convenience functions for common operations
 */
export const validateAndCreate = (collectionName: string, data: any, docId?: string) => 
  runtimeValidator.createDocument(collectionName, data, docId);

export const validateAndUpdate = (collectionName: string, docId: string, data: any) => 
  runtimeValidator.updateDocument(collectionName, docId, data);

export const validateAndDelete = (collectionName: string, docId: string, userId?: string) => 
  runtimeValidator.deleteDocument(collectionName, docId, userId);

export const validateBatch = (operations: any[]) => 
  runtimeValidator.validateBatch(operations);

/**
 * Emergency override functions (use with extreme caution)
 */
export const emergencyDisableValidation = () => {
  console.warn('🚨 EMERGENCY: Runtime validation disabled! This should only be used for critical system recovery.');
  runtimeValidator.updateConfig({
    enforceSchemaValidation: false,
    enforceCollectionWhitelist: false,
    enforceTenantIsolation: false,
    blockUnauthorizedOperations: false,
  });
};

export const emergencyEnableValidation = () => {
  console.log('✅ Runtime validation re-enabled.');
  runtimeValidator.updateConfig(DEFAULT_VALIDATION_CONFIG);
};
