/**
 * EVEXA Chaos Detection Initialization
 * 
 * Automatically initializes and starts the chaos detection system
 * when the application loads. Only runs in development mode for safety.
 */

import { chaosDetectionService } from '@/services/chaosDetectionService';
import { DataIntegrityValidator } from '@/lib/dataIntegrityValidator';

let isInitialized = false;

/**
 * Initialize chaos detection system
 */
export async function initializeChaosDetection(): Promise<void> {
  // Only run in development mode
  if (process.env.NODE_ENV !== 'development') {
    console.log('🔍 Chaos detection disabled in production mode');
    return;
  }

  // Prevent multiple initializations
  if (isInitialized) {
    console.log('🔍 Chaos detection already initialized');
    return;
  }

  try {
    console.log('🚨 Initializing EVEXA Chaos Detection System...');
    
    // Start the chaos detection service
    await chaosDetectionService.startMonitoring();
    
    // Set up global error handlers for chaos detection
    setupGlobalErrorHandlers();
    
    // Mark as initialized
    isInitialized = true;
    
    console.log('✅ Chaos Detection System initialized successfully');
    
    // Log initialization event
    await logInitializationEvent();
    
  } catch (error) {
    console.error('❌ Failed to initialize chaos detection:', error);
  }
}

/**
 * Setup global error handlers to catch potential chaos events
 */
function setupGlobalErrorHandlers(): void {
  // Handle unhandled promise rejections
  if (typeof window !== 'undefined') {
    window.addEventListener('unhandledrejection', (event) => {
      if (event.reason?.message?.includes('collection') || 
          event.reason?.message?.includes('firestore')) {
        console.warn('🚨 Potential data chaos detected in unhandled rejection:', event.reason);
      }
    });
    
    // Handle general errors
    window.addEventListener('error', (event) => {
      if (event.error?.message?.includes('collection') || 
          event.error?.message?.includes('firestore')) {
        console.warn('🚨 Potential data chaos detected in error:', event.error);
      }
    });
  }
}

/**
 * Log initialization event to monitoring
 */
async function logInitializationEvent(): Promise<void> {
  try {
    // Import security service dynamically to avoid circular dependencies
    const { securityMonitoringService } = await import('@/services/securityMonitoringService');
    
    await securityMonitoringService.logSecurityEvent({
      id: `chaos_init_${Date.now()}`,
      type: 'system_startup',
      severity: 'low',
      userId: 'system',
      tenantId: 'evexa-development-company',
      ipAddress: 'localhost',
      userAgent: 'EVEXA-ChaosDetection',
      details: {
        component: 'chaos_detection_system',
        action: 'initialization',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV
      }
    });
  } catch (error) {
    console.error('Failed to log chaos detection initialization:', error);
  }
}

/**
 * Shutdown chaos detection system
 */
export async function shutdownChaosDetection(): Promise<void> {
  if (!isInitialized) {
    return;
  }
  
  try {
    console.log('🛑 Shutting down EVEXA Chaos Detection System...');
    
    await chaosDetectionService.stopMonitoring();
    
    isInitialized = false;
    
    console.log('✅ Chaos Detection System shutdown complete');
    
  } catch (error) {
    console.error('❌ Failed to shutdown chaos detection:', error);
  }
}

/**
 * Check if chaos detection is initialized
 */
export function isChaosDetectionInitialized(): boolean {
  return isInitialized;
}

/**
 * Auto-initialize chaos detection when module loads (client-side only)
 */
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Initialize after a short delay to ensure other systems are ready
  setTimeout(() => {
    initializeChaosDetection().catch(error => {
      console.error('Auto-initialization of chaos detection failed:', error);
    });
  }, 3000); // 3 second delay
}
