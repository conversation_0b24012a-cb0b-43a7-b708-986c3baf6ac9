/**
 * EVEXA Data Integrity Validator
 * 
 * Prevents future collection chaos by validating:
 * - Collection naming standards
 * - Tenant scoping consistency
 * - Metadata requirements
 * - Document structure integrity
 */

import { db } from '@/lib/firebase';
import { collection, getDocs, doc, getDoc } from 'firebase/firestore';
// Import directly to avoid circular dependency
import { EVEXA_DATA_SCHEMA } from '@/lib/professionalDataManager';
import { getAllCollectionNames } from '@/lib/collectionSchemas';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  summary: {
    totalCollections: number;
    validCollections: number;
    invalidCollections: number;
    totalDocuments: number;
    validDocuments: number;
    invalidDocuments: number;
  };
}

export interface DocumentValidationResult {
  documentId: string;
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface CollectionValidationResult {
  collectionName: string;
  isValid: boolean;
  errors: string[];
  warnings: string[];
  documentCount: number;
  validDocuments: number;
  invalidDocuments: number;
  documents: DocumentValidationResult[];
}

/**
 * Data Integrity Validator Class
 */
export class DataIntegrityValidator {
  private errors: string[] = [];
  private warnings: string[] = [];
  private static monitoringEnabled = false;
  private static monitoringInterval: NodeJS.Timeout | null = null;
  private static chaosDetectionCallbacks: Array<(violation: ChaosViolation) => void> = [];

  /**
   * Validate collection naming standards
   */
  validateCollectionNaming(collectionName: string): boolean {
    // Get all valid collection names from the new schema system
    const validCollections = getAllCollectionNames();

    // Check if collection is in approved list
    if (!validCollections.includes(collectionName)) {
      this.errors.push(`Invalid collection name: ${collectionName}. Must be in standardized collection schemas.`);
      return false;
    }

    // Check naming convention (snake_case)
    if (!/^[a-z][a-z0-9_]*[a-z0-9]$/.test(collectionName)) {
      this.errors.push(`Invalid naming convention: ${collectionName}. Must use snake_case.`);
      return false;
    }

    return true;
  }

  /**
   * Validate document structure and metadata
   */
  validateDocument(documentData: any, documentId: string): DocumentValidationResult {
    const result: DocumentValidationResult = {
      documentId,
      isValid: true,
      errors: [],
      warnings: []
    };

    // Check required metadata fields
    const expectedTenantId = 'evexa-development-company'; // Standard dev tenant ID
    if (!documentData.tenantId) {
      result.errors.push('Missing required field: tenantId');
      result.isValid = false;
    } else if (documentData.tenantId !== expectedTenantId) {
      result.errors.push(`Invalid tenantId: ${documentData.tenantId}. Expected: ${expectedTenantId}`);
      result.isValid = false;
    }

    if (!documentData.createdAt) {
      result.errors.push('Missing required field: createdAt');
      result.isValid = false;
    }

    if (!documentData.updatedAt) {
      result.errors.push('Missing required field: updatedAt');
      result.isValid = false;
    }

    if (!documentData.version) {
      result.warnings.push('Missing recommended field: version');
    }

    // Check for deprecated fields
    const deprecatedFields = ['_id', 'timestamp', 'created', 'modified'];
    deprecatedFields.forEach(field => {
      if (documentData[field]) {
        result.warnings.push(`Deprecated field found: ${field}`);
      }
    });

    return result;
  }

  /**
   * Validate entire collection
   */
  async validateCollection(collectionName: string): Promise<CollectionValidationResult> {
    const result: CollectionValidationResult = {
      collectionName,
      isValid: true,
      errors: [],
      warnings: [],
      documentCount: 0,
      validDocuments: 0,
      invalidDocuments: 0,
      documents: []
    };

    try {
      // Validate collection name
      if (!this.validateCollectionNaming(collectionName)) {
        result.isValid = false;
        result.errors.push(`Invalid collection name: ${collectionName}`);
        return result;
      }

      // Get all documents in collection
      const querySnapshot = await getDocs(collection(db, collectionName));
      result.documentCount = querySnapshot.docs.length;

      if (querySnapshot.empty) {
        result.warnings.push('Collection is empty');
        return result;
      }

      // Validate each document
      querySnapshot.docs.forEach(docSnapshot => {
        const documentData = docSnapshot.data();
        const docValidation = this.validateDocument(documentData, docSnapshot.id);
        
        result.documents.push(docValidation);
        
        if (docValidation.isValid) {
          result.validDocuments++;
        } else {
          result.invalidDocuments++;
          result.isValid = false;
        }

        // Aggregate errors and warnings
        result.errors.push(...docValidation.errors.map(err => `Doc ${docSnapshot.id}: ${err}`));
        result.warnings.push(...docValidation.warnings.map(warn => `Doc ${docSnapshot.id}: ${warn}`));
      });

    } catch (error) {
      result.isValid = false;
      result.errors.push(`Failed to validate collection: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Validate entire database
   */
  async validateDatabase(): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      summary: {
        totalCollections: 0,
        validCollections: 0,
        invalidCollections: 0,
        totalDocuments: 0,
        validDocuments: 0,
        invalidDocuments: 0
      }
    };

    try {
      // Check for unauthorized collections
      const unauthorizedCollections: string[] = [];
      
      // Note: In a real implementation, you'd need to list all collections
      // For now, we'll validate only the known collections
      
      // Get all valid collection names from EVEXA_DATA_SCHEMA
      const allCollectionNames = [
        ...Object.values(EVEXA_DATA_SCHEMA.CORE_COLLECTIONS),
        ...Object.values(EVEXA_DATA_SCHEMA.FINANCIAL_COLLECTIONS),
        ...Object.values(EVEXA_DATA_SCHEMA.COMMUNICATION_COLLECTIONS),
        ...Object.values(EVEXA_DATA_SCHEMA.LOGISTICS_COLLECTIONS),
        ...Object.values(EVEXA_DATA_SCHEMA.SYSTEM_COLLECTIONS),
        ...Object.values(EVEXA_DATA_SCHEMA.EXTENDED_COLLECTIONS)
      ];

      for (const collectionName of allCollectionNames) {
        try {
          const collectionResult = await this.validateCollection(collectionName);
          
          result.summary.totalCollections++;
          result.summary.totalDocuments += collectionResult.documentCount;
          result.summary.validDocuments += collectionResult.validDocuments;
          result.summary.invalidDocuments += collectionResult.invalidDocuments;

          if (collectionResult.isValid) {
            result.summary.validCollections++;
          } else {
            result.summary.invalidCollections++;
            result.isValid = false;
          }

          result.errors.push(...collectionResult.errors);
          result.warnings.push(...collectionResult.warnings);

        } catch (error) {
          result.summary.invalidCollections++;
          result.isValid = false;
          result.errors.push(`Failed to validate collection ${collectionName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Check for tenant consistency
      if (result.summary.invalidDocuments > 0) {
        result.errors.push(`Found ${result.summary.invalidDocuments} documents with invalid metadata`);
      }

    } catch (error) {
      result.isValid = false;
      result.errors.push(`Database validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Prevent collection chaos by checking before operations
   */
  static async preventCollectionChaos(collectionName: string): Promise<boolean> {
    const validator = new DataIntegrityValidator();

    if (!validator.validateCollectionNaming(collectionName)) {
      console.error(`🚨 COLLECTION CHAOS PREVENTION: Blocked invalid collection: ${collectionName}`);
      console.error(`🚨 REMINDER: We implemented professional schema validation!`);
      console.error(`🚨 ONLY use standardized snake_case names from collection schemas`);
      throw new Error(`CHAOS PREVENTION: Invalid collection name "${collectionName}". Use standardized names from collection schemas to maintain data integrity.`);
    }

    // Additional strict checks
    if (collectionName.includes('-')) {
      throw new Error(`CHAOS PREVENTION: kebab-case not allowed: "${collectionName}". Use snake_case only.`);
    }

    if (/[A-Z]/.test(collectionName)) {
      throw new Error(`CHAOS PREVENTION: camelCase not allowed: "${collectionName}". Use snake_case only.`);
    }

    console.log(`✅ Collection name validated: ${collectionName}`);
    return true;
  }

  /**
   * Validate document before adding/updating
   */
  static validateDocumentBeforeWrite(documentData: any): boolean {
    const validator = new DataIntegrityValidator();
    const result = validator.validateDocument(documentData, 'temp-id');

    if (!result.isValid) {
      console.error(`🚨 DOCUMENT VALIDATION FAILED:`, result.errors);
      console.error(`🚨 REMINDER: We just cleaned up 411 chaotic documents!`);
      console.error(`🚨 ALL documents must have proper metadata and tenant scoping`);
      throw new Error(`CHAOS PREVENTION: Document validation failed - ${result.errors.join(', ')}. Ensure proper metadata to prevent recreating data chaos.`);
    }

    if (result.warnings.length > 0) {
      console.warn(`⚠️ DOCUMENT WARNINGS:`, result.warnings);
    }

    console.log(`✅ Document validated successfully`);
    return true;
  }

  /**
   * Start automated chaos detection monitoring
   */
  static startChaosDetection(): void {
    if (this.monitoringEnabled) {
      console.log('🔍 Chaos detection already running');
      return;
    }

    console.log('🚨 Starting automated chaos detection system...');
    this.monitoringEnabled = true;

    // Monitor every 30 seconds in development, 5 minutes in production
    const interval = process.env.NODE_ENV === 'development' ? 30000 : 300000;

    this.monitoringInterval = setInterval(async () => {
      await this.performChaosDetectionScan();
    }, interval);

    console.log(`✅ Chaos detection monitoring started (interval: ${interval/1000}s)`);
  }

  /**
   * Stop automated chaos detection monitoring
   */
  static stopChaosDetection(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.monitoringEnabled = false;
    console.log('🛑 Chaos detection monitoring stopped');
  }

  /**
   * Register callback for chaos violations
   */
  static onChaosDetected(callback: (violation: ChaosViolation) => void): void {
    this.chaosDetectionCallbacks.push(callback);
  }

  /**
   * Perform comprehensive chaos detection scan
   */
  private static async performChaosDetectionScan(): Promise<void> {
    try {
      console.log('🔍 Performing chaos detection scan...');

      // Import here to avoid circular dependency
      const { professionalDataManager } = await import('@/lib/professionalDataManager');

      // Get all collections in Firebase
      const allCollections = await professionalDataManager.getAllFirebaseCollections();

      // Check for unauthorized collections
      await this.detectUnauthorizedCollections(allCollections);

      // Check for naming violations
      await this.detectNamingViolations(allCollections);

      // Check for data integrity issues
      await this.detectDataIntegrityIssues();

      console.log('✅ Chaos detection scan completed');

    } catch (error) {
      console.error('❌ Chaos detection scan failed:', error);

      const violation: ChaosViolation = {
        type: 'monitoring_failure',
        severity: 'high',
        description: `Chaos detection scan failed: ${error}`,
        timestamp: new Date(),
        collectionName: 'system',
        details: { error: String(error) }
      };

      this.triggerChaosAlert(violation);
    }
  }

  /**
   * Detect unauthorized collections
   */
  private static async detectUnauthorizedCollections(allCollections: string[]): Promise<void> {
    const { getAllCollectionNames } = await import('@/lib/collectionSchemas');

    const authorizedCollections = getAllCollectionNames();

    for (const collection of allCollections) {
      if (!authorizedCollections.includes(collection)) {
        const violation: ChaosViolation = {
          type: 'unauthorized_collection',
          severity: 'critical',
          description: `Unauthorized collection detected: ${collection}. Only standardized schema collections are allowed.`,
          timestamp: new Date(),
          collectionName: collection,
          details: {
            authorizedCollections: authorizedCollections.length,
            detectedCollection: collection,
            availableCollections: authorizedCollections.slice(0, 10) // Show first 10 for reference
          }
        };

        this.triggerChaosAlert(violation);
      }
    }
  }

  /**
   * Detect naming convention violations
   */
  private static async detectNamingViolations(allCollections: string[]): Promise<void> {
    for (const collection of allCollections) {
      // Check snake_case naming
      if (!/^[a-z][a-z0-9_]*[a-z0-9]$/.test(collection)) {
        const violation: ChaosViolation = {
          type: 'naming_violation',
          severity: 'high',
          description: `Collection naming violation: ${collection} (must use snake_case)`,
          timestamp: new Date(),
          collectionName: collection,
          details: {
            expectedPattern: 'snake_case',
            actualName: collection
          }
        };

        this.triggerChaosAlert(violation);
      }
    }
  }

  /**
   * Detect data integrity issues
   */
  private static async detectDataIntegrityIssues(): Promise<void> {
    try {
      const { professionalDataManager } = await import('@/lib/professionalDataManager');
      const validation = await professionalDataManager.validateDataConsistency();

      if (!validation.isValid) {
        for (const issue of validation.issues) {
          const violation: ChaosViolation = {
            type: 'data_integrity_violation',
            severity: 'medium',
            description: `Data integrity issue: ${issue}`,
            timestamp: new Date(),
            collectionName: 'multiple',
            details: {
              issue,
              collectionStats: validation.collectionStats
            }
          };

          this.triggerChaosAlert(violation);
        }
      }
    } catch (error) {
      console.error('Failed to check data integrity:', error);
    }
  }

  /**
   * Trigger chaos alert and notify callbacks
   */
  private static triggerChaosAlert(violation: ChaosViolation): void {
    console.error('🚨 CHAOS VIOLATION DETECTED 🚨', violation);

    // Notify all registered callbacks
    this.chaosDetectionCallbacks.forEach(callback => {
      try {
        callback(violation);
      } catch (error) {
        console.error('Chaos detection callback failed:', error);
      }
    });

    // Log to security monitoring if available
    this.logToSecurityMonitoring(violation);
  }

  /**
   * Log chaos violation to security monitoring
   */
  private static async logToSecurityMonitoring(violation: ChaosViolation): Promise<void> {
    try {
      // Import security service dynamically to avoid circular dependencies
      const { securityMonitoringService } = await import('@/services/securityMonitoringService');

      await securityMonitoringService.logSecurityEvent({
        id: `chaos_${Date.now()}`,
        type: 'data_integrity_violation',
        severity: violation.severity === 'critical' ? 'critical' : 'high',
        userId: 'system',
        tenantId: 'evexa-development-company',
        ipAddress: 'localhost',
        userAgent: 'EVEXA-ChaosDetection',
        details: {
          chaosViolationType: violation.type,
          description: violation.description,
          collectionName: violation.collectionName,
          ...violation.details
        }
      });
    } catch (error) {
      console.error('Failed to log chaos violation to security monitoring:', error);
    }
  }
}

/**
 * Chaos Violation Interface
 */
export interface ChaosViolation {
  type: 'unauthorized_collection' | 'naming_violation' | 'data_integrity_violation' | 'monitoring_failure';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  timestamp: Date;
  collectionName: string;
  details: Record<string, any>;
}
