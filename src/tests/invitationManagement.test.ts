/**
 * Invitation Management System Tests
 * Comprehensive tests for invitation creation, management, and acceptance
 */

import { InvitationManagementService, UserInvitation } from '@/services/invitationManagementService';
import { InvitationScheduledJobsService } from '@/services/invitationScheduledJobs';

// Mock Firebase
jest.mock('@/lib/firebase', () => ({
  db: {},
}));

jest.mock('firebase/firestore', () => ({
  collection: jest.fn(),
  doc: jest.fn(),
  addDoc: jest.fn(),
  updateDoc: jest.fn(),
  getDoc: jest.fn(),
  getDocs: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  orderBy: jest.fn(),
  limit: jest.fn(),
  serverTimestamp: jest.fn(() => new Date()),
  Timestamp: {
    fromDate: jest.fn((date) => ({ toDate: () => date }))
  },
  writeBatch: jest.fn(() => ({
    update: jest.fn(),
    delete: jest.fn(),
    commit: jest.fn()
  }))
}));

// Mock Enhanced Email Service
jest.mock('@/services/enhancedEmailService', () => ({
  EnhancedEmailService: jest.fn().mockImplementation(() => ({
    sendInvitationEmail: jest.fn().mockResolvedValue({
      success: true,
      messageId: 'test-message-id'
    })
  }))
}));

// Mock crypto
jest.mock('crypto', () => ({
  randomBytes: jest.fn(() => ({ toString: () => 'mockedhex' })),
  createHash: jest.fn(() => ({
    update: jest.fn().mockReturnThis(),
    digest: jest.fn(() => 'mockedhash')
  }))
}));

describe('Invitation Management System', () => {
  const mockTenantId = 'test-tenant-123';
  let invitationService: InvitationManagementService;

  beforeEach(() => {
    jest.clearAllMocks();
    invitationService = new InvitationManagementService(mockTenantId);
    
    // Set up environment
    process.env.NEXT_PUBLIC_APP_URL = 'https://app.evexa.com';
  });

  describe('InvitationManagementService', () => {
    describe('createInvitation', () => {
      it('should create and send invitation successfully', async () => {
        const { addDoc, getDocs } = require('firebase/firestore');
        
        // Mock no existing invitation
        getDocs.mockResolvedValue({ empty: true });
        
        // Mock successful document creation
        addDoc.mockResolvedValue({ id: 'invitation-123' });

        const request = {
          tenantId: mockTenantId,
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          personaId: 'persona-123',
          invitedBy: 'admin-123',
          invitedByName: 'Admin User'
        };

        const result = await invitationService.createInvitation(request);

        expect(result).toBeDefined();
        expect(result.email).toBe(request.email);
        expect(result.status).toBe('pending');
        expect(result.token).toContain('inv_');
        expect(result.invitationUrl).toContain('invitation/');
        expect(addDoc).toHaveBeenCalled();
      });

      it('should reject invitation for existing pending user', async () => {
        const { getDocs } = require('firebase/firestore');
        
        // Mock existing pending invitation
        getDocs.mockResolvedValue({
          empty: false,
          docs: [{
            id: 'existing-invitation',
            data: () => ({
              status: 'pending',
              email: '<EMAIL>'
            })
          }]
        });

        const request = {
          tenantId: mockTenantId,
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          personaId: 'persona-123',
          invitedBy: 'admin-123',
          invitedByName: 'Admin User'
        };

        await expect(invitationService.createInvitation(request))
          .rejects.toThrow('User already has a pending invitation');
      });

      it('should reject invitation with mismatched tenant ID', async () => {
        const request = {
          tenantId: 'different-tenant',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          personaId: 'persona-123',
          invitedBy: 'admin-123',
          invitedByName: 'Admin User'
        };

        await expect(invitationService.createInvitation(request))
          .rejects.toThrow('Tenant ID mismatch');
      });
    });

    describe('getInvitationByToken', () => {
      it('should retrieve invitation by token', async () => {
        const { getDocs } = require('firebase/firestore');
        
        const mockInvitation = {
          id: 'invitation-123',
          email: '<EMAIL>',
          status: 'pending',
          sentAt: { toDate: () => new Date() },
          expiresAt: { toDate: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) },
          createdAt: { toDate: () => new Date() },
          updatedAt: { toDate: () => new Date() }
        };

        getDocs.mockResolvedValue({
          empty: false,
          docs: [{
            id: mockInvitation.id,
            data: () => mockInvitation
          }]
        });

        const result = await invitationService.getInvitationByToken('test-token');

        expect(result).toBeDefined();
        expect(result?.id).toBe(mockInvitation.id);
        expect(result?.email).toBe(mockInvitation.email);
      });

      it('should return null for non-existent token', async () => {
        const { getDocs } = require('firebase/firestore');
        
        getDocs.mockResolvedValue({ empty: true });

        const result = await invitationService.getInvitationByToken('non-existent-token');

        expect(result).toBeNull();
      });
    });

    describe('acceptInvitation', () => {
      it('should accept valid pending invitation', async () => {
        const { getDocs, updateDoc } = require('firebase/firestore');
        
        const mockInvitation = {
          id: 'invitation-123',
          status: 'pending',
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Future date
          tenantId: mockTenantId
        };

        // Mock getInvitationByToken
        getDocs.mockResolvedValue({
          empty: false,
          docs: [{
            id: mockInvitation.id,
            data: () => ({
              ...mockInvitation,
              sentAt: { toDate: () => new Date() },
              expiresAt: { toDate: () => mockInvitation.expiresAt },
              createdAt: { toDate: () => new Date() },
              updatedAt: { toDate: () => new Date() }
            })
          }]
        });

        const result = await invitationService.acceptInvitation('test-token', 'user-123');

        expect(result.success).toBe(true);
        expect(updateDoc).toHaveBeenCalled();
      });

      it('should reject expired invitation', async () => {
        const { getDocs } = require('firebase/firestore');
        
        const mockInvitation = {
          id: 'invitation-123',
          status: 'pending',
          expiresAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // Past date
          tenantId: mockTenantId
        };

        getDocs.mockResolvedValue({
          empty: false,
          docs: [{
            id: mockInvitation.id,
            data: () => ({
              ...mockInvitation,
              sentAt: { toDate: () => new Date() },
              expiresAt: { toDate: () => mockInvitation.expiresAt },
              createdAt: { toDate: () => new Date() },
              updatedAt: { toDate: () => new Date() }
            })
          }]
        });

        const result = await invitationService.acceptInvitation('test-token', 'user-123');

        expect(result.success).toBe(false);
        expect(result.error).toBe('Invitation has expired');
      });

      it('should reject non-pending invitation', async () => {
        const { getDocs } = require('firebase/firestore');
        
        const mockInvitation = {
          id: 'invitation-123',
          status: 'accepted',
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          tenantId: mockTenantId
        };

        getDocs.mockResolvedValue({
          empty: false,
          docs: [{
            id: mockInvitation.id,
            data: () => ({
              ...mockInvitation,
              sentAt: { toDate: () => new Date() },
              expiresAt: { toDate: () => mockInvitation.expiresAt },
              createdAt: { toDate: () => new Date() },
              updatedAt: { toDate: () => new Date() }
            })
          }]
        });

        const result = await invitationService.acceptInvitation('test-token', 'user-123');

        expect(result.success).toBe(false);
        expect(result.error).toBe('Invitation is no longer valid');
      });
    });

    describe('resendInvitation', () => {
      it('should resend pending invitation successfully', async () => {
        const { getDoc, updateDoc } = require('firebase/firestore');
        const { EnhancedEmailService } = require('@/services/enhancedEmailService');
        
        const mockInvitation = {
          id: 'invitation-123',
          status: 'pending',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          roleName: 'Team Member',
          invitationUrl: 'https://app.evexa.com/invitation/token',
          invitedByName: 'Admin User',
          reminderCount: 0,
          tenantId: mockTenantId
        };

        getDoc.mockResolvedValue({
          exists: () => true,
          data: () => ({
            ...mockInvitation,
            sentAt: { toDate: () => new Date() },
            expiresAt: { toDate: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) },
            createdAt: { toDate: () => new Date() },
            updatedAt: { toDate: () => new Date() }
          })
        });

        const result = await invitationService.resendInvitation('invitation-123');

        expect(result.success).toBe(true);
        expect(updateDoc).toHaveBeenCalled();
      });

      it('should reject resending non-pending invitation', async () => {
        const { getDoc } = require('firebase/firestore');
        
        const mockInvitation = {
          status: 'accepted',
          tenantId: mockTenantId
        };

        getDoc.mockResolvedValue({
          exists: () => true,
          data: () => mockInvitation
        });

        const result = await invitationService.resendInvitation('invitation-123');

        expect(result.success).toBe(false);
        expect(result.error).toBe('Can only resend pending invitations');
      });
    });

    describe('getInvitationStats', () => {
      it('should calculate invitation statistics correctly', async () => {
        const { getDocs } = require('firebase/firestore');
        
        const mockInvitations = [
          { status: 'pending' },
          { status: 'pending' },
          { status: 'accepted' },
          { status: 'accepted' },
          { status: 'accepted' },
          { status: 'expired' },
          { status: 'cancelled' }
        ];

        getDocs.mockResolvedValue({
          docs: mockInvitations.map((inv, index) => ({
            id: `invitation-${index}`,
            data: () => ({
              ...inv,
              sentAt: { toDate: () => new Date() },
              expiresAt: { toDate: () => new Date() },
              createdAt: { toDate: () => new Date() },
              updatedAt: { toDate: () => new Date() }
            })
          }))
        });

        const stats = await invitationService.getInvitationStats();

        expect(stats.total).toBe(7);
        expect(stats.pending).toBe(2);
        expect(stats.accepted).toBe(3);
        expect(stats.expired).toBe(1);
        expect(stats.cancelled).toBe(1);
        expect(stats.acceptanceRate).toBe(60); // 3 accepted out of 5 total sent (3+1+1)
      });
    });
  });

  describe('InvitationScheduledJobsService', () => {
    describe('processExpiredInvitations', () => {
      it('should process expired invitations successfully', async () => {
        const { getDocs, writeBatch } = require('firebase/firestore');
        
        const mockBatch = {
          update: jest.fn(),
          commit: jest.fn()
        };
        writeBatch.mockReturnValue(mockBatch);

        const mockExpiredInvitations = [
          { id: 'inv-1' },
          { id: 'inv-2' }
        ];

        getDocs.mockResolvedValue({
          empty: false,
          docs: mockExpiredInvitations.map(inv => ({
            id: inv.id,
            data: () => ({ status: 'pending' })
          }))
        });

        const result = await InvitationScheduledJobsService.processExpiredInvitations();

        expect(result.success).toBe(true);
        expect(result.processed).toBe(2);
        expect(mockBatch.update).toHaveBeenCalledTimes(2);
        expect(mockBatch.commit).toHaveBeenCalled();
      });

      it('should handle no expired invitations', async () => {
        const { getDocs } = require('firebase/firestore');
        
        getDocs.mockResolvedValue({ empty: true });

        const result = await InvitationScheduledJobsService.processExpiredInvitations();

        expect(result.success).toBe(true);
        expect(result.processed).toBe(0);
      });
    });

    describe('sendInvitationReminders', () => {
      it('should send reminders for eligible invitations', async () => {
        const { getDocs } = require('firebase/firestore');
        const { EnhancedEmailService } = require('@/services/enhancedEmailService');
        
        const mockInvitations = [
          {
            id: 'inv-1',
            tenantId: 'tenant-1',
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe',
            roleName: 'Team Member',
            invitationUrl: 'https://app.evexa.com/invitation/token1',
            invitedByName: 'Admin',
            reminderCount: 0,
            sentAt: { toDate: () => new Date(Date.now() - 4 * 24 * 60 * 60 * 1000) },
            expiresAt: { toDate: () => new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) },
            createdAt: { toDate: () => new Date() },
            updatedAt: { toDate: () => new Date() }
          }
        ];

        getDocs.mockResolvedValue({
          empty: false,
          docs: mockInvitations.map(inv => ({
            id: inv.id,
            data: () => inv
          }))
        });

        const result = await InvitationScheduledJobsService.sendInvitationReminders();

        expect(result.success).toBe(true);
        expect(result.remindersSent).toBe(1);
      });
    });

    describe('getJobStatistics', () => {
      it('should return job statistics', async () => {
        const { getDocs } = require('firebase/firestore');
        
        // Mock different query results
        getDocs
          .mockResolvedValueOnce({ size: 5 }) // pending
          .mockResolvedValueOnce({ size: 2 }) // expired
          .mockResolvedValueOnce({ size: 3 }) // needing reminders
          .mockResolvedValueOnce({ size: 10 }); // old for cleanup

        const stats = await InvitationScheduledJobsService.getJobStatistics();

        expect(stats.pendingInvitations).toBe(5);
        expect(stats.expiredInvitations).toBe(2);
        expect(stats.invitationsNeedingReminders).toBe(3);
        expect(stats.oldInvitationsForCleanup).toBe(10);
      });
    });
  });
});
