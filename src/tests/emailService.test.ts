/**
 * Email Service Integration Tests
 * Tests for SendGrid integration, quota management, and email delivery
 */

import { EnhancedEmailService } from '@/services/enhancedEmailService';
import { EmailConfigService } from '@/services/emailConfigService';
import { sendEmailWithSendGrid } from '@/lib/firebase-email-service';

// Mock Firebase
jest.mock('@/lib/firebase', () => ({
  db: {},
}));

jest.mock('firebase/firestore', () => ({
  doc: jest.fn(),
  getDoc: jest.fn(),
  setDoc: jest.fn(),
  updateDoc: jest.fn(),
  addDoc: jest.fn(),
  collection: jest.fn(),
  serverTimestamp: jest.fn(() => new Date()),
}));

// Mock SendGrid
jest.mock('@sendgrid/mail', () => ({
  setApiKey: jest.fn(),
  send: jest.fn(),
}));

describe('Email Service Integration', () => {
  const mockTenantId = 'test-tenant-123';
  let emailService: EnhancedEmailService;
  let configService: EmailConfigService;

  beforeEach(() => {
    jest.clearAllMocks();
    emailService = new EnhancedEmailService(mockTenantId);
    configService = new EmailConfigService(mockTenantId);
    
    // Set up environment variables
    process.env.SENDGRID_API_KEY = 'test-api-key';
    process.env.SENDGRID_FROM_EMAIL = '<EMAIL>';
    process.env.SENDGRID_FROM_NAME = 'EVEXA Test';
  });

  describe('SendGrid Integration', () => {
    it('should send email successfully with SendGrid', async () => {
      // Mock successful SendGrid response
      const mockSendGrid = require('@sendgrid/mail');
      mockSendGrid.send.mockResolvedValue([{
        statusCode: 202,
        headers: { 'x-message-id': 'test-message-id' }
      }]);

      const result = await sendEmailWithSendGrid({
        to: '<EMAIL>',
        subject: 'Test Email',
        html: '<h1>Test</h1>',
        customArgs: {
          tenantId: mockTenantId,
          source: 'test'
        }
      });

      expect(result.success).toBe(true);
      expect(result.messageId).toBe('test-message-id');
      expect(mockSendGrid.send).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Test Email',
          html: '<h1>Test</h1>',
          from: {
            email: '<EMAIL>',
            name: 'EVEXA Test'
          }
        })
      );
    });

    it('should handle SendGrid errors gracefully', async () => {
      const mockSendGrid = require('@sendgrid/mail');
      mockSendGrid.send.mockRejectedValue(new Error('SendGrid API error'));

      const result = await sendEmailWithSendGrid({
        to: '<EMAIL>',
        subject: 'Test Email',
        html: '<h1>Test</h1>'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('SendGrid API error');
    });

    it('should fail when API key is not configured', async () => {
      delete process.env.SENDGRID_API_KEY;

      const result = await sendEmailWithSendGrid({
        to: '<EMAIL>',
        subject: 'Test Email',
        html: '<h1>Test</h1>'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('SendGrid API key not configured');
    });
  });

  describe('Email Configuration Service', () => {
    it('should return default configuration when none exists', async () => {
      const { getDoc } = require('firebase/firestore');
      getDoc.mockResolvedValue({ exists: () => false });

      const config = await configService.getGlobalConfig();

      expect(config.provider).toBe('sendgrid');
      expect(config.fromEmail).toBe('<EMAIL>');
      expect(config.fromName).toBe('EVEXA Test');
    });

    it('should check email quota correctly', async () => {
      const { getDoc } = require('firebase/firestore');
      getDoc.mockResolvedValue({
        exists: () => true,
        data: () => ({
          tenantId: mockTenantId,
          dailyLimit: 100,
          monthlyLimit: 1000,
          dailyUsed: 50,
          monthlyUsed: 200,
          isBlocked: false,
          lastResetDate: new Date()
        })
      });

      const canSend = await configService.canSendEmail();
      expect(canSend.allowed).toBe(true);
    });

    it('should block when daily limit exceeded', async () => {
      const { getDoc } = require('firebase/firestore');
      getDoc.mockResolvedValue({
        exists: () => true,
        data: () => ({
          tenantId: mockTenantId,
          dailyLimit: 100,
          monthlyLimit: 1000,
          dailyUsed: 100,
          monthlyUsed: 200,
          isBlocked: false,
          lastResetDate: new Date()
        })
      });

      const canSend = await configService.canSendEmail();
      expect(canSend.allowed).toBe(false);
      expect(canSend.reason).toBe('Daily email limit exceeded');
    });

    it('should block when account is blocked', async () => {
      const { getDoc } = require('firebase/firestore');
      getDoc.mockResolvedValue({
        exists: () => true,
        data: () => ({
          tenantId: mockTenantId,
          dailyLimit: 100,
          monthlyLimit: 1000,
          dailyUsed: 10,
          monthlyUsed: 50,
          isBlocked: true,
          blockReason: 'Spam complaints',
          lastResetDate: new Date()
        })
      });

      const canSend = await configService.canSendEmail();
      expect(canSend.allowed).toBe(false);
      expect(canSend.reason).toBe('Spam complaints');
    });
  });

  describe('Enhanced Email Service', () => {
    it('should send email with quota checking', async () => {
      // Mock quota check
      const { getDoc } = require('firebase/firestore');
      getDoc.mockResolvedValue({
        exists: () => true,
        data: () => ({
          tenantId: mockTenantId,
          dailyLimit: 100,
          monthlyLimit: 1000,
          dailyUsed: 10,
          monthlyUsed: 50,
          isBlocked: false,
          lastResetDate: new Date()
        })
      });

      // Mock successful SendGrid
      const mockSendGrid = require('@sendgrid/mail');
      mockSendGrid.send.mockResolvedValue([{
        statusCode: 202,
        headers: { 'x-message-id': 'test-message-id' }
      }]);

      const result = await emailService.sendEmail({
        tenantId: mockTenantId,
        to: '<EMAIL>',
        subject: 'Test Email',
        html: '<h1>Test</h1>',
        priority: 'normal'
      });

      expect(result.success).toBe(true);
      expect(result.messageId).toBe('test-message-id');
      expect(result.quotaRemaining).toBeDefined();
    });

    it('should reject email when quota exceeded', async () => {
      // Mock quota exceeded
      const { getDoc } = require('firebase/firestore');
      getDoc.mockResolvedValue({
        exists: () => true,
        data: () => ({
          tenantId: mockTenantId,
          dailyLimit: 100,
          monthlyLimit: 1000,
          dailyUsed: 100,
          monthlyUsed: 500,
          isBlocked: false,
          lastResetDate: new Date()
        })
      });

      const result = await emailService.sendEmail({
        tenantId: mockTenantId,
        to: '<EMAIL>',
        subject: 'Test Email',
        html: '<h1>Test</h1>'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Daily email limit exceeded');
    });

    it('should reject email with mismatched tenant ID', async () => {
      const result = await emailService.sendEmail({
        tenantId: 'different-tenant',
        to: '<EMAIL>',
        subject: 'Test Email',
        html: '<h1>Test</h1>'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Tenant ID mismatch');
    });
  });

  describe('Email Analytics', () => {
    it('should log email analytics on successful send', async () => {
      const { addDocument } = require('@/services/firestoreService');
      jest.doMock('@/services/firestoreService', () => ({
        addDocument: jest.fn().mockResolvedValue({ id: 'analytics-id' })
      }));

      // Mock quota and SendGrid success
      const { getDoc } = require('firebase/firestore');
      getDoc.mockResolvedValue({
        exists: () => true,
        data: () => ({
          tenantId: mockTenantId,
          dailyLimit: 100,
          monthlyLimit: 1000,
          dailyUsed: 10,
          monthlyUsed: 50,
          isBlocked: false,
          lastResetDate: new Date()
        })
      });

      const mockSendGrid = require('@sendgrid/mail');
      mockSendGrid.send.mockResolvedValue([{
        statusCode: 202,
        headers: { 'x-message-id': 'test-message-id' }
      }]);

      await emailService.sendEmail({
        tenantId: mockTenantId,
        to: '<EMAIL>',
        subject: 'Test Email',
        html: '<h1>Test</h1>',
        tags: ['test', 'integration']
      });

      // Verify analytics logging would be called
      // Note: This would need proper mocking of the addDocument function
    });
  });
});
