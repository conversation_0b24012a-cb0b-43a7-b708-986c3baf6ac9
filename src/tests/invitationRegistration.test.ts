/**
 * Invitation Registration System Tests
 * Tests for user registration flow from invitations with persona assignment
 */

import { InvitationRegistrationService } from '@/services/invitationRegistrationService';

// Mock Firebase Auth
jest.mock('firebase/auth', () => ({
  createUserWithEmailAndPassword: jest.fn(),
  updateProfile: jest.fn(),
  sendEmailVerification: jest.fn(),
  getAuth: jest.fn()
}));

// Mock Firebase Firestore
jest.mock('firebase/firestore', () => ({
  doc: jest.fn(),
  setDoc: jest.fn(),
  getDoc: jest.fn(),
  updateDoc: jest.fn(),
  serverTimestamp: jest.fn(() => new Date()),
  runTransaction: jest.fn()
}));

// Mock Firebase app
jest.mock('@/lib/firebase', () => ({
  auth: {},
  db: {}
}));

// Mock Invitation Management Service
jest.mock('@/services/invitationManagementService', () => ({
  InvitationManagementService: jest.fn().mockImplementation(() => ({
    acceptInvitation: jest.fn().mockResolvedValue({ success: true })
  }))
}));

// Mock fetch for API calls
global.fetch = jest.fn();

describe('Invitation Registration System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Set up environment
    process.env.NEXT_PUBLIC_APP_URL = 'https://app.evexa.com';
  });

  describe('InvitationRegistrationService', () => {
    describe('validateInvitationToken', () => {
      it('should validate a valid invitation token', async () => {
        const mockInvitation = {
          id: 'invitation-123',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          status: 'pending',
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          tenantId: 'tenant-123',
          personaId: 'persona-123'
        };

        (fetch as jest.Mock).mockResolvedValue({
          ok: true,
          json: () => Promise.resolve(mockInvitation)
        });

        const result = await InvitationRegistrationService.validateInvitationToken('valid-token');

        expect(result.isValid).toBe(true);
        expect(result.invitation).toEqual(mockInvitation);
        expect(fetch).toHaveBeenCalledWith('/api/invitations/valid-token');
      });

      it('should reject expired invitation token', async () => {
        const mockInvitation = {
          id: 'invitation-123',
          email: '<EMAIL>',
          status: 'pending',
          expiresAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // Past date
        };

        (fetch as jest.Mock).mockResolvedValue({
          ok: true,
          json: () => Promise.resolve(mockInvitation)
        });

        const result = await InvitationRegistrationService.validateInvitationToken('expired-token');

        expect(result.isValid).toBe(false);
        expect(result.expired).toBe(true);
        expect(result.error).toBe('This invitation has expired');
      });

      it('should reject non-pending invitation', async () => {
        const mockInvitation = {
          id: 'invitation-123',
          email: '<EMAIL>',
          status: 'accepted',
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
        };

        (fetch as jest.Mock).mockResolvedValue({
          ok: true,
          json: () => Promise.resolve(mockInvitation)
        });

        const result = await InvitationRegistrationService.validateInvitationToken('used-token');

        expect(result.isValid).toBe(false);
        expect(result.error).toBe('This invitation is no longer valid');
      });

      it('should handle API errors', async () => {
        (fetch as jest.Mock).mockResolvedValue({
          ok: false,
          status: 404
        });

        const result = await InvitationRegistrationService.validateInvitationToken('invalid-token');

        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Invitation not found or invalid');
      });
    });

    describe('registerFromInvitation', () => {
      it('should register user successfully from valid invitation', async () => {
        const { createUserWithEmailAndPassword, updateProfile, sendEmailVerification } = require('firebase/auth');
        const { setDoc, getDoc } = require('firebase/firestore');

        // Mock valid invitation
        (fetch as jest.Mock).mockResolvedValue({
          ok: true,
          json: () => Promise.resolve({
            id: 'invitation-123',
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe',
            status: 'pending',
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            tenantId: 'tenant-123',
            personaId: 'persona-123'
          })
        });

        // Mock Firebase Auth user creation
        const mockFirebaseUser = {
          uid: 'user-123',
          email: '<EMAIL>',
          displayName: 'John Doe',
          emailVerified: false,
          photoURL: null
        };

        createUserWithEmailAndPassword.mockResolvedValue({
          user: mockFirebaseUser
        });

        updateProfile.mockResolvedValue(undefined);
        sendEmailVerification.mockResolvedValue(undefined);

        // Mock persona lookup
        getDoc.mockResolvedValue({
          exists: () => true,
          data: () => ({
            name: 'Team Member',
            permissions: ['read', 'write']
          })
        });

        // Mock user profile creation
        setDoc.mockResolvedValue(undefined);

        const request = {
          invitationToken: 'valid-token',
          password: 'SecurePass123!',
          confirmPassword: 'SecurePass123!',
          acceptTerms: true,
          acceptPrivacy: true,
          profileData: {
            phone: '+**********',
            timezone: 'America/New_York',
            language: 'en'
          }
        };

        const result = await InvitationRegistrationService.registerFromInvitation(request);

        expect(result.success).toBe(true);
        expect(result.user).toBeDefined();
        expect(result.firebaseUser).toEqual(mockFirebaseUser);
        expect(result.requiresEmailVerification).toBe(true);
        expect(createUserWithEmailAndPassword).toHaveBeenCalledWith(
          expect.anything(),
          '<EMAIL>',
          'SecurePass123!'
        );
        expect(setDoc).toHaveBeenCalled();
      });

      it('should validate password requirements', async () => {
        const request = {
          invitationToken: 'valid-token',
          password: 'weak',
          confirmPassword: 'weak',
          acceptTerms: true,
          acceptPrivacy: true
        };

        const result = await InvitationRegistrationService.registerFromInvitation(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Password must be at least 8 characters long');
      });

      it('should validate password complexity', async () => {
        const request = {
          invitationToken: 'valid-token',
          password: 'simplepassword',
          confirmPassword: 'simplepassword',
          acceptTerms: true,
          acceptPrivacy: true
        };

        const result = await InvitationRegistrationService.registerFromInvitation(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Password must contain at least one uppercase letter');
      });

      it('should validate password confirmation', async () => {
        const request = {
          invitationToken: 'valid-token',
          password: 'SecurePass123!',
          confirmPassword: 'DifferentPass123!',
          acceptTerms: true,
          acceptPrivacy: true
        };

        const result = await InvitationRegistrationService.registerFromInvitation(request);

        expect(result.success).toBe(false);
        expect(result.error).toBe('Passwords do not match');
      });

      it('should validate terms acceptance', async () => {
        const request = {
          invitationToken: 'valid-token',
          password: 'SecurePass123!',
          confirmPassword: 'SecurePass123!',
          acceptTerms: false,
          acceptPrivacy: true
        };

        const result = await InvitationRegistrationService.registerFromInvitation(request);

        expect(result.success).toBe(false);
        expect(result.error).toBe('You must accept the Terms of Service');
      });

      it('should validate privacy policy acceptance', async () => {
        const request = {
          invitationToken: 'valid-token',
          password: 'SecurePass123!',
          confirmPassword: 'SecurePass123!',
          acceptTerms: true,
          acceptPrivacy: false
        };

        const result = await InvitationRegistrationService.registerFromInvitation(request);

        expect(result.success).toBe(false);
        expect(result.error).toBe('You must accept the Privacy Policy');
      });

      it('should handle Firebase Auth errors gracefully', async () => {
        const { createUserWithEmailAndPassword } = require('firebase/auth');

        // Mock valid invitation
        (fetch as jest.Mock).mockResolvedValue({
          ok: true,
          json: () => Promise.resolve({
            id: 'invitation-123',
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe',
            status: 'pending',
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            tenantId: 'tenant-123',
            personaId: 'persona-123'
          })
        });

        // Mock Firebase Auth error
        createUserWithEmailAndPassword.mockRejectedValue({
          code: 'auth/email-already-in-use',
          message: 'Email already in use'
        });

        const request = {
          invitationToken: 'valid-token',
          password: 'SecurePass123!',
          confirmPassword: 'SecurePass123!',
          acceptTerms: true,
          acceptPrivacy: true
        };

        const result = await InvitationRegistrationService.registerFromInvitation(request);

        expect(result.success).toBe(false);
        expect(result.error).toBe('An account with this email already exists');
      });
    });

    describe('updateUserProfile', () => {
      it('should update user profile successfully', async () => {
        const { updateDoc } = require('firebase/firestore');
        
        updateDoc.mockResolvedValue(undefined);

        const updates = {
          phone: '+**********',
          profileImageUrl: 'https://example.com/photo.jpg',
          onboardingCompleted: true
        };

        const result = await InvitationRegistrationService.updateUserProfile('user-123', updates);

        expect(result.success).toBe(true);
        expect(updateDoc).toHaveBeenCalled();
      });

      it('should handle update errors', async () => {
        const { updateDoc } = require('firebase/firestore');
        
        updateDoc.mockRejectedValue(new Error('Firestore error'));

        const result = await InvitationRegistrationService.updateUserProfile('user-123', {});

        expect(result.success).toBe(false);
        expect(result.error).toBe('Failed to update user profile');
      });
    });

    describe('getUserProfile', () => {
      it('should retrieve user profile successfully', async () => {
        const { getDoc } = require('firebase/firestore');
        
        const mockUserProfile = {
          id: 'user-123',
          email: '<EMAIL>',
          displayName: 'John Doe',
          role: 'user',
          tenantId: 'tenant-123'
        };

        getDoc.mockResolvedValue({
          exists: () => true,
          id: 'user-123',
          data: () => mockUserProfile
        });

        const result = await InvitationRegistrationService.getUserProfile('user-123');

        expect(result).toEqual({ id: 'user-123', ...mockUserProfile });
      });

      it('should return null for non-existent user', async () => {
        const { getDoc } = require('firebase/firestore');
        
        getDoc.mockResolvedValue({
          exists: () => false
        });

        const result = await InvitationRegistrationService.getUserProfile('non-existent');

        expect(result).toBeNull();
      });
    });
  });
});
