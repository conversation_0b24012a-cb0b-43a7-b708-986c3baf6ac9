/**
 * Persona-Specific Workflow Integration Tests
 * Tests different user personas and their specific workflows
 */

import { test, expect, Page } from '@playwright/test';
import { 
  AuthHelpers, 
  InvitationHelpers, 
  PermissionHelpers, 
  DataHelpers,
  DateHelpers 
} from './utils/integration-test-helpers';

const testAdmin = {
  email: '<EMAIL>',
  password: 'AdminPassword123!',
  displayName: 'Test Admin',
  firstName: 'Admin',
  lastName: 'User'
};

test.describe('Persona-Specific Workflow Tests', () => {
  let adminPage: Page;
  let userPage: Page;

  test.beforeAll(async ({ browser }) => {
    const adminContext = await browser.newContext();
    const userContext = await browser.newContext();
    
    adminPage = await adminContext.newPage();
    userPage = await userContext.newPage();
  });

  test.afterAll(async () => {
    await adminPage.close();
    await userPage.close();
  });

  test.describe('Exhibition Manager Persona', () => {
    const exhibitionManager = {
      email: `exhibition-manager-${Date.now()}@personatest.com`,
      firstName: 'Exhibition',
      lastName: 'Manager',
      password: 'ExhibitionManager123!'
    };

    test('Admin invites Exhibition Manager', async () => {
      await AuthHelpers.loginUser(adminPage, testAdmin);
      
      const token = await InvitationHelpers.sendInvitation(adminPage, {
        email: exhibitionManager.email,
        firstName: exhibitionManager.firstName,
        lastName: exhibitionManager.lastName,
        personaId: 'exhibition-manager',
        customMessage: 'Welcome to the exhibition team!'
      });
      
      await InvitationHelpers.acceptInvitation(userPage, token, exhibitionManager.password);
    });

    test('Exhibition Manager can manage exhibitions', async () => {
      // Verify access to exhibitions module
      await PermissionHelpers.verifyModuleAccess(userPage, 'exhibitions', true);
      
      // Create an exhibition
      await DataHelpers.createExhibition(userPage, {
        name: 'Tech Expo 2024',
        description: 'Annual technology exhibition',
        startDate: DateHelpers.getFutureDate(30),
        endDate: DateHelpers.getFutureDate(33),
        status: 'planning'
      });
      
      // Verify exhibition appears in list
      await expect(userPage.locator('[data-testid="exhibitions-list"]')).toContainText('Tech Expo 2024');
    });

    test('Exhibition Manager can manage tasks', async () => {
      // Navigate to exhibition tasks
      await userPage.goto('/exhibitions');
      await userPage.click('[data-testid="exhibition-Tech Expo 2024"]');
      await userPage.click('[data-testid="tab-tasks"]');
      
      // Create tasks
      await DataHelpers.createTask(userPage, {
        title: 'Design booth layout',
        description: 'Create the booth design and layout plan',
        priority: 'high',
        dueDate: DateHelpers.getFutureDate(20)
      });
      
      await DataHelpers.createTask(userPage, {
        title: 'Order promotional materials',
        description: 'Order brochures, banners, and giveaways',
        priority: 'medium',
        dueDate: DateHelpers.getFutureDate(25)
      });
      
      // Verify tasks are created
      await expect(userPage.locator('[data-testid="tasks-list"]')).toContainText('Design booth layout');
      await expect(userPage.locator('[data-testid="tasks-list"]')).toContainText('Order promotional materials');
    });

    test('Exhibition Manager cannot access restricted modules', async () => {
      // Should not access financial modules
      await PermissionHelpers.verifyModuleAccess(userPage, 'financials', false);
      
      // Should not access user management
      await PermissionHelpers.verifyModuleAccess(userPage, 'access-control', false);
      
      // Should not access system settings
      await PermissionHelpers.verifyModuleAccess(userPage, 'settings', false);
    });

    test('Exhibition Manager can access analytics', async () => {
      // Should have access to analytics
      await PermissionHelpers.verifyModuleAccess(userPage, 'analytics', true);
      
      // Verify can see exhibition-specific analytics
      await userPage.goto('/analytics');
      await expect(userPage.locator('[data-testid="exhibition-analytics"]')).toBeVisible();
      await expect(userPage.locator('[data-testid="task-analytics"]')).toBeVisible();
    });
  });

  test.describe('Marketing Specialist Persona', () => {
    const marketingSpecialist = {
      email: `marketing-specialist-${Date.now()}@personatest.com`,
      firstName: 'Marketing',
      lastName: 'Specialist',
      password: 'MarketingSpecialist123!'
    };

    test('Admin invites Marketing Specialist', async () => {
      const token = await InvitationHelpers.sendInvitation(adminPage, {
        email: marketingSpecialist.email,
        firstName: marketingSpecialist.firstName,
        lastName: marketingSpecialist.lastName,
        personaId: 'marketing-specialist',
        customMessage: 'Welcome to the marketing team!'
      });
      
      // Switch to new user context
      const newUserContext = await userPage.context().browser()?.newContext();
      const marketingPage = await newUserContext?.newPage();
      
      if (marketingPage) {
        await InvitationHelpers.acceptInvitation(marketingPage, token, marketingSpecialist.password);
        
        // Test marketing-specific workflows
        await PermissionHelpers.verifyModuleAccess(marketingPage, 'exhibitions', true);
        await PermissionHelpers.verifyModuleAccess(marketingPage, 'events', true);
        await PermissionHelpers.verifyModuleAccess(marketingPage, 'analytics', true);
        
        // Should not access financials
        await PermissionHelpers.verifyModuleAccess(marketingPage, 'financials', false);
        
        await marketingPage.close();
      }
    });
  });

  test.describe('Financial Controller Persona', () => {
    const financialController = {
      email: `financial-controller-${Date.now()}@personatest.com`,
      firstName: 'Financial',
      lastName: 'Controller',
      password: 'FinancialController123!'
    };

    test('Admin invites Financial Controller', async () => {
      const token = await InvitationHelpers.sendInvitation(adminPage, {
        email: financialController.email,
        firstName: financialController.firstName,
        lastName: financialController.lastName,
        personaId: 'financial-controller',
        customMessage: 'Welcome to the finance team!'
      });
      
      const newUserContext = await userPage.context().browser()?.newContext();
      const financePage = await newUserContext?.newPage();
      
      if (financePage) {
        await InvitationHelpers.acceptInvitation(financePage, token, financialController.password);
        
        // Test financial-specific workflows
        await PermissionHelpers.verifyModuleAccess(financePage, 'financials', true);
        await PermissionHelpers.verifyModuleAccess(financePage, 'analytics', true);
        
        // Can view exhibitions but with limited permissions
        await PermissionHelpers.verifyModuleAccess(financePage, 'exhibitions', true);
        
        // Should not access user management
        await PermissionHelpers.verifyModuleAccess(financePage, 'access-control', false);
        
        await financePage.close();
      }
    });
  });

  test.describe('Basic User Persona', () => {
    const basicUser = {
      email: `basic-user-${Date.now()}@personatest.com`,
      firstName: 'Basic',
      lastName: 'User',
      password: 'BasicUser123!'
    };

    test('Admin invites Basic User', async () => {
      const token = await InvitationHelpers.sendInvitation(adminPage, {
        email: basicUser.email,
        firstName: basicUser.firstName,
        lastName: basicUser.lastName,
        personaId: 'basic-user',
        customMessage: 'Welcome to the team!'
      });
      
      const newUserContext = await userPage.context().browser()?.newContext();
      const basicUserPage = await newUserContext?.newPage();
      
      if (basicUserPage) {
        await InvitationHelpers.acceptInvitation(basicUserPage, token, basicUser.password);
        
        // Test basic user limitations
        await PermissionHelpers.verifyModuleAccess(basicUserPage, 'exhibitions', true);
        await PermissionHelpers.verifyModuleAccess(basicUserPage, 'tasks', true);
        
        // Should not access advanced features
        await PermissionHelpers.verifyModuleAccess(basicUserPage, 'events', false);
        await PermissionHelpers.verifyModuleAccess(basicUserPage, 'financials', false);
        await PermissionHelpers.verifyModuleAccess(basicUserPage, 'analytics', false);
        await PermissionHelpers.verifyModuleAccess(basicUserPage, 'access-control', false);
        await PermissionHelpers.verifyModuleAccess(basicUserPage, 'settings', false);
        
        // Verify read-only access to exhibitions
        await basicUserPage.goto('/exhibitions');
        await expect(basicUserPage.locator('[data-testid="create-exhibition-button"]')).not.toBeVisible();
        
        await basicUserPage.close();
      }
    });
  });

  test.describe('Cross-Persona Collaboration', () => {
    test('Different personas can collaborate on same exhibition', async () => {
      // Exhibition Manager creates exhibition
      await userPage.goto('/exhibitions');
      await userPage.click('[data-testid="exhibition-Tech Expo 2024"]');
      
      // Add team members from different personas
      await userPage.click('[data-testid="tab-team"]');
      await userPage.click('[data-testid="add-team-member-button"]');
      
      // Add marketing specialist to exhibition team
      await userPage.fill('[data-testid="team-member-email"]', '<EMAIL>');
      await userPage.selectOption('[data-testid="team-member-role"]', 'marketing');
      await userPage.click('[data-testid="add-member-button"]');
      
      // Verify team member added
      await expect(userPage.locator('[data-testid="team-members-list"]')).toContainText('<EMAIL>');
      
      // Verify permissions are maintained per persona
      await expect(userPage.locator('[data-testid="member-permissions"]')).toContainText('Marketing');
    });

    test('Admin can view all user activities across personas', async () => {
      await adminPage.goto('/access-control');
      
      // View user activities
      await adminPage.click('[data-testid="tab-activity"]');
      
      // Should see activities from all personas
      await expect(adminPage.locator('[data-testid="activity-log"]')).toContainText('Exhibition Manager');
      await expect(adminPage.locator('[data-testid="activity-log"]')).toContainText('created exhibition');
      await expect(adminPage.locator('[data-testid="activity-log"]')).toContainText('created task');
      
      // Filter by persona
      await adminPage.selectOption('[data-testid="filter-persona"]', 'exhibition-manager');
      await expect(adminPage.locator('[data-testid="activity-log"]')).toContainText('Tech Expo 2024');
    });
  });

  test.describe('Persona Permission Updates', () => {
    test('Admin can modify user persona', async () => {
      await adminPage.goto('/access-control');
      
      // Find Exhibition Manager user
      const userRow = adminPage.locator(`[data-testid="user-${exhibitionManager.email}"]`);
      await userRow.click();
      
      // Change persona
      await adminPage.click('[data-testid="edit-user-button"]');
      await adminPage.selectOption('[data-testid="user-persona-select"]', 'marketing-specialist');
      await adminPage.click('[data-testid="save-user-button"]');
      
      // Verify persona change
      await expect(userRow).toContainText('Marketing Specialist');
      
      // User should now have marketing permissions
      // This would require the user to refresh/re-login to see changes
    });

    test('Persona changes are reflected in real-time', async () => {
      // After persona change, user should have different permissions
      await userPage.reload();
      
      // Should now have marketing specialist permissions
      await PermissionHelpers.verifyModuleAccess(userPage, 'events', true);
      
      // May lose some exhibition management specific features
      // This depends on the specific permission differences between personas
    });
  });
});
