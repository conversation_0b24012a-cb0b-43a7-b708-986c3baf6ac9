/**
 * Complete User Journey End-to-End Integration Tests
 * Tests the full user lifecycle from invitation to system usage with persona permissions
 */

import { test, expect, Page } from '@playwright/test';

// Test data
const testTenant = {
  id: 'e2e-test-tenant',
  name: 'E2E Test Company',
  domain: 'e2etest.com'
};

const testAdmin = {
  email: '<EMAIL>',
  password: 'AdminPassword123!',
  displayName: 'Test Admin'
};

const testInvitee = {
  email: `invited-${Date.now()}@e2etest.com`,
  firstName: '<PERSON>',
  lastName: 'Doe',
  password: 'InviteePassword123!'
};

test.describe('Complete User Journey Integration Tests', () => {
  let adminPage: Page;
  let inviteePage: Page;
  let invitationToken: string;

  test.beforeAll(async ({ browser }) => {
    // Create separate browser contexts for admin and invitee
    const adminContext = await browser.newContext();
    const inviteeContext = await browser.newContext();
    
    adminPage = await adminContext.newPage();
    inviteePage = await inviteeContext.newPage();
  });

  test.afterAll(async () => {
    await adminPage.close();
    await inviteePage.close();
  });

  test('Admin can send invitation with persona assignment', async () => {
    // Admin login
    await adminPage.goto('/login');
    await adminPage.fill('[data-testid="email-input"]', testAdmin.email);
    await adminPage.fill('[data-testid="password-input"]', testAdmin.password);
    await adminPage.click('[data-testid="login-button"]');
    
    // Wait for dashboard
    await expect(adminPage).toHaveURL('/dashboard');
    
    // Navigate to user management
    await adminPage.click('[data-testid="nav-access-control"]');
    await expect(adminPage).toHaveURL('/access-control');
    
    // Click invite user button
    await adminPage.click('[data-testid="invite-user-button"]');
    
    // Fill invitation form
    await adminPage.fill('[data-testid="email-input"]', testInvitee.email);
    await adminPage.fill('[data-testid="first-name-input"]', testInvitee.firstName);
    await adminPage.fill('[data-testid="last-name-input"]', testInvitee.lastName);
    
    // Select Exhibition Manager persona
    await adminPage.click('[data-testid="persona-select"]');
    await adminPage.click('[data-testid="persona-exhibition-manager"]');
    
    // Add custom message
    await adminPage.fill('[data-testid="custom-message"]', 'Welcome to our exhibition team!');
    
    // Send invitation
    await adminPage.click('[data-testid="send-invitation-button"]');
    
    // Verify success message
    await expect(adminPage.locator('[data-testid="success-message"]')).toContainText('Invitation sent successfully');
    
    // Verify invitation appears in pending list
    await expect(adminPage.locator('[data-testid="pending-invitations"]')).toContainText(testInvitee.email);
  });

  test('Invitee receives and accepts invitation', async () => {
    // Simulate email click - go directly to invitation page
    // In real scenario, this would come from email link
    invitationToken = 'test-invitation-token'; // This would be extracted from email
    
    await inviteePage.goto(`/invitation/${invitationToken}`);
    
    // Verify invitation details are displayed
    await expect(inviteePage.locator('[data-testid="invitation-details"]')).toContainText(testInvitee.firstName);
    await expect(inviteePage.locator('[data-testid="invitation-details"]')).toContainText('Exhibition Manager');
    await expect(inviteePage.locator('[data-testid="custom-message"]')).toContainText('Welcome to our exhibition team!');
    
    // Fill registration form
    await inviteePage.fill('[data-testid="password-input"]', testInvitee.password);
    await inviteePage.fill('[data-testid="confirm-password-input"]', testInvitee.password);
    
    // Accept terms
    await inviteePage.check('[data-testid="accept-terms-checkbox"]');
    await inviteePage.check('[data-testid="accept-privacy-checkbox"]');
    
    // Submit registration
    await inviteePage.click('[data-testid="accept-invitation-button"]');
    
    // Should redirect to dashboard
    await expect(inviteePage).toHaveURL('/dashboard');
    
    // Verify welcome message for new user
    await expect(inviteePage.locator('[data-testid="welcome-message"]')).toContainText('Welcome, John!');
  });

  test('New user has correct persona permissions', async () => {
    // Verify user can access exhibition management
    await inviteePage.click('[data-testid="nav-exhibitions"]');
    await expect(inviteePage).toHaveURL('/exhibitions');
    
    // Should see exhibitions page without access denied
    await expect(inviteePage.locator('[data-testid="exhibitions-list"]')).toBeVisible();
    
    // Verify user can create exhibitions (Exhibition Manager permission)
    await inviteePage.click('[data-testid="create-exhibition-button"]');
    await expect(inviteePage.locator('[data-testid="exhibition-form"]')).toBeVisible();
    
    // Verify user cannot access admin features
    await inviteePage.goto('/access-control');
    await expect(inviteePage.locator('[data-testid="access-denied"]')).toContainText('Access Denied');
    
    // Verify user cannot access financial features (not in Exhibition Manager persona)
    await inviteePage.goto('/financials');
    await expect(inviteePage.locator('[data-testid="access-denied"]')).toContainText('Access Denied');
  });

  test('User can perform allowed actions within persona scope', async () => {
    // Navigate to exhibitions
    await inviteePage.goto('/exhibitions');
    
    // Create a new exhibition
    await inviteePage.click('[data-testid="create-exhibition-button"]');
    
    await inviteePage.fill('[data-testid="exhibition-name"]', 'E2E Test Exhibition');
    await inviteePage.fill('[data-testid="exhibition-description"]', 'Created during E2E testing');
    await inviteePage.selectOption('[data-testid="exhibition-status"]', 'planning');
    
    // Set dates
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() + 2);
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 3);
    
    await inviteePage.fill('[data-testid="start-date"]', startDate.toISOString().split('T')[0]);
    await inviteePage.fill('[data-testid="end-date"]', endDate.toISOString().split('T')[0]);
    
    // Save exhibition
    await inviteePage.click('[data-testid="save-exhibition-button"]');
    
    // Verify success
    await expect(inviteePage.locator('[data-testid="success-message"]')).toContainText('Exhibition created successfully');
    
    // Verify exhibition appears in list
    await expect(inviteePage.locator('[data-testid="exhibitions-list"]')).toContainText('E2E Test Exhibition');
  });

  test('User can manage tasks within exhibition', async () => {
    // Navigate to the created exhibition
    await inviteePage.click('[data-testid="exhibition-E2E Test Exhibition"]');
    
    // Go to tasks tab
    await inviteePage.click('[data-testid="tab-tasks"]');
    
    // Create a new task
    await inviteePage.click('[data-testid="create-task-button"]');
    
    await inviteePage.fill('[data-testid="task-title"]', 'Setup booth design');
    await inviteePage.fill('[data-testid="task-description"]', 'Design and plan the exhibition booth layout');
    await inviteePage.selectOption('[data-testid="task-priority"]', 'high');
    
    // Set due date
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 14);
    await inviteePage.fill('[data-testid="task-due-date"]', dueDate.toISOString().split('T')[0]);
    
    // Assign to self
    await inviteePage.click('[data-testid="assign-to-me-button"]');
    
    // Save task
    await inviteePage.click('[data-testid="save-task-button"]');
    
    // Verify task creation
    await expect(inviteePage.locator('[data-testid="success-message"]')).toContainText('Task created successfully');
    await expect(inviteePage.locator('[data-testid="tasks-list"]')).toContainText('Setup booth design');
  });

  test('Admin can see new user and their activities', async () => {
    // Switch back to admin page
    await adminPage.goto('/access-control');
    
    // Refresh user list
    await adminPage.click('[data-testid="refresh-users-button"]');
    
    // Verify new user appears in active users list
    await expect(adminPage.locator('[data-testid="active-users-list"]')).toContainText(testInvitee.email);
    await expect(adminPage.locator('[data-testid="active-users-list"]')).toContainText('Exhibition Manager');
    
    // Check user activity
    await adminPage.click(`[data-testid="user-${testInvitee.email}"]`);
    
    // Verify user profile shows correct information
    await expect(adminPage.locator('[data-testid="user-profile"]')).toContainText(testInvitee.firstName);
    await expect(adminPage.locator('[data-testid="user-profile"]')).toContainText('Exhibition Manager');
    await expect(adminPage.locator('[data-testid="user-status"]')).toContainText('Active');
    
    // Verify recent activities
    await expect(adminPage.locator('[data-testid="recent-activities"]')).toContainText('Created exhibition');
    await expect(adminPage.locator('[data-testid="recent-activities"]')).toContainText('Created task');
  });

  test('Subscription limits are enforced correctly', async () => {
    // Admin tries to invite more users than allowed by subscription
    await adminPage.goto('/access-control');
    
    // Check current user count and limits
    const userCount = await adminPage.locator('[data-testid="current-user-count"]').textContent();
    const userLimit = await adminPage.locator('[data-testid="user-limit"]').textContent();
    
    // If near limit, invitation should show warning
    if (parseInt(userCount || '0') >= parseInt(userLimit || '10') * 0.8) {
      await adminPage.click('[data-testid="invite-user-button"]');
      await expect(adminPage.locator('[data-testid="limit-warning"]')).toContainText('approaching your user limit');
    }
    
    // If at limit, invitation should be blocked
    if (parseInt(userCount || '0') >= parseInt(userLimit || '10')) {
      await adminPage.click('[data-testid="invite-user-button"]');
      await expect(adminPage.locator('[data-testid="limit-reached"]')).toContainText('user limit reached');
      await expect(adminPage.locator('[data-testid="upgrade-prompt"]')).toBeVisible();
    }
  });

  test('Data isolation between tenants is maintained', async () => {
    // Verify user can only see data from their tenant
    await inviteePage.goto('/exhibitions');

    // All exhibitions should belong to the test tenant
    const exhibitions = await inviteePage.locator('[data-testid="exhibition-item"]').all();

    for (const exhibition of exhibitions) {
      // Each exhibition should have the correct tenant context
      // This would be verified through data attributes or API responses
      await expect(exhibition).toHaveAttribute('data-tenant-id', testTenant.id);
    }

    // Verify API calls include proper tenant isolation
    await inviteePage.route('**/api/**', (route) => {
      const request = route.request();
      const headers = request.headers();

      // Verify tenant ID is included in requests
      expect(headers['x-tenant-id'] || request.url()).toContain(testTenant.id);
      route.continue();
    });
  });

  test('User session management works correctly', async () => {
    // Verify user stays logged in across page refreshes
    await inviteePage.reload();
    await expect(inviteePage.locator('[data-testid="user-menu"]')).toContainText(testInvitee.firstName);

    // Verify user can logout
    await inviteePage.click('[data-testid="user-menu"]');
    await inviteePage.click('[data-testid="logout-button"]');

    // Should redirect to login page
    await expect(inviteePage).toHaveURL('/login');

    // Verify user can login again
    await inviteePage.fill('[data-testid="email-input"]', testInvitee.email);
    await inviteePage.fill('[data-testid="password-input"]', testInvitee.password);
    await inviteePage.click('[data-testid="login-button"]');

    // Should redirect back to dashboard
    await expect(inviteePage).toHaveURL('/dashboard');
  });

  test('Error handling works throughout the journey', async () => {
    // Test network error handling during invitation
    await adminPage.goto('/access-control');

    // Intercept and fail invitation API call
    await adminPage.route('**/api/invitations', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Server error' })
      });
    });

    await adminPage.click('[data-testid="invite-user-button"]');
    await adminPage.fill('[data-testid="email-input"]', '<EMAIL>');
    await adminPage.fill('[data-testid="first-name-input"]', 'Error');
    await adminPage.fill('[data-testid="last-name-input"]', 'Test');
    await adminPage.click('[data-testid="persona-select"]');
    await adminPage.click('[data-testid="persona-basic-user"]');
    await adminPage.click('[data-testid="send-invitation-button"]');

    // Should show error message
    await expect(adminPage.locator('[data-testid="error-message"]')).toContainText('Failed to send invitation');

    // Form should remain filled for retry
    await expect(adminPage.locator('[data-testid="email-input"]')).toHaveValue('<EMAIL>');
  });
});
