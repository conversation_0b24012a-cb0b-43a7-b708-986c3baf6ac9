/**
 * Integration Test Helpers
 * Utility functions for E2E integration testing
 */

import { Page, expect } from '@playwright/test';

// Test data interfaces
export interface TestUser {
  email: string;
  password: string;
  displayName: string;
  firstName?: string;
  lastName?: string;
  role?: string;
}

export interface TestTenant {
  id: string;
  name: string;
  domain: string;
}

export interface TestInvitation {
  email: string;
  firstName: string;
  lastName: string;
  personaId: string;
  customMessage?: string;
}

// Authentication helpers
export class AuthHelpers {
  static async loginUser(page: Page, user: TestUser): Promise<void> {
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', user.email);
    await page.fill('[data-testid="password-input"]', user.password);
    await page.click('[data-testid="login-button"]');
    
    // Wait for successful login
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  }

  static async logoutUser(page: Page): Promise<void> {
    await page.click('[data-testid="user-menu"]');
    await page.click('[data-testid="logout-button"]');
    await expect(page).toHaveURL('/login');
  }

  static async verifyUserSession(page: Page, user: TestUser): Promise<void> {
    await expect(page.locator('[data-testid="user-menu"]')).toContainText(
      user.firstName || user.displayName
    );
  }
}

// Invitation flow helpers
export class InvitationHelpers {
  static async sendInvitation(
    page: Page, 
    invitation: TestInvitation
  ): Promise<string> {
    await page.goto('/access-control');
    await page.click('[data-testid="invite-user-button"]');
    
    // Fill invitation form
    await page.fill('[data-testid="email-input"]', invitation.email);
    await page.fill('[data-testid="first-name-input"]', invitation.firstName);
    await page.fill('[data-testid="last-name-input"]', invitation.lastName);
    
    // Select persona
    await page.click('[data-testid="persona-select"]');
    await page.click(`[data-testid="persona-${invitation.personaId}"]`);
    
    if (invitation.customMessage) {
      await page.fill('[data-testid="custom-message"]', invitation.customMessage);
    }
    
    // Send invitation
    await page.click('[data-testid="send-invitation-button"]');
    
    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Invitation sent');
    
    // Return mock token (in real scenario, this would be extracted from email)
    return `test-token-${Date.now()}`;
  }

  static async acceptInvitation(
    page: Page, 
    token: string, 
    password: string
  ): Promise<void> {
    await page.goto(`/invitation/${token}`);
    
    // Verify invitation page loads
    await expect(page.locator('[data-testid="invitation-details"]')).toBeVisible();
    
    // Fill registration form
    await page.fill('[data-testid="password-input"]', password);
    await page.fill('[data-testid="confirm-password-input"]', password);
    
    // Accept terms
    await page.check('[data-testid="accept-terms-checkbox"]');
    await page.check('[data-testid="accept-privacy-checkbox"]');
    
    // Submit registration
    await page.click('[data-testid="accept-invitation-button"]');
    
    // Verify successful registration
    await expect(page).toHaveURL('/dashboard');
  }

  static async verifyInvitationInList(
    page: Page, 
    email: string, 
    status: 'pending' | 'accepted' | 'expired' = 'pending'
  ): Promise<void> {
    await page.goto('/access-control');
    
    const invitationRow = page.locator(`[data-testid="invitation-${email}"]`);
    await expect(invitationRow).toBeVisible();
    await expect(invitationRow).toContainText(status);
  }
}

// Permission testing helpers
export class PermissionHelpers {
  static async verifyModuleAccess(
    page: Page, 
    module: string, 
    shouldHaveAccess: boolean
  ): Promise<void> {
    await page.goto(`/${module}`);
    
    if (shouldHaveAccess) {
      await expect(page.locator('[data-testid="access-denied"]')).not.toBeVisible();
      await expect(page.locator(`[data-testid="${module}-content"]`)).toBeVisible();
    } else {
      await expect(page.locator('[data-testid="access-denied"]')).toBeVisible();
      await expect(page.locator('[data-testid="access-denied"]')).toContainText('Access Denied');
    }
  }

  static async verifyActionPermission(
    page: Page, 
    actionSelector: string, 
    shouldBeAllowed: boolean
  ): Promise<void> {
    const actionButton = page.locator(actionSelector);
    
    if (shouldBeAllowed) {
      await expect(actionButton).toBeEnabled();
      await expect(actionButton).not.toHaveAttribute('disabled');
    } else {
      await expect(actionButton).toBeDisabled();
    }
  }

  static async testPersonaPermissions(
    page: Page, 
    persona: string
  ): Promise<void> {
    const permissions = getPersonaPermissions(persona);
    
    for (const [module, hasAccess] of Object.entries(permissions)) {
      await this.verifyModuleAccess(page, module, hasAccess);
    }
  }
}

// Data creation helpers
export class DataHelpers {
  static async createExhibition(
    page: Page, 
    exhibitionData: {
      name: string;
      description: string;
      startDate: string;
      endDate: string;
      status?: string;
    }
  ): Promise<void> {
    await page.goto('/exhibitions');
    await page.click('[data-testid="create-exhibition-button"]');
    
    await page.fill('[data-testid="exhibition-name"]', exhibitionData.name);
    await page.fill('[data-testid="exhibition-description"]', exhibitionData.description);
    await page.fill('[data-testid="start-date"]', exhibitionData.startDate);
    await page.fill('[data-testid="end-date"]', exhibitionData.endDate);
    
    if (exhibitionData.status) {
      await page.selectOption('[data-testid="exhibition-status"]', exhibitionData.status);
    }
    
    await page.click('[data-testid="save-exhibition-button"]');
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Exhibition created');
  }

  static async createTask(
    page: Page, 
    taskData: {
      title: string;
      description: string;
      priority: string;
      dueDate: string;
    }
  ): Promise<void> {
    await page.click('[data-testid="create-task-button"]');
    
    await page.fill('[data-testid="task-title"]', taskData.title);
    await page.fill('[data-testid="task-description"]', taskData.description);
    await page.selectOption('[data-testid="task-priority"]', taskData.priority);
    await page.fill('[data-testid="task-due-date"]', taskData.dueDate);
    
    await page.click('[data-testid="save-task-button"]');
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Task created');
  }
}

// Subscription and limits testing helpers
export class SubscriptionHelpers {
  static async verifyUserLimits(
    page: Page, 
    expectedCurrent: number, 
    expectedLimit: number
  ): Promise<void> {
    await page.goto('/access-control');
    
    const currentCount = await page.locator('[data-testid="current-user-count"]').textContent();
    const limitCount = await page.locator('[data-testid="user-limit"]').textContent();
    
    expect(parseInt(currentCount || '0')).toBe(expectedCurrent);
    expect(parseInt(limitCount || '0')).toBe(expectedLimit);
  }

  static async testInvitationLimits(page: Page): Promise<void> {
    const userCount = await page.locator('[data-testid="current-user-count"]').textContent();
    const userLimit = await page.locator('[data-testid="user-limit"]').textContent();
    
    const current = parseInt(userCount || '0');
    const limit = parseInt(userLimit || '10');
    
    // Test warning at 80% capacity
    if (current >= limit * 0.8) {
      await page.click('[data-testid="invite-user-button"]');
      await expect(page.locator('[data-testid="limit-warning"]')).toBeVisible();
    }
    
    // Test blocking at 100% capacity
    if (current >= limit) {
      await page.click('[data-testid="invite-user-button"]');
      await expect(page.locator('[data-testid="limit-reached"]')).toBeVisible();
      await expect(page.locator('[data-testid="upgrade-prompt"]')).toBeVisible();
    }
  }
}

// Network and error testing helpers
export class ErrorHelpers {
  static async simulateNetworkError(
    page: Page, 
    apiPath: string, 
    errorCode: number = 500
  ): Promise<void> {
    await page.route(`**${apiPath}`, route => {
      route.fulfill({
        status: errorCode,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Simulated network error' })
      });
    });
  }

  static async verifyErrorHandling(
    page: Page, 
    expectedErrorMessage: string
  ): Promise<void> {
    await expect(page.locator('[data-testid="error-message"]')).toContainText(expectedErrorMessage);
  }

  static async verifyFormPersistence(
    page: Page, 
    formFields: Record<string, string>
  ): Promise<void> {
    for (const [selector, expectedValue] of Object.entries(formFields)) {
      await expect(page.locator(selector)).toHaveValue(expectedValue);
    }
  }
}

// Helper function to get persona permissions
function getPersonaPermissions(persona: string): Record<string, boolean> {
  const permissions: Record<string, Record<string, boolean>> = {
    'admin': {
      'exhibitions': true,
      'tasks': true,
      'events': true,
      'financials': true,
      'access-control': true,
      'analytics': true,
      'settings': true
    },
    'exhibition-manager': {
      'exhibitions': true,
      'tasks': true,
      'events': true,
      'financials': false,
      'access-control': false,
      'analytics': true,
      'settings': false
    },
    'marketing-specialist': {
      'exhibitions': true,
      'tasks': true,
      'events': true,
      'financials': false,
      'access-control': false,
      'analytics': true,
      'settings': false
    },
    'basic-user': {
      'exhibitions': true,
      'tasks': true,
      'events': false,
      'financials': false,
      'access-control': false,
      'analytics': false,
      'settings': false
    }
  };
  
  return permissions[persona] || {};
}

// Date utilities for testing
export class DateHelpers {
  static getFutureDate(daysFromNow: number): string {
    const date = new Date();
    date.setDate(date.getDate() + daysFromNow);
    return date.toISOString().split('T')[0];
  }

  static getPastDate(daysAgo: number): string {
    const date = new Date();
    date.setDate(date.getDate() - daysAgo);
    return date.toISOString().split('T')[0];
  }
}
